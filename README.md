# 高性能V2X网关系统使用指南

## 概述

本系统是一个高性能的V2X消息网关，支持同时连接多个MQTT Broker，并集成RocketMQ消息队列，提供完整的消息处理和验证能力。

## 功能特性

### 🚀 核心功能
- **双MQTT Broker支持**：同时监听项目一和项目十一的MQTT broker
- **高性能并发处理**：使用线程池优化消息处理性能
- **RocketMQ集成**：将MQTT消息转发到RocketMQ进行持久化和分发
- **实时监控**：提供连接状态监控和消息统计
- **自动重连**：网络断线时自动重连机制
- **控制台界面**：简单的命令行交互界面

### 📋 支持的消息类型
- **CAM消息**：合作感知消息，车辆位置和状态信息
- **MAP消息**：路口地图数据
- **交通事件数据**：基于CAM数据生成的交通事件

## 系统架构

```
MQTT Broker 1 (项目一) ──┐
                      ├──> 双MQTT消费者 ──> RocketMQ ──> RocketMQ消费者
MQTT Broker 2 (项目十一) ──┘              (验证)      (验证消息)
```

## 配置说明

### MQTT配置 (`src/main/resources/mqtt-config.yml`)

```yaml
mqtt:
  # Broker配置
  brokers:
    broker1:
      url: "tcp://*************:31811"
      username: "abbrsne/device-all-pass"
      password: "FvREptbVZdWOHmTe"
      description: "项目一路口MQTT Broker"
      enabled: true
    broker2:
      url: "tcp://*************:31811"
      username: "atcztnt/device-all-pass"
      password: "YKRvpgNUCbOerSZL"
      description: "项目十一路口MQTT Broker"
      enabled: true
  
  # RocketMQ配置
  rocketmq:
    nameserver: "**************:56738;**************:56739"
    producer:
      group: "dual_mqtt_producer_group"
```

## 启动方式

### 方式一：完整网关系统
```bash
# 编译项目
mvn clean compile

# 启动完整网关（包含MQTT消费者 + RocketMQ消费者）
java -cp "target/classes:target/lib/*" com.javaedge.GatewayApplication
```

### 方式二：单独启动双MQTT消费者
```bash
java -cp "target/classes:target/lib/*" com.javaedge.mqtt.DualMqttConsumer
```

### 方式三：单独启动RocketMQ消费者验证
```bash
java -cp "target/classes:target/lib/*" com.javaedge.rocketmq.RocketMqConsumerDemo
```

## 控制台命令

启动完整网关后，可使用以下命令：

- `help` / `h` - 显示帮助信息
- `status` / `s` - 显示系统状态
- `stats` - 显示详细统计信息
- `clear` / `cls` - 清屏
- `quit` / `exit` / `q` - 退出系统

## 监控信息

### 连接状态监控
系统会定期输出MQTT broker连接状态：
```
MQTT连接状态:
  项目一路口MQTT Broker : ✓ 已连接
  项目十一路口MQTT Broker : ✓ 已连接
```

### 消息统计
```
MQTT消息总数: 1234
交通灯数据消息数: 567
交通事件数据消息数: 890
```

## 性能优化

### 线程池配置
- **消息处理线程池**：40个线程，支持高并发消息处理
- **Broker管理线程池**：缓存线程池，动态管理连接

### 内存优化
- 使用对象池减少GC压力
- 异步消息处理避免阻塞
- 合理的连接超时和重试机制

## 故障排除

### 常见问题

1. **MQTT连接失败**
   - 检查网络连接
   - 验证用户名密码
   - 确认broker地址和端口

2. **RocketMQ连接失败**
   - 确认RocketMQ服务已启动
   - 检查NameServer地址配置
   - 验证防火墙设置

3. **消息处理慢**
   - 增加线程池大小
   - 检查网络延迟
   - 优化消息处理逻辑

### 日志查看
系统使用logback框架，日志配置在 `src/main/resources/logback.xml`

## 扩展开发

### 添加新的消息类型
1. 在 `DualMqttConsumer.processMessage()` 中添加新的消息类型判断
2. 创建对应的处理方法
3. 定义相应的数据结构

### 添加新的Broker
在 `mqtt-config.yml` 中添加新的broker配置即可，系统会自动连接。

## 注意事项

- 确保RocketMQ服务在启动网关前已经运行
- 建议在生产环境中调整JVM参数以获得最佳性能
- 定期监控系统资源使用情况
- 建议配置日志轮转避免磁盘空间不足

## 技术栈

- **Java 8**：确保兼容性
- **Eclipse Paho MQTT**：MQTT客户端
- **Apache RocketMQ**：消息队列
- **Protobuf**：消息序列化
- **FastJSON**：JSON处理
- **SLF4J + Logback**：日志框架