package com.javaedge;

import com.javaedge.mqtt.DualMqttConsumer;
import com.javaedge.rocketmq.RocketMqConsumerDemo;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Scanner;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * 
 * 高性能网关应用程序主类 [[memory:4816152]]
 * 支持双MQTT Broker同时监听和RocketMQ消息验证
 * 符合CAICT协议要求，提供完整的V2X消息处理能力
 */
@Slf4j
public class GatewayApplication {
    
    private static DualMqttConsumer mqttConsumer;
    private static volatile boolean running = true;
    
    public static void main(String[] args) {
        log.info("==========================================");
        log.info("    高性能V2X网关系统启动中...");
        log.info("    支持双MQTT Broker + RocketMQ");
        log.info("    Version: 2.0 (优化版)");
        log.info("==========================================");
        
        try {
            // 启动双MQTT消费者
            log.info("1. 启动双MQTT Broker消费者...");
            mqttConsumer = new DualMqttConsumer();
            CompletableFuture.runAsync(() -> mqttConsumer.start());
            Thread.sleep(3000); // 等待MQTT连接建立
            
            // 启动RocketMQ消费者（验证消息传输）
            log.info("2. 启动RocketMQ消费者（用于验证消息传输）...");
            CompletableFuture.runAsync(() -> RocketMqConsumerDemo.startConsumer());
            Thread.sleep(2000); // 等待RocketMQ消费者启动
            
            log.info("==========================================");
            log.info("    网关系统启动完成！");
            log.info("    MQTT消费者: 监听双broker消息");
            log.info("    RocketMQ消费者: 验证消息传输链路");
            log.info("==========================================");
            
            // 启动监控和控制台
            startMonitoring();
            startConsole();
            
        } catch (Exception e) {
            log.error("网关系统启动失败: {}", e.getMessage(), e);
            System.exit(1);
        }
    }
    
    /**
     * 启动监控线程
     * 定期输出系统状态和统计信息 [[memory:4816137]]
     */
    private static void startMonitoring() {
        Thread monitorThread = new Thread(() -> {
            while (running) {
                try {
                    Thread.sleep(60000); // 每分钟输出一次状态
                    
                    if (mqttConsumer != null) {
                        log.info("=== 系统状态监控 ===");
                        
                        // MQTT连接状态
                        Map<String, Boolean> connectionStatus = mqttConsumer.getConnectionStatus();
                        log.info("MQTT连接状态:");
                        connectionStatus.forEach((brokerId, isConnected) -> {
                            log.info("  {} : {}", brokerId, isConnected ? "✓ 已连接" : "✗ 断开");
                        });
                        
                        // 消息统计
                        int messageCount = mqttConsumer.getMessageCount();
                        log.info("MQTT消息总数: {}", messageCount);
                        
                        // RocketMQ消费统计
                        RocketMqConsumerDemo.printStats();
                        
                        log.info("系统运行正常，时间: {}", new java.util.Date());
                        log.info("==================");
                    }
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    log.error("监控线程异常: {}", e.getMessage());
                }
            }
        });
        
        monitorThread.setDaemon(true);
        monitorThread.setName("GatewayMonitor");
        monitorThread.start();
        log.info("系统监控线程已启动");
    }
    
    /**
     * 启动控制台交互
     * 提供简单的命令行操作界面
     */
    private static void startConsole() {
        Thread consoleThread = new Thread(() -> {
            Scanner scanner = new Scanner(System.in);
            
            printHelp();
            
            while (running) {
                try {
                    System.out.print("gateway> ");
                    String command = scanner.nextLine().trim().toLowerCase();
                    
                    switch (command) {
                        case "help":
                        case "h":
                            printHelp();
                            break;
                            
                        case "status":
                        case "s":
                            printStatus();
                            break;
                            
                        case "stats":
                            printDetailedStats();
                            break;
                            
                        case "quit":
                        case "exit":
                        case "q":
                            log.info("正在关闭网关系统...");
                            shutdown();
                            return;
                            
                        case "clear":
                        case "cls":
                            // 清屏
                            for (int i = 0; i < 50; i++) {
                                System.out.println();
                            }
                            break;
                            
                        default:
                            if (!command.isEmpty()) {
                                System.out.println("未知命令: " + command + "，输入 'help' 查看帮助");
                            }
                            break;
                    }
                } catch (Exception e) {
                    log.error("控制台命令处理异常: {}", e.getMessage());
                }
            }
            
            scanner.close();
        });
        
        consoleThread.setDaemon(true);
        consoleThread.setName("GatewayConsole");
        consoleThread.start();
    }
    
    /**
     * 打印帮助信息
     */
    private static void printHelp() {
        System.out.println("\n========== 网关控制台命令 ==========");
        System.out.println("help/h     - 显示帮助信息");
        System.out.println("status/s   - 显示系统状态");
        System.out.println("stats      - 显示详细统计");
        System.out.println("clear/cls  - 清屏");
        System.out.println("quit/exit/q - 退出系统");
        System.out.println("================================\n");
    }
    
    /**
     * 打印系统状态
     */
    private static void printStatus() {
        System.out.println("\n========== 系统状态 ==========");
        
        if (mqttConsumer != null) {
            Map<String, Boolean> connectionStatus = mqttConsumer.getConnectionStatus();
            System.out.println("MQTT Broker连接状态:");
            connectionStatus.forEach((brokerId, isConnected) -> {
                System.out.println("  " + brokerId + " : " + (isConnected ? "✓ 已连接" : "✗ 断开"));
            });
            
            System.out.println("MQTT消息总数: " + mqttConsumer.getMessageCount());
        } else {
            System.out.println("MQTT消费者未初始化");
        }
        
        System.out.println("系统运行状态: " + (running ? "正常运行" : "正在关闭"));
        System.out.println("当前时间: " + new java.util.Date());
        System.out.println("===========================\n");
    }
    
    /**
     * 打印详细统计信息
     */
    private static void printDetailedStats() {
        System.out.println("\n========== 详细统计 ==========");
        
        if (mqttConsumer != null) {
            // MQTT统计
            System.out.println("【MQTT统计】");
            Map<String, Boolean> connectionStatus = mqttConsumer.getConnectionStatus();
            connectionStatus.forEach((brokerId, isConnected) -> {
                System.out.println("  Broker: " + brokerId + " - " + (isConnected ? "在线" : "离线"));
            });
            System.out.println("  消息总数: " + mqttConsumer.getMessageCount());
            
            // RocketMQ统计
            System.out.println("\n【RocketMQ统计】");
            RocketMqConsumerDemo.printStats();
        }
        
        // JVM信息
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory() / 1024 / 1024;
        long freeMemory = runtime.freeMemory() / 1024 / 1024;
        long usedMemory = totalMemory - freeMemory;
        
        System.out.println("\n【系统信息】");
        System.out.println("  JVM内存使用: " + usedMemory + "MB / " + totalMemory + "MB");
        System.out.println("  可用处理器: " + runtime.availableProcessors());
        System.out.println("  运行时间: " + getUptime());
        
        System.out.println("===========================\n");
    }
    
    /**
     * 获取运行时间
     */
    private static String getUptime() {
        long uptimeMs = java.lang.management.ManagementFactory.getRuntimeMXBean().getUptime();
        long hours = uptimeMs / (1000 * 60 * 60);
        long minutes = (uptimeMs % (1000 * 60 * 60)) / (1000 * 60);
        long seconds = (uptimeMs % (1000 * 60)) / 1000;
        return String.format("%d小时%d分钟%d秒", hours, minutes, seconds);
    }
    
    /**
     * 优雅关闭系统
     */
    private static void shutdown() {
        running = false;
        
        log.info("开始关闭网关系统...");
        
        if (mqttConsumer != null) {
            mqttConsumer.shutdown();
        }
        
        log.info("网关系统已关闭");
        System.exit(0);
    }
}