package com.javaedge;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.javaedge.dto.TrafficEventData;
import com.javaedge.dto.TrafficLightData;
import com.javaedge.config.MqttConfig;
import com.javaedge.deserializer.CamDataMessageDeserializer;
import com.javaedge.deserializer.DeserializationException;
import com.javaedge.factory.XintongyuanDataFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import road.data.proto.*;
import road.data.proto.MapData;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
/**
 * @deprecated 已废弃，请使用 {@link com.javaedge.mqtt.DualMqttConsumer} 和 {@link com.javaedge.GatewayApplication}
 * 新版本支持双broker并发连接和高性能消息处理
 */
@Deprecated
public class MqttClientConsumer {

    private static final CamDataMessageDeserializer camDataDeserializer = new CamDataMessageDeserializer();
    private static final ExecutorService executor = Executors.newFixedThreadPool(20);
    private static final int RECONNECT_DELAY_MS = 5000;
    private static final int CONNECTION_TIMEOUT = 60; // 1min
    private static final int KEEP_ALIVE_INTERVAL = 120; // 2min

    public static void main(String[] args) {
        // MQTT Broker 地址和主题配置
//        String brokerUrl = "tcp://192.168.1.41:1883";
        String brokerUrl = "tcp://140.206.122.1:31811";

        // 项目一路口的账号密码
        String pro1Username = "abbrsne/device-all-pass";
        String pro1Password = "FvREptbVZdWOHmTe";

        // 项目十一路口的账号密码
        String pro11Username = "atcztnt/device-all-pass";
        String pro11Password = "YKRvpgNUCbOerSZL";

        // 加载MQTT配置
        MqttConfig mqttConfig = new MqttConfig();
        Map<String, Object> mqttConfigNodeIds = mqttConfig.getNodeIds();
        String clientId = mqttConfig.getClientId();

        // 获取需要订阅的融合感知数据主题
        List<String> camTopics = mqttConfig.getTopics("cam");
        int qos = 1;

        try {
            // 创建 MQTT 客户端
            MqttClient spatAndRteClient = new MqttClient(brokerUrl, clientId, new MemoryPersistence());

            // 设置连接选项
            MqttConnectOptions options = new MqttConnectOptions();
            options.setCleanSession(true);
            options.setUserName(pro1Username);
            options.setPassword(pro1Password.toCharArray());
            options.setConnectionTimeout(CONNECTION_TIMEOUT);
            options.setKeepAliveInterval(KEEP_ALIVE_INTERVAL);
            options.setAutomaticReconnect(true);

            spatAndRteClient.setCallback(new MqttCallback() {
                @Override
                public void connectionLost(Throwable cause) {
                    log.error("连接丢失：{}", cause.getMessage());
                    // 由于设置了自动重连，这里只需记录日志
                    // 如果需要额外的重连逻辑，可以在这里实现
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) {
                    // 使用线程池处理消息，避免每次创建新线程
                    log.info("收到消息：Topic={}, Message={}", topic, new String(message.getPayload()));
                    executor.submit(() -> processMessage(topic, message));
                }

                private void processMessage(String topic, MqttMessage message) {
                    try {
                        // 使用反序列化器将二进制数据转换为对象
                        CamData camData = camDataDeserializer.deserialize(message.getPayload());
                        if (camData == null) {
                            log.error("消息反序列化失败: 消息为空");
                            return;
                        }
                        handleSpatDataMessage(camData,topic);
                        handleRTEDataMessage(camData, topic);
                    } catch (DeserializationException e) {
                        log.error("消息反序列化失败: " + e.getMessage());
                    } catch (Exception e) {
                        log.error("处理spat或rte消息时发生未知错误");
                        e.printStackTrace();
                    }
                }

                private void handleRTEDataMessage(CamData camData, String topic) {
                    List<RteData> rteDataList = camData.getRteListList();
                    if (rteDataList == null || rteDataList.isEmpty()) {
                        // 该帧消息无 RteData 对象实例
                        return;
                    }

//                    try {
//                        Position3D refPos = camData.getRefPos();
//                        log.info("rteDataList={}", JSON.toJSONString(rteDataList));
//                        String fileName = "rte_" + "_" + System.currentTimeMillis() + ".txt";
//                        Files.write(Paths.get(fileName), JSON.toJSONString(rteDataList).getBytes());
//                        log.info("rteData 报文已写入文件: {}", fileName);
//                    } catch (Exception e) {
//                        log.error("处理 rte 消息异常: {}", e.getMessage());
//                    }

                    // 构建业务数据对象
                    TrafficEventData trafficEventData = XintongyuanDataFactory.toRTEData(rteDataList, topic);
                    if (trafficEventData == null || trafficEventData.getRtes() == null || trafficEventData.getRtes().isEmpty()) {
                        // 该帧消息无有效 rte 数据
                        log.warn("该帧消息无有效 rte 数据");
                        return;
                    }
                    deliverRTEData(trafficEventData);
                }

                private void handleSpatDataMessage(CamData camData, String topic) {
                    if (camData.getRoadSignalState() == null) {
                        // 该帧消息无 spat 对象实例：spat 2HZ，其它消息最大10HZ，所以可能无 spat 信息
                        return;
                    }
                    SpatData spatData = camData.getRoadSignalState();
                    // 检查 spatData 对象的所有字段是否为空
                    if (spatData.equals(SpatData.getDefaultInstance())) {
                        // 该帧消息的spat内容为空：spat 2HZ，其它消息最大10HZ，所以可能无 spat 信息
                        return;
                    }
//
//                    try {
//                        // spatData真正有数据
//                        log.info("spat={}", JSON.toJSONString(spatData));
//                        String fileName = "spat_" + System.currentTimeMillis() + ".txt";
//
//                        // 使用PrintWriter直接写入JSON字符串
//                        try (java.io.PrintWriter writer = new java.io.PrintWriter(
//                                new java.io.FileWriter(fileName))) {
//                            String spatJson = JSON.toJSONString(spatData, SerializerFeature.DisableCircularReferenceDetect);
//                            writer.println(spatJson);
//                        }
//                        log.info("spat报文已写入文件: {}", fileName);
//                    } catch (Exception e) {
//                        log.error("处理spat消息异常: {}", e.getMessage());
//                    }

                    // 构建业务数据对象
                    TrafficLightData trafficLightData = XintongyuanDataFactory.toTrafficLightData(spatData, topic);
                    processTrafficLigntData(trafficLightData);
                }

                /**
                 * 处理业务逻辑
                 */
                private void processTrafficLigntData(TrafficLightData trafficLightData) {
                    // 输出JSON字符串
                    log.info("jsonStr:" + JSON.toJSONString(trafficLightData));

                    // 将数据写入 RocketMQ topic
                    writeToRocketMQ(trafficLightData);
//                    writeToRocketMQ(trafficLightData);
                }
                
                /**
                 * 处理业务逻辑
                 */
                private void deliverRTEData(TrafficEventData trafficEventData) {
                    log.info("jsonStr=" + JSON.toJSONString(trafficEventData));
                    writeToRocketMQ(trafficEventData);
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {
                    // 发布消息完成后的处理（这里不需要实现）
                    log.info("deliveryComplete---------" + token.isComplete());
                }
            });

            // 连接到 MQTT Broker
            spatAndRteClient.connect(options);

            for (String topic : camTopics) {
                spatAndRteClient.subscribe(topic, qos);
                log.info("Subscribed to cam topic: " + topic);
            }

            // ========== 新增 map topic 消费 ===========
            List<String> mapTopics = mqttConfig.getTopics("map");
            MqttClient mapClient = new MqttClient(brokerUrl, clientId + "-map", new MemoryPersistence());
            MqttConnectOptions mapOptions = new MqttConnectOptions();
            mapOptions.setCleanSession(true);
            mapOptions.setUserName(pro1Username);
            mapOptions.setPassword(pro1Password.toCharArray());
            mapOptions.setConnectionTimeout(CONNECTION_TIMEOUT);
            mapOptions.setKeepAliveInterval(KEEP_ALIVE_INTERVAL);
            mapOptions.setAutomaticReconnect(true);

            mapClient.setCallback(new MqttCallback() {
                @Override
                public void connectionLost(Throwable cause) {
                    log.error("MAP连接丢失：{}", cause.getMessage());
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) {
                    String msg = new String(message.getPayload());
                    log.info("收到MAP消息：Topic={}, Message={}", topic, msg);
                    String fileName = "map_" + topic.replace('/', '_') + "_" + System.currentTimeMillis() + ".txt";
                    try (java.io.PrintWriter writer = new java.io.PrintWriter(
                            new java.io.FileWriter(fileName))) {
                        String spatJson = JSON.toJSONString(msg, SerializerFeature.DisableCircularReferenceDetect);
                        writer.println(spatJson);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    log.info("MAP报文已写入文件: {}", fileName);
                    executor.submit(() -> processMapMessage(topic, message));
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {}

                private void processMapMessage(String topic, MqttMessage message) {
                    try {
                        // 修正：直接用proto MapData反序列化
                        MapData mapData = MapData.parseFrom(message.getPayload());
                        // 要在入口处，根据 topic 包含的路口 id 判断为 yml 文件中的哪条路口
                        mqttConfigNodeIds.forEach((k, v) -> {
                            if (topic.contains(v.toString())) {
                                // 这里就是 yml 中配置的路口
                                log.info("路口 {} 的 map 数据: {}", k, JSON.toJSONString(mapData));
                            }
                        });
                        handleMapDownDataMessage(mapData, topic);
                    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                        log.error("消息反序列化失败: " + e.getMessage());
                    } catch (Exception e) {
                        log.error("处理Map消息时发生未知错误");
                        e.printStackTrace();
                    }
                }

                private void handleMapDownDataMessage(MapData mapData, String topic) {
                    if (mapData == null) {
                        log.warn("收到空的MapData对象");
                        return;
                    }
                    try {
                        // 1. 打印日志和写入原始报文
                        log.info("mapData={}", JSON.toJSONString(mapData));
                        String fileName = "map_" + System.currentTimeMillis() + ".txt";
                        try (java.io.PrintWriter writer = new java.io.PrintWriter(
                                new java.io.FileWriter(fileName))) {
                            String mapJson = JSON.toJSONString(mapData, SerializerFeature.DisableCircularReferenceDetect);
                            writer.println(mapJson);
                        }
                        log.info("map报文已写入文件: {}", fileName);
                    } catch (Exception e) {
                        log.error("处理map消息异常: {}", e.getMessage());
                    }
                    // 2. 转换为信通院格式
                    if (mapData == null || !mapData.hasMap()) {
                        return;
                    }
                    road.data.proto.MAP mapProto = mapData.getMap();
                    com.javaedge.dto.map.MapData xtyMapData = com.javaedge.factory.XintongyuanDataFactory.toMapData(mapProto, topic);
                    // 3. 业务处理（如写MQTT等）
                    log.info("信通院格式MapData: {}", JSON.toJSONString(xtyMapData));
                    // writeToMQTT(xtyMapData); // 如需写MQTT可放开
                }
            });

            mapClient.connect(mapOptions);
            for (String topic : mapTopics) {
                mapClient.subscribe(topic, qos);
                log.info("Subscribed to map topic: " + topic);
            }
            // ========== 新增 map topic 消费 END =========

        } catch (MqttException e) {
            log.error("MQTT连接异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("程序运行异常: " + e.getMessage());
        }
    }

    private static void writeToRocketMQ(TrafficLightData trafficLightData) {
        // --生产主题
        //crossStaticDataUpdate
        //realTimeDataPhaseLights
        //realTimeDataEvents
        //realTimeDataPerception
        //realTimeDataMap
        //--测试主题
        //test-crossStaticDataUpdate
        //test-realTimeDataPhaseLights
        //test-realTimeDataEvents
        //test-realTimeDataPerception
        //test-realTimeDataMap
        //内网NameServer: 192.168.139.27:9876;192.168.139.26:9876
        //外网NameServer:140.206.168.62:56738;140.206.168.62:56739
        //rocketMQ4.9.8

        try {
            // 1. 创建DefaultMQProducer实例
            DefaultMQProducer producer = new DefaultMQProducer("test-realTimeDataPhaseLights");
            // 2. 设置NameServer地址
            producer.setNamesrvAddr("140.206.168.62:56738;140.206.168.62:56739");
            // 3. 启动Producer
            producer.start();
            // 4. 创建消息
            Message message = new Message("test-realTimeDataPhaseLights", "TagA", JSON.toJSONString(trafficLightData).getBytes());
            // 5. 发送消息
            producer.send(message);
            // 6. 关闭Producer
            producer.shutdown();
        } catch (Exception e) {
            log.error("发送消息到RocketMQ异常: " + e.getMessage());
        }
    }

    private static void writeToRocketMQ(TrafficEventData trafficEventData) {
        try {
            // 1. 创建DefaultMQProducer实例
            DefaultMQProducer producer = new DefaultMQProducer("test-realTimeDataEvents");
            // 2. 设置NameServer地址
            producer.setNamesrvAddr("140.206.168.62:56738;140.206.168.62:56739");
            // 启用自动创建主题
            producer.setCreateTopicKey("TBW102");
            // 3. 启动Producer
            producer.start();
            // 4. 创建消息 - 使用已存在的主题或默认主题
            Message message = new Message("TBW102", "TagA", JSON.toJSONString(trafficEventData).getBytes());
            // 5. 发送消息
            producer.send(message);
            System.out.println("成功发送TrafficEventData消息到RocketMQ");
            // 6. 关闭Producer
            producer.shutdown();
        } catch (Exception e) {
            log.error("发送消息到RocketMQ异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 优雅地关闭线程池
     */
    private static void shutdownExecutor() {
        if (executor != null && !executor.isShutdown()) {
            try {
                // 尝试优雅关闭
                executor.shutdown();
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    // 如果等待超时，强制关闭
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 添加JVM关闭钩子，确保资源正确释放
     */
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("应用程序正在关闭，清理资源...");
            shutdownExecutor();
        }));
    }
}