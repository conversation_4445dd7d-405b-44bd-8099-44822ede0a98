package com.javaedge.config;

import lombok.Data;

/**
 * <AUTHOR>
 * 
 * MQTT Broker配置实体类 [[memory:4816152]]
 * 用于配置MQTT broker连接信息，支持CAICT协议
 */
@Data
public class BrokerConfig {
    /**
     * MQTT Broker连接URL
     * 格式: tcp://host:port
     */
    private String url;
    
    /**
     * MQTT连接用户名
     * 根据CAICT协议要求配置
     */
    private String username;
    
    /**
     * MQTT连接密码
     */
    private String password;
    
    /**
     * Broker描述信息
     */
    private String description;
    
    /**
     * 是否启用此broker
     */
    private boolean enabled = true;
    
    /**
     * 连接超时时间（秒）
     */
    private int connectionTimeout = 60;
    
    /**
     * 心跳间隔（秒）
     */
    private int keepAliveInterval = 120;
    
    /**
     * 是否启用自动重连
     */
    private boolean automaticReconnect = true;
    
    /**
     * 构造器
     */
    public BrokerConfig() {
    }
    
    /**
     * 全参构造器
     */
    public BrokerConfig(String url, String username, String password, String description) {
        this.url = url;
        this.username = username;
        this.password = password;
        this.description = description;
    }
}