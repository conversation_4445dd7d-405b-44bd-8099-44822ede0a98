package com.javaedge.config;

import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 配置文件加载器
 */
@Slf4j
public class ConfigLoader {
    private static final String CONFIG_FILE = "mqtt-config.yml";
    private static ConfigLoader instance;
    private Map<String, String> eventTypeMappings;

    private ConfigLoader() {
        loadConfig();
    }

    public static ConfigLoader getInstance() {
        if (instance == null) {
            synchronized (ConfigLoader.class) {
                if (instance == null) {
                    instance = new ConfigLoader();
                }
            }
        }
        return instance;
    }

    private void loadConfig() {
        eventTypeMappings = new HashMap<>();
        try {
            Yaml yaml = new Yaml();
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE);
            if (inputStream == null) {
                log.error("配置文件未找到: " + CONFIG_FILE);
                return;
            }

            Map<String, Object> config = yaml.load(inputStream);
            Map<String, Object> mqtt = (Map<String, Object>) config.get("mqtt");
            if (mqtt != null) {
                List<Map<String, Object>> eventTypes = (List<Map<String, Object>>) mqtt.get("eventType");
                if (eventTypes != null) {
                    for (Map<String, Object> eventType : eventTypes) {
                        String localCode = (String) eventType.get("localCode");
                        String remoteCode = (String) eventType.get("remoteCode");
                        if (localCode != null && remoteCode != null && !remoteCode.isEmpty()) {
                            eventTypeMappings.put(localCode, remoteCode);
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("加载配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 根据本地事件代码获取远程事件代码
     * @param localCode 本地事件代码
     * @return 远程事件代码，如果未找到映射则返回原值
     */
    public String getRemoteEventCode(String localCode) {
        return eventTypeMappings.getOrDefault(localCode, localCode);
    }

    /**
     * 获取所有事件类型映射
     * @return 事件类型映射Map
     */
    public Map<String, String> getAllEventTypeMappings() {
        return new HashMap<>(eventTypeMappings);
    }
}