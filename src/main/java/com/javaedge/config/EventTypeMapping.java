package com.javaedge.config;

/**
 * 事件类型映射配置类
 */
public class EventTypeMapping {
    private String localCode;
    private String remoteCode; 
    private String description;

    public EventTypeMapping() {
    }

    public EventTypeMapping(String localCode, String remoteCode, String description) {
        this.localCode = localCode;
        this.remoteCode = remoteCode;
        this.description = description;
    }

    public String getLocalCode() {
        return localCode;
    }

    public void setLocalCode(String localCode) {
        this.localCode = localCode;
    }

    public String getRemoteCode() {
        return remoteCode;
    }

    public void setRemoteCode(String remoteCode) {
        this.remoteCode = remoteCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "EventTypeMapping{" +
                "localCode='" + localCode + '\'' +
                ", remoteCode='" + remoteCode + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
} 