package com.javaedge.config;

import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
/**
 * <AUTHOR>
 *
 * MQTT配置加载类
 * 负责从YAML配置文件中加载MQTT主题配置
 */
public class MqttConfig {
    private Map<String, Object> config;

    /**
     * 加载MQTT配置
     */
    public MqttConfig() {
        try {
            Yaml yaml = new Yaml();
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("mqtt-config.yml");
            config = yaml.load(inputStream);
        } catch (Exception e) {
            log.error("加载MQTT配置文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定类型的所有主题
     *
     * @param topicType 主题类型 (participant, rte, trafficflow)
     * @return 主题列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getTopics(String topicType) {
        List<String> result = new ArrayList<>();
        try {
            Map<String, Object> mqtt = (Map<String, Object>) config.get("mqtt");
            Map<String, Object> topics = (Map<String, Object>) mqtt.get("topics");
            List<Map<String, Object>> topicList = (List<Map<String, Object>>) topics.get(topicType);

            for (Map<String, Object> topicEntry : topicList) {
                result.add((String) topicEntry.get("topic"));
            }
        } catch (Exception e) {
            log.error("获取主题配置失败: " + e.getMessage());
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    public Map<String, Object> getNodeIds() {
        Map<String, Object> nodeIds = new HashMap<>();
        try {
            Map<String, Object> mqtt = (Map<String, Object>) config.get("mqtt");
            List<Map<String, Object>> intersections = (List<Map<String, Object>>) mqtt.get("intersections");

            if (intersections != null) {
                for (Map<String, Object> intersection : intersections) {
                    String id = (String) intersection.get("id");
                    if (id != null) {
                        nodeIds.put(id, intersection);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取路口配置失败: " + e.getMessage());
        }
        return nodeIds;
    }

    /**
     * 根据名称获取特定主题
     *
     * 根据名称获取特定主题
     *
     * @param topicType 主题类型 (participant, rte, trafficflow)
     * @param name 主题名称
     * @return 主题字符串，如果未找到则返回null
     */
    @SuppressWarnings("unchecked")
    public String getTopicByName(String topicType, String name) {
        try {
            Map<String, Object> mqtt = (Map<String, Object>) config.get("mqtt");
            Map<String, Object> topics = (Map<String, Object>) mqtt.get("topics");
            List<Map<String, Object>> topicList = (List<Map<String, Object>>) topics.get(topicType);

            for (Map<String, Object> topicEntry : topicList) {
                if (name.equals(topicEntry.get("name"))) {
                    return (String) topicEntry.get("topic");
                }
            }
        } catch (Exception e) {
            log.error("获取主题配置失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 获取客户端ID
     * @return 客户端ID字符串
     */
    @SuppressWarnings("unchecked")
    public String getClientId() {
        try {
            Map<String, Object> mqtt = (Map<String, Object>) config.get("mqtt");
            Map<String, Object> client = (Map<String, Object>) mqtt.get("client");
            return (String) client.get("id");
        } catch (Exception e) {
            log.error("获取客户端ID失败: " + e.getMessage());
            return "JavaMqttConsumerClientTest"; // 默认值
        }
    }

    /**
     * 获取路口经纬度信息
     * @return Map<String, Map<String, Object>> 路口ID -> {refPosLat, refPosLon, name, description}
     */
    @SuppressWarnings("unchecked")
    public Map<String, Map<String, Object>> getIntersections() {
        Map<String, Map<String, Object>> refPositions = new HashMap<>();
        try {
            Map<String, Object> mqtt = (Map<String, Object>) config.get("mqtt");
            List<Map<String, Object>> intersections = (List<Map<String, Object>>) mqtt.get("intersections");

            if (intersections != null) {
                for (Map<String, Object> intersection : intersections) {
                    String id = (String) intersection.get("id");
                    Object refPosLat = intersection.get("refPosLat");
                    Object refPosLon = intersection.get("refPosLon");

                    if (id != null && refPosLat != null && refPosLon != null) {
                        Map<String, Object> positionInfo = new HashMap<>();
                        positionInfo.put("refPosLat", refPosLat);
                        positionInfo.put("refPosLon", refPosLon);
                        positionInfo.put("name", intersection.get("name"));
                        positionInfo.put("description", intersection.get("description"));
                        positionInfo.put("nodeId", intersection.get("nodeId"));
                        positionInfo.put("regionId", intersection.get("regionId"));
                        positionInfo.put("localNodeId", intersection.get("localNodeId"));

                        refPositions.put(id, positionInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取路口经纬度配置失败: " + e.getMessage());
        }
        return refPositions;
    }

    /**
     * 获取相位映射配置
     * @return Map<Integer, Integer> 原始相位ID -> 转换后相位ID
     */
    @SuppressWarnings("unchecked")
    public Map<Integer, Integer> getPhaseMappings() {
        Map<Integer, Integer> phaseMappings = new HashMap<>();
        try {
            Map<String, Object> mqtt = (Map<String, Object>) config.get("mqtt");
            List<Map<String, Object>> phaseMapping = (List<Map<String, Object>>) mqtt.get("phaseMapping");

            if (phaseMapping != null) {
                for (Map<String, Object> mapping : phaseMapping) {
                    Object originalPhaseId = mapping.get("originalPhaseId");
                    Object convertedPhaseId = mapping.get("convertedPhaseId");

                    if (originalPhaseId != null && convertedPhaseId != null) {
                        phaseMappings.put((Integer) originalPhaseId, (Integer) convertedPhaseId);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取相位映射配置失败: " + e.getMessage());
        }
        return phaseMappings;
    }
    
    /**
     * 获取所有Broker配置
     * 支持多broker配置，实现高性能并发连接 [[memory:4816137]]
     *
     * @return Broker配置列表
     */
    @SuppressWarnings("unchecked")
    public List<BrokerConfig> getBrokerConfigs() {
        List<BrokerConfig> brokerConfigs = new ArrayList<>();
        try {
            Map<String, Object> mqtt = (Map<String, Object>) config.get("mqtt");
            Map<String, Object> brokers = (Map<String, Object>) mqtt.get("brokers");
            
            if (brokers != null) {
                for (Map.Entry<String, Object> entry : brokers.entrySet()) {
                    Map<String, Object> brokerData = (Map<String, Object>) entry.getValue();
                    
                    BrokerConfig brokerConfig = new BrokerConfig();
                    brokerConfig.setUrl((String) brokerData.get("url"));
                    brokerConfig.setUsername((String) brokerData.get("username"));
                    brokerConfig.setPassword((String) brokerData.get("password"));
                    brokerConfig.setDescription((String) brokerData.get("description"));
                    brokerConfig.setEnabled((Boolean) brokerData.getOrDefault("enabled", true));
                    brokerConfig.setConnectionTimeout((Integer) brokerData.getOrDefault("connectionTimeout", 60));
                    brokerConfig.setKeepAliveInterval((Integer) brokerData.getOrDefault("keepAliveInterval", 120));
                    brokerConfig.setAutomaticReconnect((Boolean) brokerData.getOrDefault("automaticReconnect", true));
                    
                    brokerConfigs.add(brokerConfig);
                }
            }
        } catch (Exception e) {
            log.error("获取Broker配置失败: " + e.getMessage());
        }
        return brokerConfigs;
    }
}