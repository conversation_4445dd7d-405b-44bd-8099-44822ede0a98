package com.javaedge.deserializer;

import com.google.protobuf.InvalidProtocolBufferException;
import road.data.proto.CamData;

public class CamDataMessageDeserializer implements MessageDeserializer<CamData> {

    @Override
    public CamData deserialize(byte[] data) throws DeserializationException {
        if (data == null || data.length == 0) {
            throw new DeserializationException("Payload is empty");
        }

        try {
            // 使用Protobuf的parseFrom方法解析字节数据
            return CamData.parseFrom(data);
        } catch (InvalidProtocolBufferException e) {
            // 将Protobuf的异常包装成自定义异常
            throw new DeserializationException("Failed to parse RsPerceptionInfo from protobuf", e);
        }
    }
}