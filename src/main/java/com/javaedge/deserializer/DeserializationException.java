package com.javaedge.deserializer;

/**
 * 自定义反序列化异常
 * 用于封装底层反序列化过程中可能出现的各种异常
 */
public class DeserializationException extends RuntimeException {

    public DeserializationException(String message) {
        super(message);
    }

    public DeserializationException(String message, Throwable cause) {
        super(message, cause);
    }

    public DeserializationException(Throwable cause) {
        super(cause);
    }
}
