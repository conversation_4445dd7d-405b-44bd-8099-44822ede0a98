package com.javaedge.deserializer;

import com.google.protobuf.InvalidProtocolBufferException;
import road.data.proto.Percep.RsPerceptionInfo;

/**
 * 车辆消息反序列化器
 * 用于将Protobuf字节数据反序列化为RsPerceptionInfo对象
 */
public class VehicleMessageDeserializer implements MessageDeserializer<RsPerceptionInfo> {

    @Override
    public RsPerceptionInfo deserialize(byte[] data) throws DeserializationException {
        if (data == null || data.length == 0) {
            throw new DeserializationException("Payload is empty");
        }

        try {
            // 使用Protobuf的parseFrom方法解析字节数据
            return RsPerceptionInfo.parseFrom(data);
        } catch (InvalidProtocolBufferException e) {
            // 将Protobuf的异常包装成自定义异常
            throw new DeserializationException("Failed to parse RsPerceptionInfo from protobuf", e);
        }
    }
}