package com.javaedge.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class IntersectionState {
    /**
     * 区域编号，全局唯一的地区ID，6位数字，按照GB/T2260标准执行。只到嘉定区级，因此写死为 310114.
     */
    private int regionId = 310114;
    /**
     * 路口编号，地区内部唯一的节点ID，5位数字。
     */
    private int intersectionId;
    /**
     * 路口信号机的工作状态指示，参考YD/T 3709-2020中IntersectionStatusObject定义。
     */
    private int status;
    /**
     * 相位信息，Phase类型的定义应符合表 6。
     */
    private List<Phase> phases;
}