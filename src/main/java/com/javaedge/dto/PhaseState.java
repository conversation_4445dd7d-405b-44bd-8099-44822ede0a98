package com.javaedge.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PhaseState {
    /**
     * 灯色，信号灯相位的灯色状态，参考 YD/T 3709-2020 中 LightState。
     */
    private int light;
    /**
     * 倒计时时间，TimeCountingDown 类型的定义应符合表 8。
     * 序号2和3选其一，不可同时出现，二者选其一。
     */
    private TimeCountingDown counting;
    /**
     * UTC时间，UTCTiming 类型的定义应符合表 9。
     * 序号2和3选其一，不可同时出现，二者选其一。
     */
    private UTCTiming utcTiming;
}