package com.javaedge.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * 经纬度偏移量
 */
@Data
public class PositionOffsetLL {
    /**
     * 在参考位置的 ± 22.634554 米范围内
     */
    private Position2D positionLL1;
    /**
     * 在参考位置的 ± 90.571389 米范围内
     */
    private Position2D positionLL2;
    /**
     * 在参考位置的 ± 362.31873 米范围内
     */
    private Position2D positionLL3;
    /**
     * 在参考位置的 ± 1.449308 千米范围内
     */
    private Position2D positionLL4;
    /**
     * 在参考位置的 ± 23.189096 千米范围内
     */
    private Position2D positionLL5;
    /**
     * 在参考位置的 ± 92.756481 千米范围内
     */
    private Position2D positionLL6;
    /**
     * 节点是经纬度绝对坐标，不是参考位置 高说信通院口径是不传
     */
    private Position2D positionLatLon;
} 