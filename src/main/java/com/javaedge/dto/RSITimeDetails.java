package com.javaedge.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * 时间信息
 */
@Data
public class RSITimeDetails {
    /**
     * 开始时间，数值用来表示当前年份，已经过去的总分钟数（UTC 时间），可选
     */
    private Integer startTime;
    /**
     * 开始时间年份，如2021，可选
     */
    private Integer startTimeYear;
    /**
     * 结束时间，数值用来表示当前年份，已经过去的总分钟数（UTC 时间），可选
     */
    private Integer endTime;
    /**
     * 结束时间年份，如2021，可选
     */
    private Integer endTimeYear;
    /**
     * 结束时间置信度，数值描述了 95%置信水平的时间精度，0表示不可用，可选
     */
    private Integer endTimeConfidence;
}