package com.javaedge.dto;

import lombok.Data;

import java.util.List;

@Data
/**
 * 关联车道
 */
public class ReferenceLink {
    /**
     * 上游节点，NodeReferenceID 类型，必须
     */
    private NodeReferenceID upStreamNodeId;
    /**
     * 下游节点，NodeReferenceID 类型，必须
     */
    private NodeReferenceID downStreamNodeId;
    /**
     * 关联车道，[0..15]，如受影响的车道为lane1,3,5.则"referenceLanes":[1,3,5]，可选
     */
    private List<Integer> referenceLane;
} 