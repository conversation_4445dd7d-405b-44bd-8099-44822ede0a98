package com.javaedge.dto;

import lombok.Data;

@Data
public class TimeCountingDown {
    /**
     * 开始时间，如果当前该相位状态已开始（未结束），则该数值为0；如果当前该相位状态未开始，则表示当前时刻距离该相位状态开始的时间。
     * TimeMark 类型的定义应符合表 10。
     */
    private int startTime;
    /**
     * 最小结束时间，表示当前时刻距离该相位状态结束的最短时间（不管当前时刻该相位状态是否开始）。
     * TimeMark 类型的定义应符合表 10。
     */
    private Integer minEndTime;
    /**
     * 最大结束时间，表示当前时刻距离该相位状态结束的最长时间（不管当前时刻该相位状态是否开始）。
     * TimeMark 类型的定义应符合表 10。
     */
    private Integer maxEndTime;
    /**
     * 估计计算事件，表示当前时刻距离该相位状态结束的估计时间（不管当前时刻该相位状态是否开始）。
     * TimeMark 类型的定义应符合表 10。
     */
    private int likelyEndTime;
    /**
     * likelyEndTime的时间置信度水平，单位为 0.005。
     */
    private Integer timeConfidence;
    /**
     * 下一周期开始时间，表示当前时刻距离该相位状态下一次开始的估计时长。
     * TimeMark 类型的定义应符合表 10。
     */
    private Integer nextStartTime;
    /**
     * 下一周期持续时间，表示该相位状态下一次开始后的持续时长。
     * TimeMark 类型的定义应符合表 10。
     */
    private Integer nextDuration;
}
