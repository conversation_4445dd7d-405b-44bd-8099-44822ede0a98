package com.javaedge.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TrafficLightData {
    /**
     * 消息编号，编号数值为0~127,循环使用。
     */
    private long msgCnt;
    /**
     * 时间戳，消息广播的时间戳；单位为毫秒，UTC时间
     * 格式：YYYY-MM-DDThh:mm:ss.SSS+08:00
     */
    private String timeStamp;
    /**
     * 路口名称，可选。目前路侧没有
     */
    private String name;
    /**
     * 路口状态，IntersectionState 类型的定义应符合表 5。
     */
    private List<IntersectionState> intersections;
}