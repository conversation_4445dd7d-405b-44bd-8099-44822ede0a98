package com.javaedge.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UTCTiming {
    /**
     * 开始时间，如果当前该相位状态已开始（未结束），则该数值为当前状态开始的时刻；如果当前该相位状态未开始，则表示当前该相位状态开始的时刻。
     * TimeMark 类型的定义应符合表 10。
     */
    private int startUtcTime;
    /**
     * 最小结束时间，表示该相位状态以最短时间结束所对应的时刻。
     * TimeMark 类型的定义应符合表 10。
     */
    private Integer minEndUtcTime;
    /**
     * 最大结束时间，表示该相位状态以最长时间结束所对应的时刻。
     * TimeMark 类型的定义应符合表 10。
     */
    private Integer maxEndUtcTime;
    /**
     * 估计计算事件，表示该相位状态估计结束的时刻。
     * TimeMark 类型的定义应符合表 10。
     */
    private int likelyEndUtcTime;
    /**
     * likelyEndUtcTime的时间置信度水平，单位为 0.005。
     */
    private Integer timeConfidence;
    /**
     * 下一周期开始时间，表示该相位状态估计下一次开始的时刻。
     * TimeMark 类型的定义应符合表 10。
     */
    private Integer nextStartUtcTime;
    /**
     * 下一周期持续时间，表示该相位状态下一次开始后再结束的估计时刻。
     * TimeMark 类型的定义应符合表 10。
     */
    private Integer nextEndUtcTime;
}
