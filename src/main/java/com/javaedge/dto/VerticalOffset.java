package com.javaedge.dto;

/**
 * 海拔偏移量
 */
public class VerticalOffset {
    /**
     * 垂直范围为 ± 6.3 米
     */
    private Integer offset1;
    /**
     * 垂直范围为 ± 12.7 米
     */
    private Integer offset2;
    /**
     * 垂直范围为 ± 25.5 米
     */
    private Integer offset3;
    /**
     * 垂直范围为 ± 51.1 米
     */
    private Integer offset4;
    /**
     * 垂直范围为 ± 102.3 米
     */
    private Integer offset5;
    /**
     * 表示车辆所处的相对高度信息，单位:dm，取值范围(-4096~61439)其中-4096表示无效数据
     */
    private Integer elevation;

    public Integer getOffset1() { return offset1; }
    public void setOffset1(Integer offset1) { this.offset1 = offset1; }
    public Integer getOffset2() { return offset2; }
    public void setOffset2(Integer offset2) { this.offset2 = offset2; }
    public Integer getOffset3() { return offset3; }
    public void setOffset3(Integer offset3) { this.offset3 = offset3; }
    public Integer getOffset4() { return offset4; }
    public void setOffset4(Integer offset4) { this.offset4 = offset4; }
    public Integer getOffset5() { return offset5; }
    public void setOffset5(Integer offset5) { this.offset5 = offset5; }
    public Integer getElevation() { return elevation; }
    public void setElevation(Integer elevation) { this.elevation = elevation; }
} 