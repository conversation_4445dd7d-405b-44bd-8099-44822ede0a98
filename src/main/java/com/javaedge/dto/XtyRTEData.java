package com.javaedge.dto;

import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * 道路交通事件信息
 */
@Data
public class XtyRTEData {
    /**
     * 事件编号，唯一编号，到最大值循环，取值范围0~32767，必须
     */
    private Integer rteId;

    /**
     * 事件类型，长度4，取值按GB/T 29100-2012，详见附录B，必须
     */
    private String eventType;

    /**
     * 事件来源。参考 YD/T 3709-2020 中 EventSource 定义，“0”表示unknown，“1”表示police，“2”表示government，“3”表示meteorological，“4”表示internet，“5”表示detection
     * 可选
     */
    private String eventSource;

    /**
     * 相对位置，PositionOffsetLLV定义参照表18，必须
     */
    private PositionOffsetLLV eventPosition;

    /**
     * 事件影响半径，单位分米，分辨率为100分米，必须
     */
    private Integer eventRadius;

    /**
     * 事件描述，针对不同事件的进一步描述，针对不同事件类型进行定义。不允许为空字符串“”；中文采用 GB2312 编码， 其他为 ASCII 码，可选
     */
    private String description;

    /**
     * 时间信息，定义道路交通事件的生效时间属性，可选
     */
    private RSITimeDetails timeDetails;

    /**
     * 事件优先级，0-7，数字越大，级别越高，可选
     */
    private Integer priority;

    /**
     * 关联路径，ReferencePath 类型的定义应符合表16，可选
     */
    private List<ReferencePath> referencePaths;

    /**
     * 关联车道，ReferenceLink 类型的定义应符合表17，可选
     */
    private List<ReferenceLink> referenceLinks;

    /**
     * 事件置信度，单位为 0.005，最大值 200，可选
     */
    private Integer eventConfidence;
} 