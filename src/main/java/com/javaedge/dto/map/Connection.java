package com.javaedge.dto.map;

import lombok.Data;

/**
 * <AUTHOR>
 * 连接关系（信通院Connection数据帧）
 * remoteIntersection: 下游路段出口节点ID，NodeReferenceID类型
 * connectingLane: 连接的下游路段车道基本信息，ConnectingLane类型
 * phaseId: 信号灯相位号，0~255
 */
@Data
public class Connection {
    /** 下游路段出口节点ID，NodeReferenceID类型 */
    private com.javaedge.dto.NodeReferenceID remoteIntersection;
    /** 连接的下游路段车道基本信息，ConnectingLane类型 */
    private ConnectingLane connectingLane;
    /** 信号灯相位号，0~255 */
    private Integer phaseId;
} 