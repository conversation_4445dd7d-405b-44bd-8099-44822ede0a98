package com.javaedge.dto.map;

import java.util.List;
import com.javaedge.dto.PositionOffsetLLV;
import lombok.Data;

/**
 * <AUTHOR>
 * 车道（信通院Lane数据帧）
 * laneId: 车道ID
 * laneWidth: 车道宽度，分辨率1cm
 * laneAttributes: 共享属性，LaneAttributes类型
 * maneuvers: 车道的允许转向行为，参考AllowedManeuvers定义
 * connectsTo: 连接关系，Connection类型
 * speedLimits: 限速，SpeedLimit类型
 */
@Data
public class Lane {
    /** 车道ID */
    private int laneId;
    /** 车道宽度，分辨率1cm */
    private Integer laneWidth;
    /** 共享属性，LaneAttributes类型 */
    private LaneAttributes laneAttributes;
    /** 车道的允许转向行为，参考AllowedManeuvers定义 */
    private Integer maneuvers;
    /** 连接关系，Connection类型 */
    private List<Connection> connectsTo;
    /** 限速，SpeedLimit类型 */
    private List<SpeedLimit> speedLimits;
    private PositionOffsetLLV points;
    private List<StopLine> stopLines;
    private Integer guidedLaneWidth;
    private Integer guidedLaneLength;
    private String laneTypeAttrVehExt;
} 