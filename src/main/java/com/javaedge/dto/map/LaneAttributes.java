package com.javaedge.dto.map;

import lombok.Data;

/**
 * <AUTHOR>
 * 车道属性（信通院LaneAttributes数据帧）
 * shareWith: 共享属性，参考YD/T 3709-2020中LaneSharing定义
 * laneType: 车道属性类型，0:vehicle; 1:crosswalk; 2:nonvehicle; 3:sidewalk; 4:median; 5:striping; 6:trackedVehicle; 7:parking
 */
@Data
public class LaneAttributes {
    /** 共享属性，参考YD/T 3709-2020中LaneSharing定义 */
    private Integer shareWith;
    /** 车道属性类型，0:vehicle; 1:crosswalk; 2:nonvehicle; 3:sidewalk; 4:median; 5:striping; 6:trackedVehicle; 7:parking */
    private int laneType;
} 