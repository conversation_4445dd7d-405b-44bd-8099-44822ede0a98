package com.javaedge.dto.map;

import java.util.List;
import com.javaedge.dto.NodeReferenceID;
import com.javaedge.dto.PositionOffsetLLV;
import lombok.Data;

/**
 * <AUTHOR>
 * 路段Link（信通院Link数据帧）
 * name: 名称
 * upstreamNodeId: 上游节点ID，NodeReferenceID类型
 * speedLimits: 限速集合，SpeedLimit类型
 * linkWidth: link宽度，分辨率1cm
 * points: 相对位置，PositionOffsetLLV类型
 * movements: 转向，Movement类型
 * lanes: 车道，Lane类型
 */
@Data
public class Link {
    /** 名称 */
    private String name;
    /** 上游节点ID，NodeReferenceID类型 */
    private NodeReferenceID upstreamNodeId;
    /** 限速集合，SpeedLimit类型 */
    private List<SpeedLimit> speedLimits;
    /** link宽度，分辨率1cm */
    private int linkWidth;
    /** 相对位置，PositionOffsetLLV类型 */
    private PositionOffsetLLV points;
    /** 转向，Movement类型 */
    private List<Movement> movements;
    /** 车道，Lane类型 */
    private List<Lane> lanes;
}