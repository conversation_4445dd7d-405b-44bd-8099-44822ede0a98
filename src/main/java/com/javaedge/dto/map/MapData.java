package com.javaedge.dto.map;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * 云控基础平台对外提供的地图实时数据（信通院MAP数据帧）
 * msgCnt: 消息编号 0~127, 循环使用
 * timeStamp: 年度分钟数，分辨率1分钟，0~527040
 * nodes: 地图节点列表，Node类型
 */
@Data
public class MapData {
    /** 消息编号 0~127, 循环使用 */
    private int msgCnt;
    /** 年度分钟数，分辨率1分钟，0~527040 */
    private MinuteOfTheYear timeStamp;
    /** 地图节点列表，Node类型 */
    private List<Node> nodes;
} 