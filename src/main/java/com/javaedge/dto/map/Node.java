package com.javaedge.dto.map;

import java.util.List;
import com.javaedge.dto.NodeReferenceID;
import lombok.Data;

/**
 * <AUTHOR>
 * 地图节点（信通院Node数据帧）
 * name: 节点属性名称
 * id: 节点属性ID，NodeReferenceID类型
 * refPos: 节点属性位置，Position3D类型
 * inLinks: 节点上下游路段集合，Link类型
 */
@Data
public class Node {
    /** 节点属性名称 */
    private String name;
    /** 节点属性ID，NodeReferenceID类型 */
    private NodeReferenceID id;
    /** 节点属性位置，Position3D类型 */
    private Position3D refPos;
    /** 节点上下游路段集合，Link类型 */
    private List<Link> inLinks;
} 