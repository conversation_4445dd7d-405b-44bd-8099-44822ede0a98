package com.javaedge.dto.map;

import java.util.List;

/**
 * 停车线（信通院StopLine数据帧）
 * centerPoint: 停车线中点 [经度, 纬度, 高度]
 * type: 停车线属性，1：导向车道停车线；2：待行区停车线
 * sltSignal: 停车线转向信号列表，StopLineTurnSignal类型
 */
public class StopLine {
    /** 停车线中点 [经度, 纬度, 高度] */
    private double[] centerPoint;
    /** 停车线属性，1：导向车道停车线；2：待行区停车线 */
    private int type;
    /** 停车线转向信号列表，StopLineTurnSignal类型 */
    private List<StopLineTurnSignal> sltSignal;

    public double[] getCenterPoint() { return centerPoint; }
    public void setCenterPoint(double[] centerPoint) { this.centerPoint = centerPoint; }
    public int getType() { return type; }
    public void setType(int type) { this.type = type; }
    public List<StopLineTurnSignal> getSltSignal() { return sltSignal; }
    public void setSltSignal(List<StopLineTurnSignal> sltSignal) { this.sltSignal = sltSignal; }
} 