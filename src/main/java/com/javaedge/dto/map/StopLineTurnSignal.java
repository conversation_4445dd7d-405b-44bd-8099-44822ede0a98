package com.javaedge.dto.map;

/**
 * 停车线转向信号（信通院StopLineTurnSignal数据帧）
 * turn: 车道功能（允许转向），0:approachLane; 1:waitingArea
 * stopLineSignal: 停车线信号，0:none; 1:stop; 2:pass; 3:carefulPassage; 4:median; 5:stopBeforePassing
 */
public class StopLineTurnSignal {
    /** 车道功能（允许转向） */
    private int turn;
    /** 停车线信号 */
    private int stopLineSignal;

    public int getTurn() { return turn; }
    public void setTurn(int turn) { this.turn = turn; }
    public int getStopLineSignal() { return stopLineSignal; }
    public void setStopLineSignal(int stopLineSignal) { this.stopLineSignal = stopLineSignal; }
} 