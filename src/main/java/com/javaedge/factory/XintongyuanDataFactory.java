package com.javaedge.factory;

import com.alibaba.fastjson.JSONObject;

import com.javaedge.config.ConfigLoader;
import com.javaedge.config.MqttConfig;
import com.javaedge.dto.*;
import com.javaedge.dto.IntersectionState;
import com.javaedge.dto.map.*;
import com.javaedge.dto.map.ConnectingLane;
import com.javaedge.dto.map.Connection;
import com.javaedge.dto.map.Lane;
import com.javaedge.dto.map.LaneAttributes;
import com.javaedge.dto.map.Link;
import com.javaedge.dto.map.MapData;
import com.javaedge.dto.map.Movement;
import com.javaedge.dto.map.Node;
import com.javaedge.dto.map.Position3D;
import com.javaedge.utils.CoordinateConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import road.data.proto.*;
import road.data.proto.Phase;
import road.data.proto.PhaseState;
import road.data.proto.TimeCountingDown;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 */
@Slf4j
public class XintongyuanDataFactory {

    private static MqttConfig mqttConfig = new MqttConfig();
    static Map<String, Map<String, Object>> intersections = mqttConfig.getIntersections();
    static Map<Integer, Integer> phaseMappings = mqttConfig.getPhaseMappings();

    public static TrafficLightData toTrafficLightData(SpatData spatData, String topic) {
        TrafficLightData trafficLightData = new TrafficLightData();
        trafficLightData.setMsgCnt(spatData.getMsgCnt());
        trafficLightData.setTimeStamp(toUTCShanghai(spatData.getTimestamp()));
        List<IntersectionState> intersectionStates = new ArrayList<>();
        List<road.data.proto.IntersectionState> intersectionsList = spatData.getIntersectionsList();
        for (road.data.proto.IntersectionState spatInterState : intersectionsList) {
            IntersectionState xtyInterState = new IntersectionState();

            xtyInterState.setIntersectionId(getMappingIntersectionId(topic));
            xtyInterState.setStatus(14);
            List<com.javaedge.dto.Phase> xtyPhases = new ArrayList<>();
            List<Phase> spatPhasesList = spatInterState.getPhasesList();
            for (Phase spatPhase : spatPhasesList) {
                com.javaedge.dto.Phase xtyPhase = new com.javaedge.dto.Phase();

                xtyPhase.setPhaseId(getMappingPhaseId(spatPhase.getId()));
                // 构建PhaseState列表
                List<com.javaedge.dto.PhaseState> xtyPhaseStates = new ArrayList<>();
                List<PhaseState> spatPhaseStatesList = spatPhase.getPhaseStatesList();
                for (PhaseState spatPhaseState : spatPhaseStatesList) {
                    com.javaedge.dto.PhaseState xtyPhaseState = new com.javaedge.dto.PhaseState();
                    xtyPhaseState.setLight(spatPhaseState.getLight().getNumber());

                    TimeCountingDown spatPhaseStateTiming = spatPhaseState.getTiming();
                    com.javaedge.dto.TimeCountingDown xtyTimeCountingDown = new com.javaedge.dto.TimeCountingDown();
                    toTimeCountingdown(xtyTimeCountingDown, spatPhaseStateTiming);
                    xtyPhaseState.setCounting(xtyTimeCountingDown);
                    xtyPhaseStates.add(xtyPhaseState);
                }
                xtyPhase.setPhasestates(xtyPhaseStates);
                xtyPhases.add(xtyPhase);
            }
            xtyInterState.setPhases(xtyPhases);
            intersectionStates.add(xtyInterState);
        }
        trafficLightData.setIntersections(intersectionStates);
        return trafficLightData;
    }

    private static int getMappingPhaseId(int id) {
        Integer phaseId = phaseMappings.get(id);
        if (phaseId == null) {
            log.error("未找到相位映射，localPhaseId: {}", id);
        }
        return phaseId;
    }

    private static int getMappingIntersectionId(String topic) {
        AtomicReference<String> localNodeId = new AtomicReference<>();
        intersections.forEach((k, v) -> {
            if (topic.contains(k)) {
                localNodeId.set((String) v.get("nodeId"));
            }
        });
        return localNodeId.get() == null ? 0 : Integer.parseInt(localNodeId.get());
    }

    public static TrafficEventData toRTEData(List<RteData> rteDataList, String topic) {
        TrafficEventData data = new TrafficEventData();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'+08");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        data.setTimeStamp(sdf.format(new Date()));

        List<XtyRTEData> rtes = new ArrayList<>();
        for (RteData srcRteData : rteDataList) {
            XtyRTEData rte = new XtyRTEData();
            rte.setRteId(srcRteData.getRteId());


            String localEventType = String.valueOf(srcRteData.getRteType());
            String remoteEventType = ConfigLoader.getInstance().getRemoteEventCode(localEventType);
            if (StringUtils.isEmpty(remoteEventType)) {
                log.warn("未找到事件类型映射，localEventType: {}, remoteEventType: {}", localEventType, remoteEventType);
                return null;
            }
            rte.setEventType(remoteEventType);

            rte.setEventSource(String.valueOf(srcRteData.getEventSource().getNumber()));
            rte.setEventPosition(toEventAbsPos(srcRteData.getRtePos(), topic));
            rte.setDescription(srcRteData.getDescription());
            rtes.add(rte);
        }
        data.setRtes(rtes);
        return data;
    }

    private static PositionOffsetLLV toEventAbsPos(road.data.proto.Position3D rtePos, String topic) {
        AtomicReference<Double> refPosLatRef = new AtomicReference<>();
        AtomicReference<Double> refPosLonRef = new AtomicReference<>();

        intersections.forEach((k, v) -> {
            if (topic.contains(k)) {
                refPosLatRef.set((Double) v.get("refPosLat"));
                refPosLonRef.set((Double) v.get("refPosLon"));
            }
        });
        Double refPosLat = refPosLatRef.get();
        Double refPosLon = refPosLonRef.get();
        PositionOffsetLLV positionOffsetLLV = new PositionOffsetLLV();
        PositionOffsetLL positionOffsetLL = new PositionOffsetLL();
        Position2D positionLL1 = new Position2D();
        positionOffsetLLV.setOffsetLL(positionOffsetLL);
        if (refPosLat != null && refPosLon != null) {
            log.info("找到匹配路口，经度: {}, 纬度: {}", refPosLon, refPosLat);

            double lon = rtePos.getLon() / 1e7;
            double lat = rtePos.getLat() / 1e7;
            JSONObject wgs84ToGcj02 = CoordinateConversionUtils.wgs84ToGcj02(lon, lat);
            double rteLongitude = wgs84ToGcj02.getDoubleValue("lng");
            double rteLatitude = wgs84ToGcj02.getDoubleValue("lat");

            JSONObject refWgs84ToGcj02 = CoordinateConversionUtils.wgs84ToGcj02(refPosLon, refPosLat);
            double refLongitude = refWgs84ToGcj02.getDoubleValue("lng");
            double refLatitude = refWgs84ToGcj02.getDoubleValue("lat");
            positionLL1.setLongitude(BigDecimal.valueOf(rteLongitude - refLongitude));
            positionLL1.setLatitude(BigDecimal.valueOf(rteLatitude - refLatitude));
            positionOffsetLL.setPositionLL1(positionLL1); // 确定LL1
        } else {
            log.warn("未找到匹配的路口配置，topic: {}", topic);
        }
        return positionOffsetLLV;
    }

    private static void toTimeCountingdown(com.javaedge.dto.TimeCountingDown xtyTimeCountingDown, TimeCountingDown spatPhaseStateTiming) {
        xtyTimeCountingDown.setStartTime(spatPhaseStateTiming.getStartTime());
        xtyTimeCountingDown.setMinEndTime(spatPhaseStateTiming.getMinEndTime());
        xtyTimeCountingDown.setMaxEndTime(spatPhaseStateTiming.getMaxEndTime());
        xtyTimeCountingDown.setLikelyEndTime(spatPhaseStateTiming.getLikelyEndTime());
        xtyTimeCountingDown.setNextStartTime(spatPhaseStateTiming.getNextStartTime());
        xtyTimeCountingDown.setNextDuration(spatPhaseStateTiming.getNextDuration());
    }

    // 将timestamp转换为UTC+8的时间字符串
    private static String toUTCShanghai(long timestamp) {


        Date date = new Date(timestamp);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        return sdf.format(date);
    }

    /**
     * 将 proto MapData 转换为信通院格式 dto.map.MapData
     */
    public static MapData toMapData(MAP mapProto, String topic) {
        MapData mapData = new MapData();
        mapData.setMsgCnt(mapProto.getMsgCnt());
        MinuteOfTheYear minuteOfTheYear = new MinuteOfTheYear();
        minuteOfTheYear.setMinuteOfTheYear(mapProto.getTimestamp());
        mapData.setTimeStamp(minuteOfTheYear);
        List<Node> nodeList = new ArrayList<>();
        for (int i = 0; i < mapProto.getNodesCount(); i++) {
            nodeList.add(toNode(mapProto.getNodes(i), topic));
        }
        mapData.setNodes(nodeList);
        return mapData;
    }

    private static Node toNode(road.data.proto.Node protoNode, String topic) {
        if (protoNode == null) {
            return null;
        }
        Node node = new Node();
        node.setName(protoNode.getName());
        NodeReferenceID nodeReferenceID = new NodeReferenceID();
        nodeReferenceID.setIntersectionId(getMappingIntersectionId(topic));
        node.setId(nodeReferenceID);

        node.setRefPos(toXtyRefPosition(protoNode.getRefPos()));
        List<Link> inLinks = new ArrayList<>();
        for (int i = 0; i < protoNode.getInLinksCount(); i++) {
            inLinks.add(toLink(protoNode.getInLinks(i), topic));
        }
        node.setInLinks(inLinks);
        return node;
    }

    private static Position3D toXtyRefPosition(road.data.proto.Position3D refPos) {
        if (refPos == null) {
            return null;
        }
        Position3D position3D = new Position3D();
        position3D.setElevation((refPos.getEle()));
        position3D.setLongitude(change(refPos.getLon()));
        position3D.setLatitude(change(refPos.getLat()));
        return position3D;
    }

    private static BigDecimal change(int ele) {
        // 将入参整型数值小数点左移七位，保留七位小数精度
        return new BigDecimal(ele).divide(new BigDecimal(10000000), 7, RoundingMode.HALF_UP);
    }

    private static Link toLink(road.data.proto.Link protoLink, String topic) {
        if (protoLink == null) {
            return null;
        }
        Link link = new Link();
        link.setName(protoLink.getName());
        NodeReferenceID upstreamNodeId = new NodeReferenceID();
        //  TODO Map 各个 node 的对应其他路口的 nodeId 映射给全了吗？
        upstreamNodeId.setIntersectionId(getMappingIntersectionId(topic));
        link.setUpstreamNodeId(upstreamNodeId);

        List<SpeedLimit> speedLimits = new ArrayList<>();
        for (int i = 0; i < protoLink.getSpeedLimitsCount(); i++) {
            speedLimits.add(toSpeedLimit(protoLink.getSpeedLimits(i)));
        }
        link.setSpeedLimits(speedLimits);
        link.setLinkWidth(protoLink.getLinkWidth());

        // points略 FIXME

        link.setUpstreamNodeId(upstreamNodeId);

        List<Movement> movements = new ArrayList<>();
        for (int i = 0; i < protoLink.getMovementsCount(); i++) {
            movements.add(toMovement(protoLink.getMovements(i), topic));
        }
        link.setMovements(movements);
        List<Lane> lanes = new ArrayList<>();
        for (int i = 0; i < protoLink.getLanesCount(); i++) {
            lanes.add(toLane(protoLink.getLanes(i), topic));
        }
        link.setLanes(lanes);
        return link;
    }

    private static LaneAttributes toLaneAttributes(road.data.proto.LaneAttributes proto) {
        if (proto == null) {
            return null;
        }
        LaneAttributes attr = new LaneAttributes();
        attr.setShareWith(proto.hasShareWith() ? proto.getShareWith().getShareWith() : null);
        attr.setLaneType(proto.hasLaneType() ? proto.getLaneType().getChoiceId() : null);
        return attr;
    }

    private static Lane toLane(road.data.proto.Lane protoLane, String topic) {
        if (protoLane == null) {
            return null;
        }
        Lane lane = new Lane();
        lane.setLaneId(protoLane.getLaneId());
        lane.setLaneWidth(protoLane.getLaneWidth());
        lane.setLaneAttributes(toLaneAttributes(protoLane.getLaneAttributes()));

        // maneuvers需要将旧报文的值转换为二进制，左侧第x位数字为1，表示x方向上的通过。
        //常见转换映射：32,768：1   16,384：2  8,192：3   4,096：4    2,048：5   1,024：6   512：7256：8     128：9   64：10  32：11  16:12
        // 16384 转换为二进制是：100000000000000（15位二进制数）
        //
        //按照您的映射表：
        //
        //32,768：1（二进制：1000000000000000，第1位是1）
        //16,384：2（二进制：100000000000000，第2位是1）
        //8,192：3（二进制：10000000000000，第3位是1）
        //4,096：4（二进制：1000000000000，第4位是1）
        //...
        //所以16384的二进制表示中，从左侧数第2位是1，其余都是0。
        //
        //根据规则"左侧第x位数字为1，表示x方向上的通过"，因此表示方向2上的通过。

        lane.setManeuvers(protoLane.hasManeuvers() ? convertManeuversToDirection(protoLane.getManeuvers().getManeuver()) : null);

        List<Connection> connectsTo = new ArrayList<>();
        for (int i = 0; i < protoLane.getConnectsToCount(); i++) {
            connectsTo.add(toConnection(protoLane.getConnectsTo(i), topic));
        }
        lane.setConnectsTo(connectsTo);
        List<SpeedLimit> speedLimits = new ArrayList<>();
        for (int i = 0; i < protoLane.getSpeedLimitsCount(); i++) {
            speedLimits.add(toSpeedLimit(protoLane.getSpeedLimits(i)));
        }
        lane.setSpeedLimits(speedLimits);
        // TODO 缺失 points
        return lane;
    }

    private static Connection toConnection(road.data.proto.Connection protoConn, String topic) {
        if (protoConn == null) {
            return null;
        }
        Connection conn = new Connection();
        NodeReferenceID nodeReferenceID = new NodeReferenceID();
        // TODO 未给全
        nodeReferenceID.setIntersectionId(getMappingIntersectionId(topic));
        conn.setRemoteIntersection(nodeReferenceID);

        ConnectingLane connectingLane = new ConnectingLane();
        connectingLane.setLane(protoConn.getConnectingLane().getLane());
        connectingLane.setManeuvers(convertManeuversToDirection(protoConn.getConnectingLane().getManeuver().getManeuver()));
        conn.setConnectingLane(connectingLane);
        conn.setPhaseId(getMappingPhaseId(protoConn.getPhaseId()));
        return conn;
    }

    private static Movement toMovement(road.data.proto.Movement proto, String topic) {
        if (proto == null) {
            return null;
        }
        Movement movement = new Movement();
        NodeReferenceID nodeReferenceID = new NodeReferenceID();
        nodeReferenceID.setIntersectionId(getMappingIntersectionId(topic));
        movement.setRemoteIntersection(nodeReferenceID);
        movement.setPhaseId(getMappingPhaseId(proto.getPhaseId()));
        return movement;
    }

    private static SpeedLimit toSpeedLimit(RegulatorySpeedLimit proto) {
        if (proto == null) {
            return null;
        }
        SpeedLimit speedLimit = new SpeedLimit();
        speedLimit.setType(proto.getSpeedLimitTypeValue());
        speedLimit.setSpeed(proto.getSpeed());
        return speedLimit;
    }

    /**
     * 将maneuvers的原始数值转换为方向编号
     * 根据规则：左侧第x位数字为1，表示x方向上的通过
     * 常见转换映射：32,768：1   16,384：2  8,192：3   4,096：4    2,048：5   1,024：6   512：7   256：8     128：9   64：10  32：11  16：12
     */
    private static Integer convertManeuversToDirection(int maneuver) {
        if (maneuver == 0) {
            return null;
        }

        // 使用映射表进行转换
        Map<Integer, Integer> maneuverMap = new HashMap<>();
        maneuverMap.put(32768, 1);   // 二进制：1000000000000000，第1位是1
        maneuverMap.put(16384, 2);   // 二进制：100000000000000，第2位是1
        maneuverMap.put(8192, 3);    // 二进制：10000000000000，第3位是1
        maneuverMap.put(4096, 4);    // 二进制：1000000000000，第4位是1
        maneuverMap.put(2048, 5);    // 二进制：100000000000，第5位是1
        maneuverMap.put(1024, 6);    // 二进制：10000000000，第6位是1
        maneuverMap.put(512, 7);     // 二进制：1000000000，第7位是1
        maneuverMap.put(256, 8);     // 二进制：100000000，第8位是1
        maneuverMap.put(128, 9);     // 二进制：10000000，第9位是1
        maneuverMap.put(64, 10);     // 二进制：1000000，第10位是1
        maneuverMap.put(32, 11);     // 二进制：100000，第11位是1
        maneuverMap.put(16, 12);     // 二进制：10000，第12位是1

        // 查找对应的方向编号
        return maneuverMap.get(maneuver);
    }
}