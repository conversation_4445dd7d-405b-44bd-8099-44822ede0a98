package com.javaedge.mqtt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.javaedge.config.BrokerConfig;
import com.javaedge.config.MqttConfig;
import com.javaedge.dto.TrafficEventData;
import com.javaedge.dto.TrafficLightData;
import com.javaedge.dto.XtyRTEData;
import com.javaedge.deserializer.CamDataMessageDeserializer;
import com.javaedge.deserializer.DeserializationException;
import com.javaedge.factory.XintongyuanDataFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import road.data.proto.CamData;
import road.data.proto.MapData;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * 
 * 高性能双MQTT Broker消费者 [[memory:4816152]]
 * 支持同时连接和监听多个MQTT broker，提供高可用性和负载分担能力
 * 符合CAICT协议要求，支持CAM、MAP等V2X消息类型
 */
@Slf4j
public class DualMqttConsumer {
    
    private static final ExecutorService messageExecutor = Executors.newFixedThreadPool(40); // 增加线程池大小支持双broker
    private static final ExecutorService brokerExecutor = Executors.newCachedThreadPool(); // 用于管理多个broker连接
    private static final int RECONNECT_DELAY_MS = 5000;
    private static final AtomicInteger messageCounter = new AtomicInteger(0);
    
    // 存储多个MQTT客户端连接
    private final Map<String, MqttClient> mqttClients = new ConcurrentHashMap<>();
    private final Map<String, BrokerConfig> brokerConfigs = new ConcurrentHashMap<>();
    
    // RocketMQ生产者 - 共享实例
    private static DefaultMQProducer rocketmqProducer;
    
    // 配置管理
    private final MqttConfig mqttConfig;
    private final Map<String, Object> mqttConfigNodeIds;
    private final List<String> camTopics;
    private final List<String> mapTopics;
    
    static {
        // 初始化RocketMQ生产者
        try {
            rocketmqProducer = new DefaultMQProducer("dual_mqtt_producer_group");
            rocketmqProducer.setNamesrvAddr("140.206.168.62:56738;140.206.168.62:56739");
            rocketmqProducer.setRetryTimesWhenSendFailed(3);
            rocketmqProducer.setRetryTimesWhenSendAsyncFailed(3);
            rocketmqProducer.setSendMsgTimeout(6000);
            rocketmqProducer.start();
            log.info("RocketMQ生产者启动成功");
        } catch (Exception e) {
            log.error("RocketMQ生产者启动失败: {}", e.getMessage());
        }
    }
    
    public DualMqttConsumer() {
        this.mqttConfig = new MqttConfig();
        this.mqttConfigNodeIds = mqttConfig.getNodeIds();
        this.camTopics = mqttConfig.getTopics("cam");
        this.mapTopics = mqttConfig.getTopics("map");
    }
    
    /**
     * 启动双MQTT Broker消费者
     * 支持并发连接多个broker，提供高性能消息处理能力 [[memory:4816137]]
     */
    public void start() {
        List<BrokerConfig> configs = mqttConfig.getBrokerConfigs();
        
        if (configs.isEmpty()) {
            log.error("未找到有效的Broker配置");
            return;
        }
        
        log.info("开始启动双MQTT Broker消费者，共{}个broker", configs.size());
        
        // 并发启动多个broker连接
        for (BrokerConfig config : configs) {
            if (config.isEnabled()) {
                brokerExecutor.submit(() -> connectToBroker(config));
            }
        }
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));
        
        log.info("双MQTT Broker消费者启动完成");
    }
    
    /**
     * 连接到指定的MQTT Broker
     */
    private void connectToBroker(BrokerConfig brokerConfig) {
        String brokerId = brokerConfig.getDescription();
        String clientId = mqttConfig.getClientId() + "_" + brokerId + "_" + System.currentTimeMillis();
        
        try {
            log.info("正在连接到Broker: {} [{}]", brokerId, brokerConfig.getUrl());
            
            MqttClient client = new MqttClient(brokerConfig.getUrl(), clientId, new MemoryPersistence());
            
            // 设置连接选项
            MqttConnectOptions options = new MqttConnectOptions();
            options.setCleanSession(true);
            options.setUserName(brokerConfig.getUsername());
            options.setPassword(brokerConfig.getPassword().toCharArray());
            options.setConnectionTimeout(brokerConfig.getConnectionTimeout());
            options.setKeepAliveInterval(brokerConfig.getKeepAliveInterval());
            options.setAutomaticReconnect(brokerConfig.isAutomaticReconnect());
            
            // 设置回调
            client.setCallback(createMqttCallback(brokerId));
            
            // 连接
            client.connect(options);
            log.info("成功连接到Broker: {} [{}]", brokerId, brokerConfig.getUrl());
            
            // 存储客户端和配置
            mqttClients.put(brokerId, client);
            brokerConfigs.put(brokerId, brokerConfig);
            
            // 订阅主题
            subscribeToTopics(client, brokerId);
            
        } catch (Exception e) {
            log.error("连接Broker {}失败: {}", brokerId, e.getMessage());
            // 延迟重试
            scheduleReconnect(brokerConfig);
        }
    }
    
    /**
     * 创建MQTT回调处理器
     */
    private MqttCallback createMqttCallback(String brokerId) {
        return new MqttCallback() {
            @Override
            public void connectionLost(Throwable cause) {
                log.error("Broker {} 连接丢失: {}", brokerId, cause.getMessage());
                // 清理断开的连接
                mqttClients.remove(brokerId);
                // 自动重连会由Paho客户端处理
            }
            
            @Override
            public void messageArrived(String topic, MqttMessage message) {
                // 异步处理消息，避免阻塞
                messageExecutor.submit(() -> processMessage(brokerId, topic, message));
            }
            
            @Override
            public void deliveryComplete(IMqttDeliveryToken token) {
                // 发布消息完成回调（如果需要发布消息的话）
            }
        };
    }
    
    /**
     * 订阅主题
     */
    private void subscribeToTopics(MqttClient client, String brokerId) throws MqttException {
        int qos = 1;
        
        // 订阅CAM主题
        for (String topic : camTopics) {
            client.subscribe(topic, qos);
            log.info("Broker {} 订阅CAM主题: {}", brokerId, topic);
        }
        
        // 订阅MAP主题
        for (String topic : mapTopics) {
            client.subscribe(topic, qos);
            log.info("Broker {} 订阅MAP主题: {}", brokerId, topic);
        }
    }
    
    /**
     * 处理接收到的消息
     */
    private void processMessage(String brokerId, String topic, MqttMessage message) {
        try {
            int msgCount = messageCounter.incrementAndGet();
            log.debug("Broker {} 收到第{}条消息，主题: {}, 大小: {} bytes", 
                     brokerId, msgCount, topic, message.getPayload().length);
            
            byte[] payload = message.getPayload();
            
            if (topic.contains("/cam")) {
                processCamMessage(brokerId, topic, payload);
            } else if (topic.contains("/map")) {
                processMapMessage(brokerId, topic, payload);
            } else {
                log.warn("Broker {} 收到未知类型的消息主题: {}", brokerId, topic);
            }
            
        } catch (Exception e) {
            log.error("Broker {} 处理消息失败，主题: {}, 错误: {}", brokerId, topic, e.getMessage());
        }
    }
    
    /**
     * 处理CAM消息
     */
    private void processCamMessage(String brokerId, String topic, byte[] payload) {
        try {
            CamData camData = CamData.parseFrom(payload);
            log.debug("Broker {} 解析CAM数据成功，设备ID: {}", brokerId, camData.getDeviceId());
            
            // 创建TrafficEventData用于事件上报
            TrafficEventData trafficEventData = createTrafficEventFromCam(camData, topic);
            
            // 发送到RocketMQ
            if (trafficEventData != null) {
                sendToRocketMQ(trafficEventData, "TrafficEventData", brokerId);
            }
            
            // 简单记录CAM数据统计
            if (camData.hasRefPos()) {
                log.debug("Broker {} 处理CAM消息 - 设备ID: {}, 位置: [{}, {}]", 
                         brokerId, camData.getDeviceId(), 
                         camData.getRefPos().getLat() / 10000000.0, 
                         camData.getRefPos().getLon() / 10000000.0);
            } else {
                log.debug("Broker {} 处理CAM消息 - 设备ID: {}", brokerId, camData.getDeviceId());
            }
            
        } catch (Exception e) {
            log.error("Broker {} 处理CAM消息失败: {}", brokerId, e.getMessage());
        }
    }
    
    /**
     * 从CAM数据创建交通事件数据 [[memory:4816137]]
     */
    private TrafficEventData createTrafficEventFromCam(CamData camData, String topic) {
        try {
            TrafficEventData eventData = new TrafficEventData();
            
            // 设置时间戳
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'+08");
            eventData.setTimeStamp(sdf.format(new Date()));
            
            // 创建RTE数据列表
            List<XtyRTEData> rteList = new ArrayList<>();
            XtyRTEData rteData = new XtyRTEData();
            
            // 设置事件信息（使用简单的随机ID，实际应用中应该有更好的ID生成策略）
            rteData.setRteId((int)(System.currentTimeMillis() % 32767));
            rteData.setEventType("0801"); // 车辆事件类型
            rteData.setEventSource("5"); // detection
            rteData.setDescription("CAM车辆位置更新");
            rteData.setEventRadius(50); // 50分米影响半径
            
            rteList.add(rteData);
            eventData.setRtes(rteList);
            
            return eventData;
        } catch (Exception e) {
            log.error("创建CAM事件数据失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 处理MAP消息
     */
    private void processMapMessage(String brokerId, String topic, byte[] payload) {
        try {
            road.data.proto.MapData mapData = road.data.proto.MapData.parseFrom(payload);
            log.info("Broker {} 收到MAP消息，数据大小: {} bytes", brokerId, payload.length);
            
            // 创建简单的MAP事件记录
            TrafficEventData mapEventData = new TrafficEventData();
            
            // 设置时间戳
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'+08");
            mapEventData.setTimeStamp(sdf.format(new Date()));
            
            // 创建MAP事件的RTE数据
            List<XtyRTEData> rteList = new ArrayList<>();
            XtyRTEData mapRteData = new XtyRTEData();
            mapRteData.setRteId((int)(System.currentTimeMillis() % 32767));
            mapRteData.setEventType("0601"); // 基础设施事件类型
            mapRteData.setEventSource("5"); // detection
            mapRteData.setDescription("MAP路口地图数据更新 - 大小:" + payload.length + "字节");
            mapRteData.setEventRadius(100); // 100分米影响半径
            
            rteList.add(mapRteData);
            mapEventData.setRtes(rteList);
            
            // 发送MAP事件到RocketMQ
            sendToRocketMQ(mapEventData, "MapEventData", brokerId);
            
        } catch (Exception e) {
            log.error("Broker {} 处理MAP消息失败: {}", brokerId, e.getMessage());
        }
    }
    
    /**
     * 发送消息到RocketMQ
     */
    private void sendToRocketMQ(Object data, String dataType, String brokerId) {
        if (rocketmqProducer == null) {
            log.warn("RocketMQ生产者未初始化，跳过发送");
            return;
        }
        
        try {
            String jsonData = JSON.toJSONString(data, SerializerFeature.WriteMapNullValue);
            String topicName = "dual_mqtt_" + dataType.toLowerCase();
            
            // 使用正确的Message构造函数
            Message message = new Message();
            message.setTopic(topicName);
            message.setTags(brokerId + "_" + dataType);
            message.setKeys(brokerId);
            message.setBody(jsonData.getBytes());
            
            rocketmqProducer.sendOneway(message);
            log.debug("成功发送{}到RocketMQ，来源Broker: {}", dataType, brokerId);
            
        } catch (Exception e) {
            log.error("发送{}到RocketMQ失败，来源Broker: {}, 错误: {}", dataType, brokerId, e.getMessage());
        }
    }
    
    /**
     * 调度重连
     */
    private void scheduleReconnect(BrokerConfig brokerConfig) {
        brokerExecutor.submit(() -> {
            try {
                Thread.sleep(RECONNECT_DELAY_MS);
                connectToBroker(brokerConfig);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("重连任务被中断");
            }
        });
    }
    
    /**
     * 获取连接状态统计
     */
    public Map<String, Boolean> getConnectionStatus() {
        Map<String, Boolean> status = new ConcurrentHashMap<>();
        mqttClients.forEach((brokerId, client) -> {
            status.put(brokerId, client.isConnected());
        });
        return status;
    }
    
    /**
     * 获取消息统计
     */
    public int getMessageCount() {
        return messageCounter.get();
    }
    
    /**
     * 关闭所有连接
     */
    public void shutdown() {
        log.info("开始关闭双MQTT Broker消费者...");
        
        // 关闭所有MQTT客户端
        mqttClients.forEach((brokerId, client) -> {
            try {
                if (client.isConnected()) {
                    client.disconnect();
                    log.info("断开Broker连接: {}", brokerId);
                }
                client.close();
            } catch (Exception e) {
                log.error("关闭Broker {}连接失败: {}", brokerId, e.getMessage());
            }
        });
        
        // 关闭线程池
        messageExecutor.shutdown();
        brokerExecutor.shutdown();
        
        // 关闭RocketMQ生产者
        if (rocketmqProducer != null) {
            rocketmqProducer.shutdown();
            log.info("RocketMQ生产者已关闭");
        }
        
        log.info("双MQTT Broker消费者已关闭");
    }
    
    /**
     * 主方法 - 启动应用
     */
    public static void main(String[] args) {
        DualMqttConsumer consumer = new DualMqttConsumer();
        consumer.start();
        
        // 保持主线程运行
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            log.info("应用程序被中断");
            consumer.shutdown();
        }
    }
}