package com.javaedge.rocketmq;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * 
 * RocketMQ消费者演示类 [[memory:4816152]]
 * 用于验证MQTT消息推送到RocketMQ后能否正常被拉取消费
 * 主要目的是确认消息传输链路的完整性，消费逻辑保持简单
 */
@Slf4j
public class RocketMqConsumerDemo {
    
    private static final AtomicLong trafficLightCount = new AtomicLong(0);
    private static final AtomicLong trafficEventCount = new AtomicLong(0);
    
    /**
     * 启动RocketMQ消费者
     * 支持Java 8语法，确保兼容性 [[memory:4816137]]
     */
    public static void startConsumer() {
        try {
            // 创建消费者实例 - 用于消费交通灯数据
            DefaultMQPushConsumer trafficLightConsumer = new DefaultMQPushConsumer("traffic_light_consumer_group");
            trafficLightConsumer.setNamesrvAddr("140.206.168.62:56738;140.206.168.62:56739");
            trafficLightConsumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            trafficLightConsumer.setMessageModel(MessageModel.CLUSTERING);
            trafficLightConsumer.setConsumeThreadMin(2);
            trafficLightConsumer.setConsumeThreadMax(10);
            
            // 订阅交通灯数据主题
            trafficLightConsumer.subscribe("dual_mqtt_trafficlightdata", "*");
            
            // 设置消息监听器
            trafficLightConsumer.registerMessageListener(new MessageListenerConcurrently() {
                @Override
                public ConsumeConcurrentlyStatus consumeMessage(
                        List<MessageExt> messages,
                        ConsumeConcurrentlyContext context) {
                    
                    for (MessageExt message : messages) {
                        try {
                            long count = trafficLightCount.incrementAndGet();
                            String content = new String(message.getBody(), StandardCharsets.UTF_8);
                            
                            log.info("=== RocketMQ消费成功 ===");
                            log.info("第{}条交通灯数据消息", count);
                            log.info("主题: {}", message.getTopic());
                            log.info("标签: {}", message.getTags());
                            log.info("消息ID: {}", message.getMsgId());
                            log.info("队列ID: {}", message.getQueueId());
                            log.info("消息大小: {} bytes", message.getBody().length);
                            log.info("Broker来源: {}", new String(message.getKeys() != null ? message.getKeys().getBytes() : "unknown".getBytes()));
                            
                            // 简单验证JSON格式
                            if (content.contains("intersectionId") && content.contains("phases")) {
                                log.info("✓ 交通灯数据格式验证通过");
                            } else {
                                log.warn("⚠ 交通灯数据格式可能异常");
                            }
                            
                            log.info("消息内容预览: {}...", content.length() > 200 ? content.substring(0, 200) : content);
                            log.info("========================");
                            
                        } catch (Exception e) {
                            log.error("处理交通灯消息失败: {}", e.getMessage());
                            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                        }
                    }
                    
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
            });
            
            // 创建消费者实例 - 用于消费交通事件数据
            DefaultMQPushConsumer trafficEventConsumer = new DefaultMQPushConsumer("traffic_event_consumer_group");
            trafficEventConsumer.setNamesrvAddr("140.206.168.62:56738;140.206.168.62:56739");
            trafficEventConsumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
            trafficEventConsumer.setMessageModel(MessageModel.CLUSTERING);
            trafficEventConsumer.setConsumeThreadMin(2);
            trafficEventConsumer.setConsumeThreadMax(10);
            
            // 订阅交通事件数据主题
            trafficEventConsumer.subscribe("dual_mqtt_trafficeventdata", "*");
            
            // 设置消息监听器
            trafficEventConsumer.registerMessageListener(new MessageListenerConcurrently() {
                @Override
                public ConsumeConcurrentlyStatus consumeMessage(
                        List<MessageExt> messages,
                        ConsumeConcurrentlyContext context) {
                    
                    for (MessageExt message : messages) {
                        try {
                            long count = trafficEventCount.incrementAndGet();
                            String content = new String(message.getBody(), StandardCharsets.UTF_8);
                            
                            log.info("=== RocketMQ消费成功 ===");
                            log.info("第{}条交通事件数据消息", count);
                            log.info("主题: {}", message.getTopic());
                            log.info("标签: {}", message.getTags());
                            log.info("消息ID: {}", message.getMsgId());
                            log.info("队列ID: {}", message.getQueueId());
                            log.info("消息大小: {} bytes", message.getBody().length);
                            log.info("Broker来源: {}", new String(message.getKeys() != null ? message.getKeys().getBytes() : "unknown".getBytes()));
                            
                            // 简单验证JSON格式
                            if (content.contains("eventType") && content.contains("eventTime")) {
                                log.info("✓ 交通事件数据格式验证通过");
                            } else {
                                log.warn("⚠ 交通事件数据格式可能异常");
                            }
                            
                            log.info("消息内容预览: {}...", content.length() > 200 ? content.substring(0, 200) : content);
                            log.info("========================");
                            
                        } catch (Exception e) {
                            log.error("处理交通事件消息失败: {}", e.getMessage());
                            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                        }
                    }
                    
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
            });
            
            // 启动消费者
            trafficLightConsumer.start();
            trafficEventConsumer.start();
            
            log.info("RocketMQ消费者启动成功！");
            log.info("交通灯数据消费者组: traffic_light_consumer_group");
            log.info("交通事件数据消费者组: traffic_event_consumer_group");
            log.info("等待消息中...");
            
            // 添加关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                log.info("正在关闭RocketMQ消费者...");
                trafficLightConsumer.shutdown();
                trafficEventConsumer.shutdown();
                log.info("RocketMQ消费者已关闭");
                
                // 打印统计信息
                log.info("=== 消费统计 ===");
                log.info("交通灯数据消息总数: {}", trafficLightCount.get());
                log.info("交通事件数据消息总数: {}", trafficEventCount.get());
                log.info("消息消费总数: {}", trafficLightCount.get() + trafficEventCount.get());
                log.info("===============");
            }));
            
            // 定期打印统计信息
            Thread statsThread = new Thread(() -> {
                while (!Thread.currentThread().isInterrupted()) {
                    try {
                        Thread.sleep(30000); // 每30秒打印一次
                        long lightCount = trafficLightCount.get();
                        long eventCount = trafficEventCount.get();
                        long totalCount = lightCount + eventCount;
                        
                        if (totalCount > 0) {
                            log.info("【统计】已消费消息 - 交通灯: {}, 事件: {}, 总计: {}", 
                                   lightCount, eventCount, totalCount);
                        } else {
                            log.info("【等待】暂未收到消息，请检查MQTT生产者是否正常运行");
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            });
            statsThread.setDaemon(true);
            statsThread.start();
            
        } catch (Exception e) {
            log.error("启动RocketMQ消费者失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 获取消费统计信息
     */
    public static void printStats() {
        log.info("=== RocketMQ消费统计 ===");
        log.info("交通灯数据消息数: {}", trafficLightCount.get());
        log.info("交通事件数据消息数: {}", trafficEventCount.get());
        log.info("消息总数: {}", trafficLightCount.get() + trafficEventCount.get());
        log.info("====================");
    }
    
    /**
     * 主方法 - 独立运行RocketMQ消费者
     */
    public static void main(String[] args) {
        log.info("启动RocketMQ消费者演示程序...");
        
        startConsumer();
        
        // 保持主线程运行
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            log.info("RocketMQ消费者程序被中断");
        }
    }
}