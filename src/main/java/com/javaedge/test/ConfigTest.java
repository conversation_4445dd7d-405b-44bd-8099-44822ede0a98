package com.javaedge.test;

import com.javaedge.config.ConfigLoader;

import java.util.Map;

/**
 * 配置测试类
 */
public class ConfigTest {
    
    public static void main(String[] args) {
        System.out.println("=== 开始测试配置加载功能 ===");
        
        // 获取配置加载器实例
        ConfigLoader configLoader = ConfigLoader.getInstance();
        
        // 获取所有映射关系
        Map<String, String> mappings = configLoader.getAllEventTypeMappings();
        System.out.println("\n所有事件类型映射关系：");
        for (Map.Entry<String, String> entry : mappings.entrySet()) {
            System.out.println("本地代码: " + entry.getKey() + " -> 远程代码: " + entry.getValue());
        }
        
        // 测试具体的映射
        System.out.println("\n=== 测试具体映射 ===");
        String[] testCodes = {"913", "412", "901", "902", "904", "405", "907", "707", "999"};
        
        for (String testCode : testCodes) {
            String remoteCode = configLoader.getRemoteEventCode(testCode);
            System.out.println("本地代码 " + testCode + " 映射为远程代码: " + remoteCode);
        }
        
        System.out.println("\n=== 配置测试完成 ===");
    }
} 