package com.javaedge.test;

import com.google.protobuf.util.JsonFormat;
import road.data.proto.MAP;

public class JsonParseTest {

    /**
     * 清理JSON中的错误嵌套结构
     */
    private static String cleanupNestedInLinksEx(String json) {
        // 检测并修复嵌套的inLinksEx问题
        String cleaned = json;

        // 查找第一个完整的inLinksEx数组结束位置
        int firstInLinksExStart = cleaned.indexOf("\"inLinksEx\":[");
        if (firstInLinksExStart != -1) {
            // 找到第一个inLinksEx的结束位置
            int bracketCount = 0;
            int pos = firstInLinksExStart + "\"inLinksEx\":[".length();
            boolean inString = false;
            boolean escaped = false;

            while (pos < cleaned.length()) {
                char c = cleaned.charAt(pos);

                if (escaped) {
                    escaped = false;
                } else if (c == '\\') {
                    escaped = true;
                } else if (c == '"') {
                    inString = !inString;
                } else if (!inString) {
                    if (c == '[') {
                        bracketCount++;
                    } else if (c == ']') {
                        bracketCount--;
                        if (bracketCount == 0) {
                            // 找到了匹配的结束括号
                            String beforeInLinksEx = cleaned.substring(0, firstInLinksExStart);
                            String inLinksExPart = cleaned.substring(firstInLinksExStart, pos + 1);
                            String afterInLinksEx = cleaned.substring(pos + 1);

                            // 移除后面可能的重复inLinksEx
                            int nextInLinksExStart = afterInLinksEx.indexOf(",\"inLinksEx\":[");
                            if (nextInLinksExStart != -1) {
                                afterInLinksEx = afterInLinksEx.substring(0, nextInLinksExStart);
                            }

                            cleaned = beforeInLinksEx + inLinksExPart + afterInLinksEx;
                            break;
                        }
                    }
                }
                pos++;
            }
        }

        return cleaned;
    }

    /**
     * 处理 JSON 字段名映射问题
     */
    private static String convertJsonFieldNames(String json) {
        String result = json;

        // 只处理基本的字段映射
        result = result.replaceAll("\"id\":\\s*\\{\\s*\"id\":", "\"id\":{\"nodeId\":");
        result = result.replaceAll("\"remoteIntersection\":\\s*\\{\\s*\"id\":", "\"remoteIntersection\":{\"nodeId\":");
        result = result.replaceAll("\"upstreamNodeId\":\\s*\\{\\s*\"id\":", "\"upstreamNodeId\":{\"nodeId\":");
        result = result.replaceAll(
                "\"laneType\":\\s*\\{\\s*\"motorVehicleLanes\":\\s*\\{\\s*\"motorVehicleLanes\":\\s*(\\d+)\\s*\\}\\s*\\}",
                "\"laneType\":{\"choiceId\":10,\"value\":{\"motorVehicleLanes\":{\"motorVehicleLanes\":$1}}}");

        return result;
    }

    public static void main(String[] args) {
        // 测试用的简化JSON数据
        String originalJson = "{\"msgCnt\":1,\"nodes\":[{\"id\":{\"id\":350,\"region\":1627},\"name\":\"墨玉南路-博园路\",\"refPos\":{\"ele\":0,\"lat\":312861776,\"lon\":1211713032}}],\"timestamp\":1749710343787}";

        try {
            System.out.println("开始测试JSON解析...");

            // 清理嵌套结构
            String cleanedJson = cleanupNestedInLinksEx(originalJson);
            System.out.println("清理后JSON: " + cleanedJson);

            // 转换时间戳为分钟数
            long timestampMillis = 1749710343787L;
            long timestampMinutes = timestampMillis / 1000 / 60;

            System.out.println("原始时间戳（毫秒）: " + timestampMillis);
            System.out.println("转换后（分钟数）: " + timestampMinutes);

            // 先处理字段名映射
            String processedJson = convertJsonFieldNames(cleanedJson);

            // 然后替换时间戳
            processedJson = processedJson.replaceAll("\"timestamp\":\\s*\\d+", "\"timestamp\": " + timestampMinutes);

            System.out.println("处理后的JSON: " + processedJson);

            // 使用protobuf解析
            MAP.Builder mapBuilder = MAP.newBuilder();

            JsonFormat.Parser parser = JsonFormat.parser()
                .ignoringUnknownFields();

            parser.merge(processedJson, mapBuilder);
            MAP mapData = mapBuilder.build();

            System.out.println("========== 解析成功! ==========");
            System.out.println("msgCnt: " + mapData.getMsgCnt());
            System.out.println("timestamp: " + mapData.getTimestamp());
            System.out.println("nodes size: " + mapData.getNodesCount());

            if (mapData.getNodesCount() > 0) {
                System.out.println("第一个节点信息:");
                System.out.println("  - 节点ID: " + mapData.getNodes(0).getId().getNodeId());
                System.out.println("  - 节点名称: " + mapData.getNodes(0).getName());
                System.out.println("  - 坐标: lat=" + mapData.getNodes(0).getRefPos().getLat() +
                                 ", lon=" + mapData.getNodes(0).getRefPos().getLon() +
                                 ", ele=" + mapData.getNodes(0).getRefPos().getEle());
            }

            System.out.println("========== 问题已修复! ==========");

        } catch (Exception e) {
            System.err.println("解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
