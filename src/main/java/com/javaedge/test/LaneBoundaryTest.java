package com.javaedge.test;

import com.google.protobuf.util.JsonFormat;
import road.data.proto.LaneBoundary;
import road.data.proto.Position3D;

public class LaneBoundaryTest {

    /**
     * 处理 JSON 字段名映射问题
     */
    private static String convertJsonFieldNames(String json) {
        String result = json;

        // 处理 LaneBoundary 中的字段名映射
        // 在 LaneBoundary 中，"points" 需要映射为 "laneBoundaryPoints"
        // 先处理 type 字段，避免与其他地方的 type 混淆
        // 在包含 points 数组的对象中，将 type 替换为 laneBoundaryType
        result = result.replaceAll("(\\{[^}]*\"points\"[^}]*),\"type\":", "$1,\"laneBoundaryType\":");

        // 然后处理 points 字段
        result = result.replaceAll("\"points\":", "\"laneBoundaryPoints\":");

        return result;
    }

    public static void main(String[] args) {
        // 测试 LaneBoundary 的 JSON 解析
        String laneBoundaryJson = "{\"points\":[{\"ele\":0,\"lat\":312754899,\"lon\":1211603459},{\"ele\":0,\"lat\":312759361,\"lon\":1211603806}],\"type\":10}";

        System.out.println("原始 JSON:");
        System.out.println(laneBoundaryJson);

        // 应用字段名映射
        String processedJson = convertJsonFieldNames(laneBoundaryJson);
        System.out.println("\n处理后的 JSON:");
        System.out.println(processedJson);

        try {
            // 使用 Protobuf 的 JsonFormat 来解析 JSON
            LaneBoundary.Builder builder = LaneBoundary.newBuilder();
            JsonFormat.parser().merge(processedJson, builder);
            LaneBoundary laneBoundary = builder.build();

            System.out.println("\n解析成功!");
            System.out.println("laneBoundaryType: " + laneBoundary.getLaneBoundaryType());
            System.out.println("laneBoundaryPoints count: " + laneBoundary.getLaneBoundaryPointsCount());

            for (int i = 0; i < laneBoundary.getLaneBoundaryPointsCount(); i++) {
                Position3D point = laneBoundary.getLaneBoundaryPoints(i);
                System.out.println("Point " + i + ": lat=" + point.getLat() + ", lon=" + point.getLon() + ", ele=" + point.getEle());
            }

        } catch (Exception e) {
            System.out.println("\n解析失败:");
            e.printStackTrace();
        }
    }
}
