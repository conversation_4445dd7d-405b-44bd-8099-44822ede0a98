// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *加速度精度 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.AccelerationConfidence}
 */
public  final class AccelerationConfidence extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.AccelerationConfidence)
    AccelerationConfidenceOrBuilder {
private static final long serialVersionUID = 0L;
  // Use AccelerationConfidence.newBuilder() to construct.
  private AccelerationConfidence(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private AccelerationConfidence() {
    lonAccelConfid_ = 0;
    latAccelConfid_ = 0;
    verticalAccelConfid_ = 0;
    yawRateConfid_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new AccelerationConfidence();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private AccelerationConfidence(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {
            int rawValue = input.readEnum();

            lonAccelConfid_ = rawValue;
            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            latAccelConfid_ = rawValue;
            break;
          }
          case 24: {
            int rawValue = input.readEnum();

            verticalAccelConfid_ = rawValue;
            break;
          }
          case 32: {
            int rawValue = input.readEnum();

            yawRateConfid_ = rawValue;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_AccelerationConfidence_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_AccelerationConfidence_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.AccelerationConfidence.class, road.data.proto.AccelerationConfidence.Builder.class);
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence}
   */
  public enum AccConfidence
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * __ Not Equipped or unavailable
     * </pre>
     *
     * <code>ACC_CONFID_UNAVAILABLE = 0;</code>
     */
    ACC_CONFID_UNAVAILABLE(0),
    /**
     * <pre>
     * 100 m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC100DE = 1;</code>
     */
    ACC_CONFID_PREC100DE(1),
    /**
     * <pre>
     * 10 m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC10DEG = 2;</code>
     */
    ACC_CONFID_PREC10DEG(2),
    /**
     * <pre>
     * 5 m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC5DEG = 3;</code>
     */
    ACC_CONFID_PREC5DEG(3),
    /**
     * <pre>
     * 1 m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC1DEG = 4;</code>
     */
    ACC_CONFID_PREC1DEG(4),
    /**
     * <pre>
     * 0.1 m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC0_1DEG = 5;</code>
     */
    ACC_CONFID_PREC0_1DEG(5),
    /**
     * <pre>
     * 0.05 m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC0_05DEG = 6;</code>
     */
    ACC_CONFID_PREC0_05DEG(6),
    /**
     * <pre>
     *  0.01m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC0_01DEG = 7;</code>
     */
    ACC_CONFID_PREC0_01DEG(7),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * __ Not Equipped or unavailable
     * </pre>
     *
     * <code>ACC_CONFID_UNAVAILABLE = 0;</code>
     */
    public static final int ACC_CONFID_UNAVAILABLE_VALUE = 0;
    /**
     * <pre>
     * 100 m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC100DE = 1;</code>
     */
    public static final int ACC_CONFID_PREC100DE_VALUE = 1;
    /**
     * <pre>
     * 10 m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC10DEG = 2;</code>
     */
    public static final int ACC_CONFID_PREC10DEG_VALUE = 2;
    /**
     * <pre>
     * 5 m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC5DEG = 3;</code>
     */
    public static final int ACC_CONFID_PREC5DEG_VALUE = 3;
    /**
     * <pre>
     * 1 m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC1DEG = 4;</code>
     */
    public static final int ACC_CONFID_PREC1DEG_VALUE = 4;
    /**
     * <pre>
     * 0.1 m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC0_1DEG = 5;</code>
     */
    public static final int ACC_CONFID_PREC0_1DEG_VALUE = 5;
    /**
     * <pre>
     * 0.05 m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC0_05DEG = 6;</code>
     */
    public static final int ACC_CONFID_PREC0_05DEG_VALUE = 6;
    /**
     * <pre>
     *  0.01m/s2
     * </pre>
     *
     * <code>ACC_CONFID_PREC0_01DEG = 7;</code>
     */
    public static final int ACC_CONFID_PREC0_01DEG_VALUE = 7;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static AccConfidence valueOf(int value) {
      return forNumber(value);
    }

    public static AccConfidence forNumber(int value) {
      switch (value) {
        case 0: return ACC_CONFID_UNAVAILABLE;
        case 1: return ACC_CONFID_PREC100DE;
        case 2: return ACC_CONFID_PREC10DEG;
        case 3: return ACC_CONFID_PREC5DEG;
        case 4: return ACC_CONFID_PREC1DEG;
        case 5: return ACC_CONFID_PREC0_1DEG;
        case 6: return ACC_CONFID_PREC0_05DEG;
        case 7: return ACC_CONFID_PREC0_01DEG;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<AccConfidence>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        AccConfidence> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<AccConfidence>() {
            public AccConfidence findValueByNumber(int number) {
              return AccConfidence.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.AccelerationConfidence.getDescriptor().getEnumTypes().get(0);
    }

    private static final AccConfidence[] VALUES = values();

    public static AccConfidence valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private AccConfidence(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence)
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.AccelerationConfidence.AngularVConfidence}
   */
  public enum AngularVConfidence
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *NOT EQUIPPED OR UNAVAILABLE
     * </pre>
     *
     * <code>ANGULARV_CONFID_UNAVAILABLE = 0;</code>
     */
    ANGULARV_CONFID_UNAVAILABLE(0),
    /**
     * <pre>
     *100 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC100DEG = 1;</code>
     */
    ANGULARV_CONFID_PREC100DEG(1),
    /**
     * <pre>
     *10 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC10DEG = 2;</code>
     */
    ANGULARV_CONFID_PREC10DEG(2),
    /**
     * <pre>
     *5 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC5DEG = 3;</code>
     */
    ANGULARV_CONFID_PREC5DEG(3),
    /**
     * <pre>
     *1 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC1DEG = 4;</code>
     */
    ANGULARV_CONFID_PREC1DEG(4),
    /**
     * <pre>
     * 0.1 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC0_1DEG = 5;</code>
     */
    ANGULARV_CONFID_PREC0_1DEG(5),
    /**
     * <pre>
     * 0.05 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC0_05DEG = 6;</code>
     */
    ANGULARV_CONFID_PREC0_05DEG(6),
    /**
     * <pre>
     * 0.01 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC0_01DEG = 7;</code>
     */
    ANGULARV_CONFID_PREC0_01DEG(7),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *NOT EQUIPPED OR UNAVAILABLE
     * </pre>
     *
     * <code>ANGULARV_CONFID_UNAVAILABLE = 0;</code>
     */
    public static final int ANGULARV_CONFID_UNAVAILABLE_VALUE = 0;
    /**
     * <pre>
     *100 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC100DEG = 1;</code>
     */
    public static final int ANGULARV_CONFID_PREC100DEG_VALUE = 1;
    /**
     * <pre>
     *10 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC10DEG = 2;</code>
     */
    public static final int ANGULARV_CONFID_PREC10DEG_VALUE = 2;
    /**
     * <pre>
     *5 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC5DEG = 3;</code>
     */
    public static final int ANGULARV_CONFID_PREC5DEG_VALUE = 3;
    /**
     * <pre>
     *1 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC1DEG = 4;</code>
     */
    public static final int ANGULARV_CONFID_PREC1DEG_VALUE = 4;
    /**
     * <pre>
     * 0.1 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC0_1DEG = 5;</code>
     */
    public static final int ANGULARV_CONFID_PREC0_1DEG_VALUE = 5;
    /**
     * <pre>
     * 0.05 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC0_05DEG = 6;</code>
     */
    public static final int ANGULARV_CONFID_PREC0_05DEG_VALUE = 6;
    /**
     * <pre>
     * 0.01 DEGREE/SEC
     * </pre>
     *
     * <code>ANGULARV_CONFID_PREC0_01DEG = 7;</code>
     */
    public static final int ANGULARV_CONFID_PREC0_01DEG_VALUE = 7;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static AngularVConfidence valueOf(int value) {
      return forNumber(value);
    }

    public static AngularVConfidence forNumber(int value) {
      switch (value) {
        case 0: return ANGULARV_CONFID_UNAVAILABLE;
        case 1: return ANGULARV_CONFID_PREC100DEG;
        case 2: return ANGULARV_CONFID_PREC10DEG;
        case 3: return ANGULARV_CONFID_PREC5DEG;
        case 4: return ANGULARV_CONFID_PREC1DEG;
        case 5: return ANGULARV_CONFID_PREC0_1DEG;
        case 6: return ANGULARV_CONFID_PREC0_05DEG;
        case 7: return ANGULARV_CONFID_PREC0_01DEG;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<AngularVConfidence>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        AngularVConfidence> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<AngularVConfidence>() {
            public AngularVConfidence findValueByNumber(int number) {
              return AngularVConfidence.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.AccelerationConfidence.getDescriptor().getEnumTypes().get(1);
    }

    private static final AngularVConfidence[] VALUES = values();

    public static AngularVConfidence valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private AngularVConfidence(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.AccelerationConfidence.AngularVConfidence)
  }

  public static final int LONACCELCONFID_FIELD_NUMBER = 1;
  private int lonAccelConfid_;
  /**
   * <pre>
   * 定义车辆横向加速度精度。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence lonAccelConfid = 1;</code>
   */
  public int getLonAccelConfidValue() {
    return lonAccelConfid_;
  }
  /**
   * <pre>
   * 定义车辆横向加速度精度。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence lonAccelConfid = 1;</code>
   */
  public road.data.proto.AccelerationConfidence.AccConfidence getLonAccelConfid() {
    @SuppressWarnings("deprecation")
    road.data.proto.AccelerationConfidence.AccConfidence result = road.data.proto.AccelerationConfidence.AccConfidence.valueOf(lonAccelConfid_);
    return result == null ? road.data.proto.AccelerationConfidence.AccConfidence.UNRECOGNIZED : result;
  }

  public static final int LATACCELCONFID_FIELD_NUMBER = 2;
  private int latAccelConfid_;
  /**
   * <pre>
   * 定义车辆纵向加速度精度。取值同上
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence latAccelConfid = 2;</code>
   */
  public int getLatAccelConfidValue() {
    return latAccelConfid_;
  }
  /**
   * <pre>
   * 定义车辆纵向加速度精度。取值同上
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence latAccelConfid = 2;</code>
   */
  public road.data.proto.AccelerationConfidence.AccConfidence getLatAccelConfid() {
    @SuppressWarnings("deprecation")
    road.data.proto.AccelerationConfidence.AccConfidence result = road.data.proto.AccelerationConfidence.AccConfidence.valueOf(latAccelConfid_);
    return result == null ? road.data.proto.AccelerationConfidence.AccConfidence.UNRECOGNIZED : result;
  }

  public static final int VERTICALACCELCONFID_FIELD_NUMBER = 3;
  private int verticalAccelConfid_;
  /**
   * <pre>
   * 定义Z轴方向的加速度精度。取值同上
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence verticalAccelConfid = 3;</code>
   */
  public int getVerticalAccelConfidValue() {
    return verticalAccelConfid_;
  }
  /**
   * <pre>
   * 定义Z轴方向的加速度精度。取值同上
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence verticalAccelConfid = 3;</code>
   */
  public road.data.proto.AccelerationConfidence.AccConfidence getVerticalAccelConfid() {
    @SuppressWarnings("deprecation")
    road.data.proto.AccelerationConfidence.AccConfidence result = road.data.proto.AccelerationConfidence.AccConfidence.valueOf(verticalAccelConfid_);
    return result == null ? road.data.proto.AccelerationConfidence.AccConfidence.UNRECOGNIZED : result;
  }

  public static final int YAWRATECONFID_FIELD_NUMBER = 4;
  private int yawRateConfid_;
  /**
   * <pre>
   * 车辆摆角速度精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AngularVConfidence yawRateConfid = 4;</code>
   */
  public int getYawRateConfidValue() {
    return yawRateConfid_;
  }
  /**
   * <pre>
   * 车辆摆角速度精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AngularVConfidence yawRateConfid = 4;</code>
   */
  public road.data.proto.AccelerationConfidence.AngularVConfidence getYawRateConfid() {
    @SuppressWarnings("deprecation")
    road.data.proto.AccelerationConfidence.AngularVConfidence result = road.data.proto.AccelerationConfidence.AngularVConfidence.valueOf(yawRateConfid_);
    return result == null ? road.data.proto.AccelerationConfidence.AngularVConfidence.UNRECOGNIZED : result;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (lonAccelConfid_ != road.data.proto.AccelerationConfidence.AccConfidence.ACC_CONFID_UNAVAILABLE.getNumber()) {
      output.writeEnum(1, lonAccelConfid_);
    }
    if (latAccelConfid_ != road.data.proto.AccelerationConfidence.AccConfidence.ACC_CONFID_UNAVAILABLE.getNumber()) {
      output.writeEnum(2, latAccelConfid_);
    }
    if (verticalAccelConfid_ != road.data.proto.AccelerationConfidence.AccConfidence.ACC_CONFID_UNAVAILABLE.getNumber()) {
      output.writeEnum(3, verticalAccelConfid_);
    }
    if (yawRateConfid_ != road.data.proto.AccelerationConfidence.AngularVConfidence.ANGULARV_CONFID_UNAVAILABLE.getNumber()) {
      output.writeEnum(4, yawRateConfid_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (lonAccelConfid_ != road.data.proto.AccelerationConfidence.AccConfidence.ACC_CONFID_UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(1, lonAccelConfid_);
    }
    if (latAccelConfid_ != road.data.proto.AccelerationConfidence.AccConfidence.ACC_CONFID_UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, latAccelConfid_);
    }
    if (verticalAccelConfid_ != road.data.proto.AccelerationConfidence.AccConfidence.ACC_CONFID_UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, verticalAccelConfid_);
    }
    if (yawRateConfid_ != road.data.proto.AccelerationConfidence.AngularVConfidence.ANGULARV_CONFID_UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(4, yawRateConfid_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.AccelerationConfidence)) {
      return super.equals(obj);
    }
    road.data.proto.AccelerationConfidence other = (road.data.proto.AccelerationConfidence) obj;

    if (lonAccelConfid_ != other.lonAccelConfid_) return false;
    if (latAccelConfid_ != other.latAccelConfid_) return false;
    if (verticalAccelConfid_ != other.verticalAccelConfid_) return false;
    if (yawRateConfid_ != other.yawRateConfid_) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + LONACCELCONFID_FIELD_NUMBER;
    hash = (53 * hash) + lonAccelConfid_;
    hash = (37 * hash) + LATACCELCONFID_FIELD_NUMBER;
    hash = (53 * hash) + latAccelConfid_;
    hash = (37 * hash) + VERTICALACCELCONFID_FIELD_NUMBER;
    hash = (53 * hash) + verticalAccelConfid_;
    hash = (37 * hash) + YAWRATECONFID_FIELD_NUMBER;
    hash = (53 * hash) + yawRateConfid_;
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.AccelerationConfidence parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.AccelerationConfidence parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.AccelerationConfidence parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.AccelerationConfidence parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.AccelerationConfidence parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.AccelerationConfidence parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.AccelerationConfidence parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.AccelerationConfidence parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.AccelerationConfidence parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.AccelerationConfidence parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.AccelerationConfidence parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.AccelerationConfidence parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.AccelerationConfidence prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *加速度精度 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.AccelerationConfidence}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.AccelerationConfidence)
      road.data.proto.AccelerationConfidenceOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_AccelerationConfidence_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_AccelerationConfidence_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.AccelerationConfidence.class, road.data.proto.AccelerationConfidence.Builder.class);
    }

    // Construct using road.data.proto.AccelerationConfidence.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      lonAccelConfid_ = 0;

      latAccelConfid_ = 0;

      verticalAccelConfid_ = 0;

      yawRateConfid_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_AccelerationConfidence_descriptor;
    }

    @java.lang.Override
    public road.data.proto.AccelerationConfidence getDefaultInstanceForType() {
      return road.data.proto.AccelerationConfidence.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.AccelerationConfidence build() {
      road.data.proto.AccelerationConfidence result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.AccelerationConfidence buildPartial() {
      road.data.proto.AccelerationConfidence result = new road.data.proto.AccelerationConfidence(this);
      result.lonAccelConfid_ = lonAccelConfid_;
      result.latAccelConfid_ = latAccelConfid_;
      result.verticalAccelConfid_ = verticalAccelConfid_;
      result.yawRateConfid_ = yawRateConfid_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.AccelerationConfidence) {
        return mergeFrom((road.data.proto.AccelerationConfidence)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.AccelerationConfidence other) {
      if (other == road.data.proto.AccelerationConfidence.getDefaultInstance()) return this;
      if (other.lonAccelConfid_ != 0) {
        setLonAccelConfidValue(other.getLonAccelConfidValue());
      }
      if (other.latAccelConfid_ != 0) {
        setLatAccelConfidValue(other.getLatAccelConfidValue());
      }
      if (other.verticalAccelConfid_ != 0) {
        setVerticalAccelConfidValue(other.getVerticalAccelConfidValue());
      }
      if (other.yawRateConfid_ != 0) {
        setYawRateConfidValue(other.getYawRateConfidValue());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.AccelerationConfidence parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.AccelerationConfidence) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int lonAccelConfid_ = 0;
    /**
     * <pre>
     * 定义车辆横向加速度精度。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence lonAccelConfid = 1;</code>
     */
    public int getLonAccelConfidValue() {
      return lonAccelConfid_;
    }
    /**
     * <pre>
     * 定义车辆横向加速度精度。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence lonAccelConfid = 1;</code>
     */
    public Builder setLonAccelConfidValue(int value) {
      lonAccelConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 定义车辆横向加速度精度。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence lonAccelConfid = 1;</code>
     */
    public road.data.proto.AccelerationConfidence.AccConfidence getLonAccelConfid() {
      @SuppressWarnings("deprecation")
      road.data.proto.AccelerationConfidence.AccConfidence result = road.data.proto.AccelerationConfidence.AccConfidence.valueOf(lonAccelConfid_);
      return result == null ? road.data.proto.AccelerationConfidence.AccConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 定义车辆横向加速度精度。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence lonAccelConfid = 1;</code>
     */
    public Builder setLonAccelConfid(road.data.proto.AccelerationConfidence.AccConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      lonAccelConfid_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 定义车辆横向加速度精度。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence lonAccelConfid = 1;</code>
     */
    public Builder clearLonAccelConfid() {
      
      lonAccelConfid_ = 0;
      onChanged();
      return this;
    }

    private int latAccelConfid_ = 0;
    /**
     * <pre>
     * 定义车辆纵向加速度精度。取值同上
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence latAccelConfid = 2;</code>
     */
    public int getLatAccelConfidValue() {
      return latAccelConfid_;
    }
    /**
     * <pre>
     * 定义车辆纵向加速度精度。取值同上
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence latAccelConfid = 2;</code>
     */
    public Builder setLatAccelConfidValue(int value) {
      latAccelConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 定义车辆纵向加速度精度。取值同上
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence latAccelConfid = 2;</code>
     */
    public road.data.proto.AccelerationConfidence.AccConfidence getLatAccelConfid() {
      @SuppressWarnings("deprecation")
      road.data.proto.AccelerationConfidence.AccConfidence result = road.data.proto.AccelerationConfidence.AccConfidence.valueOf(latAccelConfid_);
      return result == null ? road.data.proto.AccelerationConfidence.AccConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 定义车辆纵向加速度精度。取值同上
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence latAccelConfid = 2;</code>
     */
    public Builder setLatAccelConfid(road.data.proto.AccelerationConfidence.AccConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      latAccelConfid_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 定义车辆纵向加速度精度。取值同上
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence latAccelConfid = 2;</code>
     */
    public Builder clearLatAccelConfid() {
      
      latAccelConfid_ = 0;
      onChanged();
      return this;
    }

    private int verticalAccelConfid_ = 0;
    /**
     * <pre>
     * 定义Z轴方向的加速度精度。取值同上
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence verticalAccelConfid = 3;</code>
     */
    public int getVerticalAccelConfidValue() {
      return verticalAccelConfid_;
    }
    /**
     * <pre>
     * 定义Z轴方向的加速度精度。取值同上
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence verticalAccelConfid = 3;</code>
     */
    public Builder setVerticalAccelConfidValue(int value) {
      verticalAccelConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 定义Z轴方向的加速度精度。取值同上
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence verticalAccelConfid = 3;</code>
     */
    public road.data.proto.AccelerationConfidence.AccConfidence getVerticalAccelConfid() {
      @SuppressWarnings("deprecation")
      road.data.proto.AccelerationConfidence.AccConfidence result = road.data.proto.AccelerationConfidence.AccConfidence.valueOf(verticalAccelConfid_);
      return result == null ? road.data.proto.AccelerationConfidence.AccConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 定义Z轴方向的加速度精度。取值同上
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence verticalAccelConfid = 3;</code>
     */
    public Builder setVerticalAccelConfid(road.data.proto.AccelerationConfidence.AccConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      verticalAccelConfid_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 定义Z轴方向的加速度精度。取值同上
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence verticalAccelConfid = 3;</code>
     */
    public Builder clearVerticalAccelConfid() {
      
      verticalAccelConfid_ = 0;
      onChanged();
      return this;
    }

    private int yawRateConfid_ = 0;
    /**
     * <pre>
     * 车辆摆角速度精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AngularVConfidence yawRateConfid = 4;</code>
     */
    public int getYawRateConfidValue() {
      return yawRateConfid_;
    }
    /**
     * <pre>
     * 车辆摆角速度精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AngularVConfidence yawRateConfid = 4;</code>
     */
    public Builder setYawRateConfidValue(int value) {
      yawRateConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 车辆摆角速度精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AngularVConfidence yawRateConfid = 4;</code>
     */
    public road.data.proto.AccelerationConfidence.AngularVConfidence getYawRateConfid() {
      @SuppressWarnings("deprecation")
      road.data.proto.AccelerationConfidence.AngularVConfidence result = road.data.proto.AccelerationConfidence.AngularVConfidence.valueOf(yawRateConfid_);
      return result == null ? road.data.proto.AccelerationConfidence.AngularVConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 车辆摆角速度精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AngularVConfidence yawRateConfid = 4;</code>
     */
    public Builder setYawRateConfid(road.data.proto.AccelerationConfidence.AngularVConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      yawRateConfid_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 车辆摆角速度精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AngularVConfidence yawRateConfid = 4;</code>
     */
    public Builder clearYawRateConfid() {
      
      yawRateConfid_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.AccelerationConfidence)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.AccelerationConfidence)
  private static final road.data.proto.AccelerationConfidence DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.AccelerationConfidence();
  }

  public static road.data.proto.AccelerationConfidence getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AccelerationConfidence>
      PARSER = new com.google.protobuf.AbstractParser<AccelerationConfidence>() {
    @java.lang.Override
    public AccelerationConfidence parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new AccelerationConfidence(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<AccelerationConfidence> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AccelerationConfidence> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.AccelerationConfidence getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

