// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface AccelerationConfidenceOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.AccelerationConfidence)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 定义车辆横向加速度精度。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence lonAccelConfid = 1;</code>
   */
  int getLonAccelConfidValue();
  /**
   * <pre>
   * 定义车辆横向加速度精度。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence lonAccelConfid = 1;</code>
   */
  road.data.proto.AccelerationConfidence.AccConfidence getLonAccelConfid();

  /**
   * <pre>
   * 定义车辆纵向加速度精度。取值同上
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence latAccelConfid = 2;</code>
   */
  int getLatAccelConfidValue();
  /**
   * <pre>
   * 定义车辆纵向加速度精度。取值同上
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence latAccelConfid = 2;</code>
   */
  road.data.proto.AccelerationConfidence.AccConfidence getLatAccelConfid();

  /**
   * <pre>
   * 定义Z轴方向的加速度精度。取值同上
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence verticalAccelConfid = 3;</code>
   */
  int getVerticalAccelConfidValue();
  /**
   * <pre>
   * 定义Z轴方向的加速度精度。取值同上
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AccConfidence verticalAccelConfid = 3;</code>
   */
  road.data.proto.AccelerationConfidence.AccConfidence getVerticalAccelConfid();

  /**
   * <pre>
   * 车辆摆角速度精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AngularVConfidence yawRateConfid = 4;</code>
   */
  int getYawRateConfidValue();
  /**
   * <pre>
   * 车辆摆角速度精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence.AngularVConfidence yawRateConfid = 4;</code>
   */
  road.data.proto.AccelerationConfidence.AngularVConfidence getYawRateConfid();
}
