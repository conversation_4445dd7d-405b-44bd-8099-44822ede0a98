// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *加速度   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.AccelerationSet4Way}
 */
public  final class AccelerationSet4Way extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.AccelerationSet4Way)
    AccelerationSet4WayOrBuilder {
private static final long serialVersionUID = 0L;
  // Use AccelerationSet4Way.newBuilder() to construct.
  private AccelerationSet4Way(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private AccelerationSet4Way() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new AccelerationSet4Way();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private AccelerationSet4Way(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            lat_ = input.readInt32();
            break;
          }
          case 16: {

            lon_ = input.readInt32();
            break;
          }
          case 24: {

            vert_ = input.readInt32();
            break;
          }
          case 32: {

            yaw_ = input.readInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_AccelerationSet4Way_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_AccelerationSet4Way_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.AccelerationSet4Way.class, road.data.proto.AccelerationSet4Way.Builder.class);
  }

  public static final int LAT_FIELD_NUMBER = 1;
  private int lat_;
  /**
   * <pre>
   * 可选，定义车辆纵向加速度。分辨率为0.01m/s^2，向前加速为正，反向为负。
   * </pre>
   *
   * <code>int32 lat = 1;</code>
   */
  public int getLat() {
    return lat_;
  }

  public static final int LON_FIELD_NUMBER = 2;
  private int lon_;
  /**
   * <pre>
   * 可选，定义车辆横向加速度。分辨率为0.01m/s^2，向前加速为正，反向为负。
   * </pre>
   *
   * <code>int32 lon = 2;</code>
   */
  public int getLon() {
    return lon_;
  }

  public static final int VERT_FIELD_NUMBER = 3;
  private int vert_;
  /**
   * <pre>
   * 可选，定义Z轴方向的加速度大小，Z轴方向竖直向下，沿着Z 轴方向为正。分辨率为0.02g，g为重力加速度典型值 9.80665m/s2。沿重力方向向下为正，反向为负
   * </pre>
   *
   * <code>int32 vert = 3;</code>
   */
  public int getVert() {
    return vert_;
  }

  public static final int YAW_FIELD_NUMBER = 4;
  private int yaw_;
  /**
   * <pre>
   * 可选，车辆摆角速度，辨率单位为0.01°/s。顺时针旋转为正，反向为负
   * </pre>
   *
   * <code>int32 yaw = 4;</code>
   */
  public int getYaw() {
    return yaw_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (lat_ != 0) {
      output.writeInt32(1, lat_);
    }
    if (lon_ != 0) {
      output.writeInt32(2, lon_);
    }
    if (vert_ != 0) {
      output.writeInt32(3, vert_);
    }
    if (yaw_ != 0) {
      output.writeInt32(4, yaw_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (lat_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, lat_);
    }
    if (lon_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, lon_);
    }
    if (vert_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, vert_);
    }
    if (yaw_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, yaw_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.AccelerationSet4Way)) {
      return super.equals(obj);
    }
    road.data.proto.AccelerationSet4Way other = (road.data.proto.AccelerationSet4Way) obj;

    if (getLat()
        != other.getLat()) return false;
    if (getLon()
        != other.getLon()) return false;
    if (getVert()
        != other.getVert()) return false;
    if (getYaw()
        != other.getYaw()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + LAT_FIELD_NUMBER;
    hash = (53 * hash) + getLat();
    hash = (37 * hash) + LON_FIELD_NUMBER;
    hash = (53 * hash) + getLon();
    hash = (37 * hash) + VERT_FIELD_NUMBER;
    hash = (53 * hash) + getVert();
    hash = (37 * hash) + YAW_FIELD_NUMBER;
    hash = (53 * hash) + getYaw();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.AccelerationSet4Way parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.AccelerationSet4Way parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.AccelerationSet4Way parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.AccelerationSet4Way parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.AccelerationSet4Way parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.AccelerationSet4Way parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.AccelerationSet4Way parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.AccelerationSet4Way parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.AccelerationSet4Way parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.AccelerationSet4Way parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.AccelerationSet4Way parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.AccelerationSet4Way parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.AccelerationSet4Way prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *加速度   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.AccelerationSet4Way}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.AccelerationSet4Way)
      road.data.proto.AccelerationSet4WayOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_AccelerationSet4Way_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_AccelerationSet4Way_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.AccelerationSet4Way.class, road.data.proto.AccelerationSet4Way.Builder.class);
    }

    // Construct using road.data.proto.AccelerationSet4Way.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      lat_ = 0;

      lon_ = 0;

      vert_ = 0;

      yaw_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_AccelerationSet4Way_descriptor;
    }

    @java.lang.Override
    public road.data.proto.AccelerationSet4Way getDefaultInstanceForType() {
      return road.data.proto.AccelerationSet4Way.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.AccelerationSet4Way build() {
      road.data.proto.AccelerationSet4Way result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.AccelerationSet4Way buildPartial() {
      road.data.proto.AccelerationSet4Way result = new road.data.proto.AccelerationSet4Way(this);
      result.lat_ = lat_;
      result.lon_ = lon_;
      result.vert_ = vert_;
      result.yaw_ = yaw_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.AccelerationSet4Way) {
        return mergeFrom((road.data.proto.AccelerationSet4Way)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.AccelerationSet4Way other) {
      if (other == road.data.proto.AccelerationSet4Way.getDefaultInstance()) return this;
      if (other.getLat() != 0) {
        setLat(other.getLat());
      }
      if (other.getLon() != 0) {
        setLon(other.getLon());
      }
      if (other.getVert() != 0) {
        setVert(other.getVert());
      }
      if (other.getYaw() != 0) {
        setYaw(other.getYaw());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.AccelerationSet4Way parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.AccelerationSet4Way) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int lat_ ;
    /**
     * <pre>
     * 可选，定义车辆纵向加速度。分辨率为0.01m/s^2，向前加速为正，反向为负。
     * </pre>
     *
     * <code>int32 lat = 1;</code>
     */
    public int getLat() {
      return lat_;
    }
    /**
     * <pre>
     * 可选，定义车辆纵向加速度。分辨率为0.01m/s^2，向前加速为正，反向为负。
     * </pre>
     *
     * <code>int32 lat = 1;</code>
     */
    public Builder setLat(int value) {
      
      lat_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆纵向加速度。分辨率为0.01m/s^2，向前加速为正，反向为负。
     * </pre>
     *
     * <code>int32 lat = 1;</code>
     */
    public Builder clearLat() {
      
      lat_ = 0;
      onChanged();
      return this;
    }

    private int lon_ ;
    /**
     * <pre>
     * 可选，定义车辆横向加速度。分辨率为0.01m/s^2，向前加速为正，反向为负。
     * </pre>
     *
     * <code>int32 lon = 2;</code>
     */
    public int getLon() {
      return lon_;
    }
    /**
     * <pre>
     * 可选，定义车辆横向加速度。分辨率为0.01m/s^2，向前加速为正，反向为负。
     * </pre>
     *
     * <code>int32 lon = 2;</code>
     */
    public Builder setLon(int value) {
      
      lon_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆横向加速度。分辨率为0.01m/s^2，向前加速为正，反向为负。
     * </pre>
     *
     * <code>int32 lon = 2;</code>
     */
    public Builder clearLon() {
      
      lon_ = 0;
      onChanged();
      return this;
    }

    private int vert_ ;
    /**
     * <pre>
     * 可选，定义Z轴方向的加速度大小，Z轴方向竖直向下，沿着Z 轴方向为正。分辨率为0.02g，g为重力加速度典型值 9.80665m/s2。沿重力方向向下为正，反向为负
     * </pre>
     *
     * <code>int32 vert = 3;</code>
     */
    public int getVert() {
      return vert_;
    }
    /**
     * <pre>
     * 可选，定义Z轴方向的加速度大小，Z轴方向竖直向下，沿着Z 轴方向为正。分辨率为0.02g，g为重力加速度典型值 9.80665m/s2。沿重力方向向下为正，反向为负
     * </pre>
     *
     * <code>int32 vert = 3;</code>
     */
    public Builder setVert(int value) {
      
      vert_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，定义Z轴方向的加速度大小，Z轴方向竖直向下，沿着Z 轴方向为正。分辨率为0.02g，g为重力加速度典型值 9.80665m/s2。沿重力方向向下为正，反向为负
     * </pre>
     *
     * <code>int32 vert = 3;</code>
     */
    public Builder clearVert() {
      
      vert_ = 0;
      onChanged();
      return this;
    }

    private int yaw_ ;
    /**
     * <pre>
     * 可选，车辆摆角速度，辨率单位为0.01°/s。顺时针旋转为正，反向为负
     * </pre>
     *
     * <code>int32 yaw = 4;</code>
     */
    public int getYaw() {
      return yaw_;
    }
    /**
     * <pre>
     * 可选，车辆摆角速度，辨率单位为0.01°/s。顺时针旋转为正，反向为负
     * </pre>
     *
     * <code>int32 yaw = 4;</code>
     */
    public Builder setYaw(int value) {
      
      yaw_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，车辆摆角速度，辨率单位为0.01°/s。顺时针旋转为正，反向为负
     * </pre>
     *
     * <code>int32 yaw = 4;</code>
     */
    public Builder clearYaw() {
      
      yaw_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.AccelerationSet4Way)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.AccelerationSet4Way)
  private static final road.data.proto.AccelerationSet4Way DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.AccelerationSet4Way();
  }

  public static road.data.proto.AccelerationSet4Way getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AccelerationSet4Way>
      PARSER = new com.google.protobuf.AbstractParser<AccelerationSet4Way>() {
    @java.lang.Override
    public AccelerationSet4Way parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new AccelerationSet4Way(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<AccelerationSet4Way> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AccelerationSet4Way> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.AccelerationSet4Way getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

