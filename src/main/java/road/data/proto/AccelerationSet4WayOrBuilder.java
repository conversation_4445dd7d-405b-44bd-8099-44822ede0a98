// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface AccelerationSet4WayOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.AccelerationSet4Way)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 可选，定义车辆纵向加速度。分辨率为0.01m/s^2，向前加速为正，反向为负。
   * </pre>
   *
   * <code>int32 lat = 1;</code>
   */
  int getLat();

  /**
   * <pre>
   * 可选，定义车辆横向加速度。分辨率为0.01m/s^2，向前加速为正，反向为负。
   * </pre>
   *
   * <code>int32 lon = 2;</code>
   */
  int getLon();

  /**
   * <pre>
   * 可选，定义Z轴方向的加速度大小，Z轴方向竖直向下，沿着Z 轴方向为正。分辨率为0.02g，g为重力加速度典型值 9.80665m/s2。沿重力方向向下为正，反向为负
   * </pre>
   *
   * <code>int32 vert = 3;</code>
   */
  int getVert();

  /**
   * <pre>
   * 可选，车辆摆角速度，辨率单位为0.01°/s。顺时针旋转为正，反向为负
   * </pre>
   *
   * <code>int32 yaw = 4;</code>
   */
  int getYaw();
}
