// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface AllowedManeuversOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.AllowedManeuvers)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *定义一个（机动车）车道的允许转向行为。例如：如果参数含义表示'允许直行'和'允许右转向'，那么二进制为101000000000，对应十进制为40960，该参数值填写40960。
   * </pre>
   *
   * <code>uint32 maneuver = 1;</code>
   */
  int getManeuver();
}
