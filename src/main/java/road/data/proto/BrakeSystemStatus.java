// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车辆刹车系统状态 BrakeSystemStatus  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.BrakeSystemStatus}
 */
public  final class BrakeSystemStatus extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.BrakeSystemStatus)
    BrakeSystemStatusOrBuilder {
private static final long serialVersionUID = 0L;
  // Use BrakeSystemStatus.newBuilder() to construct.
  private BrakeSystemStatus(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private BrakeSystemStatus() {
    brakePadel_ = 0;
    traction_ = 0;
    abs_ = 0;
    scs_ = 0;
    brakeBoost_ = 0;
    auxBrakes_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new BrakeSystemStatus();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private BrakeSystemStatus(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {
            int rawValue = input.readEnum();

            brakePadel_ = rawValue;
            break;
          }
          case 16: {

            wheelBrakes_ = input.readUInt32();
            break;
          }
          case 24: {
            int rawValue = input.readEnum();

            traction_ = rawValue;
            break;
          }
          case 32: {
            int rawValue = input.readEnum();

            abs_ = rawValue;
            break;
          }
          case 40: {
            int rawValue = input.readEnum();

            scs_ = rawValue;
            break;
          }
          case 48: {
            int rawValue = input.readEnum();

            brakeBoost_ = rawValue;
            break;
          }
          case 56: {
            int rawValue = input.readEnum();

            auxBrakes_ = rawValue;
            break;
          }
          case 64: {

            brakeControl_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_BrakeSystemStatus_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_BrakeSystemStatus_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.BrakeSystemStatus.class, road.data.proto.BrakeSystemStatus.Builder.class);
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.BrakeSystemStatus.BrakePedalStatus}
   */
  public enum BrakePedalStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 车辆制动踏板检测器不可用
     * </pre>
     *
     * <code>UNAVAILABLE_PEDAL = 0;</code>
     */
    UNAVAILABLE_PEDAL(0),
    /**
     * <pre>
     * 车辆制动踏板未踩下
     * </pre>
     *
     * <code>OFF_PEDAL = 1;</code>
     */
    OFF_PEDAL(1),
    /**
     * <pre>
     *踩下车辆的制动踏板
     * </pre>
     *
     * <code>ON_PEDAL = 2;</code>
     */
    ON_PEDAL(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * 车辆制动踏板检测器不可用
     * </pre>
     *
     * <code>UNAVAILABLE_PEDAL = 0;</code>
     */
    public static final int UNAVAILABLE_PEDAL_VALUE = 0;
    /**
     * <pre>
     * 车辆制动踏板未踩下
     * </pre>
     *
     * <code>OFF_PEDAL = 1;</code>
     */
    public static final int OFF_PEDAL_VALUE = 1;
    /**
     * <pre>
     *踩下车辆的制动踏板
     * </pre>
     *
     * <code>ON_PEDAL = 2;</code>
     */
    public static final int ON_PEDAL_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static BrakePedalStatus valueOf(int value) {
      return forNumber(value);
    }

    public static BrakePedalStatus forNumber(int value) {
      switch (value) {
        case 0: return UNAVAILABLE_PEDAL;
        case 1: return OFF_PEDAL;
        case 2: return ON_PEDAL;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<BrakePedalStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        BrakePedalStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<BrakePedalStatus>() {
            public BrakePedalStatus findValueByNumber(int number) {
              return BrakePedalStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.BrakeSystemStatus.getDescriptor().getEnumTypes().get(0);
    }

    private static final BrakePedalStatus[] VALUES = values();

    public static BrakePedalStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private BrakePedalStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.BrakeSystemStatus.BrakePedalStatus)
  }

  /**
   * <pre>
   * 1:LEFT_FRONT; //左前活动;
   * 2:LEFT_REAR;  //左后主动;
   * 3:RIGHT_FRONT;  //右前活动;
   * 4:RIGHT_REAR; //右后主动。
   * </pre>
   *
   * Protobuf enum {@code cn.seisys.v2x.pb.BrakeSystemStatus.TractionControlStatus}
   */
  public enum TractionControlStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *系统未装备或不可用;
     * </pre>
     *
     * <code>UNAVAILABLE_TRACTION = 0;</code>
     */
    UNAVAILABLE_TRACTION(0),
    /**
     * <pre>
     *系统处于关闭状态;
     * </pre>
     *
     * <code>OFF_TRACTION = 1;</code>
     */
    OFF_TRACTION(1),
    /**
     * <pre>
     *系统处于开启状态，但未触发;
     * </pre>
     *
     * <code>ON_TRACTION = 2;</code>
     */
    ON_TRACTION(2),
    /**
     * <pre>
     *系统被触发，处于作用状态.
     * </pre>
     *
     * <code>ENGAGED_TRACTION = 3;</code>
     */
    ENGAGED_TRACTION(3),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *系统未装备或不可用;
     * </pre>
     *
     * <code>UNAVAILABLE_TRACTION = 0;</code>
     */
    public static final int UNAVAILABLE_TRACTION_VALUE = 0;
    /**
     * <pre>
     *系统处于关闭状态;
     * </pre>
     *
     * <code>OFF_TRACTION = 1;</code>
     */
    public static final int OFF_TRACTION_VALUE = 1;
    /**
     * <pre>
     *系统处于开启状态，但未触发;
     * </pre>
     *
     * <code>ON_TRACTION = 2;</code>
     */
    public static final int ON_TRACTION_VALUE = 2;
    /**
     * <pre>
     *系统被触发，处于作用状态.
     * </pre>
     *
     * <code>ENGAGED_TRACTION = 3;</code>
     */
    public static final int ENGAGED_TRACTION_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TractionControlStatus valueOf(int value) {
      return forNumber(value);
    }

    public static TractionControlStatus forNumber(int value) {
      switch (value) {
        case 0: return UNAVAILABLE_TRACTION;
        case 1: return OFF_TRACTION;
        case 2: return ON_TRACTION;
        case 3: return ENGAGED_TRACTION;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<TractionControlStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        TractionControlStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<TractionControlStatus>() {
            public TractionControlStatus findValueByNumber(int number) {
              return TractionControlStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.BrakeSystemStatus.getDescriptor().getEnumTypes().get(1);
    }

    private static final TractionControlStatus[] VALUES = values();

    public static TractionControlStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private TractionControlStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.BrakeSystemStatus.TractionControlStatus)
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.BrakeSystemStatus.AntiLockBrakeStatus}
   */
  public enum AntiLockBrakeStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *车辆未配备 ABS 刹车或 ABS 刹车状态不可用;
     * </pre>
     *
     * <code>UNAVAILABLE_ABS = 0;</code>
     */
    UNAVAILABLE_ABS(0),
    /**
     * <pre>
     *车辆的 ABS关闭;
     * </pre>
     *
     * <code>OFF_ABS = 1;</code>
     */
    OFF_ABS(1),
    /**
     * <pre>
     * 车辆的 ABS开启（但未接合）;
     * </pre>
     *
     * <code>ON_ABS = 2;</code>
     */
    ON_ABS(2),
    /**
     * <pre>
     *车辆的ABS控制在任何车轮上接合.
     * </pre>
     *
     * <code>ENGAGED_ABS = 3;</code>
     */
    ENGAGED_ABS(3),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *车辆未配备 ABS 刹车或 ABS 刹车状态不可用;
     * </pre>
     *
     * <code>UNAVAILABLE_ABS = 0;</code>
     */
    public static final int UNAVAILABLE_ABS_VALUE = 0;
    /**
     * <pre>
     *车辆的 ABS关闭;
     * </pre>
     *
     * <code>OFF_ABS = 1;</code>
     */
    public static final int OFF_ABS_VALUE = 1;
    /**
     * <pre>
     * 车辆的 ABS开启（但未接合）;
     * </pre>
     *
     * <code>ON_ABS = 2;</code>
     */
    public static final int ON_ABS_VALUE = 2;
    /**
     * <pre>
     *车辆的ABS控制在任何车轮上接合.
     * </pre>
     *
     * <code>ENGAGED_ABS = 3;</code>
     */
    public static final int ENGAGED_ABS_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static AntiLockBrakeStatus valueOf(int value) {
      return forNumber(value);
    }

    public static AntiLockBrakeStatus forNumber(int value) {
      switch (value) {
        case 0: return UNAVAILABLE_ABS;
        case 1: return OFF_ABS;
        case 2: return ON_ABS;
        case 3: return ENGAGED_ABS;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<AntiLockBrakeStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        AntiLockBrakeStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<AntiLockBrakeStatus>() {
            public AntiLockBrakeStatus findValueByNumber(int number) {
              return AntiLockBrakeStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.BrakeSystemStatus.getDescriptor().getEnumTypes().get(2);
    }

    private static final AntiLockBrakeStatus[] VALUES = values();

    public static AntiLockBrakeStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private AntiLockBrakeStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.BrakeSystemStatus.AntiLockBrakeStatus)
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.BrakeSystemStatus.StabilityControlStatus}
   */
  public enum StabilityControlStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *系统未装备或不可用;
     * </pre>
     *
     * <code>UNAVAILABLE_SCS = 0;</code>
     */
    UNAVAILABLE_SCS(0),
    /**
     * <pre>
     *系统处于关闭状态;
     * </pre>
     *
     * <code>OFF_SCS = 1;</code>
     */
    OFF_SCS(1),
    /**
     * <pre>
     *系统处于开启状态，但未触发;
     * </pre>
     *
     * <code>ON_SCS = 2;</code>
     */
    ON_SCS(2),
    /**
     * <pre>
     *系统被触发，处于作用状态.
     * </pre>
     *
     * <code>ENGAGED_SCS = 3;</code>
     */
    ENGAGED_SCS(3),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *系统未装备或不可用;
     * </pre>
     *
     * <code>UNAVAILABLE_SCS = 0;</code>
     */
    public static final int UNAVAILABLE_SCS_VALUE = 0;
    /**
     * <pre>
     *系统处于关闭状态;
     * </pre>
     *
     * <code>OFF_SCS = 1;</code>
     */
    public static final int OFF_SCS_VALUE = 1;
    /**
     * <pre>
     *系统处于开启状态，但未触发;
     * </pre>
     *
     * <code>ON_SCS = 2;</code>
     */
    public static final int ON_SCS_VALUE = 2;
    /**
     * <pre>
     *系统被触发，处于作用状态.
     * </pre>
     *
     * <code>ENGAGED_SCS = 3;</code>
     */
    public static final int ENGAGED_SCS_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static StabilityControlStatus valueOf(int value) {
      return forNumber(value);
    }

    public static StabilityControlStatus forNumber(int value) {
      switch (value) {
        case 0: return UNAVAILABLE_SCS;
        case 1: return OFF_SCS;
        case 2: return ON_SCS;
        case 3: return ENGAGED_SCS;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<StabilityControlStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        StabilityControlStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<StabilityControlStatus>() {
            public StabilityControlStatus findValueByNumber(int number) {
              return StabilityControlStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.BrakeSystemStatus.getDescriptor().getEnumTypes().get(3);
    }

    private static final StabilityControlStatus[] VALUES = values();

    public static StabilityControlStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private StabilityControlStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.BrakeSystemStatus.StabilityControlStatus)
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.BrakeSystemStatus.BrakeBoostApplied}
   */
  public enum BrakeBoostApplied
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *车辆未配备制动助力或制动助力数据不可用
     * </pre>
     *
     * <code>UNAVAILABLE_BBA = 0;</code>
     */
    UNAVAILABLE_BBA(0),
    /**
     * <pre>
     *车辆制动助力关闭
     * </pre>
     *
     * <code>OFF_BBA = 1;</code>
     */
    OFF_BBA(1),
    /**
     * <pre>
     *车辆的制动助力开启（应用）
     * </pre>
     *
     * <code>ON_BBA = 2;</code>
     */
    ON_BBA(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *车辆未配备制动助力或制动助力数据不可用
     * </pre>
     *
     * <code>UNAVAILABLE_BBA = 0;</code>
     */
    public static final int UNAVAILABLE_BBA_VALUE = 0;
    /**
     * <pre>
     *车辆制动助力关闭
     * </pre>
     *
     * <code>OFF_BBA = 1;</code>
     */
    public static final int OFF_BBA_VALUE = 1;
    /**
     * <pre>
     *车辆的制动助力开启（应用）
     * </pre>
     *
     * <code>ON_BBA = 2;</code>
     */
    public static final int ON_BBA_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static BrakeBoostApplied valueOf(int value) {
      return forNumber(value);
    }

    public static BrakeBoostApplied forNumber(int value) {
      switch (value) {
        case 0: return UNAVAILABLE_BBA;
        case 1: return OFF_BBA;
        case 2: return ON_BBA;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<BrakeBoostApplied>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        BrakeBoostApplied> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<BrakeBoostApplied>() {
            public BrakeBoostApplied findValueByNumber(int number) {
              return BrakeBoostApplied.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.BrakeSystemStatus.getDescriptor().getEnumTypes().get(4);
    }

    private static final BrakeBoostApplied[] VALUES = values();

    public static BrakeBoostApplied valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private BrakeBoostApplied(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.BrakeSystemStatus.BrakeBoostApplied)
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.BrakeSystemStatus.AuxiliaryBrakeStatus}
   */
  public enum AuxiliaryBrakeStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *车辆未配备辅助制动器或辅助制动器状态不可用;
     * </pre>
     *
     * <code>UNAVAILABLE_AUX = 0;</code>
     */
    UNAVAILABLE_AUX(0),
    /**
     * <pre>
     *车辆的辅助制动器关闭;
     * </pre>
     *
     * <code>OFF_AUX = 1;</code>
     */
    OFF_AUX(1),
    /**
     * <pre>
     *车辆的辅助制动器开启;
     * </pre>
     *
     * <code>ON_AUX = 2;</code>
     */
    ON_AUX(2),
    /**
     * <pre>
     *保留.
     * </pre>
     *
     * <code>ENGAGED_AUX = 3;</code>
     */
    ENGAGED_AUX(3),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *车辆未配备辅助制动器或辅助制动器状态不可用;
     * </pre>
     *
     * <code>UNAVAILABLE_AUX = 0;</code>
     */
    public static final int UNAVAILABLE_AUX_VALUE = 0;
    /**
     * <pre>
     *车辆的辅助制动器关闭;
     * </pre>
     *
     * <code>OFF_AUX = 1;</code>
     */
    public static final int OFF_AUX_VALUE = 1;
    /**
     * <pre>
     *车辆的辅助制动器开启;
     * </pre>
     *
     * <code>ON_AUX = 2;</code>
     */
    public static final int ON_AUX_VALUE = 2;
    /**
     * <pre>
     *保留.
     * </pre>
     *
     * <code>ENGAGED_AUX = 3;</code>
     */
    public static final int ENGAGED_AUX_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static AuxiliaryBrakeStatus valueOf(int value) {
      return forNumber(value);
    }

    public static AuxiliaryBrakeStatus forNumber(int value) {
      switch (value) {
        case 0: return UNAVAILABLE_AUX;
        case 1: return OFF_AUX;
        case 2: return ON_AUX;
        case 3: return ENGAGED_AUX;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<AuxiliaryBrakeStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        AuxiliaryBrakeStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<AuxiliaryBrakeStatus>() {
            public AuxiliaryBrakeStatus findValueByNumber(int number) {
              return AuxiliaryBrakeStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.BrakeSystemStatus.getDescriptor().getEnumTypes().get(5);
    }

    private static final AuxiliaryBrakeStatus[] VALUES = values();

    public static AuxiliaryBrakeStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private AuxiliaryBrakeStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.BrakeSystemStatus.AuxiliaryBrakeStatus)
  }

  public static final int BRAKEPADEL_FIELD_NUMBER = 1;
  private int brakePadel_;
  /**
   * <pre>
   * 可选，刹车踏板踩下情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakePedalStatus brakePadel = 1;</code>
   */
  public int getBrakePadelValue() {
    return brakePadel_;
  }
  /**
   * <pre>
   * 可选，刹车踏板踩下情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakePedalStatus brakePadel = 1;</code>
   */
  public road.data.proto.BrakeSystemStatus.BrakePedalStatus getBrakePadel() {
    @SuppressWarnings("deprecation")
    road.data.proto.BrakeSystemStatus.BrakePedalStatus result = road.data.proto.BrakeSystemStatus.BrakePedalStatus.valueOf(brakePadel_);
    return result == null ? road.data.proto.BrakeSystemStatus.BrakePedalStatus.UNRECOGNIZED : result;
  }

  public static final int WHEELBRAKES_FIELD_NUMBER = 2;
  private int wheelBrakes_;
  /**
   * <pre>
   * 可选，车轮制动情况BrakeAppliedStatus，位串，转化为二进制后，二进制第x位数字为1对应的含义：
   * </pre>
   *
   * <code>uint32 wheelBrakes = 2;</code>
   */
  public int getWheelBrakes() {
    return wheelBrakes_;
  }

  public static final int TRACTION_FIELD_NUMBER = 3;
  private int traction_;
  /**
   * <pre>
   *可选，牵引力控制系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.TractionControlStatus traction = 3;</code>
   */
  public int getTractionValue() {
    return traction_;
  }
  /**
   * <pre>
   *可选，牵引力控制系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.TractionControlStatus traction = 3;</code>
   */
  public road.data.proto.BrakeSystemStatus.TractionControlStatus getTraction() {
    @SuppressWarnings("deprecation")
    road.data.proto.BrakeSystemStatus.TractionControlStatus result = road.data.proto.BrakeSystemStatus.TractionControlStatus.valueOf(traction_);
    return result == null ? road.data.proto.BrakeSystemStatus.TractionControlStatus.UNRECOGNIZED : result;
  }

  public static final int ABS_FIELD_NUMBER = 4;
  private int abs_;
  /**
   * <pre>
   *可选，制动防抱死系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AntiLockBrakeStatus abs = 4;</code>
   */
  public int getAbsValue() {
    return abs_;
  }
  /**
   * <pre>
   *可选，制动防抱死系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AntiLockBrakeStatus abs = 4;</code>
   */
  public road.data.proto.BrakeSystemStatus.AntiLockBrakeStatus getAbs() {
    @SuppressWarnings("deprecation")
    road.data.proto.BrakeSystemStatus.AntiLockBrakeStatus result = road.data.proto.BrakeSystemStatus.AntiLockBrakeStatus.valueOf(abs_);
    return result == null ? road.data.proto.BrakeSystemStatus.AntiLockBrakeStatus.UNRECOGNIZED : result;
  }

  public static final int SCS_FIELD_NUMBER = 5;
  private int scs_;
  /**
   * <pre>
   * 可选，车身稳定控制系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.StabilityControlStatus scs = 5;</code>
   */
  public int getScsValue() {
    return scs_;
  }
  /**
   * <pre>
   * 可选，车身稳定控制系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.StabilityControlStatus scs = 5;</code>
   */
  public road.data.proto.BrakeSystemStatus.StabilityControlStatus getScs() {
    @SuppressWarnings("deprecation")
    road.data.proto.BrakeSystemStatus.StabilityControlStatus result = road.data.proto.BrakeSystemStatus.StabilityControlStatus.valueOf(scs_);
    return result == null ? road.data.proto.BrakeSystemStatus.StabilityControlStatus.UNRECOGNIZED : result;
  }

  public static final int BRAKEBOOST_FIELD_NUMBER = 6;
  private int brakeBoost_;
  /**
   * <pre>
   *可选，刹车助力系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakeBoostApplied brakeBoost = 6;</code>
   */
  public int getBrakeBoostValue() {
    return brakeBoost_;
  }
  /**
   * <pre>
   *可选，刹车助力系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakeBoostApplied brakeBoost = 6;</code>
   */
  public road.data.proto.BrakeSystemStatus.BrakeBoostApplied getBrakeBoost() {
    @SuppressWarnings("deprecation")
    road.data.proto.BrakeSystemStatus.BrakeBoostApplied result = road.data.proto.BrakeSystemStatus.BrakeBoostApplied.valueOf(brakeBoost_);
    return result == null ? road.data.proto.BrakeSystemStatus.BrakeBoostApplied.UNRECOGNIZED : result;
  }

  public static final int AUXBRAKES_FIELD_NUMBER = 7;
  private int auxBrakes_;
  /**
   * <pre>
   *可选，辅助制动系统（一般指手刹）情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AuxiliaryBrakeStatus auxBrakes = 7;</code>
   */
  public int getAuxBrakesValue() {
    return auxBrakes_;
  }
  /**
   * <pre>
   *可选，辅助制动系统（一般指手刹）情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AuxiliaryBrakeStatus auxBrakes = 7;</code>
   */
  public road.data.proto.BrakeSystemStatus.AuxiliaryBrakeStatus getAuxBrakes() {
    @SuppressWarnings("deprecation")
    road.data.proto.BrakeSystemStatus.AuxiliaryBrakeStatus result = road.data.proto.BrakeSystemStatus.AuxiliaryBrakeStatus.valueOf(auxBrakes_);
    return result == null ? road.data.proto.BrakeSystemStatus.AuxiliaryBrakeStatus.UNRECOGNIZED : result;
  }

  public static final int BRAKECONTROL_FIELD_NUMBER = 8;
  private int brakeControl_;
  /**
   * <pre>
   *可选，刹车踩踏强度 百分比：0~100%，精度0.1% BrakeControl类型
   * </pre>
   *
   * <code>uint32 brakeControl = 8;</code>
   */
  public int getBrakeControl() {
    return brakeControl_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (brakePadel_ != road.data.proto.BrakeSystemStatus.BrakePedalStatus.UNAVAILABLE_PEDAL.getNumber()) {
      output.writeEnum(1, brakePadel_);
    }
    if (wheelBrakes_ != 0) {
      output.writeUInt32(2, wheelBrakes_);
    }
    if (traction_ != road.data.proto.BrakeSystemStatus.TractionControlStatus.UNAVAILABLE_TRACTION.getNumber()) {
      output.writeEnum(3, traction_);
    }
    if (abs_ != road.data.proto.BrakeSystemStatus.AntiLockBrakeStatus.UNAVAILABLE_ABS.getNumber()) {
      output.writeEnum(4, abs_);
    }
    if (scs_ != road.data.proto.BrakeSystemStatus.StabilityControlStatus.UNAVAILABLE_SCS.getNumber()) {
      output.writeEnum(5, scs_);
    }
    if (brakeBoost_ != road.data.proto.BrakeSystemStatus.BrakeBoostApplied.UNAVAILABLE_BBA.getNumber()) {
      output.writeEnum(6, brakeBoost_);
    }
    if (auxBrakes_ != road.data.proto.BrakeSystemStatus.AuxiliaryBrakeStatus.UNAVAILABLE_AUX.getNumber()) {
      output.writeEnum(7, auxBrakes_);
    }
    if (brakeControl_ != 0) {
      output.writeUInt32(8, brakeControl_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (brakePadel_ != road.data.proto.BrakeSystemStatus.BrakePedalStatus.UNAVAILABLE_PEDAL.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(1, brakePadel_);
    }
    if (wheelBrakes_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, wheelBrakes_);
    }
    if (traction_ != road.data.proto.BrakeSystemStatus.TractionControlStatus.UNAVAILABLE_TRACTION.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, traction_);
    }
    if (abs_ != road.data.proto.BrakeSystemStatus.AntiLockBrakeStatus.UNAVAILABLE_ABS.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(4, abs_);
    }
    if (scs_ != road.data.proto.BrakeSystemStatus.StabilityControlStatus.UNAVAILABLE_SCS.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(5, scs_);
    }
    if (brakeBoost_ != road.data.proto.BrakeSystemStatus.BrakeBoostApplied.UNAVAILABLE_BBA.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(6, brakeBoost_);
    }
    if (auxBrakes_ != road.data.proto.BrakeSystemStatus.AuxiliaryBrakeStatus.UNAVAILABLE_AUX.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(7, auxBrakes_);
    }
    if (brakeControl_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(8, brakeControl_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.BrakeSystemStatus)) {
      return super.equals(obj);
    }
    road.data.proto.BrakeSystemStatus other = (road.data.proto.BrakeSystemStatus) obj;

    if (brakePadel_ != other.brakePadel_) return false;
    if (getWheelBrakes()
        != other.getWheelBrakes()) return false;
    if (traction_ != other.traction_) return false;
    if (abs_ != other.abs_) return false;
    if (scs_ != other.scs_) return false;
    if (brakeBoost_ != other.brakeBoost_) return false;
    if (auxBrakes_ != other.auxBrakes_) return false;
    if (getBrakeControl()
        != other.getBrakeControl()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + BRAKEPADEL_FIELD_NUMBER;
    hash = (53 * hash) + brakePadel_;
    hash = (37 * hash) + WHEELBRAKES_FIELD_NUMBER;
    hash = (53 * hash) + getWheelBrakes();
    hash = (37 * hash) + TRACTION_FIELD_NUMBER;
    hash = (53 * hash) + traction_;
    hash = (37 * hash) + ABS_FIELD_NUMBER;
    hash = (53 * hash) + abs_;
    hash = (37 * hash) + SCS_FIELD_NUMBER;
    hash = (53 * hash) + scs_;
    hash = (37 * hash) + BRAKEBOOST_FIELD_NUMBER;
    hash = (53 * hash) + brakeBoost_;
    hash = (37 * hash) + AUXBRAKES_FIELD_NUMBER;
    hash = (53 * hash) + auxBrakes_;
    hash = (37 * hash) + BRAKECONTROL_FIELD_NUMBER;
    hash = (53 * hash) + getBrakeControl();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.BrakeSystemStatus parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.BrakeSystemStatus parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.BrakeSystemStatus parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.BrakeSystemStatus parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.BrakeSystemStatus parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.BrakeSystemStatus parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.BrakeSystemStatus parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.BrakeSystemStatus parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.BrakeSystemStatus parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.BrakeSystemStatus parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.BrakeSystemStatus parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.BrakeSystemStatus parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.BrakeSystemStatus prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *车辆刹车系统状态 BrakeSystemStatus  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.BrakeSystemStatus}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.BrakeSystemStatus)
      road.data.proto.BrakeSystemStatusOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_BrakeSystemStatus_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_BrakeSystemStatus_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.BrakeSystemStatus.class, road.data.proto.BrakeSystemStatus.Builder.class);
    }

    // Construct using road.data.proto.BrakeSystemStatus.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      brakePadel_ = 0;

      wheelBrakes_ = 0;

      traction_ = 0;

      abs_ = 0;

      scs_ = 0;

      brakeBoost_ = 0;

      auxBrakes_ = 0;

      brakeControl_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_BrakeSystemStatus_descriptor;
    }

    @java.lang.Override
    public road.data.proto.BrakeSystemStatus getDefaultInstanceForType() {
      return road.data.proto.BrakeSystemStatus.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.BrakeSystemStatus build() {
      road.data.proto.BrakeSystemStatus result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.BrakeSystemStatus buildPartial() {
      road.data.proto.BrakeSystemStatus result = new road.data.proto.BrakeSystemStatus(this);
      result.brakePadel_ = brakePadel_;
      result.wheelBrakes_ = wheelBrakes_;
      result.traction_ = traction_;
      result.abs_ = abs_;
      result.scs_ = scs_;
      result.brakeBoost_ = brakeBoost_;
      result.auxBrakes_ = auxBrakes_;
      result.brakeControl_ = brakeControl_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.BrakeSystemStatus) {
        return mergeFrom((road.data.proto.BrakeSystemStatus)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.BrakeSystemStatus other) {
      if (other == road.data.proto.BrakeSystemStatus.getDefaultInstance()) return this;
      if (other.brakePadel_ != 0) {
        setBrakePadelValue(other.getBrakePadelValue());
      }
      if (other.getWheelBrakes() != 0) {
        setWheelBrakes(other.getWheelBrakes());
      }
      if (other.traction_ != 0) {
        setTractionValue(other.getTractionValue());
      }
      if (other.abs_ != 0) {
        setAbsValue(other.getAbsValue());
      }
      if (other.scs_ != 0) {
        setScsValue(other.getScsValue());
      }
      if (other.brakeBoost_ != 0) {
        setBrakeBoostValue(other.getBrakeBoostValue());
      }
      if (other.auxBrakes_ != 0) {
        setAuxBrakesValue(other.getAuxBrakesValue());
      }
      if (other.getBrakeControl() != 0) {
        setBrakeControl(other.getBrakeControl());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.BrakeSystemStatus parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.BrakeSystemStatus) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int brakePadel_ = 0;
    /**
     * <pre>
     * 可选，刹车踏板踩下情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakePedalStatus brakePadel = 1;</code>
     */
    public int getBrakePadelValue() {
      return brakePadel_;
    }
    /**
     * <pre>
     * 可选，刹车踏板踩下情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakePedalStatus brakePadel = 1;</code>
     */
    public Builder setBrakePadelValue(int value) {
      brakePadel_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，刹车踏板踩下情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakePedalStatus brakePadel = 1;</code>
     */
    public road.data.proto.BrakeSystemStatus.BrakePedalStatus getBrakePadel() {
      @SuppressWarnings("deprecation")
      road.data.proto.BrakeSystemStatus.BrakePedalStatus result = road.data.proto.BrakeSystemStatus.BrakePedalStatus.valueOf(brakePadel_);
      return result == null ? road.data.proto.BrakeSystemStatus.BrakePedalStatus.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，刹车踏板踩下情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakePedalStatus brakePadel = 1;</code>
     */
    public Builder setBrakePadel(road.data.proto.BrakeSystemStatus.BrakePedalStatus value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      brakePadel_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，刹车踏板踩下情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakePedalStatus brakePadel = 1;</code>
     */
    public Builder clearBrakePadel() {
      
      brakePadel_ = 0;
      onChanged();
      return this;
    }

    private int wheelBrakes_ ;
    /**
     * <pre>
     * 可选，车轮制动情况BrakeAppliedStatus，位串，转化为二进制后，二进制第x位数字为1对应的含义：
     * </pre>
     *
     * <code>uint32 wheelBrakes = 2;</code>
     */
    public int getWheelBrakes() {
      return wheelBrakes_;
    }
    /**
     * <pre>
     * 可选，车轮制动情况BrakeAppliedStatus，位串，转化为二进制后，二进制第x位数字为1对应的含义：
     * </pre>
     *
     * <code>uint32 wheelBrakes = 2;</code>
     */
    public Builder setWheelBrakes(int value) {
      
      wheelBrakes_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，车轮制动情况BrakeAppliedStatus，位串，转化为二进制后，二进制第x位数字为1对应的含义：
     * </pre>
     *
     * <code>uint32 wheelBrakes = 2;</code>
     */
    public Builder clearWheelBrakes() {
      
      wheelBrakes_ = 0;
      onChanged();
      return this;
    }

    private int traction_ = 0;
    /**
     * <pre>
     *可选，牵引力控制系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.TractionControlStatus traction = 3;</code>
     */
    public int getTractionValue() {
      return traction_;
    }
    /**
     * <pre>
     *可选，牵引力控制系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.TractionControlStatus traction = 3;</code>
     */
    public Builder setTractionValue(int value) {
      traction_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，牵引力控制系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.TractionControlStatus traction = 3;</code>
     */
    public road.data.proto.BrakeSystemStatus.TractionControlStatus getTraction() {
      @SuppressWarnings("deprecation")
      road.data.proto.BrakeSystemStatus.TractionControlStatus result = road.data.proto.BrakeSystemStatus.TractionControlStatus.valueOf(traction_);
      return result == null ? road.data.proto.BrakeSystemStatus.TractionControlStatus.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，牵引力控制系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.TractionControlStatus traction = 3;</code>
     */
    public Builder setTraction(road.data.proto.BrakeSystemStatus.TractionControlStatus value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      traction_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，牵引力控制系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.TractionControlStatus traction = 3;</code>
     */
    public Builder clearTraction() {
      
      traction_ = 0;
      onChanged();
      return this;
    }

    private int abs_ = 0;
    /**
     * <pre>
     *可选，制动防抱死系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AntiLockBrakeStatus abs = 4;</code>
     */
    public int getAbsValue() {
      return abs_;
    }
    /**
     * <pre>
     *可选，制动防抱死系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AntiLockBrakeStatus abs = 4;</code>
     */
    public Builder setAbsValue(int value) {
      abs_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，制动防抱死系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AntiLockBrakeStatus abs = 4;</code>
     */
    public road.data.proto.BrakeSystemStatus.AntiLockBrakeStatus getAbs() {
      @SuppressWarnings("deprecation")
      road.data.proto.BrakeSystemStatus.AntiLockBrakeStatus result = road.data.proto.BrakeSystemStatus.AntiLockBrakeStatus.valueOf(abs_);
      return result == null ? road.data.proto.BrakeSystemStatus.AntiLockBrakeStatus.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，制动防抱死系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AntiLockBrakeStatus abs = 4;</code>
     */
    public Builder setAbs(road.data.proto.BrakeSystemStatus.AntiLockBrakeStatus value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      abs_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，制动防抱死系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AntiLockBrakeStatus abs = 4;</code>
     */
    public Builder clearAbs() {
      
      abs_ = 0;
      onChanged();
      return this;
    }

    private int scs_ = 0;
    /**
     * <pre>
     * 可选，车身稳定控制系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.StabilityControlStatus scs = 5;</code>
     */
    public int getScsValue() {
      return scs_;
    }
    /**
     * <pre>
     * 可选，车身稳定控制系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.StabilityControlStatus scs = 5;</code>
     */
    public Builder setScsValue(int value) {
      scs_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，车身稳定控制系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.StabilityControlStatus scs = 5;</code>
     */
    public road.data.proto.BrakeSystemStatus.StabilityControlStatus getScs() {
      @SuppressWarnings("deprecation")
      road.data.proto.BrakeSystemStatus.StabilityControlStatus result = road.data.proto.BrakeSystemStatus.StabilityControlStatus.valueOf(scs_);
      return result == null ? road.data.proto.BrakeSystemStatus.StabilityControlStatus.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，车身稳定控制系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.StabilityControlStatus scs = 5;</code>
     */
    public Builder setScs(road.data.proto.BrakeSystemStatus.StabilityControlStatus value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      scs_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，车身稳定控制系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.StabilityControlStatus scs = 5;</code>
     */
    public Builder clearScs() {
      
      scs_ = 0;
      onChanged();
      return this;
    }

    private int brakeBoost_ = 0;
    /**
     * <pre>
     *可选，刹车助力系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakeBoostApplied brakeBoost = 6;</code>
     */
    public int getBrakeBoostValue() {
      return brakeBoost_;
    }
    /**
     * <pre>
     *可选，刹车助力系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakeBoostApplied brakeBoost = 6;</code>
     */
    public Builder setBrakeBoostValue(int value) {
      brakeBoost_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，刹车助力系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakeBoostApplied brakeBoost = 6;</code>
     */
    public road.data.proto.BrakeSystemStatus.BrakeBoostApplied getBrakeBoost() {
      @SuppressWarnings("deprecation")
      road.data.proto.BrakeSystemStatus.BrakeBoostApplied result = road.data.proto.BrakeSystemStatus.BrakeBoostApplied.valueOf(brakeBoost_);
      return result == null ? road.data.proto.BrakeSystemStatus.BrakeBoostApplied.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，刹车助力系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakeBoostApplied brakeBoost = 6;</code>
     */
    public Builder setBrakeBoost(road.data.proto.BrakeSystemStatus.BrakeBoostApplied value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      brakeBoost_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，刹车助力系统作用情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakeBoostApplied brakeBoost = 6;</code>
     */
    public Builder clearBrakeBoost() {
      
      brakeBoost_ = 0;
      onChanged();
      return this;
    }

    private int auxBrakes_ = 0;
    /**
     * <pre>
     *可选，辅助制动系统（一般指手刹）情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AuxiliaryBrakeStatus auxBrakes = 7;</code>
     */
    public int getAuxBrakesValue() {
      return auxBrakes_;
    }
    /**
     * <pre>
     *可选，辅助制动系统（一般指手刹）情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AuxiliaryBrakeStatus auxBrakes = 7;</code>
     */
    public Builder setAuxBrakesValue(int value) {
      auxBrakes_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，辅助制动系统（一般指手刹）情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AuxiliaryBrakeStatus auxBrakes = 7;</code>
     */
    public road.data.proto.BrakeSystemStatus.AuxiliaryBrakeStatus getAuxBrakes() {
      @SuppressWarnings("deprecation")
      road.data.proto.BrakeSystemStatus.AuxiliaryBrakeStatus result = road.data.proto.BrakeSystemStatus.AuxiliaryBrakeStatus.valueOf(auxBrakes_);
      return result == null ? road.data.proto.BrakeSystemStatus.AuxiliaryBrakeStatus.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，辅助制动系统（一般指手刹）情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AuxiliaryBrakeStatus auxBrakes = 7;</code>
     */
    public Builder setAuxBrakes(road.data.proto.BrakeSystemStatus.AuxiliaryBrakeStatus value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      auxBrakes_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，辅助制动系统（一般指手刹）情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AuxiliaryBrakeStatus auxBrakes = 7;</code>
     */
    public Builder clearAuxBrakes() {
      
      auxBrakes_ = 0;
      onChanged();
      return this;
    }

    private int brakeControl_ ;
    /**
     * <pre>
     *可选，刹车踩踏强度 百分比：0~100%，精度0.1% BrakeControl类型
     * </pre>
     *
     * <code>uint32 brakeControl = 8;</code>
     */
    public int getBrakeControl() {
      return brakeControl_;
    }
    /**
     * <pre>
     *可选，刹车踩踏强度 百分比：0~100%，精度0.1% BrakeControl类型
     * </pre>
     *
     * <code>uint32 brakeControl = 8;</code>
     */
    public Builder setBrakeControl(int value) {
      
      brakeControl_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，刹车踩踏强度 百分比：0~100%，精度0.1% BrakeControl类型
     * </pre>
     *
     * <code>uint32 brakeControl = 8;</code>
     */
    public Builder clearBrakeControl() {
      
      brakeControl_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.BrakeSystemStatus)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.BrakeSystemStatus)
  private static final road.data.proto.BrakeSystemStatus DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.BrakeSystemStatus();
  }

  public static road.data.proto.BrakeSystemStatus getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BrakeSystemStatus>
      PARSER = new com.google.protobuf.AbstractParser<BrakeSystemStatus>() {
    @java.lang.Override
    public BrakeSystemStatus parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new BrakeSystemStatus(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<BrakeSystemStatus> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BrakeSystemStatus> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.BrakeSystemStatus getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

