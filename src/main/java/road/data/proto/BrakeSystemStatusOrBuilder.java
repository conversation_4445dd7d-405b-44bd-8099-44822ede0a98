// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface BrakeSystemStatusOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.BrakeSystemStatus)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 可选，刹车踏板踩下情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakePedalStatus brakePadel = 1;</code>
   */
  int getBrakePadelValue();
  /**
   * <pre>
   * 可选，刹车踏板踩下情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakePedalStatus brakePadel = 1;</code>
   */
  road.data.proto.BrakeSystemStatus.BrakePedalStatus getBrakePadel();

  /**
   * <pre>
   * 可选，车轮制动情况BrakeAppliedStatus，位串，转化为二进制后，二进制第x位数字为1对应的含义：
   * </pre>
   *
   * <code>uint32 wheelBrakes = 2;</code>
   */
  int getWheelBrakes();

  /**
   * <pre>
   *可选，牵引力控制系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.TractionControlStatus traction = 3;</code>
   */
  int getTractionValue();
  /**
   * <pre>
   *可选，牵引力控制系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.TractionControlStatus traction = 3;</code>
   */
  road.data.proto.BrakeSystemStatus.TractionControlStatus getTraction();

  /**
   * <pre>
   *可选，制动防抱死系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AntiLockBrakeStatus abs = 4;</code>
   */
  int getAbsValue();
  /**
   * <pre>
   *可选，制动防抱死系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AntiLockBrakeStatus abs = 4;</code>
   */
  road.data.proto.BrakeSystemStatus.AntiLockBrakeStatus getAbs();

  /**
   * <pre>
   * 可选，车身稳定控制系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.StabilityControlStatus scs = 5;</code>
   */
  int getScsValue();
  /**
   * <pre>
   * 可选，车身稳定控制系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.StabilityControlStatus scs = 5;</code>
   */
  road.data.proto.BrakeSystemStatus.StabilityControlStatus getScs();

  /**
   * <pre>
   *可选，刹车助力系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakeBoostApplied brakeBoost = 6;</code>
   */
  int getBrakeBoostValue();
  /**
   * <pre>
   *可选，刹车助力系统作用情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.BrakeBoostApplied brakeBoost = 6;</code>
   */
  road.data.proto.BrakeSystemStatus.BrakeBoostApplied getBrakeBoost();

  /**
   * <pre>
   *可选，辅助制动系统（一般指手刹）情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AuxiliaryBrakeStatus auxBrakes = 7;</code>
   */
  int getAuxBrakesValue();
  /**
   * <pre>
   *可选，辅助制动系统（一般指手刹）情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus.AuxiliaryBrakeStatus auxBrakes = 7;</code>
   */
  road.data.proto.BrakeSystemStatus.AuxiliaryBrakeStatus getAuxBrakes();

  /**
   * <pre>
   *可选，刹车踩踏强度 百分比：0~100%，精度0.1% BrakeControl类型
   * </pre>
   *
   * <code>uint32 brakeControl = 8;</code>
   */
  int getBrakeControl();
}
