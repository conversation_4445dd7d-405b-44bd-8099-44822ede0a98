// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *基本安全信息BSM   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.BsmData}
 */
public  final class BsmData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.BsmData)
    BsmDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use BsmData.newBuilder() to construct.
  private BsmData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private BsmData() {
    obuId_ = "";
    plateNo_ = "";
    transmission_ = 0;
    vehicleType_ = 0;
    fuelType_ = 0;
    driveModedriveStatus_ = 0;
    emergencyStatus_ = 0;
    wiper_ = 0;
    outofControl_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new BsmData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private BsmData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            obuId_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            plateNo_ = s;
            break;
          }
          case 24: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 34: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (pos_ != null) {
              subBuilder = pos_.toBuilder();
            }
            pos_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(pos_);
              pos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 42: {
            road.data.proto.PositionConfidenceSet.Builder subBuilder = null;
            if (posConfid_ != null) {
              subBuilder = posConfid_.toBuilder();
            }
            posConfid_ = input.readMessage(road.data.proto.PositionConfidenceSet.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(posConfid_);
              posConfid_ = subBuilder.buildPartial();
            }

            break;
          }
          case 50: {
            road.data.proto.PositionAccuracy.Builder subBuilder = null;
            if (posAccuracy_ != null) {
              subBuilder = posAccuracy_.toBuilder();
            }
            posAccuracy_ = input.readMessage(road.data.proto.PositionAccuracy.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(posAccuracy_);
              posAccuracy_ = subBuilder.buildPartial();
            }

            break;
          }
          case 58: {
            road.data.proto.AccelerationSet4Way.Builder subBuilder = null;
            if (acceleration_ != null) {
              subBuilder = acceleration_.toBuilder();
            }
            acceleration_ = input.readMessage(road.data.proto.AccelerationSet4Way.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(acceleration_);
              acceleration_ = subBuilder.buildPartial();
            }

            break;
          }
          case 64: {
            int rawValue = input.readEnum();

            transmission_ = rawValue;
            break;
          }
          case 72: {

            speed_ = input.readUInt32();
            break;
          }
          case 80: {

            heading_ = input.readUInt32();
            break;
          }
          case 88: {

            steeringWheelAngle_ = input.readInt32();
            break;
          }
          case 98: {
            road.data.proto.MotionConfidenceSet.Builder subBuilder = null;
            if (motionConfid_ != null) {
              subBuilder = motionConfid_.toBuilder();
            }
            motionConfid_ = input.readMessage(road.data.proto.MotionConfidenceSet.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(motionConfid_);
              motionConfid_ = subBuilder.buildPartial();
            }

            break;
          }
          case 106: {
            road.data.proto.BrakeSystemStatus.Builder subBuilder = null;
            if (brakes_ != null) {
              subBuilder = brakes_.toBuilder();
            }
            brakes_ = input.readMessage(road.data.proto.BrakeSystemStatus.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(brakes_);
              brakes_ = subBuilder.buildPartial();
            }

            break;
          }
          case 114: {
            road.data.proto.ThrottleSystemStatus.Builder subBuilder = null;
            if (throttle_ != null) {
              subBuilder = throttle_.toBuilder();
            }
            throttle_ = input.readMessage(road.data.proto.ThrottleSystemStatus.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(throttle_);
              throttle_ = subBuilder.buildPartial();
            }

            break;
          }
          case 122: {
            road.data.proto.VehicleSize.Builder subBuilder = null;
            if (size_ != null) {
              subBuilder = size_.toBuilder();
            }
            size_ = input.readMessage(road.data.proto.VehicleSize.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(size_);
              size_ = subBuilder.buildPartial();
            }

            break;
          }
          case 128: {
            int rawValue = input.readEnum();

            vehicleType_ = rawValue;
            break;
          }
          case 136: {
            int rawValue = input.readEnum();

            fuelType_ = rawValue;
            break;
          }
          case 144: {
            int rawValue = input.readEnum();

            driveModedriveStatus_ = rawValue;
            break;
          }
          case 152: {
            int rawValue = input.readEnum();

            emergencyStatus_ = rawValue;
            break;
          }
          case 160: {

            light_ = input.readUInt32();
            break;
          }
          case 168: {
            int rawValue = input.readEnum();

            wiper_ = rawValue;
            break;
          }
          case 176: {
            int rawValue = input.readEnum();

            outofControl_ = rawValue;
            break;
          }
          case 184: {

            endurance_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_BsmData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_BsmData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.BsmData.class, road.data.proto.BsmData.Builder.class);
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.BsmData.TransmissionState}
   */
  public enum TransmissionState
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 空挡;
     * </pre>
     *
     * <code>TRANSMISSION_NEUTRAL = 0;</code>
     */
    TRANSMISSION_NEUTRAL(0),
    /**
     * <pre>
     *停止档;
     * </pre>
     *
     * <code>TRANSMISSION_PARK = 1;</code>
     */
    TRANSMISSION_PARK(1),
    /**
     * <pre>
     * 前进档;
     * </pre>
     *
     * <code>TRANSMISSION_FORWARD_GEARS = 2;</code>
     */
    TRANSMISSION_FORWARD_GEARS(2),
    /**
     * <pre>
     * 倒挡
     * </pre>
     *
     * <code>TRANSMISSION_REVERSE_GEARS = 3;</code>
     */
    TRANSMISSION_REVERSE_GEARS(3),
    /**
     * <pre>
     * 保留
     * </pre>
     *
     * <code>TRANSMISSION_RESERVED1 = 4;</code>
     */
    TRANSMISSION_RESERVED1(4),
    /**
     * <pre>
     * 保留
     * </pre>
     *
     * <code>TRANSMISSION_RESERVED2 = 5;</code>
     */
    TRANSMISSION_RESERVED2(5),
    /**
     * <pre>
     * 保留
     * </pre>
     *
     * <code>TRANSMISSION_RESERVED3 = 6;</code>
     */
    TRANSMISSION_RESERVED3(6),
    /**
     * <pre>
     * 未配备或不可用的值，
     * </pre>
     *
     * <code>TRANSMISSION_UNAVAILABLE = 7;</code>
     */
    TRANSMISSION_UNAVAILABLE(7),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * 空挡;
     * </pre>
     *
     * <code>TRANSMISSION_NEUTRAL = 0;</code>
     */
    public static final int TRANSMISSION_NEUTRAL_VALUE = 0;
    /**
     * <pre>
     *停止档;
     * </pre>
     *
     * <code>TRANSMISSION_PARK = 1;</code>
     */
    public static final int TRANSMISSION_PARK_VALUE = 1;
    /**
     * <pre>
     * 前进档;
     * </pre>
     *
     * <code>TRANSMISSION_FORWARD_GEARS = 2;</code>
     */
    public static final int TRANSMISSION_FORWARD_GEARS_VALUE = 2;
    /**
     * <pre>
     * 倒挡
     * </pre>
     *
     * <code>TRANSMISSION_REVERSE_GEARS = 3;</code>
     */
    public static final int TRANSMISSION_REVERSE_GEARS_VALUE = 3;
    /**
     * <pre>
     * 保留
     * </pre>
     *
     * <code>TRANSMISSION_RESERVED1 = 4;</code>
     */
    public static final int TRANSMISSION_RESERVED1_VALUE = 4;
    /**
     * <pre>
     * 保留
     * </pre>
     *
     * <code>TRANSMISSION_RESERVED2 = 5;</code>
     */
    public static final int TRANSMISSION_RESERVED2_VALUE = 5;
    /**
     * <pre>
     * 保留
     * </pre>
     *
     * <code>TRANSMISSION_RESERVED3 = 6;</code>
     */
    public static final int TRANSMISSION_RESERVED3_VALUE = 6;
    /**
     * <pre>
     * 未配备或不可用的值，
     * </pre>
     *
     * <code>TRANSMISSION_UNAVAILABLE = 7;</code>
     */
    public static final int TRANSMISSION_UNAVAILABLE_VALUE = 7;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TransmissionState valueOf(int value) {
      return forNumber(value);
    }

    public static TransmissionState forNumber(int value) {
      switch (value) {
        case 0: return TRANSMISSION_NEUTRAL;
        case 1: return TRANSMISSION_PARK;
        case 2: return TRANSMISSION_FORWARD_GEARS;
        case 3: return TRANSMISSION_REVERSE_GEARS;
        case 4: return TRANSMISSION_RESERVED1;
        case 5: return TRANSMISSION_RESERVED2;
        case 6: return TRANSMISSION_RESERVED3;
        case 7: return TRANSMISSION_UNAVAILABLE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<TransmissionState>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        TransmissionState> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<TransmissionState>() {
            public TransmissionState findValueByNumber(int number) {
              return TransmissionState.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.BsmData.getDescriptor().getEnumTypes().get(0);
    }

    private static final TransmissionState[] VALUES = values();

    public static TransmissionState valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private TransmissionState(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.BsmData.TransmissionState)
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.BsmData.VehicleFuelType}
   */
  public enum VehicleFuelType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 汽油动力
     * </pre>
     *
     * <code>VEHICLE_TUEL_UNKNOWNFUEL = 0;</code>
     */
    VEHICLE_TUEL_UNKNOWNFUEL(0),
    /**
     * <pre>
     * 汽油燃料类型
     * </pre>
     *
     * <code>GASOLINE = 1;</code>
     */
    GASOLINE(1),
    /**
     * <pre>
     * 乙醇（包括混合）
     * </pre>
     *
     * <code>ETHANOL = 2;</code>
     */
    ETHANOL(2),
    /**
     * <pre>
     * 柴油机
     * </pre>
     *
     * <code>DIESEL = 3;</code>
     */
    DIESEL(3),
    /**
     * <pre>
     * 电动
     * </pre>
     *
     * <code>ELECTRIC = 4;</code>
     */
    ELECTRIC(4),
    /**
     * <pre>
     * 混合
     * </pre>
     *
     * <code>HYBRID = 5;</code>
     */
    HYBRID(5),
    /**
     * <pre>
     * 氢燃料类型
     * </pre>
     *
     * <code>HYDROGEN = 6;</code>
     */
    HYDROGEN(6),
    /**
     * <pre>
     * 天然气液化
     * </pre>
     *
     * <code>NATGASLIQUID = 7;</code>
     */
    NATGASLIQUID(7),
    /**
     * <pre>
     * 天然气压缩
     * </pre>
     *
     * <code>NATGASCOMP = 8;</code>
     */
    NATGASCOMP(8),
    /**
     * <pre>
     * 丙烷
     * </pre>
     *
     * <code>PROPANE = 9;</code>
     */
    PROPANE(9),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * 汽油动力
     * </pre>
     *
     * <code>VEHICLE_TUEL_UNKNOWNFUEL = 0;</code>
     */
    public static final int VEHICLE_TUEL_UNKNOWNFUEL_VALUE = 0;
    /**
     * <pre>
     * 汽油燃料类型
     * </pre>
     *
     * <code>GASOLINE = 1;</code>
     */
    public static final int GASOLINE_VALUE = 1;
    /**
     * <pre>
     * 乙醇（包括混合）
     * </pre>
     *
     * <code>ETHANOL = 2;</code>
     */
    public static final int ETHANOL_VALUE = 2;
    /**
     * <pre>
     * 柴油机
     * </pre>
     *
     * <code>DIESEL = 3;</code>
     */
    public static final int DIESEL_VALUE = 3;
    /**
     * <pre>
     * 电动
     * </pre>
     *
     * <code>ELECTRIC = 4;</code>
     */
    public static final int ELECTRIC_VALUE = 4;
    /**
     * <pre>
     * 混合
     * </pre>
     *
     * <code>HYBRID = 5;</code>
     */
    public static final int HYBRID_VALUE = 5;
    /**
     * <pre>
     * 氢燃料类型
     * </pre>
     *
     * <code>HYDROGEN = 6;</code>
     */
    public static final int HYDROGEN_VALUE = 6;
    /**
     * <pre>
     * 天然气液化
     * </pre>
     *
     * <code>NATGASLIQUID = 7;</code>
     */
    public static final int NATGASLIQUID_VALUE = 7;
    /**
     * <pre>
     * 天然气压缩
     * </pre>
     *
     * <code>NATGASCOMP = 8;</code>
     */
    public static final int NATGASCOMP_VALUE = 8;
    /**
     * <pre>
     * 丙烷
     * </pre>
     *
     * <code>PROPANE = 9;</code>
     */
    public static final int PROPANE_VALUE = 9;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static VehicleFuelType valueOf(int value) {
      return forNumber(value);
    }

    public static VehicleFuelType forNumber(int value) {
      switch (value) {
        case 0: return VEHICLE_TUEL_UNKNOWNFUEL;
        case 1: return GASOLINE;
        case 2: return ETHANOL;
        case 3: return DIESEL;
        case 4: return ELECTRIC;
        case 5: return HYBRID;
        case 6: return HYDROGEN;
        case 7: return NATGASLIQUID;
        case 8: return NATGASCOMP;
        case 9: return PROPANE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<VehicleFuelType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        VehicleFuelType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<VehicleFuelType>() {
            public VehicleFuelType findValueByNumber(int number) {
              return VehicleFuelType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.BsmData.getDescriptor().getEnumTypes().get(1);
    }

    private static final VehicleFuelType[] VALUES = values();

    public static VehicleFuelType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private VehicleFuelType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.BsmData.VehicleFuelType)
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.BsmData.DriveStatus}
   */
  public enum DriveStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *自动驾驶模式
     * </pre>
     *
     * <code>AUTOPILOT = 0;</code>
     */
    AUTOPILOT(0),
    /**
     * <pre>
     *人工驾驶模式
     * </pre>
     *
     * <code>MANUAL = 1;</code>
     */
    MANUAL(1),
    /**
     * <pre>
     *人工接管模式
     * </pre>
     *
     * <code>SECURITY = 2;</code>
     */
    SECURITY(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *自动驾驶模式
     * </pre>
     *
     * <code>AUTOPILOT = 0;</code>
     */
    public static final int AUTOPILOT_VALUE = 0;
    /**
     * <pre>
     *人工驾驶模式
     * </pre>
     *
     * <code>MANUAL = 1;</code>
     */
    public static final int MANUAL_VALUE = 1;
    /**
     * <pre>
     *人工接管模式
     * </pre>
     *
     * <code>SECURITY = 2;</code>
     */
    public static final int SECURITY_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static DriveStatus valueOf(int value) {
      return forNumber(value);
    }

    public static DriveStatus forNumber(int value) {
      switch (value) {
        case 0: return AUTOPILOT;
        case 1: return MANUAL;
        case 2: return SECURITY;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<DriveStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        DriveStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<DriveStatus>() {
            public DriveStatus findValueByNumber(int number) {
              return DriveStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.BsmData.getDescriptor().getEnumTypes().get(2);
    }

    private static final DriveStatus[] VALUES = values();

    public static DriveStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private DriveStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.BsmData.DriveStatus)
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.BsmData.EmergenyStatus}
   */
  public enum EmergenyStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>NO_EMER = 0;</code>
     */
    NO_EMER(0),
    /**
     * <code>YES_EMER = 1;</code>
     */
    YES_EMER(1),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>NO_EMER = 0;</code>
     */
    public static final int NO_EMER_VALUE = 0;
    /**
     * <code>YES_EMER = 1;</code>
     */
    public static final int YES_EMER_VALUE = 1;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EmergenyStatus valueOf(int value) {
      return forNumber(value);
    }

    public static EmergenyStatus forNumber(int value) {
      switch (value) {
        case 0: return NO_EMER;
        case 1: return YES_EMER;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EmergenyStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EmergenyStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EmergenyStatus>() {
            public EmergenyStatus findValueByNumber(int number) {
              return EmergenyStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.BsmData.getDescriptor().getEnumTypes().get(3);
    }

    private static final EmergenyStatus[] VALUES = values();

    public static EmergenyStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EmergenyStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.BsmData.EmergenyStatus)
  }

  /**
   * <pre>
   *1：OFF 未开灯；
   *2：LEFT 左转灯；
   *3-RIGHT 右转灯；
   *4-EMERGENCY 双闪；
   *5-REVERSE 倒车；
   *6-FOG 雾灯；
   *7-DIP 近光灯；
   *8-HIGH 远光灯
   * </pre>
   *
   * Protobuf enum {@code cn.seisys.v2x.pb.BsmData.Wiper}
   */
  public enum Wiper
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *未开启；
     * </pre>
     *
     * <code>OFF = 0;</code>
     */
    OFF(0),
    /**
     * <pre>
     * 间歇；
     * </pre>
     *
     * <code>INT = 1;</code>
     */
    INT(1),
    /**
     * <pre>
     *低速；
     * </pre>
     *
     * <code>LO = 2;</code>
     */
    LO(2),
    /**
     * <pre>
     *高速
     * </pre>
     *
     * <code>HI = 3;</code>
     */
    HI(3),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *未开启；
     * </pre>
     *
     * <code>OFF = 0;</code>
     */
    public static final int OFF_VALUE = 0;
    /**
     * <pre>
     * 间歇；
     * </pre>
     *
     * <code>INT = 1;</code>
     */
    public static final int INT_VALUE = 1;
    /**
     * <pre>
     *低速；
     * </pre>
     *
     * <code>LO = 2;</code>
     */
    public static final int LO_VALUE = 2;
    /**
     * <pre>
     *高速
     * </pre>
     *
     * <code>HI = 3;</code>
     */
    public static final int HI_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static Wiper valueOf(int value) {
      return forNumber(value);
    }

    public static Wiper forNumber(int value) {
      switch (value) {
        case 0: return OFF;
        case 1: return INT;
        case 2: return LO;
        case 3: return HI;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<Wiper>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        Wiper> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<Wiper>() {
            public Wiper findValueByNumber(int number) {
              return Wiper.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.BsmData.getDescriptor().getEnumTypes().get(4);
    }

    private static final Wiper[] VALUES = values();

    public static Wiper valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private Wiper(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.BsmData.Wiper)
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.BsmData.OutofControl}
   */
  public enum OutofControl
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>NO_OUTCON = 0;</code>
     */
    NO_OUTCON(0),
    /**
     * <code>YES_OUTCON = 1;</code>
     */
    YES_OUTCON(1),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>NO_OUTCON = 0;</code>
     */
    public static final int NO_OUTCON_VALUE = 0;
    /**
     * <code>YES_OUTCON = 1;</code>
     */
    public static final int YES_OUTCON_VALUE = 1;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static OutofControl valueOf(int value) {
      return forNumber(value);
    }

    public static OutofControl forNumber(int value) {
      switch (value) {
        case 0: return NO_OUTCON;
        case 1: return YES_OUTCON;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<OutofControl>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        OutofControl> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<OutofControl>() {
            public OutofControl findValueByNumber(int number) {
              return OutofControl.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.BsmData.getDescriptor().getEnumTypes().get(5);
    }

    private static final OutofControl[] VALUES = values();

    public static OutofControl valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private OutofControl(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.BsmData.OutofControl)
  }

  public static final int OBUID_FIELD_NUMBER = 1;
  private volatile java.lang.Object obuId_;
  /**
   * <pre>
   * 车辆 Id（OBU 设备序列号）
   * </pre>
   *
   * <code>string obuId = 1;</code>
   */
  public java.lang.String getObuId() {
    java.lang.Object ref = obuId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      obuId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 车辆 Id（OBU 设备序列号）
   * </pre>
   *
   * <code>string obuId = 1;</code>
   */
  public com.google.protobuf.ByteString
      getObuIdBytes() {
    java.lang.Object ref = obuId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      obuId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PLATENO_FIELD_NUMBER = 2;
  private volatile java.lang.Object plateNo_;
  /**
   * <pre>
   * 可选，车牌号，字符串，最大为36个字符，支持中文和数字
   * </pre>
   *
   * <code>string plateNo = 2;</code>
   */
  public java.lang.String getPlateNo() {
    java.lang.Object ref = plateNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      plateNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 可选，车牌号，字符串，最大为36个字符，支持中文和数字
   * </pre>
   *
   * <code>string plateNo = 2;</code>
   */
  public com.google.protobuf.ByteString
      getPlateNoBytes() {
    java.lang.Object ref = plateNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      plateNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 3;
  private long timestamp_;
  /**
   * <pre>
   * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 3;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int POS_FIELD_NUMBER = 4;
  private road.data.proto.Position3D pos_;
  /**
   * <pre>
   * 经纬度和高程信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  public boolean hasPos() {
    return pos_ != null;
  }
  /**
   * <pre>
   * 经纬度和高程信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  public road.data.proto.Position3D getPos() {
    return pos_ == null ? road.data.proto.Position3D.getDefaultInstance() : pos_;
  }
  /**
   * <pre>
   * 经纬度和高程信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  public road.data.proto.Position3DOrBuilder getPosOrBuilder() {
    return getPos();
  }

  public static final int POSCONFID_FIELD_NUMBER = 5;
  private road.data.proto.PositionConfidenceSet posConfid_;
  /**
   * <pre>
   * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
   */
  public boolean hasPosConfid() {
    return posConfid_ != null;
  }
  /**
   * <pre>
   * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
   */
  public road.data.proto.PositionConfidenceSet getPosConfid() {
    return posConfid_ == null ? road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
  }
  /**
   * <pre>
   * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
   */
  public road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder() {
    return getPosConfid();
  }

  public static final int POSACCURACY_FIELD_NUMBER = 6;
  private road.data.proto.PositionAccuracy posAccuracy_;
  /**
   * <pre>
   * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
   */
  public boolean hasPosAccuracy() {
    return posAccuracy_ != null;
  }
  /**
   * <pre>
   * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
   */
  public road.data.proto.PositionAccuracy getPosAccuracy() {
    return posAccuracy_ == null ? road.data.proto.PositionAccuracy.getDefaultInstance() : posAccuracy_;
  }
  /**
   * <pre>
   * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
   */
  public road.data.proto.PositionAccuracyOrBuilder getPosAccuracyOrBuilder() {
    return getPosAccuracy();
  }

  public static final int ACCELERATION_FIELD_NUMBER = 7;
  private road.data.proto.AccelerationSet4Way acceleration_;
  /**
   * <pre>
   * 可选，加速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
   */
  public boolean hasAcceleration() {
    return acceleration_ != null;
  }
  /**
   * <pre>
   * 可选，加速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
   */
  public road.data.proto.AccelerationSet4Way getAcceleration() {
    return acceleration_ == null ? road.data.proto.AccelerationSet4Way.getDefaultInstance() : acceleration_;
  }
  /**
   * <pre>
   * 可选，加速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
   */
  public road.data.proto.AccelerationSet4WayOrBuilder getAccelerationOrBuilder() {
    return getAcceleration();
  }

  public static final int TRANSMISSION_FIELD_NUMBER = 8;
  private int transmission_;
  /**
   * <pre>
   *可选，车辆档位
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.TransmissionState transmission = 8;</code>
   */
  public int getTransmissionValue() {
    return transmission_;
  }
  /**
   * <pre>
   *可选，车辆档位
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.TransmissionState transmission = 8;</code>
   */
  public road.data.proto.BsmData.TransmissionState getTransmission() {
    @SuppressWarnings("deprecation")
    road.data.proto.BsmData.TransmissionState result = road.data.proto.BsmData.TransmissionState.valueOf(transmission_);
    return result == null ? road.data.proto.BsmData.TransmissionState.UNRECOGNIZED : result;
  }

  public static final int SPEED_FIELD_NUMBER = 9;
  private int speed_;
  /**
   * <pre>
   * 定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
   * </pre>
   *
   * <code>uint32 speed = 9;</code>
   */
  public int getSpeed() {
    return speed_;
  }

  public static final int HEADING_FIELD_NUMBER = 10;
  private int heading_;
  /**
   * <pre>
   * 航向角，为车头方向与正北方向的顺时针夹角。分辨率为0.0125°
   * </pre>
   *
   * <code>uint32 heading = 10;</code>
   */
  public int getHeading() {
    return heading_;
  }

  public static final int STEERINGWHEELANGLE_FIELD_NUMBER = 11;
  private int steeringWheelAngle_;
  /**
   * <pre>
   * 可选，[_240, 240]	方向盘角度，分辨率为1.5°
   * </pre>
   *
   * <code>int32 steeringWheelAngle = 11;</code>
   */
  public int getSteeringWheelAngle() {
    return steeringWheelAngle_;
  }

  public static final int MOTIONCONFID_FIELD_NUMBER = 12;
  private road.data.proto.MotionConfidenceSet motionConfid_;
  /**
   * <pre>
   *可选，车辆运动状态精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  public boolean hasMotionConfid() {
    return motionConfid_ != null;
  }
  /**
   * <pre>
   *可选，车辆运动状态精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  public road.data.proto.MotionConfidenceSet getMotionConfid() {
    return motionConfid_ == null ? road.data.proto.MotionConfidenceSet.getDefaultInstance() : motionConfid_;
  }
  /**
   * <pre>
   *可选，车辆运动状态精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  public road.data.proto.MotionConfidenceSetOrBuilder getMotionConfidOrBuilder() {
    return getMotionConfid();
  }

  public static final int BRAKES_FIELD_NUMBER = 13;
  private road.data.proto.BrakeSystemStatus brakes_;
  /**
   * <pre>
   * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
   */
  public boolean hasBrakes() {
    return brakes_ != null;
  }
  /**
   * <pre>
   * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
   */
  public road.data.proto.BrakeSystemStatus getBrakes() {
    return brakes_ == null ? road.data.proto.BrakeSystemStatus.getDefaultInstance() : brakes_;
  }
  /**
   * <pre>
   * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
   */
  public road.data.proto.BrakeSystemStatusOrBuilder getBrakesOrBuilder() {
    return getBrakes();
  }

  public static final int THROTTLE_FIELD_NUMBER = 14;
  private road.data.proto.ThrottleSystemStatus throttle_;
  /**
   * <pre>
   *可选，定义车辆的给油系统状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
   */
  public boolean hasThrottle() {
    return throttle_ != null;
  }
  /**
   * <pre>
   *可选，定义车辆的给油系统状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
   */
  public road.data.proto.ThrottleSystemStatus getThrottle() {
    return throttle_ == null ? road.data.proto.ThrottleSystemStatus.getDefaultInstance() : throttle_;
  }
  /**
   * <pre>
   *可选，定义车辆的给油系统状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
   */
  public road.data.proto.ThrottleSystemStatusOrBuilder getThrottleOrBuilder() {
    return getThrottle();
  }

  public static final int SIZE_FIELD_NUMBER = 15;
  private road.data.proto.VehicleSize size_;
  /**
   * <pre>
   * 车辆尺寸
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
   */
  public boolean hasSize() {
    return size_ != null;
  }
  /**
   * <pre>
   * 车辆尺寸
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
   */
  public road.data.proto.VehicleSize getSize() {
    return size_ == null ? road.data.proto.VehicleSize.getDefaultInstance() : size_;
  }
  /**
   * <pre>
   * 车辆尺寸
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
   */
  public road.data.proto.VehicleSizeOrBuilder getSizeOrBuilder() {
    return getSize();
  }

  public static final int VEHICLETYPE_FIELD_NUMBER = 16;
  private int vehicleType_;
  /**
   * <pre>
   * 车辆基本类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 16;</code>
   */
  public int getVehicleTypeValue() {
    return vehicleType_;
  }
  /**
   * <pre>
   * 车辆基本类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 16;</code>
   */
  public road.data.proto.VehicleType getVehicleType() {
    @SuppressWarnings("deprecation")
    road.data.proto.VehicleType result = road.data.proto.VehicleType.valueOf(vehicleType_);
    return result == null ? road.data.proto.VehicleType.UNRECOGNIZED : result;
  }

  public static final int FUELTYPE_FIELD_NUMBER = 17;
  private int fuelType_;
  /**
   * <pre>
   * 可选，车辆燃油类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.VehicleFuelType fuelType = 17;</code>
   */
  public int getFuelTypeValue() {
    return fuelType_;
  }
  /**
   * <pre>
   * 可选，车辆燃油类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.VehicleFuelType fuelType = 17;</code>
   */
  public road.data.proto.BsmData.VehicleFuelType getFuelType() {
    @SuppressWarnings("deprecation")
    road.data.proto.BsmData.VehicleFuelType result = road.data.proto.BsmData.VehicleFuelType.valueOf(fuelType_);
    return result == null ? road.data.proto.BsmData.VehicleFuelType.UNRECOGNIZED : result;
  }

  public static final int DRIVEMODEDRIVESTATUS_FIELD_NUMBER = 18;
  private int driveModedriveStatus_;
  /**
   * <pre>
   * 可选，驾驶状态 1：自动驾驶模式 2：人工驾驶模式 3：人工接管模式
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.DriveStatus driveModedriveStatus = 18;</code>
   */
  public int getDriveModedriveStatusValue() {
    return driveModedriveStatus_;
  }
  /**
   * <pre>
   * 可选，驾驶状态 1：自动驾驶模式 2：人工驾驶模式 3：人工接管模式
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.DriveStatus driveModedriveStatus = 18;</code>
   */
  public road.data.proto.BsmData.DriveStatus getDriveModedriveStatus() {
    @SuppressWarnings("deprecation")
    road.data.proto.BsmData.DriveStatus result = road.data.proto.BsmData.DriveStatus.valueOf(driveModedriveStatus_);
    return result == null ? road.data.proto.BsmData.DriveStatus.UNRECOGNIZED : result;
  }

  public static final int EMERGENCYSTATUS_FIELD_NUMBER = 19;
  private int emergencyStatus_;
  /**
   * <pre>
   * 可选，危险报警闪光灯（双闪） 0否 1是
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.EmergenyStatus emergencyStatus = 19;</code>
   */
  public int getEmergencyStatusValue() {
    return emergencyStatus_;
  }
  /**
   * <pre>
   * 可选，危险报警闪光灯（双闪） 0否 1是
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.EmergenyStatus emergencyStatus = 19;</code>
   */
  public road.data.proto.BsmData.EmergenyStatus getEmergencyStatus() {
    @SuppressWarnings("deprecation")
    road.data.proto.BsmData.EmergenyStatus result = road.data.proto.BsmData.EmergenyStatus.valueOf(emergencyStatus_);
    return result == null ? road.data.proto.BsmData.EmergenyStatus.UNRECOGNIZED : result;
  }

  public static final int LIGHT_FIELD_NUMBER = 20;
  private int light_;
  /**
   * <pre>
   *可选，灯光状态，转化为二进制后，二进制左起第x位数字为1对应的含义：
   * </pre>
   *
   * <code>uint32 light = 20;</code>
   */
  public int getLight() {
    return light_;
  }

  public static final int WIPER_FIELD_NUMBER = 21;
  private int wiper_;
  /**
   * <pre>
   *可选，雨刷
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.Wiper wiper = 21;</code>
   */
  public int getWiperValue() {
    return wiper_;
  }
  /**
   * <pre>
   *可选，雨刷
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.Wiper wiper = 21;</code>
   */
  public road.data.proto.BsmData.Wiper getWiper() {
    @SuppressWarnings("deprecation")
    road.data.proto.BsmData.Wiper result = road.data.proto.BsmData.Wiper.valueOf(wiper_);
    return result == null ? road.data.proto.BsmData.Wiper.UNRECOGNIZED : result;
  }

  public static final int OUTOFCONTROL_FIELD_NUMBER = 22;
  private int outofControl_;
  /**
   * <pre>
   * 可选，车辆失控 0 否 1 是
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.OutofControl outofControl = 22;</code>
   */
  public int getOutofControlValue() {
    return outofControl_;
  }
  /**
   * <pre>
   * 可选，车辆失控 0 否 1 是
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.OutofControl outofControl = 22;</code>
   */
  public road.data.proto.BsmData.OutofControl getOutofControl() {
    @SuppressWarnings("deprecation")
    road.data.proto.BsmData.OutofControl result = road.data.proto.BsmData.OutofControl.valueOf(outofControl_);
    return result == null ? road.data.proto.BsmData.OutofControl.UNRECOGNIZED : result;
  }

  public static final int ENDURANCE_FIELD_NUMBER = 23;
  private int endurance_;
  /**
   * <pre>
   * 可选，续航里程 单位0.01 km
   * </pre>
   *
   * <code>uint32 endurance = 23;</code>
   */
  public int getEndurance() {
    return endurance_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getObuIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, obuId_);
    }
    if (!getPlateNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, plateNo_);
    }
    if (timestamp_ != 0L) {
      output.writeUInt64(3, timestamp_);
    }
    if (pos_ != null) {
      output.writeMessage(4, getPos());
    }
    if (posConfid_ != null) {
      output.writeMessage(5, getPosConfid());
    }
    if (posAccuracy_ != null) {
      output.writeMessage(6, getPosAccuracy());
    }
    if (acceleration_ != null) {
      output.writeMessage(7, getAcceleration());
    }
    if (transmission_ != road.data.proto.BsmData.TransmissionState.TRANSMISSION_NEUTRAL.getNumber()) {
      output.writeEnum(8, transmission_);
    }
    if (speed_ != 0) {
      output.writeUInt32(9, speed_);
    }
    if (heading_ != 0) {
      output.writeUInt32(10, heading_);
    }
    if (steeringWheelAngle_ != 0) {
      output.writeInt32(11, steeringWheelAngle_);
    }
    if (motionConfid_ != null) {
      output.writeMessage(12, getMotionConfid());
    }
    if (brakes_ != null) {
      output.writeMessage(13, getBrakes());
    }
    if (throttle_ != null) {
      output.writeMessage(14, getThrottle());
    }
    if (size_ != null) {
      output.writeMessage(15, getSize());
    }
    if (vehicleType_ != road.data.proto.VehicleType.UNKNOWN_VEHICLE_CLASS.getNumber()) {
      output.writeEnum(16, vehicleType_);
    }
    if (fuelType_ != road.data.proto.BsmData.VehicleFuelType.VEHICLE_TUEL_UNKNOWNFUEL.getNumber()) {
      output.writeEnum(17, fuelType_);
    }
    if (driveModedriveStatus_ != road.data.proto.BsmData.DriveStatus.AUTOPILOT.getNumber()) {
      output.writeEnum(18, driveModedriveStatus_);
    }
    if (emergencyStatus_ != road.data.proto.BsmData.EmergenyStatus.NO_EMER.getNumber()) {
      output.writeEnum(19, emergencyStatus_);
    }
    if (light_ != 0) {
      output.writeUInt32(20, light_);
    }
    if (wiper_ != road.data.proto.BsmData.Wiper.OFF.getNumber()) {
      output.writeEnum(21, wiper_);
    }
    if (outofControl_ != road.data.proto.BsmData.OutofControl.NO_OUTCON.getNumber()) {
      output.writeEnum(22, outofControl_);
    }
    if (endurance_ != 0) {
      output.writeUInt32(23, endurance_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getObuIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, obuId_);
    }
    if (!getPlateNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, plateNo_);
    }
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, timestamp_);
    }
    if (pos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getPos());
    }
    if (posConfid_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getPosConfid());
    }
    if (posAccuracy_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getPosAccuracy());
    }
    if (acceleration_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getAcceleration());
    }
    if (transmission_ != road.data.proto.BsmData.TransmissionState.TRANSMISSION_NEUTRAL.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(8, transmission_);
    }
    if (speed_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(9, speed_);
    }
    if (heading_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(10, heading_);
    }
    if (steeringWheelAngle_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(11, steeringWheelAngle_);
    }
    if (motionConfid_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(12, getMotionConfid());
    }
    if (brakes_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(13, getBrakes());
    }
    if (throttle_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(14, getThrottle());
    }
    if (size_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(15, getSize());
    }
    if (vehicleType_ != road.data.proto.VehicleType.UNKNOWN_VEHICLE_CLASS.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(16, vehicleType_);
    }
    if (fuelType_ != road.data.proto.BsmData.VehicleFuelType.VEHICLE_TUEL_UNKNOWNFUEL.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(17, fuelType_);
    }
    if (driveModedriveStatus_ != road.data.proto.BsmData.DriveStatus.AUTOPILOT.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(18, driveModedriveStatus_);
    }
    if (emergencyStatus_ != road.data.proto.BsmData.EmergenyStatus.NO_EMER.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(19, emergencyStatus_);
    }
    if (light_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(20, light_);
    }
    if (wiper_ != road.data.proto.BsmData.Wiper.OFF.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(21, wiper_);
    }
    if (outofControl_ != road.data.proto.BsmData.OutofControl.NO_OUTCON.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(22, outofControl_);
    }
    if (endurance_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(23, endurance_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.BsmData)) {
      return super.equals(obj);
    }
    road.data.proto.BsmData other = (road.data.proto.BsmData) obj;

    if (!getObuId()
        .equals(other.getObuId())) return false;
    if (!getPlateNo()
        .equals(other.getPlateNo())) return false;
    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (hasPos() != other.hasPos()) return false;
    if (hasPos()) {
      if (!getPos()
          .equals(other.getPos())) return false;
    }
    if (hasPosConfid() != other.hasPosConfid()) return false;
    if (hasPosConfid()) {
      if (!getPosConfid()
          .equals(other.getPosConfid())) return false;
    }
    if (hasPosAccuracy() != other.hasPosAccuracy()) return false;
    if (hasPosAccuracy()) {
      if (!getPosAccuracy()
          .equals(other.getPosAccuracy())) return false;
    }
    if (hasAcceleration() != other.hasAcceleration()) return false;
    if (hasAcceleration()) {
      if (!getAcceleration()
          .equals(other.getAcceleration())) return false;
    }
    if (transmission_ != other.transmission_) return false;
    if (getSpeed()
        != other.getSpeed()) return false;
    if (getHeading()
        != other.getHeading()) return false;
    if (getSteeringWheelAngle()
        != other.getSteeringWheelAngle()) return false;
    if (hasMotionConfid() != other.hasMotionConfid()) return false;
    if (hasMotionConfid()) {
      if (!getMotionConfid()
          .equals(other.getMotionConfid())) return false;
    }
    if (hasBrakes() != other.hasBrakes()) return false;
    if (hasBrakes()) {
      if (!getBrakes()
          .equals(other.getBrakes())) return false;
    }
    if (hasThrottle() != other.hasThrottle()) return false;
    if (hasThrottle()) {
      if (!getThrottle()
          .equals(other.getThrottle())) return false;
    }
    if (hasSize() != other.hasSize()) return false;
    if (hasSize()) {
      if (!getSize()
          .equals(other.getSize())) return false;
    }
    if (vehicleType_ != other.vehicleType_) return false;
    if (fuelType_ != other.fuelType_) return false;
    if (driveModedriveStatus_ != other.driveModedriveStatus_) return false;
    if (emergencyStatus_ != other.emergencyStatus_) return false;
    if (getLight()
        != other.getLight()) return false;
    if (wiper_ != other.wiper_) return false;
    if (outofControl_ != other.outofControl_) return false;
    if (getEndurance()
        != other.getEndurance()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + OBUID_FIELD_NUMBER;
    hash = (53 * hash) + getObuId().hashCode();
    hash = (37 * hash) + PLATENO_FIELD_NUMBER;
    hash = (53 * hash) + getPlateNo().hashCode();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    if (hasPos()) {
      hash = (37 * hash) + POS_FIELD_NUMBER;
      hash = (53 * hash) + getPos().hashCode();
    }
    if (hasPosConfid()) {
      hash = (37 * hash) + POSCONFID_FIELD_NUMBER;
      hash = (53 * hash) + getPosConfid().hashCode();
    }
    if (hasPosAccuracy()) {
      hash = (37 * hash) + POSACCURACY_FIELD_NUMBER;
      hash = (53 * hash) + getPosAccuracy().hashCode();
    }
    if (hasAcceleration()) {
      hash = (37 * hash) + ACCELERATION_FIELD_NUMBER;
      hash = (53 * hash) + getAcceleration().hashCode();
    }
    hash = (37 * hash) + TRANSMISSION_FIELD_NUMBER;
    hash = (53 * hash) + transmission_;
    hash = (37 * hash) + SPEED_FIELD_NUMBER;
    hash = (53 * hash) + getSpeed();
    hash = (37 * hash) + HEADING_FIELD_NUMBER;
    hash = (53 * hash) + getHeading();
    hash = (37 * hash) + STEERINGWHEELANGLE_FIELD_NUMBER;
    hash = (53 * hash) + getSteeringWheelAngle();
    if (hasMotionConfid()) {
      hash = (37 * hash) + MOTIONCONFID_FIELD_NUMBER;
      hash = (53 * hash) + getMotionConfid().hashCode();
    }
    if (hasBrakes()) {
      hash = (37 * hash) + BRAKES_FIELD_NUMBER;
      hash = (53 * hash) + getBrakes().hashCode();
    }
    if (hasThrottle()) {
      hash = (37 * hash) + THROTTLE_FIELD_NUMBER;
      hash = (53 * hash) + getThrottle().hashCode();
    }
    if (hasSize()) {
      hash = (37 * hash) + SIZE_FIELD_NUMBER;
      hash = (53 * hash) + getSize().hashCode();
    }
    hash = (37 * hash) + VEHICLETYPE_FIELD_NUMBER;
    hash = (53 * hash) + vehicleType_;
    hash = (37 * hash) + FUELTYPE_FIELD_NUMBER;
    hash = (53 * hash) + fuelType_;
    hash = (37 * hash) + DRIVEMODEDRIVESTATUS_FIELD_NUMBER;
    hash = (53 * hash) + driveModedriveStatus_;
    hash = (37 * hash) + EMERGENCYSTATUS_FIELD_NUMBER;
    hash = (53 * hash) + emergencyStatus_;
    hash = (37 * hash) + LIGHT_FIELD_NUMBER;
    hash = (53 * hash) + getLight();
    hash = (37 * hash) + WIPER_FIELD_NUMBER;
    hash = (53 * hash) + wiper_;
    hash = (37 * hash) + OUTOFCONTROL_FIELD_NUMBER;
    hash = (53 * hash) + outofControl_;
    hash = (37 * hash) + ENDURANCE_FIELD_NUMBER;
    hash = (53 * hash) + getEndurance();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.BsmData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.BsmData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.BsmData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.BsmData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.BsmData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.BsmData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.BsmData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.BsmData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.BsmData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.BsmData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.BsmData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.BsmData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.BsmData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *基本安全信息BSM   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.BsmData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.BsmData)
      road.data.proto.BsmDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_BsmData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_BsmData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.BsmData.class, road.data.proto.BsmData.Builder.class);
    }

    // Construct using road.data.proto.BsmData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      obuId_ = "";

      plateNo_ = "";

      timestamp_ = 0L;

      if (posBuilder_ == null) {
        pos_ = null;
      } else {
        pos_ = null;
        posBuilder_ = null;
      }
      if (posConfidBuilder_ == null) {
        posConfid_ = null;
      } else {
        posConfid_ = null;
        posConfidBuilder_ = null;
      }
      if (posAccuracyBuilder_ == null) {
        posAccuracy_ = null;
      } else {
        posAccuracy_ = null;
        posAccuracyBuilder_ = null;
      }
      if (accelerationBuilder_ == null) {
        acceleration_ = null;
      } else {
        acceleration_ = null;
        accelerationBuilder_ = null;
      }
      transmission_ = 0;

      speed_ = 0;

      heading_ = 0;

      steeringWheelAngle_ = 0;

      if (motionConfidBuilder_ == null) {
        motionConfid_ = null;
      } else {
        motionConfid_ = null;
        motionConfidBuilder_ = null;
      }
      if (brakesBuilder_ == null) {
        brakes_ = null;
      } else {
        brakes_ = null;
        brakesBuilder_ = null;
      }
      if (throttleBuilder_ == null) {
        throttle_ = null;
      } else {
        throttle_ = null;
        throttleBuilder_ = null;
      }
      if (sizeBuilder_ == null) {
        size_ = null;
      } else {
        size_ = null;
        sizeBuilder_ = null;
      }
      vehicleType_ = 0;

      fuelType_ = 0;

      driveModedriveStatus_ = 0;

      emergencyStatus_ = 0;

      light_ = 0;

      wiper_ = 0;

      outofControl_ = 0;

      endurance_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_BsmData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.BsmData getDefaultInstanceForType() {
      return road.data.proto.BsmData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.BsmData build() {
      road.data.proto.BsmData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.BsmData buildPartial() {
      road.data.proto.BsmData result = new road.data.proto.BsmData(this);
      result.obuId_ = obuId_;
      result.plateNo_ = plateNo_;
      result.timestamp_ = timestamp_;
      if (posBuilder_ == null) {
        result.pos_ = pos_;
      } else {
        result.pos_ = posBuilder_.build();
      }
      if (posConfidBuilder_ == null) {
        result.posConfid_ = posConfid_;
      } else {
        result.posConfid_ = posConfidBuilder_.build();
      }
      if (posAccuracyBuilder_ == null) {
        result.posAccuracy_ = posAccuracy_;
      } else {
        result.posAccuracy_ = posAccuracyBuilder_.build();
      }
      if (accelerationBuilder_ == null) {
        result.acceleration_ = acceleration_;
      } else {
        result.acceleration_ = accelerationBuilder_.build();
      }
      result.transmission_ = transmission_;
      result.speed_ = speed_;
      result.heading_ = heading_;
      result.steeringWheelAngle_ = steeringWheelAngle_;
      if (motionConfidBuilder_ == null) {
        result.motionConfid_ = motionConfid_;
      } else {
        result.motionConfid_ = motionConfidBuilder_.build();
      }
      if (brakesBuilder_ == null) {
        result.brakes_ = brakes_;
      } else {
        result.brakes_ = brakesBuilder_.build();
      }
      if (throttleBuilder_ == null) {
        result.throttle_ = throttle_;
      } else {
        result.throttle_ = throttleBuilder_.build();
      }
      if (sizeBuilder_ == null) {
        result.size_ = size_;
      } else {
        result.size_ = sizeBuilder_.build();
      }
      result.vehicleType_ = vehicleType_;
      result.fuelType_ = fuelType_;
      result.driveModedriveStatus_ = driveModedriveStatus_;
      result.emergencyStatus_ = emergencyStatus_;
      result.light_ = light_;
      result.wiper_ = wiper_;
      result.outofControl_ = outofControl_;
      result.endurance_ = endurance_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.BsmData) {
        return mergeFrom((road.data.proto.BsmData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.BsmData other) {
      if (other == road.data.proto.BsmData.getDefaultInstance()) return this;
      if (!other.getObuId().isEmpty()) {
        obuId_ = other.obuId_;
        onChanged();
      }
      if (!other.getPlateNo().isEmpty()) {
        plateNo_ = other.plateNo_;
        onChanged();
      }
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (other.hasPos()) {
        mergePos(other.getPos());
      }
      if (other.hasPosConfid()) {
        mergePosConfid(other.getPosConfid());
      }
      if (other.hasPosAccuracy()) {
        mergePosAccuracy(other.getPosAccuracy());
      }
      if (other.hasAcceleration()) {
        mergeAcceleration(other.getAcceleration());
      }
      if (other.transmission_ != 0) {
        setTransmissionValue(other.getTransmissionValue());
      }
      if (other.getSpeed() != 0) {
        setSpeed(other.getSpeed());
      }
      if (other.getHeading() != 0) {
        setHeading(other.getHeading());
      }
      if (other.getSteeringWheelAngle() != 0) {
        setSteeringWheelAngle(other.getSteeringWheelAngle());
      }
      if (other.hasMotionConfid()) {
        mergeMotionConfid(other.getMotionConfid());
      }
      if (other.hasBrakes()) {
        mergeBrakes(other.getBrakes());
      }
      if (other.hasThrottle()) {
        mergeThrottle(other.getThrottle());
      }
      if (other.hasSize()) {
        mergeSize(other.getSize());
      }
      if (other.vehicleType_ != 0) {
        setVehicleTypeValue(other.getVehicleTypeValue());
      }
      if (other.fuelType_ != 0) {
        setFuelTypeValue(other.getFuelTypeValue());
      }
      if (other.driveModedriveStatus_ != 0) {
        setDriveModedriveStatusValue(other.getDriveModedriveStatusValue());
      }
      if (other.emergencyStatus_ != 0) {
        setEmergencyStatusValue(other.getEmergencyStatusValue());
      }
      if (other.getLight() != 0) {
        setLight(other.getLight());
      }
      if (other.wiper_ != 0) {
        setWiperValue(other.getWiperValue());
      }
      if (other.outofControl_ != 0) {
        setOutofControlValue(other.getOutofControlValue());
      }
      if (other.getEndurance() != 0) {
        setEndurance(other.getEndurance());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.BsmData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.BsmData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object obuId_ = "";
    /**
     * <pre>
     * 车辆 Id（OBU 设备序列号）
     * </pre>
     *
     * <code>string obuId = 1;</code>
     */
    public java.lang.String getObuId() {
      java.lang.Object ref = obuId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        obuId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 车辆 Id（OBU 设备序列号）
     * </pre>
     *
     * <code>string obuId = 1;</code>
     */
    public com.google.protobuf.ByteString
        getObuIdBytes() {
      java.lang.Object ref = obuId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        obuId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 车辆 Id（OBU 设备序列号）
     * </pre>
     *
     * <code>string obuId = 1;</code>
     */
    public Builder setObuId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      obuId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 车辆 Id（OBU 设备序列号）
     * </pre>
     *
     * <code>string obuId = 1;</code>
     */
    public Builder clearObuId() {
      
      obuId_ = getDefaultInstance().getObuId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 车辆 Id（OBU 设备序列号）
     * </pre>
     *
     * <code>string obuId = 1;</code>
     */
    public Builder setObuIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      obuId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object plateNo_ = "";
    /**
     * <pre>
     * 可选，车牌号，字符串，最大为36个字符，支持中文和数字
     * </pre>
     *
     * <code>string plateNo = 2;</code>
     */
    public java.lang.String getPlateNo() {
      java.lang.Object ref = plateNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        plateNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 可选，车牌号，字符串，最大为36个字符，支持中文和数字
     * </pre>
     *
     * <code>string plateNo = 2;</code>
     */
    public com.google.protobuf.ByteString
        getPlateNoBytes() {
      java.lang.Object ref = plateNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        plateNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 可选，车牌号，字符串，最大为36个字符，支持中文和数字
     * </pre>
     *
     * <code>string plateNo = 2;</code>
     */
    public Builder setPlateNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      plateNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，车牌号，字符串，最大为36个字符，支持中文和数字
     * </pre>
     *
     * <code>string plateNo = 2;</code>
     */
    public Builder clearPlateNo() {
      
      plateNo_ = getDefaultInstance().getPlateNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，车牌号，字符串，最大为36个字符，支持中文和数字
     * </pre>
     *
     * <code>string plateNo = 2;</code>
     */
    public Builder setPlateNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      plateNo_ = value;
      onChanged();
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private road.data.proto.Position3D pos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> posBuilder_;
    /**
     * <pre>
     * 经纬度和高程信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public boolean hasPos() {
      return posBuilder_ != null || pos_ != null;
    }
    /**
     * <pre>
     * 经纬度和高程信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public road.data.proto.Position3D getPos() {
      if (posBuilder_ == null) {
        return pos_ == null ? road.data.proto.Position3D.getDefaultInstance() : pos_;
      } else {
        return posBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 经纬度和高程信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public Builder setPos(road.data.proto.Position3D value) {
      if (posBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        pos_ = value;
        onChanged();
      } else {
        posBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 经纬度和高程信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public Builder setPos(
        road.data.proto.Position3D.Builder builderForValue) {
      if (posBuilder_ == null) {
        pos_ = builderForValue.build();
        onChanged();
      } else {
        posBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 经纬度和高程信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public Builder mergePos(road.data.proto.Position3D value) {
      if (posBuilder_ == null) {
        if (pos_ != null) {
          pos_ =
            road.data.proto.Position3D.newBuilder(pos_).mergeFrom(value).buildPartial();
        } else {
          pos_ = value;
        }
        onChanged();
      } else {
        posBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 经纬度和高程信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public Builder clearPos() {
      if (posBuilder_ == null) {
        pos_ = null;
        onChanged();
      } else {
        pos_ = null;
        posBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 经纬度和高程信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public road.data.proto.Position3D.Builder getPosBuilder() {
      
      onChanged();
      return getPosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 经纬度和高程信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public road.data.proto.Position3DOrBuilder getPosOrBuilder() {
      if (posBuilder_ != null) {
        return posBuilder_.getMessageOrBuilder();
      } else {
        return pos_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : pos_;
      }
    }
    /**
     * <pre>
     * 经纬度和高程信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getPosFieldBuilder() {
      if (posBuilder_ == null) {
        posBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getPos(),
                getParentForChildren(),
                isClean());
        pos_ = null;
      }
      return posBuilder_;
    }

    private road.data.proto.PositionConfidenceSet posConfid_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder> posConfidBuilder_;
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
     */
    public boolean hasPosConfid() {
      return posConfidBuilder_ != null || posConfid_ != null;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
     */
    public road.data.proto.PositionConfidenceSet getPosConfid() {
      if (posConfidBuilder_ == null) {
        return posConfid_ == null ? road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
      } else {
        return posConfidBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
     */
    public Builder setPosConfid(road.data.proto.PositionConfidenceSet value) {
      if (posConfidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        posConfid_ = value;
        onChanged();
      } else {
        posConfidBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
     */
    public Builder setPosConfid(
        road.data.proto.PositionConfidenceSet.Builder builderForValue) {
      if (posConfidBuilder_ == null) {
        posConfid_ = builderForValue.build();
        onChanged();
      } else {
        posConfidBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
     */
    public Builder mergePosConfid(road.data.proto.PositionConfidenceSet value) {
      if (posConfidBuilder_ == null) {
        if (posConfid_ != null) {
          posConfid_ =
            road.data.proto.PositionConfidenceSet.newBuilder(posConfid_).mergeFrom(value).buildPartial();
        } else {
          posConfid_ = value;
        }
        onChanged();
      } else {
        posConfidBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
     */
    public Builder clearPosConfid() {
      if (posConfidBuilder_ == null) {
        posConfid_ = null;
        onChanged();
      } else {
        posConfid_ = null;
        posConfidBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
     */
    public road.data.proto.PositionConfidenceSet.Builder getPosConfidBuilder() {
      
      onChanged();
      return getPosConfidFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
     */
    public road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder() {
      if (posConfidBuilder_ != null) {
        return posConfidBuilder_.getMessageOrBuilder();
      } else {
        return posConfid_ == null ?
            road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
      }
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder> 
        getPosConfidFieldBuilder() {
      if (posConfidBuilder_ == null) {
        posConfidBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder>(
                getPosConfid(),
                getParentForChildren(),
                isClean());
        posConfid_ = null;
      }
      return posConfidBuilder_;
    }

    private road.data.proto.PositionAccuracy posAccuracy_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PositionAccuracy, road.data.proto.PositionAccuracy.Builder, road.data.proto.PositionAccuracyOrBuilder> posAccuracyBuilder_;
    /**
     * <pre>
     * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
     */
    public boolean hasPosAccuracy() {
      return posAccuracyBuilder_ != null || posAccuracy_ != null;
    }
    /**
     * <pre>
     * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
     */
    public road.data.proto.PositionAccuracy getPosAccuracy() {
      if (posAccuracyBuilder_ == null) {
        return posAccuracy_ == null ? road.data.proto.PositionAccuracy.getDefaultInstance() : posAccuracy_;
      } else {
        return posAccuracyBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
     */
    public Builder setPosAccuracy(road.data.proto.PositionAccuracy value) {
      if (posAccuracyBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        posAccuracy_ = value;
        onChanged();
      } else {
        posAccuracyBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
     */
    public Builder setPosAccuracy(
        road.data.proto.PositionAccuracy.Builder builderForValue) {
      if (posAccuracyBuilder_ == null) {
        posAccuracy_ = builderForValue.build();
        onChanged();
      } else {
        posAccuracyBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
     */
    public Builder mergePosAccuracy(road.data.proto.PositionAccuracy value) {
      if (posAccuracyBuilder_ == null) {
        if (posAccuracy_ != null) {
          posAccuracy_ =
            road.data.proto.PositionAccuracy.newBuilder(posAccuracy_).mergeFrom(value).buildPartial();
        } else {
          posAccuracy_ = value;
        }
        onChanged();
      } else {
        posAccuracyBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
     */
    public Builder clearPosAccuracy() {
      if (posAccuracyBuilder_ == null) {
        posAccuracy_ = null;
        onChanged();
      } else {
        posAccuracy_ = null;
        posAccuracyBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
     */
    public road.data.proto.PositionAccuracy.Builder getPosAccuracyBuilder() {
      
      onChanged();
      return getPosAccuracyFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
     */
    public road.data.proto.PositionAccuracyOrBuilder getPosAccuracyOrBuilder() {
      if (posAccuracyBuilder_ != null) {
        return posAccuracyBuilder_.getMessageOrBuilder();
      } else {
        return posAccuracy_ == null ?
            road.data.proto.PositionAccuracy.getDefaultInstance() : posAccuracy_;
      }
    }
    /**
     * <pre>
     * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PositionAccuracy, road.data.proto.PositionAccuracy.Builder, road.data.proto.PositionAccuracyOrBuilder> 
        getPosAccuracyFieldBuilder() {
      if (posAccuracyBuilder_ == null) {
        posAccuracyBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.PositionAccuracy, road.data.proto.PositionAccuracy.Builder, road.data.proto.PositionAccuracyOrBuilder>(
                getPosAccuracy(),
                getParentForChildren(),
                isClean());
        posAccuracy_ = null;
      }
      return posAccuracyBuilder_;
    }

    private road.data.proto.AccelerationSet4Way acceleration_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AccelerationSet4Way, road.data.proto.AccelerationSet4Way.Builder, road.data.proto.AccelerationSet4WayOrBuilder> accelerationBuilder_;
    /**
     * <pre>
     * 可选，加速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public boolean hasAcceleration() {
      return accelerationBuilder_ != null || acceleration_ != null;
    }
    /**
     * <pre>
     * 可选，加速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public road.data.proto.AccelerationSet4Way getAcceleration() {
      if (accelerationBuilder_ == null) {
        return acceleration_ == null ? road.data.proto.AccelerationSet4Way.getDefaultInstance() : acceleration_;
      } else {
        return accelerationBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，加速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public Builder setAcceleration(road.data.proto.AccelerationSet4Way value) {
      if (accelerationBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        acceleration_ = value;
        onChanged();
      } else {
        accelerationBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，加速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public Builder setAcceleration(
        road.data.proto.AccelerationSet4Way.Builder builderForValue) {
      if (accelerationBuilder_ == null) {
        acceleration_ = builderForValue.build();
        onChanged();
      } else {
        accelerationBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，加速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public Builder mergeAcceleration(road.data.proto.AccelerationSet4Way value) {
      if (accelerationBuilder_ == null) {
        if (acceleration_ != null) {
          acceleration_ =
            road.data.proto.AccelerationSet4Way.newBuilder(acceleration_).mergeFrom(value).buildPartial();
        } else {
          acceleration_ = value;
        }
        onChanged();
      } else {
        accelerationBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，加速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public Builder clearAcceleration() {
      if (accelerationBuilder_ == null) {
        acceleration_ = null;
        onChanged();
      } else {
        acceleration_ = null;
        accelerationBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，加速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public road.data.proto.AccelerationSet4Way.Builder getAccelerationBuilder() {
      
      onChanged();
      return getAccelerationFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，加速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public road.data.proto.AccelerationSet4WayOrBuilder getAccelerationOrBuilder() {
      if (accelerationBuilder_ != null) {
        return accelerationBuilder_.getMessageOrBuilder();
      } else {
        return acceleration_ == null ?
            road.data.proto.AccelerationSet4Way.getDefaultInstance() : acceleration_;
      }
    }
    /**
     * <pre>
     * 可选，加速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AccelerationSet4Way, road.data.proto.AccelerationSet4Way.Builder, road.data.proto.AccelerationSet4WayOrBuilder> 
        getAccelerationFieldBuilder() {
      if (accelerationBuilder_ == null) {
        accelerationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.AccelerationSet4Way, road.data.proto.AccelerationSet4Way.Builder, road.data.proto.AccelerationSet4WayOrBuilder>(
                getAcceleration(),
                getParentForChildren(),
                isClean());
        acceleration_ = null;
      }
      return accelerationBuilder_;
    }

    private int transmission_ = 0;
    /**
     * <pre>
     *可选，车辆档位
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.TransmissionState transmission = 8;</code>
     */
    public int getTransmissionValue() {
      return transmission_;
    }
    /**
     * <pre>
     *可选，车辆档位
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.TransmissionState transmission = 8;</code>
     */
    public Builder setTransmissionValue(int value) {
      transmission_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车辆档位
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.TransmissionState transmission = 8;</code>
     */
    public road.data.proto.BsmData.TransmissionState getTransmission() {
      @SuppressWarnings("deprecation")
      road.data.proto.BsmData.TransmissionState result = road.data.proto.BsmData.TransmissionState.valueOf(transmission_);
      return result == null ? road.data.proto.BsmData.TransmissionState.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，车辆档位
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.TransmissionState transmission = 8;</code>
     */
    public Builder setTransmission(road.data.proto.BsmData.TransmissionState value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      transmission_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车辆档位
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.TransmissionState transmission = 8;</code>
     */
    public Builder clearTransmission() {
      
      transmission_ = 0;
      onChanged();
      return this;
    }

    private int speed_ ;
    /**
     * <pre>
     * 定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 speed = 9;</code>
     */
    public int getSpeed() {
      return speed_;
    }
    /**
     * <pre>
     * 定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 speed = 9;</code>
     */
    public Builder setSpeed(int value) {
      
      speed_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 speed = 9;</code>
     */
    public Builder clearSpeed() {
      
      speed_ = 0;
      onChanged();
      return this;
    }

    private int heading_ ;
    /**
     * <pre>
     * 航向角，为车头方向与正北方向的顺时针夹角。分辨率为0.0125°
     * </pre>
     *
     * <code>uint32 heading = 10;</code>
     */
    public int getHeading() {
      return heading_;
    }
    /**
     * <pre>
     * 航向角，为车头方向与正北方向的顺时针夹角。分辨率为0.0125°
     * </pre>
     *
     * <code>uint32 heading = 10;</code>
     */
    public Builder setHeading(int value) {
      
      heading_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 航向角，为车头方向与正北方向的顺时针夹角。分辨率为0.0125°
     * </pre>
     *
     * <code>uint32 heading = 10;</code>
     */
    public Builder clearHeading() {
      
      heading_ = 0;
      onChanged();
      return this;
    }

    private int steeringWheelAngle_ ;
    /**
     * <pre>
     * 可选，[_240, 240]	方向盘角度，分辨率为1.5°
     * </pre>
     *
     * <code>int32 steeringWheelAngle = 11;</code>
     */
    public int getSteeringWheelAngle() {
      return steeringWheelAngle_;
    }
    /**
     * <pre>
     * 可选，[_240, 240]	方向盘角度，分辨率为1.5°
     * </pre>
     *
     * <code>int32 steeringWheelAngle = 11;</code>
     */
    public Builder setSteeringWheelAngle(int value) {
      
      steeringWheelAngle_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，[_240, 240]	方向盘角度，分辨率为1.5°
     * </pre>
     *
     * <code>int32 steeringWheelAngle = 11;</code>
     */
    public Builder clearSteeringWheelAngle() {
      
      steeringWheelAngle_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.MotionConfidenceSet motionConfid_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MotionConfidenceSet, road.data.proto.MotionConfidenceSet.Builder, road.data.proto.MotionConfidenceSetOrBuilder> motionConfidBuilder_;
    /**
     * <pre>
     *可选，车辆运动状态精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public boolean hasMotionConfid() {
      return motionConfidBuilder_ != null || motionConfid_ != null;
    }
    /**
     * <pre>
     *可选，车辆运动状态精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public road.data.proto.MotionConfidenceSet getMotionConfid() {
      if (motionConfidBuilder_ == null) {
        return motionConfid_ == null ? road.data.proto.MotionConfidenceSet.getDefaultInstance() : motionConfid_;
      } else {
        return motionConfidBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，车辆运动状态精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public Builder setMotionConfid(road.data.proto.MotionConfidenceSet value) {
      if (motionConfidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        motionConfid_ = value;
        onChanged();
      } else {
        motionConfidBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，车辆运动状态精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public Builder setMotionConfid(
        road.data.proto.MotionConfidenceSet.Builder builderForValue) {
      if (motionConfidBuilder_ == null) {
        motionConfid_ = builderForValue.build();
        onChanged();
      } else {
        motionConfidBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，车辆运动状态精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public Builder mergeMotionConfid(road.data.proto.MotionConfidenceSet value) {
      if (motionConfidBuilder_ == null) {
        if (motionConfid_ != null) {
          motionConfid_ =
            road.data.proto.MotionConfidenceSet.newBuilder(motionConfid_).mergeFrom(value).buildPartial();
        } else {
          motionConfid_ = value;
        }
        onChanged();
      } else {
        motionConfidBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，车辆运动状态精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public Builder clearMotionConfid() {
      if (motionConfidBuilder_ == null) {
        motionConfid_ = null;
        onChanged();
      } else {
        motionConfid_ = null;
        motionConfidBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，车辆运动状态精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public road.data.proto.MotionConfidenceSet.Builder getMotionConfidBuilder() {
      
      onChanged();
      return getMotionConfidFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，车辆运动状态精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public road.data.proto.MotionConfidenceSetOrBuilder getMotionConfidOrBuilder() {
      if (motionConfidBuilder_ != null) {
        return motionConfidBuilder_.getMessageOrBuilder();
      } else {
        return motionConfid_ == null ?
            road.data.proto.MotionConfidenceSet.getDefaultInstance() : motionConfid_;
      }
    }
    /**
     * <pre>
     *可选，车辆运动状态精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MotionConfidenceSet, road.data.proto.MotionConfidenceSet.Builder, road.data.proto.MotionConfidenceSetOrBuilder> 
        getMotionConfidFieldBuilder() {
      if (motionConfidBuilder_ == null) {
        motionConfidBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.MotionConfidenceSet, road.data.proto.MotionConfidenceSet.Builder, road.data.proto.MotionConfidenceSetOrBuilder>(
                getMotionConfid(),
                getParentForChildren(),
                isClean());
        motionConfid_ = null;
      }
      return motionConfidBuilder_;
    }

    private road.data.proto.BrakeSystemStatus brakes_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.BrakeSystemStatus, road.data.proto.BrakeSystemStatus.Builder, road.data.proto.BrakeSystemStatusOrBuilder> brakesBuilder_;
    /**
     * <pre>
     * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
     */
    public boolean hasBrakes() {
      return brakesBuilder_ != null || brakes_ != null;
    }
    /**
     * <pre>
     * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
     */
    public road.data.proto.BrakeSystemStatus getBrakes() {
      if (brakesBuilder_ == null) {
        return brakes_ == null ? road.data.proto.BrakeSystemStatus.getDefaultInstance() : brakes_;
      } else {
        return brakesBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
     */
    public Builder setBrakes(road.data.proto.BrakeSystemStatus value) {
      if (brakesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        brakes_ = value;
        onChanged();
      } else {
        brakesBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
     */
    public Builder setBrakes(
        road.data.proto.BrakeSystemStatus.Builder builderForValue) {
      if (brakesBuilder_ == null) {
        brakes_ = builderForValue.build();
        onChanged();
      } else {
        brakesBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
     */
    public Builder mergeBrakes(road.data.proto.BrakeSystemStatus value) {
      if (brakesBuilder_ == null) {
        if (brakes_ != null) {
          brakes_ =
            road.data.proto.BrakeSystemStatus.newBuilder(brakes_).mergeFrom(value).buildPartial();
        } else {
          brakes_ = value;
        }
        onChanged();
      } else {
        brakesBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
     */
    public Builder clearBrakes() {
      if (brakesBuilder_ == null) {
        brakes_ = null;
        onChanged();
      } else {
        brakes_ = null;
        brakesBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
     */
    public road.data.proto.BrakeSystemStatus.Builder getBrakesBuilder() {
      
      onChanged();
      return getBrakesFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
     */
    public road.data.proto.BrakeSystemStatusOrBuilder getBrakesOrBuilder() {
      if (brakesBuilder_ != null) {
        return brakesBuilder_.getMessageOrBuilder();
      } else {
        return brakes_ == null ?
            road.data.proto.BrakeSystemStatus.getDefaultInstance() : brakes_;
      }
    }
    /**
     * <pre>
     * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.BrakeSystemStatus, road.data.proto.BrakeSystemStatus.Builder, road.data.proto.BrakeSystemStatusOrBuilder> 
        getBrakesFieldBuilder() {
      if (brakesBuilder_ == null) {
        brakesBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.BrakeSystemStatus, road.data.proto.BrakeSystemStatus.Builder, road.data.proto.BrakeSystemStatusOrBuilder>(
                getBrakes(),
                getParentForChildren(),
                isClean());
        brakes_ = null;
      }
      return brakesBuilder_;
    }

    private road.data.proto.ThrottleSystemStatus throttle_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ThrottleSystemStatus, road.data.proto.ThrottleSystemStatus.Builder, road.data.proto.ThrottleSystemStatusOrBuilder> throttleBuilder_;
    /**
     * <pre>
     *可选，定义车辆的给油系统状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
     */
    public boolean hasThrottle() {
      return throttleBuilder_ != null || throttle_ != null;
    }
    /**
     * <pre>
     *可选，定义车辆的给油系统状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
     */
    public road.data.proto.ThrottleSystemStatus getThrottle() {
      if (throttleBuilder_ == null) {
        return throttle_ == null ? road.data.proto.ThrottleSystemStatus.getDefaultInstance() : throttle_;
      } else {
        return throttleBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，定义车辆的给油系统状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
     */
    public Builder setThrottle(road.data.proto.ThrottleSystemStatus value) {
      if (throttleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        throttle_ = value;
        onChanged();
      } else {
        throttleBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，定义车辆的给油系统状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
     */
    public Builder setThrottle(
        road.data.proto.ThrottleSystemStatus.Builder builderForValue) {
      if (throttleBuilder_ == null) {
        throttle_ = builderForValue.build();
        onChanged();
      } else {
        throttleBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，定义车辆的给油系统状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
     */
    public Builder mergeThrottle(road.data.proto.ThrottleSystemStatus value) {
      if (throttleBuilder_ == null) {
        if (throttle_ != null) {
          throttle_ =
            road.data.proto.ThrottleSystemStatus.newBuilder(throttle_).mergeFrom(value).buildPartial();
        } else {
          throttle_ = value;
        }
        onChanged();
      } else {
        throttleBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，定义车辆的给油系统状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
     */
    public Builder clearThrottle() {
      if (throttleBuilder_ == null) {
        throttle_ = null;
        onChanged();
      } else {
        throttle_ = null;
        throttleBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，定义车辆的给油系统状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
     */
    public road.data.proto.ThrottleSystemStatus.Builder getThrottleBuilder() {
      
      onChanged();
      return getThrottleFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，定义车辆的给油系统状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
     */
    public road.data.proto.ThrottleSystemStatusOrBuilder getThrottleOrBuilder() {
      if (throttleBuilder_ != null) {
        return throttleBuilder_.getMessageOrBuilder();
      } else {
        return throttle_ == null ?
            road.data.proto.ThrottleSystemStatus.getDefaultInstance() : throttle_;
      }
    }
    /**
     * <pre>
     *可选，定义车辆的给油系统状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ThrottleSystemStatus, road.data.proto.ThrottleSystemStatus.Builder, road.data.proto.ThrottleSystemStatusOrBuilder> 
        getThrottleFieldBuilder() {
      if (throttleBuilder_ == null) {
        throttleBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ThrottleSystemStatus, road.data.proto.ThrottleSystemStatus.Builder, road.data.proto.ThrottleSystemStatusOrBuilder>(
                getThrottle(),
                getParentForChildren(),
                isClean());
        throttle_ = null;
      }
      return throttleBuilder_;
    }

    private road.data.proto.VehicleSize size_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.VehicleSize, road.data.proto.VehicleSize.Builder, road.data.proto.VehicleSizeOrBuilder> sizeBuilder_;
    /**
     * <pre>
     * 车辆尺寸
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
     */
    public boolean hasSize() {
      return sizeBuilder_ != null || size_ != null;
    }
    /**
     * <pre>
     * 车辆尺寸
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
     */
    public road.data.proto.VehicleSize getSize() {
      if (sizeBuilder_ == null) {
        return size_ == null ? road.data.proto.VehicleSize.getDefaultInstance() : size_;
      } else {
        return sizeBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 车辆尺寸
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
     */
    public Builder setSize(road.data.proto.VehicleSize value) {
      if (sizeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        size_ = value;
        onChanged();
      } else {
        sizeBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 车辆尺寸
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
     */
    public Builder setSize(
        road.data.proto.VehicleSize.Builder builderForValue) {
      if (sizeBuilder_ == null) {
        size_ = builderForValue.build();
        onChanged();
      } else {
        sizeBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 车辆尺寸
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
     */
    public Builder mergeSize(road.data.proto.VehicleSize value) {
      if (sizeBuilder_ == null) {
        if (size_ != null) {
          size_ =
            road.data.proto.VehicleSize.newBuilder(size_).mergeFrom(value).buildPartial();
        } else {
          size_ = value;
        }
        onChanged();
      } else {
        sizeBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 车辆尺寸
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
     */
    public Builder clearSize() {
      if (sizeBuilder_ == null) {
        size_ = null;
        onChanged();
      } else {
        size_ = null;
        sizeBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 车辆尺寸
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
     */
    public road.data.proto.VehicleSize.Builder getSizeBuilder() {
      
      onChanged();
      return getSizeFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 车辆尺寸
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
     */
    public road.data.proto.VehicleSizeOrBuilder getSizeOrBuilder() {
      if (sizeBuilder_ != null) {
        return sizeBuilder_.getMessageOrBuilder();
      } else {
        return size_ == null ?
            road.data.proto.VehicleSize.getDefaultInstance() : size_;
      }
    }
    /**
     * <pre>
     * 车辆尺寸
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.VehicleSize, road.data.proto.VehicleSize.Builder, road.data.proto.VehicleSizeOrBuilder> 
        getSizeFieldBuilder() {
      if (sizeBuilder_ == null) {
        sizeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.VehicleSize, road.data.proto.VehicleSize.Builder, road.data.proto.VehicleSizeOrBuilder>(
                getSize(),
                getParentForChildren(),
                isClean());
        size_ = null;
      }
      return sizeBuilder_;
    }

    private int vehicleType_ = 0;
    /**
     * <pre>
     * 车辆基本类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 16;</code>
     */
    public int getVehicleTypeValue() {
      return vehicleType_;
    }
    /**
     * <pre>
     * 车辆基本类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 16;</code>
     */
    public Builder setVehicleTypeValue(int value) {
      vehicleType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 车辆基本类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 16;</code>
     */
    public road.data.proto.VehicleType getVehicleType() {
      @SuppressWarnings("deprecation")
      road.data.proto.VehicleType result = road.data.proto.VehicleType.valueOf(vehicleType_);
      return result == null ? road.data.proto.VehicleType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 车辆基本类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 16;</code>
     */
    public Builder setVehicleType(road.data.proto.VehicleType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      vehicleType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 车辆基本类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 16;</code>
     */
    public Builder clearVehicleType() {
      
      vehicleType_ = 0;
      onChanged();
      return this;
    }

    private int fuelType_ = 0;
    /**
     * <pre>
     * 可选，车辆燃油类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.VehicleFuelType fuelType = 17;</code>
     */
    public int getFuelTypeValue() {
      return fuelType_;
    }
    /**
     * <pre>
     * 可选，车辆燃油类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.VehicleFuelType fuelType = 17;</code>
     */
    public Builder setFuelTypeValue(int value) {
      fuelType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，车辆燃油类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.VehicleFuelType fuelType = 17;</code>
     */
    public road.data.proto.BsmData.VehicleFuelType getFuelType() {
      @SuppressWarnings("deprecation")
      road.data.proto.BsmData.VehicleFuelType result = road.data.proto.BsmData.VehicleFuelType.valueOf(fuelType_);
      return result == null ? road.data.proto.BsmData.VehicleFuelType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，车辆燃油类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.VehicleFuelType fuelType = 17;</code>
     */
    public Builder setFuelType(road.data.proto.BsmData.VehicleFuelType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      fuelType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，车辆燃油类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.VehicleFuelType fuelType = 17;</code>
     */
    public Builder clearFuelType() {
      
      fuelType_ = 0;
      onChanged();
      return this;
    }

    private int driveModedriveStatus_ = 0;
    /**
     * <pre>
     * 可选，驾驶状态 1：自动驾驶模式 2：人工驾驶模式 3：人工接管模式
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.DriveStatus driveModedriveStatus = 18;</code>
     */
    public int getDriveModedriveStatusValue() {
      return driveModedriveStatus_;
    }
    /**
     * <pre>
     * 可选，驾驶状态 1：自动驾驶模式 2：人工驾驶模式 3：人工接管模式
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.DriveStatus driveModedriveStatus = 18;</code>
     */
    public Builder setDriveModedriveStatusValue(int value) {
      driveModedriveStatus_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，驾驶状态 1：自动驾驶模式 2：人工驾驶模式 3：人工接管模式
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.DriveStatus driveModedriveStatus = 18;</code>
     */
    public road.data.proto.BsmData.DriveStatus getDriveModedriveStatus() {
      @SuppressWarnings("deprecation")
      road.data.proto.BsmData.DriveStatus result = road.data.proto.BsmData.DriveStatus.valueOf(driveModedriveStatus_);
      return result == null ? road.data.proto.BsmData.DriveStatus.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，驾驶状态 1：自动驾驶模式 2：人工驾驶模式 3：人工接管模式
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.DriveStatus driveModedriveStatus = 18;</code>
     */
    public Builder setDriveModedriveStatus(road.data.proto.BsmData.DriveStatus value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      driveModedriveStatus_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，驾驶状态 1：自动驾驶模式 2：人工驾驶模式 3：人工接管模式
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.DriveStatus driveModedriveStatus = 18;</code>
     */
    public Builder clearDriveModedriveStatus() {
      
      driveModedriveStatus_ = 0;
      onChanged();
      return this;
    }

    private int emergencyStatus_ = 0;
    /**
     * <pre>
     * 可选，危险报警闪光灯（双闪） 0否 1是
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.EmergenyStatus emergencyStatus = 19;</code>
     */
    public int getEmergencyStatusValue() {
      return emergencyStatus_;
    }
    /**
     * <pre>
     * 可选，危险报警闪光灯（双闪） 0否 1是
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.EmergenyStatus emergencyStatus = 19;</code>
     */
    public Builder setEmergencyStatusValue(int value) {
      emergencyStatus_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，危险报警闪光灯（双闪） 0否 1是
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.EmergenyStatus emergencyStatus = 19;</code>
     */
    public road.data.proto.BsmData.EmergenyStatus getEmergencyStatus() {
      @SuppressWarnings("deprecation")
      road.data.proto.BsmData.EmergenyStatus result = road.data.proto.BsmData.EmergenyStatus.valueOf(emergencyStatus_);
      return result == null ? road.data.proto.BsmData.EmergenyStatus.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，危险报警闪光灯（双闪） 0否 1是
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.EmergenyStatus emergencyStatus = 19;</code>
     */
    public Builder setEmergencyStatus(road.data.proto.BsmData.EmergenyStatus value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      emergencyStatus_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，危险报警闪光灯（双闪） 0否 1是
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.EmergenyStatus emergencyStatus = 19;</code>
     */
    public Builder clearEmergencyStatus() {
      
      emergencyStatus_ = 0;
      onChanged();
      return this;
    }

    private int light_ ;
    /**
     * <pre>
     *可选，灯光状态，转化为二进制后，二进制左起第x位数字为1对应的含义：
     * </pre>
     *
     * <code>uint32 light = 20;</code>
     */
    public int getLight() {
      return light_;
    }
    /**
     * <pre>
     *可选，灯光状态，转化为二进制后，二进制左起第x位数字为1对应的含义：
     * </pre>
     *
     * <code>uint32 light = 20;</code>
     */
    public Builder setLight(int value) {
      
      light_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，灯光状态，转化为二进制后，二进制左起第x位数字为1对应的含义：
     * </pre>
     *
     * <code>uint32 light = 20;</code>
     */
    public Builder clearLight() {
      
      light_ = 0;
      onChanged();
      return this;
    }

    private int wiper_ = 0;
    /**
     * <pre>
     *可选，雨刷
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.Wiper wiper = 21;</code>
     */
    public int getWiperValue() {
      return wiper_;
    }
    /**
     * <pre>
     *可选，雨刷
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.Wiper wiper = 21;</code>
     */
    public Builder setWiperValue(int value) {
      wiper_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，雨刷
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.Wiper wiper = 21;</code>
     */
    public road.data.proto.BsmData.Wiper getWiper() {
      @SuppressWarnings("deprecation")
      road.data.proto.BsmData.Wiper result = road.data.proto.BsmData.Wiper.valueOf(wiper_);
      return result == null ? road.data.proto.BsmData.Wiper.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，雨刷
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.Wiper wiper = 21;</code>
     */
    public Builder setWiper(road.data.proto.BsmData.Wiper value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      wiper_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，雨刷
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.Wiper wiper = 21;</code>
     */
    public Builder clearWiper() {
      
      wiper_ = 0;
      onChanged();
      return this;
    }

    private int outofControl_ = 0;
    /**
     * <pre>
     * 可选，车辆失控 0 否 1 是
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.OutofControl outofControl = 22;</code>
     */
    public int getOutofControlValue() {
      return outofControl_;
    }
    /**
     * <pre>
     * 可选，车辆失控 0 否 1 是
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.OutofControl outofControl = 22;</code>
     */
    public Builder setOutofControlValue(int value) {
      outofControl_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，车辆失控 0 否 1 是
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.OutofControl outofControl = 22;</code>
     */
    public road.data.proto.BsmData.OutofControl getOutofControl() {
      @SuppressWarnings("deprecation")
      road.data.proto.BsmData.OutofControl result = road.data.proto.BsmData.OutofControl.valueOf(outofControl_);
      return result == null ? road.data.proto.BsmData.OutofControl.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，车辆失控 0 否 1 是
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.OutofControl outofControl = 22;</code>
     */
    public Builder setOutofControl(road.data.proto.BsmData.OutofControl value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      outofControl_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，车辆失控 0 否 1 是
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.BsmData.OutofControl outofControl = 22;</code>
     */
    public Builder clearOutofControl() {
      
      outofControl_ = 0;
      onChanged();
      return this;
    }

    private int endurance_ ;
    /**
     * <pre>
     * 可选，续航里程 单位0.01 km
     * </pre>
     *
     * <code>uint32 endurance = 23;</code>
     */
    public int getEndurance() {
      return endurance_;
    }
    /**
     * <pre>
     * 可选，续航里程 单位0.01 km
     * </pre>
     *
     * <code>uint32 endurance = 23;</code>
     */
    public Builder setEndurance(int value) {
      
      endurance_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，续航里程 单位0.01 km
     * </pre>
     *
     * <code>uint32 endurance = 23;</code>
     */
    public Builder clearEndurance() {
      
      endurance_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.BsmData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.BsmData)
  private static final road.data.proto.BsmData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.BsmData();
  }

  public static road.data.proto.BsmData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BsmData>
      PARSER = new com.google.protobuf.AbstractParser<BsmData>() {
    @java.lang.Override
    public BsmData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new BsmData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<BsmData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BsmData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.BsmData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

