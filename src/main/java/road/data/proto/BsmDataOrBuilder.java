// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface BsmDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.BsmData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 车辆 Id（OBU 设备序列号）
   * </pre>
   *
   * <code>string obuId = 1;</code>
   */
  java.lang.String getObuId();
  /**
   * <pre>
   * 车辆 Id（OBU 设备序列号）
   * </pre>
   *
   * <code>string obuId = 1;</code>
   */
  com.google.protobuf.ByteString
      getObuIdBytes();

  /**
   * <pre>
   * 可选，车牌号，字符串，最大为36个字符，支持中文和数字
   * </pre>
   *
   * <code>string plateNo = 2;</code>
   */
  java.lang.String getPlateNo();
  /**
   * <pre>
   * 可选，车牌号，字符串，最大为36个字符，支持中文和数字
   * </pre>
   *
   * <code>string plateNo = 2;</code>
   */
  com.google.protobuf.ByteString
      getPlateNoBytes();

  /**
   * <pre>
   * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 3;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   * 经纬度和高程信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  boolean hasPos();
  /**
   * <pre>
   * 经纬度和高程信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  road.data.proto.Position3D getPos();
  /**
   * <pre>
   * 经纬度和高程信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  road.data.proto.Position3DOrBuilder getPosOrBuilder();

  /**
   * <pre>
   * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
   */
  boolean hasPosConfid();
  /**
   * <pre>
   * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
   */
  road.data.proto.PositionConfidenceSet getPosConfid();
  /**
   * <pre>
   * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 5;</code>
   */
  road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder();

  /**
   * <pre>
   * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
   */
  boolean hasPosAccuracy();
  /**
   * <pre>
   * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
   */
  road.data.proto.PositionAccuracy getPosAccuracy();
  /**
   * <pre>
   * 可选，定位精度，定义用椭圆模型表示的GNSS系统精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionAccuracy posAccuracy = 6;</code>
   */
  road.data.proto.PositionAccuracyOrBuilder getPosAccuracyOrBuilder();

  /**
   * <pre>
   * 可选，加速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
   */
  boolean hasAcceleration();
  /**
   * <pre>
   * 可选，加速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
   */
  road.data.proto.AccelerationSet4Way getAcceleration();
  /**
   * <pre>
   * 可选，加速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
   */
  road.data.proto.AccelerationSet4WayOrBuilder getAccelerationOrBuilder();

  /**
   * <pre>
   *可选，车辆档位
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.TransmissionState transmission = 8;</code>
   */
  int getTransmissionValue();
  /**
   * <pre>
   *可选，车辆档位
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.TransmissionState transmission = 8;</code>
   */
  road.data.proto.BsmData.TransmissionState getTransmission();

  /**
   * <pre>
   * 定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
   * </pre>
   *
   * <code>uint32 speed = 9;</code>
   */
  int getSpeed();

  /**
   * <pre>
   * 航向角，为车头方向与正北方向的顺时针夹角。分辨率为0.0125°
   * </pre>
   *
   * <code>uint32 heading = 10;</code>
   */
  int getHeading();

  /**
   * <pre>
   * 可选，[_240, 240]	方向盘角度，分辨率为1.5°
   * </pre>
   *
   * <code>int32 steeringWheelAngle = 11;</code>
   */
  int getSteeringWheelAngle();

  /**
   * <pre>
   *可选，车辆运动状态精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  boolean hasMotionConfid();
  /**
   * <pre>
   *可选，车辆运动状态精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  road.data.proto.MotionConfidenceSet getMotionConfid();
  /**
   * <pre>
   *可选，车辆运动状态精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  road.data.proto.MotionConfidenceSetOrBuilder getMotionConfidOrBuilder();

  /**
   * <pre>
   * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
   */
  boolean hasBrakes();
  /**
   * <pre>
   * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
   */
  road.data.proto.BrakeSystemStatus getBrakes();
  /**
   * <pre>
   * 可选，定义车辆的刹车系统状态，包括了7种不同类型的状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BrakeSystemStatus brakes = 13;</code>
   */
  road.data.proto.BrakeSystemStatusOrBuilder getBrakesOrBuilder();

  /**
   * <pre>
   *可选，定义车辆的给油系统状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
   */
  boolean hasThrottle();
  /**
   * <pre>
   *可选，定义车辆的给油系统状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
   */
  road.data.proto.ThrottleSystemStatus getThrottle();
  /**
   * <pre>
   *可选，定义车辆的给油系统状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus throttle = 14;</code>
   */
  road.data.proto.ThrottleSystemStatusOrBuilder getThrottleOrBuilder();

  /**
   * <pre>
   * 车辆尺寸
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
   */
  boolean hasSize();
  /**
   * <pre>
   * 车辆尺寸
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
   */
  road.data.proto.VehicleSize getSize();
  /**
   * <pre>
   * 车辆尺寸
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleSize size = 15;</code>
   */
  road.data.proto.VehicleSizeOrBuilder getSizeOrBuilder();

  /**
   * <pre>
   * 车辆基本类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 16;</code>
   */
  int getVehicleTypeValue();
  /**
   * <pre>
   * 车辆基本类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 16;</code>
   */
  road.data.proto.VehicleType getVehicleType();

  /**
   * <pre>
   * 可选，车辆燃油类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.VehicleFuelType fuelType = 17;</code>
   */
  int getFuelTypeValue();
  /**
   * <pre>
   * 可选，车辆燃油类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.VehicleFuelType fuelType = 17;</code>
   */
  road.data.proto.BsmData.VehicleFuelType getFuelType();

  /**
   * <pre>
   * 可选，驾驶状态 1：自动驾驶模式 2：人工驾驶模式 3：人工接管模式
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.DriveStatus driveModedriveStatus = 18;</code>
   */
  int getDriveModedriveStatusValue();
  /**
   * <pre>
   * 可选，驾驶状态 1：自动驾驶模式 2：人工驾驶模式 3：人工接管模式
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.DriveStatus driveModedriveStatus = 18;</code>
   */
  road.data.proto.BsmData.DriveStatus getDriveModedriveStatus();

  /**
   * <pre>
   * 可选，危险报警闪光灯（双闪） 0否 1是
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.EmergenyStatus emergencyStatus = 19;</code>
   */
  int getEmergencyStatusValue();
  /**
   * <pre>
   * 可选，危险报警闪光灯（双闪） 0否 1是
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.EmergenyStatus emergencyStatus = 19;</code>
   */
  road.data.proto.BsmData.EmergenyStatus getEmergencyStatus();

  /**
   * <pre>
   *可选，灯光状态，转化为二进制后，二进制左起第x位数字为1对应的含义：
   * </pre>
   *
   * <code>uint32 light = 20;</code>
   */
  int getLight();

  /**
   * <pre>
   *可选，雨刷
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.Wiper wiper = 21;</code>
   */
  int getWiperValue();
  /**
   * <pre>
   *可选，雨刷
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.Wiper wiper = 21;</code>
   */
  road.data.proto.BsmData.Wiper getWiper();

  /**
   * <pre>
   * 可选，车辆失控 0 否 1 是
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.OutofControl outofControl = 22;</code>
   */
  int getOutofControlValue();
  /**
   * <pre>
   * 可选，车辆失控 0 否 1 是
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.BsmData.OutofControl outofControl = 22;</code>
   */
  road.data.proto.BsmData.OutofControl getOutofControl();

  /**
   * <pre>
   * 可选，续航里程 单位0.01 km
   * </pre>
   *
   * <code>uint32 endurance = 23;</code>
   */
  int getEndurance();
}
