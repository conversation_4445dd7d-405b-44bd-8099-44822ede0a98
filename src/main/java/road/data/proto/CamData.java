// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *合作感知信息   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.CamData}
 */
public  final class CamData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.CamData)
    CamDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use CamData.newBuilder() to construct.
  private CamData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private CamData() {
    ver_ = "";
    deviceId_ = "";
    mapDeviceId_ = "";
    sceneType_ = 0;
    ptcList_ = java.util.Collections.emptyList();
    obstacleList_ = java.util.Collections.emptyList();
    rteList_ = java.util.Collections.emptyList();
    rtsList_ = java.util.Collections.emptyList();
    bsmList_ = java.util.Collections.emptyList();
    virList_ = java.util.Collections.emptyList();
    rscList_ = java.util.Collections.emptyList();
    trafficFlow_ = java.util.Collections.emptyList();
    signalSchemeList_ = java.util.Collections.emptyList();
    detectedRegion_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new CamData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private CamData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            type_ = input.readUInt32();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            ver_ = s;
            break;
          }
          case 24: {

            msgCnt_ = input.readUInt32();
            break;
          }
          case 32: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            deviceId_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            mapDeviceId_ = s;
            break;
          }
          case 58: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (refPos_ != null) {
              subBuilder = refPos_.toBuilder();
            }
            refPos_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(refPos_);
              refPos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 64: {
            int rawValue = input.readEnum();

            sceneType_ = rawValue;
            break;
          }
          case 74: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              ptcList_ = new java.util.ArrayList<road.data.proto.ParticipantData>();
              mutable_bitField0_ |= 0x00000001;
            }
            ptcList_.add(
                input.readMessage(road.data.proto.ParticipantData.parser(), extensionRegistry));
            break;
          }
          case 82: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              obstacleList_ = new java.util.ArrayList<road.data.proto.ObstacleData>();
              mutable_bitField0_ |= 0x00000002;
            }
            obstacleList_.add(
                input.readMessage(road.data.proto.ObstacleData.parser(), extensionRegistry));
            break;
          }
          case 90: {
            if (!((mutable_bitField0_ & 0x00000004) != 0)) {
              rteList_ = new java.util.ArrayList<road.data.proto.RteData>();
              mutable_bitField0_ |= 0x00000004;
            }
            rteList_.add(
                input.readMessage(road.data.proto.RteData.parser(), extensionRegistry));
            break;
          }
          case 98: {
            if (!((mutable_bitField0_ & 0x00000008) != 0)) {
              rtsList_ = new java.util.ArrayList<road.data.proto.RtsData>();
              mutable_bitField0_ |= 0x00000008;
            }
            rtsList_.add(
                input.readMessage(road.data.proto.RtsData.parser(), extensionRegistry));
            break;
          }
          case 106: {
            if (!((mutable_bitField0_ & 0x00000010) != 0)) {
              bsmList_ = new java.util.ArrayList<road.data.proto.BsmData>();
              mutable_bitField0_ |= 0x00000010;
            }
            bsmList_.add(
                input.readMessage(road.data.proto.BsmData.parser(), extensionRegistry));
            break;
          }
          case 114: {
            if (!((mutable_bitField0_ & 0x00000020) != 0)) {
              virList_ = new java.util.ArrayList<road.data.proto.VirData>();
              mutable_bitField0_ |= 0x00000020;
            }
            virList_.add(
                input.readMessage(road.data.proto.VirData.parser(), extensionRegistry));
            break;
          }
          case 122: {
            if (!((mutable_bitField0_ & 0x00000040) != 0)) {
              rscList_ = new java.util.ArrayList<road.data.proto.RscData>();
              mutable_bitField0_ |= 0x00000040;
            }
            rscList_.add(
                input.readMessage(road.data.proto.RscData.parser(), extensionRegistry));
            break;
          }
          case 130: {
            road.data.proto.SpatData.Builder subBuilder = null;
            if (roadSignalState_ != null) {
              subBuilder = roadSignalState_.toBuilder();
            }
            roadSignalState_ = input.readMessage(road.data.proto.SpatData.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(roadSignalState_);
              roadSignalState_ = subBuilder.buildPartial();
            }

            break;
          }
          case 138: {
            if (!((mutable_bitField0_ & 0x00000080) != 0)) {
              trafficFlow_ = new java.util.ArrayList<road.data.proto.TrafficFlow>();
              mutable_bitField0_ |= 0x00000080;
            }
            trafficFlow_.add(
                input.readMessage(road.data.proto.TrafficFlow.parser(), extensionRegistry));
            break;
          }
          case 146: {
            if (!((mutable_bitField0_ & 0x00000100) != 0)) {
              signalSchemeList_ = new java.util.ArrayList<road.data.proto.SignalScheme>();
              mutable_bitField0_ |= 0x00000100;
            }
            signalSchemeList_.add(
                input.readMessage(road.data.proto.SignalScheme.parser(), extensionRegistry));
            break;
          }
          case 154: {
            if (!((mutable_bitField0_ & 0x00000200) != 0)) {
              detectedRegion_ = new java.util.ArrayList<road.data.proto.Polygon>();
              mutable_bitField0_ |= 0x00000200;
            }
            detectedRegion_.add(
                input.readMessage(road.data.proto.Polygon.parser(), extensionRegistry));
            break;
          }
          case 160: {

            toAlgorithmTime_ = input.readUInt64();
            break;
          }
          case 168: {

            toDatabusTime_ = input.readUInt64();
            break;
          }
          case 176: {

            toCloudTime_ = input.readUInt64();
            break;
          }
          case 184: {

            id_ = input.readUInt64();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        ptcList_ = java.util.Collections.unmodifiableList(ptcList_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        obstacleList_ = java.util.Collections.unmodifiableList(obstacleList_);
      }
      if (((mutable_bitField0_ & 0x00000004) != 0)) {
        rteList_ = java.util.Collections.unmodifiableList(rteList_);
      }
      if (((mutable_bitField0_ & 0x00000008) != 0)) {
        rtsList_ = java.util.Collections.unmodifiableList(rtsList_);
      }
      if (((mutable_bitField0_ & 0x00000010) != 0)) {
        bsmList_ = java.util.Collections.unmodifiableList(bsmList_);
      }
      if (((mutable_bitField0_ & 0x00000020) != 0)) {
        virList_ = java.util.Collections.unmodifiableList(virList_);
      }
      if (((mutable_bitField0_ & 0x00000040) != 0)) {
        rscList_ = java.util.Collections.unmodifiableList(rscList_);
      }
      if (((mutable_bitField0_ & 0x00000080) != 0)) {
        trafficFlow_ = java.util.Collections.unmodifiableList(trafficFlow_);
      }
      if (((mutable_bitField0_ & 0x00000100) != 0)) {
        signalSchemeList_ = java.util.Collections.unmodifiableList(signalSchemeList_);
      }
      if (((mutable_bitField0_ & 0x00000200) != 0)) {
        detectedRegion_ = java.util.Collections.unmodifiableList(detectedRegion_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_CamData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_CamData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.CamData.class, road.data.proto.CamData.Builder.class);
  }

  public static final int TYPE_FIELD_NUMBER = 1;
  private int type_;
  /**
   * <pre>
   * type取值为1,表示MEC向RSU发送目标物和事件信息
   * </pre>
   *
   * <code>uint32 type = 1;</code>
   */
  public int getType() {
    return type_;
  }

  public static final int VER_FIELD_NUMBER = 2;
  private volatile java.lang.Object ver_;
  /**
   * <pre>
   * 版本号，目前版本固定为“01”
   * </pre>
   *
   * <code>string ver = 2;</code>
   */
  public java.lang.String getVer() {
    java.lang.Object ref = ver_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      ver_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 版本号，目前版本固定为“01”
   * </pre>
   *
   * <code>string ver = 2;</code>
   */
  public com.google.protobuf.ByteString
      getVerBytes() {
    java.lang.Object ref = ver_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      ver_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MSGCNT_FIELD_NUMBER = 3;
  private int msgCnt_;
  /**
   * <pre>
   * 定义消息编号。发送方对发送的同类消息(type=1)依次进行编号。编号循环发送。
   * </pre>
   *
   * <code>uint32 msgCnt = 3;</code>
   */
  public int getMsgCnt() {
    return msgCnt_;
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 4;
  private long timestamp_;
  /**
   * <pre>
   * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 4;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int DEVICEID_FIELD_NUMBER = 5;
  private volatile java.lang.Object deviceId_;
  /**
   * <pre>
   * MEC的设备编号。例如，MEC的ESN编号，或者每个MEC的序列编号。只支持可见字符（ASCII码[32,126]）。
   * </pre>
   *
   * <code>string deviceId = 5;</code>
   */
  public java.lang.String getDeviceId() {
    java.lang.Object ref = deviceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deviceId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * MEC的设备编号。例如，MEC的ESN编号，或者每个MEC的序列编号。只支持可见字符（ASCII码[32,126]）。
   * </pre>
   *
   * <code>string deviceId = 5;</code>
   */
  public com.google.protobuf.ByteString
      getDeviceIdBytes() {
    java.lang.Object ref = deviceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deviceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MAPDEVICEID_FIELD_NUMBER = 6;
  private volatile java.lang.Object mapDeviceId_;
  /**
   * <pre>
   *位置相关的设备编号
   * </pre>
   *
   * <code>string mapDeviceId = 6;</code>
   */
  public java.lang.String getMapDeviceId() {
    java.lang.Object ref = mapDeviceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      mapDeviceId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *位置相关的设备编号
   * </pre>
   *
   * <code>string mapDeviceId = 6;</code>
   */
  public com.google.protobuf.ByteString
      getMapDeviceIdBytes() {
    java.lang.Object ref = mapDeviceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      mapDeviceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REFPOS_FIELD_NUMBER = 7;
  private road.data.proto.Position3D refPos_;
  /**
   * <pre>
   * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
   */
  public boolean hasRefPos() {
    return refPos_ != null;
  }
  /**
   * <pre>
   * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
   */
  public road.data.proto.Position3D getRefPos() {
    return refPos_ == null ? road.data.proto.Position3D.getDefaultInstance() : refPos_;
  }
  /**
   * <pre>
   * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
   */
  public road.data.proto.Position3DOrBuilder getRefPosOrBuilder() {
    return getRefPos();
  }

  public static final int SCENETYPE_FIELD_NUMBER = 8;
  private int sceneType_;
  /**
   * <pre>
   * 可选，表示场地类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SceneType sceneType = 8;</code>
   */
  public int getSceneTypeValue() {
    return sceneType_;
  }
  /**
   * <pre>
   * 可选，表示场地类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SceneType sceneType = 8;</code>
   */
  public road.data.proto.SceneType getSceneType() {
    @SuppressWarnings("deprecation")
    road.data.proto.SceneType result = road.data.proto.SceneType.valueOf(sceneType_);
    return result == null ? road.data.proto.SceneType.UNRECOGNIZED : result;
  }

  public static final int PTCLIST_FIELD_NUMBER = 9;
  private java.util.List<road.data.proto.ParticipantData> ptcList_;
  /**
   * <pre>
   *可选，定义目标物列表，属于RSM中的交通参与者
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
   */
  public java.util.List<road.data.proto.ParticipantData> getPtcListList() {
    return ptcList_;
  }
  /**
   * <pre>
   *可选，定义目标物列表，属于RSM中的交通参与者
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
   */
  public java.util.List<? extends road.data.proto.ParticipantDataOrBuilder> 
      getPtcListOrBuilderList() {
    return ptcList_;
  }
  /**
   * <pre>
   *可选，定义目标物列表，属于RSM中的交通参与者
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
   */
  public int getPtcListCount() {
    return ptcList_.size();
  }
  /**
   * <pre>
   *可选，定义目标物列表，属于RSM中的交通参与者
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
   */
  public road.data.proto.ParticipantData getPtcList(int index) {
    return ptcList_.get(index);
  }
  /**
   * <pre>
   *可选，定义目标物列表，属于RSM中的交通参与者
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
   */
  public road.data.proto.ParticipantDataOrBuilder getPtcListOrBuilder(
      int index) {
    return ptcList_.get(index);
  }

  public static final int OBSTACLELIST_FIELD_NUMBER = 10;
  private java.util.List<road.data.proto.ObstacleData> obstacleList_;
  /**
   * <pre>
   *可选，定义障碍物列表，属于RSM中的障碍物
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
   */
  public java.util.List<road.data.proto.ObstacleData> getObstacleListList() {
    return obstacleList_;
  }
  /**
   * <pre>
   *可选，定义障碍物列表，属于RSM中的障碍物
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
   */
  public java.util.List<? extends road.data.proto.ObstacleDataOrBuilder> 
      getObstacleListOrBuilderList() {
    return obstacleList_;
  }
  /**
   * <pre>
   *可选，定义障碍物列表，属于RSM中的障碍物
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
   */
  public int getObstacleListCount() {
    return obstacleList_.size();
  }
  /**
   * <pre>
   *可选，定义障碍物列表，属于RSM中的障碍物
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
   */
  public road.data.proto.ObstacleData getObstacleList(int index) {
    return obstacleList_.get(index);
  }
  /**
   * <pre>
   *可选，定义障碍物列表，属于RSM中的障碍物
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
   */
  public road.data.proto.ObstacleDataOrBuilder getObstacleListOrBuilder(
      int index) {
    return obstacleList_.get(index);
  }

  public static final int RTELIST_FIELD_NUMBER = 11;
  private java.util.List<road.data.proto.RteData> rteList_;
  /**
   * <pre>
   *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
   */
  public java.util.List<road.data.proto.RteData> getRteListList() {
    return rteList_;
  }
  /**
   * <pre>
   *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
   */
  public java.util.List<? extends road.data.proto.RteDataOrBuilder> 
      getRteListOrBuilderList() {
    return rteList_;
  }
  /**
   * <pre>
   *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
   */
  public int getRteListCount() {
    return rteList_.size();
  }
  /**
   * <pre>
   *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
   */
  public road.data.proto.RteData getRteList(int index) {
    return rteList_.get(index);
  }
  /**
   * <pre>
   *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
   */
  public road.data.proto.RteDataOrBuilder getRteListOrBuilder(
      int index) {
    return rteList_.get(index);
  }

  public static final int RTSLIST_FIELD_NUMBER = 12;
  private java.util.List<road.data.proto.RtsData> rtsList_;
  /**
   * <pre>
   *可选，交通标志信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
   */
  public java.util.List<road.data.proto.RtsData> getRtsListList() {
    return rtsList_;
  }
  /**
   * <pre>
   *可选，交通标志信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
   */
  public java.util.List<? extends road.data.proto.RtsDataOrBuilder> 
      getRtsListOrBuilderList() {
    return rtsList_;
  }
  /**
   * <pre>
   *可选，交通标志信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
   */
  public int getRtsListCount() {
    return rtsList_.size();
  }
  /**
   * <pre>
   *可选，交通标志信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
   */
  public road.data.proto.RtsData getRtsList(int index) {
    return rtsList_.get(index);
  }
  /**
   * <pre>
   *可选，交通标志信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
   */
  public road.data.proto.RtsDataOrBuilder getRtsListOrBuilder(
      int index) {
    return rtsList_.get(index);
  }

  public static final int BSMLIST_FIELD_NUMBER = 13;
  private java.util.List<road.data.proto.BsmData> bsmList_;
  /**
   * <pre>
   *可选，基本安全消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
   */
  public java.util.List<road.data.proto.BsmData> getBsmListList() {
    return bsmList_;
  }
  /**
   * <pre>
   *可选，基本安全消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
   */
  public java.util.List<? extends road.data.proto.BsmDataOrBuilder> 
      getBsmListOrBuilderList() {
    return bsmList_;
  }
  /**
   * <pre>
   *可选，基本安全消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
   */
  public int getBsmListCount() {
    return bsmList_.size();
  }
  /**
   * <pre>
   *可选，基本安全消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
   */
  public road.data.proto.BsmData getBsmList(int index) {
    return bsmList_.get(index);
  }
  /**
   * <pre>
   *可选，基本安全消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
   */
  public road.data.proto.BsmDataOrBuilder getBsmListOrBuilder(
      int index) {
    return bsmList_.get(index);
  }

  public static final int VIRLIST_FIELD_NUMBER = 14;
  private java.util.List<road.data.proto.VirData> virList_;
  /**
   * <pre>
   *可选，车辆意图及请求消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
   */
  public java.util.List<road.data.proto.VirData> getVirListList() {
    return virList_;
  }
  /**
   * <pre>
   *可选，车辆意图及请求消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
   */
  public java.util.List<? extends road.data.proto.VirDataOrBuilder> 
      getVirListOrBuilderList() {
    return virList_;
  }
  /**
   * <pre>
   *可选，车辆意图及请求消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
   */
  public int getVirListCount() {
    return virList_.size();
  }
  /**
   * <pre>
   *可选，车辆意图及请求消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
   */
  public road.data.proto.VirData getVirList(int index) {
    return virList_.get(index);
  }
  /**
   * <pre>
   *可选，车辆意图及请求消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
   */
  public road.data.proto.VirDataOrBuilder getVirListOrBuilder(
      int index) {
    return virList_.get(index);
  }

  public static final int RSCLIST_FIELD_NUMBER = 15;
  private java.util.List<road.data.proto.RscData> rscList_;
  /**
   * <pre>
   *可选，车辆协作或引导消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
   */
  public java.util.List<road.data.proto.RscData> getRscListList() {
    return rscList_;
  }
  /**
   * <pre>
   *可选，车辆协作或引导消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
   */
  public java.util.List<? extends road.data.proto.RscDataOrBuilder> 
      getRscListOrBuilderList() {
    return rscList_;
  }
  /**
   * <pre>
   *可选，车辆协作或引导消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
   */
  public int getRscListCount() {
    return rscList_.size();
  }
  /**
   * <pre>
   *可选，车辆协作或引导消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
   */
  public road.data.proto.RscData getRscList(int index) {
    return rscList_.get(index);
  }
  /**
   * <pre>
   *可选，车辆协作或引导消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
   */
  public road.data.proto.RscDataOrBuilder getRscListOrBuilder(
      int index) {
    return rscList_.get(index);
  }

  public static final int ROADSIGNALSTATE_FIELD_NUMBER = 16;
  private road.data.proto.SpatData roadSignalState_;
  /**
   * <pre>
   * 可选，实时交通相位SPAT信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
   */
  public boolean hasRoadSignalState() {
    return roadSignalState_ != null;
  }
  /**
   * <pre>
   * 可选，实时交通相位SPAT信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
   */
  public road.data.proto.SpatData getRoadSignalState() {
    return roadSignalState_ == null ? road.data.proto.SpatData.getDefaultInstance() : roadSignalState_;
  }
  /**
   * <pre>
   * 可选，实时交通相位SPAT信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
   */
  public road.data.proto.SpatDataOrBuilder getRoadSignalStateOrBuilder() {
    return getRoadSignalState();
  }

  public static final int TRAFFICFLOW_FIELD_NUMBER = 17;
  private java.util.List<road.data.proto.TrafficFlow> trafficFlow_;
  /**
   * <pre>
   *可选，实时交通流信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
   */
  public java.util.List<road.data.proto.TrafficFlow> getTrafficFlowList() {
    return trafficFlow_;
  }
  /**
   * <pre>
   *可选，实时交通流信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
   */
  public java.util.List<? extends road.data.proto.TrafficFlowOrBuilder> 
      getTrafficFlowOrBuilderList() {
    return trafficFlow_;
  }
  /**
   * <pre>
   *可选，实时交通流信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
   */
  public int getTrafficFlowCount() {
    return trafficFlow_.size();
  }
  /**
   * <pre>
   *可选，实时交通流信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
   */
  public road.data.proto.TrafficFlow getTrafficFlow(int index) {
    return trafficFlow_.get(index);
  }
  /**
   * <pre>
   *可选，实时交通流信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
   */
  public road.data.proto.TrafficFlowOrBuilder getTrafficFlowOrBuilder(
      int index) {
    return trafficFlow_.get(index);
  }

  public static final int SIGNALSCHEMELIST_FIELD_NUMBER = 18;
  private java.util.List<road.data.proto.SignalScheme> signalSchemeList_;
  /**
   * <pre>
   *可选，信号优化建议列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
   */
  public java.util.List<road.data.proto.SignalScheme> getSignalSchemeListList() {
    return signalSchemeList_;
  }
  /**
   * <pre>
   *可选，信号优化建议列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
   */
  public java.util.List<? extends road.data.proto.SignalSchemeOrBuilder> 
      getSignalSchemeListOrBuilderList() {
    return signalSchemeList_;
  }
  /**
   * <pre>
   *可选，信号优化建议列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
   */
  public int getSignalSchemeListCount() {
    return signalSchemeList_.size();
  }
  /**
   * <pre>
   *可选，信号优化建议列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
   */
  public road.data.proto.SignalScheme getSignalSchemeList(int index) {
    return signalSchemeList_.get(index);
  }
  /**
   * <pre>
   *可选，信号优化建议列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
   */
  public road.data.proto.SignalSchemeOrBuilder getSignalSchemeListOrBuilder(
      int index) {
    return signalSchemeList_.get(index);
  }

  public static final int DETECTEDREGION_FIELD_NUMBER = 19;
  private java.util.List<road.data.proto.Polygon> detectedRegion_;
  /**
   * <pre>
   * 可选，定义感知区域列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
   */
  public java.util.List<road.data.proto.Polygon> getDetectedRegionList() {
    return detectedRegion_;
  }
  /**
   * <pre>
   * 可选，定义感知区域列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
   */
  public java.util.List<? extends road.data.proto.PolygonOrBuilder> 
      getDetectedRegionOrBuilderList() {
    return detectedRegion_;
  }
  /**
   * <pre>
   * 可选，定义感知区域列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
   */
  public int getDetectedRegionCount() {
    return detectedRegion_.size();
  }
  /**
   * <pre>
   * 可选，定义感知区域列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
   */
  public road.data.proto.Polygon getDetectedRegion(int index) {
    return detectedRegion_.get(index);
  }
  /**
   * <pre>
   * 可选，定义感知区域列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
   */
  public road.data.proto.PolygonOrBuilder getDetectedRegionOrBuilder(
      int index) {
    return detectedRegion_.get(index);
  }

  public static final int TOALGORITHMTIME_FIELD_NUMBER = 20;
  private long toAlgorithmTime_;
  /**
   * <pre>
   *  可选，到达融合算法的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 toAlgorithmTime = 20;</code>
   */
  public long getToAlgorithmTime() {
    return toAlgorithmTime_;
  }

  public static final int TODATABUSTIME_FIELD_NUMBER = 21;
  private long toDatabusTime_;
  /**
   * <pre>
   * 可选，到达当前接收端的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 toDatabusTime = 21;</code>
   */
  public long getToDatabusTime() {
    return toDatabusTime_;
  }

  public static final int TOCLOUDTIME_FIELD_NUMBER = 22;
  private long toCloudTime_;
  /**
   * <pre>
   * 可选，到达云端的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 toCloudTime = 22;</code>
   */
  public long getToCloudTime() {
    return toCloudTime_;
  }

  public static final int ID_FIELD_NUMBER = 23;
  private long id_;
  /**
   * <pre>
   * 可选，数据唯一标识id
   * </pre>
   *
   * <code>uint64 id = 23;</code>
   */
  public long getId() {
    return id_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (type_ != 0) {
      output.writeUInt32(1, type_);
    }
    if (!getVerBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, ver_);
    }
    if (msgCnt_ != 0) {
      output.writeUInt32(3, msgCnt_);
    }
    if (timestamp_ != 0L) {
      output.writeUInt64(4, timestamp_);
    }
    if (!getDeviceIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, deviceId_);
    }
    if (!getMapDeviceIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, mapDeviceId_);
    }
    if (refPos_ != null) {
      output.writeMessage(7, getRefPos());
    }
    if (sceneType_ != road.data.proto.SceneType.SCENE_TYPE_URBAN.getNumber()) {
      output.writeEnum(8, sceneType_);
    }
    for (int i = 0; i < ptcList_.size(); i++) {
      output.writeMessage(9, ptcList_.get(i));
    }
    for (int i = 0; i < obstacleList_.size(); i++) {
      output.writeMessage(10, obstacleList_.get(i));
    }
    for (int i = 0; i < rteList_.size(); i++) {
      output.writeMessage(11, rteList_.get(i));
    }
    for (int i = 0; i < rtsList_.size(); i++) {
      output.writeMessage(12, rtsList_.get(i));
    }
    for (int i = 0; i < bsmList_.size(); i++) {
      output.writeMessage(13, bsmList_.get(i));
    }
    for (int i = 0; i < virList_.size(); i++) {
      output.writeMessage(14, virList_.get(i));
    }
    for (int i = 0; i < rscList_.size(); i++) {
      output.writeMessage(15, rscList_.get(i));
    }
    if (roadSignalState_ != null) {
      output.writeMessage(16, getRoadSignalState());
    }
    for (int i = 0; i < trafficFlow_.size(); i++) {
      output.writeMessage(17, trafficFlow_.get(i));
    }
    for (int i = 0; i < signalSchemeList_.size(); i++) {
      output.writeMessage(18, signalSchemeList_.get(i));
    }
    for (int i = 0; i < detectedRegion_.size(); i++) {
      output.writeMessage(19, detectedRegion_.get(i));
    }
    if (toAlgorithmTime_ != 0L) {
      output.writeUInt64(20, toAlgorithmTime_);
    }
    if (toDatabusTime_ != 0L) {
      output.writeUInt64(21, toDatabusTime_);
    }
    if (toCloudTime_ != 0L) {
      output.writeUInt64(22, toCloudTime_);
    }
    if (id_ != 0L) {
      output.writeUInt64(23, id_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (type_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, type_);
    }
    if (!getVerBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, ver_);
    }
    if (msgCnt_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, msgCnt_);
    }
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(4, timestamp_);
    }
    if (!getDeviceIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, deviceId_);
    }
    if (!getMapDeviceIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, mapDeviceId_);
    }
    if (refPos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getRefPos());
    }
    if (sceneType_ != road.data.proto.SceneType.SCENE_TYPE_URBAN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(8, sceneType_);
    }
    for (int i = 0; i < ptcList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(9, ptcList_.get(i));
    }
    for (int i = 0; i < obstacleList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(10, obstacleList_.get(i));
    }
    for (int i = 0; i < rteList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(11, rteList_.get(i));
    }
    for (int i = 0; i < rtsList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(12, rtsList_.get(i));
    }
    for (int i = 0; i < bsmList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(13, bsmList_.get(i));
    }
    for (int i = 0; i < virList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(14, virList_.get(i));
    }
    for (int i = 0; i < rscList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(15, rscList_.get(i));
    }
    if (roadSignalState_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(16, getRoadSignalState());
    }
    for (int i = 0; i < trafficFlow_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(17, trafficFlow_.get(i));
    }
    for (int i = 0; i < signalSchemeList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(18, signalSchemeList_.get(i));
    }
    for (int i = 0; i < detectedRegion_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(19, detectedRegion_.get(i));
    }
    if (toAlgorithmTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(20, toAlgorithmTime_);
    }
    if (toDatabusTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(21, toDatabusTime_);
    }
    if (toCloudTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(22, toCloudTime_);
    }
    if (id_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(23, id_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.CamData)) {
      return super.equals(obj);
    }
    road.data.proto.CamData other = (road.data.proto.CamData) obj;

    if (getType()
        != other.getType()) return false;
    if (!getVer()
        .equals(other.getVer())) return false;
    if (getMsgCnt()
        != other.getMsgCnt()) return false;
    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (!getDeviceId()
        .equals(other.getDeviceId())) return false;
    if (!getMapDeviceId()
        .equals(other.getMapDeviceId())) return false;
    if (hasRefPos() != other.hasRefPos()) return false;
    if (hasRefPos()) {
      if (!getRefPos()
          .equals(other.getRefPos())) return false;
    }
    if (sceneType_ != other.sceneType_) return false;
    if (!getPtcListList()
        .equals(other.getPtcListList())) return false;
    if (!getObstacleListList()
        .equals(other.getObstacleListList())) return false;
    if (!getRteListList()
        .equals(other.getRteListList())) return false;
    if (!getRtsListList()
        .equals(other.getRtsListList())) return false;
    if (!getBsmListList()
        .equals(other.getBsmListList())) return false;
    if (!getVirListList()
        .equals(other.getVirListList())) return false;
    if (!getRscListList()
        .equals(other.getRscListList())) return false;
    if (hasRoadSignalState() != other.hasRoadSignalState()) return false;
    if (hasRoadSignalState()) {
      if (!getRoadSignalState()
          .equals(other.getRoadSignalState())) return false;
    }
    if (!getTrafficFlowList()
        .equals(other.getTrafficFlowList())) return false;
    if (!getSignalSchemeListList()
        .equals(other.getSignalSchemeListList())) return false;
    if (!getDetectedRegionList()
        .equals(other.getDetectedRegionList())) return false;
    if (getToAlgorithmTime()
        != other.getToAlgorithmTime()) return false;
    if (getToDatabusTime()
        != other.getToDatabusTime()) return false;
    if (getToCloudTime()
        != other.getToCloudTime()) return false;
    if (getId()
        != other.getId()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getType();
    hash = (37 * hash) + VER_FIELD_NUMBER;
    hash = (53 * hash) + getVer().hashCode();
    hash = (37 * hash) + MSGCNT_FIELD_NUMBER;
    hash = (53 * hash) + getMsgCnt();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    hash = (37 * hash) + DEVICEID_FIELD_NUMBER;
    hash = (53 * hash) + getDeviceId().hashCode();
    hash = (37 * hash) + MAPDEVICEID_FIELD_NUMBER;
    hash = (53 * hash) + getMapDeviceId().hashCode();
    if (hasRefPos()) {
      hash = (37 * hash) + REFPOS_FIELD_NUMBER;
      hash = (53 * hash) + getRefPos().hashCode();
    }
    hash = (37 * hash) + SCENETYPE_FIELD_NUMBER;
    hash = (53 * hash) + sceneType_;
    if (getPtcListCount() > 0) {
      hash = (37 * hash) + PTCLIST_FIELD_NUMBER;
      hash = (53 * hash) + getPtcListList().hashCode();
    }
    if (getObstacleListCount() > 0) {
      hash = (37 * hash) + OBSTACLELIST_FIELD_NUMBER;
      hash = (53 * hash) + getObstacleListList().hashCode();
    }
    if (getRteListCount() > 0) {
      hash = (37 * hash) + RTELIST_FIELD_NUMBER;
      hash = (53 * hash) + getRteListList().hashCode();
    }
    if (getRtsListCount() > 0) {
      hash = (37 * hash) + RTSLIST_FIELD_NUMBER;
      hash = (53 * hash) + getRtsListList().hashCode();
    }
    if (getBsmListCount() > 0) {
      hash = (37 * hash) + BSMLIST_FIELD_NUMBER;
      hash = (53 * hash) + getBsmListList().hashCode();
    }
    if (getVirListCount() > 0) {
      hash = (37 * hash) + VIRLIST_FIELD_NUMBER;
      hash = (53 * hash) + getVirListList().hashCode();
    }
    if (getRscListCount() > 0) {
      hash = (37 * hash) + RSCLIST_FIELD_NUMBER;
      hash = (53 * hash) + getRscListList().hashCode();
    }
    if (hasRoadSignalState()) {
      hash = (37 * hash) + ROADSIGNALSTATE_FIELD_NUMBER;
      hash = (53 * hash) + getRoadSignalState().hashCode();
    }
    if (getTrafficFlowCount() > 0) {
      hash = (37 * hash) + TRAFFICFLOW_FIELD_NUMBER;
      hash = (53 * hash) + getTrafficFlowList().hashCode();
    }
    if (getSignalSchemeListCount() > 0) {
      hash = (37 * hash) + SIGNALSCHEMELIST_FIELD_NUMBER;
      hash = (53 * hash) + getSignalSchemeListList().hashCode();
    }
    if (getDetectedRegionCount() > 0) {
      hash = (37 * hash) + DETECTEDREGION_FIELD_NUMBER;
      hash = (53 * hash) + getDetectedRegionList().hashCode();
    }
    hash = (37 * hash) + TOALGORITHMTIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getToAlgorithmTime());
    hash = (37 * hash) + TODATABUSTIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getToDatabusTime());
    hash = (37 * hash) + TOCLOUDTIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getToCloudTime());
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getId());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.CamData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.CamData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.CamData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.CamData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.CamData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.CamData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.CamData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.CamData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.CamData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.CamData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.CamData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.CamData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.CamData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *合作感知信息   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.CamData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.CamData)
      road.data.proto.CamDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_CamData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_CamData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.CamData.class, road.data.proto.CamData.Builder.class);
    }

    // Construct using road.data.proto.CamData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getPtcListFieldBuilder();
        getObstacleListFieldBuilder();
        getRteListFieldBuilder();
        getRtsListFieldBuilder();
        getBsmListFieldBuilder();
        getVirListFieldBuilder();
        getRscListFieldBuilder();
        getTrafficFlowFieldBuilder();
        getSignalSchemeListFieldBuilder();
        getDetectedRegionFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      type_ = 0;

      ver_ = "";

      msgCnt_ = 0;

      timestamp_ = 0L;

      deviceId_ = "";

      mapDeviceId_ = "";

      if (refPosBuilder_ == null) {
        refPos_ = null;
      } else {
        refPos_ = null;
        refPosBuilder_ = null;
      }
      sceneType_ = 0;

      if (ptcListBuilder_ == null) {
        ptcList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        ptcListBuilder_.clear();
      }
      if (obstacleListBuilder_ == null) {
        obstacleList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        obstacleListBuilder_.clear();
      }
      if (rteListBuilder_ == null) {
        rteList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
      } else {
        rteListBuilder_.clear();
      }
      if (rtsListBuilder_ == null) {
        rtsList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
      } else {
        rtsListBuilder_.clear();
      }
      if (bsmListBuilder_ == null) {
        bsmList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
      } else {
        bsmListBuilder_.clear();
      }
      if (virListBuilder_ == null) {
        virList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
      } else {
        virListBuilder_.clear();
      }
      if (rscListBuilder_ == null) {
        rscList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
      } else {
        rscListBuilder_.clear();
      }
      if (roadSignalStateBuilder_ == null) {
        roadSignalState_ = null;
      } else {
        roadSignalState_ = null;
        roadSignalStateBuilder_ = null;
      }
      if (trafficFlowBuilder_ == null) {
        trafficFlow_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
      } else {
        trafficFlowBuilder_.clear();
      }
      if (signalSchemeListBuilder_ == null) {
        signalSchemeList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000100);
      } else {
        signalSchemeListBuilder_.clear();
      }
      if (detectedRegionBuilder_ == null) {
        detectedRegion_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000200);
      } else {
        detectedRegionBuilder_.clear();
      }
      toAlgorithmTime_ = 0L;

      toDatabusTime_ = 0L;

      toCloudTime_ = 0L;

      id_ = 0L;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_CamData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.CamData getDefaultInstanceForType() {
      return road.data.proto.CamData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.CamData build() {
      road.data.proto.CamData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.CamData buildPartial() {
      road.data.proto.CamData result = new road.data.proto.CamData(this);
      int from_bitField0_ = bitField0_;
      result.type_ = type_;
      result.ver_ = ver_;
      result.msgCnt_ = msgCnt_;
      result.timestamp_ = timestamp_;
      result.deviceId_ = deviceId_;
      result.mapDeviceId_ = mapDeviceId_;
      if (refPosBuilder_ == null) {
        result.refPos_ = refPos_;
      } else {
        result.refPos_ = refPosBuilder_.build();
      }
      result.sceneType_ = sceneType_;
      if (ptcListBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          ptcList_ = java.util.Collections.unmodifiableList(ptcList_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.ptcList_ = ptcList_;
      } else {
        result.ptcList_ = ptcListBuilder_.build();
      }
      if (obstacleListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          obstacleList_ = java.util.Collections.unmodifiableList(obstacleList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.obstacleList_ = obstacleList_;
      } else {
        result.obstacleList_ = obstacleListBuilder_.build();
      }
      if (rteListBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          rteList_ = java.util.Collections.unmodifiableList(rteList_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.rteList_ = rteList_;
      } else {
        result.rteList_ = rteListBuilder_.build();
      }
      if (rtsListBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          rtsList_ = java.util.Collections.unmodifiableList(rtsList_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.rtsList_ = rtsList_;
      } else {
        result.rtsList_ = rtsListBuilder_.build();
      }
      if (bsmListBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          bsmList_ = java.util.Collections.unmodifiableList(bsmList_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.bsmList_ = bsmList_;
      } else {
        result.bsmList_ = bsmListBuilder_.build();
      }
      if (virListBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0)) {
          virList_ = java.util.Collections.unmodifiableList(virList_);
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.virList_ = virList_;
      } else {
        result.virList_ = virListBuilder_.build();
      }
      if (rscListBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0)) {
          rscList_ = java.util.Collections.unmodifiableList(rscList_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.rscList_ = rscList_;
      } else {
        result.rscList_ = rscListBuilder_.build();
      }
      if (roadSignalStateBuilder_ == null) {
        result.roadSignalState_ = roadSignalState_;
      } else {
        result.roadSignalState_ = roadSignalStateBuilder_.build();
      }
      if (trafficFlowBuilder_ == null) {
        if (((bitField0_ & 0x00000080) != 0)) {
          trafficFlow_ = java.util.Collections.unmodifiableList(trafficFlow_);
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.trafficFlow_ = trafficFlow_;
      } else {
        result.trafficFlow_ = trafficFlowBuilder_.build();
      }
      if (signalSchemeListBuilder_ == null) {
        if (((bitField0_ & 0x00000100) != 0)) {
          signalSchemeList_ = java.util.Collections.unmodifiableList(signalSchemeList_);
          bitField0_ = (bitField0_ & ~0x00000100);
        }
        result.signalSchemeList_ = signalSchemeList_;
      } else {
        result.signalSchemeList_ = signalSchemeListBuilder_.build();
      }
      if (detectedRegionBuilder_ == null) {
        if (((bitField0_ & 0x00000200) != 0)) {
          detectedRegion_ = java.util.Collections.unmodifiableList(detectedRegion_);
          bitField0_ = (bitField0_ & ~0x00000200);
        }
        result.detectedRegion_ = detectedRegion_;
      } else {
        result.detectedRegion_ = detectedRegionBuilder_.build();
      }
      result.toAlgorithmTime_ = toAlgorithmTime_;
      result.toDatabusTime_ = toDatabusTime_;
      result.toCloudTime_ = toCloudTime_;
      result.id_ = id_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.CamData) {
        return mergeFrom((road.data.proto.CamData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.CamData other) {
      if (other == road.data.proto.CamData.getDefaultInstance()) return this;
      if (other.getType() != 0) {
        setType(other.getType());
      }
      if (!other.getVer().isEmpty()) {
        ver_ = other.ver_;
        onChanged();
      }
      if (other.getMsgCnt() != 0) {
        setMsgCnt(other.getMsgCnt());
      }
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (!other.getDeviceId().isEmpty()) {
        deviceId_ = other.deviceId_;
        onChanged();
      }
      if (!other.getMapDeviceId().isEmpty()) {
        mapDeviceId_ = other.mapDeviceId_;
        onChanged();
      }
      if (other.hasRefPos()) {
        mergeRefPos(other.getRefPos());
      }
      if (other.sceneType_ != 0) {
        setSceneTypeValue(other.getSceneTypeValue());
      }
      if (ptcListBuilder_ == null) {
        if (!other.ptcList_.isEmpty()) {
          if (ptcList_.isEmpty()) {
            ptcList_ = other.ptcList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePtcListIsMutable();
            ptcList_.addAll(other.ptcList_);
          }
          onChanged();
        }
      } else {
        if (!other.ptcList_.isEmpty()) {
          if (ptcListBuilder_.isEmpty()) {
            ptcListBuilder_.dispose();
            ptcListBuilder_ = null;
            ptcList_ = other.ptcList_;
            bitField0_ = (bitField0_ & ~0x00000001);
            ptcListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getPtcListFieldBuilder() : null;
          } else {
            ptcListBuilder_.addAllMessages(other.ptcList_);
          }
        }
      }
      if (obstacleListBuilder_ == null) {
        if (!other.obstacleList_.isEmpty()) {
          if (obstacleList_.isEmpty()) {
            obstacleList_ = other.obstacleList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureObstacleListIsMutable();
            obstacleList_.addAll(other.obstacleList_);
          }
          onChanged();
        }
      } else {
        if (!other.obstacleList_.isEmpty()) {
          if (obstacleListBuilder_.isEmpty()) {
            obstacleListBuilder_.dispose();
            obstacleListBuilder_ = null;
            obstacleList_ = other.obstacleList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            obstacleListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getObstacleListFieldBuilder() : null;
          } else {
            obstacleListBuilder_.addAllMessages(other.obstacleList_);
          }
        }
      }
      if (rteListBuilder_ == null) {
        if (!other.rteList_.isEmpty()) {
          if (rteList_.isEmpty()) {
            rteList_ = other.rteList_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureRteListIsMutable();
            rteList_.addAll(other.rteList_);
          }
          onChanged();
        }
      } else {
        if (!other.rteList_.isEmpty()) {
          if (rteListBuilder_.isEmpty()) {
            rteListBuilder_.dispose();
            rteListBuilder_ = null;
            rteList_ = other.rteList_;
            bitField0_ = (bitField0_ & ~0x00000004);
            rteListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getRteListFieldBuilder() : null;
          } else {
            rteListBuilder_.addAllMessages(other.rteList_);
          }
        }
      }
      if (rtsListBuilder_ == null) {
        if (!other.rtsList_.isEmpty()) {
          if (rtsList_.isEmpty()) {
            rtsList_ = other.rtsList_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureRtsListIsMutable();
            rtsList_.addAll(other.rtsList_);
          }
          onChanged();
        }
      } else {
        if (!other.rtsList_.isEmpty()) {
          if (rtsListBuilder_.isEmpty()) {
            rtsListBuilder_.dispose();
            rtsListBuilder_ = null;
            rtsList_ = other.rtsList_;
            bitField0_ = (bitField0_ & ~0x00000008);
            rtsListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getRtsListFieldBuilder() : null;
          } else {
            rtsListBuilder_.addAllMessages(other.rtsList_);
          }
        }
      }
      if (bsmListBuilder_ == null) {
        if (!other.bsmList_.isEmpty()) {
          if (bsmList_.isEmpty()) {
            bsmList_ = other.bsmList_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureBsmListIsMutable();
            bsmList_.addAll(other.bsmList_);
          }
          onChanged();
        }
      } else {
        if (!other.bsmList_.isEmpty()) {
          if (bsmListBuilder_.isEmpty()) {
            bsmListBuilder_.dispose();
            bsmListBuilder_ = null;
            bsmList_ = other.bsmList_;
            bitField0_ = (bitField0_ & ~0x00000010);
            bsmListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getBsmListFieldBuilder() : null;
          } else {
            bsmListBuilder_.addAllMessages(other.bsmList_);
          }
        }
      }
      if (virListBuilder_ == null) {
        if (!other.virList_.isEmpty()) {
          if (virList_.isEmpty()) {
            virList_ = other.virList_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureVirListIsMutable();
            virList_.addAll(other.virList_);
          }
          onChanged();
        }
      } else {
        if (!other.virList_.isEmpty()) {
          if (virListBuilder_.isEmpty()) {
            virListBuilder_.dispose();
            virListBuilder_ = null;
            virList_ = other.virList_;
            bitField0_ = (bitField0_ & ~0x00000020);
            virListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getVirListFieldBuilder() : null;
          } else {
            virListBuilder_.addAllMessages(other.virList_);
          }
        }
      }
      if (rscListBuilder_ == null) {
        if (!other.rscList_.isEmpty()) {
          if (rscList_.isEmpty()) {
            rscList_ = other.rscList_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureRscListIsMutable();
            rscList_.addAll(other.rscList_);
          }
          onChanged();
        }
      } else {
        if (!other.rscList_.isEmpty()) {
          if (rscListBuilder_.isEmpty()) {
            rscListBuilder_.dispose();
            rscListBuilder_ = null;
            rscList_ = other.rscList_;
            bitField0_ = (bitField0_ & ~0x00000040);
            rscListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getRscListFieldBuilder() : null;
          } else {
            rscListBuilder_.addAllMessages(other.rscList_);
          }
        }
      }
      if (other.hasRoadSignalState()) {
        mergeRoadSignalState(other.getRoadSignalState());
      }
      if (trafficFlowBuilder_ == null) {
        if (!other.trafficFlow_.isEmpty()) {
          if (trafficFlow_.isEmpty()) {
            trafficFlow_ = other.trafficFlow_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureTrafficFlowIsMutable();
            trafficFlow_.addAll(other.trafficFlow_);
          }
          onChanged();
        }
      } else {
        if (!other.trafficFlow_.isEmpty()) {
          if (trafficFlowBuilder_.isEmpty()) {
            trafficFlowBuilder_.dispose();
            trafficFlowBuilder_ = null;
            trafficFlow_ = other.trafficFlow_;
            bitField0_ = (bitField0_ & ~0x00000080);
            trafficFlowBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getTrafficFlowFieldBuilder() : null;
          } else {
            trafficFlowBuilder_.addAllMessages(other.trafficFlow_);
          }
        }
      }
      if (signalSchemeListBuilder_ == null) {
        if (!other.signalSchemeList_.isEmpty()) {
          if (signalSchemeList_.isEmpty()) {
            signalSchemeList_ = other.signalSchemeList_;
            bitField0_ = (bitField0_ & ~0x00000100);
          } else {
            ensureSignalSchemeListIsMutable();
            signalSchemeList_.addAll(other.signalSchemeList_);
          }
          onChanged();
        }
      } else {
        if (!other.signalSchemeList_.isEmpty()) {
          if (signalSchemeListBuilder_.isEmpty()) {
            signalSchemeListBuilder_.dispose();
            signalSchemeListBuilder_ = null;
            signalSchemeList_ = other.signalSchemeList_;
            bitField0_ = (bitField0_ & ~0x00000100);
            signalSchemeListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getSignalSchemeListFieldBuilder() : null;
          } else {
            signalSchemeListBuilder_.addAllMessages(other.signalSchemeList_);
          }
        }
      }
      if (detectedRegionBuilder_ == null) {
        if (!other.detectedRegion_.isEmpty()) {
          if (detectedRegion_.isEmpty()) {
            detectedRegion_ = other.detectedRegion_;
            bitField0_ = (bitField0_ & ~0x00000200);
          } else {
            ensureDetectedRegionIsMutable();
            detectedRegion_.addAll(other.detectedRegion_);
          }
          onChanged();
        }
      } else {
        if (!other.detectedRegion_.isEmpty()) {
          if (detectedRegionBuilder_.isEmpty()) {
            detectedRegionBuilder_.dispose();
            detectedRegionBuilder_ = null;
            detectedRegion_ = other.detectedRegion_;
            bitField0_ = (bitField0_ & ~0x00000200);
            detectedRegionBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getDetectedRegionFieldBuilder() : null;
          } else {
            detectedRegionBuilder_.addAllMessages(other.detectedRegion_);
          }
        }
      }
      if (other.getToAlgorithmTime() != 0L) {
        setToAlgorithmTime(other.getToAlgorithmTime());
      }
      if (other.getToDatabusTime() != 0L) {
        setToDatabusTime(other.getToDatabusTime());
      }
      if (other.getToCloudTime() != 0L) {
        setToCloudTime(other.getToCloudTime());
      }
      if (other.getId() != 0L) {
        setId(other.getId());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.CamData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.CamData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int type_ ;
    /**
     * <pre>
     * type取值为1,表示MEC向RSU发送目标物和事件信息
     * </pre>
     *
     * <code>uint32 type = 1;</code>
     */
    public int getType() {
      return type_;
    }
    /**
     * <pre>
     * type取值为1,表示MEC向RSU发送目标物和事件信息
     * </pre>
     *
     * <code>uint32 type = 1;</code>
     */
    public Builder setType(int value) {
      
      type_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * type取值为1,表示MEC向RSU发送目标物和事件信息
     * </pre>
     *
     * <code>uint32 type = 1;</code>
     */
    public Builder clearType() {
      
      type_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object ver_ = "";
    /**
     * <pre>
     * 版本号，目前版本固定为“01”
     * </pre>
     *
     * <code>string ver = 2;</code>
     */
    public java.lang.String getVer() {
      java.lang.Object ref = ver_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ver_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 版本号，目前版本固定为“01”
     * </pre>
     *
     * <code>string ver = 2;</code>
     */
    public com.google.protobuf.ByteString
        getVerBytes() {
      java.lang.Object ref = ver_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ver_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 版本号，目前版本固定为“01”
     * </pre>
     *
     * <code>string ver = 2;</code>
     */
    public Builder setVer(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      ver_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 版本号，目前版本固定为“01”
     * </pre>
     *
     * <code>string ver = 2;</code>
     */
    public Builder clearVer() {
      
      ver_ = getDefaultInstance().getVer();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 版本号，目前版本固定为“01”
     * </pre>
     *
     * <code>string ver = 2;</code>
     */
    public Builder setVerBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      ver_ = value;
      onChanged();
      return this;
    }

    private int msgCnt_ ;
    /**
     * <pre>
     * 定义消息编号。发送方对发送的同类消息(type=1)依次进行编号。编号循环发送。
     * </pre>
     *
     * <code>uint32 msgCnt = 3;</code>
     */
    public int getMsgCnt() {
      return msgCnt_;
    }
    /**
     * <pre>
     * 定义消息编号。发送方对发送的同类消息(type=1)依次进行编号。编号循环发送。
     * </pre>
     *
     * <code>uint32 msgCnt = 3;</code>
     */
    public Builder setMsgCnt(int value) {
      
      msgCnt_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 定义消息编号。发送方对发送的同类消息(type=1)依次进行编号。编号循环发送。
     * </pre>
     *
     * <code>uint32 msgCnt = 3;</code>
     */
    public Builder clearMsgCnt() {
      
      msgCnt_ = 0;
      onChanged();
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 4;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 4;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 4;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object deviceId_ = "";
    /**
     * <pre>
     * MEC的设备编号。例如，MEC的ESN编号，或者每个MEC的序列编号。只支持可见字符（ASCII码[32,126]）。
     * </pre>
     *
     * <code>string deviceId = 5;</code>
     */
    public java.lang.String getDeviceId() {
      java.lang.Object ref = deviceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * MEC的设备编号。例如，MEC的ESN编号，或者每个MEC的序列编号。只支持可见字符（ASCII码[32,126]）。
     * </pre>
     *
     * <code>string deviceId = 5;</code>
     */
    public com.google.protobuf.ByteString
        getDeviceIdBytes() {
      java.lang.Object ref = deviceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * MEC的设备编号。例如，MEC的ESN编号，或者每个MEC的序列编号。只支持可见字符（ASCII码[32,126]）。
     * </pre>
     *
     * <code>string deviceId = 5;</code>
     */
    public Builder setDeviceId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deviceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * MEC的设备编号。例如，MEC的ESN编号，或者每个MEC的序列编号。只支持可见字符（ASCII码[32,126]）。
     * </pre>
     *
     * <code>string deviceId = 5;</code>
     */
    public Builder clearDeviceId() {
      
      deviceId_ = getDefaultInstance().getDeviceId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * MEC的设备编号。例如，MEC的ESN编号，或者每个MEC的序列编号。只支持可见字符（ASCII码[32,126]）。
     * </pre>
     *
     * <code>string deviceId = 5;</code>
     */
    public Builder setDeviceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deviceId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object mapDeviceId_ = "";
    /**
     * <pre>
     *位置相关的设备编号
     * </pre>
     *
     * <code>string mapDeviceId = 6;</code>
     */
    public java.lang.String getMapDeviceId() {
      java.lang.Object ref = mapDeviceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        mapDeviceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *位置相关的设备编号
     * </pre>
     *
     * <code>string mapDeviceId = 6;</code>
     */
    public com.google.protobuf.ByteString
        getMapDeviceIdBytes() {
      java.lang.Object ref = mapDeviceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        mapDeviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *位置相关的设备编号
     * </pre>
     *
     * <code>string mapDeviceId = 6;</code>
     */
    public Builder setMapDeviceId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      mapDeviceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *位置相关的设备编号
     * </pre>
     *
     * <code>string mapDeviceId = 6;</code>
     */
    public Builder clearMapDeviceId() {
      
      mapDeviceId_ = getDefaultInstance().getMapDeviceId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *位置相关的设备编号
     * </pre>
     *
     * <code>string mapDeviceId = 6;</code>
     */
    public Builder setMapDeviceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      mapDeviceId_ = value;
      onChanged();
      return this;
    }

    private road.data.proto.Position3D refPos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> refPosBuilder_;
    /**
     * <pre>
     * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
     */
    public boolean hasRefPos() {
      return refPosBuilder_ != null || refPos_ != null;
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
     */
    public road.data.proto.Position3D getRefPos() {
      if (refPosBuilder_ == null) {
        return refPos_ == null ? road.data.proto.Position3D.getDefaultInstance() : refPos_;
      } else {
        return refPosBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
     */
    public Builder setRefPos(road.data.proto.Position3D value) {
      if (refPosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        refPos_ = value;
        onChanged();
      } else {
        refPosBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
     */
    public Builder setRefPos(
        road.data.proto.Position3D.Builder builderForValue) {
      if (refPosBuilder_ == null) {
        refPos_ = builderForValue.build();
        onChanged();
      } else {
        refPosBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
     */
    public Builder mergeRefPos(road.data.proto.Position3D value) {
      if (refPosBuilder_ == null) {
        if (refPos_ != null) {
          refPos_ =
            road.data.proto.Position3D.newBuilder(refPos_).mergeFrom(value).buildPartial();
        } else {
          refPos_ = value;
        }
        onChanged();
      } else {
        refPosBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
     */
    public Builder clearRefPos() {
      if (refPosBuilder_ == null) {
        refPos_ = null;
        onChanged();
      } else {
        refPos_ = null;
        refPosBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
     */
    public road.data.proto.Position3D.Builder getRefPosBuilder() {
      
      onChanged();
      return getRefPosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
     */
    public road.data.proto.Position3DOrBuilder getRefPosOrBuilder() {
      if (refPosBuilder_ != null) {
        return refPosBuilder_.getMessageOrBuilder();
      } else {
        return refPos_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : refPos_;
      }
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getRefPosFieldBuilder() {
      if (refPosBuilder_ == null) {
        refPosBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getRefPos(),
                getParentForChildren(),
                isClean());
        refPos_ = null;
      }
      return refPosBuilder_;
    }

    private int sceneType_ = 0;
    /**
     * <pre>
     * 可选，表示场地类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SceneType sceneType = 8;</code>
     */
    public int getSceneTypeValue() {
      return sceneType_;
    }
    /**
     * <pre>
     * 可选，表示场地类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SceneType sceneType = 8;</code>
     */
    public Builder setSceneTypeValue(int value) {
      sceneType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，表示场地类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SceneType sceneType = 8;</code>
     */
    public road.data.proto.SceneType getSceneType() {
      @SuppressWarnings("deprecation")
      road.data.proto.SceneType result = road.data.proto.SceneType.valueOf(sceneType_);
      return result == null ? road.data.proto.SceneType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，表示场地类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SceneType sceneType = 8;</code>
     */
    public Builder setSceneType(road.data.proto.SceneType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      sceneType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，表示场地类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SceneType sceneType = 8;</code>
     */
    public Builder clearSceneType() {
      
      sceneType_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.ParticipantData> ptcList_ =
      java.util.Collections.emptyList();
    private void ensurePtcListIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        ptcList_ = new java.util.ArrayList<road.data.proto.ParticipantData>(ptcList_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ParticipantData, road.data.proto.ParticipantData.Builder, road.data.proto.ParticipantDataOrBuilder> ptcListBuilder_;

    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public java.util.List<road.data.proto.ParticipantData> getPtcListList() {
      if (ptcListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(ptcList_);
      } else {
        return ptcListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public int getPtcListCount() {
      if (ptcListBuilder_ == null) {
        return ptcList_.size();
      } else {
        return ptcListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public road.data.proto.ParticipantData getPtcList(int index) {
      if (ptcListBuilder_ == null) {
        return ptcList_.get(index);
      } else {
        return ptcListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public Builder setPtcList(
        int index, road.data.proto.ParticipantData value) {
      if (ptcListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePtcListIsMutable();
        ptcList_.set(index, value);
        onChanged();
      } else {
        ptcListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public Builder setPtcList(
        int index, road.data.proto.ParticipantData.Builder builderForValue) {
      if (ptcListBuilder_ == null) {
        ensurePtcListIsMutable();
        ptcList_.set(index, builderForValue.build());
        onChanged();
      } else {
        ptcListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public Builder addPtcList(road.data.proto.ParticipantData value) {
      if (ptcListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePtcListIsMutable();
        ptcList_.add(value);
        onChanged();
      } else {
        ptcListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public Builder addPtcList(
        int index, road.data.proto.ParticipantData value) {
      if (ptcListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePtcListIsMutable();
        ptcList_.add(index, value);
        onChanged();
      } else {
        ptcListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public Builder addPtcList(
        road.data.proto.ParticipantData.Builder builderForValue) {
      if (ptcListBuilder_ == null) {
        ensurePtcListIsMutable();
        ptcList_.add(builderForValue.build());
        onChanged();
      } else {
        ptcListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public Builder addPtcList(
        int index, road.data.proto.ParticipantData.Builder builderForValue) {
      if (ptcListBuilder_ == null) {
        ensurePtcListIsMutable();
        ptcList_.add(index, builderForValue.build());
        onChanged();
      } else {
        ptcListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public Builder addAllPtcList(
        java.lang.Iterable<? extends road.data.proto.ParticipantData> values) {
      if (ptcListBuilder_ == null) {
        ensurePtcListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, ptcList_);
        onChanged();
      } else {
        ptcListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public Builder clearPtcList() {
      if (ptcListBuilder_ == null) {
        ptcList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        ptcListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public Builder removePtcList(int index) {
      if (ptcListBuilder_ == null) {
        ensurePtcListIsMutable();
        ptcList_.remove(index);
        onChanged();
      } else {
        ptcListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public road.data.proto.ParticipantData.Builder getPtcListBuilder(
        int index) {
      return getPtcListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public road.data.proto.ParticipantDataOrBuilder getPtcListOrBuilder(
        int index) {
      if (ptcListBuilder_ == null) {
        return ptcList_.get(index);  } else {
        return ptcListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public java.util.List<? extends road.data.proto.ParticipantDataOrBuilder> 
         getPtcListOrBuilderList() {
      if (ptcListBuilder_ != null) {
        return ptcListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(ptcList_);
      }
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public road.data.proto.ParticipantData.Builder addPtcListBuilder() {
      return getPtcListFieldBuilder().addBuilder(
          road.data.proto.ParticipantData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public road.data.proto.ParticipantData.Builder addPtcListBuilder(
        int index) {
      return getPtcListFieldBuilder().addBuilder(
          index, road.data.proto.ParticipantData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，定义目标物列表，属于RSM中的交通参与者
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
     */
    public java.util.List<road.data.proto.ParticipantData.Builder> 
         getPtcListBuilderList() {
      return getPtcListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ParticipantData, road.data.proto.ParticipantData.Builder, road.data.proto.ParticipantDataOrBuilder> 
        getPtcListFieldBuilder() {
      if (ptcListBuilder_ == null) {
        ptcListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.ParticipantData, road.data.proto.ParticipantData.Builder, road.data.proto.ParticipantDataOrBuilder>(
                ptcList_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        ptcList_ = null;
      }
      return ptcListBuilder_;
    }

    private java.util.List<road.data.proto.ObstacleData> obstacleList_ =
      java.util.Collections.emptyList();
    private void ensureObstacleListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        obstacleList_ = new java.util.ArrayList<road.data.proto.ObstacleData>(obstacleList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ObstacleData, road.data.proto.ObstacleData.Builder, road.data.proto.ObstacleDataOrBuilder> obstacleListBuilder_;

    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public java.util.List<road.data.proto.ObstacleData> getObstacleListList() {
      if (obstacleListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(obstacleList_);
      } else {
        return obstacleListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public int getObstacleListCount() {
      if (obstacleListBuilder_ == null) {
        return obstacleList_.size();
      } else {
        return obstacleListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public road.data.proto.ObstacleData getObstacleList(int index) {
      if (obstacleListBuilder_ == null) {
        return obstacleList_.get(index);
      } else {
        return obstacleListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public Builder setObstacleList(
        int index, road.data.proto.ObstacleData value) {
      if (obstacleListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureObstacleListIsMutable();
        obstacleList_.set(index, value);
        onChanged();
      } else {
        obstacleListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public Builder setObstacleList(
        int index, road.data.proto.ObstacleData.Builder builderForValue) {
      if (obstacleListBuilder_ == null) {
        ensureObstacleListIsMutable();
        obstacleList_.set(index, builderForValue.build());
        onChanged();
      } else {
        obstacleListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public Builder addObstacleList(road.data.proto.ObstacleData value) {
      if (obstacleListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureObstacleListIsMutable();
        obstacleList_.add(value);
        onChanged();
      } else {
        obstacleListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public Builder addObstacleList(
        int index, road.data.proto.ObstacleData value) {
      if (obstacleListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureObstacleListIsMutable();
        obstacleList_.add(index, value);
        onChanged();
      } else {
        obstacleListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public Builder addObstacleList(
        road.data.proto.ObstacleData.Builder builderForValue) {
      if (obstacleListBuilder_ == null) {
        ensureObstacleListIsMutable();
        obstacleList_.add(builderForValue.build());
        onChanged();
      } else {
        obstacleListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public Builder addObstacleList(
        int index, road.data.proto.ObstacleData.Builder builderForValue) {
      if (obstacleListBuilder_ == null) {
        ensureObstacleListIsMutable();
        obstacleList_.add(index, builderForValue.build());
        onChanged();
      } else {
        obstacleListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public Builder addAllObstacleList(
        java.lang.Iterable<? extends road.data.proto.ObstacleData> values) {
      if (obstacleListBuilder_ == null) {
        ensureObstacleListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, obstacleList_);
        onChanged();
      } else {
        obstacleListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public Builder clearObstacleList() {
      if (obstacleListBuilder_ == null) {
        obstacleList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        obstacleListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public Builder removeObstacleList(int index) {
      if (obstacleListBuilder_ == null) {
        ensureObstacleListIsMutable();
        obstacleList_.remove(index);
        onChanged();
      } else {
        obstacleListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public road.data.proto.ObstacleData.Builder getObstacleListBuilder(
        int index) {
      return getObstacleListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public road.data.proto.ObstacleDataOrBuilder getObstacleListOrBuilder(
        int index) {
      if (obstacleListBuilder_ == null) {
        return obstacleList_.get(index);  } else {
        return obstacleListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public java.util.List<? extends road.data.proto.ObstacleDataOrBuilder> 
         getObstacleListOrBuilderList() {
      if (obstacleListBuilder_ != null) {
        return obstacleListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(obstacleList_);
      }
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public road.data.proto.ObstacleData.Builder addObstacleListBuilder() {
      return getObstacleListFieldBuilder().addBuilder(
          road.data.proto.ObstacleData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public road.data.proto.ObstacleData.Builder addObstacleListBuilder(
        int index) {
      return getObstacleListFieldBuilder().addBuilder(
          index, road.data.proto.ObstacleData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，定义障碍物列表，属于RSM中的障碍物
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
     */
    public java.util.List<road.data.proto.ObstacleData.Builder> 
         getObstacleListBuilderList() {
      return getObstacleListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ObstacleData, road.data.proto.ObstacleData.Builder, road.data.proto.ObstacleDataOrBuilder> 
        getObstacleListFieldBuilder() {
      if (obstacleListBuilder_ == null) {
        obstacleListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.ObstacleData, road.data.proto.ObstacleData.Builder, road.data.proto.ObstacleDataOrBuilder>(
                obstacleList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        obstacleList_ = null;
      }
      return obstacleListBuilder_;
    }

    private java.util.List<road.data.proto.RteData> rteList_ =
      java.util.Collections.emptyList();
    private void ensureRteListIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        rteList_ = new java.util.ArrayList<road.data.proto.RteData>(rteList_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RteData, road.data.proto.RteData.Builder, road.data.proto.RteDataOrBuilder> rteListBuilder_;

    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public java.util.List<road.data.proto.RteData> getRteListList() {
      if (rteListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rteList_);
      } else {
        return rteListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public int getRteListCount() {
      if (rteListBuilder_ == null) {
        return rteList_.size();
      } else {
        return rteListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public road.data.proto.RteData getRteList(int index) {
      if (rteListBuilder_ == null) {
        return rteList_.get(index);
      } else {
        return rteListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public Builder setRteList(
        int index, road.data.proto.RteData value) {
      if (rteListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRteListIsMutable();
        rteList_.set(index, value);
        onChanged();
      } else {
        rteListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public Builder setRteList(
        int index, road.data.proto.RteData.Builder builderForValue) {
      if (rteListBuilder_ == null) {
        ensureRteListIsMutable();
        rteList_.set(index, builderForValue.build());
        onChanged();
      } else {
        rteListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public Builder addRteList(road.data.proto.RteData value) {
      if (rteListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRteListIsMutable();
        rteList_.add(value);
        onChanged();
      } else {
        rteListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public Builder addRteList(
        int index, road.data.proto.RteData value) {
      if (rteListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRteListIsMutable();
        rteList_.add(index, value);
        onChanged();
      } else {
        rteListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public Builder addRteList(
        road.data.proto.RteData.Builder builderForValue) {
      if (rteListBuilder_ == null) {
        ensureRteListIsMutable();
        rteList_.add(builderForValue.build());
        onChanged();
      } else {
        rteListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public Builder addRteList(
        int index, road.data.proto.RteData.Builder builderForValue) {
      if (rteListBuilder_ == null) {
        ensureRteListIsMutable();
        rteList_.add(index, builderForValue.build());
        onChanged();
      } else {
        rteListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public Builder addAllRteList(
        java.lang.Iterable<? extends road.data.proto.RteData> values) {
      if (rteListBuilder_ == null) {
        ensureRteListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rteList_);
        onChanged();
      } else {
        rteListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public Builder clearRteList() {
      if (rteListBuilder_ == null) {
        rteList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        rteListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public Builder removeRteList(int index) {
      if (rteListBuilder_ == null) {
        ensureRteListIsMutable();
        rteList_.remove(index);
        onChanged();
      } else {
        rteListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public road.data.proto.RteData.Builder getRteListBuilder(
        int index) {
      return getRteListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public road.data.proto.RteDataOrBuilder getRteListOrBuilder(
        int index) {
      if (rteListBuilder_ == null) {
        return rteList_.get(index);  } else {
        return rteListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public java.util.List<? extends road.data.proto.RteDataOrBuilder> 
         getRteListOrBuilderList() {
      if (rteListBuilder_ != null) {
        return rteListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rteList_);
      }
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public road.data.proto.RteData.Builder addRteListBuilder() {
      return getRteListFieldBuilder().addBuilder(
          road.data.proto.RteData.getDefaultInstance());
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public road.data.proto.RteData.Builder addRteListBuilder(
        int index) {
      return getRteListFieldBuilder().addBuilder(
          index, road.data.proto.RteData.getDefaultInstance());
    }
    /**
     * <pre>
     *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
     */
    public java.util.List<road.data.proto.RteData.Builder> 
         getRteListBuilderList() {
      return getRteListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RteData, road.data.proto.RteData.Builder, road.data.proto.RteDataOrBuilder> 
        getRteListFieldBuilder() {
      if (rteListBuilder_ == null) {
        rteListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.RteData, road.data.proto.RteData.Builder, road.data.proto.RteDataOrBuilder>(
                rteList_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        rteList_ = null;
      }
      return rteListBuilder_;
    }

    private java.util.List<road.data.proto.RtsData> rtsList_ =
      java.util.Collections.emptyList();
    private void ensureRtsListIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        rtsList_ = new java.util.ArrayList<road.data.proto.RtsData>(rtsList_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RtsData, road.data.proto.RtsData.Builder, road.data.proto.RtsDataOrBuilder> rtsListBuilder_;

    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public java.util.List<road.data.proto.RtsData> getRtsListList() {
      if (rtsListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rtsList_);
      } else {
        return rtsListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public int getRtsListCount() {
      if (rtsListBuilder_ == null) {
        return rtsList_.size();
      } else {
        return rtsListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public road.data.proto.RtsData getRtsList(int index) {
      if (rtsListBuilder_ == null) {
        return rtsList_.get(index);
      } else {
        return rtsListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public Builder setRtsList(
        int index, road.data.proto.RtsData value) {
      if (rtsListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRtsListIsMutable();
        rtsList_.set(index, value);
        onChanged();
      } else {
        rtsListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public Builder setRtsList(
        int index, road.data.proto.RtsData.Builder builderForValue) {
      if (rtsListBuilder_ == null) {
        ensureRtsListIsMutable();
        rtsList_.set(index, builderForValue.build());
        onChanged();
      } else {
        rtsListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public Builder addRtsList(road.data.proto.RtsData value) {
      if (rtsListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRtsListIsMutable();
        rtsList_.add(value);
        onChanged();
      } else {
        rtsListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public Builder addRtsList(
        int index, road.data.proto.RtsData value) {
      if (rtsListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRtsListIsMutable();
        rtsList_.add(index, value);
        onChanged();
      } else {
        rtsListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public Builder addRtsList(
        road.data.proto.RtsData.Builder builderForValue) {
      if (rtsListBuilder_ == null) {
        ensureRtsListIsMutable();
        rtsList_.add(builderForValue.build());
        onChanged();
      } else {
        rtsListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public Builder addRtsList(
        int index, road.data.proto.RtsData.Builder builderForValue) {
      if (rtsListBuilder_ == null) {
        ensureRtsListIsMutable();
        rtsList_.add(index, builderForValue.build());
        onChanged();
      } else {
        rtsListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public Builder addAllRtsList(
        java.lang.Iterable<? extends road.data.proto.RtsData> values) {
      if (rtsListBuilder_ == null) {
        ensureRtsListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rtsList_);
        onChanged();
      } else {
        rtsListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public Builder clearRtsList() {
      if (rtsListBuilder_ == null) {
        rtsList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        rtsListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public Builder removeRtsList(int index) {
      if (rtsListBuilder_ == null) {
        ensureRtsListIsMutable();
        rtsList_.remove(index);
        onChanged();
      } else {
        rtsListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public road.data.proto.RtsData.Builder getRtsListBuilder(
        int index) {
      return getRtsListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public road.data.proto.RtsDataOrBuilder getRtsListOrBuilder(
        int index) {
      if (rtsListBuilder_ == null) {
        return rtsList_.get(index);  } else {
        return rtsListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public java.util.List<? extends road.data.proto.RtsDataOrBuilder> 
         getRtsListOrBuilderList() {
      if (rtsListBuilder_ != null) {
        return rtsListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rtsList_);
      }
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public road.data.proto.RtsData.Builder addRtsListBuilder() {
      return getRtsListFieldBuilder().addBuilder(
          road.data.proto.RtsData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public road.data.proto.RtsData.Builder addRtsListBuilder(
        int index) {
      return getRtsListFieldBuilder().addBuilder(
          index, road.data.proto.RtsData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，交通标志信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
     */
    public java.util.List<road.data.proto.RtsData.Builder> 
         getRtsListBuilderList() {
      return getRtsListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RtsData, road.data.proto.RtsData.Builder, road.data.proto.RtsDataOrBuilder> 
        getRtsListFieldBuilder() {
      if (rtsListBuilder_ == null) {
        rtsListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.RtsData, road.data.proto.RtsData.Builder, road.data.proto.RtsDataOrBuilder>(
                rtsList_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        rtsList_ = null;
      }
      return rtsListBuilder_;
    }

    private java.util.List<road.data.proto.BsmData> bsmList_ =
      java.util.Collections.emptyList();
    private void ensureBsmListIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        bsmList_ = new java.util.ArrayList<road.data.proto.BsmData>(bsmList_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.BsmData, road.data.proto.BsmData.Builder, road.data.proto.BsmDataOrBuilder> bsmListBuilder_;

    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public java.util.List<road.data.proto.BsmData> getBsmListList() {
      if (bsmListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(bsmList_);
      } else {
        return bsmListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public int getBsmListCount() {
      if (bsmListBuilder_ == null) {
        return bsmList_.size();
      } else {
        return bsmListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public road.data.proto.BsmData getBsmList(int index) {
      if (bsmListBuilder_ == null) {
        return bsmList_.get(index);
      } else {
        return bsmListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public Builder setBsmList(
        int index, road.data.proto.BsmData value) {
      if (bsmListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBsmListIsMutable();
        bsmList_.set(index, value);
        onChanged();
      } else {
        bsmListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public Builder setBsmList(
        int index, road.data.proto.BsmData.Builder builderForValue) {
      if (bsmListBuilder_ == null) {
        ensureBsmListIsMutable();
        bsmList_.set(index, builderForValue.build());
        onChanged();
      } else {
        bsmListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public Builder addBsmList(road.data.proto.BsmData value) {
      if (bsmListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBsmListIsMutable();
        bsmList_.add(value);
        onChanged();
      } else {
        bsmListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public Builder addBsmList(
        int index, road.data.proto.BsmData value) {
      if (bsmListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBsmListIsMutable();
        bsmList_.add(index, value);
        onChanged();
      } else {
        bsmListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public Builder addBsmList(
        road.data.proto.BsmData.Builder builderForValue) {
      if (bsmListBuilder_ == null) {
        ensureBsmListIsMutable();
        bsmList_.add(builderForValue.build());
        onChanged();
      } else {
        bsmListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public Builder addBsmList(
        int index, road.data.proto.BsmData.Builder builderForValue) {
      if (bsmListBuilder_ == null) {
        ensureBsmListIsMutable();
        bsmList_.add(index, builderForValue.build());
        onChanged();
      } else {
        bsmListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public Builder addAllBsmList(
        java.lang.Iterable<? extends road.data.proto.BsmData> values) {
      if (bsmListBuilder_ == null) {
        ensureBsmListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, bsmList_);
        onChanged();
      } else {
        bsmListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public Builder clearBsmList() {
      if (bsmListBuilder_ == null) {
        bsmList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        bsmListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public Builder removeBsmList(int index) {
      if (bsmListBuilder_ == null) {
        ensureBsmListIsMutable();
        bsmList_.remove(index);
        onChanged();
      } else {
        bsmListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public road.data.proto.BsmData.Builder getBsmListBuilder(
        int index) {
      return getBsmListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public road.data.proto.BsmDataOrBuilder getBsmListOrBuilder(
        int index) {
      if (bsmListBuilder_ == null) {
        return bsmList_.get(index);  } else {
        return bsmListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public java.util.List<? extends road.data.proto.BsmDataOrBuilder> 
         getBsmListOrBuilderList() {
      if (bsmListBuilder_ != null) {
        return bsmListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(bsmList_);
      }
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public road.data.proto.BsmData.Builder addBsmListBuilder() {
      return getBsmListFieldBuilder().addBuilder(
          road.data.proto.BsmData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public road.data.proto.BsmData.Builder addBsmListBuilder(
        int index) {
      return getBsmListFieldBuilder().addBuilder(
          index, road.data.proto.BsmData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，基本安全消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
     */
    public java.util.List<road.data.proto.BsmData.Builder> 
         getBsmListBuilderList() {
      return getBsmListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.BsmData, road.data.proto.BsmData.Builder, road.data.proto.BsmDataOrBuilder> 
        getBsmListFieldBuilder() {
      if (bsmListBuilder_ == null) {
        bsmListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.BsmData, road.data.proto.BsmData.Builder, road.data.proto.BsmDataOrBuilder>(
                bsmList_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        bsmList_ = null;
      }
      return bsmListBuilder_;
    }

    private java.util.List<road.data.proto.VirData> virList_ =
      java.util.Collections.emptyList();
    private void ensureVirListIsMutable() {
      if (!((bitField0_ & 0x00000020) != 0)) {
        virList_ = new java.util.ArrayList<road.data.proto.VirData>(virList_);
        bitField0_ |= 0x00000020;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.VirData, road.data.proto.VirData.Builder, road.data.proto.VirDataOrBuilder> virListBuilder_;

    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public java.util.List<road.data.proto.VirData> getVirListList() {
      if (virListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(virList_);
      } else {
        return virListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public int getVirListCount() {
      if (virListBuilder_ == null) {
        return virList_.size();
      } else {
        return virListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public road.data.proto.VirData getVirList(int index) {
      if (virListBuilder_ == null) {
        return virList_.get(index);
      } else {
        return virListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public Builder setVirList(
        int index, road.data.proto.VirData value) {
      if (virListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureVirListIsMutable();
        virList_.set(index, value);
        onChanged();
      } else {
        virListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public Builder setVirList(
        int index, road.data.proto.VirData.Builder builderForValue) {
      if (virListBuilder_ == null) {
        ensureVirListIsMutable();
        virList_.set(index, builderForValue.build());
        onChanged();
      } else {
        virListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public Builder addVirList(road.data.proto.VirData value) {
      if (virListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureVirListIsMutable();
        virList_.add(value);
        onChanged();
      } else {
        virListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public Builder addVirList(
        int index, road.data.proto.VirData value) {
      if (virListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureVirListIsMutable();
        virList_.add(index, value);
        onChanged();
      } else {
        virListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public Builder addVirList(
        road.data.proto.VirData.Builder builderForValue) {
      if (virListBuilder_ == null) {
        ensureVirListIsMutable();
        virList_.add(builderForValue.build());
        onChanged();
      } else {
        virListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public Builder addVirList(
        int index, road.data.proto.VirData.Builder builderForValue) {
      if (virListBuilder_ == null) {
        ensureVirListIsMutable();
        virList_.add(index, builderForValue.build());
        onChanged();
      } else {
        virListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public Builder addAllVirList(
        java.lang.Iterable<? extends road.data.proto.VirData> values) {
      if (virListBuilder_ == null) {
        ensureVirListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, virList_);
        onChanged();
      } else {
        virListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public Builder clearVirList() {
      if (virListBuilder_ == null) {
        virList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
      } else {
        virListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public Builder removeVirList(int index) {
      if (virListBuilder_ == null) {
        ensureVirListIsMutable();
        virList_.remove(index);
        onChanged();
      } else {
        virListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public road.data.proto.VirData.Builder getVirListBuilder(
        int index) {
      return getVirListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public road.data.proto.VirDataOrBuilder getVirListOrBuilder(
        int index) {
      if (virListBuilder_ == null) {
        return virList_.get(index);  } else {
        return virListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public java.util.List<? extends road.data.proto.VirDataOrBuilder> 
         getVirListOrBuilderList() {
      if (virListBuilder_ != null) {
        return virListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(virList_);
      }
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public road.data.proto.VirData.Builder addVirListBuilder() {
      return getVirListFieldBuilder().addBuilder(
          road.data.proto.VirData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public road.data.proto.VirData.Builder addVirListBuilder(
        int index) {
      return getVirListFieldBuilder().addBuilder(
          index, road.data.proto.VirData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，车辆意图及请求消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
     */
    public java.util.List<road.data.proto.VirData.Builder> 
         getVirListBuilderList() {
      return getVirListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.VirData, road.data.proto.VirData.Builder, road.data.proto.VirDataOrBuilder> 
        getVirListFieldBuilder() {
      if (virListBuilder_ == null) {
        virListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.VirData, road.data.proto.VirData.Builder, road.data.proto.VirDataOrBuilder>(
                virList_,
                ((bitField0_ & 0x00000020) != 0),
                getParentForChildren(),
                isClean());
        virList_ = null;
      }
      return virListBuilder_;
    }

    private java.util.List<road.data.proto.RscData> rscList_ =
      java.util.Collections.emptyList();
    private void ensureRscListIsMutable() {
      if (!((bitField0_ & 0x00000040) != 0)) {
        rscList_ = new java.util.ArrayList<road.data.proto.RscData>(rscList_);
        bitField0_ |= 0x00000040;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RscData, road.data.proto.RscData.Builder, road.data.proto.RscDataOrBuilder> rscListBuilder_;

    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public java.util.List<road.data.proto.RscData> getRscListList() {
      if (rscListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rscList_);
      } else {
        return rscListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public int getRscListCount() {
      if (rscListBuilder_ == null) {
        return rscList_.size();
      } else {
        return rscListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public road.data.proto.RscData getRscList(int index) {
      if (rscListBuilder_ == null) {
        return rscList_.get(index);
      } else {
        return rscListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public Builder setRscList(
        int index, road.data.proto.RscData value) {
      if (rscListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRscListIsMutable();
        rscList_.set(index, value);
        onChanged();
      } else {
        rscListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public Builder setRscList(
        int index, road.data.proto.RscData.Builder builderForValue) {
      if (rscListBuilder_ == null) {
        ensureRscListIsMutable();
        rscList_.set(index, builderForValue.build());
        onChanged();
      } else {
        rscListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public Builder addRscList(road.data.proto.RscData value) {
      if (rscListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRscListIsMutable();
        rscList_.add(value);
        onChanged();
      } else {
        rscListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public Builder addRscList(
        int index, road.data.proto.RscData value) {
      if (rscListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRscListIsMutable();
        rscList_.add(index, value);
        onChanged();
      } else {
        rscListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public Builder addRscList(
        road.data.proto.RscData.Builder builderForValue) {
      if (rscListBuilder_ == null) {
        ensureRscListIsMutable();
        rscList_.add(builderForValue.build());
        onChanged();
      } else {
        rscListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public Builder addRscList(
        int index, road.data.proto.RscData.Builder builderForValue) {
      if (rscListBuilder_ == null) {
        ensureRscListIsMutable();
        rscList_.add(index, builderForValue.build());
        onChanged();
      } else {
        rscListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public Builder addAllRscList(
        java.lang.Iterable<? extends road.data.proto.RscData> values) {
      if (rscListBuilder_ == null) {
        ensureRscListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rscList_);
        onChanged();
      } else {
        rscListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public Builder clearRscList() {
      if (rscListBuilder_ == null) {
        rscList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
      } else {
        rscListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public Builder removeRscList(int index) {
      if (rscListBuilder_ == null) {
        ensureRscListIsMutable();
        rscList_.remove(index);
        onChanged();
      } else {
        rscListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public road.data.proto.RscData.Builder getRscListBuilder(
        int index) {
      return getRscListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public road.data.proto.RscDataOrBuilder getRscListOrBuilder(
        int index) {
      if (rscListBuilder_ == null) {
        return rscList_.get(index);  } else {
        return rscListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public java.util.List<? extends road.data.proto.RscDataOrBuilder> 
         getRscListOrBuilderList() {
      if (rscListBuilder_ != null) {
        return rscListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rscList_);
      }
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public road.data.proto.RscData.Builder addRscListBuilder() {
      return getRscListFieldBuilder().addBuilder(
          road.data.proto.RscData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public road.data.proto.RscData.Builder addRscListBuilder(
        int index) {
      return getRscListFieldBuilder().addBuilder(
          index, road.data.proto.RscData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，车辆协作或引导消息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
     */
    public java.util.List<road.data.proto.RscData.Builder> 
         getRscListBuilderList() {
      return getRscListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RscData, road.data.proto.RscData.Builder, road.data.proto.RscDataOrBuilder> 
        getRscListFieldBuilder() {
      if (rscListBuilder_ == null) {
        rscListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.RscData, road.data.proto.RscData.Builder, road.data.proto.RscDataOrBuilder>(
                rscList_,
                ((bitField0_ & 0x00000040) != 0),
                getParentForChildren(),
                isClean());
        rscList_ = null;
      }
      return rscListBuilder_;
    }

    private road.data.proto.SpatData roadSignalState_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.SpatData, road.data.proto.SpatData.Builder, road.data.proto.SpatDataOrBuilder> roadSignalStateBuilder_;
    /**
     * <pre>
     * 可选，实时交通相位SPAT信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
     */
    public boolean hasRoadSignalState() {
      return roadSignalStateBuilder_ != null || roadSignalState_ != null;
    }
    /**
     * <pre>
     * 可选，实时交通相位SPAT信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
     */
    public road.data.proto.SpatData getRoadSignalState() {
      if (roadSignalStateBuilder_ == null) {
        return roadSignalState_ == null ? road.data.proto.SpatData.getDefaultInstance() : roadSignalState_;
      } else {
        return roadSignalStateBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，实时交通相位SPAT信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
     */
    public Builder setRoadSignalState(road.data.proto.SpatData value) {
      if (roadSignalStateBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        roadSignalState_ = value;
        onChanged();
      } else {
        roadSignalStateBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，实时交通相位SPAT信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
     */
    public Builder setRoadSignalState(
        road.data.proto.SpatData.Builder builderForValue) {
      if (roadSignalStateBuilder_ == null) {
        roadSignalState_ = builderForValue.build();
        onChanged();
      } else {
        roadSignalStateBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，实时交通相位SPAT信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
     */
    public Builder mergeRoadSignalState(road.data.proto.SpatData value) {
      if (roadSignalStateBuilder_ == null) {
        if (roadSignalState_ != null) {
          roadSignalState_ =
            road.data.proto.SpatData.newBuilder(roadSignalState_).mergeFrom(value).buildPartial();
        } else {
          roadSignalState_ = value;
        }
        onChanged();
      } else {
        roadSignalStateBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，实时交通相位SPAT信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
     */
    public Builder clearRoadSignalState() {
      if (roadSignalStateBuilder_ == null) {
        roadSignalState_ = null;
        onChanged();
      } else {
        roadSignalState_ = null;
        roadSignalStateBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，实时交通相位SPAT信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
     */
    public road.data.proto.SpatData.Builder getRoadSignalStateBuilder() {
      
      onChanged();
      return getRoadSignalStateFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，实时交通相位SPAT信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
     */
    public road.data.proto.SpatDataOrBuilder getRoadSignalStateOrBuilder() {
      if (roadSignalStateBuilder_ != null) {
        return roadSignalStateBuilder_.getMessageOrBuilder();
      } else {
        return roadSignalState_ == null ?
            road.data.proto.SpatData.getDefaultInstance() : roadSignalState_;
      }
    }
    /**
     * <pre>
     * 可选，实时交通相位SPAT信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.SpatData, road.data.proto.SpatData.Builder, road.data.proto.SpatDataOrBuilder> 
        getRoadSignalStateFieldBuilder() {
      if (roadSignalStateBuilder_ == null) {
        roadSignalStateBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.SpatData, road.data.proto.SpatData.Builder, road.data.proto.SpatDataOrBuilder>(
                getRoadSignalState(),
                getParentForChildren(),
                isClean());
        roadSignalState_ = null;
      }
      return roadSignalStateBuilder_;
    }

    private java.util.List<road.data.proto.TrafficFlow> trafficFlow_ =
      java.util.Collections.emptyList();
    private void ensureTrafficFlowIsMutable() {
      if (!((bitField0_ & 0x00000080) != 0)) {
        trafficFlow_ = new java.util.ArrayList<road.data.proto.TrafficFlow>(trafficFlow_);
        bitField0_ |= 0x00000080;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.TrafficFlow, road.data.proto.TrafficFlow.Builder, road.data.proto.TrafficFlowOrBuilder> trafficFlowBuilder_;

    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public java.util.List<road.data.proto.TrafficFlow> getTrafficFlowList() {
      if (trafficFlowBuilder_ == null) {
        return java.util.Collections.unmodifiableList(trafficFlow_);
      } else {
        return trafficFlowBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public int getTrafficFlowCount() {
      if (trafficFlowBuilder_ == null) {
        return trafficFlow_.size();
      } else {
        return trafficFlowBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public road.data.proto.TrafficFlow getTrafficFlow(int index) {
      if (trafficFlowBuilder_ == null) {
        return trafficFlow_.get(index);
      } else {
        return trafficFlowBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public Builder setTrafficFlow(
        int index, road.data.proto.TrafficFlow value) {
      if (trafficFlowBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTrafficFlowIsMutable();
        trafficFlow_.set(index, value);
        onChanged();
      } else {
        trafficFlowBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public Builder setTrafficFlow(
        int index, road.data.proto.TrafficFlow.Builder builderForValue) {
      if (trafficFlowBuilder_ == null) {
        ensureTrafficFlowIsMutable();
        trafficFlow_.set(index, builderForValue.build());
        onChanged();
      } else {
        trafficFlowBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public Builder addTrafficFlow(road.data.proto.TrafficFlow value) {
      if (trafficFlowBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTrafficFlowIsMutable();
        trafficFlow_.add(value);
        onChanged();
      } else {
        trafficFlowBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public Builder addTrafficFlow(
        int index, road.data.proto.TrafficFlow value) {
      if (trafficFlowBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTrafficFlowIsMutable();
        trafficFlow_.add(index, value);
        onChanged();
      } else {
        trafficFlowBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public Builder addTrafficFlow(
        road.data.proto.TrafficFlow.Builder builderForValue) {
      if (trafficFlowBuilder_ == null) {
        ensureTrafficFlowIsMutable();
        trafficFlow_.add(builderForValue.build());
        onChanged();
      } else {
        trafficFlowBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public Builder addTrafficFlow(
        int index, road.data.proto.TrafficFlow.Builder builderForValue) {
      if (trafficFlowBuilder_ == null) {
        ensureTrafficFlowIsMutable();
        trafficFlow_.add(index, builderForValue.build());
        onChanged();
      } else {
        trafficFlowBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public Builder addAllTrafficFlow(
        java.lang.Iterable<? extends road.data.proto.TrafficFlow> values) {
      if (trafficFlowBuilder_ == null) {
        ensureTrafficFlowIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, trafficFlow_);
        onChanged();
      } else {
        trafficFlowBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public Builder clearTrafficFlow() {
      if (trafficFlowBuilder_ == null) {
        trafficFlow_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
      } else {
        trafficFlowBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public Builder removeTrafficFlow(int index) {
      if (trafficFlowBuilder_ == null) {
        ensureTrafficFlowIsMutable();
        trafficFlow_.remove(index);
        onChanged();
      } else {
        trafficFlowBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public road.data.proto.TrafficFlow.Builder getTrafficFlowBuilder(
        int index) {
      return getTrafficFlowFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public road.data.proto.TrafficFlowOrBuilder getTrafficFlowOrBuilder(
        int index) {
      if (trafficFlowBuilder_ == null) {
        return trafficFlow_.get(index);  } else {
        return trafficFlowBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public java.util.List<? extends road.data.proto.TrafficFlowOrBuilder> 
         getTrafficFlowOrBuilderList() {
      if (trafficFlowBuilder_ != null) {
        return trafficFlowBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(trafficFlow_);
      }
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public road.data.proto.TrafficFlow.Builder addTrafficFlowBuilder() {
      return getTrafficFlowFieldBuilder().addBuilder(
          road.data.proto.TrafficFlow.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public road.data.proto.TrafficFlow.Builder addTrafficFlowBuilder(
        int index) {
      return getTrafficFlowFieldBuilder().addBuilder(
          index, road.data.proto.TrafficFlow.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，实时交通流信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
     */
    public java.util.List<road.data.proto.TrafficFlow.Builder> 
         getTrafficFlowBuilderList() {
      return getTrafficFlowFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.TrafficFlow, road.data.proto.TrafficFlow.Builder, road.data.proto.TrafficFlowOrBuilder> 
        getTrafficFlowFieldBuilder() {
      if (trafficFlowBuilder_ == null) {
        trafficFlowBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.TrafficFlow, road.data.proto.TrafficFlow.Builder, road.data.proto.TrafficFlowOrBuilder>(
                trafficFlow_,
                ((bitField0_ & 0x00000080) != 0),
                getParentForChildren(),
                isClean());
        trafficFlow_ = null;
      }
      return trafficFlowBuilder_;
    }

    private java.util.List<road.data.proto.SignalScheme> signalSchemeList_ =
      java.util.Collections.emptyList();
    private void ensureSignalSchemeListIsMutable() {
      if (!((bitField0_ & 0x00000100) != 0)) {
        signalSchemeList_ = new java.util.ArrayList<road.data.proto.SignalScheme>(signalSchemeList_);
        bitField0_ |= 0x00000100;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.SignalScheme, road.data.proto.SignalScheme.Builder, road.data.proto.SignalSchemeOrBuilder> signalSchemeListBuilder_;

    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public java.util.List<road.data.proto.SignalScheme> getSignalSchemeListList() {
      if (signalSchemeListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(signalSchemeList_);
      } else {
        return signalSchemeListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public int getSignalSchemeListCount() {
      if (signalSchemeListBuilder_ == null) {
        return signalSchemeList_.size();
      } else {
        return signalSchemeListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public road.data.proto.SignalScheme getSignalSchemeList(int index) {
      if (signalSchemeListBuilder_ == null) {
        return signalSchemeList_.get(index);
      } else {
        return signalSchemeListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public Builder setSignalSchemeList(
        int index, road.data.proto.SignalScheme value) {
      if (signalSchemeListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSignalSchemeListIsMutable();
        signalSchemeList_.set(index, value);
        onChanged();
      } else {
        signalSchemeListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public Builder setSignalSchemeList(
        int index, road.data.proto.SignalScheme.Builder builderForValue) {
      if (signalSchemeListBuilder_ == null) {
        ensureSignalSchemeListIsMutable();
        signalSchemeList_.set(index, builderForValue.build());
        onChanged();
      } else {
        signalSchemeListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public Builder addSignalSchemeList(road.data.proto.SignalScheme value) {
      if (signalSchemeListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSignalSchemeListIsMutable();
        signalSchemeList_.add(value);
        onChanged();
      } else {
        signalSchemeListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public Builder addSignalSchemeList(
        int index, road.data.proto.SignalScheme value) {
      if (signalSchemeListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSignalSchemeListIsMutable();
        signalSchemeList_.add(index, value);
        onChanged();
      } else {
        signalSchemeListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public Builder addSignalSchemeList(
        road.data.proto.SignalScheme.Builder builderForValue) {
      if (signalSchemeListBuilder_ == null) {
        ensureSignalSchemeListIsMutable();
        signalSchemeList_.add(builderForValue.build());
        onChanged();
      } else {
        signalSchemeListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public Builder addSignalSchemeList(
        int index, road.data.proto.SignalScheme.Builder builderForValue) {
      if (signalSchemeListBuilder_ == null) {
        ensureSignalSchemeListIsMutable();
        signalSchemeList_.add(index, builderForValue.build());
        onChanged();
      } else {
        signalSchemeListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public Builder addAllSignalSchemeList(
        java.lang.Iterable<? extends road.data.proto.SignalScheme> values) {
      if (signalSchemeListBuilder_ == null) {
        ensureSignalSchemeListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, signalSchemeList_);
        onChanged();
      } else {
        signalSchemeListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public Builder clearSignalSchemeList() {
      if (signalSchemeListBuilder_ == null) {
        signalSchemeList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
      } else {
        signalSchemeListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public Builder removeSignalSchemeList(int index) {
      if (signalSchemeListBuilder_ == null) {
        ensureSignalSchemeListIsMutable();
        signalSchemeList_.remove(index);
        onChanged();
      } else {
        signalSchemeListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public road.data.proto.SignalScheme.Builder getSignalSchemeListBuilder(
        int index) {
      return getSignalSchemeListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public road.data.proto.SignalSchemeOrBuilder getSignalSchemeListOrBuilder(
        int index) {
      if (signalSchemeListBuilder_ == null) {
        return signalSchemeList_.get(index);  } else {
        return signalSchemeListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public java.util.List<? extends road.data.proto.SignalSchemeOrBuilder> 
         getSignalSchemeListOrBuilderList() {
      if (signalSchemeListBuilder_ != null) {
        return signalSchemeListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(signalSchemeList_);
      }
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public road.data.proto.SignalScheme.Builder addSignalSchemeListBuilder() {
      return getSignalSchemeListFieldBuilder().addBuilder(
          road.data.proto.SignalScheme.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public road.data.proto.SignalScheme.Builder addSignalSchemeListBuilder(
        int index) {
      return getSignalSchemeListFieldBuilder().addBuilder(
          index, road.data.proto.SignalScheme.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，信号优化建议列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
     */
    public java.util.List<road.data.proto.SignalScheme.Builder> 
         getSignalSchemeListBuilderList() {
      return getSignalSchemeListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.SignalScheme, road.data.proto.SignalScheme.Builder, road.data.proto.SignalSchemeOrBuilder> 
        getSignalSchemeListFieldBuilder() {
      if (signalSchemeListBuilder_ == null) {
        signalSchemeListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.SignalScheme, road.data.proto.SignalScheme.Builder, road.data.proto.SignalSchemeOrBuilder>(
                signalSchemeList_,
                ((bitField0_ & 0x00000100) != 0),
                getParentForChildren(),
                isClean());
        signalSchemeList_ = null;
      }
      return signalSchemeListBuilder_;
    }

    private java.util.List<road.data.proto.Polygon> detectedRegion_ =
      java.util.Collections.emptyList();
    private void ensureDetectedRegionIsMutable() {
      if (!((bitField0_ & 0x00000200) != 0)) {
        detectedRegion_ = new java.util.ArrayList<road.data.proto.Polygon>(detectedRegion_);
        bitField0_ |= 0x00000200;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> detectedRegionBuilder_;

    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public java.util.List<road.data.proto.Polygon> getDetectedRegionList() {
      if (detectedRegionBuilder_ == null) {
        return java.util.Collections.unmodifiableList(detectedRegion_);
      } else {
        return detectedRegionBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public int getDetectedRegionCount() {
      if (detectedRegionBuilder_ == null) {
        return detectedRegion_.size();
      } else {
        return detectedRegionBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public road.data.proto.Polygon getDetectedRegion(int index) {
      if (detectedRegionBuilder_ == null) {
        return detectedRegion_.get(index);
      } else {
        return detectedRegionBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public Builder setDetectedRegion(
        int index, road.data.proto.Polygon value) {
      if (detectedRegionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetectedRegionIsMutable();
        detectedRegion_.set(index, value);
        onChanged();
      } else {
        detectedRegionBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public Builder setDetectedRegion(
        int index, road.data.proto.Polygon.Builder builderForValue) {
      if (detectedRegionBuilder_ == null) {
        ensureDetectedRegionIsMutable();
        detectedRegion_.set(index, builderForValue.build());
        onChanged();
      } else {
        detectedRegionBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public Builder addDetectedRegion(road.data.proto.Polygon value) {
      if (detectedRegionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetectedRegionIsMutable();
        detectedRegion_.add(value);
        onChanged();
      } else {
        detectedRegionBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public Builder addDetectedRegion(
        int index, road.data.proto.Polygon value) {
      if (detectedRegionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetectedRegionIsMutable();
        detectedRegion_.add(index, value);
        onChanged();
      } else {
        detectedRegionBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public Builder addDetectedRegion(
        road.data.proto.Polygon.Builder builderForValue) {
      if (detectedRegionBuilder_ == null) {
        ensureDetectedRegionIsMutable();
        detectedRegion_.add(builderForValue.build());
        onChanged();
      } else {
        detectedRegionBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public Builder addDetectedRegion(
        int index, road.data.proto.Polygon.Builder builderForValue) {
      if (detectedRegionBuilder_ == null) {
        ensureDetectedRegionIsMutable();
        detectedRegion_.add(index, builderForValue.build());
        onChanged();
      } else {
        detectedRegionBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public Builder addAllDetectedRegion(
        java.lang.Iterable<? extends road.data.proto.Polygon> values) {
      if (detectedRegionBuilder_ == null) {
        ensureDetectedRegionIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, detectedRegion_);
        onChanged();
      } else {
        detectedRegionBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public Builder clearDetectedRegion() {
      if (detectedRegionBuilder_ == null) {
        detectedRegion_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
      } else {
        detectedRegionBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public Builder removeDetectedRegion(int index) {
      if (detectedRegionBuilder_ == null) {
        ensureDetectedRegionIsMutable();
        detectedRegion_.remove(index);
        onChanged();
      } else {
        detectedRegionBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public road.data.proto.Polygon.Builder getDetectedRegionBuilder(
        int index) {
      return getDetectedRegionFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public road.data.proto.PolygonOrBuilder getDetectedRegionOrBuilder(
        int index) {
      if (detectedRegionBuilder_ == null) {
        return detectedRegion_.get(index);  } else {
        return detectedRegionBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public java.util.List<? extends road.data.proto.PolygonOrBuilder> 
         getDetectedRegionOrBuilderList() {
      if (detectedRegionBuilder_ != null) {
        return detectedRegionBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(detectedRegion_);
      }
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public road.data.proto.Polygon.Builder addDetectedRegionBuilder() {
      return getDetectedRegionFieldBuilder().addBuilder(
          road.data.proto.Polygon.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public road.data.proto.Polygon.Builder addDetectedRegionBuilder(
        int index) {
      return getDetectedRegionFieldBuilder().addBuilder(
          index, road.data.proto.Polygon.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，定义感知区域列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
     */
    public java.util.List<road.data.proto.Polygon.Builder> 
         getDetectedRegionBuilderList() {
      return getDetectedRegionFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> 
        getDetectedRegionFieldBuilder() {
      if (detectedRegionBuilder_ == null) {
        detectedRegionBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder>(
                detectedRegion_,
                ((bitField0_ & 0x00000200) != 0),
                getParentForChildren(),
                isClean());
        detectedRegion_ = null;
      }
      return detectedRegionBuilder_;
    }

    private long toAlgorithmTime_ ;
    /**
     * <pre>
     *  可选，到达融合算法的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 toAlgorithmTime = 20;</code>
     */
    public long getToAlgorithmTime() {
      return toAlgorithmTime_;
    }
    /**
     * <pre>
     *  可选，到达融合算法的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 toAlgorithmTime = 20;</code>
     */
    public Builder setToAlgorithmTime(long value) {
      
      toAlgorithmTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *  可选，到达融合算法的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 toAlgorithmTime = 20;</code>
     */
    public Builder clearToAlgorithmTime() {
      
      toAlgorithmTime_ = 0L;
      onChanged();
      return this;
    }

    private long toDatabusTime_ ;
    /**
     * <pre>
     * 可选，到达当前接收端的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 toDatabusTime = 21;</code>
     */
    public long getToDatabusTime() {
      return toDatabusTime_;
    }
    /**
     * <pre>
     * 可选，到达当前接收端的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 toDatabusTime = 21;</code>
     */
    public Builder setToDatabusTime(long value) {
      
      toDatabusTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，到达当前接收端的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 toDatabusTime = 21;</code>
     */
    public Builder clearToDatabusTime() {
      
      toDatabusTime_ = 0L;
      onChanged();
      return this;
    }

    private long toCloudTime_ ;
    /**
     * <pre>
     * 可选，到达云端的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 toCloudTime = 22;</code>
     */
    public long getToCloudTime() {
      return toCloudTime_;
    }
    /**
     * <pre>
     * 可选，到达云端的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 toCloudTime = 22;</code>
     */
    public Builder setToCloudTime(long value) {
      
      toCloudTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，到达云端的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 toCloudTime = 22;</code>
     */
    public Builder clearToCloudTime() {
      
      toCloudTime_ = 0L;
      onChanged();
      return this;
    }

    private long id_ ;
    /**
     * <pre>
     * 可选，数据唯一标识id
     * </pre>
     *
     * <code>uint64 id = 23;</code>
     */
    public long getId() {
      return id_;
    }
    /**
     * <pre>
     * 可选，数据唯一标识id
     * </pre>
     *
     * <code>uint64 id = 23;</code>
     */
    public Builder setId(long value) {
      
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，数据唯一标识id
     * </pre>
     *
     * <code>uint64 id = 23;</code>
     */
    public Builder clearId() {
      
      id_ = 0L;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.CamData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.CamData)
  private static final road.data.proto.CamData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.CamData();
  }

  public static road.data.proto.CamData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CamData>
      PARSER = new com.google.protobuf.AbstractParser<CamData>() {
    @java.lang.Override
    public CamData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new CamData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<CamData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CamData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.CamData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

