// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface CamDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.CamData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * type取值为1,表示MEC向RSU发送目标物和事件信息
   * </pre>
   *
   * <code>uint32 type = 1;</code>
   */
  int getType();

  /**
   * <pre>
   * 版本号，目前版本固定为“01”
   * </pre>
   *
   * <code>string ver = 2;</code>
   */
  java.lang.String getVer();
  /**
   * <pre>
   * 版本号，目前版本固定为“01”
   * </pre>
   *
   * <code>string ver = 2;</code>
   */
  com.google.protobuf.ByteString
      getVerBytes();

  /**
   * <pre>
   * 定义消息编号。发送方对发送的同类消息(type=1)依次进行编号。编号循环发送。
   * </pre>
   *
   * <code>uint32 msgCnt = 3;</code>
   */
  int getMsgCnt();

  /**
   * <pre>
   * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 4;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   * MEC的设备编号。例如，MEC的ESN编号，或者每个MEC的序列编号。只支持可见字符（ASCII码[32,126]）。
   * </pre>
   *
   * <code>string deviceId = 5;</code>
   */
  java.lang.String getDeviceId();
  /**
   * <pre>
   * MEC的设备编号。例如，MEC的ESN编号，或者每个MEC的序列编号。只支持可见字符（ASCII码[32,126]）。
   * </pre>
   *
   * <code>string deviceId = 5;</code>
   */
  com.google.protobuf.ByteString
      getDeviceIdBytes();

  /**
   * <pre>
   *位置相关的设备编号
   * </pre>
   *
   * <code>string mapDeviceId = 6;</code>
   */
  java.lang.String getMapDeviceId();
  /**
   * <pre>
   *位置相关的设备编号
   * </pre>
   *
   * <code>string mapDeviceId = 6;</code>
   */
  com.google.protobuf.ByteString
      getMapDeviceIdBytes();

  /**
   * <pre>
   * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
   */
  boolean hasRefPos();
  /**
   * <pre>
   * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
   */
  road.data.proto.Position3D getRefPos();
  /**
   * <pre>
   * 位置基准参考点,绝对坐标(感知区域中心点)。注：MEC的的经纬度坐标位置。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 7;</code>
   */
  road.data.proto.Position3DOrBuilder getRefPosOrBuilder();

  /**
   * <pre>
   * 可选，表示场地类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SceneType sceneType = 8;</code>
   */
  int getSceneTypeValue();
  /**
   * <pre>
   * 可选，表示场地类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SceneType sceneType = 8;</code>
   */
  road.data.proto.SceneType getSceneType();

  /**
   * <pre>
   *可选，定义目标物列表，属于RSM中的交通参与者
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
   */
  java.util.List<road.data.proto.ParticipantData> 
      getPtcListList();
  /**
   * <pre>
   *可选，定义目标物列表，属于RSM中的交通参与者
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
   */
  road.data.proto.ParticipantData getPtcList(int index);
  /**
   * <pre>
   *可选，定义目标物列表，属于RSM中的交通参与者
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
   */
  int getPtcListCount();
  /**
   * <pre>
   *可选，定义目标物列表，属于RSM中的交通参与者
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
   */
  java.util.List<? extends road.data.proto.ParticipantDataOrBuilder> 
      getPtcListOrBuilderList();
  /**
   * <pre>
   *可选，定义目标物列表，属于RSM中的交通参与者
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData ptcList = 9;</code>
   */
  road.data.proto.ParticipantDataOrBuilder getPtcListOrBuilder(
      int index);

  /**
   * <pre>
   *可选，定义障碍物列表，属于RSM中的障碍物
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
   */
  java.util.List<road.data.proto.ObstacleData> 
      getObstacleListList();
  /**
   * <pre>
   *可选，定义障碍物列表，属于RSM中的障碍物
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
   */
  road.data.proto.ObstacleData getObstacleList(int index);
  /**
   * <pre>
   *可选，定义障碍物列表，属于RSM中的障碍物
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
   */
  int getObstacleListCount();
  /**
   * <pre>
   *可选，定义障碍物列表，属于RSM中的障碍物
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
   */
  java.util.List<? extends road.data.proto.ObstacleDataOrBuilder> 
      getObstacleListOrBuilderList();
  /**
   * <pre>
   *可选，定义障碍物列表，属于RSM中的障碍物
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleList = 10;</code>
   */
  road.data.proto.ObstacleDataOrBuilder getObstacleListOrBuilder(
      int index);

  /**
   * <pre>
   *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
   */
  java.util.List<road.data.proto.RteData> 
      getRteListList();
  /**
   * <pre>
   *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
   */
  road.data.proto.RteData getRteList(int index);
  /**
   * <pre>
   *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
   */
  int getRteListCount();
  /**
   * <pre>
   *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
   */
  java.util.List<? extends road.data.proto.RteDataOrBuilder> 
      getRteListOrBuilderList();
  /**
   * <pre>
   *repeated RsiData rsi_list = 10;	// 交通事件和标志信息。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RteData rteList = 11;</code>
   */
  road.data.proto.RteDataOrBuilder getRteListOrBuilder(
      int index);

  /**
   * <pre>
   *可选，交通标志信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
   */
  java.util.List<road.data.proto.RtsData> 
      getRtsListList();
  /**
   * <pre>
   *可选，交通标志信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
   */
  road.data.proto.RtsData getRtsList(int index);
  /**
   * <pre>
   *可选，交通标志信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
   */
  int getRtsListCount();
  /**
   * <pre>
   *可选，交通标志信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
   */
  java.util.List<? extends road.data.proto.RtsDataOrBuilder> 
      getRtsListOrBuilderList();
  /**
   * <pre>
   *可选，交通标志信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsList = 12;</code>
   */
  road.data.proto.RtsDataOrBuilder getRtsListOrBuilder(
      int index);

  /**
   * <pre>
   *可选，基本安全消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
   */
  java.util.List<road.data.proto.BsmData> 
      getBsmListList();
  /**
   * <pre>
   *可选，基本安全消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
   */
  road.data.proto.BsmData getBsmList(int index);
  /**
   * <pre>
   *可选，基本安全消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
   */
  int getBsmListCount();
  /**
   * <pre>
   *可选，基本安全消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
   */
  java.util.List<? extends road.data.proto.BsmDataOrBuilder> 
      getBsmListOrBuilderList();
  /**
   * <pre>
   *可选，基本安全消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.BsmData bsmList = 13;</code>
   */
  road.data.proto.BsmDataOrBuilder getBsmListOrBuilder(
      int index);

  /**
   * <pre>
   *可选，车辆意图及请求消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
   */
  java.util.List<road.data.proto.VirData> 
      getVirListList();
  /**
   * <pre>
   *可选，车辆意图及请求消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
   */
  road.data.proto.VirData getVirList(int index);
  /**
   * <pre>
   *可选，车辆意图及请求消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
   */
  int getVirListCount();
  /**
   * <pre>
   *可选，车辆意图及请求消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
   */
  java.util.List<? extends road.data.proto.VirDataOrBuilder> 
      getVirListOrBuilderList();
  /**
   * <pre>
   *可选，车辆意图及请求消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.VirData virList = 14;</code>
   */
  road.data.proto.VirDataOrBuilder getVirListOrBuilder(
      int index);

  /**
   * <pre>
   *可选，车辆协作或引导消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
   */
  java.util.List<road.data.proto.RscData> 
      getRscListList();
  /**
   * <pre>
   *可选，车辆协作或引导消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
   */
  road.data.proto.RscData getRscList(int index);
  /**
   * <pre>
   *可选，车辆协作或引导消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
   */
  int getRscListCount();
  /**
   * <pre>
   *可选，车辆协作或引导消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
   */
  java.util.List<? extends road.data.proto.RscDataOrBuilder> 
      getRscListOrBuilderList();
  /**
   * <pre>
   *可选，车辆协作或引导消息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RscData rscList = 15;</code>
   */
  road.data.proto.RscDataOrBuilder getRscListOrBuilder(
      int index);

  /**
   * <pre>
   * 可选，实时交通相位SPAT信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
   */
  boolean hasRoadSignalState();
  /**
   * <pre>
   * 可选，实时交通相位SPAT信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
   */
  road.data.proto.SpatData getRoadSignalState();
  /**
   * <pre>
   * 可选，实时交通相位SPAT信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpatData roadSignalState = 16;</code>
   */
  road.data.proto.SpatDataOrBuilder getRoadSignalStateOrBuilder();

  /**
   * <pre>
   *可选，实时交通流信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
   */
  java.util.List<road.data.proto.TrafficFlow> 
      getTrafficFlowList();
  /**
   * <pre>
   *可选，实时交通流信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
   */
  road.data.proto.TrafficFlow getTrafficFlow(int index);
  /**
   * <pre>
   *可选，实时交通流信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
   */
  int getTrafficFlowCount();
  /**
   * <pre>
   *可选，实时交通流信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
   */
  java.util.List<? extends road.data.proto.TrafficFlowOrBuilder> 
      getTrafficFlowOrBuilderList();
  /**
   * <pre>
   *可选，实时交通流信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlow = 17;</code>
   */
  road.data.proto.TrafficFlowOrBuilder getTrafficFlowOrBuilder(
      int index);

  /**
   * <pre>
   *可选，信号优化建议列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
   */
  java.util.List<road.data.proto.SignalScheme> 
      getSignalSchemeListList();
  /**
   * <pre>
   *可选，信号优化建议列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
   */
  road.data.proto.SignalScheme getSignalSchemeList(int index);
  /**
   * <pre>
   *可选，信号优化建议列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
   */
  int getSignalSchemeListCount();
  /**
   * <pre>
   *可选，信号优化建议列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
   */
  java.util.List<? extends road.data.proto.SignalSchemeOrBuilder> 
      getSignalSchemeListOrBuilderList();
  /**
   * <pre>
   *可选，信号优化建议列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 18;</code>
   */
  road.data.proto.SignalSchemeOrBuilder getSignalSchemeListOrBuilder(
      int index);

  /**
   * <pre>
   * 可选，定义感知区域列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
   */
  java.util.List<road.data.proto.Polygon> 
      getDetectedRegionList();
  /**
   * <pre>
   * 可选，定义感知区域列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
   */
  road.data.proto.Polygon getDetectedRegion(int index);
  /**
   * <pre>
   * 可选，定义感知区域列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
   */
  int getDetectedRegionCount();
  /**
   * <pre>
   * 可选，定义感知区域列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
   */
  java.util.List<? extends road.data.proto.PolygonOrBuilder> 
      getDetectedRegionOrBuilderList();
  /**
   * <pre>
   * 可选，定义感知区域列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon detectedRegion = 19;</code>
   */
  road.data.proto.PolygonOrBuilder getDetectedRegionOrBuilder(
      int index);

  /**
   * <pre>
   *  可选，到达融合算法的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 toAlgorithmTime = 20;</code>
   */
  long getToAlgorithmTime();

  /**
   * <pre>
   * 可选，到达当前接收端的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 toDatabusTime = 21;</code>
   */
  long getToDatabusTime();

  /**
   * <pre>
   * 可选，到达云端的时间戳，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 toCloudTime = 22;</code>
   */
  long getToCloudTime();

  /**
   * <pre>
   * 可选，数据唯一标识id
   * </pre>
   *
   * <code>uint64 id = 23;</code>
   */
  long getId();
}
