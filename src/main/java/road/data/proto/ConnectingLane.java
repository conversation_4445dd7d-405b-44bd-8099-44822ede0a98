// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *连接车道ConnectionLane     
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ConnectingLane}
 */
public  final class ConnectingLane extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ConnectingLane)
    ConnectingLaneOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ConnectingLane.newBuilder() to construct.
  private ConnectingLane(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ConnectingLane() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ConnectingLane();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ConnectingLane(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            lane_ = input.readUInt32();
            break;
          }
          case 18: {
            road.data.proto.AllowedManeuvers.Builder subBuilder = null;
            if (maneuver_ != null) {
              subBuilder = maneuver_.toBuilder();
            }
            maneuver_ = input.readMessage(road.data.proto.AllowedManeuvers.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(maneuver_);
              maneuver_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectingLane_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectingLane_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ConnectingLane.class, road.data.proto.ConnectingLane.Builder.class);
  }

  public static final int LANE_FIELD_NUMBER = 1;
  private int lane_;
  /**
   * <pre>
   *LaneId车道定义在每一条有向路段上，同一条有向路段上的每个车道，都拥有一个单独的ID。车道号以该车道行驶方向为参考，自左向右从1开始编号。
   * </pre>
   *
   * <code>uint32 lane = 1;</code>
   */
  public int getLane() {
    return lane_;
  }

  public static final int MANEUVER_FIELD_NUMBER = 2;
  private road.data.proto.AllowedManeuvers maneuver_;
  /**
   * <pre>
   *可选，同Lane中定义，该转向的允许行驶行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
   */
  public boolean hasManeuver() {
    return maneuver_ != null;
  }
  /**
   * <pre>
   *可选，同Lane中定义，该转向的允许行驶行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
   */
  public road.data.proto.AllowedManeuvers getManeuver() {
    return maneuver_ == null ? road.data.proto.AllowedManeuvers.getDefaultInstance() : maneuver_;
  }
  /**
   * <pre>
   *可选，同Lane中定义，该转向的允许行驶行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
   */
  public road.data.proto.AllowedManeuversOrBuilder getManeuverOrBuilder() {
    return getManeuver();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (lane_ != 0) {
      output.writeUInt32(1, lane_);
    }
    if (maneuver_ != null) {
      output.writeMessage(2, getManeuver());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (lane_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, lane_);
    }
    if (maneuver_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getManeuver());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ConnectingLane)) {
      return super.equals(obj);
    }
    road.data.proto.ConnectingLane other = (road.data.proto.ConnectingLane) obj;

    if (getLane()
        != other.getLane()) return false;
    if (hasManeuver() != other.hasManeuver()) return false;
    if (hasManeuver()) {
      if (!getManeuver()
          .equals(other.getManeuver())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + LANE_FIELD_NUMBER;
    hash = (53 * hash) + getLane();
    if (hasManeuver()) {
      hash = (37 * hash) + MANEUVER_FIELD_NUMBER;
      hash = (53 * hash) + getManeuver().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ConnectingLane parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ConnectingLane parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ConnectingLane parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ConnectingLane parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ConnectingLane parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ConnectingLane parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ConnectingLane parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ConnectingLane parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ConnectingLane parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ConnectingLane parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ConnectingLane parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ConnectingLane parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ConnectingLane prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *连接车道ConnectionLane     
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ConnectingLane}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ConnectingLane)
      road.data.proto.ConnectingLaneOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectingLane_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectingLane_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ConnectingLane.class, road.data.proto.ConnectingLane.Builder.class);
    }

    // Construct using road.data.proto.ConnectingLane.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      lane_ = 0;

      if (maneuverBuilder_ == null) {
        maneuver_ = null;
      } else {
        maneuver_ = null;
        maneuverBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectingLane_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ConnectingLane getDefaultInstanceForType() {
      return road.data.proto.ConnectingLane.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ConnectingLane build() {
      road.data.proto.ConnectingLane result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ConnectingLane buildPartial() {
      road.data.proto.ConnectingLane result = new road.data.proto.ConnectingLane(this);
      result.lane_ = lane_;
      if (maneuverBuilder_ == null) {
        result.maneuver_ = maneuver_;
      } else {
        result.maneuver_ = maneuverBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ConnectingLane) {
        return mergeFrom((road.data.proto.ConnectingLane)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ConnectingLane other) {
      if (other == road.data.proto.ConnectingLane.getDefaultInstance()) return this;
      if (other.getLane() != 0) {
        setLane(other.getLane());
      }
      if (other.hasManeuver()) {
        mergeManeuver(other.getManeuver());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ConnectingLane parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ConnectingLane) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int lane_ ;
    /**
     * <pre>
     *LaneId车道定义在每一条有向路段上，同一条有向路段上的每个车道，都拥有一个单独的ID。车道号以该车道行驶方向为参考，自左向右从1开始编号。
     * </pre>
     *
     * <code>uint32 lane = 1;</code>
     */
    public int getLane() {
      return lane_;
    }
    /**
     * <pre>
     *LaneId车道定义在每一条有向路段上，同一条有向路段上的每个车道，都拥有一个单独的ID。车道号以该车道行驶方向为参考，自左向右从1开始编号。
     * </pre>
     *
     * <code>uint32 lane = 1;</code>
     */
    public Builder setLane(int value) {
      
      lane_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *LaneId车道定义在每一条有向路段上，同一条有向路段上的每个车道，都拥有一个单独的ID。车道号以该车道行驶方向为参考，自左向右从1开始编号。
     * </pre>
     *
     * <code>uint32 lane = 1;</code>
     */
    public Builder clearLane() {
      
      lane_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.AllowedManeuvers maneuver_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AllowedManeuvers, road.data.proto.AllowedManeuvers.Builder, road.data.proto.AllowedManeuversOrBuilder> maneuverBuilder_;
    /**
     * <pre>
     *可选，同Lane中定义，该转向的允许行驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
     */
    public boolean hasManeuver() {
      return maneuverBuilder_ != null || maneuver_ != null;
    }
    /**
     * <pre>
     *可选，同Lane中定义，该转向的允许行驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
     */
    public road.data.proto.AllowedManeuvers getManeuver() {
      if (maneuverBuilder_ == null) {
        return maneuver_ == null ? road.data.proto.AllowedManeuvers.getDefaultInstance() : maneuver_;
      } else {
        return maneuverBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，同Lane中定义，该转向的允许行驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
     */
    public Builder setManeuver(road.data.proto.AllowedManeuvers value) {
      if (maneuverBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        maneuver_ = value;
        onChanged();
      } else {
        maneuverBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，同Lane中定义，该转向的允许行驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
     */
    public Builder setManeuver(
        road.data.proto.AllowedManeuvers.Builder builderForValue) {
      if (maneuverBuilder_ == null) {
        maneuver_ = builderForValue.build();
        onChanged();
      } else {
        maneuverBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，同Lane中定义，该转向的允许行驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
     */
    public Builder mergeManeuver(road.data.proto.AllowedManeuvers value) {
      if (maneuverBuilder_ == null) {
        if (maneuver_ != null) {
          maneuver_ =
            road.data.proto.AllowedManeuvers.newBuilder(maneuver_).mergeFrom(value).buildPartial();
        } else {
          maneuver_ = value;
        }
        onChanged();
      } else {
        maneuverBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，同Lane中定义，该转向的允许行驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
     */
    public Builder clearManeuver() {
      if (maneuverBuilder_ == null) {
        maneuver_ = null;
        onChanged();
      } else {
        maneuver_ = null;
        maneuverBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，同Lane中定义，该转向的允许行驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
     */
    public road.data.proto.AllowedManeuvers.Builder getManeuverBuilder() {
      
      onChanged();
      return getManeuverFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，同Lane中定义，该转向的允许行驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
     */
    public road.data.proto.AllowedManeuversOrBuilder getManeuverOrBuilder() {
      if (maneuverBuilder_ != null) {
        return maneuverBuilder_.getMessageOrBuilder();
      } else {
        return maneuver_ == null ?
            road.data.proto.AllowedManeuvers.getDefaultInstance() : maneuver_;
      }
    }
    /**
     * <pre>
     *可选，同Lane中定义，该转向的允许行驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AllowedManeuvers, road.data.proto.AllowedManeuvers.Builder, road.data.proto.AllowedManeuversOrBuilder> 
        getManeuverFieldBuilder() {
      if (maneuverBuilder_ == null) {
        maneuverBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.AllowedManeuvers, road.data.proto.AllowedManeuvers.Builder, road.data.proto.AllowedManeuversOrBuilder>(
                getManeuver(),
                getParentForChildren(),
                isClean());
        maneuver_ = null;
      }
      return maneuverBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ConnectingLane)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ConnectingLane)
  private static final road.data.proto.ConnectingLane DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ConnectingLane();
  }

  public static road.data.proto.ConnectingLane getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ConnectingLane>
      PARSER = new com.google.protobuf.AbstractParser<ConnectingLane>() {
    @java.lang.Override
    public ConnectingLane parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ConnectingLane(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ConnectingLane> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ConnectingLane> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ConnectingLane getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

