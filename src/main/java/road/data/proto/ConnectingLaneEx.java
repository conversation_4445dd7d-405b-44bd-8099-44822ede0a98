// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *定位上游车道转向连接的下游车道的扩展信息 ConnectingLaneEx                
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ConnectingLaneEx}
 */
public  final class ConnectingLaneEx extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ConnectingLaneEx)
    ConnectingLaneExOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ConnectingLaneEx.newBuilder() to construct.
  private ConnectingLaneEx(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ConnectingLaneEx() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ConnectingLaneEx();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ConnectingLaneEx(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            targetSection_ = input.readInt32();
            break;
          }
          case 16: {

            targetLane_ = input.readInt32();
            break;
          }
          case 24: {

            connectingLaneWidth_ = input.readInt32();
            break;
          }
          case 34: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (connectingLanePoints_ != null) {
              subBuilder = connectingLanePoints_.toBuilder();
            }
            connectingLanePoints_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(connectingLanePoints_);
              connectingLanePoints_ = subBuilder.buildPartial();
            }

            break;
          }
          case 40: {

            isolatedConnectingLane_ = input.readBool();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectingLaneEx_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectingLaneEx_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ConnectingLaneEx.class, road.data.proto.ConnectingLaneEx.Builder.class);
  }

  public static final int TARGETSECTION_FIELD_NUMBER = 1;
  private int targetSection_;
  /**
   * <pre>
   *连接路段索引
   * </pre>
   *
   * <code>int32 targetSection = 1;</code>
   */
  public int getTargetSection() {
    return targetSection_;
  }

  public static final int TARGETLANE_FIELD_NUMBER = 2;
  private int targetLane_;
  /**
   * <pre>
   *连接车道索引
   * </pre>
   *
   * <code>int32 targetLane = 2;</code>
   */
  public int getTargetLane() {
    return targetLane_;
  }

  public static final int CONNECTINGLANEWIDTH_FIELD_NUMBER = 3;
  private int connectingLaneWidth_;
  /**
   * <pre>
   *可选，指示真实或虚拟连接车道的宽度
   * </pre>
   *
   * <code>int32 connectingLaneWidth = 3;</code>
   */
  public int getConnectingLaneWidth() {
    return connectingLaneWidth_;
  }

  public static final int CONNECTINGLANEPOINTS_FIELD_NUMBER = 4;
  private road.data.proto.Position3D connectingLanePoints_;
  /**
   * <pre>
   *可选，指示真实或虚拟连接车道的位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
   */
  public boolean hasConnectingLanePoints() {
    return connectingLanePoints_ != null;
  }
  /**
   * <pre>
   *可选，指示真实或虚拟连接车道的位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
   */
  public road.data.proto.Position3D getConnectingLanePoints() {
    return connectingLanePoints_ == null ? road.data.proto.Position3D.getDefaultInstance() : connectingLanePoints_;
  }
  /**
   * <pre>
   *可选，指示真实或虚拟连接车道的位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
   */
  public road.data.proto.Position3DOrBuilder getConnectingLanePointsOrBuilder() {
    return getConnectingLanePoints();
  }

  public static final int ISOLATEDCONNECTINGLANE_FIELD_NUMBER = 5;
  private boolean isolatedConnectingLane_;
  /**
   * <pre>
   *可选，真正孤立的车道
   * </pre>
   *
   * <code>bool isolatedConnectingLane = 5;</code>
   */
  public boolean getIsolatedConnectingLane() {
    return isolatedConnectingLane_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (targetSection_ != 0) {
      output.writeInt32(1, targetSection_);
    }
    if (targetLane_ != 0) {
      output.writeInt32(2, targetLane_);
    }
    if (connectingLaneWidth_ != 0) {
      output.writeInt32(3, connectingLaneWidth_);
    }
    if (connectingLanePoints_ != null) {
      output.writeMessage(4, getConnectingLanePoints());
    }
    if (isolatedConnectingLane_ != false) {
      output.writeBool(5, isolatedConnectingLane_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (targetSection_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, targetSection_);
    }
    if (targetLane_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, targetLane_);
    }
    if (connectingLaneWidth_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, connectingLaneWidth_);
    }
    if (connectingLanePoints_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getConnectingLanePoints());
    }
    if (isolatedConnectingLane_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(5, isolatedConnectingLane_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ConnectingLaneEx)) {
      return super.equals(obj);
    }
    road.data.proto.ConnectingLaneEx other = (road.data.proto.ConnectingLaneEx) obj;

    if (getTargetSection()
        != other.getTargetSection()) return false;
    if (getTargetLane()
        != other.getTargetLane()) return false;
    if (getConnectingLaneWidth()
        != other.getConnectingLaneWidth()) return false;
    if (hasConnectingLanePoints() != other.hasConnectingLanePoints()) return false;
    if (hasConnectingLanePoints()) {
      if (!getConnectingLanePoints()
          .equals(other.getConnectingLanePoints())) return false;
    }
    if (getIsolatedConnectingLane()
        != other.getIsolatedConnectingLane()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TARGETSECTION_FIELD_NUMBER;
    hash = (53 * hash) + getTargetSection();
    hash = (37 * hash) + TARGETLANE_FIELD_NUMBER;
    hash = (53 * hash) + getTargetLane();
    hash = (37 * hash) + CONNECTINGLANEWIDTH_FIELD_NUMBER;
    hash = (53 * hash) + getConnectingLaneWidth();
    if (hasConnectingLanePoints()) {
      hash = (37 * hash) + CONNECTINGLANEPOINTS_FIELD_NUMBER;
      hash = (53 * hash) + getConnectingLanePoints().hashCode();
    }
    hash = (37 * hash) + ISOLATEDCONNECTINGLANE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIsolatedConnectingLane());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ConnectingLaneEx parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ConnectingLaneEx parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ConnectingLaneEx parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ConnectingLaneEx parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ConnectingLaneEx parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ConnectingLaneEx parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ConnectingLaneEx parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ConnectingLaneEx parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ConnectingLaneEx parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ConnectingLaneEx parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ConnectingLaneEx parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ConnectingLaneEx parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ConnectingLaneEx prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *定位上游车道转向连接的下游车道的扩展信息 ConnectingLaneEx                
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ConnectingLaneEx}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ConnectingLaneEx)
      road.data.proto.ConnectingLaneExOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectingLaneEx_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectingLaneEx_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ConnectingLaneEx.class, road.data.proto.ConnectingLaneEx.Builder.class);
    }

    // Construct using road.data.proto.ConnectingLaneEx.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      targetSection_ = 0;

      targetLane_ = 0;

      connectingLaneWidth_ = 0;

      if (connectingLanePointsBuilder_ == null) {
        connectingLanePoints_ = null;
      } else {
        connectingLanePoints_ = null;
        connectingLanePointsBuilder_ = null;
      }
      isolatedConnectingLane_ = false;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectingLaneEx_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ConnectingLaneEx getDefaultInstanceForType() {
      return road.data.proto.ConnectingLaneEx.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ConnectingLaneEx build() {
      road.data.proto.ConnectingLaneEx result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ConnectingLaneEx buildPartial() {
      road.data.proto.ConnectingLaneEx result = new road.data.proto.ConnectingLaneEx(this);
      result.targetSection_ = targetSection_;
      result.targetLane_ = targetLane_;
      result.connectingLaneWidth_ = connectingLaneWidth_;
      if (connectingLanePointsBuilder_ == null) {
        result.connectingLanePoints_ = connectingLanePoints_;
      } else {
        result.connectingLanePoints_ = connectingLanePointsBuilder_.build();
      }
      result.isolatedConnectingLane_ = isolatedConnectingLane_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ConnectingLaneEx) {
        return mergeFrom((road.data.proto.ConnectingLaneEx)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ConnectingLaneEx other) {
      if (other == road.data.proto.ConnectingLaneEx.getDefaultInstance()) return this;
      if (other.getTargetSection() != 0) {
        setTargetSection(other.getTargetSection());
      }
      if (other.getTargetLane() != 0) {
        setTargetLane(other.getTargetLane());
      }
      if (other.getConnectingLaneWidth() != 0) {
        setConnectingLaneWidth(other.getConnectingLaneWidth());
      }
      if (other.hasConnectingLanePoints()) {
        mergeConnectingLanePoints(other.getConnectingLanePoints());
      }
      if (other.getIsolatedConnectingLane() != false) {
        setIsolatedConnectingLane(other.getIsolatedConnectingLane());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ConnectingLaneEx parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ConnectingLaneEx) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int targetSection_ ;
    /**
     * <pre>
     *连接路段索引
     * </pre>
     *
     * <code>int32 targetSection = 1;</code>
     */
    public int getTargetSection() {
      return targetSection_;
    }
    /**
     * <pre>
     *连接路段索引
     * </pre>
     *
     * <code>int32 targetSection = 1;</code>
     */
    public Builder setTargetSection(int value) {
      
      targetSection_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *连接路段索引
     * </pre>
     *
     * <code>int32 targetSection = 1;</code>
     */
    public Builder clearTargetSection() {
      
      targetSection_ = 0;
      onChanged();
      return this;
    }

    private int targetLane_ ;
    /**
     * <pre>
     *连接车道索引
     * </pre>
     *
     * <code>int32 targetLane = 2;</code>
     */
    public int getTargetLane() {
      return targetLane_;
    }
    /**
     * <pre>
     *连接车道索引
     * </pre>
     *
     * <code>int32 targetLane = 2;</code>
     */
    public Builder setTargetLane(int value) {
      
      targetLane_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *连接车道索引
     * </pre>
     *
     * <code>int32 targetLane = 2;</code>
     */
    public Builder clearTargetLane() {
      
      targetLane_ = 0;
      onChanged();
      return this;
    }

    private int connectingLaneWidth_ ;
    /**
     * <pre>
     *可选，指示真实或虚拟连接车道的宽度
     * </pre>
     *
     * <code>int32 connectingLaneWidth = 3;</code>
     */
    public int getConnectingLaneWidth() {
      return connectingLaneWidth_;
    }
    /**
     * <pre>
     *可选，指示真实或虚拟连接车道的宽度
     * </pre>
     *
     * <code>int32 connectingLaneWidth = 3;</code>
     */
    public Builder setConnectingLaneWidth(int value) {
      
      connectingLaneWidth_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，指示真实或虚拟连接车道的宽度
     * </pre>
     *
     * <code>int32 connectingLaneWidth = 3;</code>
     */
    public Builder clearConnectingLaneWidth() {
      
      connectingLaneWidth_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.Position3D connectingLanePoints_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> connectingLanePointsBuilder_;
    /**
     * <pre>
     *可选，指示真实或虚拟连接车道的位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
     */
    public boolean hasConnectingLanePoints() {
      return connectingLanePointsBuilder_ != null || connectingLanePoints_ != null;
    }
    /**
     * <pre>
     *可选，指示真实或虚拟连接车道的位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
     */
    public road.data.proto.Position3D getConnectingLanePoints() {
      if (connectingLanePointsBuilder_ == null) {
        return connectingLanePoints_ == null ? road.data.proto.Position3D.getDefaultInstance() : connectingLanePoints_;
      } else {
        return connectingLanePointsBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，指示真实或虚拟连接车道的位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
     */
    public Builder setConnectingLanePoints(road.data.proto.Position3D value) {
      if (connectingLanePointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        connectingLanePoints_ = value;
        onChanged();
      } else {
        connectingLanePointsBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，指示真实或虚拟连接车道的位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
     */
    public Builder setConnectingLanePoints(
        road.data.proto.Position3D.Builder builderForValue) {
      if (connectingLanePointsBuilder_ == null) {
        connectingLanePoints_ = builderForValue.build();
        onChanged();
      } else {
        connectingLanePointsBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，指示真实或虚拟连接车道的位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
     */
    public Builder mergeConnectingLanePoints(road.data.proto.Position3D value) {
      if (connectingLanePointsBuilder_ == null) {
        if (connectingLanePoints_ != null) {
          connectingLanePoints_ =
            road.data.proto.Position3D.newBuilder(connectingLanePoints_).mergeFrom(value).buildPartial();
        } else {
          connectingLanePoints_ = value;
        }
        onChanged();
      } else {
        connectingLanePointsBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，指示真实或虚拟连接车道的位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
     */
    public Builder clearConnectingLanePoints() {
      if (connectingLanePointsBuilder_ == null) {
        connectingLanePoints_ = null;
        onChanged();
      } else {
        connectingLanePoints_ = null;
        connectingLanePointsBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，指示真实或虚拟连接车道的位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
     */
    public road.data.proto.Position3D.Builder getConnectingLanePointsBuilder() {
      
      onChanged();
      return getConnectingLanePointsFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，指示真实或虚拟连接车道的位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
     */
    public road.data.proto.Position3DOrBuilder getConnectingLanePointsOrBuilder() {
      if (connectingLanePointsBuilder_ != null) {
        return connectingLanePointsBuilder_.getMessageOrBuilder();
      } else {
        return connectingLanePoints_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : connectingLanePoints_;
      }
    }
    /**
     * <pre>
     *可选，指示真实或虚拟连接车道的位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getConnectingLanePointsFieldBuilder() {
      if (connectingLanePointsBuilder_ == null) {
        connectingLanePointsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getConnectingLanePoints(),
                getParentForChildren(),
                isClean());
        connectingLanePoints_ = null;
      }
      return connectingLanePointsBuilder_;
    }

    private boolean isolatedConnectingLane_ ;
    /**
     * <pre>
     *可选，真正孤立的车道
     * </pre>
     *
     * <code>bool isolatedConnectingLane = 5;</code>
     */
    public boolean getIsolatedConnectingLane() {
      return isolatedConnectingLane_;
    }
    /**
     * <pre>
     *可选，真正孤立的车道
     * </pre>
     *
     * <code>bool isolatedConnectingLane = 5;</code>
     */
    public Builder setIsolatedConnectingLane(boolean value) {
      
      isolatedConnectingLane_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，真正孤立的车道
     * </pre>
     *
     * <code>bool isolatedConnectingLane = 5;</code>
     */
    public Builder clearIsolatedConnectingLane() {
      
      isolatedConnectingLane_ = false;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ConnectingLaneEx)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ConnectingLaneEx)
  private static final road.data.proto.ConnectingLaneEx DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ConnectingLaneEx();
  }

  public static road.data.proto.ConnectingLaneEx getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ConnectingLaneEx>
      PARSER = new com.google.protobuf.AbstractParser<ConnectingLaneEx>() {
    @java.lang.Override
    public ConnectingLaneEx parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ConnectingLaneEx(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ConnectingLaneEx> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ConnectingLaneEx> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ConnectingLaneEx getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

