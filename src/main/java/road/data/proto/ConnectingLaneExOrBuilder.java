// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ConnectingLaneExOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ConnectingLaneEx)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *连接路段索引
   * </pre>
   *
   * <code>int32 targetSection = 1;</code>
   */
  int getTargetSection();

  /**
   * <pre>
   *连接车道索引
   * </pre>
   *
   * <code>int32 targetLane = 2;</code>
   */
  int getTargetLane();

  /**
   * <pre>
   *可选，指示真实或虚拟连接车道的宽度
   * </pre>
   *
   * <code>int32 connectingLaneWidth = 3;</code>
   */
  int getConnectingLaneWidth();

  /**
   * <pre>
   *可选，指示真实或虚拟连接车道的位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
   */
  boolean hasConnectingLanePoints();
  /**
   * <pre>
   *可选，指示真实或虚拟连接车道的位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
   */
  road.data.proto.Position3D getConnectingLanePoints();
  /**
   * <pre>
   *可选，指示真实或虚拟连接车道的位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D connectingLanePoints = 4;</code>
   */
  road.data.proto.Position3DOrBuilder getConnectingLanePointsOrBuilder();

  /**
   * <pre>
   *可选，真正孤立的车道
   * </pre>
   *
   * <code>bool isolatedConnectingLane = 5;</code>
   */
  boolean getIsolatedConnectingLane();
}
