// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ConnectingLaneOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ConnectingLane)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *LaneId车道定义在每一条有向路段上，同一条有向路段上的每个车道，都拥有一个单独的ID。车道号以该车道行驶方向为参考，自左向右从1开始编号。
   * </pre>
   *
   * <code>uint32 lane = 1;</code>
   */
  int getLane();

  /**
   * <pre>
   *可选，同Lane中定义，该转向的允许行驶行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
   */
  boolean hasManeuver();
  /**
   * <pre>
   *可选，同Lane中定义，该转向的允许行驶行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
   */
  road.data.proto.AllowedManeuvers getManeuver();
  /**
   * <pre>
   *可选，同Lane中定义，该转向的允许行驶行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuver = 2;</code>
   */
  road.data.proto.AllowedManeuversOrBuilder getManeuverOrBuilder();
}
