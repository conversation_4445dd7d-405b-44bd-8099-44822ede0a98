// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *道路连接关系Connection     
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.Connection}
 */
public  final class Connection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.Connection)
    ConnectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Connection.newBuilder() to construct.
  private Connection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Connection() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Connection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Connection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (remoteIntersection_ != null) {
              subBuilder = remoteIntersection_.toBuilder();
            }
            remoteIntersection_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(remoteIntersection_);
              remoteIntersection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            road.data.proto.ConnectingLane.Builder subBuilder = null;
            if (connectingLane_ != null) {
              subBuilder = connectingLane_.toBuilder();
            }
            connectingLane_ = input.readMessage(road.data.proto.ConnectingLane.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(connectingLane_);
              connectingLane_ = subBuilder.buildPartial();
            }

            break;
          }
          case 24: {

            phaseId_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Connection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Connection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.Connection.class, road.data.proto.Connection.Builder.class);
  }

  public static final int REMOTEINTERSECTION_FIELD_NUMBER = 1;
  private road.data.proto.NodeReferenceId remoteIntersection_;
  /**
   * <pre>
   * 节点属性ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  public boolean hasRemoteIntersection() {
    return remoteIntersection_ != null;
  }
  /**
   * <pre>
   * 节点属性ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  public road.data.proto.NodeReferenceId getRemoteIntersection() {
    return remoteIntersection_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : remoteIntersection_;
  }
  /**
   * <pre>
   * 节点属性ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getRemoteIntersectionOrBuilder() {
    return getRemoteIntersection();
  }

  public static final int CONNECTINGLANE_FIELD_NUMBER = 2;
  private road.data.proto.ConnectingLane connectingLane_;
  /**
   * <pre>
   * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
   */
  public boolean hasConnectingLane() {
    return connectingLane_ != null;
  }
  /**
   * <pre>
   * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
   */
  public road.data.proto.ConnectingLane getConnectingLane() {
    return connectingLane_ == null ? road.data.proto.ConnectingLane.getDefaultInstance() : connectingLane_;
  }
  /**
   * <pre>
   * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
   */
  public road.data.proto.ConnectingLaneOrBuilder getConnectingLaneOrBuilder() {
    return getConnectingLane();
  }

  public static final int PHASEID_FIELD_NUMBER = 3;
  private int phaseId_;
  /**
   * <pre>
   * 可选，对应的信号灯相位号，0值表示无效
   * </pre>
   *
   * <code>uint32 phaseId = 3;</code>
   */
  public int getPhaseId() {
    return phaseId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (remoteIntersection_ != null) {
      output.writeMessage(1, getRemoteIntersection());
    }
    if (connectingLane_ != null) {
      output.writeMessage(2, getConnectingLane());
    }
    if (phaseId_ != 0) {
      output.writeUInt32(3, phaseId_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (remoteIntersection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getRemoteIntersection());
    }
    if (connectingLane_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getConnectingLane());
    }
    if (phaseId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, phaseId_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.Connection)) {
      return super.equals(obj);
    }
    road.data.proto.Connection other = (road.data.proto.Connection) obj;

    if (hasRemoteIntersection() != other.hasRemoteIntersection()) return false;
    if (hasRemoteIntersection()) {
      if (!getRemoteIntersection()
          .equals(other.getRemoteIntersection())) return false;
    }
    if (hasConnectingLane() != other.hasConnectingLane()) return false;
    if (hasConnectingLane()) {
      if (!getConnectingLane()
          .equals(other.getConnectingLane())) return false;
    }
    if (getPhaseId()
        != other.getPhaseId()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRemoteIntersection()) {
      hash = (37 * hash) + REMOTEINTERSECTION_FIELD_NUMBER;
      hash = (53 * hash) + getRemoteIntersection().hashCode();
    }
    if (hasConnectingLane()) {
      hash = (37 * hash) + CONNECTINGLANE_FIELD_NUMBER;
      hash = (53 * hash) + getConnectingLane().hashCode();
    }
    hash = (37 * hash) + PHASEID_FIELD_NUMBER;
    hash = (53 * hash) + getPhaseId();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.Connection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Connection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Connection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Connection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Connection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Connection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Connection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.Connection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.Connection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.Connection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.Connection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.Connection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.Connection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *道路连接关系Connection     
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.Connection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.Connection)
      road.data.proto.ConnectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Connection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Connection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.Connection.class, road.data.proto.Connection.Builder.class);
    }

    // Construct using road.data.proto.Connection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (remoteIntersectionBuilder_ == null) {
        remoteIntersection_ = null;
      } else {
        remoteIntersection_ = null;
        remoteIntersectionBuilder_ = null;
      }
      if (connectingLaneBuilder_ == null) {
        connectingLane_ = null;
      } else {
        connectingLane_ = null;
        connectingLaneBuilder_ = null;
      }
      phaseId_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Connection_descriptor;
    }

    @java.lang.Override
    public road.data.proto.Connection getDefaultInstanceForType() {
      return road.data.proto.Connection.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.Connection build() {
      road.data.proto.Connection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.Connection buildPartial() {
      road.data.proto.Connection result = new road.data.proto.Connection(this);
      if (remoteIntersectionBuilder_ == null) {
        result.remoteIntersection_ = remoteIntersection_;
      } else {
        result.remoteIntersection_ = remoteIntersectionBuilder_.build();
      }
      if (connectingLaneBuilder_ == null) {
        result.connectingLane_ = connectingLane_;
      } else {
        result.connectingLane_ = connectingLaneBuilder_.build();
      }
      result.phaseId_ = phaseId_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.Connection) {
        return mergeFrom((road.data.proto.Connection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.Connection other) {
      if (other == road.data.proto.Connection.getDefaultInstance()) return this;
      if (other.hasRemoteIntersection()) {
        mergeRemoteIntersection(other.getRemoteIntersection());
      }
      if (other.hasConnectingLane()) {
        mergeConnectingLane(other.getConnectingLane());
      }
      if (other.getPhaseId() != 0) {
        setPhaseId(other.getPhaseId());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.Connection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.Connection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.NodeReferenceId remoteIntersection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> remoteIntersectionBuilder_;
    /**
     * <pre>
     * 节点属性ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public boolean hasRemoteIntersection() {
      return remoteIntersectionBuilder_ != null || remoteIntersection_ != null;
    }
    /**
     * <pre>
     * 节点属性ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public road.data.proto.NodeReferenceId getRemoteIntersection() {
      if (remoteIntersectionBuilder_ == null) {
        return remoteIntersection_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : remoteIntersection_;
      } else {
        return remoteIntersectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 节点属性ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public Builder setRemoteIntersection(road.data.proto.NodeReferenceId value) {
      if (remoteIntersectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        remoteIntersection_ = value;
        onChanged();
      } else {
        remoteIntersectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 节点属性ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public Builder setRemoteIntersection(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (remoteIntersectionBuilder_ == null) {
        remoteIntersection_ = builderForValue.build();
        onChanged();
      } else {
        remoteIntersectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 节点属性ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public Builder mergeRemoteIntersection(road.data.proto.NodeReferenceId value) {
      if (remoteIntersectionBuilder_ == null) {
        if (remoteIntersection_ != null) {
          remoteIntersection_ =
            road.data.proto.NodeReferenceId.newBuilder(remoteIntersection_).mergeFrom(value).buildPartial();
        } else {
          remoteIntersection_ = value;
        }
        onChanged();
      } else {
        remoteIntersectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 节点属性ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public Builder clearRemoteIntersection() {
      if (remoteIntersectionBuilder_ == null) {
        remoteIntersection_ = null;
        onChanged();
      } else {
        remoteIntersection_ = null;
        remoteIntersectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 节点属性ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getRemoteIntersectionBuilder() {
      
      onChanged();
      return getRemoteIntersectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 节点属性ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getRemoteIntersectionOrBuilder() {
      if (remoteIntersectionBuilder_ != null) {
        return remoteIntersectionBuilder_.getMessageOrBuilder();
      } else {
        return remoteIntersection_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : remoteIntersection_;
      }
    }
    /**
     * <pre>
     * 节点属性ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getRemoteIntersectionFieldBuilder() {
      if (remoteIntersectionBuilder_ == null) {
        remoteIntersectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getRemoteIntersection(),
                getParentForChildren(),
                isClean());
        remoteIntersection_ = null;
      }
      return remoteIntersectionBuilder_;
    }

    private road.data.proto.ConnectingLane connectingLane_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ConnectingLane, road.data.proto.ConnectingLane.Builder, road.data.proto.ConnectingLaneOrBuilder> connectingLaneBuilder_;
    /**
     * <pre>
     * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
     */
    public boolean hasConnectingLane() {
      return connectingLaneBuilder_ != null || connectingLane_ != null;
    }
    /**
     * <pre>
     * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
     */
    public road.data.proto.ConnectingLane getConnectingLane() {
      if (connectingLaneBuilder_ == null) {
        return connectingLane_ == null ? road.data.proto.ConnectingLane.getDefaultInstance() : connectingLane_;
      } else {
        return connectingLaneBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
     */
    public Builder setConnectingLane(road.data.proto.ConnectingLane value) {
      if (connectingLaneBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        connectingLane_ = value;
        onChanged();
      } else {
        connectingLaneBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
     */
    public Builder setConnectingLane(
        road.data.proto.ConnectingLane.Builder builderForValue) {
      if (connectingLaneBuilder_ == null) {
        connectingLane_ = builderForValue.build();
        onChanged();
      } else {
        connectingLaneBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
     */
    public Builder mergeConnectingLane(road.data.proto.ConnectingLane value) {
      if (connectingLaneBuilder_ == null) {
        if (connectingLane_ != null) {
          connectingLane_ =
            road.data.proto.ConnectingLane.newBuilder(connectingLane_).mergeFrom(value).buildPartial();
        } else {
          connectingLane_ = value;
        }
        onChanged();
      } else {
        connectingLaneBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
     */
    public Builder clearConnectingLane() {
      if (connectingLaneBuilder_ == null) {
        connectingLane_ = null;
        onChanged();
      } else {
        connectingLane_ = null;
        connectingLaneBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
     */
    public road.data.proto.ConnectingLane.Builder getConnectingLaneBuilder() {
      
      onChanged();
      return getConnectingLaneFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
     */
    public road.data.proto.ConnectingLaneOrBuilder getConnectingLaneOrBuilder() {
      if (connectingLaneBuilder_ != null) {
        return connectingLaneBuilder_.getMessageOrBuilder();
      } else {
        return connectingLane_ == null ?
            road.data.proto.ConnectingLane.getDefaultInstance() : connectingLane_;
      }
    }
    /**
     * <pre>
     * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ConnectingLane, road.data.proto.ConnectingLane.Builder, road.data.proto.ConnectingLaneOrBuilder> 
        getConnectingLaneFieldBuilder() {
      if (connectingLaneBuilder_ == null) {
        connectingLaneBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ConnectingLane, road.data.proto.ConnectingLane.Builder, road.data.proto.ConnectingLaneOrBuilder>(
                getConnectingLane(),
                getParentForChildren(),
                isClean());
        connectingLane_ = null;
      }
      return connectingLaneBuilder_;
    }

    private int phaseId_ ;
    /**
     * <pre>
     * 可选，对应的信号灯相位号，0值表示无效
     * </pre>
     *
     * <code>uint32 phaseId = 3;</code>
     */
    public int getPhaseId() {
      return phaseId_;
    }
    /**
     * <pre>
     * 可选，对应的信号灯相位号，0值表示无效
     * </pre>
     *
     * <code>uint32 phaseId = 3;</code>
     */
    public Builder setPhaseId(int value) {
      
      phaseId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，对应的信号灯相位号，0值表示无效
     * </pre>
     *
     * <code>uint32 phaseId = 3;</code>
     */
    public Builder clearPhaseId() {
      
      phaseId_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.Connection)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.Connection)
  private static final road.data.proto.Connection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.Connection();
  }

  public static road.data.proto.Connection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Connection>
      PARSER = new com.google.protobuf.AbstractParser<Connection>() {
    @java.lang.Override
    public Connection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Connection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Connection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Connection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.Connection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

