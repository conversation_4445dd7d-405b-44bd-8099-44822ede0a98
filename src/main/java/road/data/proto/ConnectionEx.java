// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车道与下游路段车道的连接关系扩展信息列表 ConnectionEx             
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ConnectionEx}
 */
public  final class ConnectionEx extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ConnectionEx)
    ConnectionExOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ConnectionEx.newBuilder() to construct.
  private ConnectionEx(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ConnectionEx() {
    connectionLane_ = java.util.Collections.emptyList();
    turnDirection_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ConnectionEx();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ConnectionEx(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (remoteIntersection_ != null) {
              subBuilder = remoteIntersection_.toBuilder();
            }
            remoteIntersection_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(remoteIntersection_);
              remoteIntersection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            road.data.proto.SignalWaitingLane.Builder subBuilder = null;
            if (swl_ != null) {
              subBuilder = swl_.toBuilder();
            }
            swl_ = input.readMessage(road.data.proto.SignalWaitingLane.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(swl_);
              swl_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              connectionLane_ = new java.util.ArrayList<road.data.proto.ConnectingLaneEx>();
              mutable_bitField0_ |= 0x00000001;
            }
            connectionLane_.add(
                input.readMessage(road.data.proto.ConnectingLaneEx.parser(), extensionRegistry));
            break;
          }
          case 32: {

            phaseId_ = input.readUInt32();
            break;
          }
          case 40: {
            int rawValue = input.readEnum();

            turnDirection_ = rawValue;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        connectionLane_ = java.util.Collections.unmodifiableList(connectionLane_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectionEx_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectionEx_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ConnectionEx.class, road.data.proto.ConnectionEx.Builder.class);
  }

  public static final int REMOTEINTERSECTION_FIELD_NUMBER = 1;
  private road.data.proto.NodeReferenceId remoteIntersection_;
  /**
   * <pre>
   *车道连接的链路的下游交叉点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  public boolean hasRemoteIntersection() {
    return remoteIntersection_ != null;
  }
  /**
   * <pre>
   *车道连接的链路的下游交叉点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  public road.data.proto.NodeReferenceId getRemoteIntersection() {
    return remoteIntersection_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : remoteIntersection_;
  }
  /**
   * <pre>
   *车道连接的链路的下游交叉点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getRemoteIntersectionOrBuilder() {
    return getRemoteIntersection();
  }

  public static final int SWL_FIELD_NUMBER = 2;
  private road.data.proto.SignalWaitingLane swl_;
  /**
   * <pre>
   *可选，特定信号灯相位的等待区域
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
   */
  public boolean hasSwl() {
    return swl_ != null;
  }
  /**
   * <pre>
   *可选，特定信号灯相位的等待区域
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
   */
  public road.data.proto.SignalWaitingLane getSwl() {
    return swl_ == null ? road.data.proto.SignalWaitingLane.getDefaultInstance() : swl_;
  }
  /**
   * <pre>
   *可选，特定信号灯相位的等待区域
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
   */
  public road.data.proto.SignalWaitingLaneOrBuilder getSwlOrBuilder() {
    return getSwl();
  }

  public static final int CONNECTIONLANE_FIELD_NUMBER = 3;
  private java.util.List<road.data.proto.ConnectingLaneEx> connectionLane_;
  /**
   * <pre>
   *可选，定位上游车道转向连接的下游车道的扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
   */
  public java.util.List<road.data.proto.ConnectingLaneEx> getConnectionLaneList() {
    return connectionLane_;
  }
  /**
   * <pre>
   *可选，定位上游车道转向连接的下游车道的扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
   */
  public java.util.List<? extends road.data.proto.ConnectingLaneExOrBuilder> 
      getConnectionLaneOrBuilderList() {
    return connectionLane_;
  }
  /**
   * <pre>
   *可选，定位上游车道转向连接的下游车道的扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
   */
  public int getConnectionLaneCount() {
    return connectionLane_.size();
  }
  /**
   * <pre>
   *可选，定位上游车道转向连接的下游车道的扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
   */
  public road.data.proto.ConnectingLaneEx getConnectionLane(int index) {
    return connectionLane_.get(index);
  }
  /**
   * <pre>
   *可选，定位上游车道转向连接的下游车道的扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
   */
  public road.data.proto.ConnectingLaneExOrBuilder getConnectionLaneOrBuilder(
      int index) {
    return connectionLane_.get(index);
  }

  public static final int PHASEID_FIELD_NUMBER = 4;
  private int phaseId_;
  /**
   * <pre>
   *可选，相位
   * </pre>
   *
   * <code>uint32 phaseId = 4;</code>
   */
  public int getPhaseId() {
    return phaseId_;
  }

  public static final int TURNDIRECTION_FIELD_NUMBER = 5;
  private int turnDirection_;
  /**
   * <pre>
   *可选，指示与此运动对应的转弯方向
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 5;</code>
   */
  public int getTurnDirectionValue() {
    return turnDirection_;
  }
  /**
   * <pre>
   *可选，指示与此运动对应的转弯方向
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 5;</code>
   */
  public road.data.proto.Maneuver getTurnDirection() {
    @SuppressWarnings("deprecation")
    road.data.proto.Maneuver result = road.data.proto.Maneuver.valueOf(turnDirection_);
    return result == null ? road.data.proto.Maneuver.UNRECOGNIZED : result;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (remoteIntersection_ != null) {
      output.writeMessage(1, getRemoteIntersection());
    }
    if (swl_ != null) {
      output.writeMessage(2, getSwl());
    }
    for (int i = 0; i < connectionLane_.size(); i++) {
      output.writeMessage(3, connectionLane_.get(i));
    }
    if (phaseId_ != 0) {
      output.writeUInt32(4, phaseId_);
    }
    if (turnDirection_ != road.data.proto.Maneuver.MANEUVER_STRAIGHT.getNumber()) {
      output.writeEnum(5, turnDirection_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (remoteIntersection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getRemoteIntersection());
    }
    if (swl_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getSwl());
    }
    for (int i = 0; i < connectionLane_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, connectionLane_.get(i));
    }
    if (phaseId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, phaseId_);
    }
    if (turnDirection_ != road.data.proto.Maneuver.MANEUVER_STRAIGHT.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(5, turnDirection_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ConnectionEx)) {
      return super.equals(obj);
    }
    road.data.proto.ConnectionEx other = (road.data.proto.ConnectionEx) obj;

    if (hasRemoteIntersection() != other.hasRemoteIntersection()) return false;
    if (hasRemoteIntersection()) {
      if (!getRemoteIntersection()
          .equals(other.getRemoteIntersection())) return false;
    }
    if (hasSwl() != other.hasSwl()) return false;
    if (hasSwl()) {
      if (!getSwl()
          .equals(other.getSwl())) return false;
    }
    if (!getConnectionLaneList()
        .equals(other.getConnectionLaneList())) return false;
    if (getPhaseId()
        != other.getPhaseId()) return false;
    if (turnDirection_ != other.turnDirection_) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRemoteIntersection()) {
      hash = (37 * hash) + REMOTEINTERSECTION_FIELD_NUMBER;
      hash = (53 * hash) + getRemoteIntersection().hashCode();
    }
    if (hasSwl()) {
      hash = (37 * hash) + SWL_FIELD_NUMBER;
      hash = (53 * hash) + getSwl().hashCode();
    }
    if (getConnectionLaneCount() > 0) {
      hash = (37 * hash) + CONNECTIONLANE_FIELD_NUMBER;
      hash = (53 * hash) + getConnectionLaneList().hashCode();
    }
    hash = (37 * hash) + PHASEID_FIELD_NUMBER;
    hash = (53 * hash) + getPhaseId();
    hash = (37 * hash) + TURNDIRECTION_FIELD_NUMBER;
    hash = (53 * hash) + turnDirection_;
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ConnectionEx parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ConnectionEx parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ConnectionEx parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ConnectionEx parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ConnectionEx parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ConnectionEx parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ConnectionEx parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ConnectionEx parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ConnectionEx parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ConnectionEx parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ConnectionEx parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ConnectionEx parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ConnectionEx prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *车道与下游路段车道的连接关系扩展信息列表 ConnectionEx             
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ConnectionEx}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ConnectionEx)
      road.data.proto.ConnectionExOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectionEx_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectionEx_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ConnectionEx.class, road.data.proto.ConnectionEx.Builder.class);
    }

    // Construct using road.data.proto.ConnectionEx.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getConnectionLaneFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (remoteIntersectionBuilder_ == null) {
        remoteIntersection_ = null;
      } else {
        remoteIntersection_ = null;
        remoteIntersectionBuilder_ = null;
      }
      if (swlBuilder_ == null) {
        swl_ = null;
      } else {
        swl_ = null;
        swlBuilder_ = null;
      }
      if (connectionLaneBuilder_ == null) {
        connectionLane_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        connectionLaneBuilder_.clear();
      }
      phaseId_ = 0;

      turnDirection_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ConnectionEx_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ConnectionEx getDefaultInstanceForType() {
      return road.data.proto.ConnectionEx.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ConnectionEx build() {
      road.data.proto.ConnectionEx result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ConnectionEx buildPartial() {
      road.data.proto.ConnectionEx result = new road.data.proto.ConnectionEx(this);
      int from_bitField0_ = bitField0_;
      if (remoteIntersectionBuilder_ == null) {
        result.remoteIntersection_ = remoteIntersection_;
      } else {
        result.remoteIntersection_ = remoteIntersectionBuilder_.build();
      }
      if (swlBuilder_ == null) {
        result.swl_ = swl_;
      } else {
        result.swl_ = swlBuilder_.build();
      }
      if (connectionLaneBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          connectionLane_ = java.util.Collections.unmodifiableList(connectionLane_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.connectionLane_ = connectionLane_;
      } else {
        result.connectionLane_ = connectionLaneBuilder_.build();
      }
      result.phaseId_ = phaseId_;
      result.turnDirection_ = turnDirection_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ConnectionEx) {
        return mergeFrom((road.data.proto.ConnectionEx)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ConnectionEx other) {
      if (other == road.data.proto.ConnectionEx.getDefaultInstance()) return this;
      if (other.hasRemoteIntersection()) {
        mergeRemoteIntersection(other.getRemoteIntersection());
      }
      if (other.hasSwl()) {
        mergeSwl(other.getSwl());
      }
      if (connectionLaneBuilder_ == null) {
        if (!other.connectionLane_.isEmpty()) {
          if (connectionLane_.isEmpty()) {
            connectionLane_ = other.connectionLane_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureConnectionLaneIsMutable();
            connectionLane_.addAll(other.connectionLane_);
          }
          onChanged();
        }
      } else {
        if (!other.connectionLane_.isEmpty()) {
          if (connectionLaneBuilder_.isEmpty()) {
            connectionLaneBuilder_.dispose();
            connectionLaneBuilder_ = null;
            connectionLane_ = other.connectionLane_;
            bitField0_ = (bitField0_ & ~0x00000001);
            connectionLaneBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getConnectionLaneFieldBuilder() : null;
          } else {
            connectionLaneBuilder_.addAllMessages(other.connectionLane_);
          }
        }
      }
      if (other.getPhaseId() != 0) {
        setPhaseId(other.getPhaseId());
      }
      if (other.turnDirection_ != 0) {
        setTurnDirectionValue(other.getTurnDirectionValue());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ConnectionEx parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ConnectionEx) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private road.data.proto.NodeReferenceId remoteIntersection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> remoteIntersectionBuilder_;
    /**
     * <pre>
     *车道连接的链路的下游交叉点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public boolean hasRemoteIntersection() {
      return remoteIntersectionBuilder_ != null || remoteIntersection_ != null;
    }
    /**
     * <pre>
     *车道连接的链路的下游交叉点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public road.data.proto.NodeReferenceId getRemoteIntersection() {
      if (remoteIntersectionBuilder_ == null) {
        return remoteIntersection_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : remoteIntersection_;
      } else {
        return remoteIntersectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *车道连接的链路的下游交叉点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public Builder setRemoteIntersection(road.data.proto.NodeReferenceId value) {
      if (remoteIntersectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        remoteIntersection_ = value;
        onChanged();
      } else {
        remoteIntersectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *车道连接的链路的下游交叉点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public Builder setRemoteIntersection(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (remoteIntersectionBuilder_ == null) {
        remoteIntersection_ = builderForValue.build();
        onChanged();
      } else {
        remoteIntersectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *车道连接的链路的下游交叉点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public Builder mergeRemoteIntersection(road.data.proto.NodeReferenceId value) {
      if (remoteIntersectionBuilder_ == null) {
        if (remoteIntersection_ != null) {
          remoteIntersection_ =
            road.data.proto.NodeReferenceId.newBuilder(remoteIntersection_).mergeFrom(value).buildPartial();
        } else {
          remoteIntersection_ = value;
        }
        onChanged();
      } else {
        remoteIntersectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *车道连接的链路的下游交叉点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public Builder clearRemoteIntersection() {
      if (remoteIntersectionBuilder_ == null) {
        remoteIntersection_ = null;
        onChanged();
      } else {
        remoteIntersection_ = null;
        remoteIntersectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *车道连接的链路的下游交叉点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getRemoteIntersectionBuilder() {
      
      onChanged();
      return getRemoteIntersectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *车道连接的链路的下游交叉点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getRemoteIntersectionOrBuilder() {
      if (remoteIntersectionBuilder_ != null) {
        return remoteIntersectionBuilder_.getMessageOrBuilder();
      } else {
        return remoteIntersection_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : remoteIntersection_;
      }
    }
    /**
     * <pre>
     *车道连接的链路的下游交叉点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getRemoteIntersectionFieldBuilder() {
      if (remoteIntersectionBuilder_ == null) {
        remoteIntersectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getRemoteIntersection(),
                getParentForChildren(),
                isClean());
        remoteIntersection_ = null;
      }
      return remoteIntersectionBuilder_;
    }

    private road.data.proto.SignalWaitingLane swl_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.SignalWaitingLane, road.data.proto.SignalWaitingLane.Builder, road.data.proto.SignalWaitingLaneOrBuilder> swlBuilder_;
    /**
     * <pre>
     *可选，特定信号灯相位的等待区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
     */
    public boolean hasSwl() {
      return swlBuilder_ != null || swl_ != null;
    }
    /**
     * <pre>
     *可选，特定信号灯相位的等待区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
     */
    public road.data.proto.SignalWaitingLane getSwl() {
      if (swlBuilder_ == null) {
        return swl_ == null ? road.data.proto.SignalWaitingLane.getDefaultInstance() : swl_;
      } else {
        return swlBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，特定信号灯相位的等待区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
     */
    public Builder setSwl(road.data.proto.SignalWaitingLane value) {
      if (swlBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        swl_ = value;
        onChanged();
      } else {
        swlBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，特定信号灯相位的等待区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
     */
    public Builder setSwl(
        road.data.proto.SignalWaitingLane.Builder builderForValue) {
      if (swlBuilder_ == null) {
        swl_ = builderForValue.build();
        onChanged();
      } else {
        swlBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，特定信号灯相位的等待区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
     */
    public Builder mergeSwl(road.data.proto.SignalWaitingLane value) {
      if (swlBuilder_ == null) {
        if (swl_ != null) {
          swl_ =
            road.data.proto.SignalWaitingLane.newBuilder(swl_).mergeFrom(value).buildPartial();
        } else {
          swl_ = value;
        }
        onChanged();
      } else {
        swlBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，特定信号灯相位的等待区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
     */
    public Builder clearSwl() {
      if (swlBuilder_ == null) {
        swl_ = null;
        onChanged();
      } else {
        swl_ = null;
        swlBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，特定信号灯相位的等待区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
     */
    public road.data.proto.SignalWaitingLane.Builder getSwlBuilder() {
      
      onChanged();
      return getSwlFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，特定信号灯相位的等待区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
     */
    public road.data.proto.SignalWaitingLaneOrBuilder getSwlOrBuilder() {
      if (swlBuilder_ != null) {
        return swlBuilder_.getMessageOrBuilder();
      } else {
        return swl_ == null ?
            road.data.proto.SignalWaitingLane.getDefaultInstance() : swl_;
      }
    }
    /**
     * <pre>
     *可选，特定信号灯相位的等待区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.SignalWaitingLane, road.data.proto.SignalWaitingLane.Builder, road.data.proto.SignalWaitingLaneOrBuilder> 
        getSwlFieldBuilder() {
      if (swlBuilder_ == null) {
        swlBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.SignalWaitingLane, road.data.proto.SignalWaitingLane.Builder, road.data.proto.SignalWaitingLaneOrBuilder>(
                getSwl(),
                getParentForChildren(),
                isClean());
        swl_ = null;
      }
      return swlBuilder_;
    }

    private java.util.List<road.data.proto.ConnectingLaneEx> connectionLane_ =
      java.util.Collections.emptyList();
    private void ensureConnectionLaneIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        connectionLane_ = new java.util.ArrayList<road.data.proto.ConnectingLaneEx>(connectionLane_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ConnectingLaneEx, road.data.proto.ConnectingLaneEx.Builder, road.data.proto.ConnectingLaneExOrBuilder> connectionLaneBuilder_;

    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public java.util.List<road.data.proto.ConnectingLaneEx> getConnectionLaneList() {
      if (connectionLaneBuilder_ == null) {
        return java.util.Collections.unmodifiableList(connectionLane_);
      } else {
        return connectionLaneBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public int getConnectionLaneCount() {
      if (connectionLaneBuilder_ == null) {
        return connectionLane_.size();
      } else {
        return connectionLaneBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public road.data.proto.ConnectingLaneEx getConnectionLane(int index) {
      if (connectionLaneBuilder_ == null) {
        return connectionLane_.get(index);
      } else {
        return connectionLaneBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public Builder setConnectionLane(
        int index, road.data.proto.ConnectingLaneEx value) {
      if (connectionLaneBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConnectionLaneIsMutable();
        connectionLane_.set(index, value);
        onChanged();
      } else {
        connectionLaneBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public Builder setConnectionLane(
        int index, road.data.proto.ConnectingLaneEx.Builder builderForValue) {
      if (connectionLaneBuilder_ == null) {
        ensureConnectionLaneIsMutable();
        connectionLane_.set(index, builderForValue.build());
        onChanged();
      } else {
        connectionLaneBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public Builder addConnectionLane(road.data.proto.ConnectingLaneEx value) {
      if (connectionLaneBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConnectionLaneIsMutable();
        connectionLane_.add(value);
        onChanged();
      } else {
        connectionLaneBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public Builder addConnectionLane(
        int index, road.data.proto.ConnectingLaneEx value) {
      if (connectionLaneBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConnectionLaneIsMutable();
        connectionLane_.add(index, value);
        onChanged();
      } else {
        connectionLaneBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public Builder addConnectionLane(
        road.data.proto.ConnectingLaneEx.Builder builderForValue) {
      if (connectionLaneBuilder_ == null) {
        ensureConnectionLaneIsMutable();
        connectionLane_.add(builderForValue.build());
        onChanged();
      } else {
        connectionLaneBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public Builder addConnectionLane(
        int index, road.data.proto.ConnectingLaneEx.Builder builderForValue) {
      if (connectionLaneBuilder_ == null) {
        ensureConnectionLaneIsMutable();
        connectionLane_.add(index, builderForValue.build());
        onChanged();
      } else {
        connectionLaneBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public Builder addAllConnectionLane(
        java.lang.Iterable<? extends road.data.proto.ConnectingLaneEx> values) {
      if (connectionLaneBuilder_ == null) {
        ensureConnectionLaneIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, connectionLane_);
        onChanged();
      } else {
        connectionLaneBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public Builder clearConnectionLane() {
      if (connectionLaneBuilder_ == null) {
        connectionLane_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        connectionLaneBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public Builder removeConnectionLane(int index) {
      if (connectionLaneBuilder_ == null) {
        ensureConnectionLaneIsMutable();
        connectionLane_.remove(index);
        onChanged();
      } else {
        connectionLaneBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public road.data.proto.ConnectingLaneEx.Builder getConnectionLaneBuilder(
        int index) {
      return getConnectionLaneFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public road.data.proto.ConnectingLaneExOrBuilder getConnectionLaneOrBuilder(
        int index) {
      if (connectionLaneBuilder_ == null) {
        return connectionLane_.get(index);  } else {
        return connectionLaneBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public java.util.List<? extends road.data.proto.ConnectingLaneExOrBuilder> 
         getConnectionLaneOrBuilderList() {
      if (connectionLaneBuilder_ != null) {
        return connectionLaneBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(connectionLane_);
      }
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public road.data.proto.ConnectingLaneEx.Builder addConnectionLaneBuilder() {
      return getConnectionLaneFieldBuilder().addBuilder(
          road.data.proto.ConnectingLaneEx.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public road.data.proto.ConnectingLaneEx.Builder addConnectionLaneBuilder(
        int index) {
      return getConnectionLaneFieldBuilder().addBuilder(
          index, road.data.proto.ConnectingLaneEx.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，定位上游车道转向连接的下游车道的扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
     */
    public java.util.List<road.data.proto.ConnectingLaneEx.Builder> 
         getConnectionLaneBuilderList() {
      return getConnectionLaneFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ConnectingLaneEx, road.data.proto.ConnectingLaneEx.Builder, road.data.proto.ConnectingLaneExOrBuilder> 
        getConnectionLaneFieldBuilder() {
      if (connectionLaneBuilder_ == null) {
        connectionLaneBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.ConnectingLaneEx, road.data.proto.ConnectingLaneEx.Builder, road.data.proto.ConnectingLaneExOrBuilder>(
                connectionLane_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        connectionLane_ = null;
      }
      return connectionLaneBuilder_;
    }

    private int phaseId_ ;
    /**
     * <pre>
     *可选，相位
     * </pre>
     *
     * <code>uint32 phaseId = 4;</code>
     */
    public int getPhaseId() {
      return phaseId_;
    }
    /**
     * <pre>
     *可选，相位
     * </pre>
     *
     * <code>uint32 phaseId = 4;</code>
     */
    public Builder setPhaseId(int value) {
      
      phaseId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，相位
     * </pre>
     *
     * <code>uint32 phaseId = 4;</code>
     */
    public Builder clearPhaseId() {
      
      phaseId_ = 0;
      onChanged();
      return this;
    }

    private int turnDirection_ = 0;
    /**
     * <pre>
     *可选，指示与此运动对应的转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 5;</code>
     */
    public int getTurnDirectionValue() {
      return turnDirection_;
    }
    /**
     * <pre>
     *可选，指示与此运动对应的转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 5;</code>
     */
    public Builder setTurnDirectionValue(int value) {
      turnDirection_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，指示与此运动对应的转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 5;</code>
     */
    public road.data.proto.Maneuver getTurnDirection() {
      @SuppressWarnings("deprecation")
      road.data.proto.Maneuver result = road.data.proto.Maneuver.valueOf(turnDirection_);
      return result == null ? road.data.proto.Maneuver.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，指示与此运动对应的转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 5;</code>
     */
    public Builder setTurnDirection(road.data.proto.Maneuver value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      turnDirection_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，指示与此运动对应的转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 5;</code>
     */
    public Builder clearTurnDirection() {
      
      turnDirection_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ConnectionEx)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ConnectionEx)
  private static final road.data.proto.ConnectionEx DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ConnectionEx();
  }

  public static road.data.proto.ConnectionEx getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ConnectionEx>
      PARSER = new com.google.protobuf.AbstractParser<ConnectionEx>() {
    @java.lang.Override
    public ConnectionEx parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ConnectionEx(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ConnectionEx> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ConnectionEx> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ConnectionEx getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

