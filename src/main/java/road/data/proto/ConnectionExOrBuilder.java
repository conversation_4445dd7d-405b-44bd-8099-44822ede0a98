// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ConnectionExOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ConnectionEx)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *车道连接的链路的下游交叉点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  boolean hasRemoteIntersection();
  /**
   * <pre>
   *车道连接的链路的下游交叉点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  road.data.proto.NodeReferenceId getRemoteIntersection();
  /**
   * <pre>
   *车道连接的链路的下游交叉点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getRemoteIntersectionOrBuilder();

  /**
   * <pre>
   *可选，特定信号灯相位的等待区域
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
   */
  boolean hasSwl();
  /**
   * <pre>
   *可选，特定信号灯相位的等待区域
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
   */
  road.data.proto.SignalWaitingLane getSwl();
  /**
   * <pre>
   *可选，特定信号灯相位的等待区域
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SignalWaitingLane swl = 2;</code>
   */
  road.data.proto.SignalWaitingLaneOrBuilder getSwlOrBuilder();

  /**
   * <pre>
   *可选，定位上游车道转向连接的下游车道的扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
   */
  java.util.List<road.data.proto.ConnectingLaneEx> 
      getConnectionLaneList();
  /**
   * <pre>
   *可选，定位上游车道转向连接的下游车道的扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
   */
  road.data.proto.ConnectingLaneEx getConnectionLane(int index);
  /**
   * <pre>
   *可选，定位上游车道转向连接的下游车道的扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
   */
  int getConnectionLaneCount();
  /**
   * <pre>
   *可选，定位上游车道转向连接的下游车道的扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
   */
  java.util.List<? extends road.data.proto.ConnectingLaneExOrBuilder> 
      getConnectionLaneOrBuilderList();
  /**
   * <pre>
   *可选，定位上游车道转向连接的下游车道的扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectingLaneEx connectionLane = 3;</code>
   */
  road.data.proto.ConnectingLaneExOrBuilder getConnectionLaneOrBuilder(
      int index);

  /**
   * <pre>
   *可选，相位
   * </pre>
   *
   * <code>uint32 phaseId = 4;</code>
   */
  int getPhaseId();

  /**
   * <pre>
   *可选，指示与此运动对应的转弯方向
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 5;</code>
   */
  int getTurnDirectionValue();
  /**
   * <pre>
   *可选，指示与此运动对应的转弯方向
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 5;</code>
   */
  road.data.proto.Maneuver getTurnDirection();
}
