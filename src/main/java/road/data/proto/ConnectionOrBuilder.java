// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ConnectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.Connection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 节点属性ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  boolean hasRemoteIntersection();
  /**
   * <pre>
   * 节点属性ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  road.data.proto.NodeReferenceId getRemoteIntersection();
  /**
   * <pre>
   * 节点属性ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getRemoteIntersectionOrBuilder();

  /**
   * <pre>
   * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
   */
  boolean hasConnectingLane();
  /**
   * <pre>
   * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
   */
  road.data.proto.ConnectingLane getConnectingLane();
  /**
   * <pre>
   * 可选，用于定位上游车道转向连接的下游车道。包括下游车道ID以及该转向的允许行驶行为下游车道ID的作用范围是该车道所在的路段。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ConnectingLane connectingLane = 2;</code>
   */
  road.data.proto.ConnectingLaneOrBuilder getConnectingLaneOrBuilder();

  /**
   * <pre>
   * 可选，对应的信号灯相位号，0值表示无效
   * </pre>
   *
   * <code>uint32 phaseId = 3;</code>
   */
  int getPhaseId();
}
