// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *数据来源  
 * </pre>
 *
 * Protobuf enum {@code cn.seisys.v2x.pb.DataSource}
 */
public enum DataSource
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   * 未知数据源类型;
   * </pre>
   *
   * <code>DATA_SOURCE_UNKNOWN = 0;</code>
   */
  DATA_SOURCE_UNKNOWN(0),
  /**
   * <pre>
   * RSU 自身信息;
   * </pre>
   *
   * <code>SELFINFO = 1;</code>
   */
  SELFINFO(1),
  /**
   * <pre>
   * 来源于参与者自身的V2X广播消息;
   * </pre>
   *
   * <code>V2X = 2;</code>
   */
  V2X(2),
  /**
   * <pre>
   * 来源于视频传感器;
   * </pre>
   *
   * <code>VIDEO = 3;</code>
   */
  VIDEO(3),
  /**
   * <pre>
   * 来源于微波雷达传感器;
   * </pre>
   *
   * <code>MICROWAVE_RADAR = 4;</code>
   */
  MICROWAVE_RADAR(4),
  /**
   * <pre>
   * 来源于地磁线圈传感器;
   * </pre>
   *
   * <code>LOOP = 5;</code>
   */
  LOOP(5),
  /**
   * <pre>
   * 来源于激光雷达传感器;
   * </pre>
   *
   * <code>LIDAR = 6;</code>
   */
  LIDAR(6),
  /**
   * <pre>
   * 2 类或以上感知数据的融合结果;
   * </pre>
   *
   * <code>INTEGRATED = 7;</code>
   */
  INTEGRATED(7),
  /**
   * <pre>
   * 保留
   * </pre>
   *
   * <code>DATA_SOURCE_RESERVE = 8;</code>
   */
  DATA_SOURCE_RESERVE(8),
  /**
   * <pre>
   *云端转发
   * </pre>
   *
   * <code>CLOUD_FORWARDING = 9;</code>
   */
  CLOUD_FORWARDING(9),
  /**
   * <pre>
   *路侧自动转发
   * </pre>
   *
   * <code>MEC_TO_MEC = 10;</code>
   */
  MEC_TO_MEC(10),
  /**
   * <pre>
   *云端自动转发
   * </pre>
   *
   * <code>CLOUD_TO_CLOUD = 11;</code>
   */
  CLOUD_TO_CLOUD(11),
  /**
   * <pre>
   *云端人工下发
   * </pre>
   *
   * <code>CLOUD_MANUAL = 12;</code>
   */
  CLOUD_MANUAL(12),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   * 未知数据源类型;
   * </pre>
   *
   * <code>DATA_SOURCE_UNKNOWN = 0;</code>
   */
  public static final int DATA_SOURCE_UNKNOWN_VALUE = 0;
  /**
   * <pre>
   * RSU 自身信息;
   * </pre>
   *
   * <code>SELFINFO = 1;</code>
   */
  public static final int SELFINFO_VALUE = 1;
  /**
   * <pre>
   * 来源于参与者自身的V2X广播消息;
   * </pre>
   *
   * <code>V2X = 2;</code>
   */
  public static final int V2X_VALUE = 2;
  /**
   * <pre>
   * 来源于视频传感器;
   * </pre>
   *
   * <code>VIDEO = 3;</code>
   */
  public static final int VIDEO_VALUE = 3;
  /**
   * <pre>
   * 来源于微波雷达传感器;
   * </pre>
   *
   * <code>MICROWAVE_RADAR = 4;</code>
   */
  public static final int MICROWAVE_RADAR_VALUE = 4;
  /**
   * <pre>
   * 来源于地磁线圈传感器;
   * </pre>
   *
   * <code>LOOP = 5;</code>
   */
  public static final int LOOP_VALUE = 5;
  /**
   * <pre>
   * 来源于激光雷达传感器;
   * </pre>
   *
   * <code>LIDAR = 6;</code>
   */
  public static final int LIDAR_VALUE = 6;
  /**
   * <pre>
   * 2 类或以上感知数据的融合结果;
   * </pre>
   *
   * <code>INTEGRATED = 7;</code>
   */
  public static final int INTEGRATED_VALUE = 7;
  /**
   * <pre>
   * 保留
   * </pre>
   *
   * <code>DATA_SOURCE_RESERVE = 8;</code>
   */
  public static final int DATA_SOURCE_RESERVE_VALUE = 8;
  /**
   * <pre>
   *云端转发
   * </pre>
   *
   * <code>CLOUD_FORWARDING = 9;</code>
   */
  public static final int CLOUD_FORWARDING_VALUE = 9;
  /**
   * <pre>
   *路侧自动转发
   * </pre>
   *
   * <code>MEC_TO_MEC = 10;</code>
   */
  public static final int MEC_TO_MEC_VALUE = 10;
  /**
   * <pre>
   *云端自动转发
   * </pre>
   *
   * <code>CLOUD_TO_CLOUD = 11;</code>
   */
  public static final int CLOUD_TO_CLOUD_VALUE = 11;
  /**
   * <pre>
   *云端人工下发
   * </pre>
   *
   * <code>CLOUD_MANUAL = 12;</code>
   */
  public static final int CLOUD_MANUAL_VALUE = 12;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static DataSource valueOf(int value) {
    return forNumber(value);
  }

  public static DataSource forNumber(int value) {
    switch (value) {
      case 0: return DATA_SOURCE_UNKNOWN;
      case 1: return SELFINFO;
      case 2: return V2X;
      case 3: return VIDEO;
      case 4: return MICROWAVE_RADAR;
      case 5: return LOOP;
      case 6: return LIDAR;
      case 7: return INTEGRATED;
      case 8: return DATA_SOURCE_RESERVE;
      case 9: return CLOUD_FORWARDING;
      case 10: return MEC_TO_MEC;
      case 11: return CLOUD_TO_CLOUD;
      case 12: return CLOUD_MANUAL;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<DataSource>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      DataSource> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<DataSource>() {
          public DataSource findValueByNumber(int number) {
            return DataSource.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return road.data.proto.V2X.getDescriptor().getEnumTypes().get(1);
  }

  private static final DataSource[] VALUES = values();

  public static DataSource valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private DataSource(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.DataSource)
}

