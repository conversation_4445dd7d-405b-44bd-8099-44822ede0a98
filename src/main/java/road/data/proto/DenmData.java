// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *V2X分布式环境通知消息DENM 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.DenmData}
 */
public  final class DenmData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.DenmData)
    DenmDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use DenmData.newBuilder() to construct.
  private DenmData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private DenmData() {
    ver_ = "";
    address_ = "";
    sceneType_ = 0;
    statusList_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new DenmData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private DenmData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            type_ = input.readUInt32();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            ver_ = s;
            break;
          }
          case 24: {

            msgCnt_ = input.readUInt32();
            break;
          }
          case 32: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            address_ = s;
            break;
          }
          case 50: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (refPos_ != null) {
              subBuilder = refPos_.toBuilder();
            }
            refPos_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(refPos_);
              refPos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 56: {
            int rawValue = input.readEnum();

            sceneType_ = rawValue;
            break;
          }
          case 66: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              statusList_ = new java.util.ArrayList<road.data.proto.StatusData>();
              mutable_bitField0_ |= 0x00000001;
            }
            statusList_.add(
                input.readMessage(road.data.proto.StatusData.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        statusList_ = java.util.Collections.unmodifiableList(statusList_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DenmData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DenmData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.DenmData.class, road.data.proto.DenmData.Builder.class);
  }

  public static final int TYPE_FIELD_NUMBER = 1;
  private int type_;
  /**
   * <pre>
   * type取值为2，表示MEC向RSU发送的心跳状态消息
   * </pre>
   *
   * <code>uint32 type = 1;</code>
   */
  public int getType() {
    return type_;
  }

  public static final int VER_FIELD_NUMBER = 2;
  private volatile java.lang.Object ver_;
  /**
   * <pre>
   * 版本号，目前版本固定为“01”
   * </pre>
   *
   * <code>string ver = 2;</code>
   */
  public java.lang.String getVer() {
    java.lang.Object ref = ver_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      ver_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 版本号，目前版本固定为“01”
   * </pre>
   *
   * <code>string ver = 2;</code>
   */
  public com.google.protobuf.ByteString
      getVerBytes() {
    java.lang.Object ref = ver_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      ver_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MSGCNT_FIELD_NUMBER = 3;
  private int msgCnt_;
  /**
   * <pre>
   * 定义消息编号。发送方对发送的同类消息(type=2)依次进行编号。编号循环发送。
   * </pre>
   *
   * <code>uint32 msgCnt = 3;</code>
   */
  public int getMsgCnt() {
    return msgCnt_;
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 4;
  private long timestamp_;
  /**
   * <pre>
   * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 4;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int ADDRESS_FIELD_NUMBER = 5;
  private volatile java.lang.Object address_;
  /**
   * <pre>
   * 设备所在位置 (a) 提供ASCII字符文本形式;(b) 提供中文编码形式，符合GB2312_80的编码规则，一个字有2字节信息编码。
   * </pre>
   *
   * <code>string address = 5;</code>
   */
  public java.lang.String getAddress() {
    java.lang.Object ref = address_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      address_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 设备所在位置 (a) 提供ASCII字符文本形式;(b) 提供中文编码形式，符合GB2312_80的编码规则，一个字有2字节信息编码。
   * </pre>
   *
   * <code>string address = 5;</code>
   */
  public com.google.protobuf.ByteString
      getAddressBytes() {
    java.lang.Object ref = address_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      address_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REFPOS_FIELD_NUMBER = 6;
  private road.data.proto.Position3D refPos_;
  /**
   * <pre>
   * 位置基准参考点,绝对坐标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
   */
  public boolean hasRefPos() {
    return refPos_ != null;
  }
  /**
   * <pre>
   * 位置基准参考点,绝对坐标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
   */
  public road.data.proto.Position3D getRefPos() {
    return refPos_ == null ? road.data.proto.Position3D.getDefaultInstance() : refPos_;
  }
  /**
   * <pre>
   * 位置基准参考点,绝对坐标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
   */
  public road.data.proto.Position3DOrBuilder getRefPosOrBuilder() {
    return getRefPos();
  }

  public static final int SCENETYPE_FIELD_NUMBER = 7;
  private int sceneType_;
  /**
   * <pre>
   * 表示场地类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SceneType sceneType = 7;</code>
   */
  public int getSceneTypeValue() {
    return sceneType_;
  }
  /**
   * <pre>
   * 表示场地类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SceneType sceneType = 7;</code>
   */
  public road.data.proto.SceneType getSceneType() {
    @SuppressWarnings("deprecation")
    road.data.proto.SceneType result = road.data.proto.SceneType.valueOf(sceneType_);
    return result == null ? road.data.proto.SceneType.UNRECOGNIZED : result;
  }

  public static final int STATUSLIST_FIELD_NUMBER = 8;
  private java.util.List<road.data.proto.StatusData> statusList_;
  /**
   * <pre>
   *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
   */
  public java.util.List<road.data.proto.StatusData> getStatusListList() {
    return statusList_;
  }
  /**
   * <pre>
   *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
   */
  public java.util.List<? extends road.data.proto.StatusDataOrBuilder> 
      getStatusListOrBuilderList() {
    return statusList_;
  }
  /**
   * <pre>
   *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
   */
  public int getStatusListCount() {
    return statusList_.size();
  }
  /**
   * <pre>
   *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
   */
  public road.data.proto.StatusData getStatusList(int index) {
    return statusList_.get(index);
  }
  /**
   * <pre>
   *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
   */
  public road.data.proto.StatusDataOrBuilder getStatusListOrBuilder(
      int index) {
    return statusList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (type_ != 0) {
      output.writeUInt32(1, type_);
    }
    if (!getVerBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, ver_);
    }
    if (msgCnt_ != 0) {
      output.writeUInt32(3, msgCnt_);
    }
    if (timestamp_ != 0L) {
      output.writeUInt64(4, timestamp_);
    }
    if (!getAddressBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, address_);
    }
    if (refPos_ != null) {
      output.writeMessage(6, getRefPos());
    }
    if (sceneType_ != road.data.proto.SceneType.SCENE_TYPE_URBAN.getNumber()) {
      output.writeEnum(7, sceneType_);
    }
    for (int i = 0; i < statusList_.size(); i++) {
      output.writeMessage(8, statusList_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (type_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, type_);
    }
    if (!getVerBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, ver_);
    }
    if (msgCnt_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, msgCnt_);
    }
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(4, timestamp_);
    }
    if (!getAddressBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, address_);
    }
    if (refPos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getRefPos());
    }
    if (sceneType_ != road.data.proto.SceneType.SCENE_TYPE_URBAN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(7, sceneType_);
    }
    for (int i = 0; i < statusList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, statusList_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.DenmData)) {
      return super.equals(obj);
    }
    road.data.proto.DenmData other = (road.data.proto.DenmData) obj;

    if (getType()
        != other.getType()) return false;
    if (!getVer()
        .equals(other.getVer())) return false;
    if (getMsgCnt()
        != other.getMsgCnt()) return false;
    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (!getAddress()
        .equals(other.getAddress())) return false;
    if (hasRefPos() != other.hasRefPos()) return false;
    if (hasRefPos()) {
      if (!getRefPos()
          .equals(other.getRefPos())) return false;
    }
    if (sceneType_ != other.sceneType_) return false;
    if (!getStatusListList()
        .equals(other.getStatusListList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getType();
    hash = (37 * hash) + VER_FIELD_NUMBER;
    hash = (53 * hash) + getVer().hashCode();
    hash = (37 * hash) + MSGCNT_FIELD_NUMBER;
    hash = (53 * hash) + getMsgCnt();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    hash = (37 * hash) + ADDRESS_FIELD_NUMBER;
    hash = (53 * hash) + getAddress().hashCode();
    if (hasRefPos()) {
      hash = (37 * hash) + REFPOS_FIELD_NUMBER;
      hash = (53 * hash) + getRefPos().hashCode();
    }
    hash = (37 * hash) + SCENETYPE_FIELD_NUMBER;
    hash = (53 * hash) + sceneType_;
    if (getStatusListCount() > 0) {
      hash = (37 * hash) + STATUSLIST_FIELD_NUMBER;
      hash = (53 * hash) + getStatusListList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.DenmData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DenmData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DenmData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DenmData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DenmData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DenmData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DenmData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.DenmData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.DenmData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.DenmData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.DenmData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.DenmData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.DenmData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *V2X分布式环境通知消息DENM 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.DenmData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.DenmData)
      road.data.proto.DenmDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DenmData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DenmData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.DenmData.class, road.data.proto.DenmData.Builder.class);
    }

    // Construct using road.data.proto.DenmData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getStatusListFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      type_ = 0;

      ver_ = "";

      msgCnt_ = 0;

      timestamp_ = 0L;

      address_ = "";

      if (refPosBuilder_ == null) {
        refPos_ = null;
      } else {
        refPos_ = null;
        refPosBuilder_ = null;
      }
      sceneType_ = 0;

      if (statusListBuilder_ == null) {
        statusList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        statusListBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DenmData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.DenmData getDefaultInstanceForType() {
      return road.data.proto.DenmData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.DenmData build() {
      road.data.proto.DenmData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.DenmData buildPartial() {
      road.data.proto.DenmData result = new road.data.proto.DenmData(this);
      int from_bitField0_ = bitField0_;
      result.type_ = type_;
      result.ver_ = ver_;
      result.msgCnt_ = msgCnt_;
      result.timestamp_ = timestamp_;
      result.address_ = address_;
      if (refPosBuilder_ == null) {
        result.refPos_ = refPos_;
      } else {
        result.refPos_ = refPosBuilder_.build();
      }
      result.sceneType_ = sceneType_;
      if (statusListBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          statusList_ = java.util.Collections.unmodifiableList(statusList_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.statusList_ = statusList_;
      } else {
        result.statusList_ = statusListBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.DenmData) {
        return mergeFrom((road.data.proto.DenmData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.DenmData other) {
      if (other == road.data.proto.DenmData.getDefaultInstance()) return this;
      if (other.getType() != 0) {
        setType(other.getType());
      }
      if (!other.getVer().isEmpty()) {
        ver_ = other.ver_;
        onChanged();
      }
      if (other.getMsgCnt() != 0) {
        setMsgCnt(other.getMsgCnt());
      }
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (!other.getAddress().isEmpty()) {
        address_ = other.address_;
        onChanged();
      }
      if (other.hasRefPos()) {
        mergeRefPos(other.getRefPos());
      }
      if (other.sceneType_ != 0) {
        setSceneTypeValue(other.getSceneTypeValue());
      }
      if (statusListBuilder_ == null) {
        if (!other.statusList_.isEmpty()) {
          if (statusList_.isEmpty()) {
            statusList_ = other.statusList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureStatusListIsMutable();
            statusList_.addAll(other.statusList_);
          }
          onChanged();
        }
      } else {
        if (!other.statusList_.isEmpty()) {
          if (statusListBuilder_.isEmpty()) {
            statusListBuilder_.dispose();
            statusListBuilder_ = null;
            statusList_ = other.statusList_;
            bitField0_ = (bitField0_ & ~0x00000001);
            statusListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getStatusListFieldBuilder() : null;
          } else {
            statusListBuilder_.addAllMessages(other.statusList_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.DenmData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.DenmData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int type_ ;
    /**
     * <pre>
     * type取值为2，表示MEC向RSU发送的心跳状态消息
     * </pre>
     *
     * <code>uint32 type = 1;</code>
     */
    public int getType() {
      return type_;
    }
    /**
     * <pre>
     * type取值为2，表示MEC向RSU发送的心跳状态消息
     * </pre>
     *
     * <code>uint32 type = 1;</code>
     */
    public Builder setType(int value) {
      
      type_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * type取值为2，表示MEC向RSU发送的心跳状态消息
     * </pre>
     *
     * <code>uint32 type = 1;</code>
     */
    public Builder clearType() {
      
      type_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object ver_ = "";
    /**
     * <pre>
     * 版本号，目前版本固定为“01”
     * </pre>
     *
     * <code>string ver = 2;</code>
     */
    public java.lang.String getVer() {
      java.lang.Object ref = ver_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ver_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 版本号，目前版本固定为“01”
     * </pre>
     *
     * <code>string ver = 2;</code>
     */
    public com.google.protobuf.ByteString
        getVerBytes() {
      java.lang.Object ref = ver_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ver_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 版本号，目前版本固定为“01”
     * </pre>
     *
     * <code>string ver = 2;</code>
     */
    public Builder setVer(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      ver_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 版本号，目前版本固定为“01”
     * </pre>
     *
     * <code>string ver = 2;</code>
     */
    public Builder clearVer() {
      
      ver_ = getDefaultInstance().getVer();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 版本号，目前版本固定为“01”
     * </pre>
     *
     * <code>string ver = 2;</code>
     */
    public Builder setVerBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      ver_ = value;
      onChanged();
      return this;
    }

    private int msgCnt_ ;
    /**
     * <pre>
     * 定义消息编号。发送方对发送的同类消息(type=2)依次进行编号。编号循环发送。
     * </pre>
     *
     * <code>uint32 msgCnt = 3;</code>
     */
    public int getMsgCnt() {
      return msgCnt_;
    }
    /**
     * <pre>
     * 定义消息编号。发送方对发送的同类消息(type=2)依次进行编号。编号循环发送。
     * </pre>
     *
     * <code>uint32 msgCnt = 3;</code>
     */
    public Builder setMsgCnt(int value) {
      
      msgCnt_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 定义消息编号。发送方对发送的同类消息(type=2)依次进行编号。编号循环发送。
     * </pre>
     *
     * <code>uint32 msgCnt = 3;</code>
     */
    public Builder clearMsgCnt() {
      
      msgCnt_ = 0;
      onChanged();
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 4;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 4;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 4;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object address_ = "";
    /**
     * <pre>
     * 设备所在位置 (a) 提供ASCII字符文本形式;(b) 提供中文编码形式，符合GB2312_80的编码规则，一个字有2字节信息编码。
     * </pre>
     *
     * <code>string address = 5;</code>
     */
    public java.lang.String getAddress() {
      java.lang.Object ref = address_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        address_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 设备所在位置 (a) 提供ASCII字符文本形式;(b) 提供中文编码形式，符合GB2312_80的编码规则，一个字有2字节信息编码。
     * </pre>
     *
     * <code>string address = 5;</code>
     */
    public com.google.protobuf.ByteString
        getAddressBytes() {
      java.lang.Object ref = address_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        address_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 设备所在位置 (a) 提供ASCII字符文本形式;(b) 提供中文编码形式，符合GB2312_80的编码规则，一个字有2字节信息编码。
     * </pre>
     *
     * <code>string address = 5;</code>
     */
    public Builder setAddress(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      address_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 设备所在位置 (a) 提供ASCII字符文本形式;(b) 提供中文编码形式，符合GB2312_80的编码规则，一个字有2字节信息编码。
     * </pre>
     *
     * <code>string address = 5;</code>
     */
    public Builder clearAddress() {
      
      address_ = getDefaultInstance().getAddress();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 设备所在位置 (a) 提供ASCII字符文本形式;(b) 提供中文编码形式，符合GB2312_80的编码规则，一个字有2字节信息编码。
     * </pre>
     *
     * <code>string address = 5;</code>
     */
    public Builder setAddressBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      address_ = value;
      onChanged();
      return this;
    }

    private road.data.proto.Position3D refPos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> refPosBuilder_;
    /**
     * <pre>
     * 位置基准参考点,绝对坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
     */
    public boolean hasRefPos() {
      return refPosBuilder_ != null || refPos_ != null;
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
     */
    public road.data.proto.Position3D getRefPos() {
      if (refPosBuilder_ == null) {
        return refPos_ == null ? road.data.proto.Position3D.getDefaultInstance() : refPos_;
      } else {
        return refPosBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
     */
    public Builder setRefPos(road.data.proto.Position3D value) {
      if (refPosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        refPos_ = value;
        onChanged();
      } else {
        refPosBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
     */
    public Builder setRefPos(
        road.data.proto.Position3D.Builder builderForValue) {
      if (refPosBuilder_ == null) {
        refPos_ = builderForValue.build();
        onChanged();
      } else {
        refPosBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
     */
    public Builder mergeRefPos(road.data.proto.Position3D value) {
      if (refPosBuilder_ == null) {
        if (refPos_ != null) {
          refPos_ =
            road.data.proto.Position3D.newBuilder(refPos_).mergeFrom(value).buildPartial();
        } else {
          refPos_ = value;
        }
        onChanged();
      } else {
        refPosBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
     */
    public Builder clearRefPos() {
      if (refPosBuilder_ == null) {
        refPos_ = null;
        onChanged();
      } else {
        refPos_ = null;
        refPosBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
     */
    public road.data.proto.Position3D.Builder getRefPosBuilder() {
      
      onChanged();
      return getRefPosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
     */
    public road.data.proto.Position3DOrBuilder getRefPosOrBuilder() {
      if (refPosBuilder_ != null) {
        return refPosBuilder_.getMessageOrBuilder();
      } else {
        return refPos_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : refPos_;
      }
    }
    /**
     * <pre>
     * 位置基准参考点,绝对坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getRefPosFieldBuilder() {
      if (refPosBuilder_ == null) {
        refPosBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getRefPos(),
                getParentForChildren(),
                isClean());
        refPos_ = null;
      }
      return refPosBuilder_;
    }

    private int sceneType_ = 0;
    /**
     * <pre>
     * 表示场地类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SceneType sceneType = 7;</code>
     */
    public int getSceneTypeValue() {
      return sceneType_;
    }
    /**
     * <pre>
     * 表示场地类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SceneType sceneType = 7;</code>
     */
    public Builder setSceneTypeValue(int value) {
      sceneType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 表示场地类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SceneType sceneType = 7;</code>
     */
    public road.data.proto.SceneType getSceneType() {
      @SuppressWarnings("deprecation")
      road.data.proto.SceneType result = road.data.proto.SceneType.valueOf(sceneType_);
      return result == null ? road.data.proto.SceneType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 表示场地类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SceneType sceneType = 7;</code>
     */
    public Builder setSceneType(road.data.proto.SceneType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      sceneType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 表示场地类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SceneType sceneType = 7;</code>
     */
    public Builder clearSceneType() {
      
      sceneType_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.StatusData> statusList_ =
      java.util.Collections.emptyList();
    private void ensureStatusListIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        statusList_ = new java.util.ArrayList<road.data.proto.StatusData>(statusList_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.StatusData, road.data.proto.StatusData.Builder, road.data.proto.StatusDataOrBuilder> statusListBuilder_;

    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public java.util.List<road.data.proto.StatusData> getStatusListList() {
      if (statusListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(statusList_);
      } else {
        return statusListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public int getStatusListCount() {
      if (statusListBuilder_ == null) {
        return statusList_.size();
      } else {
        return statusListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public road.data.proto.StatusData getStatusList(int index) {
      if (statusListBuilder_ == null) {
        return statusList_.get(index);
      } else {
        return statusListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public Builder setStatusList(
        int index, road.data.proto.StatusData value) {
      if (statusListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureStatusListIsMutable();
        statusList_.set(index, value);
        onChanged();
      } else {
        statusListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public Builder setStatusList(
        int index, road.data.proto.StatusData.Builder builderForValue) {
      if (statusListBuilder_ == null) {
        ensureStatusListIsMutable();
        statusList_.set(index, builderForValue.build());
        onChanged();
      } else {
        statusListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public Builder addStatusList(road.data.proto.StatusData value) {
      if (statusListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureStatusListIsMutable();
        statusList_.add(value);
        onChanged();
      } else {
        statusListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public Builder addStatusList(
        int index, road.data.proto.StatusData value) {
      if (statusListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureStatusListIsMutable();
        statusList_.add(index, value);
        onChanged();
      } else {
        statusListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public Builder addStatusList(
        road.data.proto.StatusData.Builder builderForValue) {
      if (statusListBuilder_ == null) {
        ensureStatusListIsMutable();
        statusList_.add(builderForValue.build());
        onChanged();
      } else {
        statusListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public Builder addStatusList(
        int index, road.data.proto.StatusData.Builder builderForValue) {
      if (statusListBuilder_ == null) {
        ensureStatusListIsMutable();
        statusList_.add(index, builderForValue.build());
        onChanged();
      } else {
        statusListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public Builder addAllStatusList(
        java.lang.Iterable<? extends road.data.proto.StatusData> values) {
      if (statusListBuilder_ == null) {
        ensureStatusListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, statusList_);
        onChanged();
      } else {
        statusListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public Builder clearStatusList() {
      if (statusListBuilder_ == null) {
        statusList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        statusListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public Builder removeStatusList(int index) {
      if (statusListBuilder_ == null) {
        ensureStatusListIsMutable();
        statusList_.remove(index);
        onChanged();
      } else {
        statusListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public road.data.proto.StatusData.Builder getStatusListBuilder(
        int index) {
      return getStatusListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public road.data.proto.StatusDataOrBuilder getStatusListOrBuilder(
        int index) {
      if (statusListBuilder_ == null) {
        return statusList_.get(index);  } else {
        return statusListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public java.util.List<? extends road.data.proto.StatusDataOrBuilder> 
         getStatusListOrBuilderList() {
      if (statusListBuilder_ != null) {
        return statusListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(statusList_);
      }
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public road.data.proto.StatusData.Builder addStatusListBuilder() {
      return getStatusListFieldBuilder().addBuilder(
          road.data.proto.StatusData.getDefaultInstance());
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public road.data.proto.StatusData.Builder addStatusListBuilder(
        int index) {
      return getStatusListFieldBuilder().addBuilder(
          index, road.data.proto.StatusData.getDefaultInstance());
    }
    /**
     * <pre>
     *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
     */
    public java.util.List<road.data.proto.StatusData.Builder> 
         getStatusListBuilderList() {
      return getStatusListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.StatusData, road.data.proto.StatusData.Builder, road.data.proto.StatusDataOrBuilder> 
        getStatusListFieldBuilder() {
      if (statusListBuilder_ == null) {
        statusListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.StatusData, road.data.proto.StatusData.Builder, road.data.proto.StatusDataOrBuilder>(
                statusList_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        statusList_ = null;
      }
      return statusListBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.DenmData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.DenmData)
  private static final road.data.proto.DenmData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.DenmData();
  }

  public static road.data.proto.DenmData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DenmData>
      PARSER = new com.google.protobuf.AbstractParser<DenmData>() {
    @java.lang.Override
    public DenmData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new DenmData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<DenmData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DenmData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.DenmData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

