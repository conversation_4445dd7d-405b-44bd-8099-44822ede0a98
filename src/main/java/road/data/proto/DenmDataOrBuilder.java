// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface DenmDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.DenmData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * type取值为2，表示MEC向RSU发送的心跳状态消息
   * </pre>
   *
   * <code>uint32 type = 1;</code>
   */
  int getType();

  /**
   * <pre>
   * 版本号，目前版本固定为“01”
   * </pre>
   *
   * <code>string ver = 2;</code>
   */
  java.lang.String getVer();
  /**
   * <pre>
   * 版本号，目前版本固定为“01”
   * </pre>
   *
   * <code>string ver = 2;</code>
   */
  com.google.protobuf.ByteString
      getVerBytes();

  /**
   * <pre>
   * 定义消息编号。发送方对发送的同类消息(type=2)依次进行编号。编号循环发送。
   * </pre>
   *
   * <code>uint32 msgCnt = 3;</code>
   */
  int getMsgCnt();

  /**
   * <pre>
   * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 4;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   * 设备所在位置 (a) 提供ASCII字符文本形式;(b) 提供中文编码形式，符合GB2312_80的编码规则，一个字有2字节信息编码。
   * </pre>
   *
   * <code>string address = 5;</code>
   */
  java.lang.String getAddress();
  /**
   * <pre>
   * 设备所在位置 (a) 提供ASCII字符文本形式;(b) 提供中文编码形式，符合GB2312_80的编码规则，一个字有2字节信息编码。
   * </pre>
   *
   * <code>string address = 5;</code>
   */
  com.google.protobuf.ByteString
      getAddressBytes();

  /**
   * <pre>
   * 位置基准参考点,绝对坐标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
   */
  boolean hasRefPos();
  /**
   * <pre>
   * 位置基准参考点,绝对坐标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
   */
  road.data.proto.Position3D getRefPos();
  /**
   * <pre>
   * 位置基准参考点,绝对坐标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 6;</code>
   */
  road.data.proto.Position3DOrBuilder getRefPosOrBuilder();

  /**
   * <pre>
   * 表示场地类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SceneType sceneType = 7;</code>
   */
  int getSceneTypeValue();
  /**
   * <pre>
   * 表示场地类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SceneType sceneType = 7;</code>
   */
  road.data.proto.SceneType getSceneType();

  /**
   * <pre>
   *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
   */
  java.util.List<road.data.proto.StatusData> 
      getStatusListList();
  /**
   * <pre>
   *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
   */
  road.data.proto.StatusData getStatusList(int index);
  /**
   * <pre>
   *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
   */
  int getStatusListCount();
  /**
   * <pre>
   *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
   */
  java.util.List<? extends road.data.proto.StatusDataOrBuilder> 
      getStatusListOrBuilderList();
  /**
   * <pre>
   *	定义MEC/感知节点/RSU状态列表，该列表第一个表示设备自身
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.StatusData statusList = 8;</code>
   */
  road.data.proto.StatusDataOrBuilder getStatusListOrBuilder(
      int index);
}
