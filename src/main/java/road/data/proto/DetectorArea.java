// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *交通流感知区间  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.DetectorArea}
 */
public  final class DetectorArea extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.DetectorArea)
    DetectorAreaOrBuilder {
private static final long serialVersionUID = 0L;
  // Use DetectorArea.newBuilder() to construct.
  private DetectorArea(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private DetectorArea() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new DetectorArea();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private DetectorArea(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            areaId_ = input.readInt32();
            break;
          }
          case 16: {

            setTime_ = input.readInt64();
            break;
          }
          case 26: {
            road.data.proto.Polygon.Builder subBuilder = null;
            if (polygon_ != null) {
              subBuilder = polygon_.toBuilder();
            }
            polygon_ = input.readMessage(road.data.proto.Polygon.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(polygon_);
              polygon_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (nodeId_ != null) {
              subBuilder = nodeId_.toBuilder();
            }
            nodeId_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(nodeId_);
              nodeId_ = subBuilder.buildPartial();
            }

            break;
          }
          case 40: {

            laneId_ = input.readInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DetectorArea_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DetectorArea_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.DetectorArea.class, road.data.proto.DetectorArea.Builder.class);
  }

  public static final int AREAID_FIELD_NUMBER = 1;
  private int areaId_;
  /**
   * <pre>
   *交通流感知区间ID
   * </pre>
   *
   * <code>int32 areaId = 1;</code>
   */
  public int getAreaId() {
    return areaId_;
  }

  public static final int SETTIME_FIELD_NUMBER = 2;
  private long setTime_;
  /**
   * <pre>
   *可选，UNIXTIME 时间戳 单位到秒 设置更新时间
   * </pre>
   *
   * <code>int64 setTime = 2;</code>
   */
  public long getSetTime() {
    return setTime_;
  }

  public static final int POLYGON_FIELD_NUMBER = 3;
  private road.data.proto.Polygon polygon_;
  /**
   * <pre>
   *一组三维相对位置的定点组成的多边形区域，至少有4个点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
   */
  public boolean hasPolygon() {
    return polygon_ != null;
  }
  /**
   * <pre>
   *一组三维相对位置的定点组成的多边形区域，至少有4个点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
   */
  public road.data.proto.Polygon getPolygon() {
    return polygon_ == null ? road.data.proto.Polygon.getDefaultInstance() : polygon_;
  }
  /**
   * <pre>
   *一组三维相对位置的定点组成的多边形区域，至少有4个点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
   */
  public road.data.proto.PolygonOrBuilder getPolygonOrBuilder() {
    return getPolygon();
  }

  public static final int NODEID_FIELD_NUMBER = 4;
  private road.data.proto.NodeReferenceId nodeId_;
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
   */
  public boolean hasNodeId() {
    return nodeId_ != null;
  }
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
   */
  public road.data.proto.NodeReferenceId getNodeId() {
    return nodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
  }
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder() {
    return getNodeId();
  }

  public static final int LANEID_FIELD_NUMBER = 5;
  private int laneId_;
  /**
   * <pre>
   *可选，LaneId道对象定义车道，定义来自Lane对象
   * </pre>
   *
   * <code>int32 laneId = 5;</code>
   */
  public int getLaneId() {
    return laneId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (areaId_ != 0) {
      output.writeInt32(1, areaId_);
    }
    if (setTime_ != 0L) {
      output.writeInt64(2, setTime_);
    }
    if (polygon_ != null) {
      output.writeMessage(3, getPolygon());
    }
    if (nodeId_ != null) {
      output.writeMessage(4, getNodeId());
    }
    if (laneId_ != 0) {
      output.writeInt32(5, laneId_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (areaId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, areaId_);
    }
    if (setTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, setTime_);
    }
    if (polygon_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getPolygon());
    }
    if (nodeId_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getNodeId());
    }
    if (laneId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, laneId_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.DetectorArea)) {
      return super.equals(obj);
    }
    road.data.proto.DetectorArea other = (road.data.proto.DetectorArea) obj;

    if (getAreaId()
        != other.getAreaId()) return false;
    if (getSetTime()
        != other.getSetTime()) return false;
    if (hasPolygon() != other.hasPolygon()) return false;
    if (hasPolygon()) {
      if (!getPolygon()
          .equals(other.getPolygon())) return false;
    }
    if (hasNodeId() != other.hasNodeId()) return false;
    if (hasNodeId()) {
      if (!getNodeId()
          .equals(other.getNodeId())) return false;
    }
    if (getLaneId()
        != other.getLaneId()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + AREAID_FIELD_NUMBER;
    hash = (53 * hash) + getAreaId();
    hash = (37 * hash) + SETTIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getSetTime());
    if (hasPolygon()) {
      hash = (37 * hash) + POLYGON_FIELD_NUMBER;
      hash = (53 * hash) + getPolygon().hashCode();
    }
    if (hasNodeId()) {
      hash = (37 * hash) + NODEID_FIELD_NUMBER;
      hash = (53 * hash) + getNodeId().hashCode();
    }
    hash = (37 * hash) + LANEID_FIELD_NUMBER;
    hash = (53 * hash) + getLaneId();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.DetectorArea parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DetectorArea parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DetectorArea parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DetectorArea parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DetectorArea parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DetectorArea parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DetectorArea parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.DetectorArea parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.DetectorArea parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.DetectorArea parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.DetectorArea parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.DetectorArea parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.DetectorArea prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *交通流感知区间  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.DetectorArea}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.DetectorArea)
      road.data.proto.DetectorAreaOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DetectorArea_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DetectorArea_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.DetectorArea.class, road.data.proto.DetectorArea.Builder.class);
    }

    // Construct using road.data.proto.DetectorArea.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      areaId_ = 0;

      setTime_ = 0L;

      if (polygonBuilder_ == null) {
        polygon_ = null;
      } else {
        polygon_ = null;
        polygonBuilder_ = null;
      }
      if (nodeIdBuilder_ == null) {
        nodeId_ = null;
      } else {
        nodeId_ = null;
        nodeIdBuilder_ = null;
      }
      laneId_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DetectorArea_descriptor;
    }

    @java.lang.Override
    public road.data.proto.DetectorArea getDefaultInstanceForType() {
      return road.data.proto.DetectorArea.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.DetectorArea build() {
      road.data.proto.DetectorArea result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.DetectorArea buildPartial() {
      road.data.proto.DetectorArea result = new road.data.proto.DetectorArea(this);
      result.areaId_ = areaId_;
      result.setTime_ = setTime_;
      if (polygonBuilder_ == null) {
        result.polygon_ = polygon_;
      } else {
        result.polygon_ = polygonBuilder_.build();
      }
      if (nodeIdBuilder_ == null) {
        result.nodeId_ = nodeId_;
      } else {
        result.nodeId_ = nodeIdBuilder_.build();
      }
      result.laneId_ = laneId_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.DetectorArea) {
        return mergeFrom((road.data.proto.DetectorArea)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.DetectorArea other) {
      if (other == road.data.proto.DetectorArea.getDefaultInstance()) return this;
      if (other.getAreaId() != 0) {
        setAreaId(other.getAreaId());
      }
      if (other.getSetTime() != 0L) {
        setSetTime(other.getSetTime());
      }
      if (other.hasPolygon()) {
        mergePolygon(other.getPolygon());
      }
      if (other.hasNodeId()) {
        mergeNodeId(other.getNodeId());
      }
      if (other.getLaneId() != 0) {
        setLaneId(other.getLaneId());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.DetectorArea parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.DetectorArea) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int areaId_ ;
    /**
     * <pre>
     *交通流感知区间ID
     * </pre>
     *
     * <code>int32 areaId = 1;</code>
     */
    public int getAreaId() {
      return areaId_;
    }
    /**
     * <pre>
     *交通流感知区间ID
     * </pre>
     *
     * <code>int32 areaId = 1;</code>
     */
    public Builder setAreaId(int value) {
      
      areaId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *交通流感知区间ID
     * </pre>
     *
     * <code>int32 areaId = 1;</code>
     */
    public Builder clearAreaId() {
      
      areaId_ = 0;
      onChanged();
      return this;
    }

    private long setTime_ ;
    /**
     * <pre>
     *可选，UNIXTIME 时间戳 单位到秒 设置更新时间
     * </pre>
     *
     * <code>int64 setTime = 2;</code>
     */
    public long getSetTime() {
      return setTime_;
    }
    /**
     * <pre>
     *可选，UNIXTIME 时间戳 单位到秒 设置更新时间
     * </pre>
     *
     * <code>int64 setTime = 2;</code>
     */
    public Builder setSetTime(long value) {
      
      setTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，UNIXTIME 时间戳 单位到秒 设置更新时间
     * </pre>
     *
     * <code>int64 setTime = 2;</code>
     */
    public Builder clearSetTime() {
      
      setTime_ = 0L;
      onChanged();
      return this;
    }

    private road.data.proto.Polygon polygon_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> polygonBuilder_;
    /**
     * <pre>
     *一组三维相对位置的定点组成的多边形区域，至少有4个点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
     */
    public boolean hasPolygon() {
      return polygonBuilder_ != null || polygon_ != null;
    }
    /**
     * <pre>
     *一组三维相对位置的定点组成的多边形区域，至少有4个点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
     */
    public road.data.proto.Polygon getPolygon() {
      if (polygonBuilder_ == null) {
        return polygon_ == null ? road.data.proto.Polygon.getDefaultInstance() : polygon_;
      } else {
        return polygonBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *一组三维相对位置的定点组成的多边形区域，至少有4个点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
     */
    public Builder setPolygon(road.data.proto.Polygon value) {
      if (polygonBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        polygon_ = value;
        onChanged();
      } else {
        polygonBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *一组三维相对位置的定点组成的多边形区域，至少有4个点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
     */
    public Builder setPolygon(
        road.data.proto.Polygon.Builder builderForValue) {
      if (polygonBuilder_ == null) {
        polygon_ = builderForValue.build();
        onChanged();
      } else {
        polygonBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *一组三维相对位置的定点组成的多边形区域，至少有4个点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
     */
    public Builder mergePolygon(road.data.proto.Polygon value) {
      if (polygonBuilder_ == null) {
        if (polygon_ != null) {
          polygon_ =
            road.data.proto.Polygon.newBuilder(polygon_).mergeFrom(value).buildPartial();
        } else {
          polygon_ = value;
        }
        onChanged();
      } else {
        polygonBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *一组三维相对位置的定点组成的多边形区域，至少有4个点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
     */
    public Builder clearPolygon() {
      if (polygonBuilder_ == null) {
        polygon_ = null;
        onChanged();
      } else {
        polygon_ = null;
        polygonBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *一组三维相对位置的定点组成的多边形区域，至少有4个点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
     */
    public road.data.proto.Polygon.Builder getPolygonBuilder() {
      
      onChanged();
      return getPolygonFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *一组三维相对位置的定点组成的多边形区域，至少有4个点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
     */
    public road.data.proto.PolygonOrBuilder getPolygonOrBuilder() {
      if (polygonBuilder_ != null) {
        return polygonBuilder_.getMessageOrBuilder();
      } else {
        return polygon_ == null ?
            road.data.proto.Polygon.getDefaultInstance() : polygon_;
      }
    }
    /**
     * <pre>
     *一组三维相对位置的定点组成的多边形区域，至少有4个点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> 
        getPolygonFieldBuilder() {
      if (polygonBuilder_ == null) {
        polygonBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder>(
                getPolygon(),
                getParentForChildren(),
                isClean());
        polygon_ = null;
      }
      return polygonBuilder_;
    }

    private road.data.proto.NodeReferenceId nodeId_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> nodeIdBuilder_;
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
     */
    public boolean hasNodeId() {
      return nodeIdBuilder_ != null || nodeId_ != null;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
     */
    public road.data.proto.NodeReferenceId getNodeId() {
      if (nodeIdBuilder_ == null) {
        return nodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
      } else {
        return nodeIdBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
     */
    public Builder setNodeId(road.data.proto.NodeReferenceId value) {
      if (nodeIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        nodeId_ = value;
        onChanged();
      } else {
        nodeIdBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
     */
    public Builder setNodeId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (nodeIdBuilder_ == null) {
        nodeId_ = builderForValue.build();
        onChanged();
      } else {
        nodeIdBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
     */
    public Builder mergeNodeId(road.data.proto.NodeReferenceId value) {
      if (nodeIdBuilder_ == null) {
        if (nodeId_ != null) {
          nodeId_ =
            road.data.proto.NodeReferenceId.newBuilder(nodeId_).mergeFrom(value).buildPartial();
        } else {
          nodeId_ = value;
        }
        onChanged();
      } else {
        nodeIdBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
     */
    public Builder clearNodeId() {
      if (nodeIdBuilder_ == null) {
        nodeId_ = null;
        onChanged();
      } else {
        nodeId_ = null;
        nodeIdBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getNodeIdBuilder() {
      
      onChanged();
      return getNodeIdFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder() {
      if (nodeIdBuilder_ != null) {
        return nodeIdBuilder_.getMessageOrBuilder();
      } else {
        return nodeId_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
      }
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getNodeIdFieldBuilder() {
      if (nodeIdBuilder_ == null) {
        nodeIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getNodeId(),
                getParentForChildren(),
                isClean());
        nodeId_ = null;
      }
      return nodeIdBuilder_;
    }

    private int laneId_ ;
    /**
     * <pre>
     *可选，LaneId道对象定义车道，定义来自Lane对象
     * </pre>
     *
     * <code>int32 laneId = 5;</code>
     */
    public int getLaneId() {
      return laneId_;
    }
    /**
     * <pre>
     *可选，LaneId道对象定义车道，定义来自Lane对象
     * </pre>
     *
     * <code>int32 laneId = 5;</code>
     */
    public Builder setLaneId(int value) {
      
      laneId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，LaneId道对象定义车道，定义来自Lane对象
     * </pre>
     *
     * <code>int32 laneId = 5;</code>
     */
    public Builder clearLaneId() {
      
      laneId_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.DetectorArea)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.DetectorArea)
  private static final road.data.proto.DetectorArea DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.DetectorArea();
  }

  public static road.data.proto.DetectorArea getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DetectorArea>
      PARSER = new com.google.protobuf.AbstractParser<DetectorArea>() {
    @java.lang.Override
    public DetectorArea parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new DetectorArea(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<DetectorArea> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DetectorArea> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.DetectorArea getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

