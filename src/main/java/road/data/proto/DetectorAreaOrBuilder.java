// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface DetectorAreaOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.DetectorArea)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *交通流感知区间ID
   * </pre>
   *
   * <code>int32 areaId = 1;</code>
   */
  int getAreaId();

  /**
   * <pre>
   *可选，UNIXTIME 时间戳 单位到秒 设置更新时间
   * </pre>
   *
   * <code>int64 setTime = 2;</code>
   */
  long getSetTime();

  /**
   * <pre>
   *一组三维相对位置的定点组成的多边形区域，至少有4个点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
   */
  boolean hasPolygon();
  /**
   * <pre>
   *一组三维相对位置的定点组成的多边形区域，至少有4个点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
   */
  road.data.proto.Polygon getPolygon();
  /**
   * <pre>
   *一组三维相对位置的定点组成的多边形区域，至少有4个点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 3;</code>
   */
  road.data.proto.PolygonOrBuilder getPolygonOrBuilder();

  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
   */
  boolean hasNodeId();
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
   */
  road.data.proto.NodeReferenceId getNodeId();
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 4;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder();

  /**
   * <pre>
   *可选，LaneId道对象定义车道，定义来自Lane对象
   * </pre>
   *
   * <code>int32 laneId = 5;</code>
   */
  int getLaneId();
}
