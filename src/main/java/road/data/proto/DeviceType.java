// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *________________________________________________________________________________
 *设备类型 DeviceType   
 * </pre>
 *
 * Protobuf enum {@code cn.seisys.v2x.pb.DeviceType}
 */
public enum DeviceType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *默认
   * </pre>
   *
   * <code>DEVICE_TYPE_UNKONWN = 0;</code>
   */
  DEVICE_TYPE_UNKONWN(0),
  /**
   * <pre>
   *  车载
   * </pre>
   *
   * <code>OBU = 1;</code>
   */
  OBU(1),
  /**
   * <pre>
   *  路侧单元
   * </pre>
   *
   * <code>RSU = 2;</code>
   */
  RSU(2),
  /**
   * <pre>
   *  其他 外接设备
   * </pre>
   *
   * <code>OTHER = 100;</code>
   */
  OTHER(100),
  /**
   * <pre>
   *  摄像机
   * </pre>
   *
   * <code>CAMERA = 200;</code>
   */
  CAMERA(200),
  /**
   * <pre>
   *  微米波雷达
   * </pre>
   *
   * <code>MICRO_RADAR = 202;</code>
   */
  MICRO_RADAR(202),
  /**
   * <pre>
   *  激光雷达
   * </pre>
   *
   * <code>LASER_RADAR = 203;</code>
   */
  LASER_RADAR(203),
  /**
   * <pre>
   * MEC
   * </pre>
   *
   * <code>MEC = 204;</code>
   */
  MEC(204),
  /**
   * <pre>
   *  信号灯控制器
   * </pre>
   *
   * <code>SIBOX = 205;</code>
   */
  SIBOX(205),
  /**
   * <pre>
   * 天气传感器
   * </pre>
   *
   * <code>WEATHER_SENSOR = 206;</code>
   */
  WEATHER_SENSOR(206),
  /**
   * <pre>
   * 可变情报板
   * </pre>
   *
   * <code>VMS = 207;</code>
   */
  VMS(207),
  /**
   * <pre>
   * 毫米波雷达
   * </pre>
   *
   * <code>MMW_RADAR = 208;</code>
   */
  MMW_RADAR(208),
  /**
   * <pre>
   * 云平台
   * </pre>
   *
   * <code>CLOUD = 209;</code>
   */
  CLOUD(209),
  /**
   * <pre>
   * 电子标牌
   * </pre>
   *
   * <code>ELECTRONIC_TAGS = 210;</code>
   */
  ELECTRONIC_TAGS(210),
  /**
   * <pre>
   * 智慧灯杆
   * </pre>
   *
   * <code>WISDOM_LIGHTPOLE = 211;</code>
   */
  WISDOM_LIGHTPOLE(211),
  /**
   * <pre>
   * 智慧井盖
   * </pre>
   *
   * <code>WISDOM_MANHOLECOVER = 212;</code>
   */
  WISDOM_MANHOLECOVER(212),
  /**
   * <pre>
   * 智慧站台
   * </pre>
   *
   * <code>WISDOM_PLATFORM = 213;</code>
   */
  WISDOM_PLATFORM(213),
  /**
   * <pre>
   *载体杆
   * </pre>
   *
   * <code>CARRIER = 214;</code>
   */
  CARRIER(214),
  /**
   * <pre>
   *SIGNAL = 215;   //信号灯
   * </pre>
   *
   * <code>INTEGRATED_CABINET = 216;</code>
   */
  INTEGRATED_CABINET(216),
  /**
   * <pre>
   *核心交换机
   * </pre>
   *
   * <code>CORE_SW = 217;</code>
   */
  CORE_SW(217),
  /**
   * <pre>
   *汇聚交换机
   * </pre>
   *
   * <code>GATHER_SW = 218;</code>
   */
  GATHER_SW(218),
  /**
   * <pre>
   *接入交换机
   * </pre>
   *
   * <code>ACCESS_SW = 219;</code>
   */
  ACCESS_SW(219),
  /**
   * <pre>
   *抱杆箱
   * </pre>
   *
   * <code>POLE_BOX = 220;</code>
   */
  POLE_BOX(220),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *默认
   * </pre>
   *
   * <code>DEVICE_TYPE_UNKONWN = 0;</code>
   */
  public static final int DEVICE_TYPE_UNKONWN_VALUE = 0;
  /**
   * <pre>
   *  车载
   * </pre>
   *
   * <code>OBU = 1;</code>
   */
  public static final int OBU_VALUE = 1;
  /**
   * <pre>
   *  路侧单元
   * </pre>
   *
   * <code>RSU = 2;</code>
   */
  public static final int RSU_VALUE = 2;
  /**
   * <pre>
   *  其他 外接设备
   * </pre>
   *
   * <code>OTHER = 100;</code>
   */
  public static final int OTHER_VALUE = 100;
  /**
   * <pre>
   *  摄像机
   * </pre>
   *
   * <code>CAMERA = 200;</code>
   */
  public static final int CAMERA_VALUE = 200;
  /**
   * <pre>
   *  微米波雷达
   * </pre>
   *
   * <code>MICRO_RADAR = 202;</code>
   */
  public static final int MICRO_RADAR_VALUE = 202;
  /**
   * <pre>
   *  激光雷达
   * </pre>
   *
   * <code>LASER_RADAR = 203;</code>
   */
  public static final int LASER_RADAR_VALUE = 203;
  /**
   * <pre>
   * MEC
   * </pre>
   *
   * <code>MEC = 204;</code>
   */
  public static final int MEC_VALUE = 204;
  /**
   * <pre>
   *  信号灯控制器
   * </pre>
   *
   * <code>SIBOX = 205;</code>
   */
  public static final int SIBOX_VALUE = 205;
  /**
   * <pre>
   * 天气传感器
   * </pre>
   *
   * <code>WEATHER_SENSOR = 206;</code>
   */
  public static final int WEATHER_SENSOR_VALUE = 206;
  /**
   * <pre>
   * 可变情报板
   * </pre>
   *
   * <code>VMS = 207;</code>
   */
  public static final int VMS_VALUE = 207;
  /**
   * <pre>
   * 毫米波雷达
   * </pre>
   *
   * <code>MMW_RADAR = 208;</code>
   */
  public static final int MMW_RADAR_VALUE = 208;
  /**
   * <pre>
   * 云平台
   * </pre>
   *
   * <code>CLOUD = 209;</code>
   */
  public static final int CLOUD_VALUE = 209;
  /**
   * <pre>
   * 电子标牌
   * </pre>
   *
   * <code>ELECTRONIC_TAGS = 210;</code>
   */
  public static final int ELECTRONIC_TAGS_VALUE = 210;
  /**
   * <pre>
   * 智慧灯杆
   * </pre>
   *
   * <code>WISDOM_LIGHTPOLE = 211;</code>
   */
  public static final int WISDOM_LIGHTPOLE_VALUE = 211;
  /**
   * <pre>
   * 智慧井盖
   * </pre>
   *
   * <code>WISDOM_MANHOLECOVER = 212;</code>
   */
  public static final int WISDOM_MANHOLECOVER_VALUE = 212;
  /**
   * <pre>
   * 智慧站台
   * </pre>
   *
   * <code>WISDOM_PLATFORM = 213;</code>
   */
  public static final int WISDOM_PLATFORM_VALUE = 213;
  /**
   * <pre>
   *载体杆
   * </pre>
   *
   * <code>CARRIER = 214;</code>
   */
  public static final int CARRIER_VALUE = 214;
  /**
   * <pre>
   *SIGNAL = 215;   //信号灯
   * </pre>
   *
   * <code>INTEGRATED_CABINET = 216;</code>
   */
  public static final int INTEGRATED_CABINET_VALUE = 216;
  /**
   * <pre>
   *核心交换机
   * </pre>
   *
   * <code>CORE_SW = 217;</code>
   */
  public static final int CORE_SW_VALUE = 217;
  /**
   * <pre>
   *汇聚交换机
   * </pre>
   *
   * <code>GATHER_SW = 218;</code>
   */
  public static final int GATHER_SW_VALUE = 218;
  /**
   * <pre>
   *接入交换机
   * </pre>
   *
   * <code>ACCESS_SW = 219;</code>
   */
  public static final int ACCESS_SW_VALUE = 219;
  /**
   * <pre>
   *抱杆箱
   * </pre>
   *
   * <code>POLE_BOX = 220;</code>
   */
  public static final int POLE_BOX_VALUE = 220;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static DeviceType valueOf(int value) {
    return forNumber(value);
  }

  public static DeviceType forNumber(int value) {
    switch (value) {
      case 0: return DEVICE_TYPE_UNKONWN;
      case 1: return OBU;
      case 2: return RSU;
      case 100: return OTHER;
      case 200: return CAMERA;
      case 202: return MICRO_RADAR;
      case 203: return LASER_RADAR;
      case 204: return MEC;
      case 205: return SIBOX;
      case 206: return WEATHER_SENSOR;
      case 207: return VMS;
      case 208: return MMW_RADAR;
      case 209: return CLOUD;
      case 210: return ELECTRONIC_TAGS;
      case 211: return WISDOM_LIGHTPOLE;
      case 212: return WISDOM_MANHOLECOVER;
      case 213: return WISDOM_PLATFORM;
      case 214: return CARRIER;
      case 216: return INTEGRATED_CABINET;
      case 217: return CORE_SW;
      case 218: return GATHER_SW;
      case 219: return ACCESS_SW;
      case 220: return POLE_BOX;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<DeviceType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      DeviceType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<DeviceType>() {
          public DeviceType findValueByNumber(int number) {
            return DeviceType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return road.data.proto.V2X.getDescriptor().getEnumTypes().get(12);
  }

  private static final DeviceType[] VALUES = values();

  public static DeviceType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private DeviceType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.DeviceType)
}

