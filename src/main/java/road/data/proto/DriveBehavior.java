// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 * 驾驶行为 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.DriveBehavior}
 */
public  final class DriveBehavior extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.DriveBehavior)
    DriveBehaviorOrBuilder {
private static final long serialVersionUID = 0L;
  // Use DriveBehavior.newBuilder() to construct.
  private DriveBehavior(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private DriveBehavior() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new DriveBehavior();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private DriveBehavior(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            driveBehavior_ = input.readInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveBehavior_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveBehavior_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.DriveBehavior.class, road.data.proto.DriveBehavior.Builder.class);
  }

  public static final int DRIVEBEHAVIOR_FIELD_NUMBER = 1;
  private int driveBehavior_;
  /**
   * <pre>
   *1:直行, GO_STRAIGHT_FORWARD;
   *2:向左变更车道, LANE_CHANGING_TO_LEFT;
   *3:向右变更车道, LANE_CHANGING_TO_RIGHT;
   *4:驶入, RAMP_IN;
   *5:驶出, RAMP_OUT;
   *6:直行通过交叉路口，INTERSECTION_STRAIGHT_THROUGH;
   *7:左转通过交叉路口, INTERSECTION_TURN_LEFT;
   *8:右转通过交叉路口, INTERSECTION_TURN_RIGHT;
   *9:掉头通过交叉路口, INTERSECTION_UTURN;
   *10:走走停停, STOP_AND_GO;
   *11:停止, STOP;
   *12:减速慢行, SLOW_DOWN;
   *13:加速行驶, SPEED_UP;
   *14:泊车,PARKING;
   * </pre>
   *
   * <code>int32 driveBehavior = 1;</code>
   */
  public int getDriveBehavior() {
    return driveBehavior_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (driveBehavior_ != 0) {
      output.writeInt32(1, driveBehavior_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (driveBehavior_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, driveBehavior_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.DriveBehavior)) {
      return super.equals(obj);
    }
    road.data.proto.DriveBehavior other = (road.data.proto.DriveBehavior) obj;

    if (getDriveBehavior()
        != other.getDriveBehavior()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + DRIVEBEHAVIOR_FIELD_NUMBER;
    hash = (53 * hash) + getDriveBehavior();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.DriveBehavior parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DriveBehavior parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DriveBehavior parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DriveBehavior parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DriveBehavior parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DriveBehavior parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DriveBehavior parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.DriveBehavior parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.DriveBehavior parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.DriveBehavior parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.DriveBehavior parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.DriveBehavior parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.DriveBehavior prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 驾驶行为 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.DriveBehavior}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.DriveBehavior)
      road.data.proto.DriveBehaviorOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveBehavior_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveBehavior_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.DriveBehavior.class, road.data.proto.DriveBehavior.Builder.class);
    }

    // Construct using road.data.proto.DriveBehavior.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      driveBehavior_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveBehavior_descriptor;
    }

    @java.lang.Override
    public road.data.proto.DriveBehavior getDefaultInstanceForType() {
      return road.data.proto.DriveBehavior.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.DriveBehavior build() {
      road.data.proto.DriveBehavior result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.DriveBehavior buildPartial() {
      road.data.proto.DriveBehavior result = new road.data.proto.DriveBehavior(this);
      result.driveBehavior_ = driveBehavior_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.DriveBehavior) {
        return mergeFrom((road.data.proto.DriveBehavior)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.DriveBehavior other) {
      if (other == road.data.proto.DriveBehavior.getDefaultInstance()) return this;
      if (other.getDriveBehavior() != 0) {
        setDriveBehavior(other.getDriveBehavior());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.DriveBehavior parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.DriveBehavior) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int driveBehavior_ ;
    /**
     * <pre>
     *1:直行, GO_STRAIGHT_FORWARD;
     *2:向左变更车道, LANE_CHANGING_TO_LEFT;
     *3:向右变更车道, LANE_CHANGING_TO_RIGHT;
     *4:驶入, RAMP_IN;
     *5:驶出, RAMP_OUT;
     *6:直行通过交叉路口，INTERSECTION_STRAIGHT_THROUGH;
     *7:左转通过交叉路口, INTERSECTION_TURN_LEFT;
     *8:右转通过交叉路口, INTERSECTION_TURN_RIGHT;
     *9:掉头通过交叉路口, INTERSECTION_UTURN;
     *10:走走停停, STOP_AND_GO;
     *11:停止, STOP;
     *12:减速慢行, SLOW_DOWN;
     *13:加速行驶, SPEED_UP;
     *14:泊车,PARKING;
     * </pre>
     *
     * <code>int32 driveBehavior = 1;</code>
     */
    public int getDriveBehavior() {
      return driveBehavior_;
    }
    /**
     * <pre>
     *1:直行, GO_STRAIGHT_FORWARD;
     *2:向左变更车道, LANE_CHANGING_TO_LEFT;
     *3:向右变更车道, LANE_CHANGING_TO_RIGHT;
     *4:驶入, RAMP_IN;
     *5:驶出, RAMP_OUT;
     *6:直行通过交叉路口，INTERSECTION_STRAIGHT_THROUGH;
     *7:左转通过交叉路口, INTERSECTION_TURN_LEFT;
     *8:右转通过交叉路口, INTERSECTION_TURN_RIGHT;
     *9:掉头通过交叉路口, INTERSECTION_UTURN;
     *10:走走停停, STOP_AND_GO;
     *11:停止, STOP;
     *12:减速慢行, SLOW_DOWN;
     *13:加速行驶, SPEED_UP;
     *14:泊车,PARKING;
     * </pre>
     *
     * <code>int32 driveBehavior = 1;</code>
     */
    public Builder setDriveBehavior(int value) {
      
      driveBehavior_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *1:直行, GO_STRAIGHT_FORWARD;
     *2:向左变更车道, LANE_CHANGING_TO_LEFT;
     *3:向右变更车道, LANE_CHANGING_TO_RIGHT;
     *4:驶入, RAMP_IN;
     *5:驶出, RAMP_OUT;
     *6:直行通过交叉路口，INTERSECTION_STRAIGHT_THROUGH;
     *7:左转通过交叉路口, INTERSECTION_TURN_LEFT;
     *8:右转通过交叉路口, INTERSECTION_TURN_RIGHT;
     *9:掉头通过交叉路口, INTERSECTION_UTURN;
     *10:走走停停, STOP_AND_GO;
     *11:停止, STOP;
     *12:减速慢行, SLOW_DOWN;
     *13:加速行驶, SPEED_UP;
     *14:泊车,PARKING;
     * </pre>
     *
     * <code>int32 driveBehavior = 1;</code>
     */
    public Builder clearDriveBehavior() {
      
      driveBehavior_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.DriveBehavior)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.DriveBehavior)
  private static final road.data.proto.DriveBehavior DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.DriveBehavior();
  }

  public static road.data.proto.DriveBehavior getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DriveBehavior>
      PARSER = new com.google.protobuf.AbstractParser<DriveBehavior>() {
    @java.lang.Override
    public DriveBehavior parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new DriveBehavior(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<DriveBehavior> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DriveBehavior> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.DriveBehavior getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

