// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface DriveBehaviorOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.DriveBehavior)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *1:直行, GO_STRAIGHT_FORWARD;
   *2:向左变更车道, LANE_CHANGING_TO_LEFT;
   *3:向右变更车道, LANE_CHANGING_TO_RIGHT;
   *4:驶入, RAMP_IN;
   *5:驶出, RAMP_OUT;
   *6:直行通过交叉路口，INTERSECTION_STRAIGHT_THROUGH;
   *7:左转通过交叉路口, INTERSECTION_TURN_LEFT;
   *8:右转通过交叉路口, INTERSECTION_TURN_RIGHT;
   *9:掉头通过交叉路口, INTERSECTION_UTURN;
   *10:走走停停, STOP_AND_GO;
   *11:停止, STOP;
   *12:减速慢行, SLOW_DOWN;
   *13:加速行驶, SPEED_UP;
   *14:泊车,PARKING;
   * </pre>
   *
   * <code>int32 driveBehavior = 1;</code>
   */
  int getDriveBehavior();
}
