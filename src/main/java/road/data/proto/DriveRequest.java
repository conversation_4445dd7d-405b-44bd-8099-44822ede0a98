// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车辆请求序列   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.DriveRequest}
 */
public  final class DriveRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.DriveRequest)
    DriveRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use DriveRequest.newBuilder() to construct.
  private DriveRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private DriveRequest() {
    status_ = 0;
    reqPriority_ = "";
    targetVeh_ = "";
    targetRsu_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new DriveRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private DriveRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            reqId_ = input.readUInt32();
            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            status_ = rawValue;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            reqPriority_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            targetVeh_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            targetRsu_ = s;
            break;
          }
          case 50: {
            road.data.proto.ReqInfo.Builder subBuilder = null;
            if (info_ != null) {
              subBuilder = info_.toBuilder();
            }
            info_ = input.readMessage(road.data.proto.ReqInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(info_);
              info_ = subBuilder.buildPartial();
            }

            break;
          }
          case 56: {

            lifeTime_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.DriveRequest.class, road.data.proto.DriveRequest.Builder.class);
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.DriveRequest.ReqStatus}
   */
  public enum ReqStatus
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *未知
     * </pre>
     *
     * <code>UNKNOWN = 0;</code>
     */
    UNKNOWN(0),
    /**
     * <pre>
     *提出请求但目标设备尚未确认
     * </pre>
     *
     * <code>REQUEST = 1;</code>
     */
    REQUEST(1),
    /**
     * <pre>
     *这个请求已经通过一些方法得到确认
     * </pre>
     *
     * <code>COMFIRMED = 2;</code>
     */
    COMFIRMED(2),
    /**
     * <pre>
     *车辆声称取消此请求
     * </pre>
     *
     * <code>CANCEL = 3;</code>
     */
    CANCEL(3),
    /**
     * <pre>
     *车辆刚刚完成了这个驾驶行为
     * </pre>
     *
     * <code>COMPLETE = 4;</code>
     */
    COMPLETE(4),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *未知
     * </pre>
     *
     * <code>UNKNOWN = 0;</code>
     */
    public static final int UNKNOWN_VALUE = 0;
    /**
     * <pre>
     *提出请求但目标设备尚未确认
     * </pre>
     *
     * <code>REQUEST = 1;</code>
     */
    public static final int REQUEST_VALUE = 1;
    /**
     * <pre>
     *这个请求已经通过一些方法得到确认
     * </pre>
     *
     * <code>COMFIRMED = 2;</code>
     */
    public static final int COMFIRMED_VALUE = 2;
    /**
     * <pre>
     *车辆声称取消此请求
     * </pre>
     *
     * <code>CANCEL = 3;</code>
     */
    public static final int CANCEL_VALUE = 3;
    /**
     * <pre>
     *车辆刚刚完成了这个驾驶行为
     * </pre>
     *
     * <code>COMPLETE = 4;</code>
     */
    public static final int COMPLETE_VALUE = 4;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ReqStatus valueOf(int value) {
      return forNumber(value);
    }

    public static ReqStatus forNumber(int value) {
      switch (value) {
        case 0: return UNKNOWN;
        case 1: return REQUEST;
        case 2: return COMFIRMED;
        case 3: return CANCEL;
        case 4: return COMPLETE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ReqStatus>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ReqStatus> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ReqStatus>() {
            public ReqStatus findValueByNumber(int number) {
              return ReqStatus.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.DriveRequest.getDescriptor().getEnumTypes().get(0);
    }

    private static final ReqStatus[] VALUES = values();

    public static ReqStatus valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ReqStatus(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.DriveRequest.ReqStatus)
  }

  public static final int REQID_FIELD_NUMBER = 1;
  private int reqId_;
  /**
   * <pre>
   *本次请求的本地ID串行 VIR 消息中的相同请求应保持相同的 reqId状态请求状态
   * </pre>
   *
   * <code>uint32 reqId = 1;</code>
   */
  public int getReqId() {
    return reqId_;
  }

  public static final int STATUS_FIELD_NUMBER = 2;
  private int status_;
  /**
   * <pre>
   *请求消息的状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveRequest.ReqStatus status = 2;</code>
   */
  public int getStatusValue() {
    return status_;
  }
  /**
   * <pre>
   *请求消息的状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveRequest.ReqStatus status = 2;</code>
   */
  public road.data.proto.DriveRequest.ReqStatus getStatus() {
    @SuppressWarnings("deprecation")
    road.data.proto.DriveRequest.ReqStatus result = road.data.proto.DriveRequest.ReqStatus.valueOf(status_);
    return result == null ? road.data.proto.DriveRequest.ReqStatus.UNRECOGNIZED : result;
  }

  public static final int REQPRIORITY_FIELD_NUMBER = 3;
  private volatile java.lang.Object reqPriority_;
  /**
   * <pre>
   *可选，低五位保留，应设置为零从 ********* 到 ********* 的值代表最低到最高级别
   * </pre>
   *
   * <code>string reqPriority = 3;</code>
   */
  public java.lang.String getReqPriority() {
    java.lang.Object ref = reqPriority_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      reqPriority_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，低五位保留，应设置为零从 ********* 到 ********* 的值代表最低到最高级别
   * </pre>
   *
   * <code>string reqPriority = 3;</code>
   */
  public com.google.protobuf.ByteString
      getReqPriorityBytes() {
    java.lang.Object ref = reqPriority_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      reqPriority_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TARGETVEH_FIELD_NUMBER = 4;
  private volatile java.lang.Object targetVeh_;
  /**
   * <pre>
   *可选，目标车辆的临时ID
   * </pre>
   *
   * <code>string targetVeh = 4;</code>
   */
  public java.lang.String getTargetVeh() {
    java.lang.Object ref = targetVeh_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      targetVeh_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，目标车辆的临时ID
   * </pre>
   *
   * <code>string targetVeh = 4;</code>
   */
  public com.google.protobuf.ByteString
      getTargetVehBytes() {
    java.lang.Object ref = targetVeh_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      targetVeh_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TARGETRSU_FIELD_NUMBER = 5;
  private volatile java.lang.Object targetRsu_;
  /**
   * <pre>
   *可选，目标 RSU 的临时 ID
   * </pre>
   *
   * <code>string targetRsu = 5;</code>
   */
  public java.lang.String getTargetRsu() {
    java.lang.Object ref = targetRsu_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      targetRsu_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，目标 RSU 的临时 ID
   * </pre>
   *
   * <code>string targetRsu = 5;</code>
   */
  public com.google.protobuf.ByteString
      getTargetRsuBytes() {
    java.lang.Object ref = targetRsu_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      targetRsu_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INFO_FIELD_NUMBER = 6;
  private road.data.proto.ReqInfo info_;
  /**
   * <pre>
   *可选，请求信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
   */
  public boolean hasInfo() {
    return info_ != null;
  }
  /**
   * <pre>
   *可选，请求信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
   */
  public road.data.proto.ReqInfo getInfo() {
    return info_ == null ? road.data.proto.ReqInfo.getDefaultInstance() : info_;
  }
  /**
   * <pre>
   *可选，请求信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
   */
  public road.data.proto.ReqInfoOrBuilder getInfoOrBuilder() {
    return getInfo();
  }

  public static final int LIFETIME_FIELD_NUMBER = 7;
  private int lifeTime_;
  /**
   * <pre>
   *可选，以10毫秒为单位，此请求的生命周期时间偏移量
   * </pre>
   *
   * <code>uint32 lifeTime = 7;</code>
   */
  public int getLifeTime() {
    return lifeTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (reqId_ != 0) {
      output.writeUInt32(1, reqId_);
    }
    if (status_ != road.data.proto.DriveRequest.ReqStatus.UNKNOWN.getNumber()) {
      output.writeEnum(2, status_);
    }
    if (!getReqPriorityBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, reqPriority_);
    }
    if (!getTargetVehBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, targetVeh_);
    }
    if (!getTargetRsuBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, targetRsu_);
    }
    if (info_ != null) {
      output.writeMessage(6, getInfo());
    }
    if (lifeTime_ != 0) {
      output.writeUInt32(7, lifeTime_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (reqId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, reqId_);
    }
    if (status_ != road.data.proto.DriveRequest.ReqStatus.UNKNOWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, status_);
    }
    if (!getReqPriorityBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, reqPriority_);
    }
    if (!getTargetVehBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, targetVeh_);
    }
    if (!getTargetRsuBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, targetRsu_);
    }
    if (info_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getInfo());
    }
    if (lifeTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(7, lifeTime_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.DriveRequest)) {
      return super.equals(obj);
    }
    road.data.proto.DriveRequest other = (road.data.proto.DriveRequest) obj;

    if (getReqId()
        != other.getReqId()) return false;
    if (status_ != other.status_) return false;
    if (!getReqPriority()
        .equals(other.getReqPriority())) return false;
    if (!getTargetVeh()
        .equals(other.getTargetVeh())) return false;
    if (!getTargetRsu()
        .equals(other.getTargetRsu())) return false;
    if (hasInfo() != other.hasInfo()) return false;
    if (hasInfo()) {
      if (!getInfo()
          .equals(other.getInfo())) return false;
    }
    if (getLifeTime()
        != other.getLifeTime()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + REQID_FIELD_NUMBER;
    hash = (53 * hash) + getReqId();
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + status_;
    hash = (37 * hash) + REQPRIORITY_FIELD_NUMBER;
    hash = (53 * hash) + getReqPriority().hashCode();
    hash = (37 * hash) + TARGETVEH_FIELD_NUMBER;
    hash = (53 * hash) + getTargetVeh().hashCode();
    hash = (37 * hash) + TARGETRSU_FIELD_NUMBER;
    hash = (53 * hash) + getTargetRsu().hashCode();
    if (hasInfo()) {
      hash = (37 * hash) + INFO_FIELD_NUMBER;
      hash = (53 * hash) + getInfo().hashCode();
    }
    hash = (37 * hash) + LIFETIME_FIELD_NUMBER;
    hash = (53 * hash) + getLifeTime();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.DriveRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DriveRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DriveRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DriveRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DriveRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DriveRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DriveRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.DriveRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.DriveRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.DriveRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.DriveRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.DriveRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.DriveRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *车辆请求序列   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.DriveRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.DriveRequest)
      road.data.proto.DriveRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.DriveRequest.class, road.data.proto.DriveRequest.Builder.class);
    }

    // Construct using road.data.proto.DriveRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      reqId_ = 0;

      status_ = 0;

      reqPriority_ = "";

      targetVeh_ = "";

      targetRsu_ = "";

      if (infoBuilder_ == null) {
        info_ = null;
      } else {
        info_ = null;
        infoBuilder_ = null;
      }
      lifeTime_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveRequest_descriptor;
    }

    @java.lang.Override
    public road.data.proto.DriveRequest getDefaultInstanceForType() {
      return road.data.proto.DriveRequest.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.DriveRequest build() {
      road.data.proto.DriveRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.DriveRequest buildPartial() {
      road.data.proto.DriveRequest result = new road.data.proto.DriveRequest(this);
      result.reqId_ = reqId_;
      result.status_ = status_;
      result.reqPriority_ = reqPriority_;
      result.targetVeh_ = targetVeh_;
      result.targetRsu_ = targetRsu_;
      if (infoBuilder_ == null) {
        result.info_ = info_;
      } else {
        result.info_ = infoBuilder_.build();
      }
      result.lifeTime_ = lifeTime_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.DriveRequest) {
        return mergeFrom((road.data.proto.DriveRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.DriveRequest other) {
      if (other == road.data.proto.DriveRequest.getDefaultInstance()) return this;
      if (other.getReqId() != 0) {
        setReqId(other.getReqId());
      }
      if (other.status_ != 0) {
        setStatusValue(other.getStatusValue());
      }
      if (!other.getReqPriority().isEmpty()) {
        reqPriority_ = other.reqPriority_;
        onChanged();
      }
      if (!other.getTargetVeh().isEmpty()) {
        targetVeh_ = other.targetVeh_;
        onChanged();
      }
      if (!other.getTargetRsu().isEmpty()) {
        targetRsu_ = other.targetRsu_;
        onChanged();
      }
      if (other.hasInfo()) {
        mergeInfo(other.getInfo());
      }
      if (other.getLifeTime() != 0) {
        setLifeTime(other.getLifeTime());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.DriveRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.DriveRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int reqId_ ;
    /**
     * <pre>
     *本次请求的本地ID串行 VIR 消息中的相同请求应保持相同的 reqId状态请求状态
     * </pre>
     *
     * <code>uint32 reqId = 1;</code>
     */
    public int getReqId() {
      return reqId_;
    }
    /**
     * <pre>
     *本次请求的本地ID串行 VIR 消息中的相同请求应保持相同的 reqId状态请求状态
     * </pre>
     *
     * <code>uint32 reqId = 1;</code>
     */
    public Builder setReqId(int value) {
      
      reqId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *本次请求的本地ID串行 VIR 消息中的相同请求应保持相同的 reqId状态请求状态
     * </pre>
     *
     * <code>uint32 reqId = 1;</code>
     */
    public Builder clearReqId() {
      
      reqId_ = 0;
      onChanged();
      return this;
    }

    private int status_ = 0;
    /**
     * <pre>
     *请求消息的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveRequest.ReqStatus status = 2;</code>
     */
    public int getStatusValue() {
      return status_;
    }
    /**
     * <pre>
     *请求消息的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveRequest.ReqStatus status = 2;</code>
     */
    public Builder setStatusValue(int value) {
      status_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *请求消息的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveRequest.ReqStatus status = 2;</code>
     */
    public road.data.proto.DriveRequest.ReqStatus getStatus() {
      @SuppressWarnings("deprecation")
      road.data.proto.DriveRequest.ReqStatus result = road.data.proto.DriveRequest.ReqStatus.valueOf(status_);
      return result == null ? road.data.proto.DriveRequest.ReqStatus.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *请求消息的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveRequest.ReqStatus status = 2;</code>
     */
    public Builder setStatus(road.data.proto.DriveRequest.ReqStatus value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      status_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *请求消息的状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveRequest.ReqStatus status = 2;</code>
     */
    public Builder clearStatus() {
      
      status_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object reqPriority_ = "";
    /**
     * <pre>
     *可选，低五位保留，应设置为零从 ********* 到 ********* 的值代表最低到最高级别
     * </pre>
     *
     * <code>string reqPriority = 3;</code>
     */
    public java.lang.String getReqPriority() {
      java.lang.Object ref = reqPriority_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        reqPriority_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，低五位保留，应设置为零从 ********* 到 ********* 的值代表最低到最高级别
     * </pre>
     *
     * <code>string reqPriority = 3;</code>
     */
    public com.google.protobuf.ByteString
        getReqPriorityBytes() {
      java.lang.Object ref = reqPriority_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reqPriority_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，低五位保留，应设置为零从 ********* 到 ********* 的值代表最低到最高级别
     * </pre>
     *
     * <code>string reqPriority = 3;</code>
     */
    public Builder setReqPriority(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      reqPriority_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，低五位保留，应设置为零从 ********* 到 ********* 的值代表最低到最高级别
     * </pre>
     *
     * <code>string reqPriority = 3;</code>
     */
    public Builder clearReqPriority() {
      
      reqPriority_ = getDefaultInstance().getReqPriority();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，低五位保留，应设置为零从 ********* 到 ********* 的值代表最低到最高级别
     * </pre>
     *
     * <code>string reqPriority = 3;</code>
     */
    public Builder setReqPriorityBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      reqPriority_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object targetVeh_ = "";
    /**
     * <pre>
     *可选，目标车辆的临时ID
     * </pre>
     *
     * <code>string targetVeh = 4;</code>
     */
    public java.lang.String getTargetVeh() {
      java.lang.Object ref = targetVeh_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        targetVeh_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，目标车辆的临时ID
     * </pre>
     *
     * <code>string targetVeh = 4;</code>
     */
    public com.google.protobuf.ByteString
        getTargetVehBytes() {
      java.lang.Object ref = targetVeh_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        targetVeh_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，目标车辆的临时ID
     * </pre>
     *
     * <code>string targetVeh = 4;</code>
     */
    public Builder setTargetVeh(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      targetVeh_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，目标车辆的临时ID
     * </pre>
     *
     * <code>string targetVeh = 4;</code>
     */
    public Builder clearTargetVeh() {
      
      targetVeh_ = getDefaultInstance().getTargetVeh();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，目标车辆的临时ID
     * </pre>
     *
     * <code>string targetVeh = 4;</code>
     */
    public Builder setTargetVehBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      targetVeh_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object targetRsu_ = "";
    /**
     * <pre>
     *可选，目标 RSU 的临时 ID
     * </pre>
     *
     * <code>string targetRsu = 5;</code>
     */
    public java.lang.String getTargetRsu() {
      java.lang.Object ref = targetRsu_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        targetRsu_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，目标 RSU 的临时 ID
     * </pre>
     *
     * <code>string targetRsu = 5;</code>
     */
    public com.google.protobuf.ByteString
        getTargetRsuBytes() {
      java.lang.Object ref = targetRsu_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        targetRsu_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，目标 RSU 的临时 ID
     * </pre>
     *
     * <code>string targetRsu = 5;</code>
     */
    public Builder setTargetRsu(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      targetRsu_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，目标 RSU 的临时 ID
     * </pre>
     *
     * <code>string targetRsu = 5;</code>
     */
    public Builder clearTargetRsu() {
      
      targetRsu_ = getDefaultInstance().getTargetRsu();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，目标 RSU 的临时 ID
     * </pre>
     *
     * <code>string targetRsu = 5;</code>
     */
    public Builder setTargetRsuBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      targetRsu_ = value;
      onChanged();
      return this;
    }

    private road.data.proto.ReqInfo info_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReqInfo, road.data.proto.ReqInfo.Builder, road.data.proto.ReqInfoOrBuilder> infoBuilder_;
    /**
     * <pre>
     *可选，请求信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
     */
    public boolean hasInfo() {
      return infoBuilder_ != null || info_ != null;
    }
    /**
     * <pre>
     *可选，请求信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
     */
    public road.data.proto.ReqInfo getInfo() {
      if (infoBuilder_ == null) {
        return info_ == null ? road.data.proto.ReqInfo.getDefaultInstance() : info_;
      } else {
        return infoBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，请求信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
     */
    public Builder setInfo(road.data.proto.ReqInfo value) {
      if (infoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        info_ = value;
        onChanged();
      } else {
        infoBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，请求信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
     */
    public Builder setInfo(
        road.data.proto.ReqInfo.Builder builderForValue) {
      if (infoBuilder_ == null) {
        info_ = builderForValue.build();
        onChanged();
      } else {
        infoBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，请求信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
     */
    public Builder mergeInfo(road.data.proto.ReqInfo value) {
      if (infoBuilder_ == null) {
        if (info_ != null) {
          info_ =
            road.data.proto.ReqInfo.newBuilder(info_).mergeFrom(value).buildPartial();
        } else {
          info_ = value;
        }
        onChanged();
      } else {
        infoBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，请求信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
     */
    public Builder clearInfo() {
      if (infoBuilder_ == null) {
        info_ = null;
        onChanged();
      } else {
        info_ = null;
        infoBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，请求信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
     */
    public road.data.proto.ReqInfo.Builder getInfoBuilder() {
      
      onChanged();
      return getInfoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，请求信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
     */
    public road.data.proto.ReqInfoOrBuilder getInfoOrBuilder() {
      if (infoBuilder_ != null) {
        return infoBuilder_.getMessageOrBuilder();
      } else {
        return info_ == null ?
            road.data.proto.ReqInfo.getDefaultInstance() : info_;
      }
    }
    /**
     * <pre>
     *可选，请求信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReqInfo, road.data.proto.ReqInfo.Builder, road.data.proto.ReqInfoOrBuilder> 
        getInfoFieldBuilder() {
      if (infoBuilder_ == null) {
        infoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ReqInfo, road.data.proto.ReqInfo.Builder, road.data.proto.ReqInfoOrBuilder>(
                getInfo(),
                getParentForChildren(),
                isClean());
        info_ = null;
      }
      return infoBuilder_;
    }

    private int lifeTime_ ;
    /**
     * <pre>
     *可选，以10毫秒为单位，此请求的生命周期时间偏移量
     * </pre>
     *
     * <code>uint32 lifeTime = 7;</code>
     */
    public int getLifeTime() {
      return lifeTime_;
    }
    /**
     * <pre>
     *可选，以10毫秒为单位，此请求的生命周期时间偏移量
     * </pre>
     *
     * <code>uint32 lifeTime = 7;</code>
     */
    public Builder setLifeTime(int value) {
      
      lifeTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，以10毫秒为单位，此请求的生命周期时间偏移量
     * </pre>
     *
     * <code>uint32 lifeTime = 7;</code>
     */
    public Builder clearLifeTime() {
      
      lifeTime_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.DriveRequest)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.DriveRequest)
  private static final road.data.proto.DriveRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.DriveRequest();
  }

  public static road.data.proto.DriveRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DriveRequest>
      PARSER = new com.google.protobuf.AbstractParser<DriveRequest>() {
    @java.lang.Override
    public DriveRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new DriveRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<DriveRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DriveRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.DriveRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

