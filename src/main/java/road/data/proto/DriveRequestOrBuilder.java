// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface DriveRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.DriveRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *本次请求的本地ID串行 VIR 消息中的相同请求应保持相同的 reqId状态请求状态
   * </pre>
   *
   * <code>uint32 reqId = 1;</code>
   */
  int getReqId();

  /**
   * <pre>
   *请求消息的状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveRequest.ReqStatus status = 2;</code>
   */
  int getStatusValue();
  /**
   * <pre>
   *请求消息的状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveRequest.ReqStatus status = 2;</code>
   */
  road.data.proto.DriveRequest.ReqStatus getStatus();

  /**
   * <pre>
   *可选，低五位保留，应设置为零从 ********* 到 ********* 的值代表最低到最高级别
   * </pre>
   *
   * <code>string reqPriority = 3;</code>
   */
  java.lang.String getReqPriority();
  /**
   * <pre>
   *可选，低五位保留，应设置为零从 ********* 到 ********* 的值代表最低到最高级别
   * </pre>
   *
   * <code>string reqPriority = 3;</code>
   */
  com.google.protobuf.ByteString
      getReqPriorityBytes();

  /**
   * <pre>
   *可选，目标车辆的临时ID
   * </pre>
   *
   * <code>string targetVeh = 4;</code>
   */
  java.lang.String getTargetVeh();
  /**
   * <pre>
   *可选，目标车辆的临时ID
   * </pre>
   *
   * <code>string targetVeh = 4;</code>
   */
  com.google.protobuf.ByteString
      getTargetVehBytes();

  /**
   * <pre>
   *可选，目标 RSU 的临时 ID
   * </pre>
   *
   * <code>string targetRsu = 5;</code>
   */
  java.lang.String getTargetRsu();
  /**
   * <pre>
   *可选，目标 RSU 的临时 ID
   * </pre>
   *
   * <code>string targetRsu = 5;</code>
   */
  com.google.protobuf.ByteString
      getTargetRsuBytes();

  /**
   * <pre>
   *可选，请求信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
   */
  boolean hasInfo();
  /**
   * <pre>
   *可选，请求信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
   */
  road.data.proto.ReqInfo getInfo();
  /**
   * <pre>
   *可选，请求信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqInfo info = 6;</code>
   */
  road.data.proto.ReqInfoOrBuilder getInfoOrBuilder();

  /**
   * <pre>
   *可选，以10毫秒为单位，此请求的生命周期时间偏移量
   * </pre>
   *
   * <code>uint32 lifeTime = 7;</code>
   */
  int getLifeTime();
}
