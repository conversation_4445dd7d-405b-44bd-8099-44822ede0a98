// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *驾驶建议 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.DriveSuggestion}
 */
public  final class DriveSuggestion extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.DriveSuggestion)
    DriveSuggestionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use DriveSuggestion.newBuilder() to construct.
  private DriveSuggestion(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private DriveSuggestion() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new DriveSuggestion();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private DriveSuggestion(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.DriveBehavior.Builder subBuilder = null;
            if (suggestion_ != null) {
              subBuilder = suggestion_.toBuilder();
            }
            suggestion_ = input.readMessage(road.data.proto.DriveBehavior.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(suggestion_);
              suggestion_ = subBuilder.buildPartial();
            }

            break;
          }
          case 16: {

            timeOffset_ = input.readUInt32();
            break;
          }
          case 26: {
            road.data.proto.ReferenceLink.Builder subBuilder = null;
            if (relatedLink_ != null) {
              subBuilder = relatedLink_.toBuilder();
            }
            relatedLink_ = input.readMessage(road.data.proto.ReferenceLink.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(relatedLink_);
              relatedLink_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            road.data.proto.ReferencePath.Builder subBuilder = null;
            if (relatedPath_ != null) {
              subBuilder = relatedPath_.toBuilder();
            }
            relatedPath_ = input.readMessage(road.data.proto.ReferencePath.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(relatedPath_);
              relatedPath_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveSuggestion_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveSuggestion_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.DriveSuggestion.class, road.data.proto.DriveSuggestion.Builder.class);
  }

  public static final int SUGGESTION_FIELD_NUMBER = 1;
  private road.data.proto.DriveBehavior suggestion_;
  /**
   * <pre>
   *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
   */
  public boolean hasSuggestion() {
    return suggestion_ != null;
  }
  /**
   * <pre>
   *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
   */
  public road.data.proto.DriveBehavior getSuggestion() {
    return suggestion_ == null ? road.data.proto.DriveBehavior.getDefaultInstance() : suggestion_;
  }
  /**
   * <pre>
   *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
   */
  public road.data.proto.DriveBehaviorOrBuilder getSuggestionOrBuilder() {
    return getSuggestion();
  }

  public static final int TIMEOFFSET_FIELD_NUMBER = 2;
  private int timeOffset_;
  /**
   * <pre>
   *可选，以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据
   * </pre>
   *
   * <code>uint32 timeOffset = 2;</code>
   */
  public int getTimeOffset() {
    return timeOffset_;
  }

  public static final int RELATEDLINK_FIELD_NUMBER = 3;
  private road.data.proto.ReferenceLink relatedLink_;
  /**
   * <pre>
   *可选，车辆决定是否遵循建议的额外判断条件
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
   */
  public boolean hasRelatedLink() {
    return relatedLink_ != null;
  }
  /**
   * <pre>
   *可选，车辆决定是否遵循建议的额外判断条件
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
   */
  public road.data.proto.ReferenceLink getRelatedLink() {
    return relatedLink_ == null ? road.data.proto.ReferenceLink.getDefaultInstance() : relatedLink_;
  }
  /**
   * <pre>
   *可选，车辆决定是否遵循建议的额外判断条件
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
   */
  public road.data.proto.ReferenceLinkOrBuilder getRelatedLinkOrBuilder() {
    return getRelatedLink();
  }

  public static final int RELATEDPATH_FIELD_NUMBER = 4;
  private road.data.proto.ReferencePath relatedPath_;
  /**
   * <pre>
   *可选，车辆决定是否遵循建议的额外判断条件
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
   */
  public boolean hasRelatedPath() {
    return relatedPath_ != null;
  }
  /**
   * <pre>
   *可选，车辆决定是否遵循建议的额外判断条件
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
   */
  public road.data.proto.ReferencePath getRelatedPath() {
    return relatedPath_ == null ? road.data.proto.ReferencePath.getDefaultInstance() : relatedPath_;
  }
  /**
   * <pre>
   *可选，车辆决定是否遵循建议的额外判断条件
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
   */
  public road.data.proto.ReferencePathOrBuilder getRelatedPathOrBuilder() {
    return getRelatedPath();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (suggestion_ != null) {
      output.writeMessage(1, getSuggestion());
    }
    if (timeOffset_ != 0) {
      output.writeUInt32(2, timeOffset_);
    }
    if (relatedLink_ != null) {
      output.writeMessage(3, getRelatedLink());
    }
    if (relatedPath_ != null) {
      output.writeMessage(4, getRelatedPath());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (suggestion_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getSuggestion());
    }
    if (timeOffset_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, timeOffset_);
    }
    if (relatedLink_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getRelatedLink());
    }
    if (relatedPath_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getRelatedPath());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.DriveSuggestion)) {
      return super.equals(obj);
    }
    road.data.proto.DriveSuggestion other = (road.data.proto.DriveSuggestion) obj;

    if (hasSuggestion() != other.hasSuggestion()) return false;
    if (hasSuggestion()) {
      if (!getSuggestion()
          .equals(other.getSuggestion())) return false;
    }
    if (getTimeOffset()
        != other.getTimeOffset()) return false;
    if (hasRelatedLink() != other.hasRelatedLink()) return false;
    if (hasRelatedLink()) {
      if (!getRelatedLink()
          .equals(other.getRelatedLink())) return false;
    }
    if (hasRelatedPath() != other.hasRelatedPath()) return false;
    if (hasRelatedPath()) {
      if (!getRelatedPath()
          .equals(other.getRelatedPath())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasSuggestion()) {
      hash = (37 * hash) + SUGGESTION_FIELD_NUMBER;
      hash = (53 * hash) + getSuggestion().hashCode();
    }
    hash = (37 * hash) + TIMEOFFSET_FIELD_NUMBER;
    hash = (53 * hash) + getTimeOffset();
    if (hasRelatedLink()) {
      hash = (37 * hash) + RELATEDLINK_FIELD_NUMBER;
      hash = (53 * hash) + getRelatedLink().hashCode();
    }
    if (hasRelatedPath()) {
      hash = (37 * hash) + RELATEDPATH_FIELD_NUMBER;
      hash = (53 * hash) + getRelatedPath().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.DriveSuggestion parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DriveSuggestion parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DriveSuggestion parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DriveSuggestion parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DriveSuggestion parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.DriveSuggestion parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.DriveSuggestion parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.DriveSuggestion parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.DriveSuggestion parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.DriveSuggestion parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.DriveSuggestion parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.DriveSuggestion parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.DriveSuggestion prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *驾驶建议 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.DriveSuggestion}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.DriveSuggestion)
      road.data.proto.DriveSuggestionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveSuggestion_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveSuggestion_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.DriveSuggestion.class, road.data.proto.DriveSuggestion.Builder.class);
    }

    // Construct using road.data.proto.DriveSuggestion.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (suggestionBuilder_ == null) {
        suggestion_ = null;
      } else {
        suggestion_ = null;
        suggestionBuilder_ = null;
      }
      timeOffset_ = 0;

      if (relatedLinkBuilder_ == null) {
        relatedLink_ = null;
      } else {
        relatedLink_ = null;
        relatedLinkBuilder_ = null;
      }
      if (relatedPathBuilder_ == null) {
        relatedPath_ = null;
      } else {
        relatedPath_ = null;
        relatedPathBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_DriveSuggestion_descriptor;
    }

    @java.lang.Override
    public road.data.proto.DriveSuggestion getDefaultInstanceForType() {
      return road.data.proto.DriveSuggestion.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.DriveSuggestion build() {
      road.data.proto.DriveSuggestion result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.DriveSuggestion buildPartial() {
      road.data.proto.DriveSuggestion result = new road.data.proto.DriveSuggestion(this);
      if (suggestionBuilder_ == null) {
        result.suggestion_ = suggestion_;
      } else {
        result.suggestion_ = suggestionBuilder_.build();
      }
      result.timeOffset_ = timeOffset_;
      if (relatedLinkBuilder_ == null) {
        result.relatedLink_ = relatedLink_;
      } else {
        result.relatedLink_ = relatedLinkBuilder_.build();
      }
      if (relatedPathBuilder_ == null) {
        result.relatedPath_ = relatedPath_;
      } else {
        result.relatedPath_ = relatedPathBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.DriveSuggestion) {
        return mergeFrom((road.data.proto.DriveSuggestion)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.DriveSuggestion other) {
      if (other == road.data.proto.DriveSuggestion.getDefaultInstance()) return this;
      if (other.hasSuggestion()) {
        mergeSuggestion(other.getSuggestion());
      }
      if (other.getTimeOffset() != 0) {
        setTimeOffset(other.getTimeOffset());
      }
      if (other.hasRelatedLink()) {
        mergeRelatedLink(other.getRelatedLink());
      }
      if (other.hasRelatedPath()) {
        mergeRelatedPath(other.getRelatedPath());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.DriveSuggestion parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.DriveSuggestion) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.DriveBehavior suggestion_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.DriveBehavior, road.data.proto.DriveBehavior.Builder, road.data.proto.DriveBehaviorOrBuilder> suggestionBuilder_;
    /**
     * <pre>
     *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
     */
    public boolean hasSuggestion() {
      return suggestionBuilder_ != null || suggestion_ != null;
    }
    /**
     * <pre>
     *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
     */
    public road.data.proto.DriveBehavior getSuggestion() {
      if (suggestionBuilder_ == null) {
        return suggestion_ == null ? road.data.proto.DriveBehavior.getDefaultInstance() : suggestion_;
      } else {
        return suggestionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
     */
    public Builder setSuggestion(road.data.proto.DriveBehavior value) {
      if (suggestionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        suggestion_ = value;
        onChanged();
      } else {
        suggestionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
     */
    public Builder setSuggestion(
        road.data.proto.DriveBehavior.Builder builderForValue) {
      if (suggestionBuilder_ == null) {
        suggestion_ = builderForValue.build();
        onChanged();
      } else {
        suggestionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
     */
    public Builder mergeSuggestion(road.data.proto.DriveBehavior value) {
      if (suggestionBuilder_ == null) {
        if (suggestion_ != null) {
          suggestion_ =
            road.data.proto.DriveBehavior.newBuilder(suggestion_).mergeFrom(value).buildPartial();
        } else {
          suggestion_ = value;
        }
        onChanged();
      } else {
        suggestionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
     */
    public Builder clearSuggestion() {
      if (suggestionBuilder_ == null) {
        suggestion_ = null;
        onChanged();
      } else {
        suggestion_ = null;
        suggestionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
     */
    public road.data.proto.DriveBehavior.Builder getSuggestionBuilder() {
      
      onChanged();
      return getSuggestionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
     */
    public road.data.proto.DriveBehaviorOrBuilder getSuggestionOrBuilder() {
      if (suggestionBuilder_ != null) {
        return suggestionBuilder_.getMessageOrBuilder();
      } else {
        return suggestion_ == null ?
            road.data.proto.DriveBehavior.getDefaultInstance() : suggestion_;
      }
    }
    /**
     * <pre>
     *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.DriveBehavior, road.data.proto.DriveBehavior.Builder, road.data.proto.DriveBehaviorOrBuilder> 
        getSuggestionFieldBuilder() {
      if (suggestionBuilder_ == null) {
        suggestionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.DriveBehavior, road.data.proto.DriveBehavior.Builder, road.data.proto.DriveBehaviorOrBuilder>(
                getSuggestion(),
                getParentForChildren(),
                isClean());
        suggestion_ = null;
      }
      return suggestionBuilder_;
    }

    private int timeOffset_ ;
    /**
     * <pre>
     *可选，以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据
     * </pre>
     *
     * <code>uint32 timeOffset = 2;</code>
     */
    public int getTimeOffset() {
      return timeOffset_;
    }
    /**
     * <pre>
     *可选，以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据
     * </pre>
     *
     * <code>uint32 timeOffset = 2;</code>
     */
    public Builder setTimeOffset(int value) {
      
      timeOffset_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据
     * </pre>
     *
     * <code>uint32 timeOffset = 2;</code>
     */
    public Builder clearTimeOffset() {
      
      timeOffset_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.ReferenceLink relatedLink_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder> relatedLinkBuilder_;
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
     */
    public boolean hasRelatedLink() {
      return relatedLinkBuilder_ != null || relatedLink_ != null;
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
     */
    public road.data.proto.ReferenceLink getRelatedLink() {
      if (relatedLinkBuilder_ == null) {
        return relatedLink_ == null ? road.data.proto.ReferenceLink.getDefaultInstance() : relatedLink_;
      } else {
        return relatedLinkBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
     */
    public Builder setRelatedLink(road.data.proto.ReferenceLink value) {
      if (relatedLinkBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        relatedLink_ = value;
        onChanged();
      } else {
        relatedLinkBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
     */
    public Builder setRelatedLink(
        road.data.proto.ReferenceLink.Builder builderForValue) {
      if (relatedLinkBuilder_ == null) {
        relatedLink_ = builderForValue.build();
        onChanged();
      } else {
        relatedLinkBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
     */
    public Builder mergeRelatedLink(road.data.proto.ReferenceLink value) {
      if (relatedLinkBuilder_ == null) {
        if (relatedLink_ != null) {
          relatedLink_ =
            road.data.proto.ReferenceLink.newBuilder(relatedLink_).mergeFrom(value).buildPartial();
        } else {
          relatedLink_ = value;
        }
        onChanged();
      } else {
        relatedLinkBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
     */
    public Builder clearRelatedLink() {
      if (relatedLinkBuilder_ == null) {
        relatedLink_ = null;
        onChanged();
      } else {
        relatedLink_ = null;
        relatedLinkBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
     */
    public road.data.proto.ReferenceLink.Builder getRelatedLinkBuilder() {
      
      onChanged();
      return getRelatedLinkFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
     */
    public road.data.proto.ReferenceLinkOrBuilder getRelatedLinkOrBuilder() {
      if (relatedLinkBuilder_ != null) {
        return relatedLinkBuilder_.getMessageOrBuilder();
      } else {
        return relatedLink_ == null ?
            road.data.proto.ReferenceLink.getDefaultInstance() : relatedLink_;
      }
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder> 
        getRelatedLinkFieldBuilder() {
      if (relatedLinkBuilder_ == null) {
        relatedLinkBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder>(
                getRelatedLink(),
                getParentForChildren(),
                isClean());
        relatedLink_ = null;
      }
      return relatedLinkBuilder_;
    }

    private road.data.proto.ReferencePath relatedPath_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder> relatedPathBuilder_;
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
     */
    public boolean hasRelatedPath() {
      return relatedPathBuilder_ != null || relatedPath_ != null;
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
     */
    public road.data.proto.ReferencePath getRelatedPath() {
      if (relatedPathBuilder_ == null) {
        return relatedPath_ == null ? road.data.proto.ReferencePath.getDefaultInstance() : relatedPath_;
      } else {
        return relatedPathBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
     */
    public Builder setRelatedPath(road.data.proto.ReferencePath value) {
      if (relatedPathBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        relatedPath_ = value;
        onChanged();
      } else {
        relatedPathBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
     */
    public Builder setRelatedPath(
        road.data.proto.ReferencePath.Builder builderForValue) {
      if (relatedPathBuilder_ == null) {
        relatedPath_ = builderForValue.build();
        onChanged();
      } else {
        relatedPathBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
     */
    public Builder mergeRelatedPath(road.data.proto.ReferencePath value) {
      if (relatedPathBuilder_ == null) {
        if (relatedPath_ != null) {
          relatedPath_ =
            road.data.proto.ReferencePath.newBuilder(relatedPath_).mergeFrom(value).buildPartial();
        } else {
          relatedPath_ = value;
        }
        onChanged();
      } else {
        relatedPathBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
     */
    public Builder clearRelatedPath() {
      if (relatedPathBuilder_ == null) {
        relatedPath_ = null;
        onChanged();
      } else {
        relatedPath_ = null;
        relatedPathBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
     */
    public road.data.proto.ReferencePath.Builder getRelatedPathBuilder() {
      
      onChanged();
      return getRelatedPathFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
     */
    public road.data.proto.ReferencePathOrBuilder getRelatedPathOrBuilder() {
      if (relatedPathBuilder_ != null) {
        return relatedPathBuilder_.getMessageOrBuilder();
      } else {
        return relatedPath_ == null ?
            road.data.proto.ReferencePath.getDefaultInstance() : relatedPath_;
      }
    }
    /**
     * <pre>
     *可选，车辆决定是否遵循建议的额外判断条件
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder> 
        getRelatedPathFieldBuilder() {
      if (relatedPathBuilder_ == null) {
        relatedPathBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder>(
                getRelatedPath(),
                getParentForChildren(),
                isClean());
        relatedPath_ = null;
      }
      return relatedPathBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.DriveSuggestion)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.DriveSuggestion)
  private static final road.data.proto.DriveSuggestion DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.DriveSuggestion();
  }

  public static road.data.proto.DriveSuggestion getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DriveSuggestion>
      PARSER = new com.google.protobuf.AbstractParser<DriveSuggestion>() {
    @java.lang.Override
    public DriveSuggestion parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new DriveSuggestion(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<DriveSuggestion> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DriveSuggestion> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.DriveSuggestion getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

