// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface DriveSuggestionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.DriveSuggestion)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
   */
  boolean hasSuggestion();
  /**
   * <pre>
   *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
   */
  road.data.proto.DriveBehavior getSuggestion();
  /**
   * <pre>
   *允许或推荐的驾驶行为,在以下时间范围内 如果匹配相关链接或路径
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior suggestion = 1;</code>
   */
  road.data.proto.DriveBehaviorOrBuilder getSuggestionOrBuilder();

  /**
   * <pre>
   *可选，以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据
   * </pre>
   *
   * <code>uint32 timeOffset = 2;</code>
   */
  int getTimeOffset();

  /**
   * <pre>
   *可选，车辆决定是否遵循建议的额外判断条件
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
   */
  boolean hasRelatedLink();
  /**
   * <pre>
   *可选，车辆决定是否遵循建议的额外判断条件
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
   */
  road.data.proto.ReferenceLink getRelatedLink();
  /**
   * <pre>
   *可选，车辆决定是否遵循建议的额外判断条件
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink relatedLink = 3;</code>
   */
  road.data.proto.ReferenceLinkOrBuilder getRelatedLinkOrBuilder();

  /**
   * <pre>
   *可选，车辆决定是否遵循建议的额外判断条件
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
   */
  boolean hasRelatedPath();
  /**
   * <pre>
   *可选，车辆决定是否遵循建议的额外判断条件
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
   */
  road.data.proto.ReferencePath getRelatedPath();
  /**
   * <pre>
   *可选，车辆决定是否遵循建议的额外判断条件
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 4;</code>
   */
  road.data.proto.ReferencePathOrBuilder getRelatedPathOrBuilder();
}
