// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *方向  
 * </pre>
 *
 * Protobuf enum {@code cn.seisys.v2x.pb.HeadingConfidence}
 */
public enum HeadingConfidence
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>HEADING_CONFID_UNAVAILABLE = 0;</code>
   */
  HEADING_CONFID_UNAVAILABLE(0),
  /**
   * <code>HEADING_CONFID_PREC10DEG = 1;</code>
   */
  HEADING_CONFID_PREC10DEG(1),
  /**
   * <code>HEADING_CONFIDE_PREC05DEG = 2;</code>
   */
  HEADING_CONFIDE_PREC05DEG(2),
  /**
   * <code>HEADING_CONFIDE_PREC01DEG = 3;</code>
   */
  HEADING_CONFIDE_PREC01DEG(3),
  /**
   * <code>HEADING_CONFID_PREC_1DEG = 4;</code>
   */
  HEADING_CONFID_PREC_1DEG(4),
  /**
   * <code>HEADING_CONFID_PREC0_05DEG = 5;</code>
   */
  HEADING_CONFID_PREC0_05DEG(5),
  /**
   * <code>HEADING_CONFID_PREC0_01DEG = 6;</code>
   */
  HEADING_CONFID_PREC0_01DEG(6),
  /**
   * <code>HEADING_CONFID_PREC0_0125DEG = 7;</code>
   */
  HEADING_CONFID_PREC0_0125DEG(7),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>HEADING_CONFID_UNAVAILABLE = 0;</code>
   */
  public static final int HEADING_CONFID_UNAVAILABLE_VALUE = 0;
  /**
   * <code>HEADING_CONFID_PREC10DEG = 1;</code>
   */
  public static final int HEADING_CONFID_PREC10DEG_VALUE = 1;
  /**
   * <code>HEADING_CONFIDE_PREC05DEG = 2;</code>
   */
  public static final int HEADING_CONFIDE_PREC05DEG_VALUE = 2;
  /**
   * <code>HEADING_CONFIDE_PREC01DEG = 3;</code>
   */
  public static final int HEADING_CONFIDE_PREC01DEG_VALUE = 3;
  /**
   * <code>HEADING_CONFID_PREC_1DEG = 4;</code>
   */
  public static final int HEADING_CONFID_PREC_1DEG_VALUE = 4;
  /**
   * <code>HEADING_CONFID_PREC0_05DEG = 5;</code>
   */
  public static final int HEADING_CONFID_PREC0_05DEG_VALUE = 5;
  /**
   * <code>HEADING_CONFID_PREC0_01DEG = 6;</code>
   */
  public static final int HEADING_CONFID_PREC0_01DEG_VALUE = 6;
  /**
   * <code>HEADING_CONFID_PREC0_0125DEG = 7;</code>
   */
  public static final int HEADING_CONFID_PREC0_0125DEG_VALUE = 7;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static HeadingConfidence valueOf(int value) {
    return forNumber(value);
  }

  public static HeadingConfidence forNumber(int value) {
    switch (value) {
      case 0: return HEADING_CONFID_UNAVAILABLE;
      case 1: return HEADING_CONFID_PREC10DEG;
      case 2: return HEADING_CONFIDE_PREC05DEG;
      case 3: return HEADING_CONFIDE_PREC01DEG;
      case 4: return HEADING_CONFID_PREC_1DEG;
      case 5: return HEADING_CONFID_PREC0_05DEG;
      case 6: return HEADING_CONFID_PREC0_01DEG;
      case 7: return HEADING_CONFID_PREC0_0125DEG;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<HeadingConfidence>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      HeadingConfidence> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<HeadingConfidence>() {
          public HeadingConfidence findValueByNumber(int number) {
            return HeadingConfidence.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return road.data.proto.V2X.getDescriptor().getEnumTypes().get(5);
  }

  private static final HeadingConfidence[] VALUES = values();

  public static HeadingConfidence valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private HeadingConfidence(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.HeadingConfidence)
}

