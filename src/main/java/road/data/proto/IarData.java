// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车辆意图及请求VirData 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.IarData}
 */
public  final class IarData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.IarData)
    IarDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use IarData.newBuilder() to construct.
  private IarData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private IarData() {
    reqs_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new IarData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private IarData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.PathPlanningPoint.Builder subBuilder = null;
            if (currentPos_ != null) {
              subBuilder = currentPos_.toBuilder();
            }
            currentPos_ = input.readMessage(road.data.proto.PathPlanningPoint.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(currentPos_);
              currentPos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            road.data.proto.PathPlanning.Builder subBuilder = null;
            if (pathPlanning_ != null) {
              subBuilder = pathPlanning_.toBuilder();
            }
            pathPlanning_ = input.readMessage(road.data.proto.PathPlanning.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(pathPlanning_);
              pathPlanning_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            road.data.proto.DriveBehavior.Builder subBuilder = null;
            if (currentBehavior_ != null) {
              subBuilder = currentBehavior_.toBuilder();
            }
            currentBehavior_ = input.readMessage(road.data.proto.DriveBehavior.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(currentBehavior_);
              currentBehavior_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              reqs_ = new java.util.ArrayList<road.data.proto.DriveRequest>();
              mutable_bitField0_ |= 0x00000001;
            }
            reqs_.add(
                input.readMessage(road.data.proto.DriveRequest.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        reqs_ = java.util.Collections.unmodifiableList(reqs_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_IarData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_IarData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.IarData.class, road.data.proto.IarData.Builder.class);
  }

  public static final int CURRENTPOS_FIELD_NUMBER = 1;
  private road.data.proto.PathPlanningPoint currentPos_;
  /**
   * <pre>
   *可选，地图中的当前位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
   */
  public boolean hasCurrentPos() {
    return currentPos_ != null;
  }
  /**
   * <pre>
   *可选，地图中的当前位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
   */
  public road.data.proto.PathPlanningPoint getCurrentPos() {
    return currentPos_ == null ? road.data.proto.PathPlanningPoint.getDefaultInstance() : currentPos_;
  }
  /**
   * <pre>
   *可选，地图中的当前位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
   */
  public road.data.proto.PathPlanningPointOrBuilder getCurrentPosOrBuilder() {
    return getCurrentPos();
  }

  public static final int PATHPLANNING_FIELD_NUMBER = 2;
  private road.data.proto.PathPlanning pathPlanning_;
  /**
   * <pre>
   *可选，共享的实时路径规划,按时间顺序列出
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
   */
  public boolean hasPathPlanning() {
    return pathPlanning_ != null;
  }
  /**
   * <pre>
   *可选，共享的实时路径规划,按时间顺序列出
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
   */
  public road.data.proto.PathPlanning getPathPlanning() {
    return pathPlanning_ == null ? road.data.proto.PathPlanning.getDefaultInstance() : pathPlanning_;
  }
  /**
   * <pre>
   *可选，共享的实时路径规划,按时间顺序列出
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
   */
  public road.data.proto.PathPlanningOrBuilder getPathPlanningOrBuilder() {
    return getPathPlanning();
  }

  public static final int CURRENTBEHAVIOR_FIELD_NUMBER = 3;
  private road.data.proto.DriveBehavior currentBehavior_;
  /**
   * <pre>
   *可选，与路径规划相关的驱动行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
   */
  public boolean hasCurrentBehavior() {
    return currentBehavior_ != null;
  }
  /**
   * <pre>
   *可选，与路径规划相关的驱动行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
   */
  public road.data.proto.DriveBehavior getCurrentBehavior() {
    return currentBehavior_ == null ? road.data.proto.DriveBehavior.getDefaultInstance() : currentBehavior_;
  }
  /**
   * <pre>
   *可选，与路径规划相关的驱动行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
   */
  public road.data.proto.DriveBehaviorOrBuilder getCurrentBehaviorOrBuilder() {
    return getCurrentBehavior();
  }

  public static final int REQS_FIELD_NUMBER = 4;
  private java.util.List<road.data.proto.DriveRequest> reqs_;
  /**
   * <pre>
   *可选，请求序列
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
   */
  public java.util.List<road.data.proto.DriveRequest> getReqsList() {
    return reqs_;
  }
  /**
   * <pre>
   *可选，请求序列
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
   */
  public java.util.List<? extends road.data.proto.DriveRequestOrBuilder> 
      getReqsOrBuilderList() {
    return reqs_;
  }
  /**
   * <pre>
   *可选，请求序列
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
   */
  public int getReqsCount() {
    return reqs_.size();
  }
  /**
   * <pre>
   *可选，请求序列
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
   */
  public road.data.proto.DriveRequest getReqs(int index) {
    return reqs_.get(index);
  }
  /**
   * <pre>
   *可选，请求序列
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
   */
  public road.data.proto.DriveRequestOrBuilder getReqsOrBuilder(
      int index) {
    return reqs_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (currentPos_ != null) {
      output.writeMessage(1, getCurrentPos());
    }
    if (pathPlanning_ != null) {
      output.writeMessage(2, getPathPlanning());
    }
    if (currentBehavior_ != null) {
      output.writeMessage(3, getCurrentBehavior());
    }
    for (int i = 0; i < reqs_.size(); i++) {
      output.writeMessage(4, reqs_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (currentPos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getCurrentPos());
    }
    if (pathPlanning_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getPathPlanning());
    }
    if (currentBehavior_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getCurrentBehavior());
    }
    for (int i = 0; i < reqs_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, reqs_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.IarData)) {
      return super.equals(obj);
    }
    road.data.proto.IarData other = (road.data.proto.IarData) obj;

    if (hasCurrentPos() != other.hasCurrentPos()) return false;
    if (hasCurrentPos()) {
      if (!getCurrentPos()
          .equals(other.getCurrentPos())) return false;
    }
    if (hasPathPlanning() != other.hasPathPlanning()) return false;
    if (hasPathPlanning()) {
      if (!getPathPlanning()
          .equals(other.getPathPlanning())) return false;
    }
    if (hasCurrentBehavior() != other.hasCurrentBehavior()) return false;
    if (hasCurrentBehavior()) {
      if (!getCurrentBehavior()
          .equals(other.getCurrentBehavior())) return false;
    }
    if (!getReqsList()
        .equals(other.getReqsList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasCurrentPos()) {
      hash = (37 * hash) + CURRENTPOS_FIELD_NUMBER;
      hash = (53 * hash) + getCurrentPos().hashCode();
    }
    if (hasPathPlanning()) {
      hash = (37 * hash) + PATHPLANNING_FIELD_NUMBER;
      hash = (53 * hash) + getPathPlanning().hashCode();
    }
    if (hasCurrentBehavior()) {
      hash = (37 * hash) + CURRENTBEHAVIOR_FIELD_NUMBER;
      hash = (53 * hash) + getCurrentBehavior().hashCode();
    }
    if (getReqsCount() > 0) {
      hash = (37 * hash) + REQS_FIELD_NUMBER;
      hash = (53 * hash) + getReqsList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.IarData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.IarData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.IarData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.IarData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.IarData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.IarData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.IarData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.IarData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.IarData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.IarData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.IarData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.IarData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.IarData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *车辆意图及请求VirData 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.IarData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.IarData)
      road.data.proto.IarDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_IarData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_IarData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.IarData.class, road.data.proto.IarData.Builder.class);
    }

    // Construct using road.data.proto.IarData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getReqsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (currentPosBuilder_ == null) {
        currentPos_ = null;
      } else {
        currentPos_ = null;
        currentPosBuilder_ = null;
      }
      if (pathPlanningBuilder_ == null) {
        pathPlanning_ = null;
      } else {
        pathPlanning_ = null;
        pathPlanningBuilder_ = null;
      }
      if (currentBehaviorBuilder_ == null) {
        currentBehavior_ = null;
      } else {
        currentBehavior_ = null;
        currentBehaviorBuilder_ = null;
      }
      if (reqsBuilder_ == null) {
        reqs_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        reqsBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_IarData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.IarData getDefaultInstanceForType() {
      return road.data.proto.IarData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.IarData build() {
      road.data.proto.IarData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.IarData buildPartial() {
      road.data.proto.IarData result = new road.data.proto.IarData(this);
      int from_bitField0_ = bitField0_;
      if (currentPosBuilder_ == null) {
        result.currentPos_ = currentPos_;
      } else {
        result.currentPos_ = currentPosBuilder_.build();
      }
      if (pathPlanningBuilder_ == null) {
        result.pathPlanning_ = pathPlanning_;
      } else {
        result.pathPlanning_ = pathPlanningBuilder_.build();
      }
      if (currentBehaviorBuilder_ == null) {
        result.currentBehavior_ = currentBehavior_;
      } else {
        result.currentBehavior_ = currentBehaviorBuilder_.build();
      }
      if (reqsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          reqs_ = java.util.Collections.unmodifiableList(reqs_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.reqs_ = reqs_;
      } else {
        result.reqs_ = reqsBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.IarData) {
        return mergeFrom((road.data.proto.IarData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.IarData other) {
      if (other == road.data.proto.IarData.getDefaultInstance()) return this;
      if (other.hasCurrentPos()) {
        mergeCurrentPos(other.getCurrentPos());
      }
      if (other.hasPathPlanning()) {
        mergePathPlanning(other.getPathPlanning());
      }
      if (other.hasCurrentBehavior()) {
        mergeCurrentBehavior(other.getCurrentBehavior());
      }
      if (reqsBuilder_ == null) {
        if (!other.reqs_.isEmpty()) {
          if (reqs_.isEmpty()) {
            reqs_ = other.reqs_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureReqsIsMutable();
            reqs_.addAll(other.reqs_);
          }
          onChanged();
        }
      } else {
        if (!other.reqs_.isEmpty()) {
          if (reqsBuilder_.isEmpty()) {
            reqsBuilder_.dispose();
            reqsBuilder_ = null;
            reqs_ = other.reqs_;
            bitField0_ = (bitField0_ & ~0x00000001);
            reqsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getReqsFieldBuilder() : null;
          } else {
            reqsBuilder_.addAllMessages(other.reqs_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.IarData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.IarData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private road.data.proto.PathPlanningPoint currentPos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PathPlanningPoint, road.data.proto.PathPlanningPoint.Builder, road.data.proto.PathPlanningPointOrBuilder> currentPosBuilder_;
    /**
     * <pre>
     *可选，地图中的当前位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
     */
    public boolean hasCurrentPos() {
      return currentPosBuilder_ != null || currentPos_ != null;
    }
    /**
     * <pre>
     *可选，地图中的当前位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
     */
    public road.data.proto.PathPlanningPoint getCurrentPos() {
      if (currentPosBuilder_ == null) {
        return currentPos_ == null ? road.data.proto.PathPlanningPoint.getDefaultInstance() : currentPos_;
      } else {
        return currentPosBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，地图中的当前位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
     */
    public Builder setCurrentPos(road.data.proto.PathPlanningPoint value) {
      if (currentPosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        currentPos_ = value;
        onChanged();
      } else {
        currentPosBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，地图中的当前位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
     */
    public Builder setCurrentPos(
        road.data.proto.PathPlanningPoint.Builder builderForValue) {
      if (currentPosBuilder_ == null) {
        currentPos_ = builderForValue.build();
        onChanged();
      } else {
        currentPosBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，地图中的当前位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
     */
    public Builder mergeCurrentPos(road.data.proto.PathPlanningPoint value) {
      if (currentPosBuilder_ == null) {
        if (currentPos_ != null) {
          currentPos_ =
            road.data.proto.PathPlanningPoint.newBuilder(currentPos_).mergeFrom(value).buildPartial();
        } else {
          currentPos_ = value;
        }
        onChanged();
      } else {
        currentPosBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，地图中的当前位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
     */
    public Builder clearCurrentPos() {
      if (currentPosBuilder_ == null) {
        currentPos_ = null;
        onChanged();
      } else {
        currentPos_ = null;
        currentPosBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，地图中的当前位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
     */
    public road.data.proto.PathPlanningPoint.Builder getCurrentPosBuilder() {
      
      onChanged();
      return getCurrentPosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，地图中的当前位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
     */
    public road.data.proto.PathPlanningPointOrBuilder getCurrentPosOrBuilder() {
      if (currentPosBuilder_ != null) {
        return currentPosBuilder_.getMessageOrBuilder();
      } else {
        return currentPos_ == null ?
            road.data.proto.PathPlanningPoint.getDefaultInstance() : currentPos_;
      }
    }
    /**
     * <pre>
     *可选，地图中的当前位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PathPlanningPoint, road.data.proto.PathPlanningPoint.Builder, road.data.proto.PathPlanningPointOrBuilder> 
        getCurrentPosFieldBuilder() {
      if (currentPosBuilder_ == null) {
        currentPosBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.PathPlanningPoint, road.data.proto.PathPlanningPoint.Builder, road.data.proto.PathPlanningPointOrBuilder>(
                getCurrentPos(),
                getParentForChildren(),
                isClean());
        currentPos_ = null;
      }
      return currentPosBuilder_;
    }

    private road.data.proto.PathPlanning pathPlanning_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PathPlanning, road.data.proto.PathPlanning.Builder, road.data.proto.PathPlanningOrBuilder> pathPlanningBuilder_;
    /**
     * <pre>
     *可选，共享的实时路径规划,按时间顺序列出
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
     */
    public boolean hasPathPlanning() {
      return pathPlanningBuilder_ != null || pathPlanning_ != null;
    }
    /**
     * <pre>
     *可选，共享的实时路径规划,按时间顺序列出
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
     */
    public road.data.proto.PathPlanning getPathPlanning() {
      if (pathPlanningBuilder_ == null) {
        return pathPlanning_ == null ? road.data.proto.PathPlanning.getDefaultInstance() : pathPlanning_;
      } else {
        return pathPlanningBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，共享的实时路径规划,按时间顺序列出
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
     */
    public Builder setPathPlanning(road.data.proto.PathPlanning value) {
      if (pathPlanningBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        pathPlanning_ = value;
        onChanged();
      } else {
        pathPlanningBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，共享的实时路径规划,按时间顺序列出
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
     */
    public Builder setPathPlanning(
        road.data.proto.PathPlanning.Builder builderForValue) {
      if (pathPlanningBuilder_ == null) {
        pathPlanning_ = builderForValue.build();
        onChanged();
      } else {
        pathPlanningBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，共享的实时路径规划,按时间顺序列出
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
     */
    public Builder mergePathPlanning(road.data.proto.PathPlanning value) {
      if (pathPlanningBuilder_ == null) {
        if (pathPlanning_ != null) {
          pathPlanning_ =
            road.data.proto.PathPlanning.newBuilder(pathPlanning_).mergeFrom(value).buildPartial();
        } else {
          pathPlanning_ = value;
        }
        onChanged();
      } else {
        pathPlanningBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，共享的实时路径规划,按时间顺序列出
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
     */
    public Builder clearPathPlanning() {
      if (pathPlanningBuilder_ == null) {
        pathPlanning_ = null;
        onChanged();
      } else {
        pathPlanning_ = null;
        pathPlanningBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，共享的实时路径规划,按时间顺序列出
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
     */
    public road.data.proto.PathPlanning.Builder getPathPlanningBuilder() {
      
      onChanged();
      return getPathPlanningFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，共享的实时路径规划,按时间顺序列出
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
     */
    public road.data.proto.PathPlanningOrBuilder getPathPlanningOrBuilder() {
      if (pathPlanningBuilder_ != null) {
        return pathPlanningBuilder_.getMessageOrBuilder();
      } else {
        return pathPlanning_ == null ?
            road.data.proto.PathPlanning.getDefaultInstance() : pathPlanning_;
      }
    }
    /**
     * <pre>
     *可选，共享的实时路径规划,按时间顺序列出
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PathPlanning, road.data.proto.PathPlanning.Builder, road.data.proto.PathPlanningOrBuilder> 
        getPathPlanningFieldBuilder() {
      if (pathPlanningBuilder_ == null) {
        pathPlanningBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.PathPlanning, road.data.proto.PathPlanning.Builder, road.data.proto.PathPlanningOrBuilder>(
                getPathPlanning(),
                getParentForChildren(),
                isClean());
        pathPlanning_ = null;
      }
      return pathPlanningBuilder_;
    }

    private road.data.proto.DriveBehavior currentBehavior_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.DriveBehavior, road.data.proto.DriveBehavior.Builder, road.data.proto.DriveBehaviorOrBuilder> currentBehaviorBuilder_;
    /**
     * <pre>
     *可选，与路径规划相关的驱动行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
     */
    public boolean hasCurrentBehavior() {
      return currentBehaviorBuilder_ != null || currentBehavior_ != null;
    }
    /**
     * <pre>
     *可选，与路径规划相关的驱动行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
     */
    public road.data.proto.DriveBehavior getCurrentBehavior() {
      if (currentBehaviorBuilder_ == null) {
        return currentBehavior_ == null ? road.data.proto.DriveBehavior.getDefaultInstance() : currentBehavior_;
      } else {
        return currentBehaviorBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，与路径规划相关的驱动行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
     */
    public Builder setCurrentBehavior(road.data.proto.DriveBehavior value) {
      if (currentBehaviorBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        currentBehavior_ = value;
        onChanged();
      } else {
        currentBehaviorBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，与路径规划相关的驱动行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
     */
    public Builder setCurrentBehavior(
        road.data.proto.DriveBehavior.Builder builderForValue) {
      if (currentBehaviorBuilder_ == null) {
        currentBehavior_ = builderForValue.build();
        onChanged();
      } else {
        currentBehaviorBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，与路径规划相关的驱动行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
     */
    public Builder mergeCurrentBehavior(road.data.proto.DriveBehavior value) {
      if (currentBehaviorBuilder_ == null) {
        if (currentBehavior_ != null) {
          currentBehavior_ =
            road.data.proto.DriveBehavior.newBuilder(currentBehavior_).mergeFrom(value).buildPartial();
        } else {
          currentBehavior_ = value;
        }
        onChanged();
      } else {
        currentBehaviorBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，与路径规划相关的驱动行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
     */
    public Builder clearCurrentBehavior() {
      if (currentBehaviorBuilder_ == null) {
        currentBehavior_ = null;
        onChanged();
      } else {
        currentBehavior_ = null;
        currentBehaviorBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，与路径规划相关的驱动行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
     */
    public road.data.proto.DriveBehavior.Builder getCurrentBehaviorBuilder() {
      
      onChanged();
      return getCurrentBehaviorFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，与路径规划相关的驱动行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
     */
    public road.data.proto.DriveBehaviorOrBuilder getCurrentBehaviorOrBuilder() {
      if (currentBehaviorBuilder_ != null) {
        return currentBehaviorBuilder_.getMessageOrBuilder();
      } else {
        return currentBehavior_ == null ?
            road.data.proto.DriveBehavior.getDefaultInstance() : currentBehavior_;
      }
    }
    /**
     * <pre>
     *可选，与路径规划相关的驱动行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.DriveBehavior, road.data.proto.DriveBehavior.Builder, road.data.proto.DriveBehaviorOrBuilder> 
        getCurrentBehaviorFieldBuilder() {
      if (currentBehaviorBuilder_ == null) {
        currentBehaviorBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.DriveBehavior, road.data.proto.DriveBehavior.Builder, road.data.proto.DriveBehaviorOrBuilder>(
                getCurrentBehavior(),
                getParentForChildren(),
                isClean());
        currentBehavior_ = null;
      }
      return currentBehaviorBuilder_;
    }

    private java.util.List<road.data.proto.DriveRequest> reqs_ =
      java.util.Collections.emptyList();
    private void ensureReqsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        reqs_ = new java.util.ArrayList<road.data.proto.DriveRequest>(reqs_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.DriveRequest, road.data.proto.DriveRequest.Builder, road.data.proto.DriveRequestOrBuilder> reqsBuilder_;

    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public java.util.List<road.data.proto.DriveRequest> getReqsList() {
      if (reqsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(reqs_);
      } else {
        return reqsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public int getReqsCount() {
      if (reqsBuilder_ == null) {
        return reqs_.size();
      } else {
        return reqsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public road.data.proto.DriveRequest getReqs(int index) {
      if (reqsBuilder_ == null) {
        return reqs_.get(index);
      } else {
        return reqsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public Builder setReqs(
        int index, road.data.proto.DriveRequest value) {
      if (reqsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureReqsIsMutable();
        reqs_.set(index, value);
        onChanged();
      } else {
        reqsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public Builder setReqs(
        int index, road.data.proto.DriveRequest.Builder builderForValue) {
      if (reqsBuilder_ == null) {
        ensureReqsIsMutable();
        reqs_.set(index, builderForValue.build());
        onChanged();
      } else {
        reqsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public Builder addReqs(road.data.proto.DriveRequest value) {
      if (reqsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureReqsIsMutable();
        reqs_.add(value);
        onChanged();
      } else {
        reqsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public Builder addReqs(
        int index, road.data.proto.DriveRequest value) {
      if (reqsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureReqsIsMutable();
        reqs_.add(index, value);
        onChanged();
      } else {
        reqsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public Builder addReqs(
        road.data.proto.DriveRequest.Builder builderForValue) {
      if (reqsBuilder_ == null) {
        ensureReqsIsMutable();
        reqs_.add(builderForValue.build());
        onChanged();
      } else {
        reqsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public Builder addReqs(
        int index, road.data.proto.DriveRequest.Builder builderForValue) {
      if (reqsBuilder_ == null) {
        ensureReqsIsMutable();
        reqs_.add(index, builderForValue.build());
        onChanged();
      } else {
        reqsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public Builder addAllReqs(
        java.lang.Iterable<? extends road.data.proto.DriveRequest> values) {
      if (reqsBuilder_ == null) {
        ensureReqsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, reqs_);
        onChanged();
      } else {
        reqsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public Builder clearReqs() {
      if (reqsBuilder_ == null) {
        reqs_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        reqsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public Builder removeReqs(int index) {
      if (reqsBuilder_ == null) {
        ensureReqsIsMutable();
        reqs_.remove(index);
        onChanged();
      } else {
        reqsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public road.data.proto.DriveRequest.Builder getReqsBuilder(
        int index) {
      return getReqsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public road.data.proto.DriveRequestOrBuilder getReqsOrBuilder(
        int index) {
      if (reqsBuilder_ == null) {
        return reqs_.get(index);  } else {
        return reqsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public java.util.List<? extends road.data.proto.DriveRequestOrBuilder> 
         getReqsOrBuilderList() {
      if (reqsBuilder_ != null) {
        return reqsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(reqs_);
      }
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public road.data.proto.DriveRequest.Builder addReqsBuilder() {
      return getReqsFieldBuilder().addBuilder(
          road.data.proto.DriveRequest.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public road.data.proto.DriveRequest.Builder addReqsBuilder(
        int index) {
      return getReqsFieldBuilder().addBuilder(
          index, road.data.proto.DriveRequest.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，请求序列
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
     */
    public java.util.List<road.data.proto.DriveRequest.Builder> 
         getReqsBuilderList() {
      return getReqsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.DriveRequest, road.data.proto.DriveRequest.Builder, road.data.proto.DriveRequestOrBuilder> 
        getReqsFieldBuilder() {
      if (reqsBuilder_ == null) {
        reqsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.DriveRequest, road.data.proto.DriveRequest.Builder, road.data.proto.DriveRequestOrBuilder>(
                reqs_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        reqs_ = null;
      }
      return reqsBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.IarData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.IarData)
  private static final road.data.proto.IarData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.IarData();
  }

  public static road.data.proto.IarData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<IarData>
      PARSER = new com.google.protobuf.AbstractParser<IarData>() {
    @java.lang.Override
    public IarData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new IarData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<IarData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<IarData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.IarData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

