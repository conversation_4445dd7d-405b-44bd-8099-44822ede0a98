// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface IarDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.IarData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *可选，地图中的当前位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
   */
  boolean hasCurrentPos();
  /**
   * <pre>
   *可选，地图中的当前位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
   */
  road.data.proto.PathPlanningPoint getCurrentPos();
  /**
   * <pre>
   *可选，地图中的当前位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanningPoint currentPos = 1;</code>
   */
  road.data.proto.PathPlanningPointOrBuilder getCurrentPosOrBuilder();

  /**
   * <pre>
   *可选，共享的实时路径规划,按时间顺序列出
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
   */
  boolean hasPathPlanning();
  /**
   * <pre>
   *可选，共享的实时路径规划,按时间顺序列出
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
   */
  road.data.proto.PathPlanning getPathPlanning();
  /**
   * <pre>
   *可选，共享的实时路径规划,按时间顺序列出
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanning pathPlanning = 2;</code>
   */
  road.data.proto.PathPlanningOrBuilder getPathPlanningOrBuilder();

  /**
   * <pre>
   *可选，与路径规划相关的驱动行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
   */
  boolean hasCurrentBehavior();
  /**
   * <pre>
   *可选，与路径规划相关的驱动行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
   */
  road.data.proto.DriveBehavior getCurrentBehavior();
  /**
   * <pre>
   *可选，与路径规划相关的驱动行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior currentBehavior = 3;</code>
   */
  road.data.proto.DriveBehaviorOrBuilder getCurrentBehaviorOrBuilder();

  /**
   * <pre>
   *可选，请求序列
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
   */
  java.util.List<road.data.proto.DriveRequest> 
      getReqsList();
  /**
   * <pre>
   *可选，请求序列
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
   */
  road.data.proto.DriveRequest getReqs(int index);
  /**
   * <pre>
   *可选，请求序列
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
   */
  int getReqsCount();
  /**
   * <pre>
   *可选，请求序列
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
   */
  java.util.List<? extends road.data.proto.DriveRequestOrBuilder> 
      getReqsOrBuilderList();
  /**
   * <pre>
   *可选，请求序列
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.DriveRequest reqs = 4;</code>
   */
  road.data.proto.DriveRequestOrBuilder getReqsOrBuilder(
      int index);
}
