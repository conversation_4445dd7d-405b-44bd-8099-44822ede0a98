// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *路口状态IntersectionState    
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.IntersectionState}
 */
public  final class IntersectionState extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.IntersectionState)
    IntersectionStateOrBuilder {
private static final long serialVersionUID = 0L;
  // Use IntersectionState.newBuilder() to construct.
  private IntersectionState(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private IntersectionState() {
    status_ = "";
    timeConfidence_ = 0;
    phases_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new IntersectionState();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private IntersectionState(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (intersectionId_ != null) {
              subBuilder = intersectionId_.toBuilder();
            }
            intersectionId_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(intersectionId_);
              intersectionId_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            status_ = s;
            break;
          }
          case 24: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 32: {
            int rawValue = input.readEnum();

            timeConfidence_ = rawValue;
            break;
          }
          case 42: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              phases_ = new java.util.ArrayList<road.data.proto.Phase>();
              mutable_bitField0_ |= 0x00000001;
            }
            phases_.add(
                input.readMessage(road.data.proto.Phase.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        phases_ = java.util.Collections.unmodifiableList(phases_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_IntersectionState_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_IntersectionState_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.IntersectionState.class, road.data.proto.IntersectionState.Builder.class);
  }

  public static final int INTERSECTIONID_FIELD_NUMBER = 1;
  private road.data.proto.NodeReferenceId intersectionId_;
  /**
   * <pre>
   * 路口ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
   */
  public boolean hasIntersectionId() {
    return intersectionId_ != null;
  }
  /**
   * <pre>
   * 路口ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
   */
  public road.data.proto.NodeReferenceId getIntersectionId() {
    return intersectionId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : intersectionId_;
  }
  /**
   * <pre>
   * 路口ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getIntersectionIdOrBuilder() {
    return getIntersectionId();
  }

  public static final int STATUS_FIELD_NUMBER = 2;
  private volatile java.lang.Object status_;
  /**
   * <pre>
   * 可选，表示信号灯当前的控制模式状态，需根据信号控制系统实际的工作状态设置内部数值。
   * </pre>
   *
   * <code>string status = 2;</code>
   */
  public java.lang.String getStatus() {
    java.lang.Object ref = status_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      status_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 可选，表示信号灯当前的控制模式状态，需根据信号控制系统实际的工作状态设置内部数值。
   * </pre>
   *
   * <code>string status = 2;</code>
   */
  public com.google.protobuf.ByteString
      getStatusBytes() {
    java.lang.Object ref = status_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      status_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 3;
  private long timestamp_;
  /**
   * <pre>
   *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 3;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int TIMECONFIDENCE_FIELD_NUMBER = 4;
  private int timeConfidence_;
  /**
   * <pre>
   *可选，参考TimeConfidence
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 4;</code>
   */
  public int getTimeConfidenceValue() {
    return timeConfidence_;
  }
  /**
   * <pre>
   *可选，参考TimeConfidence
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 4;</code>
   */
  public road.data.proto.TimeConfidence getTimeConfidence() {
    @SuppressWarnings("deprecation")
    road.data.proto.TimeConfidence result = road.data.proto.TimeConfidence.valueOf(timeConfidence_);
    return result == null ? road.data.proto.TimeConfidence.UNRECOGNIZED : result;
  }

  public static final int PHASES_FIELD_NUMBER = 5;
  private java.util.List<road.data.proto.Phase> phases_;
  /**
   * <pre>
   *多个相位
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
   */
  public java.util.List<road.data.proto.Phase> getPhasesList() {
    return phases_;
  }
  /**
   * <pre>
   *多个相位
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
   */
  public java.util.List<? extends road.data.proto.PhaseOrBuilder> 
      getPhasesOrBuilderList() {
    return phases_;
  }
  /**
   * <pre>
   *多个相位
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
   */
  public int getPhasesCount() {
    return phases_.size();
  }
  /**
   * <pre>
   *多个相位
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
   */
  public road.data.proto.Phase getPhases(int index) {
    return phases_.get(index);
  }
  /**
   * <pre>
   *多个相位
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
   */
  public road.data.proto.PhaseOrBuilder getPhasesOrBuilder(
      int index) {
    return phases_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (intersectionId_ != null) {
      output.writeMessage(1, getIntersectionId());
    }
    if (!getStatusBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, status_);
    }
    if (timestamp_ != 0L) {
      output.writeUInt64(3, timestamp_);
    }
    if (timeConfidence_ != road.data.proto.TimeConfidence.UNAVAILABLE.getNumber()) {
      output.writeEnum(4, timeConfidence_);
    }
    for (int i = 0; i < phases_.size(); i++) {
      output.writeMessage(5, phases_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (intersectionId_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getIntersectionId());
    }
    if (!getStatusBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, status_);
    }
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, timestamp_);
    }
    if (timeConfidence_ != road.data.proto.TimeConfidence.UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(4, timeConfidence_);
    }
    for (int i = 0; i < phases_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, phases_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.IntersectionState)) {
      return super.equals(obj);
    }
    road.data.proto.IntersectionState other = (road.data.proto.IntersectionState) obj;

    if (hasIntersectionId() != other.hasIntersectionId()) return false;
    if (hasIntersectionId()) {
      if (!getIntersectionId()
          .equals(other.getIntersectionId())) return false;
    }
    if (!getStatus()
        .equals(other.getStatus())) return false;
    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (timeConfidence_ != other.timeConfidence_) return false;
    if (!getPhasesList()
        .equals(other.getPhasesList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasIntersectionId()) {
      hash = (37 * hash) + INTERSECTIONID_FIELD_NUMBER;
      hash = (53 * hash) + getIntersectionId().hashCode();
    }
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + getStatus().hashCode();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    hash = (37 * hash) + TIMECONFIDENCE_FIELD_NUMBER;
    hash = (53 * hash) + timeConfidence_;
    if (getPhasesCount() > 0) {
      hash = (37 * hash) + PHASES_FIELD_NUMBER;
      hash = (53 * hash) + getPhasesList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.IntersectionState parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.IntersectionState parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.IntersectionState parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.IntersectionState parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.IntersectionState parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.IntersectionState parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.IntersectionState parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.IntersectionState parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.IntersectionState parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.IntersectionState parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.IntersectionState parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.IntersectionState parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.IntersectionState prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *路口状态IntersectionState    
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.IntersectionState}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.IntersectionState)
      road.data.proto.IntersectionStateOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_IntersectionState_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_IntersectionState_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.IntersectionState.class, road.data.proto.IntersectionState.Builder.class);
    }

    // Construct using road.data.proto.IntersectionState.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getPhasesFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (intersectionIdBuilder_ == null) {
        intersectionId_ = null;
      } else {
        intersectionId_ = null;
        intersectionIdBuilder_ = null;
      }
      status_ = "";

      timestamp_ = 0L;

      timeConfidence_ = 0;

      if (phasesBuilder_ == null) {
        phases_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        phasesBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_IntersectionState_descriptor;
    }

    @java.lang.Override
    public road.data.proto.IntersectionState getDefaultInstanceForType() {
      return road.data.proto.IntersectionState.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.IntersectionState build() {
      road.data.proto.IntersectionState result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.IntersectionState buildPartial() {
      road.data.proto.IntersectionState result = new road.data.proto.IntersectionState(this);
      int from_bitField0_ = bitField0_;
      if (intersectionIdBuilder_ == null) {
        result.intersectionId_ = intersectionId_;
      } else {
        result.intersectionId_ = intersectionIdBuilder_.build();
      }
      result.status_ = status_;
      result.timestamp_ = timestamp_;
      result.timeConfidence_ = timeConfidence_;
      if (phasesBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          phases_ = java.util.Collections.unmodifiableList(phases_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.phases_ = phases_;
      } else {
        result.phases_ = phasesBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.IntersectionState) {
        return mergeFrom((road.data.proto.IntersectionState)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.IntersectionState other) {
      if (other == road.data.proto.IntersectionState.getDefaultInstance()) return this;
      if (other.hasIntersectionId()) {
        mergeIntersectionId(other.getIntersectionId());
      }
      if (!other.getStatus().isEmpty()) {
        status_ = other.status_;
        onChanged();
      }
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (other.timeConfidence_ != 0) {
        setTimeConfidenceValue(other.getTimeConfidenceValue());
      }
      if (phasesBuilder_ == null) {
        if (!other.phases_.isEmpty()) {
          if (phases_.isEmpty()) {
            phases_ = other.phases_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePhasesIsMutable();
            phases_.addAll(other.phases_);
          }
          onChanged();
        }
      } else {
        if (!other.phases_.isEmpty()) {
          if (phasesBuilder_.isEmpty()) {
            phasesBuilder_.dispose();
            phasesBuilder_ = null;
            phases_ = other.phases_;
            bitField0_ = (bitField0_ & ~0x00000001);
            phasesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getPhasesFieldBuilder() : null;
          } else {
            phasesBuilder_.addAllMessages(other.phases_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.IntersectionState parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.IntersectionState) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private road.data.proto.NodeReferenceId intersectionId_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> intersectionIdBuilder_;
    /**
     * <pre>
     * 路口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public boolean hasIntersectionId() {
      return intersectionIdBuilder_ != null || intersectionId_ != null;
    }
    /**
     * <pre>
     * 路口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public road.data.proto.NodeReferenceId getIntersectionId() {
      if (intersectionIdBuilder_ == null) {
        return intersectionId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : intersectionId_;
      } else {
        return intersectionIdBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 路口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public Builder setIntersectionId(road.data.proto.NodeReferenceId value) {
      if (intersectionIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        intersectionId_ = value;
        onChanged();
      } else {
        intersectionIdBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 路口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public Builder setIntersectionId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (intersectionIdBuilder_ == null) {
        intersectionId_ = builderForValue.build();
        onChanged();
      } else {
        intersectionIdBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 路口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public Builder mergeIntersectionId(road.data.proto.NodeReferenceId value) {
      if (intersectionIdBuilder_ == null) {
        if (intersectionId_ != null) {
          intersectionId_ =
            road.data.proto.NodeReferenceId.newBuilder(intersectionId_).mergeFrom(value).buildPartial();
        } else {
          intersectionId_ = value;
        }
        onChanged();
      } else {
        intersectionIdBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 路口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public Builder clearIntersectionId() {
      if (intersectionIdBuilder_ == null) {
        intersectionId_ = null;
        onChanged();
      } else {
        intersectionId_ = null;
        intersectionIdBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 路口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getIntersectionIdBuilder() {
      
      onChanged();
      return getIntersectionIdFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 路口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getIntersectionIdOrBuilder() {
      if (intersectionIdBuilder_ != null) {
        return intersectionIdBuilder_.getMessageOrBuilder();
      } else {
        return intersectionId_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : intersectionId_;
      }
    }
    /**
     * <pre>
     * 路口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getIntersectionIdFieldBuilder() {
      if (intersectionIdBuilder_ == null) {
        intersectionIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getIntersectionId(),
                getParentForChildren(),
                isClean());
        intersectionId_ = null;
      }
      return intersectionIdBuilder_;
    }

    private java.lang.Object status_ = "";
    /**
     * <pre>
     * 可选，表示信号灯当前的控制模式状态，需根据信号控制系统实际的工作状态设置内部数值。
     * </pre>
     *
     * <code>string status = 2;</code>
     */
    public java.lang.String getStatus() {
      java.lang.Object ref = status_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        status_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 可选，表示信号灯当前的控制模式状态，需根据信号控制系统实际的工作状态设置内部数值。
     * </pre>
     *
     * <code>string status = 2;</code>
     */
    public com.google.protobuf.ByteString
        getStatusBytes() {
      java.lang.Object ref = status_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        status_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 可选，表示信号灯当前的控制模式状态，需根据信号控制系统实际的工作状态设置内部数值。
     * </pre>
     *
     * <code>string status = 2;</code>
     */
    public Builder setStatus(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      status_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，表示信号灯当前的控制模式状态，需根据信号控制系统实际的工作状态设置内部数值。
     * </pre>
     *
     * <code>string status = 2;</code>
     */
    public Builder clearStatus() {
      
      status_ = getDefaultInstance().getStatus();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，表示信号灯当前的控制模式状态，需根据信号控制系统实际的工作状态设置内部数值。
     * </pre>
     *
     * <code>string status = 2;</code>
     */
    public Builder setStatusBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      status_ = value;
      onChanged();
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private int timeConfidence_ = 0;
    /**
     * <pre>
     *可选，参考TimeConfidence
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 4;</code>
     */
    public int getTimeConfidenceValue() {
      return timeConfidence_;
    }
    /**
     * <pre>
     *可选，参考TimeConfidence
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 4;</code>
     */
    public Builder setTimeConfidenceValue(int value) {
      timeConfidence_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，参考TimeConfidence
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 4;</code>
     */
    public road.data.proto.TimeConfidence getTimeConfidence() {
      @SuppressWarnings("deprecation")
      road.data.proto.TimeConfidence result = road.data.proto.TimeConfidence.valueOf(timeConfidence_);
      return result == null ? road.data.proto.TimeConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，参考TimeConfidence
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 4;</code>
     */
    public Builder setTimeConfidence(road.data.proto.TimeConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      timeConfidence_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，参考TimeConfidence
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 4;</code>
     */
    public Builder clearTimeConfidence() {
      
      timeConfidence_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.Phase> phases_ =
      java.util.Collections.emptyList();
    private void ensurePhasesIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        phases_ = new java.util.ArrayList<road.data.proto.Phase>(phases_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Phase, road.data.proto.Phase.Builder, road.data.proto.PhaseOrBuilder> phasesBuilder_;

    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public java.util.List<road.data.proto.Phase> getPhasesList() {
      if (phasesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(phases_);
      } else {
        return phasesBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public int getPhasesCount() {
      if (phasesBuilder_ == null) {
        return phases_.size();
      } else {
        return phasesBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public road.data.proto.Phase getPhases(int index) {
      if (phasesBuilder_ == null) {
        return phases_.get(index);
      } else {
        return phasesBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public Builder setPhases(
        int index, road.data.proto.Phase value) {
      if (phasesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePhasesIsMutable();
        phases_.set(index, value);
        onChanged();
      } else {
        phasesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public Builder setPhases(
        int index, road.data.proto.Phase.Builder builderForValue) {
      if (phasesBuilder_ == null) {
        ensurePhasesIsMutable();
        phases_.set(index, builderForValue.build());
        onChanged();
      } else {
        phasesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public Builder addPhases(road.data.proto.Phase value) {
      if (phasesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePhasesIsMutable();
        phases_.add(value);
        onChanged();
      } else {
        phasesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public Builder addPhases(
        int index, road.data.proto.Phase value) {
      if (phasesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePhasesIsMutable();
        phases_.add(index, value);
        onChanged();
      } else {
        phasesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public Builder addPhases(
        road.data.proto.Phase.Builder builderForValue) {
      if (phasesBuilder_ == null) {
        ensurePhasesIsMutable();
        phases_.add(builderForValue.build());
        onChanged();
      } else {
        phasesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public Builder addPhases(
        int index, road.data.proto.Phase.Builder builderForValue) {
      if (phasesBuilder_ == null) {
        ensurePhasesIsMutable();
        phases_.add(index, builderForValue.build());
        onChanged();
      } else {
        phasesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public Builder addAllPhases(
        java.lang.Iterable<? extends road.data.proto.Phase> values) {
      if (phasesBuilder_ == null) {
        ensurePhasesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, phases_);
        onChanged();
      } else {
        phasesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public Builder clearPhases() {
      if (phasesBuilder_ == null) {
        phases_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        phasesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public Builder removePhases(int index) {
      if (phasesBuilder_ == null) {
        ensurePhasesIsMutable();
        phases_.remove(index);
        onChanged();
      } else {
        phasesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public road.data.proto.Phase.Builder getPhasesBuilder(
        int index) {
      return getPhasesFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public road.data.proto.PhaseOrBuilder getPhasesOrBuilder(
        int index) {
      if (phasesBuilder_ == null) {
        return phases_.get(index);  } else {
        return phasesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public java.util.List<? extends road.data.proto.PhaseOrBuilder> 
         getPhasesOrBuilderList() {
      if (phasesBuilder_ != null) {
        return phasesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(phases_);
      }
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public road.data.proto.Phase.Builder addPhasesBuilder() {
      return getPhasesFieldBuilder().addBuilder(
          road.data.proto.Phase.getDefaultInstance());
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public road.data.proto.Phase.Builder addPhasesBuilder(
        int index) {
      return getPhasesFieldBuilder().addBuilder(
          index, road.data.proto.Phase.getDefaultInstance());
    }
    /**
     * <pre>
     *多个相位
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
     */
    public java.util.List<road.data.proto.Phase.Builder> 
         getPhasesBuilderList() {
      return getPhasesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Phase, road.data.proto.Phase.Builder, road.data.proto.PhaseOrBuilder> 
        getPhasesFieldBuilder() {
      if (phasesBuilder_ == null) {
        phasesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.Phase, road.data.proto.Phase.Builder, road.data.proto.PhaseOrBuilder>(
                phases_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        phases_ = null;
      }
      return phasesBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.IntersectionState)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.IntersectionState)
  private static final road.data.proto.IntersectionState DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.IntersectionState();
  }

  public static road.data.proto.IntersectionState getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<IntersectionState>
      PARSER = new com.google.protobuf.AbstractParser<IntersectionState>() {
    @java.lang.Override
    public IntersectionState parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new IntersectionState(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<IntersectionState> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<IntersectionState> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.IntersectionState getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

