// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface IntersectionStateOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.IntersectionState)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 路口ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
   */
  boolean hasIntersectionId();
  /**
   * <pre>
   * 路口ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
   */
  road.data.proto.NodeReferenceId getIntersectionId();
  /**
   * <pre>
   * 路口ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getIntersectionIdOrBuilder();

  /**
   * <pre>
   * 可选，表示信号灯当前的控制模式状态，需根据信号控制系统实际的工作状态设置内部数值。
   * </pre>
   *
   * <code>string status = 2;</code>
   */
  java.lang.String getStatus();
  /**
   * <pre>
   * 可选，表示信号灯当前的控制模式状态，需根据信号控制系统实际的工作状态设置内部数值。
   * </pre>
   *
   * <code>string status = 2;</code>
   */
  com.google.protobuf.ByteString
      getStatusBytes();

  /**
   * <pre>
   *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 3;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   *可选，参考TimeConfidence
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 4;</code>
   */
  int getTimeConfidenceValue();
  /**
   * <pre>
   *可选，参考TimeConfidence
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 4;</code>
   */
  road.data.proto.TimeConfidence getTimeConfidence();

  /**
   * <pre>
   *多个相位
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
   */
  java.util.List<road.data.proto.Phase> 
      getPhasesList();
  /**
   * <pre>
   *多个相位
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
   */
  road.data.proto.Phase getPhases(int index);
  /**
   * <pre>
   *多个相位
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
   */
  int getPhasesCount();
  /**
   * <pre>
   *多个相位
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
   */
  java.util.List<? extends road.data.proto.PhaseOrBuilder> 
      getPhasesOrBuilderList();
  /**
   * <pre>
   *多个相位
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Phase phases = 5;</code>
   */
  road.data.proto.PhaseOrBuilder getPhasesOrBuilder(
      int index);
}
