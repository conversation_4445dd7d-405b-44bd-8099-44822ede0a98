// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车道属性LaneAttributes     
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.LaneAttributes}
 */
public  final class LaneAttributes extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.LaneAttributes)
    LaneAttributesOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LaneAttributes.newBuilder() to construct.
  private LaneAttributes(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LaneAttributes() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LaneAttributes();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private LaneAttributes(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.LaneSharing.Builder subBuilder = null;
            if (shareWith_ != null) {
              subBuilder = shareWith_.toBuilder();
            }
            shareWith_ = input.readMessage(road.data.proto.LaneSharing.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(shareWith_);
              shareWith_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            road.data.proto.LaneType.Builder subBuilder = null;
            if (laneType_ != null) {
              subBuilder = laneType_.toBuilder();
            }
            laneType_ = input.readMessage(road.data.proto.LaneType.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(laneType_);
              laneType_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneAttributes_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneAttributes_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.LaneAttributes.class, road.data.proto.LaneAttributes.Builder.class);
  }

  public static final int SHAREWITH_FIELD_NUMBER = 1;
  private road.data.proto.LaneSharing shareWith_;
  /**
   * <pre>
   *可选，车道共享情况 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
   */
  public boolean hasShareWith() {
    return shareWith_ != null;
  }
  /**
   * <pre>
   *可选，车道共享情况 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
   */
  public road.data.proto.LaneSharing getShareWith() {
    return shareWith_ == null ? road.data.proto.LaneSharing.getDefaultInstance() : shareWith_;
  }
  /**
   * <pre>
   *可选，车道共享情况 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
   */
  public road.data.proto.LaneSharingOrBuilder getShareWithOrBuilder() {
    return getShareWith();
  }

  public static final int LANETYPE_FIELD_NUMBER = 2;
  private road.data.proto.LaneType laneType_;
  /**
   * <pre>
   *不同类别车道的属性集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
   */
  public boolean hasLaneType() {
    return laneType_ != null;
  }
  /**
   * <pre>
   *不同类别车道的属性集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
   */
  public road.data.proto.LaneType getLaneType() {
    return laneType_ == null ? road.data.proto.LaneType.getDefaultInstance() : laneType_;
  }
  /**
   * <pre>
   *不同类别车道的属性集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
   */
  public road.data.proto.LaneTypeOrBuilder getLaneTypeOrBuilder() {
    return getLaneType();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (shareWith_ != null) {
      output.writeMessage(1, getShareWith());
    }
    if (laneType_ != null) {
      output.writeMessage(2, getLaneType());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (shareWith_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getShareWith());
    }
    if (laneType_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getLaneType());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.LaneAttributes)) {
      return super.equals(obj);
    }
    road.data.proto.LaneAttributes other = (road.data.proto.LaneAttributes) obj;

    if (hasShareWith() != other.hasShareWith()) return false;
    if (hasShareWith()) {
      if (!getShareWith()
          .equals(other.getShareWith())) return false;
    }
    if (hasLaneType() != other.hasLaneType()) return false;
    if (hasLaneType()) {
      if (!getLaneType()
          .equals(other.getLaneType())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasShareWith()) {
      hash = (37 * hash) + SHAREWITH_FIELD_NUMBER;
      hash = (53 * hash) + getShareWith().hashCode();
    }
    if (hasLaneType()) {
      hash = (37 * hash) + LANETYPE_FIELD_NUMBER;
      hash = (53 * hash) + getLaneType().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.LaneAttributes parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneAttributes parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneAttributes parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneAttributes parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneAttributes parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneAttributes parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneAttributes parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneAttributes parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneAttributes parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneAttributes parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneAttributes parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneAttributes parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.LaneAttributes prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *车道属性LaneAttributes     
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.LaneAttributes}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.LaneAttributes)
      road.data.proto.LaneAttributesOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneAttributes_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneAttributes_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.LaneAttributes.class, road.data.proto.LaneAttributes.Builder.class);
    }

    // Construct using road.data.proto.LaneAttributes.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (shareWithBuilder_ == null) {
        shareWith_ = null;
      } else {
        shareWith_ = null;
        shareWithBuilder_ = null;
      }
      if (laneTypeBuilder_ == null) {
        laneType_ = null;
      } else {
        laneType_ = null;
        laneTypeBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneAttributes_descriptor;
    }

    @java.lang.Override
    public road.data.proto.LaneAttributes getDefaultInstanceForType() {
      return road.data.proto.LaneAttributes.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.LaneAttributes build() {
      road.data.proto.LaneAttributes result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.LaneAttributes buildPartial() {
      road.data.proto.LaneAttributes result = new road.data.proto.LaneAttributes(this);
      if (shareWithBuilder_ == null) {
        result.shareWith_ = shareWith_;
      } else {
        result.shareWith_ = shareWithBuilder_.build();
      }
      if (laneTypeBuilder_ == null) {
        result.laneType_ = laneType_;
      } else {
        result.laneType_ = laneTypeBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.LaneAttributes) {
        return mergeFrom((road.data.proto.LaneAttributes)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.LaneAttributes other) {
      if (other == road.data.proto.LaneAttributes.getDefaultInstance()) return this;
      if (other.hasShareWith()) {
        mergeShareWith(other.getShareWith());
      }
      if (other.hasLaneType()) {
        mergeLaneType(other.getLaneType());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.LaneAttributes parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.LaneAttributes) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.LaneSharing shareWith_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneSharing, road.data.proto.LaneSharing.Builder, road.data.proto.LaneSharingOrBuilder> shareWithBuilder_;
    /**
     * <pre>
     *可选，车道共享情况 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
     */
    public boolean hasShareWith() {
      return shareWithBuilder_ != null || shareWith_ != null;
    }
    /**
     * <pre>
     *可选，车道共享情况 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
     */
    public road.data.proto.LaneSharing getShareWith() {
      if (shareWithBuilder_ == null) {
        return shareWith_ == null ? road.data.proto.LaneSharing.getDefaultInstance() : shareWith_;
      } else {
        return shareWithBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，车道共享情况 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
     */
    public Builder setShareWith(road.data.proto.LaneSharing value) {
      if (shareWithBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        shareWith_ = value;
        onChanged();
      } else {
        shareWithBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，车道共享情况 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
     */
    public Builder setShareWith(
        road.data.proto.LaneSharing.Builder builderForValue) {
      if (shareWithBuilder_ == null) {
        shareWith_ = builderForValue.build();
        onChanged();
      } else {
        shareWithBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，车道共享情况 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
     */
    public Builder mergeShareWith(road.data.proto.LaneSharing value) {
      if (shareWithBuilder_ == null) {
        if (shareWith_ != null) {
          shareWith_ =
            road.data.proto.LaneSharing.newBuilder(shareWith_).mergeFrom(value).buildPartial();
        } else {
          shareWith_ = value;
        }
        onChanged();
      } else {
        shareWithBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，车道共享情况 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
     */
    public Builder clearShareWith() {
      if (shareWithBuilder_ == null) {
        shareWith_ = null;
        onChanged();
      } else {
        shareWith_ = null;
        shareWithBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，车道共享情况 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
     */
    public road.data.proto.LaneSharing.Builder getShareWithBuilder() {
      
      onChanged();
      return getShareWithFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，车道共享情况 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
     */
    public road.data.proto.LaneSharingOrBuilder getShareWithOrBuilder() {
      if (shareWithBuilder_ != null) {
        return shareWithBuilder_.getMessageOrBuilder();
      } else {
        return shareWith_ == null ?
            road.data.proto.LaneSharing.getDefaultInstance() : shareWith_;
      }
    }
    /**
     * <pre>
     *可选，车道共享情况 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneSharing, road.data.proto.LaneSharing.Builder, road.data.proto.LaneSharingOrBuilder> 
        getShareWithFieldBuilder() {
      if (shareWithBuilder_ == null) {
        shareWithBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneSharing, road.data.proto.LaneSharing.Builder, road.data.proto.LaneSharingOrBuilder>(
                getShareWith(),
                getParentForChildren(),
                isClean());
        shareWith_ = null;
      }
      return shareWithBuilder_;
    }

    private road.data.proto.LaneType laneType_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneType, road.data.proto.LaneType.Builder, road.data.proto.LaneTypeOrBuilder> laneTypeBuilder_;
    /**
     * <pre>
     *不同类别车道的属性集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
     */
    public boolean hasLaneType() {
      return laneTypeBuilder_ != null || laneType_ != null;
    }
    /**
     * <pre>
     *不同类别车道的属性集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
     */
    public road.data.proto.LaneType getLaneType() {
      if (laneTypeBuilder_ == null) {
        return laneType_ == null ? road.data.proto.LaneType.getDefaultInstance() : laneType_;
      } else {
        return laneTypeBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *不同类别车道的属性集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
     */
    public Builder setLaneType(road.data.proto.LaneType value) {
      if (laneTypeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        laneType_ = value;
        onChanged();
      } else {
        laneTypeBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *不同类别车道的属性集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
     */
    public Builder setLaneType(
        road.data.proto.LaneType.Builder builderForValue) {
      if (laneTypeBuilder_ == null) {
        laneType_ = builderForValue.build();
        onChanged();
      } else {
        laneTypeBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *不同类别车道的属性集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
     */
    public Builder mergeLaneType(road.data.proto.LaneType value) {
      if (laneTypeBuilder_ == null) {
        if (laneType_ != null) {
          laneType_ =
            road.data.proto.LaneType.newBuilder(laneType_).mergeFrom(value).buildPartial();
        } else {
          laneType_ = value;
        }
        onChanged();
      } else {
        laneTypeBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *不同类别车道的属性集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
     */
    public Builder clearLaneType() {
      if (laneTypeBuilder_ == null) {
        laneType_ = null;
        onChanged();
      } else {
        laneType_ = null;
        laneTypeBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *不同类别车道的属性集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
     */
    public road.data.proto.LaneType.Builder getLaneTypeBuilder() {
      
      onChanged();
      return getLaneTypeFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *不同类别车道的属性集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
     */
    public road.data.proto.LaneTypeOrBuilder getLaneTypeOrBuilder() {
      if (laneTypeBuilder_ != null) {
        return laneTypeBuilder_.getMessageOrBuilder();
      } else {
        return laneType_ == null ?
            road.data.proto.LaneType.getDefaultInstance() : laneType_;
      }
    }
    /**
     * <pre>
     *不同类别车道的属性集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneType, road.data.proto.LaneType.Builder, road.data.proto.LaneTypeOrBuilder> 
        getLaneTypeFieldBuilder() {
      if (laneTypeBuilder_ == null) {
        laneTypeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneType, road.data.proto.LaneType.Builder, road.data.proto.LaneTypeOrBuilder>(
                getLaneType(),
                getParentForChildren(),
                isClean());
        laneType_ = null;
      }
      return laneTypeBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.LaneAttributes)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.LaneAttributes)
  private static final road.data.proto.LaneAttributes DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.LaneAttributes();
  }

  public static road.data.proto.LaneAttributes getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LaneAttributes>
      PARSER = new com.google.protobuf.AbstractParser<LaneAttributes>() {
    @java.lang.Override
    public LaneAttributes parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new LaneAttributes(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<LaneAttributes> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LaneAttributes> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.LaneAttributes getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

