// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneAttributesBarrierOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneAttributesBarrier)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *车道隔断离的属性定义（主要指示车道隔离断的物理形式）:
   * </pre>
   *
   * <code>uint32 mediansChannelization = 1;</code>
   */
  int getMediansChannelization();
}
