// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneAttributesBikeOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneAttributesBike)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *自行车道的属性
   * </pre>
   *
   * <code>uint32 bikeLanes = 1;</code>
   */
  int getBikeLanes();
}
