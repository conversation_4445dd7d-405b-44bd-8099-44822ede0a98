// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *人行横道属性 LaneAttributesCrosswalk 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.LaneAttributesCrosswalk}
 */
public  final class LaneAttributes<PERSON>rosswalk extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.LaneAttributesCrosswalk)
    LaneAttributesCrosswalkOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LaneAttributesCrosswalk.newBuilder() to construct.
  private LaneAttributesCrosswalk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LaneAttributesCrosswalk() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LaneAttributesCrosswalk();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private LaneAttributesCrosswalk(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            pedestrianCrosswalks_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneAttributesCrosswalk_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneAttributesCrosswalk_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.LaneAttributesCrosswalk.class, road.data.proto.LaneAttributesCrosswalk.Builder.class);
  }

  public static final int PEDESTRIANCROSSWALKS_FIELD_NUMBER = 1;
  private int pedestrianCrosswalks_;
  /**
   * <pre>
   *人行横道属性
   * </pre>
   *
   * <code>uint32 pedestrianCrosswalks = 1;</code>
   */
  public int getPedestrianCrosswalks() {
    return pedestrianCrosswalks_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (pedestrianCrosswalks_ != 0) {
      output.writeUInt32(1, pedestrianCrosswalks_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (pedestrianCrosswalks_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, pedestrianCrosswalks_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.LaneAttributesCrosswalk)) {
      return super.equals(obj);
    }
    road.data.proto.LaneAttributesCrosswalk other = (road.data.proto.LaneAttributesCrosswalk) obj;

    if (getPedestrianCrosswalks()
        != other.getPedestrianCrosswalks()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + PEDESTRIANCROSSWALKS_FIELD_NUMBER;
    hash = (53 * hash) + getPedestrianCrosswalks();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.LaneAttributesCrosswalk parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneAttributesCrosswalk parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneAttributesCrosswalk parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneAttributesCrosswalk parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneAttributesCrosswalk parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneAttributesCrosswalk parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneAttributesCrosswalk parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneAttributesCrosswalk parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneAttributesCrosswalk parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneAttributesCrosswalk parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneAttributesCrosswalk parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneAttributesCrosswalk parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.LaneAttributesCrosswalk prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *人行横道属性 LaneAttributesCrosswalk 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.LaneAttributesCrosswalk}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.LaneAttributesCrosswalk)
      road.data.proto.LaneAttributesCrosswalkOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneAttributesCrosswalk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneAttributesCrosswalk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.LaneAttributesCrosswalk.class, road.data.proto.LaneAttributesCrosswalk.Builder.class);
    }

    // Construct using road.data.proto.LaneAttributesCrosswalk.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      pedestrianCrosswalks_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneAttributesCrosswalk_descriptor;
    }

    @java.lang.Override
    public road.data.proto.LaneAttributesCrosswalk getDefaultInstanceForType() {
      return road.data.proto.LaneAttributesCrosswalk.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.LaneAttributesCrosswalk build() {
      road.data.proto.LaneAttributesCrosswalk result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.LaneAttributesCrosswalk buildPartial() {
      road.data.proto.LaneAttributesCrosswalk result = new road.data.proto.LaneAttributesCrosswalk(this);
      result.pedestrianCrosswalks_ = pedestrianCrosswalks_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.LaneAttributesCrosswalk) {
        return mergeFrom((road.data.proto.LaneAttributesCrosswalk)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.LaneAttributesCrosswalk other) {
      if (other == road.data.proto.LaneAttributesCrosswalk.getDefaultInstance()) return this;
      if (other.getPedestrianCrosswalks() != 0) {
        setPedestrianCrosswalks(other.getPedestrianCrosswalks());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.LaneAttributesCrosswalk parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.LaneAttributesCrosswalk) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int pedestrianCrosswalks_ ;
    /**
     * <pre>
     *人行横道属性
     * </pre>
     *
     * <code>uint32 pedestrianCrosswalks = 1;</code>
     */
    public int getPedestrianCrosswalks() {
      return pedestrianCrosswalks_;
    }
    /**
     * <pre>
     *人行横道属性
     * </pre>
     *
     * <code>uint32 pedestrianCrosswalks = 1;</code>
     */
    public Builder setPedestrianCrosswalks(int value) {
      
      pedestrianCrosswalks_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *人行横道属性
     * </pre>
     *
     * <code>uint32 pedestrianCrosswalks = 1;</code>
     */
    public Builder clearPedestrianCrosswalks() {
      
      pedestrianCrosswalks_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.LaneAttributesCrosswalk)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.LaneAttributesCrosswalk)
  private static final road.data.proto.LaneAttributesCrosswalk DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.LaneAttributesCrosswalk();
  }

  public static road.data.proto.LaneAttributesCrosswalk getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LaneAttributesCrosswalk>
      PARSER = new com.google.protobuf.AbstractParser<LaneAttributesCrosswalk>() {
    @java.lang.Override
    public LaneAttributesCrosswalk parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new LaneAttributesCrosswalk(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<LaneAttributesCrosswalk> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LaneAttributesCrosswalk> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.LaneAttributesCrosswalk getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

