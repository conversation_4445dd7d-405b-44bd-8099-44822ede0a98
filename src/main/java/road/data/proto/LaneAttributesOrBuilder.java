// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneAttributesOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneAttributes)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *可选，车道共享情况 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
   */
  boolean hasShareWith();
  /**
   * <pre>
   *可选，车道共享情况 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
   */
  road.data.proto.LaneSharing getShareWith();
  /**
   * <pre>
   *可选，车道共享情况 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneSharing shareWith = 1;</code>
   */
  road.data.proto.LaneSharingOrBuilder getShareWithOrBuilder();

  /**
   * <pre>
   *不同类别车道的属性集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
   */
  boolean hasLaneType();
  /**
   * <pre>
   *不同类别车道的属性集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
   */
  road.data.proto.LaneType getLaneType();
  /**
   * <pre>
   *不同类别车道的属性集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneType laneType = 2;</code>
   */
  road.data.proto.LaneTypeOrBuilder getLaneTypeOrBuilder();
}
