// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneAttributesParkingOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneAttributesParking)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *停车车道的属性：
   * </pre>
   *
   * <code>uint32 parkingAndStoppingLanes = 1;</code>
   */
  int getParkingAndStoppingLanes();
}
