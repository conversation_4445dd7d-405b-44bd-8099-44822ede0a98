// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneAttributesStripingOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneAttributesStriping)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *标线车道的属性定义，指示了车道上网纹或者标志标线所传达的道路信息，如禁行、路线标识等，辅助驾驶员通过一些复杂的路口或路段，提高驾驶安全性：
   * </pre>
   *
   * <code>uint32 roadwayMarkings = 1;</code>
   */
  int getRoadwayMarkings();
}
