// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneAttributesTrackedVehicleOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneAttributesTrackedVehicle)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *轨道车辆车道的属性定义，用来描述一条轨道车辆车道的特殊属性和其允许行驶的车辆种类：
   * </pre>
   *
   * <code>uint32 trainsAndTrolleys = 1;</code>
   */
  int getTrainsAndTrolleys();
}
