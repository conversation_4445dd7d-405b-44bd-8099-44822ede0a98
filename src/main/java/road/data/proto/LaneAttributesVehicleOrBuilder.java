// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneAttributesVehicleOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneAttributesVehicle)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *车辆行驶车道的属性定义，用来描述一条车用车道的特殊属性和其允许行驶的汽车种类：
   * </pre>
   *
   * <code>uint32 motorVehicleLanes = 1;</code>
   */
  int getMotorVehicleLanes();
}
