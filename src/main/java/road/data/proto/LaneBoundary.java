// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 * 车道边界类型LaneBoundary
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.LaneBoundary}
 */
public  final class LaneBoundary extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.LaneBoundary)
    LaneBoundaryOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LaneBoundary.newBuilder() to construct.
  private LaneBoundary(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LaneBoundary() {
    laneBoundaryPoints_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LaneBoundary();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private LaneBoundary(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            laneBoundaryType_ = input.readUInt32();
            break;
          }
          case 18: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              laneBoundaryPoints_ = new java.util.ArrayList<road.data.proto.Position3D>();
              mutable_bitField0_ |= 0x00000001;
            }
            laneBoundaryPoints_.add(
                input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        laneBoundaryPoints_ = java.util.Collections.unmodifiableList(laneBoundaryPoints_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneBoundary_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneBoundary_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.LaneBoundary.class, road.data.proto.LaneBoundary.Builder.class);
  }

  public static final int LANEBOUNDARYTYPE_FIELD_NUMBER = 1;
  private int laneBoundaryType_;
  /**
   * <pre>
   *车道边界类型：
   * </pre>
   *
   * <code>uint32 laneBoundaryType = 1;</code>
   */
  public int getLaneBoundaryType() {
    return laneBoundaryType_;
  }

  public static final int LANEBOUNDARYPOINTS_FIELD_NUMBER = 2;
  private java.util.List<road.data.proto.Position3D> laneBoundaryPoints_;
  /**
   * <pre>
   *1, BOUNDARY_WHITE_SOLID_LINES //白实线
   *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
   *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
   *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
   *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
   *6, BOUNDARY_KERB //路缘，道牙
   *7, BOUNDARY_LOW_FENCE //低栅栏
   *8, BOUNDARY_HIGH_FENCE //高栅栏
   *9, BOUNDARY_POST_FENCE //立柱栅栏
   *10, BOUNDARY_UNKNOWN //未知
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
   */
  public java.util.List<road.data.proto.Position3D> getLaneBoundaryPointsList() {
    return laneBoundaryPoints_;
  }
  /**
   * <pre>
   *1, BOUNDARY_WHITE_SOLID_LINES //白实线
   *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
   *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
   *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
   *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
   *6, BOUNDARY_KERB //路缘，道牙
   *7, BOUNDARY_LOW_FENCE //低栅栏
   *8, BOUNDARY_HIGH_FENCE //高栅栏
   *9, BOUNDARY_POST_FENCE //立柱栅栏
   *10, BOUNDARY_UNKNOWN //未知
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
   */
  public java.util.List<? extends road.data.proto.Position3DOrBuilder> 
      getLaneBoundaryPointsOrBuilderList() {
    return laneBoundaryPoints_;
  }
  /**
   * <pre>
   *1, BOUNDARY_WHITE_SOLID_LINES //白实线
   *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
   *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
   *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
   *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
   *6, BOUNDARY_KERB //路缘，道牙
   *7, BOUNDARY_LOW_FENCE //低栅栏
   *8, BOUNDARY_HIGH_FENCE //高栅栏
   *9, BOUNDARY_POST_FENCE //立柱栅栏
   *10, BOUNDARY_UNKNOWN //未知
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
   */
  public int getLaneBoundaryPointsCount() {
    return laneBoundaryPoints_.size();
  }
  /**
   * <pre>
   *1, BOUNDARY_WHITE_SOLID_LINES //白实线
   *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
   *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
   *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
   *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
   *6, BOUNDARY_KERB //路缘，道牙
   *7, BOUNDARY_LOW_FENCE //低栅栏
   *8, BOUNDARY_HIGH_FENCE //高栅栏
   *9, BOUNDARY_POST_FENCE //立柱栅栏
   *10, BOUNDARY_UNKNOWN //未知
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
   */
  public road.data.proto.Position3D getLaneBoundaryPoints(int index) {
    return laneBoundaryPoints_.get(index);
  }
  /**
   * <pre>
   *1, BOUNDARY_WHITE_SOLID_LINES //白实线
   *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
   *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
   *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
   *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
   *6, BOUNDARY_KERB //路缘，道牙
   *7, BOUNDARY_LOW_FENCE //低栅栏
   *8, BOUNDARY_HIGH_FENCE //高栅栏
   *9, BOUNDARY_POST_FENCE //立柱栅栏
   *10, BOUNDARY_UNKNOWN //未知
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
   */
  public road.data.proto.Position3DOrBuilder getLaneBoundaryPointsOrBuilder(
      int index) {
    return laneBoundaryPoints_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (laneBoundaryType_ != 0) {
      output.writeUInt32(1, laneBoundaryType_);
    }
    for (int i = 0; i < laneBoundaryPoints_.size(); i++) {
      output.writeMessage(2, laneBoundaryPoints_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (laneBoundaryType_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, laneBoundaryType_);
    }
    for (int i = 0; i < laneBoundaryPoints_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, laneBoundaryPoints_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.LaneBoundary)) {
      return super.equals(obj);
    }
    road.data.proto.LaneBoundary other = (road.data.proto.LaneBoundary) obj;

    if (getLaneBoundaryType()
        != other.getLaneBoundaryType()) return false;
    if (!getLaneBoundaryPointsList()
        .equals(other.getLaneBoundaryPointsList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + LANEBOUNDARYTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getLaneBoundaryType();
    if (getLaneBoundaryPointsCount() > 0) {
      hash = (37 * hash) + LANEBOUNDARYPOINTS_FIELD_NUMBER;
      hash = (53 * hash) + getLaneBoundaryPointsList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.LaneBoundary parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneBoundary parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneBoundary parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneBoundary parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneBoundary parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneBoundary parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneBoundary parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneBoundary parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneBoundary parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneBoundary parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneBoundary parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneBoundary parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.LaneBoundary prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 车道边界类型LaneBoundary
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.LaneBoundary}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.LaneBoundary)
      road.data.proto.LaneBoundaryOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneBoundary_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneBoundary_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.LaneBoundary.class, road.data.proto.LaneBoundary.Builder.class);
    }

    // Construct using road.data.proto.LaneBoundary.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getLaneBoundaryPointsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      laneBoundaryType_ = 0;

      if (laneBoundaryPointsBuilder_ == null) {
        laneBoundaryPoints_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        laneBoundaryPointsBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneBoundary_descriptor;
    }

    @java.lang.Override
    public road.data.proto.LaneBoundary getDefaultInstanceForType() {
      return road.data.proto.LaneBoundary.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.LaneBoundary build() {
      road.data.proto.LaneBoundary result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.LaneBoundary buildPartial() {
      road.data.proto.LaneBoundary result = new road.data.proto.LaneBoundary(this);
      int from_bitField0_ = bitField0_;
      result.laneBoundaryType_ = laneBoundaryType_;
      if (laneBoundaryPointsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          laneBoundaryPoints_ = java.util.Collections.unmodifiableList(laneBoundaryPoints_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.laneBoundaryPoints_ = laneBoundaryPoints_;
      } else {
        result.laneBoundaryPoints_ = laneBoundaryPointsBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.LaneBoundary) {
        return mergeFrom((road.data.proto.LaneBoundary)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.LaneBoundary other) {
      if (other == road.data.proto.LaneBoundary.getDefaultInstance()) return this;
      if (other.getLaneBoundaryType() != 0) {
        setLaneBoundaryType(other.getLaneBoundaryType());
      }
      if (laneBoundaryPointsBuilder_ == null) {
        if (!other.laneBoundaryPoints_.isEmpty()) {
          if (laneBoundaryPoints_.isEmpty()) {
            laneBoundaryPoints_ = other.laneBoundaryPoints_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureLaneBoundaryPointsIsMutable();
            laneBoundaryPoints_.addAll(other.laneBoundaryPoints_);
          }
          onChanged();
        }
      } else {
        if (!other.laneBoundaryPoints_.isEmpty()) {
          if (laneBoundaryPointsBuilder_.isEmpty()) {
            laneBoundaryPointsBuilder_.dispose();
            laneBoundaryPointsBuilder_ = null;
            laneBoundaryPoints_ = other.laneBoundaryPoints_;
            bitField0_ = (bitField0_ & ~0x00000001);
            laneBoundaryPointsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getLaneBoundaryPointsFieldBuilder() : null;
          } else {
            laneBoundaryPointsBuilder_.addAllMessages(other.laneBoundaryPoints_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.LaneBoundary parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.LaneBoundary) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int laneBoundaryType_ ;
    /**
     * <pre>
     *车道边界类型：
     * </pre>
     *
     * <code>uint32 laneBoundaryType = 1;</code>
     */
    public int getLaneBoundaryType() {
      return laneBoundaryType_;
    }
    /**
     * <pre>
     *车道边界类型：
     * </pre>
     *
     * <code>uint32 laneBoundaryType = 1;</code>
     */
    public Builder setLaneBoundaryType(int value) {
      
      laneBoundaryType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *车道边界类型：
     * </pre>
     *
     * <code>uint32 laneBoundaryType = 1;</code>
     */
    public Builder clearLaneBoundaryType() {
      
      laneBoundaryType_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.Position3D> laneBoundaryPoints_ =
      java.util.Collections.emptyList();
    private void ensureLaneBoundaryPointsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        laneBoundaryPoints_ = new java.util.ArrayList<road.data.proto.Position3D>(laneBoundaryPoints_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> laneBoundaryPointsBuilder_;

    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public java.util.List<road.data.proto.Position3D> getLaneBoundaryPointsList() {
      if (laneBoundaryPointsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(laneBoundaryPoints_);
      } else {
        return laneBoundaryPointsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public int getLaneBoundaryPointsCount() {
      if (laneBoundaryPointsBuilder_ == null) {
        return laneBoundaryPoints_.size();
      } else {
        return laneBoundaryPointsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public road.data.proto.Position3D getLaneBoundaryPoints(int index) {
      if (laneBoundaryPointsBuilder_ == null) {
        return laneBoundaryPoints_.get(index);
      } else {
        return laneBoundaryPointsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public Builder setLaneBoundaryPoints(
        int index, road.data.proto.Position3D value) {
      if (laneBoundaryPointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLaneBoundaryPointsIsMutable();
        laneBoundaryPoints_.set(index, value);
        onChanged();
      } else {
        laneBoundaryPointsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public Builder setLaneBoundaryPoints(
        int index, road.data.proto.Position3D.Builder builderForValue) {
      if (laneBoundaryPointsBuilder_ == null) {
        ensureLaneBoundaryPointsIsMutable();
        laneBoundaryPoints_.set(index, builderForValue.build());
        onChanged();
      } else {
        laneBoundaryPointsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public Builder addLaneBoundaryPoints(road.data.proto.Position3D value) {
      if (laneBoundaryPointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLaneBoundaryPointsIsMutable();
        laneBoundaryPoints_.add(value);
        onChanged();
      } else {
        laneBoundaryPointsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public Builder addLaneBoundaryPoints(
        int index, road.data.proto.Position3D value) {
      if (laneBoundaryPointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLaneBoundaryPointsIsMutable();
        laneBoundaryPoints_.add(index, value);
        onChanged();
      } else {
        laneBoundaryPointsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public Builder addLaneBoundaryPoints(
        road.data.proto.Position3D.Builder builderForValue) {
      if (laneBoundaryPointsBuilder_ == null) {
        ensureLaneBoundaryPointsIsMutable();
        laneBoundaryPoints_.add(builderForValue.build());
        onChanged();
      } else {
        laneBoundaryPointsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public Builder addLaneBoundaryPoints(
        int index, road.data.proto.Position3D.Builder builderForValue) {
      if (laneBoundaryPointsBuilder_ == null) {
        ensureLaneBoundaryPointsIsMutable();
        laneBoundaryPoints_.add(index, builderForValue.build());
        onChanged();
      } else {
        laneBoundaryPointsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public Builder addAllLaneBoundaryPoints(
        java.lang.Iterable<? extends road.data.proto.Position3D> values) {
      if (laneBoundaryPointsBuilder_ == null) {
        ensureLaneBoundaryPointsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, laneBoundaryPoints_);
        onChanged();
      } else {
        laneBoundaryPointsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public Builder clearLaneBoundaryPoints() {
      if (laneBoundaryPointsBuilder_ == null) {
        laneBoundaryPoints_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        laneBoundaryPointsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public Builder removeLaneBoundaryPoints(int index) {
      if (laneBoundaryPointsBuilder_ == null) {
        ensureLaneBoundaryPointsIsMutable();
        laneBoundaryPoints_.remove(index);
        onChanged();
      } else {
        laneBoundaryPointsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public road.data.proto.Position3D.Builder getLaneBoundaryPointsBuilder(
        int index) {
      return getLaneBoundaryPointsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public road.data.proto.Position3DOrBuilder getLaneBoundaryPointsOrBuilder(
        int index) {
      if (laneBoundaryPointsBuilder_ == null) {
        return laneBoundaryPoints_.get(index);  } else {
        return laneBoundaryPointsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public java.util.List<? extends road.data.proto.Position3DOrBuilder> 
         getLaneBoundaryPointsOrBuilderList() {
      if (laneBoundaryPointsBuilder_ != null) {
        return laneBoundaryPointsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(laneBoundaryPoints_);
      }
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public road.data.proto.Position3D.Builder addLaneBoundaryPointsBuilder() {
      return getLaneBoundaryPointsFieldBuilder().addBuilder(
          road.data.proto.Position3D.getDefaultInstance());
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public road.data.proto.Position3D.Builder addLaneBoundaryPointsBuilder(
        int index) {
      return getLaneBoundaryPointsFieldBuilder().addBuilder(
          index, road.data.proto.Position3D.getDefaultInstance());
    }
    /**
     * <pre>
     *1, BOUNDARY_WHITE_SOLID_LINES //白实线
     *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
     *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
     *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
     *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
     *6, BOUNDARY_KERB //路缘，道牙
     *7, BOUNDARY_LOW_FENCE //低栅栏
     *8, BOUNDARY_HIGH_FENCE //高栅栏
     *9, BOUNDARY_POST_FENCE //立柱栅栏
     *10, BOUNDARY_UNKNOWN //未知
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
     */
    public java.util.List<road.data.proto.Position3D.Builder> 
         getLaneBoundaryPointsBuilderList() {
      return getLaneBoundaryPointsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getLaneBoundaryPointsFieldBuilder() {
      if (laneBoundaryPointsBuilder_ == null) {
        laneBoundaryPointsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                laneBoundaryPoints_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        laneBoundaryPoints_ = null;
      }
      return laneBoundaryPointsBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.LaneBoundary)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.LaneBoundary)
  private static final road.data.proto.LaneBoundary DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.LaneBoundary();
  }

  public static road.data.proto.LaneBoundary getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LaneBoundary>
      PARSER = new com.google.protobuf.AbstractParser<LaneBoundary>() {
    @java.lang.Override
    public LaneBoundary parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new LaneBoundary(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<LaneBoundary> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LaneBoundary> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.LaneBoundary getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

