// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneBoundaryOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneBoundary)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *车道边界类型：
   * </pre>
   *
   * <code>uint32 laneBoundaryType = 1;</code>
   */
  int getLaneBoundaryType();

  /**
   * <pre>
   *1, BOUNDARY_WHITE_SOLID_LINES //白实线
   *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
   *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
   *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
   *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
   *6, BOUNDARY_KERB //路缘，道牙
   *7, BOUNDARY_LOW_FENCE //低栅栏
   *8, BOUNDARY_HIGH_FENCE //高栅栏
   *9, BOUNDARY_POST_FENCE //立柱栅栏
   *10, BOUNDARY_UNKNOWN //未知
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
   */
  java.util.List<road.data.proto.Position3D> 
      getLaneBoundaryPointsList();
  /**
   * <pre>
   *1, BOUNDARY_WHITE_SOLID_LINES //白实线
   *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
   *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
   *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
   *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
   *6, BOUNDARY_KERB //路缘，道牙
   *7, BOUNDARY_LOW_FENCE //低栅栏
   *8, BOUNDARY_HIGH_FENCE //高栅栏
   *9, BOUNDARY_POST_FENCE //立柱栅栏
   *10, BOUNDARY_UNKNOWN //未知
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
   */
  road.data.proto.Position3D getLaneBoundaryPoints(int index);
  /**
   * <pre>
   *1, BOUNDARY_WHITE_SOLID_LINES //白实线
   *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
   *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
   *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
   *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
   *6, BOUNDARY_KERB //路缘，道牙
   *7, BOUNDARY_LOW_FENCE //低栅栏
   *8, BOUNDARY_HIGH_FENCE //高栅栏
   *9, BOUNDARY_POST_FENCE //立柱栅栏
   *10, BOUNDARY_UNKNOWN //未知
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
   */
  int getLaneBoundaryPointsCount();
  /**
   * <pre>
   *1, BOUNDARY_WHITE_SOLID_LINES //白实线
   *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
   *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
   *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
   *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
   *6, BOUNDARY_KERB //路缘，道牙
   *7, BOUNDARY_LOW_FENCE //低栅栏
   *8, BOUNDARY_HIGH_FENCE //高栅栏
   *9, BOUNDARY_POST_FENCE //立柱栅栏
   *10, BOUNDARY_UNKNOWN //未知
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
   */
  java.util.List<? extends road.data.proto.Position3DOrBuilder> 
      getLaneBoundaryPointsOrBuilderList();
  /**
   * <pre>
   *1, BOUNDARY_WHITE_SOLID_LINES //白实线
   *2, BOUNDARY_WHITE_DOTTED_LINES //白虚线
   *3, BOUNDARY_YELLOW_SOLID_LINES //黄实线
   *4, BOUNDARY_YELLOW_DOTTED_LINES //黄虚线
   *5, BOUNDARY_DOUBLE_YELLOW_LINES //双黄线
   *6, BOUNDARY_KERB //路缘，道牙
   *7, BOUNDARY_LOW_FENCE //低栅栏
   *8, BOUNDARY_HIGH_FENCE //高栅栏
   *9, BOUNDARY_POST_FENCE //立柱栅栏
   *10, BOUNDARY_UNKNOWN //未知
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D laneBoundaryPoints = 2;</code>
   */
  road.data.proto.Position3DOrBuilder getLaneBoundaryPointsOrBuilder(
      int index);
}
