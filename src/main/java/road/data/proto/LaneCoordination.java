// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *对道路或车道的引导信息   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.LaneCoordination}
 */
public  final class LaneCoordination extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.LaneCoordination)
    LaneCoordinationOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LaneCoordination.newBuilder() to construct.
  private LaneCoordination(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LaneCoordination() {
    description_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LaneCoordination();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private LaneCoordination(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.ReferenceLink.Builder subBuilder = null;
            if (targetLane_ != null) {
              subBuilder = targetLane_.toBuilder();
            }
            targetLane_ = input.readMessage(road.data.proto.ReferenceLink.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(targetLane_);
              targetLane_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            road.data.proto.ReferencePath.Builder subBuilder = null;
            if (relatedPath_ != null) {
              subBuilder = relatedPath_.toBuilder();
            }
            relatedPath_ = input.readMessage(road.data.proto.ReferencePath.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(relatedPath_);
              relatedPath_ = subBuilder.buildPartial();
            }

            break;
          }
          case 24: {

            tBegin_ = input.readUInt64();
            break;
          }
          case 32: {

            tEnd_ = input.readUInt64();
            break;
          }
          case 40: {

            recommendedSpeed_ = input.readUInt32();
            break;
          }
          case 50: {
            road.data.proto.DriveBehavior.Builder subBuilder = null;
            if (recommendedBehavior_ != null) {
              subBuilder = recommendedBehavior_.toBuilder();
            }
            recommendedBehavior_ = input.readMessage(road.data.proto.DriveBehavior.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(recommendedBehavior_);
              recommendedBehavior_ = subBuilder.buildPartial();
            }

            break;
          }
          case 58: {
            road.data.proto.CoordinationInfo.Builder subBuilder = null;
            if (info_ != null) {
              subBuilder = info_.toBuilder();
            }
            info_ = input.readMessage(road.data.proto.CoordinationInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(info_);
              info_ = subBuilder.buildPartial();
            }

            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            description_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneCoordination_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneCoordination_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.LaneCoordination.class, road.data.proto.LaneCoordination.Builder.class);
  }

  public static final int TARGETLANE_FIELD_NUMBER = 1;
  private road.data.proto.ReferenceLink targetLane_;
  /**
   * <pre>
   *RSU 试图控制的目标链路或通道
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
   */
  public boolean hasTargetLane() {
    return targetLane_ != null;
  }
  /**
   * <pre>
   *RSU 试图控制的目标链路或通道
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
   */
  public road.data.proto.ReferenceLink getTargetLane() {
    return targetLane_ == null ? road.data.proto.ReferenceLink.getDefaultInstance() : targetLane_;
  }
  /**
   * <pre>
   *RSU 试图控制的目标链路或通道
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
   */
  public road.data.proto.ReferenceLinkOrBuilder getTargetLaneOrBuilder() {
    return getTargetLane();
  }

  public static final int RELATEDPATH_FIELD_NUMBER = 2;
  private road.data.proto.ReferencePath relatedPath_;
  /**
   * <pre>
   *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
   */
  public boolean hasRelatedPath() {
    return relatedPath_ != null;
  }
  /**
   * <pre>
   *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
   */
  public road.data.proto.ReferencePath getRelatedPath() {
    return relatedPath_ == null ? road.data.proto.ReferencePath.getDefaultInstance() : relatedPath_;
  }
  /**
   * <pre>
   *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
   */
  public road.data.proto.ReferencePathOrBuilder getRelatedPathOrBuilder() {
    return getRelatedPath();
  }

  public static final int TBEGIN_FIELD_NUMBER = 3;
  private long tBegin_;
  /**
   * <pre>
   *可选，协作规划开始时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 tBegin = 3;</code>
   */
  public long getTBegin() {
    return tBegin_;
  }

  public static final int TEND_FIELD_NUMBER = 4;
  private long tEnd_;
  /**
   * <pre>
   *可选，结束时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 tEnd = 4;</code>
   */
  public long getTEnd() {
    return tEnd_;
  }

  public static final int RECOMMENDEDSPEED_FIELD_NUMBER = 5;
  private int recommendedSpeed_;
  /**
   * <pre>
   *可选，推荐速度，分辨率为0.02m/s，数值8191表示无效数值
   * </pre>
   *
   * <code>uint32 recommendedSpeed = 5;</code>
   */
  public int getRecommendedSpeed() {
    return recommendedSpeed_;
  }

  public static final int RECOMMENDEDBEHAVIOR_FIELD_NUMBER = 6;
  private road.data.proto.DriveBehavior recommendedBehavior_;
  /**
   * <pre>
   *可选，推荐驾驶行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
   */
  public boolean hasRecommendedBehavior() {
    return recommendedBehavior_ != null;
  }
  /**
   * <pre>
   *可选，推荐驾驶行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
   */
  public road.data.proto.DriveBehavior getRecommendedBehavior() {
    return recommendedBehavior_ == null ? road.data.proto.DriveBehavior.getDefaultInstance() : recommendedBehavior_;
  }
  /**
   * <pre>
   *可选，推荐驾驶行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
   */
  public road.data.proto.DriveBehaviorOrBuilder getRecommendedBehaviorOrBuilder() {
    return getRecommendedBehavior();
  }

  public static final int INFO_FIELD_NUMBER = 7;
  private road.data.proto.CoordinationInfo info_;
  /**
   * <pre>
   *可选，与当前协调相关的详细信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
   */
  public boolean hasInfo() {
    return info_ != null;
  }
  /**
   * <pre>
   *可选，与当前协调相关的详细信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
   */
  public road.data.proto.CoordinationInfo getInfo() {
    return info_ == null ? road.data.proto.CoordinationInfo.getDefaultInstance() : info_;
  }
  /**
   * <pre>
   *可选，与当前协调相关的详细信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
   */
  public road.data.proto.CoordinationInfoOrBuilder getInfoOrBuilder() {
    return getInfo();
  }

  public static final int DESCRIPTION_FIELD_NUMBER = 8;
  private volatile java.lang.Object description_;
  /**
   * <pre>
   *可选，附加描述信息
   * </pre>
   *
   * <code>string description = 8;</code>
   */
  public java.lang.String getDescription() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      description_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，附加描述信息
   * </pre>
   *
   * <code>string description = 8;</code>
   */
  public com.google.protobuf.ByteString
      getDescriptionBytes() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      description_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (targetLane_ != null) {
      output.writeMessage(1, getTargetLane());
    }
    if (relatedPath_ != null) {
      output.writeMessage(2, getRelatedPath());
    }
    if (tBegin_ != 0L) {
      output.writeUInt64(3, tBegin_);
    }
    if (tEnd_ != 0L) {
      output.writeUInt64(4, tEnd_);
    }
    if (recommendedSpeed_ != 0) {
      output.writeUInt32(5, recommendedSpeed_);
    }
    if (recommendedBehavior_ != null) {
      output.writeMessage(6, getRecommendedBehavior());
    }
    if (info_ != null) {
      output.writeMessage(7, getInfo());
    }
    if (!getDescriptionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, description_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (targetLane_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getTargetLane());
    }
    if (relatedPath_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getRelatedPath());
    }
    if (tBegin_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, tBegin_);
    }
    if (tEnd_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(4, tEnd_);
    }
    if (recommendedSpeed_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(5, recommendedSpeed_);
    }
    if (recommendedBehavior_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getRecommendedBehavior());
    }
    if (info_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getInfo());
    }
    if (!getDescriptionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, description_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.LaneCoordination)) {
      return super.equals(obj);
    }
    road.data.proto.LaneCoordination other = (road.data.proto.LaneCoordination) obj;

    if (hasTargetLane() != other.hasTargetLane()) return false;
    if (hasTargetLane()) {
      if (!getTargetLane()
          .equals(other.getTargetLane())) return false;
    }
    if (hasRelatedPath() != other.hasRelatedPath()) return false;
    if (hasRelatedPath()) {
      if (!getRelatedPath()
          .equals(other.getRelatedPath())) return false;
    }
    if (getTBegin()
        != other.getTBegin()) return false;
    if (getTEnd()
        != other.getTEnd()) return false;
    if (getRecommendedSpeed()
        != other.getRecommendedSpeed()) return false;
    if (hasRecommendedBehavior() != other.hasRecommendedBehavior()) return false;
    if (hasRecommendedBehavior()) {
      if (!getRecommendedBehavior()
          .equals(other.getRecommendedBehavior())) return false;
    }
    if (hasInfo() != other.hasInfo()) return false;
    if (hasInfo()) {
      if (!getInfo()
          .equals(other.getInfo())) return false;
    }
    if (!getDescription()
        .equals(other.getDescription())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasTargetLane()) {
      hash = (37 * hash) + TARGETLANE_FIELD_NUMBER;
      hash = (53 * hash) + getTargetLane().hashCode();
    }
    if (hasRelatedPath()) {
      hash = (37 * hash) + RELATEDPATH_FIELD_NUMBER;
      hash = (53 * hash) + getRelatedPath().hashCode();
    }
    hash = (37 * hash) + TBEGIN_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTBegin());
    hash = (37 * hash) + TEND_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTEnd());
    hash = (37 * hash) + RECOMMENDEDSPEED_FIELD_NUMBER;
    hash = (53 * hash) + getRecommendedSpeed();
    if (hasRecommendedBehavior()) {
      hash = (37 * hash) + RECOMMENDEDBEHAVIOR_FIELD_NUMBER;
      hash = (53 * hash) + getRecommendedBehavior().hashCode();
    }
    if (hasInfo()) {
      hash = (37 * hash) + INFO_FIELD_NUMBER;
      hash = (53 * hash) + getInfo().hashCode();
    }
    hash = (37 * hash) + DESCRIPTION_FIELD_NUMBER;
    hash = (53 * hash) + getDescription().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.LaneCoordination parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneCoordination parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneCoordination parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneCoordination parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneCoordination parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneCoordination parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneCoordination parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneCoordination parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneCoordination parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneCoordination parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneCoordination parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneCoordination parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.LaneCoordination prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *对道路或车道的引导信息   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.LaneCoordination}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.LaneCoordination)
      road.data.proto.LaneCoordinationOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneCoordination_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneCoordination_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.LaneCoordination.class, road.data.proto.LaneCoordination.Builder.class);
    }

    // Construct using road.data.proto.LaneCoordination.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (targetLaneBuilder_ == null) {
        targetLane_ = null;
      } else {
        targetLane_ = null;
        targetLaneBuilder_ = null;
      }
      if (relatedPathBuilder_ == null) {
        relatedPath_ = null;
      } else {
        relatedPath_ = null;
        relatedPathBuilder_ = null;
      }
      tBegin_ = 0L;

      tEnd_ = 0L;

      recommendedSpeed_ = 0;

      if (recommendedBehaviorBuilder_ == null) {
        recommendedBehavior_ = null;
      } else {
        recommendedBehavior_ = null;
        recommendedBehaviorBuilder_ = null;
      }
      if (infoBuilder_ == null) {
        info_ = null;
      } else {
        info_ = null;
        infoBuilder_ = null;
      }
      description_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneCoordination_descriptor;
    }

    @java.lang.Override
    public road.data.proto.LaneCoordination getDefaultInstanceForType() {
      return road.data.proto.LaneCoordination.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.LaneCoordination build() {
      road.data.proto.LaneCoordination result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.LaneCoordination buildPartial() {
      road.data.proto.LaneCoordination result = new road.data.proto.LaneCoordination(this);
      if (targetLaneBuilder_ == null) {
        result.targetLane_ = targetLane_;
      } else {
        result.targetLane_ = targetLaneBuilder_.build();
      }
      if (relatedPathBuilder_ == null) {
        result.relatedPath_ = relatedPath_;
      } else {
        result.relatedPath_ = relatedPathBuilder_.build();
      }
      result.tBegin_ = tBegin_;
      result.tEnd_ = tEnd_;
      result.recommendedSpeed_ = recommendedSpeed_;
      if (recommendedBehaviorBuilder_ == null) {
        result.recommendedBehavior_ = recommendedBehavior_;
      } else {
        result.recommendedBehavior_ = recommendedBehaviorBuilder_.build();
      }
      if (infoBuilder_ == null) {
        result.info_ = info_;
      } else {
        result.info_ = infoBuilder_.build();
      }
      result.description_ = description_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.LaneCoordination) {
        return mergeFrom((road.data.proto.LaneCoordination)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.LaneCoordination other) {
      if (other == road.data.proto.LaneCoordination.getDefaultInstance()) return this;
      if (other.hasTargetLane()) {
        mergeTargetLane(other.getTargetLane());
      }
      if (other.hasRelatedPath()) {
        mergeRelatedPath(other.getRelatedPath());
      }
      if (other.getTBegin() != 0L) {
        setTBegin(other.getTBegin());
      }
      if (other.getTEnd() != 0L) {
        setTEnd(other.getTEnd());
      }
      if (other.getRecommendedSpeed() != 0) {
        setRecommendedSpeed(other.getRecommendedSpeed());
      }
      if (other.hasRecommendedBehavior()) {
        mergeRecommendedBehavior(other.getRecommendedBehavior());
      }
      if (other.hasInfo()) {
        mergeInfo(other.getInfo());
      }
      if (!other.getDescription().isEmpty()) {
        description_ = other.description_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.LaneCoordination parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.LaneCoordination) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.ReferenceLink targetLane_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder> targetLaneBuilder_;
    /**
     * <pre>
     *RSU 试图控制的目标链路或通道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
     */
    public boolean hasTargetLane() {
      return targetLaneBuilder_ != null || targetLane_ != null;
    }
    /**
     * <pre>
     *RSU 试图控制的目标链路或通道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
     */
    public road.data.proto.ReferenceLink getTargetLane() {
      if (targetLaneBuilder_ == null) {
        return targetLane_ == null ? road.data.proto.ReferenceLink.getDefaultInstance() : targetLane_;
      } else {
        return targetLaneBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *RSU 试图控制的目标链路或通道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
     */
    public Builder setTargetLane(road.data.proto.ReferenceLink value) {
      if (targetLaneBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        targetLane_ = value;
        onChanged();
      } else {
        targetLaneBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *RSU 试图控制的目标链路或通道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
     */
    public Builder setTargetLane(
        road.data.proto.ReferenceLink.Builder builderForValue) {
      if (targetLaneBuilder_ == null) {
        targetLane_ = builderForValue.build();
        onChanged();
      } else {
        targetLaneBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *RSU 试图控制的目标链路或通道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
     */
    public Builder mergeTargetLane(road.data.proto.ReferenceLink value) {
      if (targetLaneBuilder_ == null) {
        if (targetLane_ != null) {
          targetLane_ =
            road.data.proto.ReferenceLink.newBuilder(targetLane_).mergeFrom(value).buildPartial();
        } else {
          targetLane_ = value;
        }
        onChanged();
      } else {
        targetLaneBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *RSU 试图控制的目标链路或通道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
     */
    public Builder clearTargetLane() {
      if (targetLaneBuilder_ == null) {
        targetLane_ = null;
        onChanged();
      } else {
        targetLane_ = null;
        targetLaneBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *RSU 试图控制的目标链路或通道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
     */
    public road.data.proto.ReferenceLink.Builder getTargetLaneBuilder() {
      
      onChanged();
      return getTargetLaneFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *RSU 试图控制的目标链路或通道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
     */
    public road.data.proto.ReferenceLinkOrBuilder getTargetLaneOrBuilder() {
      if (targetLaneBuilder_ != null) {
        return targetLaneBuilder_.getMessageOrBuilder();
      } else {
        return targetLane_ == null ?
            road.data.proto.ReferenceLink.getDefaultInstance() : targetLane_;
      }
    }
    /**
     * <pre>
     *RSU 试图控制的目标链路或通道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder> 
        getTargetLaneFieldBuilder() {
      if (targetLaneBuilder_ == null) {
        targetLaneBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder>(
                getTargetLane(),
                getParentForChildren(),
                isClean());
        targetLane_ = null;
      }
      return targetLaneBuilder_;
    }

    private road.data.proto.ReferencePath relatedPath_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder> relatedPathBuilder_;
    /**
     * <pre>
     *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
     */
    public boolean hasRelatedPath() {
      return relatedPathBuilder_ != null || relatedPath_ != null;
    }
    /**
     * <pre>
     *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
     */
    public road.data.proto.ReferencePath getRelatedPath() {
      if (relatedPathBuilder_ == null) {
        return relatedPath_ == null ? road.data.proto.ReferencePath.getDefaultInstance() : relatedPath_;
      } else {
        return relatedPathBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
     */
    public Builder setRelatedPath(road.data.proto.ReferencePath value) {
      if (relatedPathBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        relatedPath_ = value;
        onChanged();
      } else {
        relatedPathBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
     */
    public Builder setRelatedPath(
        road.data.proto.ReferencePath.Builder builderForValue) {
      if (relatedPathBuilder_ == null) {
        relatedPath_ = builderForValue.build();
        onChanged();
      } else {
        relatedPathBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
     */
    public Builder mergeRelatedPath(road.data.proto.ReferencePath value) {
      if (relatedPathBuilder_ == null) {
        if (relatedPath_ != null) {
          relatedPath_ =
            road.data.proto.ReferencePath.newBuilder(relatedPath_).mergeFrom(value).buildPartial();
        } else {
          relatedPath_ = value;
        }
        onChanged();
      } else {
        relatedPathBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
     */
    public Builder clearRelatedPath() {
      if (relatedPathBuilder_ == null) {
        relatedPath_ = null;
        onChanged();
      } else {
        relatedPath_ = null;
        relatedPathBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
     */
    public road.data.proto.ReferencePath.Builder getRelatedPathBuilder() {
      
      onChanged();
      return getRelatedPathFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
     */
    public road.data.proto.ReferencePathOrBuilder getRelatedPathOrBuilder() {
      if (relatedPathBuilder_ != null) {
        return relatedPathBuilder_.getMessageOrBuilder();
      } else {
        return relatedPath_ == null ?
            road.data.proto.ReferencePath.getDefaultInstance() : relatedPath_;
      }
    }
    /**
     * <pre>
     *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder> 
        getRelatedPathFieldBuilder() {
      if (relatedPathBuilder_ == null) {
        relatedPathBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder>(
                getRelatedPath(),
                getParentForChildren(),
                isClean());
        relatedPath_ = null;
      }
      return relatedPathBuilder_;
    }

    private long tBegin_ ;
    /**
     * <pre>
     *可选，协作规划开始时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 tBegin = 3;</code>
     */
    public long getTBegin() {
      return tBegin_;
    }
    /**
     * <pre>
     *可选，协作规划开始时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 tBegin = 3;</code>
     */
    public Builder setTBegin(long value) {
      
      tBegin_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，协作规划开始时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 tBegin = 3;</code>
     */
    public Builder clearTBegin() {
      
      tBegin_ = 0L;
      onChanged();
      return this;
    }

    private long tEnd_ ;
    /**
     * <pre>
     *可选，结束时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 tEnd = 4;</code>
     */
    public long getTEnd() {
      return tEnd_;
    }
    /**
     * <pre>
     *可选，结束时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 tEnd = 4;</code>
     */
    public Builder setTEnd(long value) {
      
      tEnd_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，结束时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 tEnd = 4;</code>
     */
    public Builder clearTEnd() {
      
      tEnd_ = 0L;
      onChanged();
      return this;
    }

    private int recommendedSpeed_ ;
    /**
     * <pre>
     *可选，推荐速度，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 recommendedSpeed = 5;</code>
     */
    public int getRecommendedSpeed() {
      return recommendedSpeed_;
    }
    /**
     * <pre>
     *可选，推荐速度，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 recommendedSpeed = 5;</code>
     */
    public Builder setRecommendedSpeed(int value) {
      
      recommendedSpeed_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，推荐速度，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 recommendedSpeed = 5;</code>
     */
    public Builder clearRecommendedSpeed() {
      
      recommendedSpeed_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.DriveBehavior recommendedBehavior_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.DriveBehavior, road.data.proto.DriveBehavior.Builder, road.data.proto.DriveBehaviorOrBuilder> recommendedBehaviorBuilder_;
    /**
     * <pre>
     *可选，推荐驾驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
     */
    public boolean hasRecommendedBehavior() {
      return recommendedBehaviorBuilder_ != null || recommendedBehavior_ != null;
    }
    /**
     * <pre>
     *可选，推荐驾驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
     */
    public road.data.proto.DriveBehavior getRecommendedBehavior() {
      if (recommendedBehaviorBuilder_ == null) {
        return recommendedBehavior_ == null ? road.data.proto.DriveBehavior.getDefaultInstance() : recommendedBehavior_;
      } else {
        return recommendedBehaviorBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，推荐驾驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
     */
    public Builder setRecommendedBehavior(road.data.proto.DriveBehavior value) {
      if (recommendedBehaviorBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        recommendedBehavior_ = value;
        onChanged();
      } else {
        recommendedBehaviorBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，推荐驾驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
     */
    public Builder setRecommendedBehavior(
        road.data.proto.DriveBehavior.Builder builderForValue) {
      if (recommendedBehaviorBuilder_ == null) {
        recommendedBehavior_ = builderForValue.build();
        onChanged();
      } else {
        recommendedBehaviorBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，推荐驾驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
     */
    public Builder mergeRecommendedBehavior(road.data.proto.DriveBehavior value) {
      if (recommendedBehaviorBuilder_ == null) {
        if (recommendedBehavior_ != null) {
          recommendedBehavior_ =
            road.data.proto.DriveBehavior.newBuilder(recommendedBehavior_).mergeFrom(value).buildPartial();
        } else {
          recommendedBehavior_ = value;
        }
        onChanged();
      } else {
        recommendedBehaviorBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，推荐驾驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
     */
    public Builder clearRecommendedBehavior() {
      if (recommendedBehaviorBuilder_ == null) {
        recommendedBehavior_ = null;
        onChanged();
      } else {
        recommendedBehavior_ = null;
        recommendedBehaviorBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，推荐驾驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
     */
    public road.data.proto.DriveBehavior.Builder getRecommendedBehaviorBuilder() {
      
      onChanged();
      return getRecommendedBehaviorFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，推荐驾驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
     */
    public road.data.proto.DriveBehaviorOrBuilder getRecommendedBehaviorOrBuilder() {
      if (recommendedBehaviorBuilder_ != null) {
        return recommendedBehaviorBuilder_.getMessageOrBuilder();
      } else {
        return recommendedBehavior_ == null ?
            road.data.proto.DriveBehavior.getDefaultInstance() : recommendedBehavior_;
      }
    }
    /**
     * <pre>
     *可选，推荐驾驶行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.DriveBehavior, road.data.proto.DriveBehavior.Builder, road.data.proto.DriveBehaviorOrBuilder> 
        getRecommendedBehaviorFieldBuilder() {
      if (recommendedBehaviorBuilder_ == null) {
        recommendedBehaviorBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.DriveBehavior, road.data.proto.DriveBehavior.Builder, road.data.proto.DriveBehaviorOrBuilder>(
                getRecommendedBehavior(),
                getParentForChildren(),
                isClean());
        recommendedBehavior_ = null;
      }
      return recommendedBehaviorBuilder_;
    }

    private road.data.proto.CoordinationInfo info_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.CoordinationInfo, road.data.proto.CoordinationInfo.Builder, road.data.proto.CoordinationInfoOrBuilder> infoBuilder_;
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
     */
    public boolean hasInfo() {
      return infoBuilder_ != null || info_ != null;
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
     */
    public road.data.proto.CoordinationInfo getInfo() {
      if (infoBuilder_ == null) {
        return info_ == null ? road.data.proto.CoordinationInfo.getDefaultInstance() : info_;
      } else {
        return infoBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
     */
    public Builder setInfo(road.data.proto.CoordinationInfo value) {
      if (infoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        info_ = value;
        onChanged();
      } else {
        infoBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
     */
    public Builder setInfo(
        road.data.proto.CoordinationInfo.Builder builderForValue) {
      if (infoBuilder_ == null) {
        info_ = builderForValue.build();
        onChanged();
      } else {
        infoBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
     */
    public Builder mergeInfo(road.data.proto.CoordinationInfo value) {
      if (infoBuilder_ == null) {
        if (info_ != null) {
          info_ =
            road.data.proto.CoordinationInfo.newBuilder(info_).mergeFrom(value).buildPartial();
        } else {
          info_ = value;
        }
        onChanged();
      } else {
        infoBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
     */
    public Builder clearInfo() {
      if (infoBuilder_ == null) {
        info_ = null;
        onChanged();
      } else {
        info_ = null;
        infoBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
     */
    public road.data.proto.CoordinationInfo.Builder getInfoBuilder() {
      
      onChanged();
      return getInfoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
     */
    public road.data.proto.CoordinationInfoOrBuilder getInfoOrBuilder() {
      if (infoBuilder_ != null) {
        return infoBuilder_.getMessageOrBuilder();
      } else {
        return info_ == null ?
            road.data.proto.CoordinationInfo.getDefaultInstance() : info_;
      }
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.CoordinationInfo, road.data.proto.CoordinationInfo.Builder, road.data.proto.CoordinationInfoOrBuilder> 
        getInfoFieldBuilder() {
      if (infoBuilder_ == null) {
        infoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.CoordinationInfo, road.data.proto.CoordinationInfo.Builder, road.data.proto.CoordinationInfoOrBuilder>(
                getInfo(),
                getParentForChildren(),
                isClean());
        info_ = null;
      }
      return infoBuilder_;
    }

    private java.lang.Object description_ = "";
    /**
     * <pre>
     *可选，附加描述信息
     * </pre>
     *
     * <code>string description = 8;</code>
     */
    public java.lang.String getDescription() {
      java.lang.Object ref = description_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        description_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，附加描述信息
     * </pre>
     *
     * <code>string description = 8;</code>
     */
    public com.google.protobuf.ByteString
        getDescriptionBytes() {
      java.lang.Object ref = description_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        description_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，附加描述信息
     * </pre>
     *
     * <code>string description = 8;</code>
     */
    public Builder setDescription(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      description_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，附加描述信息
     * </pre>
     *
     * <code>string description = 8;</code>
     */
    public Builder clearDescription() {
      
      description_ = getDefaultInstance().getDescription();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，附加描述信息
     * </pre>
     *
     * <code>string description = 8;</code>
     */
    public Builder setDescriptionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      description_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.LaneCoordination)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.LaneCoordination)
  private static final road.data.proto.LaneCoordination DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.LaneCoordination();
  }

  public static road.data.proto.LaneCoordination getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LaneCoordination>
      PARSER = new com.google.protobuf.AbstractParser<LaneCoordination>() {
    @java.lang.Override
    public LaneCoordination parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new LaneCoordination(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<LaneCoordination> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LaneCoordination> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.LaneCoordination getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

