// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneCoordinationOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneCoordination)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *RSU 试图控制的目标链路或通道
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
   */
  boolean hasTargetLane();
  /**
   * <pre>
   *RSU 试图控制的目标链路或通道
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
   */
  road.data.proto.ReferenceLink getTargetLane();
  /**
   * <pre>
   *RSU 试图控制的目标链路或通道
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink targetLane = 1;</code>
   */
  road.data.proto.ReferenceLinkOrBuilder getTargetLaneOrBuilder();

  /**
   * <pre>
   *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
   */
  boolean hasRelatedPath();
  /**
   * <pre>
   *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
   */
  road.data.proto.ReferencePath getRelatedPath();
  /**
   * <pre>
   *可选，参考路径（如果存在）以帮助车辆确定,是否应该遵循协调
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferencePath relatedPath = 2;</code>
   */
  road.data.proto.ReferencePathOrBuilder getRelatedPathOrBuilder();

  /**
   * <pre>
   *可选，协作规划开始时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 tBegin = 3;</code>
   */
  long getTBegin();

  /**
   * <pre>
   *可选，结束时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 tEnd = 4;</code>
   */
  long getTEnd();

  /**
   * <pre>
   *可选，推荐速度，分辨率为0.02m/s，数值8191表示无效数值
   * </pre>
   *
   * <code>uint32 recommendedSpeed = 5;</code>
   */
  int getRecommendedSpeed();

  /**
   * <pre>
   *可选，推荐驾驶行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
   */
  boolean hasRecommendedBehavior();
  /**
   * <pre>
   *可选，推荐驾驶行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
   */
  road.data.proto.DriveBehavior getRecommendedBehavior();
  /**
   * <pre>
   *可选，推荐驾驶行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveBehavior recommendedBehavior = 6;</code>
   */
  road.data.proto.DriveBehaviorOrBuilder getRecommendedBehaviorOrBuilder();

  /**
   * <pre>
   *可选，与当前协调相关的详细信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
   */
  boolean hasInfo();
  /**
   * <pre>
   *可选，与当前协调相关的详细信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
   */
  road.data.proto.CoordinationInfo getInfo();
  /**
   * <pre>
   *可选，与当前协调相关的详细信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 7;</code>
   */
  road.data.proto.CoordinationInfoOrBuilder getInfoOrBuilder();

  /**
   * <pre>
   *可选，附加描述信息
   * </pre>
   *
   * <code>string description = 8;</code>
   */
  java.lang.String getDescription();
  /**
   * <pre>
   *可选，附加描述信息
   * </pre>
   *
   * <code>string description = 8;</code>
   */
  com.google.protobuf.ByteString
      getDescriptionBytes();
}
