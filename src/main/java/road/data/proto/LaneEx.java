// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车道扩展信息列表LaneEx         
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.LaneEx}
 */
public  final class LaneEx extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.LaneEx)
    LaneExOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LaneEx.newBuilder() to construct.
  private LaneEx(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LaneEx() {
    connectsToEx_ = java.util.Collections.emptyList();
    speedLimits_ = java.util.Collections.emptyList();
    stPoints_ = java.util.Collections.emptyList();
    leftBoundary_ = java.util.Collections.emptyList();
    rightBoundary_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LaneEx();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private LaneEx(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            laneRefId_ = input.readInt32();
            break;
          }
          case 16: {

            laneWidth_ = input.readUInt32();
            break;
          }
          case 26: {
            road.data.proto.LaneAttributes.Builder subBuilder = null;
            if (laneAttributes_ != null) {
              subBuilder = laneAttributes_.toBuilder();
            }
            laneAttributes_ = input.readMessage(road.data.proto.LaneAttributes.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(laneAttributes_);
              laneAttributes_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            road.data.proto.AllowedManeuvers.Builder subBuilder = null;
            if (maneuvers_ != null) {
              subBuilder = maneuvers_.toBuilder();
            }
            maneuvers_ = input.readMessage(road.data.proto.AllowedManeuvers.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(maneuvers_);
              maneuvers_ = subBuilder.buildPartial();
            }

            break;
          }
          case 42: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              connectsToEx_ = new java.util.ArrayList<road.data.proto.ConnectionEx>();
              mutable_bitField0_ |= 0x00000001;
            }
            connectsToEx_.add(
                input.readMessage(road.data.proto.ConnectionEx.parser(), extensionRegistry));
            break;
          }
          case 50: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              speedLimits_ = new java.util.ArrayList<road.data.proto.RegulatorySpeedLimit>();
              mutable_bitField0_ |= 0x00000002;
            }
            speedLimits_.add(
                input.readMessage(road.data.proto.RegulatorySpeedLimit.parser(), extensionRegistry));
            break;
          }
          case 58: {
            if (!((mutable_bitField0_ & 0x00000004) != 0)) {
              stPoints_ = new java.util.ArrayList<road.data.proto.STPoint>();
              mutable_bitField0_ |= 0x00000004;
            }
            stPoints_.add(
                input.readMessage(road.data.proto.STPoint.parser(), extensionRegistry));
            break;
          }
          case 66: {
            if (!((mutable_bitField0_ & 0x00000008) != 0)) {
              leftBoundary_ = new java.util.ArrayList<road.data.proto.LaneBoundary>();
              mutable_bitField0_ |= 0x00000008;
            }
            leftBoundary_.add(
                input.readMessage(road.data.proto.LaneBoundary.parser(), extensionRegistry));
            break;
          }
          case 74: {
            if (!((mutable_bitField0_ & 0x00000010) != 0)) {
              rightBoundary_ = new java.util.ArrayList<road.data.proto.LaneBoundary>();
              mutable_bitField0_ |= 0x00000010;
            }
            rightBoundary_.add(
                input.readMessage(road.data.proto.LaneBoundary.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        connectsToEx_ = java.util.Collections.unmodifiableList(connectsToEx_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        speedLimits_ = java.util.Collections.unmodifiableList(speedLimits_);
      }
      if (((mutable_bitField0_ & 0x00000004) != 0)) {
        stPoints_ = java.util.Collections.unmodifiableList(stPoints_);
      }
      if (((mutable_bitField0_ & 0x00000008) != 0)) {
        leftBoundary_ = java.util.Collections.unmodifiableList(leftBoundary_);
      }
      if (((mutable_bitField0_ & 0x00000010) != 0)) {
        rightBoundary_ = java.util.Collections.unmodifiableList(rightBoundary_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneEx_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneEx_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.LaneEx.class, road.data.proto.LaneEx.Builder.class);
  }

  public static final int LANEREFID_FIELD_NUMBER = 1;
  private int laneRefId_;
  /**
   * <pre>
   *关联车道标识ID
   * </pre>
   *
   * <code>int32 laneRefId = 1;</code>
   */
  public int getLaneRefId() {
    return laneRefId_;
  }

  public static final int LANEWIDTH_FIELD_NUMBER = 2;
  private int laneWidth_;
  /**
   * <pre>
   *可选，车道宽度，单位：1cm
   * </pre>
   *
   * <code>uint32 laneWidth = 2;</code>
   */
  public int getLaneWidth() {
    return laneWidth_;
  }

  public static final int LANEATTRIBUTES_FIELD_NUMBER = 3;
  private road.data.proto.LaneAttributes laneAttributes_;
  /**
   * <pre>
   *可选，车道属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
   */
  public boolean hasLaneAttributes() {
    return laneAttributes_ != null;
  }
  /**
   * <pre>
   *可选，车道属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
   */
  public road.data.proto.LaneAttributes getLaneAttributes() {
    return laneAttributes_ == null ? road.data.proto.LaneAttributes.getDefaultInstance() : laneAttributes_;
  }
  /**
   * <pre>
   *可选，车道属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
   */
  public road.data.proto.LaneAttributesOrBuilder getLaneAttributesOrBuilder() {
    return getLaneAttributes();
  }

  public static final int MANEUVERS_FIELD_NUMBER = 4;
  private road.data.proto.AllowedManeuvers maneuvers_;
  /**
   * <pre>
   *可选，车道出口的允许转向行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
   */
  public boolean hasManeuvers() {
    return maneuvers_ != null;
  }
  /**
   * <pre>
   *可选，车道出口的允许转向行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
   */
  public road.data.proto.AllowedManeuvers getManeuvers() {
    return maneuvers_ == null ? road.data.proto.AllowedManeuvers.getDefaultInstance() : maneuvers_;
  }
  /**
   * <pre>
   *可选，车道出口的允许转向行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
   */
  public road.data.proto.AllowedManeuversOrBuilder getManeuversOrBuilder() {
    return getManeuvers();
  }

  public static final int CONNECTSTOEX_FIELD_NUMBER = 5;
  private java.util.List<road.data.proto.ConnectionEx> connectsToEx_;
  /**
   * <pre>
   *可选，车道与下游路段车道的连接关系扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
   */
  public java.util.List<road.data.proto.ConnectionEx> getConnectsToExList() {
    return connectsToEx_;
  }
  /**
   * <pre>
   *可选，车道与下游路段车道的连接关系扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
   */
  public java.util.List<? extends road.data.proto.ConnectionExOrBuilder> 
      getConnectsToExOrBuilderList() {
    return connectsToEx_;
  }
  /**
   * <pre>
   *可选，车道与下游路段车道的连接关系扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
   */
  public int getConnectsToExCount() {
    return connectsToEx_.size();
  }
  /**
   * <pre>
   *可选，车道与下游路段车道的连接关系扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
   */
  public road.data.proto.ConnectionEx getConnectsToEx(int index) {
    return connectsToEx_.get(index);
  }
  /**
   * <pre>
   *可选，车道与下游路段车道的连接关系扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
   */
  public road.data.proto.ConnectionExOrBuilder getConnectsToExOrBuilder(
      int index) {
    return connectsToEx_.get(index);
  }

  public static final int SPEEDLIMITS_FIELD_NUMBER = 6;
  private java.util.List<road.data.proto.RegulatorySpeedLimit> speedLimits_;
  /**
   * <pre>
   *可选，限速
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  public java.util.List<road.data.proto.RegulatorySpeedLimit> getSpeedLimitsList() {
    return speedLimits_;
  }
  /**
   * <pre>
   *可选，限速
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  public java.util.List<? extends road.data.proto.RegulatorySpeedLimitOrBuilder> 
      getSpeedLimitsOrBuilderList() {
    return speedLimits_;
  }
  /**
   * <pre>
   *可选，限速
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  public int getSpeedLimitsCount() {
    return speedLimits_.size();
  }
  /**
   * <pre>
   *可选，限速
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  public road.data.proto.RegulatorySpeedLimit getSpeedLimits(int index) {
    return speedLimits_.get(index);
  }
  /**
   * <pre>
   *可选，限速
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  public road.data.proto.RegulatorySpeedLimitOrBuilder getSpeedLimitsOrBuilder(
      int index) {
    return speedLimits_.get(index);
  }

  public static final int STPOINTS_FIELD_NUMBER = 7;
  private java.util.List<road.data.proto.STPoint> stPoints_;
  /**
   * <pre>
   *可选，ST坐标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
   */
  public java.util.List<road.data.proto.STPoint> getStPointsList() {
    return stPoints_;
  }
  /**
   * <pre>
   *可选，ST坐标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
   */
  public java.util.List<? extends road.data.proto.STPointOrBuilder> 
      getStPointsOrBuilderList() {
    return stPoints_;
  }
  /**
   * <pre>
   *可选，ST坐标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
   */
  public int getStPointsCount() {
    return stPoints_.size();
  }
  /**
   * <pre>
   *可选，ST坐标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
   */
  public road.data.proto.STPoint getStPoints(int index) {
    return stPoints_.get(index);
  }
  /**
   * <pre>
   *可选，ST坐标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
   */
  public road.data.proto.STPointOrBuilder getStPointsOrBuilder(
      int index) {
    return stPoints_.get(index);
  }

  public static final int LEFTBOUNDARY_FIELD_NUMBER = 8;
  private java.util.List<road.data.proto.LaneBoundary> leftBoundary_;
  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  public java.util.List<road.data.proto.LaneBoundary> getLeftBoundaryList() {
    return leftBoundary_;
  }
  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  public java.util.List<? extends road.data.proto.LaneBoundaryOrBuilder> 
      getLeftBoundaryOrBuilderList() {
    return leftBoundary_;
  }
  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  public int getLeftBoundaryCount() {
    return leftBoundary_.size();
  }
  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  public road.data.proto.LaneBoundary getLeftBoundary(int index) {
    return leftBoundary_.get(index);
  }
  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  public road.data.proto.LaneBoundaryOrBuilder getLeftBoundaryOrBuilder(
      int index) {
    return leftBoundary_.get(index);
  }

  public static final int RIGHTBOUNDARY_FIELD_NUMBER = 9;
  private java.util.List<road.data.proto.LaneBoundary> rightBoundary_;
  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  public java.util.List<road.data.proto.LaneBoundary> getRightBoundaryList() {
    return rightBoundary_;
  }
  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  public java.util.List<? extends road.data.proto.LaneBoundaryOrBuilder> 
      getRightBoundaryOrBuilderList() {
    return rightBoundary_;
  }
  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  public int getRightBoundaryCount() {
    return rightBoundary_.size();
  }
  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  public road.data.proto.LaneBoundary getRightBoundary(int index) {
    return rightBoundary_.get(index);
  }
  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  public road.data.proto.LaneBoundaryOrBuilder getRightBoundaryOrBuilder(
      int index) {
    return rightBoundary_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (laneRefId_ != 0) {
      output.writeInt32(1, laneRefId_);
    }
    if (laneWidth_ != 0) {
      output.writeUInt32(2, laneWidth_);
    }
    if (laneAttributes_ != null) {
      output.writeMessage(3, getLaneAttributes());
    }
    if (maneuvers_ != null) {
      output.writeMessage(4, getManeuvers());
    }
    for (int i = 0; i < connectsToEx_.size(); i++) {
      output.writeMessage(5, connectsToEx_.get(i));
    }
    for (int i = 0; i < speedLimits_.size(); i++) {
      output.writeMessage(6, speedLimits_.get(i));
    }
    for (int i = 0; i < stPoints_.size(); i++) {
      output.writeMessage(7, stPoints_.get(i));
    }
    for (int i = 0; i < leftBoundary_.size(); i++) {
      output.writeMessage(8, leftBoundary_.get(i));
    }
    for (int i = 0; i < rightBoundary_.size(); i++) {
      output.writeMessage(9, rightBoundary_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (laneRefId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, laneRefId_);
    }
    if (laneWidth_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, laneWidth_);
    }
    if (laneAttributes_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getLaneAttributes());
    }
    if (maneuvers_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getManeuvers());
    }
    for (int i = 0; i < connectsToEx_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, connectsToEx_.get(i));
    }
    for (int i = 0; i < speedLimits_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, speedLimits_.get(i));
    }
    for (int i = 0; i < stPoints_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, stPoints_.get(i));
    }
    for (int i = 0; i < leftBoundary_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, leftBoundary_.get(i));
    }
    for (int i = 0; i < rightBoundary_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(9, rightBoundary_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.LaneEx)) {
      return super.equals(obj);
    }
    road.data.proto.LaneEx other = (road.data.proto.LaneEx) obj;

    if (getLaneRefId()
        != other.getLaneRefId()) return false;
    if (getLaneWidth()
        != other.getLaneWidth()) return false;
    if (hasLaneAttributes() != other.hasLaneAttributes()) return false;
    if (hasLaneAttributes()) {
      if (!getLaneAttributes()
          .equals(other.getLaneAttributes())) return false;
    }
    if (hasManeuvers() != other.hasManeuvers()) return false;
    if (hasManeuvers()) {
      if (!getManeuvers()
          .equals(other.getManeuvers())) return false;
    }
    if (!getConnectsToExList()
        .equals(other.getConnectsToExList())) return false;
    if (!getSpeedLimitsList()
        .equals(other.getSpeedLimitsList())) return false;
    if (!getStPointsList()
        .equals(other.getStPointsList())) return false;
    if (!getLeftBoundaryList()
        .equals(other.getLeftBoundaryList())) return false;
    if (!getRightBoundaryList()
        .equals(other.getRightBoundaryList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + LANEREFID_FIELD_NUMBER;
    hash = (53 * hash) + getLaneRefId();
    hash = (37 * hash) + LANEWIDTH_FIELD_NUMBER;
    hash = (53 * hash) + getLaneWidth();
    if (hasLaneAttributes()) {
      hash = (37 * hash) + LANEATTRIBUTES_FIELD_NUMBER;
      hash = (53 * hash) + getLaneAttributes().hashCode();
    }
    if (hasManeuvers()) {
      hash = (37 * hash) + MANEUVERS_FIELD_NUMBER;
      hash = (53 * hash) + getManeuvers().hashCode();
    }
    if (getConnectsToExCount() > 0) {
      hash = (37 * hash) + CONNECTSTOEX_FIELD_NUMBER;
      hash = (53 * hash) + getConnectsToExList().hashCode();
    }
    if (getSpeedLimitsCount() > 0) {
      hash = (37 * hash) + SPEEDLIMITS_FIELD_NUMBER;
      hash = (53 * hash) + getSpeedLimitsList().hashCode();
    }
    if (getStPointsCount() > 0) {
      hash = (37 * hash) + STPOINTS_FIELD_NUMBER;
      hash = (53 * hash) + getStPointsList().hashCode();
    }
    if (getLeftBoundaryCount() > 0) {
      hash = (37 * hash) + LEFTBOUNDARY_FIELD_NUMBER;
      hash = (53 * hash) + getLeftBoundaryList().hashCode();
    }
    if (getRightBoundaryCount() > 0) {
      hash = (37 * hash) + RIGHTBOUNDARY_FIELD_NUMBER;
      hash = (53 * hash) + getRightBoundaryList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.LaneEx parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneEx parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneEx parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneEx parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneEx parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneEx parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneEx parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneEx parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneEx parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneEx parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneEx parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneEx parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.LaneEx prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *车道扩展信息列表LaneEx         
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.LaneEx}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.LaneEx)
      road.data.proto.LaneExOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneEx_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneEx_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.LaneEx.class, road.data.proto.LaneEx.Builder.class);
    }

    // Construct using road.data.proto.LaneEx.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getConnectsToExFieldBuilder();
        getSpeedLimitsFieldBuilder();
        getStPointsFieldBuilder();
        getLeftBoundaryFieldBuilder();
        getRightBoundaryFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      laneRefId_ = 0;

      laneWidth_ = 0;

      if (laneAttributesBuilder_ == null) {
        laneAttributes_ = null;
      } else {
        laneAttributes_ = null;
        laneAttributesBuilder_ = null;
      }
      if (maneuversBuilder_ == null) {
        maneuvers_ = null;
      } else {
        maneuvers_ = null;
        maneuversBuilder_ = null;
      }
      if (connectsToExBuilder_ == null) {
        connectsToEx_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        connectsToExBuilder_.clear();
      }
      if (speedLimitsBuilder_ == null) {
        speedLimits_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        speedLimitsBuilder_.clear();
      }
      if (stPointsBuilder_ == null) {
        stPoints_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
      } else {
        stPointsBuilder_.clear();
      }
      if (leftBoundaryBuilder_ == null) {
        leftBoundary_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
      } else {
        leftBoundaryBuilder_.clear();
      }
      if (rightBoundaryBuilder_ == null) {
        rightBoundary_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
      } else {
        rightBoundaryBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneEx_descriptor;
    }

    @java.lang.Override
    public road.data.proto.LaneEx getDefaultInstanceForType() {
      return road.data.proto.LaneEx.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.LaneEx build() {
      road.data.proto.LaneEx result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.LaneEx buildPartial() {
      road.data.proto.LaneEx result = new road.data.proto.LaneEx(this);
      int from_bitField0_ = bitField0_;
      result.laneRefId_ = laneRefId_;
      result.laneWidth_ = laneWidth_;
      if (laneAttributesBuilder_ == null) {
        result.laneAttributes_ = laneAttributes_;
      } else {
        result.laneAttributes_ = laneAttributesBuilder_.build();
      }
      if (maneuversBuilder_ == null) {
        result.maneuvers_ = maneuvers_;
      } else {
        result.maneuvers_ = maneuversBuilder_.build();
      }
      if (connectsToExBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          connectsToEx_ = java.util.Collections.unmodifiableList(connectsToEx_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.connectsToEx_ = connectsToEx_;
      } else {
        result.connectsToEx_ = connectsToExBuilder_.build();
      }
      if (speedLimitsBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          speedLimits_ = java.util.Collections.unmodifiableList(speedLimits_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.speedLimits_ = speedLimits_;
      } else {
        result.speedLimits_ = speedLimitsBuilder_.build();
      }
      if (stPointsBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          stPoints_ = java.util.Collections.unmodifiableList(stPoints_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.stPoints_ = stPoints_;
      } else {
        result.stPoints_ = stPointsBuilder_.build();
      }
      if (leftBoundaryBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          leftBoundary_ = java.util.Collections.unmodifiableList(leftBoundary_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.leftBoundary_ = leftBoundary_;
      } else {
        result.leftBoundary_ = leftBoundaryBuilder_.build();
      }
      if (rightBoundaryBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          rightBoundary_ = java.util.Collections.unmodifiableList(rightBoundary_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.rightBoundary_ = rightBoundary_;
      } else {
        result.rightBoundary_ = rightBoundaryBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.LaneEx) {
        return mergeFrom((road.data.proto.LaneEx)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.LaneEx other) {
      if (other == road.data.proto.LaneEx.getDefaultInstance()) return this;
      if (other.getLaneRefId() != 0) {
        setLaneRefId(other.getLaneRefId());
      }
      if (other.getLaneWidth() != 0) {
        setLaneWidth(other.getLaneWidth());
      }
      if (other.hasLaneAttributes()) {
        mergeLaneAttributes(other.getLaneAttributes());
      }
      if (other.hasManeuvers()) {
        mergeManeuvers(other.getManeuvers());
      }
      if (connectsToExBuilder_ == null) {
        if (!other.connectsToEx_.isEmpty()) {
          if (connectsToEx_.isEmpty()) {
            connectsToEx_ = other.connectsToEx_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureConnectsToExIsMutable();
            connectsToEx_.addAll(other.connectsToEx_);
          }
          onChanged();
        }
      } else {
        if (!other.connectsToEx_.isEmpty()) {
          if (connectsToExBuilder_.isEmpty()) {
            connectsToExBuilder_.dispose();
            connectsToExBuilder_ = null;
            connectsToEx_ = other.connectsToEx_;
            bitField0_ = (bitField0_ & ~0x00000001);
            connectsToExBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getConnectsToExFieldBuilder() : null;
          } else {
            connectsToExBuilder_.addAllMessages(other.connectsToEx_);
          }
        }
      }
      if (speedLimitsBuilder_ == null) {
        if (!other.speedLimits_.isEmpty()) {
          if (speedLimits_.isEmpty()) {
            speedLimits_ = other.speedLimits_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureSpeedLimitsIsMutable();
            speedLimits_.addAll(other.speedLimits_);
          }
          onChanged();
        }
      } else {
        if (!other.speedLimits_.isEmpty()) {
          if (speedLimitsBuilder_.isEmpty()) {
            speedLimitsBuilder_.dispose();
            speedLimitsBuilder_ = null;
            speedLimits_ = other.speedLimits_;
            bitField0_ = (bitField0_ & ~0x00000002);
            speedLimitsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getSpeedLimitsFieldBuilder() : null;
          } else {
            speedLimitsBuilder_.addAllMessages(other.speedLimits_);
          }
        }
      }
      if (stPointsBuilder_ == null) {
        if (!other.stPoints_.isEmpty()) {
          if (stPoints_.isEmpty()) {
            stPoints_ = other.stPoints_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureStPointsIsMutable();
            stPoints_.addAll(other.stPoints_);
          }
          onChanged();
        }
      } else {
        if (!other.stPoints_.isEmpty()) {
          if (stPointsBuilder_.isEmpty()) {
            stPointsBuilder_.dispose();
            stPointsBuilder_ = null;
            stPoints_ = other.stPoints_;
            bitField0_ = (bitField0_ & ~0x00000004);
            stPointsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getStPointsFieldBuilder() : null;
          } else {
            stPointsBuilder_.addAllMessages(other.stPoints_);
          }
        }
      }
      if (leftBoundaryBuilder_ == null) {
        if (!other.leftBoundary_.isEmpty()) {
          if (leftBoundary_.isEmpty()) {
            leftBoundary_ = other.leftBoundary_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureLeftBoundaryIsMutable();
            leftBoundary_.addAll(other.leftBoundary_);
          }
          onChanged();
        }
      } else {
        if (!other.leftBoundary_.isEmpty()) {
          if (leftBoundaryBuilder_.isEmpty()) {
            leftBoundaryBuilder_.dispose();
            leftBoundaryBuilder_ = null;
            leftBoundary_ = other.leftBoundary_;
            bitField0_ = (bitField0_ & ~0x00000008);
            leftBoundaryBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getLeftBoundaryFieldBuilder() : null;
          } else {
            leftBoundaryBuilder_.addAllMessages(other.leftBoundary_);
          }
        }
      }
      if (rightBoundaryBuilder_ == null) {
        if (!other.rightBoundary_.isEmpty()) {
          if (rightBoundary_.isEmpty()) {
            rightBoundary_ = other.rightBoundary_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureRightBoundaryIsMutable();
            rightBoundary_.addAll(other.rightBoundary_);
          }
          onChanged();
        }
      } else {
        if (!other.rightBoundary_.isEmpty()) {
          if (rightBoundaryBuilder_.isEmpty()) {
            rightBoundaryBuilder_.dispose();
            rightBoundaryBuilder_ = null;
            rightBoundary_ = other.rightBoundary_;
            bitField0_ = (bitField0_ & ~0x00000010);
            rightBoundaryBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getRightBoundaryFieldBuilder() : null;
          } else {
            rightBoundaryBuilder_.addAllMessages(other.rightBoundary_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.LaneEx parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.LaneEx) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int laneRefId_ ;
    /**
     * <pre>
     *关联车道标识ID
     * </pre>
     *
     * <code>int32 laneRefId = 1;</code>
     */
    public int getLaneRefId() {
      return laneRefId_;
    }
    /**
     * <pre>
     *关联车道标识ID
     * </pre>
     *
     * <code>int32 laneRefId = 1;</code>
     */
    public Builder setLaneRefId(int value) {
      
      laneRefId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *关联车道标识ID
     * </pre>
     *
     * <code>int32 laneRefId = 1;</code>
     */
    public Builder clearLaneRefId() {
      
      laneRefId_ = 0;
      onChanged();
      return this;
    }

    private int laneWidth_ ;
    /**
     * <pre>
     *可选，车道宽度，单位：1cm
     * </pre>
     *
     * <code>uint32 laneWidth = 2;</code>
     */
    public int getLaneWidth() {
      return laneWidth_;
    }
    /**
     * <pre>
     *可选，车道宽度，单位：1cm
     * </pre>
     *
     * <code>uint32 laneWidth = 2;</code>
     */
    public Builder setLaneWidth(int value) {
      
      laneWidth_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车道宽度，单位：1cm
     * </pre>
     *
     * <code>uint32 laneWidth = 2;</code>
     */
    public Builder clearLaneWidth() {
      
      laneWidth_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.LaneAttributes laneAttributes_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributes, road.data.proto.LaneAttributes.Builder, road.data.proto.LaneAttributesOrBuilder> laneAttributesBuilder_;
    /**
     * <pre>
     *可选，车道属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
     */
    public boolean hasLaneAttributes() {
      return laneAttributesBuilder_ != null || laneAttributes_ != null;
    }
    /**
     * <pre>
     *可选，车道属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
     */
    public road.data.proto.LaneAttributes getLaneAttributes() {
      if (laneAttributesBuilder_ == null) {
        return laneAttributes_ == null ? road.data.proto.LaneAttributes.getDefaultInstance() : laneAttributes_;
      } else {
        return laneAttributesBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，车道属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
     */
    public Builder setLaneAttributes(road.data.proto.LaneAttributes value) {
      if (laneAttributesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        laneAttributes_ = value;
        onChanged();
      } else {
        laneAttributesBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，车道属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
     */
    public Builder setLaneAttributes(
        road.data.proto.LaneAttributes.Builder builderForValue) {
      if (laneAttributesBuilder_ == null) {
        laneAttributes_ = builderForValue.build();
        onChanged();
      } else {
        laneAttributesBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，车道属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
     */
    public Builder mergeLaneAttributes(road.data.proto.LaneAttributes value) {
      if (laneAttributesBuilder_ == null) {
        if (laneAttributes_ != null) {
          laneAttributes_ =
            road.data.proto.LaneAttributes.newBuilder(laneAttributes_).mergeFrom(value).buildPartial();
        } else {
          laneAttributes_ = value;
        }
        onChanged();
      } else {
        laneAttributesBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，车道属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
     */
    public Builder clearLaneAttributes() {
      if (laneAttributesBuilder_ == null) {
        laneAttributes_ = null;
        onChanged();
      } else {
        laneAttributes_ = null;
        laneAttributesBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，车道属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
     */
    public road.data.proto.LaneAttributes.Builder getLaneAttributesBuilder() {
      
      onChanged();
      return getLaneAttributesFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，车道属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
     */
    public road.data.proto.LaneAttributesOrBuilder getLaneAttributesOrBuilder() {
      if (laneAttributesBuilder_ != null) {
        return laneAttributesBuilder_.getMessageOrBuilder();
      } else {
        return laneAttributes_ == null ?
            road.data.proto.LaneAttributes.getDefaultInstance() : laneAttributes_;
      }
    }
    /**
     * <pre>
     *可选，车道属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributes, road.data.proto.LaneAttributes.Builder, road.data.proto.LaneAttributesOrBuilder> 
        getLaneAttributesFieldBuilder() {
      if (laneAttributesBuilder_ == null) {
        laneAttributesBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneAttributes, road.data.proto.LaneAttributes.Builder, road.data.proto.LaneAttributesOrBuilder>(
                getLaneAttributes(),
                getParentForChildren(),
                isClean());
        laneAttributes_ = null;
      }
      return laneAttributesBuilder_;
    }

    private road.data.proto.AllowedManeuvers maneuvers_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AllowedManeuvers, road.data.proto.AllowedManeuvers.Builder, road.data.proto.AllowedManeuversOrBuilder> maneuversBuilder_;
    /**
     * <pre>
     *可选，车道出口的允许转向行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
     */
    public boolean hasManeuvers() {
      return maneuversBuilder_ != null || maneuvers_ != null;
    }
    /**
     * <pre>
     *可选，车道出口的允许转向行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
     */
    public road.data.proto.AllowedManeuvers getManeuvers() {
      if (maneuversBuilder_ == null) {
        return maneuvers_ == null ? road.data.proto.AllowedManeuvers.getDefaultInstance() : maneuvers_;
      } else {
        return maneuversBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，车道出口的允许转向行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
     */
    public Builder setManeuvers(road.data.proto.AllowedManeuvers value) {
      if (maneuversBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        maneuvers_ = value;
        onChanged();
      } else {
        maneuversBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，车道出口的允许转向行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
     */
    public Builder setManeuvers(
        road.data.proto.AllowedManeuvers.Builder builderForValue) {
      if (maneuversBuilder_ == null) {
        maneuvers_ = builderForValue.build();
        onChanged();
      } else {
        maneuversBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，车道出口的允许转向行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
     */
    public Builder mergeManeuvers(road.data.proto.AllowedManeuvers value) {
      if (maneuversBuilder_ == null) {
        if (maneuvers_ != null) {
          maneuvers_ =
            road.data.proto.AllowedManeuvers.newBuilder(maneuvers_).mergeFrom(value).buildPartial();
        } else {
          maneuvers_ = value;
        }
        onChanged();
      } else {
        maneuversBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，车道出口的允许转向行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
     */
    public Builder clearManeuvers() {
      if (maneuversBuilder_ == null) {
        maneuvers_ = null;
        onChanged();
      } else {
        maneuvers_ = null;
        maneuversBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，车道出口的允许转向行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
     */
    public road.data.proto.AllowedManeuvers.Builder getManeuversBuilder() {
      
      onChanged();
      return getManeuversFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，车道出口的允许转向行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
     */
    public road.data.proto.AllowedManeuversOrBuilder getManeuversOrBuilder() {
      if (maneuversBuilder_ != null) {
        return maneuversBuilder_.getMessageOrBuilder();
      } else {
        return maneuvers_ == null ?
            road.data.proto.AllowedManeuvers.getDefaultInstance() : maneuvers_;
      }
    }
    /**
     * <pre>
     *可选，车道出口的允许转向行为
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AllowedManeuvers, road.data.proto.AllowedManeuvers.Builder, road.data.proto.AllowedManeuversOrBuilder> 
        getManeuversFieldBuilder() {
      if (maneuversBuilder_ == null) {
        maneuversBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.AllowedManeuvers, road.data.proto.AllowedManeuvers.Builder, road.data.proto.AllowedManeuversOrBuilder>(
                getManeuvers(),
                getParentForChildren(),
                isClean());
        maneuvers_ = null;
      }
      return maneuversBuilder_;
    }

    private java.util.List<road.data.proto.ConnectionEx> connectsToEx_ =
      java.util.Collections.emptyList();
    private void ensureConnectsToExIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        connectsToEx_ = new java.util.ArrayList<road.data.proto.ConnectionEx>(connectsToEx_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ConnectionEx, road.data.proto.ConnectionEx.Builder, road.data.proto.ConnectionExOrBuilder> connectsToExBuilder_;

    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public java.util.List<road.data.proto.ConnectionEx> getConnectsToExList() {
      if (connectsToExBuilder_ == null) {
        return java.util.Collections.unmodifiableList(connectsToEx_);
      } else {
        return connectsToExBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public int getConnectsToExCount() {
      if (connectsToExBuilder_ == null) {
        return connectsToEx_.size();
      } else {
        return connectsToExBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public road.data.proto.ConnectionEx getConnectsToEx(int index) {
      if (connectsToExBuilder_ == null) {
        return connectsToEx_.get(index);
      } else {
        return connectsToExBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public Builder setConnectsToEx(
        int index, road.data.proto.ConnectionEx value) {
      if (connectsToExBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConnectsToExIsMutable();
        connectsToEx_.set(index, value);
        onChanged();
      } else {
        connectsToExBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public Builder setConnectsToEx(
        int index, road.data.proto.ConnectionEx.Builder builderForValue) {
      if (connectsToExBuilder_ == null) {
        ensureConnectsToExIsMutable();
        connectsToEx_.set(index, builderForValue.build());
        onChanged();
      } else {
        connectsToExBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public Builder addConnectsToEx(road.data.proto.ConnectionEx value) {
      if (connectsToExBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConnectsToExIsMutable();
        connectsToEx_.add(value);
        onChanged();
      } else {
        connectsToExBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public Builder addConnectsToEx(
        int index, road.data.proto.ConnectionEx value) {
      if (connectsToExBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureConnectsToExIsMutable();
        connectsToEx_.add(index, value);
        onChanged();
      } else {
        connectsToExBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public Builder addConnectsToEx(
        road.data.proto.ConnectionEx.Builder builderForValue) {
      if (connectsToExBuilder_ == null) {
        ensureConnectsToExIsMutable();
        connectsToEx_.add(builderForValue.build());
        onChanged();
      } else {
        connectsToExBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public Builder addConnectsToEx(
        int index, road.data.proto.ConnectionEx.Builder builderForValue) {
      if (connectsToExBuilder_ == null) {
        ensureConnectsToExIsMutable();
        connectsToEx_.add(index, builderForValue.build());
        onChanged();
      } else {
        connectsToExBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public Builder addAllConnectsToEx(
        java.lang.Iterable<? extends road.data.proto.ConnectionEx> values) {
      if (connectsToExBuilder_ == null) {
        ensureConnectsToExIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, connectsToEx_);
        onChanged();
      } else {
        connectsToExBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public Builder clearConnectsToEx() {
      if (connectsToExBuilder_ == null) {
        connectsToEx_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        connectsToExBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public Builder removeConnectsToEx(int index) {
      if (connectsToExBuilder_ == null) {
        ensureConnectsToExIsMutable();
        connectsToEx_.remove(index);
        onChanged();
      } else {
        connectsToExBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public road.data.proto.ConnectionEx.Builder getConnectsToExBuilder(
        int index) {
      return getConnectsToExFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public road.data.proto.ConnectionExOrBuilder getConnectsToExOrBuilder(
        int index) {
      if (connectsToExBuilder_ == null) {
        return connectsToEx_.get(index);  } else {
        return connectsToExBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public java.util.List<? extends road.data.proto.ConnectionExOrBuilder> 
         getConnectsToExOrBuilderList() {
      if (connectsToExBuilder_ != null) {
        return connectsToExBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(connectsToEx_);
      }
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public road.data.proto.ConnectionEx.Builder addConnectsToExBuilder() {
      return getConnectsToExFieldBuilder().addBuilder(
          road.data.proto.ConnectionEx.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public road.data.proto.ConnectionEx.Builder addConnectsToExBuilder(
        int index) {
      return getConnectsToExFieldBuilder().addBuilder(
          index, road.data.proto.ConnectionEx.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，车道与下游路段车道的连接关系扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
     */
    public java.util.List<road.data.proto.ConnectionEx.Builder> 
         getConnectsToExBuilderList() {
      return getConnectsToExFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ConnectionEx, road.data.proto.ConnectionEx.Builder, road.data.proto.ConnectionExOrBuilder> 
        getConnectsToExFieldBuilder() {
      if (connectsToExBuilder_ == null) {
        connectsToExBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.ConnectionEx, road.data.proto.ConnectionEx.Builder, road.data.proto.ConnectionExOrBuilder>(
                connectsToEx_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        connectsToEx_ = null;
      }
      return connectsToExBuilder_;
    }

    private java.util.List<road.data.proto.RegulatorySpeedLimit> speedLimits_ =
      java.util.Collections.emptyList();
    private void ensureSpeedLimitsIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        speedLimits_ = new java.util.ArrayList<road.data.proto.RegulatorySpeedLimit>(speedLimits_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RegulatorySpeedLimit, road.data.proto.RegulatorySpeedLimit.Builder, road.data.proto.RegulatorySpeedLimitOrBuilder> speedLimitsBuilder_;

    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public java.util.List<road.data.proto.RegulatorySpeedLimit> getSpeedLimitsList() {
      if (speedLimitsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(speedLimits_);
      } else {
        return speedLimitsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public int getSpeedLimitsCount() {
      if (speedLimitsBuilder_ == null) {
        return speedLimits_.size();
      } else {
        return speedLimitsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public road.data.proto.RegulatorySpeedLimit getSpeedLimits(int index) {
      if (speedLimitsBuilder_ == null) {
        return speedLimits_.get(index);
      } else {
        return speedLimitsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public Builder setSpeedLimits(
        int index, road.data.proto.RegulatorySpeedLimit value) {
      if (speedLimitsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSpeedLimitsIsMutable();
        speedLimits_.set(index, value);
        onChanged();
      } else {
        speedLimitsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public Builder setSpeedLimits(
        int index, road.data.proto.RegulatorySpeedLimit.Builder builderForValue) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        speedLimits_.set(index, builderForValue.build());
        onChanged();
      } else {
        speedLimitsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public Builder addSpeedLimits(road.data.proto.RegulatorySpeedLimit value) {
      if (speedLimitsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSpeedLimitsIsMutable();
        speedLimits_.add(value);
        onChanged();
      } else {
        speedLimitsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public Builder addSpeedLimits(
        int index, road.data.proto.RegulatorySpeedLimit value) {
      if (speedLimitsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSpeedLimitsIsMutable();
        speedLimits_.add(index, value);
        onChanged();
      } else {
        speedLimitsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public Builder addSpeedLimits(
        road.data.proto.RegulatorySpeedLimit.Builder builderForValue) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        speedLimits_.add(builderForValue.build());
        onChanged();
      } else {
        speedLimitsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public Builder addSpeedLimits(
        int index, road.data.proto.RegulatorySpeedLimit.Builder builderForValue) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        speedLimits_.add(index, builderForValue.build());
        onChanged();
      } else {
        speedLimitsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public Builder addAllSpeedLimits(
        java.lang.Iterable<? extends road.data.proto.RegulatorySpeedLimit> values) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, speedLimits_);
        onChanged();
      } else {
        speedLimitsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public Builder clearSpeedLimits() {
      if (speedLimitsBuilder_ == null) {
        speedLimits_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        speedLimitsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public Builder removeSpeedLimits(int index) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        speedLimits_.remove(index);
        onChanged();
      } else {
        speedLimitsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public road.data.proto.RegulatorySpeedLimit.Builder getSpeedLimitsBuilder(
        int index) {
      return getSpeedLimitsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public road.data.proto.RegulatorySpeedLimitOrBuilder getSpeedLimitsOrBuilder(
        int index) {
      if (speedLimitsBuilder_ == null) {
        return speedLimits_.get(index);  } else {
        return speedLimitsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public java.util.List<? extends road.data.proto.RegulatorySpeedLimitOrBuilder> 
         getSpeedLimitsOrBuilderList() {
      if (speedLimitsBuilder_ != null) {
        return speedLimitsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(speedLimits_);
      }
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public road.data.proto.RegulatorySpeedLimit.Builder addSpeedLimitsBuilder() {
      return getSpeedLimitsFieldBuilder().addBuilder(
          road.data.proto.RegulatorySpeedLimit.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public road.data.proto.RegulatorySpeedLimit.Builder addSpeedLimitsBuilder(
        int index) {
      return getSpeedLimitsFieldBuilder().addBuilder(
          index, road.data.proto.RegulatorySpeedLimit.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，限速
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
     */
    public java.util.List<road.data.proto.RegulatorySpeedLimit.Builder> 
         getSpeedLimitsBuilderList() {
      return getSpeedLimitsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RegulatorySpeedLimit, road.data.proto.RegulatorySpeedLimit.Builder, road.data.proto.RegulatorySpeedLimitOrBuilder> 
        getSpeedLimitsFieldBuilder() {
      if (speedLimitsBuilder_ == null) {
        speedLimitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.RegulatorySpeedLimit, road.data.proto.RegulatorySpeedLimit.Builder, road.data.proto.RegulatorySpeedLimitOrBuilder>(
                speedLimits_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        speedLimits_ = null;
      }
      return speedLimitsBuilder_;
    }

    private java.util.List<road.data.proto.STPoint> stPoints_ =
      java.util.Collections.emptyList();
    private void ensureStPointsIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        stPoints_ = new java.util.ArrayList<road.data.proto.STPoint>(stPoints_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.STPoint, road.data.proto.STPoint.Builder, road.data.proto.STPointOrBuilder> stPointsBuilder_;

    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public java.util.List<road.data.proto.STPoint> getStPointsList() {
      if (stPointsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(stPoints_);
      } else {
        return stPointsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public int getStPointsCount() {
      if (stPointsBuilder_ == null) {
        return stPoints_.size();
      } else {
        return stPointsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public road.data.proto.STPoint getStPoints(int index) {
      if (stPointsBuilder_ == null) {
        return stPoints_.get(index);
      } else {
        return stPointsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public Builder setStPoints(
        int index, road.data.proto.STPoint value) {
      if (stPointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureStPointsIsMutable();
        stPoints_.set(index, value);
        onChanged();
      } else {
        stPointsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public Builder setStPoints(
        int index, road.data.proto.STPoint.Builder builderForValue) {
      if (stPointsBuilder_ == null) {
        ensureStPointsIsMutable();
        stPoints_.set(index, builderForValue.build());
        onChanged();
      } else {
        stPointsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public Builder addStPoints(road.data.proto.STPoint value) {
      if (stPointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureStPointsIsMutable();
        stPoints_.add(value);
        onChanged();
      } else {
        stPointsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public Builder addStPoints(
        int index, road.data.proto.STPoint value) {
      if (stPointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureStPointsIsMutable();
        stPoints_.add(index, value);
        onChanged();
      } else {
        stPointsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public Builder addStPoints(
        road.data.proto.STPoint.Builder builderForValue) {
      if (stPointsBuilder_ == null) {
        ensureStPointsIsMutable();
        stPoints_.add(builderForValue.build());
        onChanged();
      } else {
        stPointsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public Builder addStPoints(
        int index, road.data.proto.STPoint.Builder builderForValue) {
      if (stPointsBuilder_ == null) {
        ensureStPointsIsMutable();
        stPoints_.add(index, builderForValue.build());
        onChanged();
      } else {
        stPointsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public Builder addAllStPoints(
        java.lang.Iterable<? extends road.data.proto.STPoint> values) {
      if (stPointsBuilder_ == null) {
        ensureStPointsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, stPoints_);
        onChanged();
      } else {
        stPointsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public Builder clearStPoints() {
      if (stPointsBuilder_ == null) {
        stPoints_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        stPointsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public Builder removeStPoints(int index) {
      if (stPointsBuilder_ == null) {
        ensureStPointsIsMutable();
        stPoints_.remove(index);
        onChanged();
      } else {
        stPointsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public road.data.proto.STPoint.Builder getStPointsBuilder(
        int index) {
      return getStPointsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public road.data.proto.STPointOrBuilder getStPointsOrBuilder(
        int index) {
      if (stPointsBuilder_ == null) {
        return stPoints_.get(index);  } else {
        return stPointsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public java.util.List<? extends road.data.proto.STPointOrBuilder> 
         getStPointsOrBuilderList() {
      if (stPointsBuilder_ != null) {
        return stPointsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(stPoints_);
      }
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public road.data.proto.STPoint.Builder addStPointsBuilder() {
      return getStPointsFieldBuilder().addBuilder(
          road.data.proto.STPoint.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public road.data.proto.STPoint.Builder addStPointsBuilder(
        int index) {
      return getStPointsFieldBuilder().addBuilder(
          index, road.data.proto.STPoint.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，ST坐标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
     */
    public java.util.List<road.data.proto.STPoint.Builder> 
         getStPointsBuilderList() {
      return getStPointsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.STPoint, road.data.proto.STPoint.Builder, road.data.proto.STPointOrBuilder> 
        getStPointsFieldBuilder() {
      if (stPointsBuilder_ == null) {
        stPointsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.STPoint, road.data.proto.STPoint.Builder, road.data.proto.STPointOrBuilder>(
                stPoints_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        stPoints_ = null;
      }
      return stPointsBuilder_;
    }

    private java.util.List<road.data.proto.LaneBoundary> leftBoundary_ =
      java.util.Collections.emptyList();
    private void ensureLeftBoundaryIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        leftBoundary_ = new java.util.ArrayList<road.data.proto.LaneBoundary>(leftBoundary_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.LaneBoundary, road.data.proto.LaneBoundary.Builder, road.data.proto.LaneBoundaryOrBuilder> leftBoundaryBuilder_;

    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public java.util.List<road.data.proto.LaneBoundary> getLeftBoundaryList() {
      if (leftBoundaryBuilder_ == null) {
        return java.util.Collections.unmodifiableList(leftBoundary_);
      } else {
        return leftBoundaryBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public int getLeftBoundaryCount() {
      if (leftBoundaryBuilder_ == null) {
        return leftBoundary_.size();
      } else {
        return leftBoundaryBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public road.data.proto.LaneBoundary getLeftBoundary(int index) {
      if (leftBoundaryBuilder_ == null) {
        return leftBoundary_.get(index);
      } else {
        return leftBoundaryBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public Builder setLeftBoundary(
        int index, road.data.proto.LaneBoundary value) {
      if (leftBoundaryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLeftBoundaryIsMutable();
        leftBoundary_.set(index, value);
        onChanged();
      } else {
        leftBoundaryBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public Builder setLeftBoundary(
        int index, road.data.proto.LaneBoundary.Builder builderForValue) {
      if (leftBoundaryBuilder_ == null) {
        ensureLeftBoundaryIsMutable();
        leftBoundary_.set(index, builderForValue.build());
        onChanged();
      } else {
        leftBoundaryBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public Builder addLeftBoundary(road.data.proto.LaneBoundary value) {
      if (leftBoundaryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLeftBoundaryIsMutable();
        leftBoundary_.add(value);
        onChanged();
      } else {
        leftBoundaryBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public Builder addLeftBoundary(
        int index, road.data.proto.LaneBoundary value) {
      if (leftBoundaryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLeftBoundaryIsMutable();
        leftBoundary_.add(index, value);
        onChanged();
      } else {
        leftBoundaryBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public Builder addLeftBoundary(
        road.data.proto.LaneBoundary.Builder builderForValue) {
      if (leftBoundaryBuilder_ == null) {
        ensureLeftBoundaryIsMutable();
        leftBoundary_.add(builderForValue.build());
        onChanged();
      } else {
        leftBoundaryBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public Builder addLeftBoundary(
        int index, road.data.proto.LaneBoundary.Builder builderForValue) {
      if (leftBoundaryBuilder_ == null) {
        ensureLeftBoundaryIsMutable();
        leftBoundary_.add(index, builderForValue.build());
        onChanged();
      } else {
        leftBoundaryBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public Builder addAllLeftBoundary(
        java.lang.Iterable<? extends road.data.proto.LaneBoundary> values) {
      if (leftBoundaryBuilder_ == null) {
        ensureLeftBoundaryIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, leftBoundary_);
        onChanged();
      } else {
        leftBoundaryBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public Builder clearLeftBoundary() {
      if (leftBoundaryBuilder_ == null) {
        leftBoundary_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        leftBoundaryBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public Builder removeLeftBoundary(int index) {
      if (leftBoundaryBuilder_ == null) {
        ensureLeftBoundaryIsMutable();
        leftBoundary_.remove(index);
        onChanged();
      } else {
        leftBoundaryBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public road.data.proto.LaneBoundary.Builder getLeftBoundaryBuilder(
        int index) {
      return getLeftBoundaryFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public road.data.proto.LaneBoundaryOrBuilder getLeftBoundaryOrBuilder(
        int index) {
      if (leftBoundaryBuilder_ == null) {
        return leftBoundary_.get(index);  } else {
        return leftBoundaryBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public java.util.List<? extends road.data.proto.LaneBoundaryOrBuilder> 
         getLeftBoundaryOrBuilderList() {
      if (leftBoundaryBuilder_ != null) {
        return leftBoundaryBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(leftBoundary_);
      }
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public road.data.proto.LaneBoundary.Builder addLeftBoundaryBuilder() {
      return getLeftBoundaryFieldBuilder().addBuilder(
          road.data.proto.LaneBoundary.getDefaultInstance());
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public road.data.proto.LaneBoundary.Builder addLeftBoundaryBuilder(
        int index) {
      return getLeftBoundaryFieldBuilder().addBuilder(
          index, road.data.proto.LaneBoundary.getDefaultInstance());
    }
    /**
     * <pre>
     *车道左边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
     */
    public java.util.List<road.data.proto.LaneBoundary.Builder> 
         getLeftBoundaryBuilderList() {
      return getLeftBoundaryFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.LaneBoundary, road.data.proto.LaneBoundary.Builder, road.data.proto.LaneBoundaryOrBuilder> 
        getLeftBoundaryFieldBuilder() {
      if (leftBoundaryBuilder_ == null) {
        leftBoundaryBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.LaneBoundary, road.data.proto.LaneBoundary.Builder, road.data.proto.LaneBoundaryOrBuilder>(
                leftBoundary_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        leftBoundary_ = null;
      }
      return leftBoundaryBuilder_;
    }

    private java.util.List<road.data.proto.LaneBoundary> rightBoundary_ =
      java.util.Collections.emptyList();
    private void ensureRightBoundaryIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        rightBoundary_ = new java.util.ArrayList<road.data.proto.LaneBoundary>(rightBoundary_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.LaneBoundary, road.data.proto.LaneBoundary.Builder, road.data.proto.LaneBoundaryOrBuilder> rightBoundaryBuilder_;

    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public java.util.List<road.data.proto.LaneBoundary> getRightBoundaryList() {
      if (rightBoundaryBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rightBoundary_);
      } else {
        return rightBoundaryBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public int getRightBoundaryCount() {
      if (rightBoundaryBuilder_ == null) {
        return rightBoundary_.size();
      } else {
        return rightBoundaryBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public road.data.proto.LaneBoundary getRightBoundary(int index) {
      if (rightBoundaryBuilder_ == null) {
        return rightBoundary_.get(index);
      } else {
        return rightBoundaryBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public Builder setRightBoundary(
        int index, road.data.proto.LaneBoundary value) {
      if (rightBoundaryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRightBoundaryIsMutable();
        rightBoundary_.set(index, value);
        onChanged();
      } else {
        rightBoundaryBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public Builder setRightBoundary(
        int index, road.data.proto.LaneBoundary.Builder builderForValue) {
      if (rightBoundaryBuilder_ == null) {
        ensureRightBoundaryIsMutable();
        rightBoundary_.set(index, builderForValue.build());
        onChanged();
      } else {
        rightBoundaryBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public Builder addRightBoundary(road.data.proto.LaneBoundary value) {
      if (rightBoundaryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRightBoundaryIsMutable();
        rightBoundary_.add(value);
        onChanged();
      } else {
        rightBoundaryBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public Builder addRightBoundary(
        int index, road.data.proto.LaneBoundary value) {
      if (rightBoundaryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRightBoundaryIsMutable();
        rightBoundary_.add(index, value);
        onChanged();
      } else {
        rightBoundaryBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public Builder addRightBoundary(
        road.data.proto.LaneBoundary.Builder builderForValue) {
      if (rightBoundaryBuilder_ == null) {
        ensureRightBoundaryIsMutable();
        rightBoundary_.add(builderForValue.build());
        onChanged();
      } else {
        rightBoundaryBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public Builder addRightBoundary(
        int index, road.data.proto.LaneBoundary.Builder builderForValue) {
      if (rightBoundaryBuilder_ == null) {
        ensureRightBoundaryIsMutable();
        rightBoundary_.add(index, builderForValue.build());
        onChanged();
      } else {
        rightBoundaryBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public Builder addAllRightBoundary(
        java.lang.Iterable<? extends road.data.proto.LaneBoundary> values) {
      if (rightBoundaryBuilder_ == null) {
        ensureRightBoundaryIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rightBoundary_);
        onChanged();
      } else {
        rightBoundaryBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public Builder clearRightBoundary() {
      if (rightBoundaryBuilder_ == null) {
        rightBoundary_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        rightBoundaryBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public Builder removeRightBoundary(int index) {
      if (rightBoundaryBuilder_ == null) {
        ensureRightBoundaryIsMutable();
        rightBoundary_.remove(index);
        onChanged();
      } else {
        rightBoundaryBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public road.data.proto.LaneBoundary.Builder getRightBoundaryBuilder(
        int index) {
      return getRightBoundaryFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public road.data.proto.LaneBoundaryOrBuilder getRightBoundaryOrBuilder(
        int index) {
      if (rightBoundaryBuilder_ == null) {
        return rightBoundary_.get(index);  } else {
        return rightBoundaryBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public java.util.List<? extends road.data.proto.LaneBoundaryOrBuilder> 
         getRightBoundaryOrBuilderList() {
      if (rightBoundaryBuilder_ != null) {
        return rightBoundaryBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rightBoundary_);
      }
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public road.data.proto.LaneBoundary.Builder addRightBoundaryBuilder() {
      return getRightBoundaryFieldBuilder().addBuilder(
          road.data.proto.LaneBoundary.getDefaultInstance());
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public road.data.proto.LaneBoundary.Builder addRightBoundaryBuilder(
        int index) {
      return getRightBoundaryFieldBuilder().addBuilder(
          index, road.data.proto.LaneBoundary.getDefaultInstance());
    }
    /**
     * <pre>
     *车道右边界
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
     */
    public java.util.List<road.data.proto.LaneBoundary.Builder> 
         getRightBoundaryBuilderList() {
      return getRightBoundaryFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.LaneBoundary, road.data.proto.LaneBoundary.Builder, road.data.proto.LaneBoundaryOrBuilder> 
        getRightBoundaryFieldBuilder() {
      if (rightBoundaryBuilder_ == null) {
        rightBoundaryBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.LaneBoundary, road.data.proto.LaneBoundary.Builder, road.data.proto.LaneBoundaryOrBuilder>(
                rightBoundary_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        rightBoundary_ = null;
      }
      return rightBoundaryBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.LaneEx)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.LaneEx)
  private static final road.data.proto.LaneEx DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.LaneEx();
  }

  public static road.data.proto.LaneEx getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LaneEx>
      PARSER = new com.google.protobuf.AbstractParser<LaneEx>() {
    @java.lang.Override
    public LaneEx parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new LaneEx(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<LaneEx> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LaneEx> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.LaneEx getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

