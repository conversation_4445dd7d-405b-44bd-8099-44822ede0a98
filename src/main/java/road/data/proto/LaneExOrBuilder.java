// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneExOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneEx)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *关联车道标识ID
   * </pre>
   *
   * <code>int32 laneRefId = 1;</code>
   */
  int getLaneRefId();

  /**
   * <pre>
   *可选，车道宽度，单位：1cm
   * </pre>
   *
   * <code>uint32 laneWidth = 2;</code>
   */
  int getLaneWidth();

  /**
   * <pre>
   *可选，车道属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
   */
  boolean hasLaneAttributes();
  /**
   * <pre>
   *可选，车道属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
   */
  road.data.proto.LaneAttributes getLaneAttributes();
  /**
   * <pre>
   *可选，车道属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
   */
  road.data.proto.LaneAttributesOrBuilder getLaneAttributesOrBuilder();

  /**
   * <pre>
   *可选，车道出口的允许转向行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
   */
  boolean hasManeuvers();
  /**
   * <pre>
   *可选，车道出口的允许转向行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
   */
  road.data.proto.AllowedManeuvers getManeuvers();
  /**
   * <pre>
   *可选，车道出口的允许转向行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
   */
  road.data.proto.AllowedManeuversOrBuilder getManeuversOrBuilder();

  /**
   * <pre>
   *可选，车道与下游路段车道的连接关系扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
   */
  java.util.List<road.data.proto.ConnectionEx> 
      getConnectsToExList();
  /**
   * <pre>
   *可选，车道与下游路段车道的连接关系扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
   */
  road.data.proto.ConnectionEx getConnectsToEx(int index);
  /**
   * <pre>
   *可选，车道与下游路段车道的连接关系扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
   */
  int getConnectsToExCount();
  /**
   * <pre>
   *可选，车道与下游路段车道的连接关系扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
   */
  java.util.List<? extends road.data.proto.ConnectionExOrBuilder> 
      getConnectsToExOrBuilderList();
  /**
   * <pre>
   *可选，车道与下游路段车道的连接关系扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ConnectionEx connectsToEx = 5;</code>
   */
  road.data.proto.ConnectionExOrBuilder getConnectsToExOrBuilder(
      int index);

  /**
   * <pre>
   *可选，限速
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  java.util.List<road.data.proto.RegulatorySpeedLimit> 
      getSpeedLimitsList();
  /**
   * <pre>
   *可选，限速
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  road.data.proto.RegulatorySpeedLimit getSpeedLimits(int index);
  /**
   * <pre>
   *可选，限速
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  int getSpeedLimitsCount();
  /**
   * <pre>
   *可选，限速
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  java.util.List<? extends road.data.proto.RegulatorySpeedLimitOrBuilder> 
      getSpeedLimitsOrBuilderList();
  /**
   * <pre>
   *可选，限速
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  road.data.proto.RegulatorySpeedLimitOrBuilder getSpeedLimitsOrBuilder(
      int index);

  /**
   * <pre>
   *可选，ST坐标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
   */
  java.util.List<road.data.proto.STPoint> 
      getStPointsList();
  /**
   * <pre>
   *可选，ST坐标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
   */
  road.data.proto.STPoint getStPoints(int index);
  /**
   * <pre>
   *可选，ST坐标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
   */
  int getStPointsCount();
  /**
   * <pre>
   *可选，ST坐标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
   */
  java.util.List<? extends road.data.proto.STPointOrBuilder> 
      getStPointsOrBuilderList();
  /**
   * <pre>
   *可选，ST坐标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.STPoint stPoints = 7;</code>
   */
  road.data.proto.STPointOrBuilder getStPointsOrBuilder(
      int index);

  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  java.util.List<road.data.proto.LaneBoundary> 
      getLeftBoundaryList();
  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  road.data.proto.LaneBoundary getLeftBoundary(int index);
  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  int getLeftBoundaryCount();
  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  java.util.List<? extends road.data.proto.LaneBoundaryOrBuilder> 
      getLeftBoundaryOrBuilderList();
  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  road.data.proto.LaneBoundaryOrBuilder getLeftBoundaryOrBuilder(
      int index);

  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  java.util.List<road.data.proto.LaneBoundary> 
      getRightBoundaryList();
  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  road.data.proto.LaneBoundary getRightBoundary(int index);
  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  int getRightBoundaryCount();
  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  java.util.List<? extends road.data.proto.LaneBoundaryOrBuilder> 
      getRightBoundaryOrBuilderList();
  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  road.data.proto.LaneBoundaryOrBuilder getRightBoundaryOrBuilder(
      int index);
}
