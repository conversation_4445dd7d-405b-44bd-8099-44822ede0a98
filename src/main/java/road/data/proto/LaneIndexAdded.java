// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车道级指标  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.LaneIndexAdded}
 */
public  final class LaneIndexAdded extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.LaneIndexAdded)
    LaneIndexAddedOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LaneIndexAdded.newBuilder() to construct.
  private LaneIndexAdded(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LaneIndexAdded() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LaneIndexAdded();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private LaneIndexAdded(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 16: {

            laneCapacity_ = input.readUInt32();
            break;
          }
          case 24: {

            laneSaturation_ = input.readUInt32();
            break;
          }
          case 32: {

            laneSpaceOccupy_ = input.readUInt32();
            break;
          }
          case 40: {

            laneTimeOccupy_ = input.readUInt32();
            break;
          }
          case 48: {

            laneAvgGrnQueue_ = input.readUInt32();
            break;
          }
          case 56: {

            laneGrnUtilization_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneIndexAdded_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneIndexAdded_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.LaneIndexAdded.class, road.data.proto.LaneIndexAdded.Builder.class);
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 1;
  private long timestamp_;
  /**
   * <pre>
   *数据时间 Unix timestamp数据时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 1;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int LANECAPACITY_FIELD_NUMBER = 2;
  private int laneCapacity_;
  /**
   * <pre>
   *可选，通行能力，0.01pcu/h
   * </pre>
   *
   * <code>uint32 laneCapacity = 2;</code>
   */
  public int getLaneCapacity() {
    return laneCapacity_;
  }

  public static final int LANESATURATION_FIELD_NUMBER = 3;
  private int laneSaturation_;
  /**
   * <pre>
   *可选，平均饱和度，0.01%
   * </pre>
   *
   * <code>uint32 laneSaturation = 3;</code>
   */
  public int getLaneSaturation() {
    return laneSaturation_;
  }

  public static final int LANESPACEOCCUPY_FIELD_NUMBER = 4;
  private int laneSpaceOccupy_;
  /**
   * <pre>
   *可选，平均车道空间占有率，0.01%
   * </pre>
   *
   * <code>uint32 laneSpaceOccupy = 4;</code>
   */
  public int getLaneSpaceOccupy() {
    return laneSpaceOccupy_;
  }

  public static final int LANETIMEOCCUPY_FIELD_NUMBER = 5;
  private int laneTimeOccupy_;
  /**
   * <pre>
   *可选，平均车道时间占有率，0.01%
   * </pre>
   *
   * <code>uint32 laneTimeOccupy = 5;</code>
   */
  public int getLaneTimeOccupy() {
    return laneTimeOccupy_;
  }

  public static final int LANEAVGGRNQUEUE_FIELD_NUMBER = 6;
  private int laneAvgGrnQueue_;
  /**
   * <pre>
   *可选，绿初车辆平均排队长度，0.01m
   * </pre>
   *
   * <code>uint32 laneAvgGrnQueue = 6;</code>
   */
  public int getLaneAvgGrnQueue() {
    return laneAvgGrnQueue_;
  }

  public static final int LANEGRNUTILIZATION_FIELD_NUMBER = 7;
  private int laneGrnUtilization_;
  /**
   * <pre>
   *可选，时段内平均绿灯利用率，0.01%
   * </pre>
   *
   * <code>uint32 laneGrnUtilization = 7;</code>
   */
  public int getLaneGrnUtilization() {
    return laneGrnUtilization_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (timestamp_ != 0L) {
      output.writeUInt64(1, timestamp_);
    }
    if (laneCapacity_ != 0) {
      output.writeUInt32(2, laneCapacity_);
    }
    if (laneSaturation_ != 0) {
      output.writeUInt32(3, laneSaturation_);
    }
    if (laneSpaceOccupy_ != 0) {
      output.writeUInt32(4, laneSpaceOccupy_);
    }
    if (laneTimeOccupy_ != 0) {
      output.writeUInt32(5, laneTimeOccupy_);
    }
    if (laneAvgGrnQueue_ != 0) {
      output.writeUInt32(6, laneAvgGrnQueue_);
    }
    if (laneGrnUtilization_ != 0) {
      output.writeUInt32(7, laneGrnUtilization_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, timestamp_);
    }
    if (laneCapacity_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, laneCapacity_);
    }
    if (laneSaturation_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, laneSaturation_);
    }
    if (laneSpaceOccupy_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, laneSpaceOccupy_);
    }
    if (laneTimeOccupy_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(5, laneTimeOccupy_);
    }
    if (laneAvgGrnQueue_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(6, laneAvgGrnQueue_);
    }
    if (laneGrnUtilization_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(7, laneGrnUtilization_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.LaneIndexAdded)) {
      return super.equals(obj);
    }
    road.data.proto.LaneIndexAdded other = (road.data.proto.LaneIndexAdded) obj;

    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (getLaneCapacity()
        != other.getLaneCapacity()) return false;
    if (getLaneSaturation()
        != other.getLaneSaturation()) return false;
    if (getLaneSpaceOccupy()
        != other.getLaneSpaceOccupy()) return false;
    if (getLaneTimeOccupy()
        != other.getLaneTimeOccupy()) return false;
    if (getLaneAvgGrnQueue()
        != other.getLaneAvgGrnQueue()) return false;
    if (getLaneGrnUtilization()
        != other.getLaneGrnUtilization()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    hash = (37 * hash) + LANECAPACITY_FIELD_NUMBER;
    hash = (53 * hash) + getLaneCapacity();
    hash = (37 * hash) + LANESATURATION_FIELD_NUMBER;
    hash = (53 * hash) + getLaneSaturation();
    hash = (37 * hash) + LANESPACEOCCUPY_FIELD_NUMBER;
    hash = (53 * hash) + getLaneSpaceOccupy();
    hash = (37 * hash) + LANETIMEOCCUPY_FIELD_NUMBER;
    hash = (53 * hash) + getLaneTimeOccupy();
    hash = (37 * hash) + LANEAVGGRNQUEUE_FIELD_NUMBER;
    hash = (53 * hash) + getLaneAvgGrnQueue();
    hash = (37 * hash) + LANEGRNUTILIZATION_FIELD_NUMBER;
    hash = (53 * hash) + getLaneGrnUtilization();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.LaneIndexAdded parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneIndexAdded parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneIndexAdded parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneIndexAdded parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneIndexAdded parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneIndexAdded parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneIndexAdded parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneIndexAdded parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneIndexAdded parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneIndexAdded parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneIndexAdded parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneIndexAdded parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.LaneIndexAdded prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *车道级指标  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.LaneIndexAdded}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.LaneIndexAdded)
      road.data.proto.LaneIndexAddedOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneIndexAdded_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneIndexAdded_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.LaneIndexAdded.class, road.data.proto.LaneIndexAdded.Builder.class);
    }

    // Construct using road.data.proto.LaneIndexAdded.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      timestamp_ = 0L;

      laneCapacity_ = 0;

      laneSaturation_ = 0;

      laneSpaceOccupy_ = 0;

      laneTimeOccupy_ = 0;

      laneAvgGrnQueue_ = 0;

      laneGrnUtilization_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneIndexAdded_descriptor;
    }

    @java.lang.Override
    public road.data.proto.LaneIndexAdded getDefaultInstanceForType() {
      return road.data.proto.LaneIndexAdded.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.LaneIndexAdded build() {
      road.data.proto.LaneIndexAdded result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.LaneIndexAdded buildPartial() {
      road.data.proto.LaneIndexAdded result = new road.data.proto.LaneIndexAdded(this);
      result.timestamp_ = timestamp_;
      result.laneCapacity_ = laneCapacity_;
      result.laneSaturation_ = laneSaturation_;
      result.laneSpaceOccupy_ = laneSpaceOccupy_;
      result.laneTimeOccupy_ = laneTimeOccupy_;
      result.laneAvgGrnQueue_ = laneAvgGrnQueue_;
      result.laneGrnUtilization_ = laneGrnUtilization_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.LaneIndexAdded) {
        return mergeFrom((road.data.proto.LaneIndexAdded)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.LaneIndexAdded other) {
      if (other == road.data.proto.LaneIndexAdded.getDefaultInstance()) return this;
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (other.getLaneCapacity() != 0) {
        setLaneCapacity(other.getLaneCapacity());
      }
      if (other.getLaneSaturation() != 0) {
        setLaneSaturation(other.getLaneSaturation());
      }
      if (other.getLaneSpaceOccupy() != 0) {
        setLaneSpaceOccupy(other.getLaneSpaceOccupy());
      }
      if (other.getLaneTimeOccupy() != 0) {
        setLaneTimeOccupy(other.getLaneTimeOccupy());
      }
      if (other.getLaneAvgGrnQueue() != 0) {
        setLaneAvgGrnQueue(other.getLaneAvgGrnQueue());
      }
      if (other.getLaneGrnUtilization() != 0) {
        setLaneGrnUtilization(other.getLaneGrnUtilization());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.LaneIndexAdded parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.LaneIndexAdded) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     *数据时间 Unix timestamp数据时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     *数据时间 Unix timestamp数据时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *数据时间 Unix timestamp数据时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private int laneCapacity_ ;
    /**
     * <pre>
     *可选，通行能力，0.01pcu/h
     * </pre>
     *
     * <code>uint32 laneCapacity = 2;</code>
     */
    public int getLaneCapacity() {
      return laneCapacity_;
    }
    /**
     * <pre>
     *可选，通行能力，0.01pcu/h
     * </pre>
     *
     * <code>uint32 laneCapacity = 2;</code>
     */
    public Builder setLaneCapacity(int value) {
      
      laneCapacity_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，通行能力，0.01pcu/h
     * </pre>
     *
     * <code>uint32 laneCapacity = 2;</code>
     */
    public Builder clearLaneCapacity() {
      
      laneCapacity_ = 0;
      onChanged();
      return this;
    }

    private int laneSaturation_ ;
    /**
     * <pre>
     *可选，平均饱和度，0.01%
     * </pre>
     *
     * <code>uint32 laneSaturation = 3;</code>
     */
    public int getLaneSaturation() {
      return laneSaturation_;
    }
    /**
     * <pre>
     *可选，平均饱和度，0.01%
     * </pre>
     *
     * <code>uint32 laneSaturation = 3;</code>
     */
    public Builder setLaneSaturation(int value) {
      
      laneSaturation_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，平均饱和度，0.01%
     * </pre>
     *
     * <code>uint32 laneSaturation = 3;</code>
     */
    public Builder clearLaneSaturation() {
      
      laneSaturation_ = 0;
      onChanged();
      return this;
    }

    private int laneSpaceOccupy_ ;
    /**
     * <pre>
     *可选，平均车道空间占有率，0.01%
     * </pre>
     *
     * <code>uint32 laneSpaceOccupy = 4;</code>
     */
    public int getLaneSpaceOccupy() {
      return laneSpaceOccupy_;
    }
    /**
     * <pre>
     *可选，平均车道空间占有率，0.01%
     * </pre>
     *
     * <code>uint32 laneSpaceOccupy = 4;</code>
     */
    public Builder setLaneSpaceOccupy(int value) {
      
      laneSpaceOccupy_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，平均车道空间占有率，0.01%
     * </pre>
     *
     * <code>uint32 laneSpaceOccupy = 4;</code>
     */
    public Builder clearLaneSpaceOccupy() {
      
      laneSpaceOccupy_ = 0;
      onChanged();
      return this;
    }

    private int laneTimeOccupy_ ;
    /**
     * <pre>
     *可选，平均车道时间占有率，0.01%
     * </pre>
     *
     * <code>uint32 laneTimeOccupy = 5;</code>
     */
    public int getLaneTimeOccupy() {
      return laneTimeOccupy_;
    }
    /**
     * <pre>
     *可选，平均车道时间占有率，0.01%
     * </pre>
     *
     * <code>uint32 laneTimeOccupy = 5;</code>
     */
    public Builder setLaneTimeOccupy(int value) {
      
      laneTimeOccupy_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，平均车道时间占有率，0.01%
     * </pre>
     *
     * <code>uint32 laneTimeOccupy = 5;</code>
     */
    public Builder clearLaneTimeOccupy() {
      
      laneTimeOccupy_ = 0;
      onChanged();
      return this;
    }

    private int laneAvgGrnQueue_ ;
    /**
     * <pre>
     *可选，绿初车辆平均排队长度，0.01m
     * </pre>
     *
     * <code>uint32 laneAvgGrnQueue = 6;</code>
     */
    public int getLaneAvgGrnQueue() {
      return laneAvgGrnQueue_;
    }
    /**
     * <pre>
     *可选，绿初车辆平均排队长度，0.01m
     * </pre>
     *
     * <code>uint32 laneAvgGrnQueue = 6;</code>
     */
    public Builder setLaneAvgGrnQueue(int value) {
      
      laneAvgGrnQueue_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，绿初车辆平均排队长度，0.01m
     * </pre>
     *
     * <code>uint32 laneAvgGrnQueue = 6;</code>
     */
    public Builder clearLaneAvgGrnQueue() {
      
      laneAvgGrnQueue_ = 0;
      onChanged();
      return this;
    }

    private int laneGrnUtilization_ ;
    /**
     * <pre>
     *可选，时段内平均绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 laneGrnUtilization = 7;</code>
     */
    public int getLaneGrnUtilization() {
      return laneGrnUtilization_;
    }
    /**
     * <pre>
     *可选，时段内平均绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 laneGrnUtilization = 7;</code>
     */
    public Builder setLaneGrnUtilization(int value) {
      
      laneGrnUtilization_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，时段内平均绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 laneGrnUtilization = 7;</code>
     */
    public Builder clearLaneGrnUtilization() {
      
      laneGrnUtilization_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.LaneIndexAdded)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.LaneIndexAdded)
  private static final road.data.proto.LaneIndexAdded DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.LaneIndexAdded();
  }

  public static road.data.proto.LaneIndexAdded getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LaneIndexAdded>
      PARSER = new com.google.protobuf.AbstractParser<LaneIndexAdded>() {
    @java.lang.Override
    public LaneIndexAdded parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new LaneIndexAdded(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<LaneIndexAdded> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LaneIndexAdded> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.LaneIndexAdded getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

