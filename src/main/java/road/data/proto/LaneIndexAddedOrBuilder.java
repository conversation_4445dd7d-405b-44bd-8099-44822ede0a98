// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneIndexAddedOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneIndexAdded)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *数据时间 Unix timestamp数据时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 1;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   *可选，通行能力，0.01pcu/h
   * </pre>
   *
   * <code>uint32 laneCapacity = 2;</code>
   */
  int getLaneCapacity();

  /**
   * <pre>
   *可选，平均饱和度，0.01%
   * </pre>
   *
   * <code>uint32 laneSaturation = 3;</code>
   */
  int getLaneSaturation();

  /**
   * <pre>
   *可选，平均车道空间占有率，0.01%
   * </pre>
   *
   * <code>uint32 laneSpaceOccupy = 4;</code>
   */
  int getLaneSpaceOccupy();

  /**
   * <pre>
   *可选，平均车道时间占有率，0.01%
   * </pre>
   *
   * <code>uint32 laneTimeOccupy = 5;</code>
   */
  int getLaneTimeOccupy();

  /**
   * <pre>
   *可选，绿初车辆平均排队长度，0.01m
   * </pre>
   *
   * <code>uint32 laneAvgGrnQueue = 6;</code>
   */
  int getLaneAvgGrnQueue();

  /**
   * <pre>
   *可选，时段内平均绿灯利用率，0.01%
   * </pre>
   *
   * <code>uint32 laneGrnUtilization = 7;</code>
   */
  int getLaneGrnUtilization();
}
