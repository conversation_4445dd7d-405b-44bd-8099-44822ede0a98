// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.Lane)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *LaneId车道定义在每一条有向路段上，同一条有向路段上的每个车道，都拥有一个单独的ID。车道号，以该车道行驶方向为参考，自左向右从 1 开始编号
   * </pre>
   *
   * <code>uint32 laneId = 1;</code>
   */
  int getLaneId();

  /**
   * <pre>
   *车道宽度 单位： 1cm
   * </pre>
   *
   * <code>uint32 laneWidth = 2;</code>
   */
  int getLaneWidth();

  /**
   * <pre>
   * 共享属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
   */
  boolean hasLaneAttributes();
  /**
   * <pre>
   * 共享属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
   */
  road.data.proto.LaneAttributes getLaneAttributes();
  /**
   * <pre>
   * 共享属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributes laneAttributes = 3;</code>
   */
  road.data.proto.LaneAttributesOrBuilder getLaneAttributesOrBuilder();

  /**
   * <pre>
   * 定义一个（机动车）车道的允许转向行为，参考《YDT3709_2020 基于 LTE 的车联网无线通信技术 消息层技术要求》
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
   */
  boolean hasManeuvers();
  /**
   * <pre>
   * 定义一个（机动车）车道的允许转向行为，参考《YDT3709_2020 基于 LTE 的车联网无线通信技术 消息层技术要求》
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
   */
  road.data.proto.AllowedManeuvers getManeuvers();
  /**
   * <pre>
   * 定义一个（机动车）车道的允许转向行为，参考《YDT3709_2020 基于 LTE 的车联网无线通信技术 消息层技术要求》
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AllowedManeuvers maneuvers = 4;</code>
   */
  road.data.proto.AllowedManeuversOrBuilder getManeuversOrBuilder();

  /**
   * <pre>
   * 车道与下游路段车道的连接关系列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Connection connectsTo = 5;</code>
   */
  java.util.List<road.data.proto.Connection> 
      getConnectsToList();
  /**
   * <pre>
   * 车道与下游路段车道的连接关系列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Connection connectsTo = 5;</code>
   */
  road.data.proto.Connection getConnectsTo(int index);
  /**
   * <pre>
   * 车道与下游路段车道的连接关系列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Connection connectsTo = 5;</code>
   */
  int getConnectsToCount();
  /**
   * <pre>
   * 车道与下游路段车道的连接关系列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Connection connectsTo = 5;</code>
   */
  java.util.List<? extends road.data.proto.ConnectionOrBuilder> 
      getConnectsToOrBuilderList();
  /**
   * <pre>
   * 车道与下游路段车道的连接关系列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Connection connectsTo = 5;</code>
   */
  road.data.proto.ConnectionOrBuilder getConnectsToOrBuilder(
      int index);

  /**
   * <pre>
   * 车道限速列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  java.util.List<road.data.proto.RegulatorySpeedLimit> 
      getSpeedLimitsList();
  /**
   * <pre>
   * 车道限速列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  road.data.proto.RegulatorySpeedLimit getSpeedLimits(int index);
  /**
   * <pre>
   * 车道限速列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  int getSpeedLimitsCount();
  /**
   * <pre>
   * 车道限速列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  java.util.List<? extends road.data.proto.RegulatorySpeedLimitOrBuilder> 
      getSpeedLimitsOrBuilderList();
  /**
   * <pre>
   * 车道限速列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 6;</code>
   */
  road.data.proto.RegulatorySpeedLimitOrBuilder getSpeedLimitsOrBuilder(
      int index);

  /**
   * <pre>
   *车道中间点列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 7;</code>
   */
  java.util.List<road.data.proto.Position3D> 
      getPointsList();
  /**
   * <pre>
   *车道中间点列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 7;</code>
   */
  road.data.proto.Position3D getPoints(int index);
  /**
   * <pre>
   *车道中间点列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 7;</code>
   */
  int getPointsCount();
  /**
   * <pre>
   *车道中间点列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 7;</code>
   */
  java.util.List<? extends road.data.proto.Position3DOrBuilder> 
      getPointsOrBuilderList();
  /**
   * <pre>
   *车道中间点列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 7;</code>
   */
  road.data.proto.Position3DOrBuilder getPointsOrBuilder(
      int index);

  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  java.util.List<road.data.proto.LaneBoundary> 
      getLeftBoundaryList();
  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  road.data.proto.LaneBoundary getLeftBoundary(int index);
  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  int getLeftBoundaryCount();
  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  java.util.List<? extends road.data.proto.LaneBoundaryOrBuilder> 
      getLeftBoundaryOrBuilderList();
  /**
   * <pre>
   *车道左边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary leftBoundary = 8;</code>
   */
  road.data.proto.LaneBoundaryOrBuilder getLeftBoundaryOrBuilder(
      int index);

  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  java.util.List<road.data.proto.LaneBoundary> 
      getRightBoundaryList();
  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  road.data.proto.LaneBoundary getRightBoundary(int index);
  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  int getRightBoundaryCount();
  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  java.util.List<? extends road.data.proto.LaneBoundaryOrBuilder> 
      getRightBoundaryOrBuilderList();
  /**
   * <pre>
   *车道右边界
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneBoundary rightBoundary = 9;</code>
   */
  road.data.proto.LaneBoundaryOrBuilder getRightBoundaryOrBuilder(
      int index);
}
