// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneSharingOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneSharing)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *定义车道被共享的情况。在已有的车道属性定义基础上，该数据表示此车道还会有其他的交通参与者出现，并可能拥有相同的路权,
   * </pre>
   *
   * <code>uint32 shareWith = 1;</code>
   */
  int getShareWith();
}
