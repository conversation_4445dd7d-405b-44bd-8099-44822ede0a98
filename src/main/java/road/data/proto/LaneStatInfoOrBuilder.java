// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneStatInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneStatInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *LaneId车道对象定义车道定义来自Lane对象
   * </pre>
   *
   * <code>uint32 laneId = 1;</code>
   */
  int getLaneId();

  /**
   * <pre>
   *所属路段link的编号和信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
   */
  boolean hasLinkStatInfo();
  /**
   * <pre>
   *所属路段link的编号和信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
   */
  road.data.proto.LinkStatInfo getLinkStatInfo();
  /**
   * <pre>
   *所属路段link的编号和信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
   */
  road.data.proto.LinkStatInfoOrBuilder getLinkStatInfoOrBuilder();

  /**
   * <pre>
   *可选，所属路段区段section的编号
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
   */
  boolean hasSectionStatInfo();
  /**
   * <pre>
   *可选，所属路段区段section的编号
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
   */
  road.data.proto.SectionStatInfo getSectionStatInfo();
  /**
   * <pre>
   *可选，所属路段区段section的编号
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
   */
  road.data.proto.SectionStatInfoOrBuilder getSectionStatInfoOrBuilder();

  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 4;</code>
   */
  java.lang.String getExtId();
  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 4;</code>
   */
  com.google.protobuf.ByteString
      getExtIdBytes();
}
