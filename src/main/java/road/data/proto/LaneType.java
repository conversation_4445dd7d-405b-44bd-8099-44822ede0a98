// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.LaneType}
 */
public  final class LaneType extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.LaneType)
    LaneTypeOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LaneType.newBuilder() to construct.
  private LaneType(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LaneType() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LaneType();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private LaneType(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            choiceId_ = input.readUInt32();
            break;
          }
          case 18: {
            road.data.proto.LaneTypeAttributes.Builder subBuilder = null;
            if (value_ != null) {
              subBuilder = value_.toBuilder();
            }
            value_ = input.readMessage(road.data.proto.LaneTypeAttributes.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(value_);
              value_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneType_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneType_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.LaneType.class, road.data.proto.LaneType.Builder.class);
  }

  public static final int CHOICEID_FIELD_NUMBER = 1;
  private int choiceId_;
  /**
   * <pre>
   *车道本身所属类别的序号
   * </pre>
   *
   * <code>uint32 choiceId = 1;</code>
   */
  public int getChoiceId() {
    return choiceId_;
  }

  public static final int VALUE_FIELD_NUMBER = 2;
  private road.data.proto.LaneTypeAttributes value_;
  /**
   * <pre>
   *10: LaneAttributes-Vehicle车辆行驶车道
   *20: LaneAttributes-Crosswalk人行横道属性
   *30: LaneAttributes-Bike自行车道的属性
   *40: LaneAttributes-Sidewalk人行道属性
   *50: LaneAttributes-Barrier车道隔断离的属性
   *60: LaneAttributes-Striping标线车道
   *70: LaneAttributes-TrackedVehicle轨道车辆车道
   *80: LaneAttributes-Parking停车车道的属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
   */
  public boolean hasValue() {
    return value_ != null;
  }
  /**
   * <pre>
   *10: LaneAttributes-Vehicle车辆行驶车道
   *20: LaneAttributes-Crosswalk人行横道属性
   *30: LaneAttributes-Bike自行车道的属性
   *40: LaneAttributes-Sidewalk人行道属性
   *50: LaneAttributes-Barrier车道隔断离的属性
   *60: LaneAttributes-Striping标线车道
   *70: LaneAttributes-TrackedVehicle轨道车辆车道
   *80: LaneAttributes-Parking停车车道的属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
   */
  public road.data.proto.LaneTypeAttributes getValue() {
    return value_ == null ? road.data.proto.LaneTypeAttributes.getDefaultInstance() : value_;
  }
  /**
   * <pre>
   *10: LaneAttributes-Vehicle车辆行驶车道
   *20: LaneAttributes-Crosswalk人行横道属性
   *30: LaneAttributes-Bike自行车道的属性
   *40: LaneAttributes-Sidewalk人行道属性
   *50: LaneAttributes-Barrier车道隔断离的属性
   *60: LaneAttributes-Striping标线车道
   *70: LaneAttributes-TrackedVehicle轨道车辆车道
   *80: LaneAttributes-Parking停车车道的属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
   */
  public road.data.proto.LaneTypeAttributesOrBuilder getValueOrBuilder() {
    return getValue();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (choiceId_ != 0) {
      output.writeUInt32(1, choiceId_);
    }
    if (value_ != null) {
      output.writeMessage(2, getValue());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (choiceId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, choiceId_);
    }
    if (value_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getValue());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.LaneType)) {
      return super.equals(obj);
    }
    road.data.proto.LaneType other = (road.data.proto.LaneType) obj;

    if (getChoiceId()
        != other.getChoiceId()) return false;
    if (hasValue() != other.hasValue()) return false;
    if (hasValue()) {
      if (!getValue()
          .equals(other.getValue())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHOICEID_FIELD_NUMBER;
    hash = (53 * hash) + getChoiceId();
    if (hasValue()) {
      hash = (37 * hash) + VALUE_FIELD_NUMBER;
      hash = (53 * hash) + getValue().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.LaneType parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneType parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneType parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneType parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneType parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneType parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneType parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneType parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneType parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneType parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneType parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneType parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.LaneType prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.LaneType}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.LaneType)
      road.data.proto.LaneTypeOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneType_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneType_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.LaneType.class, road.data.proto.LaneType.Builder.class);
    }

    // Construct using road.data.proto.LaneType.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      choiceId_ = 0;

      if (valueBuilder_ == null) {
        value_ = null;
      } else {
        value_ = null;
        valueBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneType_descriptor;
    }

    @java.lang.Override
    public road.data.proto.LaneType getDefaultInstanceForType() {
      return road.data.proto.LaneType.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.LaneType build() {
      road.data.proto.LaneType result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.LaneType buildPartial() {
      road.data.proto.LaneType result = new road.data.proto.LaneType(this);
      result.choiceId_ = choiceId_;
      if (valueBuilder_ == null) {
        result.value_ = value_;
      } else {
        result.value_ = valueBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.LaneType) {
        return mergeFrom((road.data.proto.LaneType)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.LaneType other) {
      if (other == road.data.proto.LaneType.getDefaultInstance()) return this;
      if (other.getChoiceId() != 0) {
        setChoiceId(other.getChoiceId());
      }
      if (other.hasValue()) {
        mergeValue(other.getValue());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.LaneType parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.LaneType) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int choiceId_ ;
    /**
     * <pre>
     *车道本身所属类别的序号
     * </pre>
     *
     * <code>uint32 choiceId = 1;</code>
     */
    public int getChoiceId() {
      return choiceId_;
    }
    /**
     * <pre>
     *车道本身所属类别的序号
     * </pre>
     *
     * <code>uint32 choiceId = 1;</code>
     */
    public Builder setChoiceId(int value) {
      
      choiceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *车道本身所属类别的序号
     * </pre>
     *
     * <code>uint32 choiceId = 1;</code>
     */
    public Builder clearChoiceId() {
      
      choiceId_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.LaneTypeAttributes value_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneTypeAttributes, road.data.proto.LaneTypeAttributes.Builder, road.data.proto.LaneTypeAttributesOrBuilder> valueBuilder_;
    /**
     * <pre>
     *10: LaneAttributes-Vehicle车辆行驶车道
     *20: LaneAttributes-Crosswalk人行横道属性
     *30: LaneAttributes-Bike自行车道的属性
     *40: LaneAttributes-Sidewalk人行道属性
     *50: LaneAttributes-Barrier车道隔断离的属性
     *60: LaneAttributes-Striping标线车道
     *70: LaneAttributes-TrackedVehicle轨道车辆车道
     *80: LaneAttributes-Parking停车车道的属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
     */
    public boolean hasValue() {
      return valueBuilder_ != null || value_ != null;
    }
    /**
     * <pre>
     *10: LaneAttributes-Vehicle车辆行驶车道
     *20: LaneAttributes-Crosswalk人行横道属性
     *30: LaneAttributes-Bike自行车道的属性
     *40: LaneAttributes-Sidewalk人行道属性
     *50: LaneAttributes-Barrier车道隔断离的属性
     *60: LaneAttributes-Striping标线车道
     *70: LaneAttributes-TrackedVehicle轨道车辆车道
     *80: LaneAttributes-Parking停车车道的属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
     */
    public road.data.proto.LaneTypeAttributes getValue() {
      if (valueBuilder_ == null) {
        return value_ == null ? road.data.proto.LaneTypeAttributes.getDefaultInstance() : value_;
      } else {
        return valueBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *10: LaneAttributes-Vehicle车辆行驶车道
     *20: LaneAttributes-Crosswalk人行横道属性
     *30: LaneAttributes-Bike自行车道的属性
     *40: LaneAttributes-Sidewalk人行道属性
     *50: LaneAttributes-Barrier车道隔断离的属性
     *60: LaneAttributes-Striping标线车道
     *70: LaneAttributes-TrackedVehicle轨道车辆车道
     *80: LaneAttributes-Parking停车车道的属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
     */
    public Builder setValue(road.data.proto.LaneTypeAttributes value) {
      if (valueBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        value_ = value;
        onChanged();
      } else {
        valueBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *10: LaneAttributes-Vehicle车辆行驶车道
     *20: LaneAttributes-Crosswalk人行横道属性
     *30: LaneAttributes-Bike自行车道的属性
     *40: LaneAttributes-Sidewalk人行道属性
     *50: LaneAttributes-Barrier车道隔断离的属性
     *60: LaneAttributes-Striping标线车道
     *70: LaneAttributes-TrackedVehicle轨道车辆车道
     *80: LaneAttributes-Parking停车车道的属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
     */
    public Builder setValue(
        road.data.proto.LaneTypeAttributes.Builder builderForValue) {
      if (valueBuilder_ == null) {
        value_ = builderForValue.build();
        onChanged();
      } else {
        valueBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *10: LaneAttributes-Vehicle车辆行驶车道
     *20: LaneAttributes-Crosswalk人行横道属性
     *30: LaneAttributes-Bike自行车道的属性
     *40: LaneAttributes-Sidewalk人行道属性
     *50: LaneAttributes-Barrier车道隔断离的属性
     *60: LaneAttributes-Striping标线车道
     *70: LaneAttributes-TrackedVehicle轨道车辆车道
     *80: LaneAttributes-Parking停车车道的属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
     */
    public Builder mergeValue(road.data.proto.LaneTypeAttributes value) {
      if (valueBuilder_ == null) {
        if (value_ != null) {
          value_ =
            road.data.proto.LaneTypeAttributes.newBuilder(value_).mergeFrom(value).buildPartial();
        } else {
          value_ = value;
        }
        onChanged();
      } else {
        valueBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *10: LaneAttributes-Vehicle车辆行驶车道
     *20: LaneAttributes-Crosswalk人行横道属性
     *30: LaneAttributes-Bike自行车道的属性
     *40: LaneAttributes-Sidewalk人行道属性
     *50: LaneAttributes-Barrier车道隔断离的属性
     *60: LaneAttributes-Striping标线车道
     *70: LaneAttributes-TrackedVehicle轨道车辆车道
     *80: LaneAttributes-Parking停车车道的属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
     */
    public Builder clearValue() {
      if (valueBuilder_ == null) {
        value_ = null;
        onChanged();
      } else {
        value_ = null;
        valueBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *10: LaneAttributes-Vehicle车辆行驶车道
     *20: LaneAttributes-Crosswalk人行横道属性
     *30: LaneAttributes-Bike自行车道的属性
     *40: LaneAttributes-Sidewalk人行道属性
     *50: LaneAttributes-Barrier车道隔断离的属性
     *60: LaneAttributes-Striping标线车道
     *70: LaneAttributes-TrackedVehicle轨道车辆车道
     *80: LaneAttributes-Parking停车车道的属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
     */
    public road.data.proto.LaneTypeAttributes.Builder getValueBuilder() {
      
      onChanged();
      return getValueFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *10: LaneAttributes-Vehicle车辆行驶车道
     *20: LaneAttributes-Crosswalk人行横道属性
     *30: LaneAttributes-Bike自行车道的属性
     *40: LaneAttributes-Sidewalk人行道属性
     *50: LaneAttributes-Barrier车道隔断离的属性
     *60: LaneAttributes-Striping标线车道
     *70: LaneAttributes-TrackedVehicle轨道车辆车道
     *80: LaneAttributes-Parking停车车道的属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
     */
    public road.data.proto.LaneTypeAttributesOrBuilder getValueOrBuilder() {
      if (valueBuilder_ != null) {
        return valueBuilder_.getMessageOrBuilder();
      } else {
        return value_ == null ?
            road.data.proto.LaneTypeAttributes.getDefaultInstance() : value_;
      }
    }
    /**
     * <pre>
     *10: LaneAttributes-Vehicle车辆行驶车道
     *20: LaneAttributes-Crosswalk人行横道属性
     *30: LaneAttributes-Bike自行车道的属性
     *40: LaneAttributes-Sidewalk人行道属性
     *50: LaneAttributes-Barrier车道隔断离的属性
     *60: LaneAttributes-Striping标线车道
     *70: LaneAttributes-TrackedVehicle轨道车辆车道
     *80: LaneAttributes-Parking停车车道的属性
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneTypeAttributes, road.data.proto.LaneTypeAttributes.Builder, road.data.proto.LaneTypeAttributesOrBuilder> 
        getValueFieldBuilder() {
      if (valueBuilder_ == null) {
        valueBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneTypeAttributes, road.data.proto.LaneTypeAttributes.Builder, road.data.proto.LaneTypeAttributesOrBuilder>(
                getValue(),
                getParentForChildren(),
                isClean());
        value_ = null;
      }
      return valueBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.LaneType)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.LaneType)
  private static final road.data.proto.LaneType DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.LaneType();
  }

  public static road.data.proto.LaneType getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LaneType>
      PARSER = new com.google.protobuf.AbstractParser<LaneType>() {
    @java.lang.Override
    public LaneType parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new LaneType(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<LaneType> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LaneType> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.LaneType getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

