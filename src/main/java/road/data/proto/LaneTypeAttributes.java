// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车道本身所属的类别特性    
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.LaneTypeAttributes}
 */
public  final class LaneTypeAttributes extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.LaneTypeAttributes)
    LaneTypeAttributesOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LaneTypeAttributes.newBuilder() to construct.
  private LaneTypeAttributes(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LaneTypeAttributes() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LaneTypeAttributes();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private LaneTypeAttributes(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.LaneAttributesVehicle.Builder subBuilder = null;
            if (laneTypeAttributesOneOfCase_ == 1) {
              subBuilder = ((road.data.proto.LaneAttributesVehicle) laneTypeAttributesOneOf_).toBuilder();
            }
            laneTypeAttributesOneOf_ =
                input.readMessage(road.data.proto.LaneAttributesVehicle.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.LaneAttributesVehicle) laneTypeAttributesOneOf_);
              laneTypeAttributesOneOf_ = subBuilder.buildPartial();
            }
            laneTypeAttributesOneOfCase_ = 1;
            break;
          }
          case 18: {
            road.data.proto.LaneAttributesCrosswalk.Builder subBuilder = null;
            if (laneTypeAttributesOneOfCase_ == 2) {
              subBuilder = ((road.data.proto.LaneAttributesCrosswalk) laneTypeAttributesOneOf_).toBuilder();
            }
            laneTypeAttributesOneOf_ =
                input.readMessage(road.data.proto.LaneAttributesCrosswalk.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.LaneAttributesCrosswalk) laneTypeAttributesOneOf_);
              laneTypeAttributesOneOf_ = subBuilder.buildPartial();
            }
            laneTypeAttributesOneOfCase_ = 2;
            break;
          }
          case 26: {
            road.data.proto.LaneAttributesBike.Builder subBuilder = null;
            if (laneTypeAttributesOneOfCase_ == 3) {
              subBuilder = ((road.data.proto.LaneAttributesBike) laneTypeAttributesOneOf_).toBuilder();
            }
            laneTypeAttributesOneOf_ =
                input.readMessage(road.data.proto.LaneAttributesBike.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.LaneAttributesBike) laneTypeAttributesOneOf_);
              laneTypeAttributesOneOf_ = subBuilder.buildPartial();
            }
            laneTypeAttributesOneOfCase_ = 3;
            break;
          }
          case 34: {
            road.data.proto.LaneAttributesSidewalk.Builder subBuilder = null;
            if (laneTypeAttributesOneOfCase_ == 4) {
              subBuilder = ((road.data.proto.LaneAttributesSidewalk) laneTypeAttributesOneOf_).toBuilder();
            }
            laneTypeAttributesOneOf_ =
                input.readMessage(road.data.proto.LaneAttributesSidewalk.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.LaneAttributesSidewalk) laneTypeAttributesOneOf_);
              laneTypeAttributesOneOf_ = subBuilder.buildPartial();
            }
            laneTypeAttributesOneOfCase_ = 4;
            break;
          }
          case 42: {
            road.data.proto.LaneAttributesBarrier.Builder subBuilder = null;
            if (laneTypeAttributesOneOfCase_ == 5) {
              subBuilder = ((road.data.proto.LaneAttributesBarrier) laneTypeAttributesOneOf_).toBuilder();
            }
            laneTypeAttributesOneOf_ =
                input.readMessage(road.data.proto.LaneAttributesBarrier.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.LaneAttributesBarrier) laneTypeAttributesOneOf_);
              laneTypeAttributesOneOf_ = subBuilder.buildPartial();
            }
            laneTypeAttributesOneOfCase_ = 5;
            break;
          }
          case 50: {
            road.data.proto.LaneAttributesStriping.Builder subBuilder = null;
            if (laneTypeAttributesOneOfCase_ == 6) {
              subBuilder = ((road.data.proto.LaneAttributesStriping) laneTypeAttributesOneOf_).toBuilder();
            }
            laneTypeAttributesOneOf_ =
                input.readMessage(road.data.proto.LaneAttributesStriping.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.LaneAttributesStriping) laneTypeAttributesOneOf_);
              laneTypeAttributesOneOf_ = subBuilder.buildPartial();
            }
            laneTypeAttributesOneOfCase_ = 6;
            break;
          }
          case 58: {
            road.data.proto.LaneAttributesTrackedVehicle.Builder subBuilder = null;
            if (laneTypeAttributesOneOfCase_ == 7) {
              subBuilder = ((road.data.proto.LaneAttributesTrackedVehicle) laneTypeAttributesOneOf_).toBuilder();
            }
            laneTypeAttributesOneOf_ =
                input.readMessage(road.data.proto.LaneAttributesTrackedVehicle.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.LaneAttributesTrackedVehicle) laneTypeAttributesOneOf_);
              laneTypeAttributesOneOf_ = subBuilder.buildPartial();
            }
            laneTypeAttributesOneOfCase_ = 7;
            break;
          }
          case 66: {
            road.data.proto.LaneAttributesParking.Builder subBuilder = null;
            if (laneTypeAttributesOneOfCase_ == 8) {
              subBuilder = ((road.data.proto.LaneAttributesParking) laneTypeAttributesOneOf_).toBuilder();
            }
            laneTypeAttributesOneOf_ =
                input.readMessage(road.data.proto.LaneAttributesParking.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.LaneAttributesParking) laneTypeAttributesOneOf_);
              laneTypeAttributesOneOf_ = subBuilder.buildPartial();
            }
            laneTypeAttributesOneOfCase_ = 8;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneTypeAttributes_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneTypeAttributes_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.LaneTypeAttributes.class, road.data.proto.LaneTypeAttributes.Builder.class);
  }

  private int laneTypeAttributesOneOfCase_ = 0;
  private java.lang.Object laneTypeAttributesOneOf_;
  public enum LaneTypeAttributesOneOfCase
      implements com.google.protobuf.Internal.EnumLite {
    MOTORVEHICLELANES(1),
    PEDESTRIANCROSSWALKS(2),
    BIKELANES(3),
    PEDESTRIANSIDEWALKPATHS(4),
    MEDIANSCHANNELIZATION(5),
    ROADWAYMARKINGS(6),
    TRAINSANDTROLLEYS(7),
    PARKINGANDSTOPPINGLANES(8),
    LANETYPEATTRIBUTESONEOF_NOT_SET(0);
    private final int value;
    private LaneTypeAttributesOneOfCase(int value) {
      this.value = value;
    }
    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static LaneTypeAttributesOneOfCase valueOf(int value) {
      return forNumber(value);
    }

    public static LaneTypeAttributesOneOfCase forNumber(int value) {
      switch (value) {
        case 1: return MOTORVEHICLELANES;
        case 2: return PEDESTRIANCROSSWALKS;
        case 3: return BIKELANES;
        case 4: return PEDESTRIANSIDEWALKPATHS;
        case 5: return MEDIANSCHANNELIZATION;
        case 6: return ROADWAYMARKINGS;
        case 7: return TRAINSANDTROLLEYS;
        case 8: return PARKINGANDSTOPPINGLANES;
        case 0: return LANETYPEATTRIBUTESONEOF_NOT_SET;
        default: return null;
      }
    }
    public int getNumber() {
      return this.value;
    }
  };

  public LaneTypeAttributesOneOfCase
  getLaneTypeAttributesOneOfCase() {
    return LaneTypeAttributesOneOfCase.forNumber(
        laneTypeAttributesOneOfCase_);
  }

  public static final int MOTORVEHICLELANES_FIELD_NUMBER = 1;
  /**
   * <pre>
   *可选，机动车道 LaneAttributesVehicle 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
   */
  public boolean hasMotorVehicleLanes() {
    return laneTypeAttributesOneOfCase_ == 1;
  }
  /**
   * <pre>
   *可选，机动车道 LaneAttributesVehicle 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
   */
  public road.data.proto.LaneAttributesVehicle getMotorVehicleLanes() {
    if (laneTypeAttributesOneOfCase_ == 1) {
       return (road.data.proto.LaneAttributesVehicle) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesVehicle.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，机动车道 LaneAttributesVehicle 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
   */
  public road.data.proto.LaneAttributesVehicleOrBuilder getMotorVehicleLanesOrBuilder() {
    if (laneTypeAttributesOneOfCase_ == 1) {
       return (road.data.proto.LaneAttributesVehicle) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesVehicle.getDefaultInstance();
  }

  public static final int PEDESTRIANCROSSWALKS_FIELD_NUMBER = 2;
  /**
   * <pre>
   *可选，人行横道 LaneAttributesCrosswalk 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
   */
  public boolean hasPedestrianCrosswalks() {
    return laneTypeAttributesOneOfCase_ == 2;
  }
  /**
   * <pre>
   *可选，人行横道 LaneAttributesCrosswalk 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
   */
  public road.data.proto.LaneAttributesCrosswalk getPedestrianCrosswalks() {
    if (laneTypeAttributesOneOfCase_ == 2) {
       return (road.data.proto.LaneAttributesCrosswalk) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesCrosswalk.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，人行横道 LaneAttributesCrosswalk 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
   */
  public road.data.proto.LaneAttributesCrosswalkOrBuilder getPedestrianCrosswalksOrBuilder() {
    if (laneTypeAttributesOneOfCase_ == 2) {
       return (road.data.proto.LaneAttributesCrosswalk) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesCrosswalk.getDefaultInstance();
  }

  public static final int BIKELANES_FIELD_NUMBER = 3;
  /**
   * <pre>
   *可选，自行车道    LaneAttributesBike  位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
   */
  public boolean hasBikeLanes() {
    return laneTypeAttributesOneOfCase_ == 3;
  }
  /**
   * <pre>
   *可选，自行车道    LaneAttributesBike  位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
   */
  public road.data.proto.LaneAttributesBike getBikeLanes() {
    if (laneTypeAttributesOneOfCase_ == 3) {
       return (road.data.proto.LaneAttributesBike) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesBike.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，自行车道    LaneAttributesBike  位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
   */
  public road.data.proto.LaneAttributesBikeOrBuilder getBikeLanesOrBuilder() {
    if (laneTypeAttributesOneOfCase_ == 3) {
       return (road.data.proto.LaneAttributesBike) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesBike.getDefaultInstance();
  }

  public static final int PEDESTRIANSIDEWALKPATHS_FIELD_NUMBER = 4;
  /**
   * <pre>
   *可选，人行道 LaneAttributesSidewalk 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
   */
  public boolean hasPedestrianSidewalkPaths() {
    return laneTypeAttributesOneOfCase_ == 4;
  }
  /**
   * <pre>
   *可选，人行道 LaneAttributesSidewalk 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
   */
  public road.data.proto.LaneAttributesSidewalk getPedestrianSidewalkPaths() {
    if (laneTypeAttributesOneOfCase_ == 4) {
       return (road.data.proto.LaneAttributesSidewalk) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesSidewalk.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，人行道 LaneAttributesSidewalk 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
   */
  public road.data.proto.LaneAttributesSidewalkOrBuilder getPedestrianSidewalkPathsOrBuilder() {
    if (laneTypeAttributesOneOfCase_ == 4) {
       return (road.data.proto.LaneAttributesSidewalk) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesSidewalk.getDefaultInstance();
  }

  public static final int MEDIANSCHANNELIZATION_FIELD_NUMBER = 5;
  /**
   * <pre>
   *可选，中值和通道化 LaneAttributesBarrier 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
   */
  public boolean hasMediansChannelization() {
    return laneTypeAttributesOneOfCase_ == 5;
  }
  /**
   * <pre>
   *可选，中值和通道化 LaneAttributesBarrier 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
   */
  public road.data.proto.LaneAttributesBarrier getMediansChannelization() {
    if (laneTypeAttributesOneOfCase_ == 5) {
       return (road.data.proto.LaneAttributesBarrier) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesBarrier.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，中值和通道化 LaneAttributesBarrier 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
   */
  public road.data.proto.LaneAttributesBarrierOrBuilder getMediansChannelizationOrBuilder() {
    if (laneTypeAttributesOneOfCase_ == 5) {
       return (road.data.proto.LaneAttributesBarrier) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesBarrier.getDefaultInstance();
  }

  public static final int ROADWAYMARKINGS_FIELD_NUMBER = 6;
  /**
   * <pre>
   *可选，道路标线 LaneAttributesStriping 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
   */
  public boolean hasRoadwayMarkings() {
    return laneTypeAttributesOneOfCase_ == 6;
  }
  /**
   * <pre>
   *可选，道路标线 LaneAttributesStriping 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
   */
  public road.data.proto.LaneAttributesStriping getRoadwayMarkings() {
    if (laneTypeAttributesOneOfCase_ == 6) {
       return (road.data.proto.LaneAttributesStriping) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesStriping.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，道路标线 LaneAttributesStriping 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
   */
  public road.data.proto.LaneAttributesStripingOrBuilder getRoadwayMarkingsOrBuilder() {
    if (laneTypeAttributesOneOfCase_ == 6) {
       return (road.data.proto.LaneAttributesStriping) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesStriping.getDefaultInstance();
  }

  public static final int TRAINSANDTROLLEYS_FIELD_NUMBER = 7;
  /**
   * <pre>
   *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
   */
  public boolean hasTrainsAndTrolleys() {
    return laneTypeAttributesOneOfCase_ == 7;
  }
  /**
   * <pre>
   *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
   */
  public road.data.proto.LaneAttributesTrackedVehicle getTrainsAndTrolleys() {
    if (laneTypeAttributesOneOfCase_ == 7) {
       return (road.data.proto.LaneAttributesTrackedVehicle) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesTrackedVehicle.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
   */
  public road.data.proto.LaneAttributesTrackedVehicleOrBuilder getTrainsAndTrolleysOrBuilder() {
    if (laneTypeAttributesOneOfCase_ == 7) {
       return (road.data.proto.LaneAttributesTrackedVehicle) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesTrackedVehicle.getDefaultInstance();
  }

  public static final int PARKINGANDSTOPPINGLANES_FIELD_NUMBER = 8;
  /**
   * <pre>
   *可选，停车和停车车道 LaneAttributesParking 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
   */
  public boolean hasParkingAndStoppingLanes() {
    return laneTypeAttributesOneOfCase_ == 8;
  }
  /**
   * <pre>
   *可选，停车和停车车道 LaneAttributesParking 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
   */
  public road.data.proto.LaneAttributesParking getParkingAndStoppingLanes() {
    if (laneTypeAttributesOneOfCase_ == 8) {
       return (road.data.proto.LaneAttributesParking) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesParking.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，停车和停车车道 LaneAttributesParking 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
   */
  public road.data.proto.LaneAttributesParkingOrBuilder getParkingAndStoppingLanesOrBuilder() {
    if (laneTypeAttributesOneOfCase_ == 8) {
       return (road.data.proto.LaneAttributesParking) laneTypeAttributesOneOf_;
    }
    return road.data.proto.LaneAttributesParking.getDefaultInstance();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (laneTypeAttributesOneOfCase_ == 1) {
      output.writeMessage(1, (road.data.proto.LaneAttributesVehicle) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 2) {
      output.writeMessage(2, (road.data.proto.LaneAttributesCrosswalk) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 3) {
      output.writeMessage(3, (road.data.proto.LaneAttributesBike) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 4) {
      output.writeMessage(4, (road.data.proto.LaneAttributesSidewalk) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 5) {
      output.writeMessage(5, (road.data.proto.LaneAttributesBarrier) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 6) {
      output.writeMessage(6, (road.data.proto.LaneAttributesStriping) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 7) {
      output.writeMessage(7, (road.data.proto.LaneAttributesTrackedVehicle) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 8) {
      output.writeMessage(8, (road.data.proto.LaneAttributesParking) laneTypeAttributesOneOf_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (laneTypeAttributesOneOfCase_ == 1) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, (road.data.proto.LaneAttributesVehicle) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 2) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, (road.data.proto.LaneAttributesCrosswalk) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 3) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, (road.data.proto.LaneAttributesBike) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 4) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, (road.data.proto.LaneAttributesSidewalk) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 5) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, (road.data.proto.LaneAttributesBarrier) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 6) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, (road.data.proto.LaneAttributesStriping) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 7) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, (road.data.proto.LaneAttributesTrackedVehicle) laneTypeAttributesOneOf_);
    }
    if (laneTypeAttributesOneOfCase_ == 8) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, (road.data.proto.LaneAttributesParking) laneTypeAttributesOneOf_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.LaneTypeAttributes)) {
      return super.equals(obj);
    }
    road.data.proto.LaneTypeAttributes other = (road.data.proto.LaneTypeAttributes) obj;

    if (!getLaneTypeAttributesOneOfCase().equals(other.getLaneTypeAttributesOneOfCase())) return false;
    switch (laneTypeAttributesOneOfCase_) {
      case 1:
        if (!getMotorVehicleLanes()
            .equals(other.getMotorVehicleLanes())) return false;
        break;
      case 2:
        if (!getPedestrianCrosswalks()
            .equals(other.getPedestrianCrosswalks())) return false;
        break;
      case 3:
        if (!getBikeLanes()
            .equals(other.getBikeLanes())) return false;
        break;
      case 4:
        if (!getPedestrianSidewalkPaths()
            .equals(other.getPedestrianSidewalkPaths())) return false;
        break;
      case 5:
        if (!getMediansChannelization()
            .equals(other.getMediansChannelization())) return false;
        break;
      case 6:
        if (!getRoadwayMarkings()
            .equals(other.getRoadwayMarkings())) return false;
        break;
      case 7:
        if (!getTrainsAndTrolleys()
            .equals(other.getTrainsAndTrolleys())) return false;
        break;
      case 8:
        if (!getParkingAndStoppingLanes()
            .equals(other.getParkingAndStoppingLanes())) return false;
        break;
      case 0:
      default:
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    switch (laneTypeAttributesOneOfCase_) {
      case 1:
        hash = (37 * hash) + MOTORVEHICLELANES_FIELD_NUMBER;
        hash = (53 * hash) + getMotorVehicleLanes().hashCode();
        break;
      case 2:
        hash = (37 * hash) + PEDESTRIANCROSSWALKS_FIELD_NUMBER;
        hash = (53 * hash) + getPedestrianCrosswalks().hashCode();
        break;
      case 3:
        hash = (37 * hash) + BIKELANES_FIELD_NUMBER;
        hash = (53 * hash) + getBikeLanes().hashCode();
        break;
      case 4:
        hash = (37 * hash) + PEDESTRIANSIDEWALKPATHS_FIELD_NUMBER;
        hash = (53 * hash) + getPedestrianSidewalkPaths().hashCode();
        break;
      case 5:
        hash = (37 * hash) + MEDIANSCHANNELIZATION_FIELD_NUMBER;
        hash = (53 * hash) + getMediansChannelization().hashCode();
        break;
      case 6:
        hash = (37 * hash) + ROADWAYMARKINGS_FIELD_NUMBER;
        hash = (53 * hash) + getRoadwayMarkings().hashCode();
        break;
      case 7:
        hash = (37 * hash) + TRAINSANDTROLLEYS_FIELD_NUMBER;
        hash = (53 * hash) + getTrainsAndTrolleys().hashCode();
        break;
      case 8:
        hash = (37 * hash) + PARKINGANDSTOPPINGLANES_FIELD_NUMBER;
        hash = (53 * hash) + getParkingAndStoppingLanes().hashCode();
        break;
      case 0:
      default:
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.LaneTypeAttributes parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneTypeAttributes parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneTypeAttributes parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneTypeAttributes parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneTypeAttributes parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LaneTypeAttributes parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LaneTypeAttributes parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneTypeAttributes parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneTypeAttributes parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneTypeAttributes parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LaneTypeAttributes parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LaneTypeAttributes parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.LaneTypeAttributes prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *车道本身所属的类别特性    
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.LaneTypeAttributes}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.LaneTypeAttributes)
      road.data.proto.LaneTypeAttributesOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneTypeAttributes_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneTypeAttributes_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.LaneTypeAttributes.class, road.data.proto.LaneTypeAttributes.Builder.class);
    }

    // Construct using road.data.proto.LaneTypeAttributes.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      laneTypeAttributesOneOfCase_ = 0;
      laneTypeAttributesOneOf_ = null;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LaneTypeAttributes_descriptor;
    }

    @java.lang.Override
    public road.data.proto.LaneTypeAttributes getDefaultInstanceForType() {
      return road.data.proto.LaneTypeAttributes.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.LaneTypeAttributes build() {
      road.data.proto.LaneTypeAttributes result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.LaneTypeAttributes buildPartial() {
      road.data.proto.LaneTypeAttributes result = new road.data.proto.LaneTypeAttributes(this);
      if (laneTypeAttributesOneOfCase_ == 1) {
        if (motorVehicleLanesBuilder_ == null) {
          result.laneTypeAttributesOneOf_ = laneTypeAttributesOneOf_;
        } else {
          result.laneTypeAttributesOneOf_ = motorVehicleLanesBuilder_.build();
        }
      }
      if (laneTypeAttributesOneOfCase_ == 2) {
        if (pedestrianCrosswalksBuilder_ == null) {
          result.laneTypeAttributesOneOf_ = laneTypeAttributesOneOf_;
        } else {
          result.laneTypeAttributesOneOf_ = pedestrianCrosswalksBuilder_.build();
        }
      }
      if (laneTypeAttributesOneOfCase_ == 3) {
        if (bikeLanesBuilder_ == null) {
          result.laneTypeAttributesOneOf_ = laneTypeAttributesOneOf_;
        } else {
          result.laneTypeAttributesOneOf_ = bikeLanesBuilder_.build();
        }
      }
      if (laneTypeAttributesOneOfCase_ == 4) {
        if (pedestrianSidewalkPathsBuilder_ == null) {
          result.laneTypeAttributesOneOf_ = laneTypeAttributesOneOf_;
        } else {
          result.laneTypeAttributesOneOf_ = pedestrianSidewalkPathsBuilder_.build();
        }
      }
      if (laneTypeAttributesOneOfCase_ == 5) {
        if (mediansChannelizationBuilder_ == null) {
          result.laneTypeAttributesOneOf_ = laneTypeAttributesOneOf_;
        } else {
          result.laneTypeAttributesOneOf_ = mediansChannelizationBuilder_.build();
        }
      }
      if (laneTypeAttributesOneOfCase_ == 6) {
        if (roadwayMarkingsBuilder_ == null) {
          result.laneTypeAttributesOneOf_ = laneTypeAttributesOneOf_;
        } else {
          result.laneTypeAttributesOneOf_ = roadwayMarkingsBuilder_.build();
        }
      }
      if (laneTypeAttributesOneOfCase_ == 7) {
        if (trainsAndTrolleysBuilder_ == null) {
          result.laneTypeAttributesOneOf_ = laneTypeAttributesOneOf_;
        } else {
          result.laneTypeAttributesOneOf_ = trainsAndTrolleysBuilder_.build();
        }
      }
      if (laneTypeAttributesOneOfCase_ == 8) {
        if (parkingAndStoppingLanesBuilder_ == null) {
          result.laneTypeAttributesOneOf_ = laneTypeAttributesOneOf_;
        } else {
          result.laneTypeAttributesOneOf_ = parkingAndStoppingLanesBuilder_.build();
        }
      }
      result.laneTypeAttributesOneOfCase_ = laneTypeAttributesOneOfCase_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.LaneTypeAttributes) {
        return mergeFrom((road.data.proto.LaneTypeAttributes)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.LaneTypeAttributes other) {
      if (other == road.data.proto.LaneTypeAttributes.getDefaultInstance()) return this;
      switch (other.getLaneTypeAttributesOneOfCase()) {
        case MOTORVEHICLELANES: {
          mergeMotorVehicleLanes(other.getMotorVehicleLanes());
          break;
        }
        case PEDESTRIANCROSSWALKS: {
          mergePedestrianCrosswalks(other.getPedestrianCrosswalks());
          break;
        }
        case BIKELANES: {
          mergeBikeLanes(other.getBikeLanes());
          break;
        }
        case PEDESTRIANSIDEWALKPATHS: {
          mergePedestrianSidewalkPaths(other.getPedestrianSidewalkPaths());
          break;
        }
        case MEDIANSCHANNELIZATION: {
          mergeMediansChannelization(other.getMediansChannelization());
          break;
        }
        case ROADWAYMARKINGS: {
          mergeRoadwayMarkings(other.getRoadwayMarkings());
          break;
        }
        case TRAINSANDTROLLEYS: {
          mergeTrainsAndTrolleys(other.getTrainsAndTrolleys());
          break;
        }
        case PARKINGANDSTOPPINGLANES: {
          mergeParkingAndStoppingLanes(other.getParkingAndStoppingLanes());
          break;
        }
        case LANETYPEATTRIBUTESONEOF_NOT_SET: {
          break;
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.LaneTypeAttributes parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.LaneTypeAttributes) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int laneTypeAttributesOneOfCase_ = 0;
    private java.lang.Object laneTypeAttributesOneOf_;
    public LaneTypeAttributesOneOfCase
        getLaneTypeAttributesOneOfCase() {
      return LaneTypeAttributesOneOfCase.forNumber(
          laneTypeAttributesOneOfCase_);
    }

    public Builder clearLaneTypeAttributesOneOf() {
      laneTypeAttributesOneOfCase_ = 0;
      laneTypeAttributesOneOf_ = null;
      onChanged();
      return this;
    }


    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesVehicle, road.data.proto.LaneAttributesVehicle.Builder, road.data.proto.LaneAttributesVehicleOrBuilder> motorVehicleLanesBuilder_;
    /**
     * <pre>
     *可选，机动车道 LaneAttributesVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
     */
    public boolean hasMotorVehicleLanes() {
      return laneTypeAttributesOneOfCase_ == 1;
    }
    /**
     * <pre>
     *可选，机动车道 LaneAttributesVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
     */
    public road.data.proto.LaneAttributesVehicle getMotorVehicleLanes() {
      if (motorVehicleLanesBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 1) {
          return (road.data.proto.LaneAttributesVehicle) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesVehicle.getDefaultInstance();
      } else {
        if (laneTypeAttributesOneOfCase_ == 1) {
          return motorVehicleLanesBuilder_.getMessage();
        }
        return road.data.proto.LaneAttributesVehicle.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，机动车道 LaneAttributesVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
     */
    public Builder setMotorVehicleLanes(road.data.proto.LaneAttributesVehicle value) {
      if (motorVehicleLanesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        laneTypeAttributesOneOf_ = value;
        onChanged();
      } else {
        motorVehicleLanesBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 1;
      return this;
    }
    /**
     * <pre>
     *可选，机动车道 LaneAttributesVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
     */
    public Builder setMotorVehicleLanes(
        road.data.proto.LaneAttributesVehicle.Builder builderForValue) {
      if (motorVehicleLanesBuilder_ == null) {
        laneTypeAttributesOneOf_ = builderForValue.build();
        onChanged();
      } else {
        motorVehicleLanesBuilder_.setMessage(builderForValue.build());
      }
      laneTypeAttributesOneOfCase_ = 1;
      return this;
    }
    /**
     * <pre>
     *可选，机动车道 LaneAttributesVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
     */
    public Builder mergeMotorVehicleLanes(road.data.proto.LaneAttributesVehicle value) {
      if (motorVehicleLanesBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 1 &&
            laneTypeAttributesOneOf_ != road.data.proto.LaneAttributesVehicle.getDefaultInstance()) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesVehicle.newBuilder((road.data.proto.LaneAttributesVehicle) laneTypeAttributesOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          laneTypeAttributesOneOf_ = value;
        }
        onChanged();
      } else {
        if (laneTypeAttributesOneOfCase_ == 1) {
          motorVehicleLanesBuilder_.mergeFrom(value);
        }
        motorVehicleLanesBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 1;
      return this;
    }
    /**
     * <pre>
     *可选，机动车道 LaneAttributesVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
     */
    public Builder clearMotorVehicleLanes() {
      if (motorVehicleLanesBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 1) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
          onChanged();
        }
      } else {
        if (laneTypeAttributesOneOfCase_ == 1) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
        }
        motorVehicleLanesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，机动车道 LaneAttributesVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
     */
    public road.data.proto.LaneAttributesVehicle.Builder getMotorVehicleLanesBuilder() {
      return getMotorVehicleLanesFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，机动车道 LaneAttributesVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
     */
    public road.data.proto.LaneAttributesVehicleOrBuilder getMotorVehicleLanesOrBuilder() {
      if ((laneTypeAttributesOneOfCase_ == 1) && (motorVehicleLanesBuilder_ != null)) {
        return motorVehicleLanesBuilder_.getMessageOrBuilder();
      } else {
        if (laneTypeAttributesOneOfCase_ == 1) {
          return (road.data.proto.LaneAttributesVehicle) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesVehicle.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，机动车道 LaneAttributesVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesVehicle, road.data.proto.LaneAttributesVehicle.Builder, road.data.proto.LaneAttributesVehicleOrBuilder> 
        getMotorVehicleLanesFieldBuilder() {
      if (motorVehicleLanesBuilder_ == null) {
        if (!(laneTypeAttributesOneOfCase_ == 1)) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesVehicle.getDefaultInstance();
        }
        motorVehicleLanesBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneAttributesVehicle, road.data.proto.LaneAttributesVehicle.Builder, road.data.proto.LaneAttributesVehicleOrBuilder>(
                (road.data.proto.LaneAttributesVehicle) laneTypeAttributesOneOf_,
                getParentForChildren(),
                isClean());
        laneTypeAttributesOneOf_ = null;
      }
      laneTypeAttributesOneOfCase_ = 1;
      onChanged();;
      return motorVehicleLanesBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesCrosswalk, road.data.proto.LaneAttributesCrosswalk.Builder, road.data.proto.LaneAttributesCrosswalkOrBuilder> pedestrianCrosswalksBuilder_;
    /**
     * <pre>
     *可选，人行横道 LaneAttributesCrosswalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
     */
    public boolean hasPedestrianCrosswalks() {
      return laneTypeAttributesOneOfCase_ == 2;
    }
    /**
     * <pre>
     *可选，人行横道 LaneAttributesCrosswalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
     */
    public road.data.proto.LaneAttributesCrosswalk getPedestrianCrosswalks() {
      if (pedestrianCrosswalksBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 2) {
          return (road.data.proto.LaneAttributesCrosswalk) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesCrosswalk.getDefaultInstance();
      } else {
        if (laneTypeAttributesOneOfCase_ == 2) {
          return pedestrianCrosswalksBuilder_.getMessage();
        }
        return road.data.proto.LaneAttributesCrosswalk.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，人行横道 LaneAttributesCrosswalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
     */
    public Builder setPedestrianCrosswalks(road.data.proto.LaneAttributesCrosswalk value) {
      if (pedestrianCrosswalksBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        laneTypeAttributesOneOf_ = value;
        onChanged();
      } else {
        pedestrianCrosswalksBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 2;
      return this;
    }
    /**
     * <pre>
     *可选，人行横道 LaneAttributesCrosswalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
     */
    public Builder setPedestrianCrosswalks(
        road.data.proto.LaneAttributesCrosswalk.Builder builderForValue) {
      if (pedestrianCrosswalksBuilder_ == null) {
        laneTypeAttributesOneOf_ = builderForValue.build();
        onChanged();
      } else {
        pedestrianCrosswalksBuilder_.setMessage(builderForValue.build());
      }
      laneTypeAttributesOneOfCase_ = 2;
      return this;
    }
    /**
     * <pre>
     *可选，人行横道 LaneAttributesCrosswalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
     */
    public Builder mergePedestrianCrosswalks(road.data.proto.LaneAttributesCrosswalk value) {
      if (pedestrianCrosswalksBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 2 &&
            laneTypeAttributesOneOf_ != road.data.proto.LaneAttributesCrosswalk.getDefaultInstance()) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesCrosswalk.newBuilder((road.data.proto.LaneAttributesCrosswalk) laneTypeAttributesOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          laneTypeAttributesOneOf_ = value;
        }
        onChanged();
      } else {
        if (laneTypeAttributesOneOfCase_ == 2) {
          pedestrianCrosswalksBuilder_.mergeFrom(value);
        }
        pedestrianCrosswalksBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 2;
      return this;
    }
    /**
     * <pre>
     *可选，人行横道 LaneAttributesCrosswalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
     */
    public Builder clearPedestrianCrosswalks() {
      if (pedestrianCrosswalksBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 2) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
          onChanged();
        }
      } else {
        if (laneTypeAttributesOneOfCase_ == 2) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
        }
        pedestrianCrosswalksBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，人行横道 LaneAttributesCrosswalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
     */
    public road.data.proto.LaneAttributesCrosswalk.Builder getPedestrianCrosswalksBuilder() {
      return getPedestrianCrosswalksFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，人行横道 LaneAttributesCrosswalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
     */
    public road.data.proto.LaneAttributesCrosswalkOrBuilder getPedestrianCrosswalksOrBuilder() {
      if ((laneTypeAttributesOneOfCase_ == 2) && (pedestrianCrosswalksBuilder_ != null)) {
        return pedestrianCrosswalksBuilder_.getMessageOrBuilder();
      } else {
        if (laneTypeAttributesOneOfCase_ == 2) {
          return (road.data.proto.LaneAttributesCrosswalk) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesCrosswalk.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，人行横道 LaneAttributesCrosswalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesCrosswalk, road.data.proto.LaneAttributesCrosswalk.Builder, road.data.proto.LaneAttributesCrosswalkOrBuilder> 
        getPedestrianCrosswalksFieldBuilder() {
      if (pedestrianCrosswalksBuilder_ == null) {
        if (!(laneTypeAttributesOneOfCase_ == 2)) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesCrosswalk.getDefaultInstance();
        }
        pedestrianCrosswalksBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneAttributesCrosswalk, road.data.proto.LaneAttributesCrosswalk.Builder, road.data.proto.LaneAttributesCrosswalkOrBuilder>(
                (road.data.proto.LaneAttributesCrosswalk) laneTypeAttributesOneOf_,
                getParentForChildren(),
                isClean());
        laneTypeAttributesOneOf_ = null;
      }
      laneTypeAttributesOneOfCase_ = 2;
      onChanged();;
      return pedestrianCrosswalksBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesBike, road.data.proto.LaneAttributesBike.Builder, road.data.proto.LaneAttributesBikeOrBuilder> bikeLanesBuilder_;
    /**
     * <pre>
     *可选，自行车道    LaneAttributesBike  位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
     */
    public boolean hasBikeLanes() {
      return laneTypeAttributesOneOfCase_ == 3;
    }
    /**
     * <pre>
     *可选，自行车道    LaneAttributesBike  位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
     */
    public road.data.proto.LaneAttributesBike getBikeLanes() {
      if (bikeLanesBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 3) {
          return (road.data.proto.LaneAttributesBike) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesBike.getDefaultInstance();
      } else {
        if (laneTypeAttributesOneOfCase_ == 3) {
          return bikeLanesBuilder_.getMessage();
        }
        return road.data.proto.LaneAttributesBike.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，自行车道    LaneAttributesBike  位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
     */
    public Builder setBikeLanes(road.data.proto.LaneAttributesBike value) {
      if (bikeLanesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        laneTypeAttributesOneOf_ = value;
        onChanged();
      } else {
        bikeLanesBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 3;
      return this;
    }
    /**
     * <pre>
     *可选，自行车道    LaneAttributesBike  位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
     */
    public Builder setBikeLanes(
        road.data.proto.LaneAttributesBike.Builder builderForValue) {
      if (bikeLanesBuilder_ == null) {
        laneTypeAttributesOneOf_ = builderForValue.build();
        onChanged();
      } else {
        bikeLanesBuilder_.setMessage(builderForValue.build());
      }
      laneTypeAttributesOneOfCase_ = 3;
      return this;
    }
    /**
     * <pre>
     *可选，自行车道    LaneAttributesBike  位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
     */
    public Builder mergeBikeLanes(road.data.proto.LaneAttributesBike value) {
      if (bikeLanesBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 3 &&
            laneTypeAttributesOneOf_ != road.data.proto.LaneAttributesBike.getDefaultInstance()) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesBike.newBuilder((road.data.proto.LaneAttributesBike) laneTypeAttributesOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          laneTypeAttributesOneOf_ = value;
        }
        onChanged();
      } else {
        if (laneTypeAttributesOneOfCase_ == 3) {
          bikeLanesBuilder_.mergeFrom(value);
        }
        bikeLanesBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 3;
      return this;
    }
    /**
     * <pre>
     *可选，自行车道    LaneAttributesBike  位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
     */
    public Builder clearBikeLanes() {
      if (bikeLanesBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 3) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
          onChanged();
        }
      } else {
        if (laneTypeAttributesOneOfCase_ == 3) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
        }
        bikeLanesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，自行车道    LaneAttributesBike  位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
     */
    public road.data.proto.LaneAttributesBike.Builder getBikeLanesBuilder() {
      return getBikeLanesFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，自行车道    LaneAttributesBike  位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
     */
    public road.data.proto.LaneAttributesBikeOrBuilder getBikeLanesOrBuilder() {
      if ((laneTypeAttributesOneOfCase_ == 3) && (bikeLanesBuilder_ != null)) {
        return bikeLanesBuilder_.getMessageOrBuilder();
      } else {
        if (laneTypeAttributesOneOfCase_ == 3) {
          return (road.data.proto.LaneAttributesBike) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesBike.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，自行车道    LaneAttributesBike  位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesBike, road.data.proto.LaneAttributesBike.Builder, road.data.proto.LaneAttributesBikeOrBuilder> 
        getBikeLanesFieldBuilder() {
      if (bikeLanesBuilder_ == null) {
        if (!(laneTypeAttributesOneOfCase_ == 3)) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesBike.getDefaultInstance();
        }
        bikeLanesBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneAttributesBike, road.data.proto.LaneAttributesBike.Builder, road.data.proto.LaneAttributesBikeOrBuilder>(
                (road.data.proto.LaneAttributesBike) laneTypeAttributesOneOf_,
                getParentForChildren(),
                isClean());
        laneTypeAttributesOneOf_ = null;
      }
      laneTypeAttributesOneOfCase_ = 3;
      onChanged();;
      return bikeLanesBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesSidewalk, road.data.proto.LaneAttributesSidewalk.Builder, road.data.proto.LaneAttributesSidewalkOrBuilder> pedestrianSidewalkPathsBuilder_;
    /**
     * <pre>
     *可选，人行道 LaneAttributesSidewalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
     */
    public boolean hasPedestrianSidewalkPaths() {
      return laneTypeAttributesOneOfCase_ == 4;
    }
    /**
     * <pre>
     *可选，人行道 LaneAttributesSidewalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
     */
    public road.data.proto.LaneAttributesSidewalk getPedestrianSidewalkPaths() {
      if (pedestrianSidewalkPathsBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 4) {
          return (road.data.proto.LaneAttributesSidewalk) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesSidewalk.getDefaultInstance();
      } else {
        if (laneTypeAttributesOneOfCase_ == 4) {
          return pedestrianSidewalkPathsBuilder_.getMessage();
        }
        return road.data.proto.LaneAttributesSidewalk.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，人行道 LaneAttributesSidewalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
     */
    public Builder setPedestrianSidewalkPaths(road.data.proto.LaneAttributesSidewalk value) {
      if (pedestrianSidewalkPathsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        laneTypeAttributesOneOf_ = value;
        onChanged();
      } else {
        pedestrianSidewalkPathsBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 4;
      return this;
    }
    /**
     * <pre>
     *可选，人行道 LaneAttributesSidewalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
     */
    public Builder setPedestrianSidewalkPaths(
        road.data.proto.LaneAttributesSidewalk.Builder builderForValue) {
      if (pedestrianSidewalkPathsBuilder_ == null) {
        laneTypeAttributesOneOf_ = builderForValue.build();
        onChanged();
      } else {
        pedestrianSidewalkPathsBuilder_.setMessage(builderForValue.build());
      }
      laneTypeAttributesOneOfCase_ = 4;
      return this;
    }
    /**
     * <pre>
     *可选，人行道 LaneAttributesSidewalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
     */
    public Builder mergePedestrianSidewalkPaths(road.data.proto.LaneAttributesSidewalk value) {
      if (pedestrianSidewalkPathsBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 4 &&
            laneTypeAttributesOneOf_ != road.data.proto.LaneAttributesSidewalk.getDefaultInstance()) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesSidewalk.newBuilder((road.data.proto.LaneAttributesSidewalk) laneTypeAttributesOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          laneTypeAttributesOneOf_ = value;
        }
        onChanged();
      } else {
        if (laneTypeAttributesOneOfCase_ == 4) {
          pedestrianSidewalkPathsBuilder_.mergeFrom(value);
        }
        pedestrianSidewalkPathsBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 4;
      return this;
    }
    /**
     * <pre>
     *可选，人行道 LaneAttributesSidewalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
     */
    public Builder clearPedestrianSidewalkPaths() {
      if (pedestrianSidewalkPathsBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 4) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
          onChanged();
        }
      } else {
        if (laneTypeAttributesOneOfCase_ == 4) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
        }
        pedestrianSidewalkPathsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，人行道 LaneAttributesSidewalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
     */
    public road.data.proto.LaneAttributesSidewalk.Builder getPedestrianSidewalkPathsBuilder() {
      return getPedestrianSidewalkPathsFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，人行道 LaneAttributesSidewalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
     */
    public road.data.proto.LaneAttributesSidewalkOrBuilder getPedestrianSidewalkPathsOrBuilder() {
      if ((laneTypeAttributesOneOfCase_ == 4) && (pedestrianSidewalkPathsBuilder_ != null)) {
        return pedestrianSidewalkPathsBuilder_.getMessageOrBuilder();
      } else {
        if (laneTypeAttributesOneOfCase_ == 4) {
          return (road.data.proto.LaneAttributesSidewalk) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesSidewalk.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，人行道 LaneAttributesSidewalk 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesSidewalk, road.data.proto.LaneAttributesSidewalk.Builder, road.data.proto.LaneAttributesSidewalkOrBuilder> 
        getPedestrianSidewalkPathsFieldBuilder() {
      if (pedestrianSidewalkPathsBuilder_ == null) {
        if (!(laneTypeAttributesOneOfCase_ == 4)) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesSidewalk.getDefaultInstance();
        }
        pedestrianSidewalkPathsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneAttributesSidewalk, road.data.proto.LaneAttributesSidewalk.Builder, road.data.proto.LaneAttributesSidewalkOrBuilder>(
                (road.data.proto.LaneAttributesSidewalk) laneTypeAttributesOneOf_,
                getParentForChildren(),
                isClean());
        laneTypeAttributesOneOf_ = null;
      }
      laneTypeAttributesOneOfCase_ = 4;
      onChanged();;
      return pedestrianSidewalkPathsBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesBarrier, road.data.proto.LaneAttributesBarrier.Builder, road.data.proto.LaneAttributesBarrierOrBuilder> mediansChannelizationBuilder_;
    /**
     * <pre>
     *可选，中值和通道化 LaneAttributesBarrier 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
     */
    public boolean hasMediansChannelization() {
      return laneTypeAttributesOneOfCase_ == 5;
    }
    /**
     * <pre>
     *可选，中值和通道化 LaneAttributesBarrier 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
     */
    public road.data.proto.LaneAttributesBarrier getMediansChannelization() {
      if (mediansChannelizationBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 5) {
          return (road.data.proto.LaneAttributesBarrier) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesBarrier.getDefaultInstance();
      } else {
        if (laneTypeAttributesOneOfCase_ == 5) {
          return mediansChannelizationBuilder_.getMessage();
        }
        return road.data.proto.LaneAttributesBarrier.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，中值和通道化 LaneAttributesBarrier 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
     */
    public Builder setMediansChannelization(road.data.proto.LaneAttributesBarrier value) {
      if (mediansChannelizationBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        laneTypeAttributesOneOf_ = value;
        onChanged();
      } else {
        mediansChannelizationBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 5;
      return this;
    }
    /**
     * <pre>
     *可选，中值和通道化 LaneAttributesBarrier 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
     */
    public Builder setMediansChannelization(
        road.data.proto.LaneAttributesBarrier.Builder builderForValue) {
      if (mediansChannelizationBuilder_ == null) {
        laneTypeAttributesOneOf_ = builderForValue.build();
        onChanged();
      } else {
        mediansChannelizationBuilder_.setMessage(builderForValue.build());
      }
      laneTypeAttributesOneOfCase_ = 5;
      return this;
    }
    /**
     * <pre>
     *可选，中值和通道化 LaneAttributesBarrier 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
     */
    public Builder mergeMediansChannelization(road.data.proto.LaneAttributesBarrier value) {
      if (mediansChannelizationBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 5 &&
            laneTypeAttributesOneOf_ != road.data.proto.LaneAttributesBarrier.getDefaultInstance()) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesBarrier.newBuilder((road.data.proto.LaneAttributesBarrier) laneTypeAttributesOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          laneTypeAttributesOneOf_ = value;
        }
        onChanged();
      } else {
        if (laneTypeAttributesOneOfCase_ == 5) {
          mediansChannelizationBuilder_.mergeFrom(value);
        }
        mediansChannelizationBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 5;
      return this;
    }
    /**
     * <pre>
     *可选，中值和通道化 LaneAttributesBarrier 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
     */
    public Builder clearMediansChannelization() {
      if (mediansChannelizationBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 5) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
          onChanged();
        }
      } else {
        if (laneTypeAttributesOneOfCase_ == 5) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
        }
        mediansChannelizationBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，中值和通道化 LaneAttributesBarrier 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
     */
    public road.data.proto.LaneAttributesBarrier.Builder getMediansChannelizationBuilder() {
      return getMediansChannelizationFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，中值和通道化 LaneAttributesBarrier 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
     */
    public road.data.proto.LaneAttributesBarrierOrBuilder getMediansChannelizationOrBuilder() {
      if ((laneTypeAttributesOneOfCase_ == 5) && (mediansChannelizationBuilder_ != null)) {
        return mediansChannelizationBuilder_.getMessageOrBuilder();
      } else {
        if (laneTypeAttributesOneOfCase_ == 5) {
          return (road.data.proto.LaneAttributesBarrier) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesBarrier.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，中值和通道化 LaneAttributesBarrier 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesBarrier, road.data.proto.LaneAttributesBarrier.Builder, road.data.proto.LaneAttributesBarrierOrBuilder> 
        getMediansChannelizationFieldBuilder() {
      if (mediansChannelizationBuilder_ == null) {
        if (!(laneTypeAttributesOneOfCase_ == 5)) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesBarrier.getDefaultInstance();
        }
        mediansChannelizationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneAttributesBarrier, road.data.proto.LaneAttributesBarrier.Builder, road.data.proto.LaneAttributesBarrierOrBuilder>(
                (road.data.proto.LaneAttributesBarrier) laneTypeAttributesOneOf_,
                getParentForChildren(),
                isClean());
        laneTypeAttributesOneOf_ = null;
      }
      laneTypeAttributesOneOfCase_ = 5;
      onChanged();;
      return mediansChannelizationBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesStriping, road.data.proto.LaneAttributesStriping.Builder, road.data.proto.LaneAttributesStripingOrBuilder> roadwayMarkingsBuilder_;
    /**
     * <pre>
     *可选，道路标线 LaneAttributesStriping 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
     */
    public boolean hasRoadwayMarkings() {
      return laneTypeAttributesOneOfCase_ == 6;
    }
    /**
     * <pre>
     *可选，道路标线 LaneAttributesStriping 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
     */
    public road.data.proto.LaneAttributesStriping getRoadwayMarkings() {
      if (roadwayMarkingsBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 6) {
          return (road.data.proto.LaneAttributesStriping) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesStriping.getDefaultInstance();
      } else {
        if (laneTypeAttributesOneOfCase_ == 6) {
          return roadwayMarkingsBuilder_.getMessage();
        }
        return road.data.proto.LaneAttributesStriping.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，道路标线 LaneAttributesStriping 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
     */
    public Builder setRoadwayMarkings(road.data.proto.LaneAttributesStriping value) {
      if (roadwayMarkingsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        laneTypeAttributesOneOf_ = value;
        onChanged();
      } else {
        roadwayMarkingsBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 6;
      return this;
    }
    /**
     * <pre>
     *可选，道路标线 LaneAttributesStriping 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
     */
    public Builder setRoadwayMarkings(
        road.data.proto.LaneAttributesStriping.Builder builderForValue) {
      if (roadwayMarkingsBuilder_ == null) {
        laneTypeAttributesOneOf_ = builderForValue.build();
        onChanged();
      } else {
        roadwayMarkingsBuilder_.setMessage(builderForValue.build());
      }
      laneTypeAttributesOneOfCase_ = 6;
      return this;
    }
    /**
     * <pre>
     *可选，道路标线 LaneAttributesStriping 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
     */
    public Builder mergeRoadwayMarkings(road.data.proto.LaneAttributesStriping value) {
      if (roadwayMarkingsBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 6 &&
            laneTypeAttributesOneOf_ != road.data.proto.LaneAttributesStriping.getDefaultInstance()) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesStriping.newBuilder((road.data.proto.LaneAttributesStriping) laneTypeAttributesOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          laneTypeAttributesOneOf_ = value;
        }
        onChanged();
      } else {
        if (laneTypeAttributesOneOfCase_ == 6) {
          roadwayMarkingsBuilder_.mergeFrom(value);
        }
        roadwayMarkingsBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 6;
      return this;
    }
    /**
     * <pre>
     *可选，道路标线 LaneAttributesStriping 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
     */
    public Builder clearRoadwayMarkings() {
      if (roadwayMarkingsBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 6) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
          onChanged();
        }
      } else {
        if (laneTypeAttributesOneOfCase_ == 6) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
        }
        roadwayMarkingsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，道路标线 LaneAttributesStriping 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
     */
    public road.data.proto.LaneAttributesStriping.Builder getRoadwayMarkingsBuilder() {
      return getRoadwayMarkingsFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，道路标线 LaneAttributesStriping 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
     */
    public road.data.proto.LaneAttributesStripingOrBuilder getRoadwayMarkingsOrBuilder() {
      if ((laneTypeAttributesOneOfCase_ == 6) && (roadwayMarkingsBuilder_ != null)) {
        return roadwayMarkingsBuilder_.getMessageOrBuilder();
      } else {
        if (laneTypeAttributesOneOfCase_ == 6) {
          return (road.data.proto.LaneAttributesStriping) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesStriping.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，道路标线 LaneAttributesStriping 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesStriping, road.data.proto.LaneAttributesStriping.Builder, road.data.proto.LaneAttributesStripingOrBuilder> 
        getRoadwayMarkingsFieldBuilder() {
      if (roadwayMarkingsBuilder_ == null) {
        if (!(laneTypeAttributesOneOfCase_ == 6)) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesStriping.getDefaultInstance();
        }
        roadwayMarkingsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneAttributesStriping, road.data.proto.LaneAttributesStriping.Builder, road.data.proto.LaneAttributesStripingOrBuilder>(
                (road.data.proto.LaneAttributesStriping) laneTypeAttributesOneOf_,
                getParentForChildren(),
                isClean());
        laneTypeAttributesOneOf_ = null;
      }
      laneTypeAttributesOneOfCase_ = 6;
      onChanged();;
      return roadwayMarkingsBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesTrackedVehicle, road.data.proto.LaneAttributesTrackedVehicle.Builder, road.data.proto.LaneAttributesTrackedVehicleOrBuilder> trainsAndTrolleysBuilder_;
    /**
     * <pre>
     *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
     */
    public boolean hasTrainsAndTrolleys() {
      return laneTypeAttributesOneOfCase_ == 7;
    }
    /**
     * <pre>
     *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
     */
    public road.data.proto.LaneAttributesTrackedVehicle getTrainsAndTrolleys() {
      if (trainsAndTrolleysBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 7) {
          return (road.data.proto.LaneAttributesTrackedVehicle) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesTrackedVehicle.getDefaultInstance();
      } else {
        if (laneTypeAttributesOneOfCase_ == 7) {
          return trainsAndTrolleysBuilder_.getMessage();
        }
        return road.data.proto.LaneAttributesTrackedVehicle.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
     */
    public Builder setTrainsAndTrolleys(road.data.proto.LaneAttributesTrackedVehicle value) {
      if (trainsAndTrolleysBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        laneTypeAttributesOneOf_ = value;
        onChanged();
      } else {
        trainsAndTrolleysBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 7;
      return this;
    }
    /**
     * <pre>
     *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
     */
    public Builder setTrainsAndTrolleys(
        road.data.proto.LaneAttributesTrackedVehicle.Builder builderForValue) {
      if (trainsAndTrolleysBuilder_ == null) {
        laneTypeAttributesOneOf_ = builderForValue.build();
        onChanged();
      } else {
        trainsAndTrolleysBuilder_.setMessage(builderForValue.build());
      }
      laneTypeAttributesOneOfCase_ = 7;
      return this;
    }
    /**
     * <pre>
     *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
     */
    public Builder mergeTrainsAndTrolleys(road.data.proto.LaneAttributesTrackedVehicle value) {
      if (trainsAndTrolleysBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 7 &&
            laneTypeAttributesOneOf_ != road.data.proto.LaneAttributesTrackedVehicle.getDefaultInstance()) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesTrackedVehicle.newBuilder((road.data.proto.LaneAttributesTrackedVehicle) laneTypeAttributesOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          laneTypeAttributesOneOf_ = value;
        }
        onChanged();
      } else {
        if (laneTypeAttributesOneOfCase_ == 7) {
          trainsAndTrolleysBuilder_.mergeFrom(value);
        }
        trainsAndTrolleysBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 7;
      return this;
    }
    /**
     * <pre>
     *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
     */
    public Builder clearTrainsAndTrolleys() {
      if (trainsAndTrolleysBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 7) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
          onChanged();
        }
      } else {
        if (laneTypeAttributesOneOfCase_ == 7) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
        }
        trainsAndTrolleysBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
     */
    public road.data.proto.LaneAttributesTrackedVehicle.Builder getTrainsAndTrolleysBuilder() {
      return getTrainsAndTrolleysFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
     */
    public road.data.proto.LaneAttributesTrackedVehicleOrBuilder getTrainsAndTrolleysOrBuilder() {
      if ((laneTypeAttributesOneOfCase_ == 7) && (trainsAndTrolleysBuilder_ != null)) {
        return trainsAndTrolleysBuilder_.getMessageOrBuilder();
      } else {
        if (laneTypeAttributesOneOfCase_ == 7) {
          return (road.data.proto.LaneAttributesTrackedVehicle) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesTrackedVehicle.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesTrackedVehicle, road.data.proto.LaneAttributesTrackedVehicle.Builder, road.data.proto.LaneAttributesTrackedVehicleOrBuilder> 
        getTrainsAndTrolleysFieldBuilder() {
      if (trainsAndTrolleysBuilder_ == null) {
        if (!(laneTypeAttributesOneOfCase_ == 7)) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesTrackedVehicle.getDefaultInstance();
        }
        trainsAndTrolleysBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneAttributesTrackedVehicle, road.data.proto.LaneAttributesTrackedVehicle.Builder, road.data.proto.LaneAttributesTrackedVehicleOrBuilder>(
                (road.data.proto.LaneAttributesTrackedVehicle) laneTypeAttributesOneOf_,
                getParentForChildren(),
                isClean());
        laneTypeAttributesOneOf_ = null;
      }
      laneTypeAttributesOneOfCase_ = 7;
      onChanged();;
      return trainsAndTrolleysBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesParking, road.data.proto.LaneAttributesParking.Builder, road.data.proto.LaneAttributesParkingOrBuilder> parkingAndStoppingLanesBuilder_;
    /**
     * <pre>
     *可选，停车和停车车道 LaneAttributesParking 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
     */
    public boolean hasParkingAndStoppingLanes() {
      return laneTypeAttributesOneOfCase_ == 8;
    }
    /**
     * <pre>
     *可选，停车和停车车道 LaneAttributesParking 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
     */
    public road.data.proto.LaneAttributesParking getParkingAndStoppingLanes() {
      if (parkingAndStoppingLanesBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 8) {
          return (road.data.proto.LaneAttributesParking) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesParking.getDefaultInstance();
      } else {
        if (laneTypeAttributesOneOfCase_ == 8) {
          return parkingAndStoppingLanesBuilder_.getMessage();
        }
        return road.data.proto.LaneAttributesParking.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，停车和停车车道 LaneAttributesParking 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
     */
    public Builder setParkingAndStoppingLanes(road.data.proto.LaneAttributesParking value) {
      if (parkingAndStoppingLanesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        laneTypeAttributesOneOf_ = value;
        onChanged();
      } else {
        parkingAndStoppingLanesBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 8;
      return this;
    }
    /**
     * <pre>
     *可选，停车和停车车道 LaneAttributesParking 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
     */
    public Builder setParkingAndStoppingLanes(
        road.data.proto.LaneAttributesParking.Builder builderForValue) {
      if (parkingAndStoppingLanesBuilder_ == null) {
        laneTypeAttributesOneOf_ = builderForValue.build();
        onChanged();
      } else {
        parkingAndStoppingLanesBuilder_.setMessage(builderForValue.build());
      }
      laneTypeAttributesOneOfCase_ = 8;
      return this;
    }
    /**
     * <pre>
     *可选，停车和停车车道 LaneAttributesParking 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
     */
    public Builder mergeParkingAndStoppingLanes(road.data.proto.LaneAttributesParking value) {
      if (parkingAndStoppingLanesBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 8 &&
            laneTypeAttributesOneOf_ != road.data.proto.LaneAttributesParking.getDefaultInstance()) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesParking.newBuilder((road.data.proto.LaneAttributesParking) laneTypeAttributesOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          laneTypeAttributesOneOf_ = value;
        }
        onChanged();
      } else {
        if (laneTypeAttributesOneOfCase_ == 8) {
          parkingAndStoppingLanesBuilder_.mergeFrom(value);
        }
        parkingAndStoppingLanesBuilder_.setMessage(value);
      }
      laneTypeAttributesOneOfCase_ = 8;
      return this;
    }
    /**
     * <pre>
     *可选，停车和停车车道 LaneAttributesParking 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
     */
    public Builder clearParkingAndStoppingLanes() {
      if (parkingAndStoppingLanesBuilder_ == null) {
        if (laneTypeAttributesOneOfCase_ == 8) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
          onChanged();
        }
      } else {
        if (laneTypeAttributesOneOfCase_ == 8) {
          laneTypeAttributesOneOfCase_ = 0;
          laneTypeAttributesOneOf_ = null;
        }
        parkingAndStoppingLanesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，停车和停车车道 LaneAttributesParking 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
     */
    public road.data.proto.LaneAttributesParking.Builder getParkingAndStoppingLanesBuilder() {
      return getParkingAndStoppingLanesFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，停车和停车车道 LaneAttributesParking 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
     */
    public road.data.proto.LaneAttributesParkingOrBuilder getParkingAndStoppingLanesOrBuilder() {
      if ((laneTypeAttributesOneOfCase_ == 8) && (parkingAndStoppingLanesBuilder_ != null)) {
        return parkingAndStoppingLanesBuilder_.getMessageOrBuilder();
      } else {
        if (laneTypeAttributesOneOfCase_ == 8) {
          return (road.data.proto.LaneAttributesParking) laneTypeAttributesOneOf_;
        }
        return road.data.proto.LaneAttributesParking.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，停车和停车车道 LaneAttributesParking 位串
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneAttributesParking, road.data.proto.LaneAttributesParking.Builder, road.data.proto.LaneAttributesParkingOrBuilder> 
        getParkingAndStoppingLanesFieldBuilder() {
      if (parkingAndStoppingLanesBuilder_ == null) {
        if (!(laneTypeAttributesOneOfCase_ == 8)) {
          laneTypeAttributesOneOf_ = road.data.proto.LaneAttributesParking.getDefaultInstance();
        }
        parkingAndStoppingLanesBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneAttributesParking, road.data.proto.LaneAttributesParking.Builder, road.data.proto.LaneAttributesParkingOrBuilder>(
                (road.data.proto.LaneAttributesParking) laneTypeAttributesOneOf_,
                getParentForChildren(),
                isClean());
        laneTypeAttributesOneOf_ = null;
      }
      laneTypeAttributesOneOfCase_ = 8;
      onChanged();;
      return parkingAndStoppingLanesBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.LaneTypeAttributes)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.LaneTypeAttributes)
  private static final road.data.proto.LaneTypeAttributes DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.LaneTypeAttributes();
  }

  public static road.data.proto.LaneTypeAttributes getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LaneTypeAttributes>
      PARSER = new com.google.protobuf.AbstractParser<LaneTypeAttributes>() {
    @java.lang.Override
    public LaneTypeAttributes parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new LaneTypeAttributes(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<LaneTypeAttributes> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LaneTypeAttributes> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.LaneTypeAttributes getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

