// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneTypeAttributesOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneTypeAttributes)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *可选，机动车道 LaneAttributesVehicle 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
   */
  boolean hasMotorVehicleLanes();
  /**
   * <pre>
   *可选，机动车道 LaneAttributesVehicle 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
   */
  road.data.proto.LaneAttributesVehicle getMotorVehicleLanes();
  /**
   * <pre>
   *可选，机动车道 LaneAttributesVehicle 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesVehicle motorVehicleLanes = 1;</code>
   */
  road.data.proto.LaneAttributesVehicleOrBuilder getMotorVehicleLanesOrBuilder();

  /**
   * <pre>
   *可选，人行横道 LaneAttributesCrosswalk 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
   */
  boolean hasPedestrianCrosswalks();
  /**
   * <pre>
   *可选，人行横道 LaneAttributesCrosswalk 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
   */
  road.data.proto.LaneAttributesCrosswalk getPedestrianCrosswalks();
  /**
   * <pre>
   *可选，人行横道 LaneAttributesCrosswalk 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesCrosswalk pedestrianCrosswalks = 2;</code>
   */
  road.data.proto.LaneAttributesCrosswalkOrBuilder getPedestrianCrosswalksOrBuilder();

  /**
   * <pre>
   *可选，自行车道    LaneAttributesBike  位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
   */
  boolean hasBikeLanes();
  /**
   * <pre>
   *可选，自行车道    LaneAttributesBike  位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
   */
  road.data.proto.LaneAttributesBike getBikeLanes();
  /**
   * <pre>
   *可选，自行车道    LaneAttributesBike  位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesBike bikeLanes = 3;</code>
   */
  road.data.proto.LaneAttributesBikeOrBuilder getBikeLanesOrBuilder();

  /**
   * <pre>
   *可选，人行道 LaneAttributesSidewalk 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
   */
  boolean hasPedestrianSidewalkPaths();
  /**
   * <pre>
   *可选，人行道 LaneAttributesSidewalk 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
   */
  road.data.proto.LaneAttributesSidewalk getPedestrianSidewalkPaths();
  /**
   * <pre>
   *可选，人行道 LaneAttributesSidewalk 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesSidewalk pedestrianSidewalkPaths = 4;</code>
   */
  road.data.proto.LaneAttributesSidewalkOrBuilder getPedestrianSidewalkPathsOrBuilder();

  /**
   * <pre>
   *可选，中值和通道化 LaneAttributesBarrier 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
   */
  boolean hasMediansChannelization();
  /**
   * <pre>
   *可选，中值和通道化 LaneAttributesBarrier 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
   */
  road.data.proto.LaneAttributesBarrier getMediansChannelization();
  /**
   * <pre>
   *可选，中值和通道化 LaneAttributesBarrier 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesBarrier mediansChannelization = 5;</code>
   */
  road.data.proto.LaneAttributesBarrierOrBuilder getMediansChannelizationOrBuilder();

  /**
   * <pre>
   *可选，道路标线 LaneAttributesStriping 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
   */
  boolean hasRoadwayMarkings();
  /**
   * <pre>
   *可选，道路标线 LaneAttributesStriping 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
   */
  road.data.proto.LaneAttributesStriping getRoadwayMarkings();
  /**
   * <pre>
   *可选，道路标线 LaneAttributesStriping 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesStriping roadwayMarkings = 6;</code>
   */
  road.data.proto.LaneAttributesStripingOrBuilder getRoadwayMarkingsOrBuilder();

  /**
   * <pre>
   *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
   */
  boolean hasTrainsAndTrolleys();
  /**
   * <pre>
   *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
   */
  road.data.proto.LaneAttributesTrackedVehicle getTrainsAndTrolleys();
  /**
   * <pre>
   *可选，火车和手推车 LaneAttributesTrackedVehicle 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesTrackedVehicle trainsAndTrolleys = 7;</code>
   */
  road.data.proto.LaneAttributesTrackedVehicleOrBuilder getTrainsAndTrolleysOrBuilder();

  /**
   * <pre>
   *可选，停车和停车车道 LaneAttributesParking 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
   */
  boolean hasParkingAndStoppingLanes();
  /**
   * <pre>
   *可选，停车和停车车道 LaneAttributesParking 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
   */
  road.data.proto.LaneAttributesParking getParkingAndStoppingLanes();
  /**
   * <pre>
   *可选，停车和停车车道 LaneAttributesParking 位串
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneAttributesParking parkingAndStoppingLanes = 8;</code>
   */
  road.data.proto.LaneAttributesParkingOrBuilder getParkingAndStoppingLanesOrBuilder();

  public road.data.proto.LaneTypeAttributes.LaneTypeAttributesOneOfCase getLaneTypeAttributesOneOfCase();
}
