// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LaneTypeOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LaneType)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *车道本身所属类别的序号
   * </pre>
   *
   * <code>uint32 choiceId = 1;</code>
   */
  int getChoiceId();

  /**
   * <pre>
   *10: LaneAttributes-Vehicle车辆行驶车道
   *20: LaneAttributes-Crosswalk人行横道属性
   *30: LaneAttributes-Bike自行车道的属性
   *40: LaneAttributes-Sidewalk人行道属性
   *50: LaneAttributes-Barrier车道隔断离的属性
   *60: LaneAttributes-Striping标线车道
   *70: LaneAttributes-TrackedVehicle轨道车辆车道
   *80: LaneAttributes-Parking停车车道的属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
   */
  boolean hasValue();
  /**
   * <pre>
   *10: LaneAttributes-Vehicle车辆行驶车道
   *20: LaneAttributes-Crosswalk人行横道属性
   *30: LaneAttributes-Bike自行车道的属性
   *40: LaneAttributes-Sidewalk人行道属性
   *50: LaneAttributes-Barrier车道隔断离的属性
   *60: LaneAttributes-Striping标线车道
   *70: LaneAttributes-TrackedVehicle轨道车辆车道
   *80: LaneAttributes-Parking停车车道的属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
   */
  road.data.proto.LaneTypeAttributes getValue();
  /**
   * <pre>
   *10: LaneAttributes-Vehicle车辆行驶车道
   *20: LaneAttributes-Crosswalk人行横道属性
   *30: LaneAttributes-Bike自行车道的属性
   *40: LaneAttributes-Sidewalk人行道属性
   *50: LaneAttributes-Barrier车道隔断离的属性
   *60: LaneAttributes-Striping标线车道
   *70: LaneAttributes-TrackedVehicle轨道车辆车道
   *80: LaneAttributes-Parking停车车道的属性
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneTypeAttributes value = 2;</code>
   */
  road.data.proto.LaneTypeAttributesOrBuilder getValueOrBuilder();
}
