// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *路段Link      
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.Link}
 */
public  final class Link extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.Link)
    LinkOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Link.newBuilder() to construct.
  private Link(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Link() {
    name_ = "";
    speedLimits_ = java.util.Collections.emptyList();
    points_ = java.util.Collections.emptyList();
    movements_ = java.util.Collections.emptyList();
    lanes_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Link();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Link(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 18: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (upstreamNodeId_ != null) {
              subBuilder = upstreamNodeId_.toBuilder();
            }
            upstreamNodeId_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(upstreamNodeId_);
              upstreamNodeId_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              speedLimits_ = new java.util.ArrayList<road.data.proto.RegulatorySpeedLimit>();
              mutable_bitField0_ |= 0x00000001;
            }
            speedLimits_.add(
                input.readMessage(road.data.proto.RegulatorySpeedLimit.parser(), extensionRegistry));
            break;
          }
          case 32: {

            linkWidth_ = input.readUInt32();
            break;
          }
          case 42: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              points_ = new java.util.ArrayList<road.data.proto.Position3D>();
              mutable_bitField0_ |= 0x00000002;
            }
            points_.add(
                input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry));
            break;
          }
          case 50: {
            if (!((mutable_bitField0_ & 0x00000004) != 0)) {
              movements_ = new java.util.ArrayList<road.data.proto.Movement>();
              mutable_bitField0_ |= 0x00000004;
            }
            movements_.add(
                input.readMessage(road.data.proto.Movement.parser(), extensionRegistry));
            break;
          }
          case 58: {
            if (!((mutable_bitField0_ & 0x00000008) != 0)) {
              lanes_ = new java.util.ArrayList<road.data.proto.Lane>();
              mutable_bitField0_ |= 0x00000008;
            }
            lanes_.add(
                input.readMessage(road.data.proto.Lane.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        speedLimits_ = java.util.Collections.unmodifiableList(speedLimits_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        points_ = java.util.Collections.unmodifiableList(points_);
      }
      if (((mutable_bitField0_ & 0x00000004) != 0)) {
        movements_ = java.util.Collections.unmodifiableList(movements_);
      }
      if (((mutable_bitField0_ & 0x00000008) != 0)) {
        lanes_ = java.util.Collections.unmodifiableList(lanes_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Link_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Link_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.Link.class, road.data.proto.Link.Builder.class);
  }

  public static final int NAME_FIELD_NUMBER = 1;
  private volatile java.lang.Object name_;
  /**
   * <pre>
   * 可选，名称
   * </pre>
   *
   * <code>string name = 1;</code>
   */
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 可选，名称
   * </pre>
   *
   * <code>string name = 1;</code>
   */
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int UPSTREAMNODEID_FIELD_NUMBER = 2;
  private road.data.proto.NodeReferenceId upstreamNodeId_;
  /**
   * <pre>
   * 上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
   */
  public boolean hasUpstreamNodeId() {
    return upstreamNodeId_ != null;
  }
  /**
   * <pre>
   * 上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
   */
  public road.data.proto.NodeReferenceId getUpstreamNodeId() {
    return upstreamNodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
  }
  /**
   * <pre>
   * 上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder() {
    return getUpstreamNodeId();
  }

  public static final int SPEEDLIMITS_FIELD_NUMBER = 3;
  private java.util.List<road.data.proto.RegulatorySpeedLimit> speedLimits_;
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  public java.util.List<road.data.proto.RegulatorySpeedLimit> getSpeedLimitsList() {
    return speedLimits_;
  }
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  public java.util.List<? extends road.data.proto.RegulatorySpeedLimitOrBuilder> 
      getSpeedLimitsOrBuilderList() {
    return speedLimits_;
  }
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  public int getSpeedLimitsCount() {
    return speedLimits_.size();
  }
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  public road.data.proto.RegulatorySpeedLimit getSpeedLimits(int index) {
    return speedLimits_.get(index);
  }
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  public road.data.proto.RegulatorySpeedLimitOrBuilder getSpeedLimitsOrBuilder(
      int index) {
    return speedLimits_.get(index);
  }

  public static final int LINKWIDTH_FIELD_NUMBER = 4;
  private int linkWidth_;
  /**
   * <pre>
   * 可选，车道宽度，分辨率为 1cm
   * </pre>
   *
   * <code>uint32 linkWidth = 4;</code>
   */
  public int getLinkWidth() {
    return linkWidth_;
  }

  public static final int POINTS_FIELD_NUMBER = 5;
  private java.util.List<road.data.proto.Position3D> points_;
  /**
   * <pre>
   * 可选，此路段中心线信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
   */
  public java.util.List<road.data.proto.Position3D> getPointsList() {
    return points_;
  }
  /**
   * <pre>
   * 可选，此路段中心线信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
   */
  public java.util.List<? extends road.data.proto.Position3DOrBuilder> 
      getPointsOrBuilderList() {
    return points_;
  }
  /**
   * <pre>
   * 可选，此路段中心线信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
   */
  public int getPointsCount() {
    return points_.size();
  }
  /**
   * <pre>
   * 可选，此路段中心线信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
   */
  public road.data.proto.Position3D getPoints(int index) {
    return points_.get(index);
  }
  /**
   * <pre>
   * 可选，此路段中心线信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
   */
  public road.data.proto.Position3DOrBuilder getPointsOrBuilder(
      int index) {
    return points_.get(index);
  }

  public static final int MOVEMENTS_FIELD_NUMBER = 6;
  private java.util.List<road.data.proto.Movement> movements_;
  /**
   * <pre>
   *可选，该路段转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
   */
  public java.util.List<road.data.proto.Movement> getMovementsList() {
    return movements_;
  }
  /**
   * <pre>
   *可选，该路段转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
   */
  public java.util.List<? extends road.data.proto.MovementOrBuilder> 
      getMovementsOrBuilderList() {
    return movements_;
  }
  /**
   * <pre>
   *可选，该路段转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
   */
  public int getMovementsCount() {
    return movements_.size();
  }
  /**
   * <pre>
   *可选，该路段转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
   */
  public road.data.proto.Movement getMovements(int index) {
    return movements_.get(index);
  }
  /**
   * <pre>
   *可选，该路段转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
   */
  public road.data.proto.MovementOrBuilder getMovementsOrBuilder(
      int index) {
    return movements_.get(index);
  }

  public static final int LANES_FIELD_NUMBER = 7;
  private java.util.List<road.data.proto.Lane> lanes_;
  /**
   * <pre>
   * 定义车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
   */
  public java.util.List<road.data.proto.Lane> getLanesList() {
    return lanes_;
  }
  /**
   * <pre>
   * 定义车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
   */
  public java.util.List<? extends road.data.proto.LaneOrBuilder> 
      getLanesOrBuilderList() {
    return lanes_;
  }
  /**
   * <pre>
   * 定义车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
   */
  public int getLanesCount() {
    return lanes_.size();
  }
  /**
   * <pre>
   * 定义车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
   */
  public road.data.proto.Lane getLanes(int index) {
    return lanes_.get(index);
  }
  /**
   * <pre>
   * 定义车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
   */
  public road.data.proto.LaneOrBuilder getLanesOrBuilder(
      int index) {
    return lanes_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
    }
    if (upstreamNodeId_ != null) {
      output.writeMessage(2, getUpstreamNodeId());
    }
    for (int i = 0; i < speedLimits_.size(); i++) {
      output.writeMessage(3, speedLimits_.get(i));
    }
    if (linkWidth_ != 0) {
      output.writeUInt32(4, linkWidth_);
    }
    for (int i = 0; i < points_.size(); i++) {
      output.writeMessage(5, points_.get(i));
    }
    for (int i = 0; i < movements_.size(); i++) {
      output.writeMessage(6, movements_.get(i));
    }
    for (int i = 0; i < lanes_.size(); i++) {
      output.writeMessage(7, lanes_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
    }
    if (upstreamNodeId_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getUpstreamNodeId());
    }
    for (int i = 0; i < speedLimits_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, speedLimits_.get(i));
    }
    if (linkWidth_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, linkWidth_);
    }
    for (int i = 0; i < points_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, points_.get(i));
    }
    for (int i = 0; i < movements_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, movements_.get(i));
    }
    for (int i = 0; i < lanes_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, lanes_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.Link)) {
      return super.equals(obj);
    }
    road.data.proto.Link other = (road.data.proto.Link) obj;

    if (!getName()
        .equals(other.getName())) return false;
    if (hasUpstreamNodeId() != other.hasUpstreamNodeId()) return false;
    if (hasUpstreamNodeId()) {
      if (!getUpstreamNodeId()
          .equals(other.getUpstreamNodeId())) return false;
    }
    if (!getSpeedLimitsList()
        .equals(other.getSpeedLimitsList())) return false;
    if (getLinkWidth()
        != other.getLinkWidth()) return false;
    if (!getPointsList()
        .equals(other.getPointsList())) return false;
    if (!getMovementsList()
        .equals(other.getMovementsList())) return false;
    if (!getLanesList()
        .equals(other.getLanesList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    if (hasUpstreamNodeId()) {
      hash = (37 * hash) + UPSTREAMNODEID_FIELD_NUMBER;
      hash = (53 * hash) + getUpstreamNodeId().hashCode();
    }
    if (getSpeedLimitsCount() > 0) {
      hash = (37 * hash) + SPEEDLIMITS_FIELD_NUMBER;
      hash = (53 * hash) + getSpeedLimitsList().hashCode();
    }
    hash = (37 * hash) + LINKWIDTH_FIELD_NUMBER;
    hash = (53 * hash) + getLinkWidth();
    if (getPointsCount() > 0) {
      hash = (37 * hash) + POINTS_FIELD_NUMBER;
      hash = (53 * hash) + getPointsList().hashCode();
    }
    if (getMovementsCount() > 0) {
      hash = (37 * hash) + MOVEMENTS_FIELD_NUMBER;
      hash = (53 * hash) + getMovementsList().hashCode();
    }
    if (getLanesCount() > 0) {
      hash = (37 * hash) + LANES_FIELD_NUMBER;
      hash = (53 * hash) + getLanesList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.Link parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Link parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Link parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Link parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Link parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Link parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Link parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.Link parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.Link parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.Link parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.Link parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.Link parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.Link prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *路段Link      
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.Link}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.Link)
      road.data.proto.LinkOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Link_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Link_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.Link.class, road.data.proto.Link.Builder.class);
    }

    // Construct using road.data.proto.Link.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getSpeedLimitsFieldBuilder();
        getPointsFieldBuilder();
        getMovementsFieldBuilder();
        getLanesFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      name_ = "";

      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = null;
      } else {
        upstreamNodeId_ = null;
        upstreamNodeIdBuilder_ = null;
      }
      if (speedLimitsBuilder_ == null) {
        speedLimits_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        speedLimitsBuilder_.clear();
      }
      linkWidth_ = 0;

      if (pointsBuilder_ == null) {
        points_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        pointsBuilder_.clear();
      }
      if (movementsBuilder_ == null) {
        movements_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
      } else {
        movementsBuilder_.clear();
      }
      if (lanesBuilder_ == null) {
        lanes_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
      } else {
        lanesBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Link_descriptor;
    }

    @java.lang.Override
    public road.data.proto.Link getDefaultInstanceForType() {
      return road.data.proto.Link.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.Link build() {
      road.data.proto.Link result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.Link buildPartial() {
      road.data.proto.Link result = new road.data.proto.Link(this);
      int from_bitField0_ = bitField0_;
      result.name_ = name_;
      if (upstreamNodeIdBuilder_ == null) {
        result.upstreamNodeId_ = upstreamNodeId_;
      } else {
        result.upstreamNodeId_ = upstreamNodeIdBuilder_.build();
      }
      if (speedLimitsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          speedLimits_ = java.util.Collections.unmodifiableList(speedLimits_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.speedLimits_ = speedLimits_;
      } else {
        result.speedLimits_ = speedLimitsBuilder_.build();
      }
      result.linkWidth_ = linkWidth_;
      if (pointsBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          points_ = java.util.Collections.unmodifiableList(points_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.points_ = points_;
      } else {
        result.points_ = pointsBuilder_.build();
      }
      if (movementsBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          movements_ = java.util.Collections.unmodifiableList(movements_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.movements_ = movements_;
      } else {
        result.movements_ = movementsBuilder_.build();
      }
      if (lanesBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          lanes_ = java.util.Collections.unmodifiableList(lanes_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.lanes_ = lanes_;
      } else {
        result.lanes_ = lanesBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.Link) {
        return mergeFrom((road.data.proto.Link)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.Link other) {
      if (other == road.data.proto.Link.getDefaultInstance()) return this;
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (other.hasUpstreamNodeId()) {
        mergeUpstreamNodeId(other.getUpstreamNodeId());
      }
      if (speedLimitsBuilder_ == null) {
        if (!other.speedLimits_.isEmpty()) {
          if (speedLimits_.isEmpty()) {
            speedLimits_ = other.speedLimits_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureSpeedLimitsIsMutable();
            speedLimits_.addAll(other.speedLimits_);
          }
          onChanged();
        }
      } else {
        if (!other.speedLimits_.isEmpty()) {
          if (speedLimitsBuilder_.isEmpty()) {
            speedLimitsBuilder_.dispose();
            speedLimitsBuilder_ = null;
            speedLimits_ = other.speedLimits_;
            bitField0_ = (bitField0_ & ~0x00000001);
            speedLimitsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getSpeedLimitsFieldBuilder() : null;
          } else {
            speedLimitsBuilder_.addAllMessages(other.speedLimits_);
          }
        }
      }
      if (other.getLinkWidth() != 0) {
        setLinkWidth(other.getLinkWidth());
      }
      if (pointsBuilder_ == null) {
        if (!other.points_.isEmpty()) {
          if (points_.isEmpty()) {
            points_ = other.points_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensurePointsIsMutable();
            points_.addAll(other.points_);
          }
          onChanged();
        }
      } else {
        if (!other.points_.isEmpty()) {
          if (pointsBuilder_.isEmpty()) {
            pointsBuilder_.dispose();
            pointsBuilder_ = null;
            points_ = other.points_;
            bitField0_ = (bitField0_ & ~0x00000002);
            pointsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getPointsFieldBuilder() : null;
          } else {
            pointsBuilder_.addAllMessages(other.points_);
          }
        }
      }
      if (movementsBuilder_ == null) {
        if (!other.movements_.isEmpty()) {
          if (movements_.isEmpty()) {
            movements_ = other.movements_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureMovementsIsMutable();
            movements_.addAll(other.movements_);
          }
          onChanged();
        }
      } else {
        if (!other.movements_.isEmpty()) {
          if (movementsBuilder_.isEmpty()) {
            movementsBuilder_.dispose();
            movementsBuilder_ = null;
            movements_ = other.movements_;
            bitField0_ = (bitField0_ & ~0x00000004);
            movementsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getMovementsFieldBuilder() : null;
          } else {
            movementsBuilder_.addAllMessages(other.movements_);
          }
        }
      }
      if (lanesBuilder_ == null) {
        if (!other.lanes_.isEmpty()) {
          if (lanes_.isEmpty()) {
            lanes_ = other.lanes_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureLanesIsMutable();
            lanes_.addAll(other.lanes_);
          }
          onChanged();
        }
      } else {
        if (!other.lanes_.isEmpty()) {
          if (lanesBuilder_.isEmpty()) {
            lanesBuilder_.dispose();
            lanesBuilder_ = null;
            lanes_ = other.lanes_;
            bitField0_ = (bitField0_ & ~0x00000008);
            lanesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getLanesFieldBuilder() : null;
          } else {
            lanesBuilder_.addAllMessages(other.lanes_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.Link parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.Link) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object name_ = "";
    /**
     * <pre>
     * 可选，名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 可选，名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 可选，名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private road.data.proto.NodeReferenceId upstreamNodeId_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> upstreamNodeIdBuilder_;
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public boolean hasUpstreamNodeId() {
      return upstreamNodeIdBuilder_ != null || upstreamNodeId_ != null;
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public road.data.proto.NodeReferenceId getUpstreamNodeId() {
      if (upstreamNodeIdBuilder_ == null) {
        return upstreamNodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
      } else {
        return upstreamNodeIdBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public Builder setUpstreamNodeId(road.data.proto.NodeReferenceId value) {
      if (upstreamNodeIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        upstreamNodeId_ = value;
        onChanged();
      } else {
        upstreamNodeIdBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public Builder setUpstreamNodeId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = builderForValue.build();
        onChanged();
      } else {
        upstreamNodeIdBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public Builder mergeUpstreamNodeId(road.data.proto.NodeReferenceId value) {
      if (upstreamNodeIdBuilder_ == null) {
        if (upstreamNodeId_ != null) {
          upstreamNodeId_ =
            road.data.proto.NodeReferenceId.newBuilder(upstreamNodeId_).mergeFrom(value).buildPartial();
        } else {
          upstreamNodeId_ = value;
        }
        onChanged();
      } else {
        upstreamNodeIdBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public Builder clearUpstreamNodeId() {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = null;
        onChanged();
      } else {
        upstreamNodeId_ = null;
        upstreamNodeIdBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getUpstreamNodeIdBuilder() {
      
      onChanged();
      return getUpstreamNodeIdFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder() {
      if (upstreamNodeIdBuilder_ != null) {
        return upstreamNodeIdBuilder_.getMessageOrBuilder();
      } else {
        return upstreamNodeId_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
      }
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getUpstreamNodeIdFieldBuilder() {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getUpstreamNodeId(),
                getParentForChildren(),
                isClean());
        upstreamNodeId_ = null;
      }
      return upstreamNodeIdBuilder_;
    }

    private java.util.List<road.data.proto.RegulatorySpeedLimit> speedLimits_ =
      java.util.Collections.emptyList();
    private void ensureSpeedLimitsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        speedLimits_ = new java.util.ArrayList<road.data.proto.RegulatorySpeedLimit>(speedLimits_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RegulatorySpeedLimit, road.data.proto.RegulatorySpeedLimit.Builder, road.data.proto.RegulatorySpeedLimitOrBuilder> speedLimitsBuilder_;

    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public java.util.List<road.data.proto.RegulatorySpeedLimit> getSpeedLimitsList() {
      if (speedLimitsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(speedLimits_);
      } else {
        return speedLimitsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public int getSpeedLimitsCount() {
      if (speedLimitsBuilder_ == null) {
        return speedLimits_.size();
      } else {
        return speedLimitsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public road.data.proto.RegulatorySpeedLimit getSpeedLimits(int index) {
      if (speedLimitsBuilder_ == null) {
        return speedLimits_.get(index);
      } else {
        return speedLimitsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder setSpeedLimits(
        int index, road.data.proto.RegulatorySpeedLimit value) {
      if (speedLimitsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSpeedLimitsIsMutable();
        speedLimits_.set(index, value);
        onChanged();
      } else {
        speedLimitsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder setSpeedLimits(
        int index, road.data.proto.RegulatorySpeedLimit.Builder builderForValue) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        speedLimits_.set(index, builderForValue.build());
        onChanged();
      } else {
        speedLimitsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder addSpeedLimits(road.data.proto.RegulatorySpeedLimit value) {
      if (speedLimitsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSpeedLimitsIsMutable();
        speedLimits_.add(value);
        onChanged();
      } else {
        speedLimitsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder addSpeedLimits(
        int index, road.data.proto.RegulatorySpeedLimit value) {
      if (speedLimitsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSpeedLimitsIsMutable();
        speedLimits_.add(index, value);
        onChanged();
      } else {
        speedLimitsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder addSpeedLimits(
        road.data.proto.RegulatorySpeedLimit.Builder builderForValue) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        speedLimits_.add(builderForValue.build());
        onChanged();
      } else {
        speedLimitsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder addSpeedLimits(
        int index, road.data.proto.RegulatorySpeedLimit.Builder builderForValue) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        speedLimits_.add(index, builderForValue.build());
        onChanged();
      } else {
        speedLimitsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder addAllSpeedLimits(
        java.lang.Iterable<? extends road.data.proto.RegulatorySpeedLimit> values) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, speedLimits_);
        onChanged();
      } else {
        speedLimitsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder clearSpeedLimits() {
      if (speedLimitsBuilder_ == null) {
        speedLimits_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        speedLimitsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder removeSpeedLimits(int index) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        speedLimits_.remove(index);
        onChanged();
      } else {
        speedLimitsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public road.data.proto.RegulatorySpeedLimit.Builder getSpeedLimitsBuilder(
        int index) {
      return getSpeedLimitsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public road.data.proto.RegulatorySpeedLimitOrBuilder getSpeedLimitsOrBuilder(
        int index) {
      if (speedLimitsBuilder_ == null) {
        return speedLimits_.get(index);  } else {
        return speedLimitsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public java.util.List<? extends road.data.proto.RegulatorySpeedLimitOrBuilder> 
         getSpeedLimitsOrBuilderList() {
      if (speedLimitsBuilder_ != null) {
        return speedLimitsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(speedLimits_);
      }
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public road.data.proto.RegulatorySpeedLimit.Builder addSpeedLimitsBuilder() {
      return getSpeedLimitsFieldBuilder().addBuilder(
          road.data.proto.RegulatorySpeedLimit.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public road.data.proto.RegulatorySpeedLimit.Builder addSpeedLimitsBuilder(
        int index) {
      return getSpeedLimitsFieldBuilder().addBuilder(
          index, road.data.proto.RegulatorySpeedLimit.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public java.util.List<road.data.proto.RegulatorySpeedLimit.Builder> 
         getSpeedLimitsBuilderList() {
      return getSpeedLimitsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RegulatorySpeedLimit, road.data.proto.RegulatorySpeedLimit.Builder, road.data.proto.RegulatorySpeedLimitOrBuilder> 
        getSpeedLimitsFieldBuilder() {
      if (speedLimitsBuilder_ == null) {
        speedLimitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.RegulatorySpeedLimit, road.data.proto.RegulatorySpeedLimit.Builder, road.data.proto.RegulatorySpeedLimitOrBuilder>(
                speedLimits_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        speedLimits_ = null;
      }
      return speedLimitsBuilder_;
    }

    private int linkWidth_ ;
    /**
     * <pre>
     * 可选，车道宽度，分辨率为 1cm
     * </pre>
     *
     * <code>uint32 linkWidth = 4;</code>
     */
    public int getLinkWidth() {
      return linkWidth_;
    }
    /**
     * <pre>
     * 可选，车道宽度，分辨率为 1cm
     * </pre>
     *
     * <code>uint32 linkWidth = 4;</code>
     */
    public Builder setLinkWidth(int value) {
      
      linkWidth_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，车道宽度，分辨率为 1cm
     * </pre>
     *
     * <code>uint32 linkWidth = 4;</code>
     */
    public Builder clearLinkWidth() {
      
      linkWidth_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.Position3D> points_ =
      java.util.Collections.emptyList();
    private void ensurePointsIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        points_ = new java.util.ArrayList<road.data.proto.Position3D>(points_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> pointsBuilder_;

    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public java.util.List<road.data.proto.Position3D> getPointsList() {
      if (pointsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(points_);
      } else {
        return pointsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public int getPointsCount() {
      if (pointsBuilder_ == null) {
        return points_.size();
      } else {
        return pointsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public road.data.proto.Position3D getPoints(int index) {
      if (pointsBuilder_ == null) {
        return points_.get(index);
      } else {
        return pointsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public Builder setPoints(
        int index, road.data.proto.Position3D value) {
      if (pointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePointsIsMutable();
        points_.set(index, value);
        onChanged();
      } else {
        pointsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public Builder setPoints(
        int index, road.data.proto.Position3D.Builder builderForValue) {
      if (pointsBuilder_ == null) {
        ensurePointsIsMutable();
        points_.set(index, builderForValue.build());
        onChanged();
      } else {
        pointsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public Builder addPoints(road.data.proto.Position3D value) {
      if (pointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePointsIsMutable();
        points_.add(value);
        onChanged();
      } else {
        pointsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public Builder addPoints(
        int index, road.data.proto.Position3D value) {
      if (pointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePointsIsMutable();
        points_.add(index, value);
        onChanged();
      } else {
        pointsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public Builder addPoints(
        road.data.proto.Position3D.Builder builderForValue) {
      if (pointsBuilder_ == null) {
        ensurePointsIsMutable();
        points_.add(builderForValue.build());
        onChanged();
      } else {
        pointsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public Builder addPoints(
        int index, road.data.proto.Position3D.Builder builderForValue) {
      if (pointsBuilder_ == null) {
        ensurePointsIsMutable();
        points_.add(index, builderForValue.build());
        onChanged();
      } else {
        pointsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public Builder addAllPoints(
        java.lang.Iterable<? extends road.data.proto.Position3D> values) {
      if (pointsBuilder_ == null) {
        ensurePointsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, points_);
        onChanged();
      } else {
        pointsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public Builder clearPoints() {
      if (pointsBuilder_ == null) {
        points_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        pointsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public Builder removePoints(int index) {
      if (pointsBuilder_ == null) {
        ensurePointsIsMutable();
        points_.remove(index);
        onChanged();
      } else {
        pointsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public road.data.proto.Position3D.Builder getPointsBuilder(
        int index) {
      return getPointsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public road.data.proto.Position3DOrBuilder getPointsOrBuilder(
        int index) {
      if (pointsBuilder_ == null) {
        return points_.get(index);  } else {
        return pointsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public java.util.List<? extends road.data.proto.Position3DOrBuilder> 
         getPointsOrBuilderList() {
      if (pointsBuilder_ != null) {
        return pointsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(points_);
      }
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public road.data.proto.Position3D.Builder addPointsBuilder() {
      return getPointsFieldBuilder().addBuilder(
          road.data.proto.Position3D.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public road.data.proto.Position3D.Builder addPointsBuilder(
        int index) {
      return getPointsFieldBuilder().addBuilder(
          index, road.data.proto.Position3D.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，此路段中心线信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
     */
    public java.util.List<road.data.proto.Position3D.Builder> 
         getPointsBuilderList() {
      return getPointsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getPointsFieldBuilder() {
      if (pointsBuilder_ == null) {
        pointsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                points_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        points_ = null;
      }
      return pointsBuilder_;
    }

    private java.util.List<road.data.proto.Movement> movements_ =
      java.util.Collections.emptyList();
    private void ensureMovementsIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        movements_ = new java.util.ArrayList<road.data.proto.Movement>(movements_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Movement, road.data.proto.Movement.Builder, road.data.proto.MovementOrBuilder> movementsBuilder_;

    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public java.util.List<road.data.proto.Movement> getMovementsList() {
      if (movementsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(movements_);
      } else {
        return movementsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public int getMovementsCount() {
      if (movementsBuilder_ == null) {
        return movements_.size();
      } else {
        return movementsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public road.data.proto.Movement getMovements(int index) {
      if (movementsBuilder_ == null) {
        return movements_.get(index);
      } else {
        return movementsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public Builder setMovements(
        int index, road.data.proto.Movement value) {
      if (movementsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMovementsIsMutable();
        movements_.set(index, value);
        onChanged();
      } else {
        movementsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public Builder setMovements(
        int index, road.data.proto.Movement.Builder builderForValue) {
      if (movementsBuilder_ == null) {
        ensureMovementsIsMutable();
        movements_.set(index, builderForValue.build());
        onChanged();
      } else {
        movementsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public Builder addMovements(road.data.proto.Movement value) {
      if (movementsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMovementsIsMutable();
        movements_.add(value);
        onChanged();
      } else {
        movementsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public Builder addMovements(
        int index, road.data.proto.Movement value) {
      if (movementsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMovementsIsMutable();
        movements_.add(index, value);
        onChanged();
      } else {
        movementsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public Builder addMovements(
        road.data.proto.Movement.Builder builderForValue) {
      if (movementsBuilder_ == null) {
        ensureMovementsIsMutable();
        movements_.add(builderForValue.build());
        onChanged();
      } else {
        movementsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public Builder addMovements(
        int index, road.data.proto.Movement.Builder builderForValue) {
      if (movementsBuilder_ == null) {
        ensureMovementsIsMutable();
        movements_.add(index, builderForValue.build());
        onChanged();
      } else {
        movementsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public Builder addAllMovements(
        java.lang.Iterable<? extends road.data.proto.Movement> values) {
      if (movementsBuilder_ == null) {
        ensureMovementsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, movements_);
        onChanged();
      } else {
        movementsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public Builder clearMovements() {
      if (movementsBuilder_ == null) {
        movements_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        movementsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public Builder removeMovements(int index) {
      if (movementsBuilder_ == null) {
        ensureMovementsIsMutable();
        movements_.remove(index);
        onChanged();
      } else {
        movementsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public road.data.proto.Movement.Builder getMovementsBuilder(
        int index) {
      return getMovementsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public road.data.proto.MovementOrBuilder getMovementsOrBuilder(
        int index) {
      if (movementsBuilder_ == null) {
        return movements_.get(index);  } else {
        return movementsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public java.util.List<? extends road.data.proto.MovementOrBuilder> 
         getMovementsOrBuilderList() {
      if (movementsBuilder_ != null) {
        return movementsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(movements_);
      }
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public road.data.proto.Movement.Builder addMovementsBuilder() {
      return getMovementsFieldBuilder().addBuilder(
          road.data.proto.Movement.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public road.data.proto.Movement.Builder addMovementsBuilder(
        int index) {
      return getMovementsFieldBuilder().addBuilder(
          index, road.data.proto.Movement.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，该路段转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
     */
    public java.util.List<road.data.proto.Movement.Builder> 
         getMovementsBuilderList() {
      return getMovementsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Movement, road.data.proto.Movement.Builder, road.data.proto.MovementOrBuilder> 
        getMovementsFieldBuilder() {
      if (movementsBuilder_ == null) {
        movementsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.Movement, road.data.proto.Movement.Builder, road.data.proto.MovementOrBuilder>(
                movements_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        movements_ = null;
      }
      return movementsBuilder_;
    }

    private java.util.List<road.data.proto.Lane> lanes_ =
      java.util.Collections.emptyList();
    private void ensureLanesIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        lanes_ = new java.util.ArrayList<road.data.proto.Lane>(lanes_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Lane, road.data.proto.Lane.Builder, road.data.proto.LaneOrBuilder> lanesBuilder_;

    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public java.util.List<road.data.proto.Lane> getLanesList() {
      if (lanesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(lanes_);
      } else {
        return lanesBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public int getLanesCount() {
      if (lanesBuilder_ == null) {
        return lanes_.size();
      } else {
        return lanesBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public road.data.proto.Lane getLanes(int index) {
      if (lanesBuilder_ == null) {
        return lanes_.get(index);
      } else {
        return lanesBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public Builder setLanes(
        int index, road.data.proto.Lane value) {
      if (lanesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLanesIsMutable();
        lanes_.set(index, value);
        onChanged();
      } else {
        lanesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public Builder setLanes(
        int index, road.data.proto.Lane.Builder builderForValue) {
      if (lanesBuilder_ == null) {
        ensureLanesIsMutable();
        lanes_.set(index, builderForValue.build());
        onChanged();
      } else {
        lanesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public Builder addLanes(road.data.proto.Lane value) {
      if (lanesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLanesIsMutable();
        lanes_.add(value);
        onChanged();
      } else {
        lanesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public Builder addLanes(
        int index, road.data.proto.Lane value) {
      if (lanesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLanesIsMutable();
        lanes_.add(index, value);
        onChanged();
      } else {
        lanesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public Builder addLanes(
        road.data.proto.Lane.Builder builderForValue) {
      if (lanesBuilder_ == null) {
        ensureLanesIsMutable();
        lanes_.add(builderForValue.build());
        onChanged();
      } else {
        lanesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public Builder addLanes(
        int index, road.data.proto.Lane.Builder builderForValue) {
      if (lanesBuilder_ == null) {
        ensureLanesIsMutable();
        lanes_.add(index, builderForValue.build());
        onChanged();
      } else {
        lanesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public Builder addAllLanes(
        java.lang.Iterable<? extends road.data.proto.Lane> values) {
      if (lanesBuilder_ == null) {
        ensureLanesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, lanes_);
        onChanged();
      } else {
        lanesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public Builder clearLanes() {
      if (lanesBuilder_ == null) {
        lanes_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        lanesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public Builder removeLanes(int index) {
      if (lanesBuilder_ == null) {
        ensureLanesIsMutable();
        lanes_.remove(index);
        onChanged();
      } else {
        lanesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public road.data.proto.Lane.Builder getLanesBuilder(
        int index) {
      return getLanesFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public road.data.proto.LaneOrBuilder getLanesOrBuilder(
        int index) {
      if (lanesBuilder_ == null) {
        return lanes_.get(index);  } else {
        return lanesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public java.util.List<? extends road.data.proto.LaneOrBuilder> 
         getLanesOrBuilderList() {
      if (lanesBuilder_ != null) {
        return lanesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(lanes_);
      }
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public road.data.proto.Lane.Builder addLanesBuilder() {
      return getLanesFieldBuilder().addBuilder(
          road.data.proto.Lane.getDefaultInstance());
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public road.data.proto.Lane.Builder addLanesBuilder(
        int index) {
      return getLanesFieldBuilder().addBuilder(
          index, road.data.proto.Lane.getDefaultInstance());
    }
    /**
     * <pre>
     * 定义车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
     */
    public java.util.List<road.data.proto.Lane.Builder> 
         getLanesBuilderList() {
      return getLanesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Lane, road.data.proto.Lane.Builder, road.data.proto.LaneOrBuilder> 
        getLanesFieldBuilder() {
      if (lanesBuilder_ == null) {
        lanesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.Lane, road.data.proto.Lane.Builder, road.data.proto.LaneOrBuilder>(
                lanes_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        lanes_ = null;
      }
      return lanesBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.Link)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.Link)
  private static final road.data.proto.Link DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.Link();
  }

  public static road.data.proto.Link getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Link>
      PARSER = new com.google.protobuf.AbstractParser<Link>() {
    @java.lang.Override
    public Link parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Link(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Link> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Link> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.Link getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

