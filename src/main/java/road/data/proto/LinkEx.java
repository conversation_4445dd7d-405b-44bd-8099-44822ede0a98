// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *节点所在路段路段扩展信息      
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.LinkEx}
 */
public  final class LinkEx extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.LinkEx)
    LinkExOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LinkEx.newBuilder() to construct.
  private LinkEx(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LinkEx() {
    name_ = "";
    speedLimits_ = java.util.Collections.emptyList();
    refLine_ = java.util.Collections.emptyList();
    movementsEx_ = java.util.Collections.emptyList();
    sections_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LinkEx();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private LinkEx(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 18: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (upstreamNodeId_ != null) {
              subBuilder = upstreamNodeId_.toBuilder();
            }
            upstreamNodeId_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(upstreamNodeId_);
              upstreamNodeId_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              speedLimits_ = new java.util.ArrayList<road.data.proto.RegulatorySpeedLimit>();
              mutable_bitField0_ |= 0x00000001;
            }
            speedLimits_.add(
                input.readMessage(road.data.proto.RegulatorySpeedLimit.parser(), extensionRegistry));
            break;
          }
          case 32: {

            linkWidth_ = input.readUInt32();
            break;
          }
          case 42: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              refLine_ = new java.util.ArrayList<road.data.proto.Position3D>();
              mutable_bitField0_ |= 0x00000002;
            }
            refLine_.add(
                input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry));
            break;
          }
          case 50: {
            if (!((mutable_bitField0_ & 0x00000004) != 0)) {
              movementsEx_ = new java.util.ArrayList<road.data.proto.MovementEx>();
              mutable_bitField0_ |= 0x00000004;
            }
            movementsEx_.add(
                input.readMessage(road.data.proto.MovementEx.parser(), extensionRegistry));
            break;
          }
          case 58: {
            if (!((mutable_bitField0_ & 0x00000008) != 0)) {
              sections_ = new java.util.ArrayList<road.data.proto.Section>();
              mutable_bitField0_ |= 0x00000008;
            }
            sections_.add(
                input.readMessage(road.data.proto.Section.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        speedLimits_ = java.util.Collections.unmodifiableList(speedLimits_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        refLine_ = java.util.Collections.unmodifiableList(refLine_);
      }
      if (((mutable_bitField0_ & 0x00000004) != 0)) {
        movementsEx_ = java.util.Collections.unmodifiableList(movementsEx_);
      }
      if (((mutable_bitField0_ & 0x00000008) != 0)) {
        sections_ = java.util.Collections.unmodifiableList(sections_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkEx_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkEx_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.LinkEx.class, road.data.proto.LinkEx.Builder.class);
  }

  public static final int NAME_FIELD_NUMBER = 1;
  private volatile java.lang.Object name_;
  /**
   * <pre>
   * 可选，名称
   * </pre>
   *
   * <code>string name = 1;</code>
   */
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 可选，名称
   * </pre>
   *
   * <code>string name = 1;</code>
   */
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int UPSTREAMNODEID_FIELD_NUMBER = 2;
  private road.data.proto.NodeReferenceId upstreamNodeId_;
  /**
   * <pre>
   * 上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
   */
  public boolean hasUpstreamNodeId() {
    return upstreamNodeId_ != null;
  }
  /**
   * <pre>
   * 上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
   */
  public road.data.proto.NodeReferenceId getUpstreamNodeId() {
    return upstreamNodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
  }
  /**
   * <pre>
   * 上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder() {
    return getUpstreamNodeId();
  }

  public static final int SPEEDLIMITS_FIELD_NUMBER = 3;
  private java.util.List<road.data.proto.RegulatorySpeedLimit> speedLimits_;
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  public java.util.List<road.data.proto.RegulatorySpeedLimit> getSpeedLimitsList() {
    return speedLimits_;
  }
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  public java.util.List<? extends road.data.proto.RegulatorySpeedLimitOrBuilder> 
      getSpeedLimitsOrBuilderList() {
    return speedLimits_;
  }
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  public int getSpeedLimitsCount() {
    return speedLimits_.size();
  }
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  public road.data.proto.RegulatorySpeedLimit getSpeedLimits(int index) {
    return speedLimits_.get(index);
  }
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  public road.data.proto.RegulatorySpeedLimitOrBuilder getSpeedLimitsOrBuilder(
      int index) {
    return speedLimits_.get(index);
  }

  public static final int LINKWIDTH_FIELD_NUMBER = 4;
  private int linkWidth_;
  /**
   * <pre>
   * 可选，车道宽度，分辨率为 1cm
   * </pre>
   *
   * <code>uint32 linkWidth = 4;</code>
   */
  public int getLinkWidth() {
    return linkWidth_;
  }

  public static final int REFLINE_FIELD_NUMBER = 5;
  private java.util.List<road.data.proto.Position3D> refLine_;
  /**
   * <pre>
   * 可选，此路段的参考线信息（0号车道中心线）
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
   */
  public java.util.List<road.data.proto.Position3D> getRefLineList() {
    return refLine_;
  }
  /**
   * <pre>
   * 可选，此路段的参考线信息（0号车道中心线）
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
   */
  public java.util.List<? extends road.data.proto.Position3DOrBuilder> 
      getRefLineOrBuilderList() {
    return refLine_;
  }
  /**
   * <pre>
   * 可选，此路段的参考线信息（0号车道中心线）
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
   */
  public int getRefLineCount() {
    return refLine_.size();
  }
  /**
   * <pre>
   * 可选，此路段的参考线信息（0号车道中心线）
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
   */
  public road.data.proto.Position3D getRefLine(int index) {
    return refLine_.get(index);
  }
  /**
   * <pre>
   * 可选，此路段的参考线信息（0号车道中心线）
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
   */
  public road.data.proto.Position3DOrBuilder getRefLineOrBuilder(
      int index) {
    return refLine_.get(index);
  }

  public static final int MOVEMENTSEX_FIELD_NUMBER = 6;
  private java.util.List<road.data.proto.MovementEx> movementsEx_;
  /**
   * <pre>
   *可选，该路段转向拓展集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
   */
  public java.util.List<road.data.proto.MovementEx> getMovementsExList() {
    return movementsEx_;
  }
  /**
   * <pre>
   *可选，该路段转向拓展集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
   */
  public java.util.List<? extends road.data.proto.MovementExOrBuilder> 
      getMovementsExOrBuilderList() {
    return movementsEx_;
  }
  /**
   * <pre>
   *可选，该路段转向拓展集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
   */
  public int getMovementsExCount() {
    return movementsEx_.size();
  }
  /**
   * <pre>
   *可选，该路段转向拓展集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
   */
  public road.data.proto.MovementEx getMovementsEx(int index) {
    return movementsEx_.get(index);
  }
  /**
   * <pre>
   *可选，该路段转向拓展集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
   */
  public road.data.proto.MovementExOrBuilder getMovementsExOrBuilder(
      int index) {
    return movementsEx_.get(index);
  }

  public static final int SECTIONS_FIELD_NUMBER = 7;
  private java.util.List<road.data.proto.Section> sections_;
  /**
   * <pre>
   *可选，该路段包含的路段区域分段集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
   */
  public java.util.List<road.data.proto.Section> getSectionsList() {
    return sections_;
  }
  /**
   * <pre>
   *可选，该路段包含的路段区域分段集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
   */
  public java.util.List<? extends road.data.proto.SectionOrBuilder> 
      getSectionsOrBuilderList() {
    return sections_;
  }
  /**
   * <pre>
   *可选，该路段包含的路段区域分段集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
   */
  public int getSectionsCount() {
    return sections_.size();
  }
  /**
   * <pre>
   *可选，该路段包含的路段区域分段集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
   */
  public road.data.proto.Section getSections(int index) {
    return sections_.get(index);
  }
  /**
   * <pre>
   *可选，该路段包含的路段区域分段集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
   */
  public road.data.proto.SectionOrBuilder getSectionsOrBuilder(
      int index) {
    return sections_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
    }
    if (upstreamNodeId_ != null) {
      output.writeMessage(2, getUpstreamNodeId());
    }
    for (int i = 0; i < speedLimits_.size(); i++) {
      output.writeMessage(3, speedLimits_.get(i));
    }
    if (linkWidth_ != 0) {
      output.writeUInt32(4, linkWidth_);
    }
    for (int i = 0; i < refLine_.size(); i++) {
      output.writeMessage(5, refLine_.get(i));
    }
    for (int i = 0; i < movementsEx_.size(); i++) {
      output.writeMessage(6, movementsEx_.get(i));
    }
    for (int i = 0; i < sections_.size(); i++) {
      output.writeMessage(7, sections_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
    }
    if (upstreamNodeId_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getUpstreamNodeId());
    }
    for (int i = 0; i < speedLimits_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, speedLimits_.get(i));
    }
    if (linkWidth_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, linkWidth_);
    }
    for (int i = 0; i < refLine_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, refLine_.get(i));
    }
    for (int i = 0; i < movementsEx_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, movementsEx_.get(i));
    }
    for (int i = 0; i < sections_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, sections_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.LinkEx)) {
      return super.equals(obj);
    }
    road.data.proto.LinkEx other = (road.data.proto.LinkEx) obj;

    if (!getName()
        .equals(other.getName())) return false;
    if (hasUpstreamNodeId() != other.hasUpstreamNodeId()) return false;
    if (hasUpstreamNodeId()) {
      if (!getUpstreamNodeId()
          .equals(other.getUpstreamNodeId())) return false;
    }
    if (!getSpeedLimitsList()
        .equals(other.getSpeedLimitsList())) return false;
    if (getLinkWidth()
        != other.getLinkWidth()) return false;
    if (!getRefLineList()
        .equals(other.getRefLineList())) return false;
    if (!getMovementsExList()
        .equals(other.getMovementsExList())) return false;
    if (!getSectionsList()
        .equals(other.getSectionsList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    if (hasUpstreamNodeId()) {
      hash = (37 * hash) + UPSTREAMNODEID_FIELD_NUMBER;
      hash = (53 * hash) + getUpstreamNodeId().hashCode();
    }
    if (getSpeedLimitsCount() > 0) {
      hash = (37 * hash) + SPEEDLIMITS_FIELD_NUMBER;
      hash = (53 * hash) + getSpeedLimitsList().hashCode();
    }
    hash = (37 * hash) + LINKWIDTH_FIELD_NUMBER;
    hash = (53 * hash) + getLinkWidth();
    if (getRefLineCount() > 0) {
      hash = (37 * hash) + REFLINE_FIELD_NUMBER;
      hash = (53 * hash) + getRefLineList().hashCode();
    }
    if (getMovementsExCount() > 0) {
      hash = (37 * hash) + MOVEMENTSEX_FIELD_NUMBER;
      hash = (53 * hash) + getMovementsExList().hashCode();
    }
    if (getSectionsCount() > 0) {
      hash = (37 * hash) + SECTIONS_FIELD_NUMBER;
      hash = (53 * hash) + getSectionsList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.LinkEx parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LinkEx parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LinkEx parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LinkEx parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LinkEx parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LinkEx parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LinkEx parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LinkEx parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LinkEx parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.LinkEx parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LinkEx parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LinkEx parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.LinkEx prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *节点所在路段路段扩展信息      
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.LinkEx}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.LinkEx)
      road.data.proto.LinkExOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkEx_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkEx_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.LinkEx.class, road.data.proto.LinkEx.Builder.class);
    }

    // Construct using road.data.proto.LinkEx.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getSpeedLimitsFieldBuilder();
        getRefLineFieldBuilder();
        getMovementsExFieldBuilder();
        getSectionsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      name_ = "";

      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = null;
      } else {
        upstreamNodeId_ = null;
        upstreamNodeIdBuilder_ = null;
      }
      if (speedLimitsBuilder_ == null) {
        speedLimits_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        speedLimitsBuilder_.clear();
      }
      linkWidth_ = 0;

      if (refLineBuilder_ == null) {
        refLine_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        refLineBuilder_.clear();
      }
      if (movementsExBuilder_ == null) {
        movementsEx_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
      } else {
        movementsExBuilder_.clear();
      }
      if (sectionsBuilder_ == null) {
        sections_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
      } else {
        sectionsBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkEx_descriptor;
    }

    @java.lang.Override
    public road.data.proto.LinkEx getDefaultInstanceForType() {
      return road.data.proto.LinkEx.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.LinkEx build() {
      road.data.proto.LinkEx result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.LinkEx buildPartial() {
      road.data.proto.LinkEx result = new road.data.proto.LinkEx(this);
      int from_bitField0_ = bitField0_;
      result.name_ = name_;
      if (upstreamNodeIdBuilder_ == null) {
        result.upstreamNodeId_ = upstreamNodeId_;
      } else {
        result.upstreamNodeId_ = upstreamNodeIdBuilder_.build();
      }
      if (speedLimitsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          speedLimits_ = java.util.Collections.unmodifiableList(speedLimits_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.speedLimits_ = speedLimits_;
      } else {
        result.speedLimits_ = speedLimitsBuilder_.build();
      }
      result.linkWidth_ = linkWidth_;
      if (refLineBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          refLine_ = java.util.Collections.unmodifiableList(refLine_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.refLine_ = refLine_;
      } else {
        result.refLine_ = refLineBuilder_.build();
      }
      if (movementsExBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          movementsEx_ = java.util.Collections.unmodifiableList(movementsEx_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.movementsEx_ = movementsEx_;
      } else {
        result.movementsEx_ = movementsExBuilder_.build();
      }
      if (sectionsBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          sections_ = java.util.Collections.unmodifiableList(sections_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.sections_ = sections_;
      } else {
        result.sections_ = sectionsBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.LinkEx) {
        return mergeFrom((road.data.proto.LinkEx)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.LinkEx other) {
      if (other == road.data.proto.LinkEx.getDefaultInstance()) return this;
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (other.hasUpstreamNodeId()) {
        mergeUpstreamNodeId(other.getUpstreamNodeId());
      }
      if (speedLimitsBuilder_ == null) {
        if (!other.speedLimits_.isEmpty()) {
          if (speedLimits_.isEmpty()) {
            speedLimits_ = other.speedLimits_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureSpeedLimitsIsMutable();
            speedLimits_.addAll(other.speedLimits_);
          }
          onChanged();
        }
      } else {
        if (!other.speedLimits_.isEmpty()) {
          if (speedLimitsBuilder_.isEmpty()) {
            speedLimitsBuilder_.dispose();
            speedLimitsBuilder_ = null;
            speedLimits_ = other.speedLimits_;
            bitField0_ = (bitField0_ & ~0x00000001);
            speedLimitsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getSpeedLimitsFieldBuilder() : null;
          } else {
            speedLimitsBuilder_.addAllMessages(other.speedLimits_);
          }
        }
      }
      if (other.getLinkWidth() != 0) {
        setLinkWidth(other.getLinkWidth());
      }
      if (refLineBuilder_ == null) {
        if (!other.refLine_.isEmpty()) {
          if (refLine_.isEmpty()) {
            refLine_ = other.refLine_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureRefLineIsMutable();
            refLine_.addAll(other.refLine_);
          }
          onChanged();
        }
      } else {
        if (!other.refLine_.isEmpty()) {
          if (refLineBuilder_.isEmpty()) {
            refLineBuilder_.dispose();
            refLineBuilder_ = null;
            refLine_ = other.refLine_;
            bitField0_ = (bitField0_ & ~0x00000002);
            refLineBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getRefLineFieldBuilder() : null;
          } else {
            refLineBuilder_.addAllMessages(other.refLine_);
          }
        }
      }
      if (movementsExBuilder_ == null) {
        if (!other.movementsEx_.isEmpty()) {
          if (movementsEx_.isEmpty()) {
            movementsEx_ = other.movementsEx_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureMovementsExIsMutable();
            movementsEx_.addAll(other.movementsEx_);
          }
          onChanged();
        }
      } else {
        if (!other.movementsEx_.isEmpty()) {
          if (movementsExBuilder_.isEmpty()) {
            movementsExBuilder_.dispose();
            movementsExBuilder_ = null;
            movementsEx_ = other.movementsEx_;
            bitField0_ = (bitField0_ & ~0x00000004);
            movementsExBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getMovementsExFieldBuilder() : null;
          } else {
            movementsExBuilder_.addAllMessages(other.movementsEx_);
          }
        }
      }
      if (sectionsBuilder_ == null) {
        if (!other.sections_.isEmpty()) {
          if (sections_.isEmpty()) {
            sections_ = other.sections_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureSectionsIsMutable();
            sections_.addAll(other.sections_);
          }
          onChanged();
        }
      } else {
        if (!other.sections_.isEmpty()) {
          if (sectionsBuilder_.isEmpty()) {
            sectionsBuilder_.dispose();
            sectionsBuilder_ = null;
            sections_ = other.sections_;
            bitField0_ = (bitField0_ & ~0x00000008);
            sectionsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getSectionsFieldBuilder() : null;
          } else {
            sectionsBuilder_.addAllMessages(other.sections_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.LinkEx parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.LinkEx) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object name_ = "";
    /**
     * <pre>
     * 可选，名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 可选，名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 可选，名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private road.data.proto.NodeReferenceId upstreamNodeId_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> upstreamNodeIdBuilder_;
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public boolean hasUpstreamNodeId() {
      return upstreamNodeIdBuilder_ != null || upstreamNodeId_ != null;
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public road.data.proto.NodeReferenceId getUpstreamNodeId() {
      if (upstreamNodeIdBuilder_ == null) {
        return upstreamNodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
      } else {
        return upstreamNodeIdBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public Builder setUpstreamNodeId(road.data.proto.NodeReferenceId value) {
      if (upstreamNodeIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        upstreamNodeId_ = value;
        onChanged();
      } else {
        upstreamNodeIdBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public Builder setUpstreamNodeId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = builderForValue.build();
        onChanged();
      } else {
        upstreamNodeIdBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public Builder mergeUpstreamNodeId(road.data.proto.NodeReferenceId value) {
      if (upstreamNodeIdBuilder_ == null) {
        if (upstreamNodeId_ != null) {
          upstreamNodeId_ =
            road.data.proto.NodeReferenceId.newBuilder(upstreamNodeId_).mergeFrom(value).buildPartial();
        } else {
          upstreamNodeId_ = value;
        }
        onChanged();
      } else {
        upstreamNodeIdBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public Builder clearUpstreamNodeId() {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = null;
        onChanged();
      } else {
        upstreamNodeId_ = null;
        upstreamNodeIdBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getUpstreamNodeIdBuilder() {
      
      onChanged();
      return getUpstreamNodeIdFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder() {
      if (upstreamNodeIdBuilder_ != null) {
        return upstreamNodeIdBuilder_.getMessageOrBuilder();
      } else {
        return upstreamNodeId_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
      }
    }
    /**
     * <pre>
     * 上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getUpstreamNodeIdFieldBuilder() {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getUpstreamNodeId(),
                getParentForChildren(),
                isClean());
        upstreamNodeId_ = null;
      }
      return upstreamNodeIdBuilder_;
    }

    private java.util.List<road.data.proto.RegulatorySpeedLimit> speedLimits_ =
      java.util.Collections.emptyList();
    private void ensureSpeedLimitsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        speedLimits_ = new java.util.ArrayList<road.data.proto.RegulatorySpeedLimit>(speedLimits_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RegulatorySpeedLimit, road.data.proto.RegulatorySpeedLimit.Builder, road.data.proto.RegulatorySpeedLimitOrBuilder> speedLimitsBuilder_;

    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public java.util.List<road.data.proto.RegulatorySpeedLimit> getSpeedLimitsList() {
      if (speedLimitsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(speedLimits_);
      } else {
        return speedLimitsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public int getSpeedLimitsCount() {
      if (speedLimitsBuilder_ == null) {
        return speedLimits_.size();
      } else {
        return speedLimitsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public road.data.proto.RegulatorySpeedLimit getSpeedLimits(int index) {
      if (speedLimitsBuilder_ == null) {
        return speedLimits_.get(index);
      } else {
        return speedLimitsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder setSpeedLimits(
        int index, road.data.proto.RegulatorySpeedLimit value) {
      if (speedLimitsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSpeedLimitsIsMutable();
        speedLimits_.set(index, value);
        onChanged();
      } else {
        speedLimitsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder setSpeedLimits(
        int index, road.data.proto.RegulatorySpeedLimit.Builder builderForValue) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        speedLimits_.set(index, builderForValue.build());
        onChanged();
      } else {
        speedLimitsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder addSpeedLimits(road.data.proto.RegulatorySpeedLimit value) {
      if (speedLimitsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSpeedLimitsIsMutable();
        speedLimits_.add(value);
        onChanged();
      } else {
        speedLimitsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder addSpeedLimits(
        int index, road.data.proto.RegulatorySpeedLimit value) {
      if (speedLimitsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSpeedLimitsIsMutable();
        speedLimits_.add(index, value);
        onChanged();
      } else {
        speedLimitsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder addSpeedLimits(
        road.data.proto.RegulatorySpeedLimit.Builder builderForValue) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        speedLimits_.add(builderForValue.build());
        onChanged();
      } else {
        speedLimitsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder addSpeedLimits(
        int index, road.data.proto.RegulatorySpeedLimit.Builder builderForValue) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        speedLimits_.add(index, builderForValue.build());
        onChanged();
      } else {
        speedLimitsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder addAllSpeedLimits(
        java.lang.Iterable<? extends road.data.proto.RegulatorySpeedLimit> values) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, speedLimits_);
        onChanged();
      } else {
        speedLimitsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder clearSpeedLimits() {
      if (speedLimitsBuilder_ == null) {
        speedLimits_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        speedLimitsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public Builder removeSpeedLimits(int index) {
      if (speedLimitsBuilder_ == null) {
        ensureSpeedLimitsIsMutable();
        speedLimits_.remove(index);
        onChanged();
      } else {
        speedLimitsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public road.data.proto.RegulatorySpeedLimit.Builder getSpeedLimitsBuilder(
        int index) {
      return getSpeedLimitsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public road.data.proto.RegulatorySpeedLimitOrBuilder getSpeedLimitsOrBuilder(
        int index) {
      if (speedLimitsBuilder_ == null) {
        return speedLimits_.get(index);  } else {
        return speedLimitsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public java.util.List<? extends road.data.proto.RegulatorySpeedLimitOrBuilder> 
         getSpeedLimitsOrBuilderList() {
      if (speedLimitsBuilder_ != null) {
        return speedLimitsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(speedLimits_);
      }
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public road.data.proto.RegulatorySpeedLimit.Builder addSpeedLimitsBuilder() {
      return getSpeedLimitsFieldBuilder().addBuilder(
          road.data.proto.RegulatorySpeedLimit.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public road.data.proto.RegulatorySpeedLimit.Builder addSpeedLimitsBuilder(
        int index) {
      return getSpeedLimitsFieldBuilder().addBuilder(
          index, road.data.proto.RegulatorySpeedLimit.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，限速集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
     */
    public java.util.List<road.data.proto.RegulatorySpeedLimit.Builder> 
         getSpeedLimitsBuilderList() {
      return getSpeedLimitsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RegulatorySpeedLimit, road.data.proto.RegulatorySpeedLimit.Builder, road.data.proto.RegulatorySpeedLimitOrBuilder> 
        getSpeedLimitsFieldBuilder() {
      if (speedLimitsBuilder_ == null) {
        speedLimitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.RegulatorySpeedLimit, road.data.proto.RegulatorySpeedLimit.Builder, road.data.proto.RegulatorySpeedLimitOrBuilder>(
                speedLimits_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        speedLimits_ = null;
      }
      return speedLimitsBuilder_;
    }

    private int linkWidth_ ;
    /**
     * <pre>
     * 可选，车道宽度，分辨率为 1cm
     * </pre>
     *
     * <code>uint32 linkWidth = 4;</code>
     */
    public int getLinkWidth() {
      return linkWidth_;
    }
    /**
     * <pre>
     * 可选，车道宽度，分辨率为 1cm
     * </pre>
     *
     * <code>uint32 linkWidth = 4;</code>
     */
    public Builder setLinkWidth(int value) {
      
      linkWidth_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，车道宽度，分辨率为 1cm
     * </pre>
     *
     * <code>uint32 linkWidth = 4;</code>
     */
    public Builder clearLinkWidth() {
      
      linkWidth_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.Position3D> refLine_ =
      java.util.Collections.emptyList();
    private void ensureRefLineIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        refLine_ = new java.util.ArrayList<road.data.proto.Position3D>(refLine_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> refLineBuilder_;

    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public java.util.List<road.data.proto.Position3D> getRefLineList() {
      if (refLineBuilder_ == null) {
        return java.util.Collections.unmodifiableList(refLine_);
      } else {
        return refLineBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public int getRefLineCount() {
      if (refLineBuilder_ == null) {
        return refLine_.size();
      } else {
        return refLineBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public road.data.proto.Position3D getRefLine(int index) {
      if (refLineBuilder_ == null) {
        return refLine_.get(index);
      } else {
        return refLineBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public Builder setRefLine(
        int index, road.data.proto.Position3D value) {
      if (refLineBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRefLineIsMutable();
        refLine_.set(index, value);
        onChanged();
      } else {
        refLineBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public Builder setRefLine(
        int index, road.data.proto.Position3D.Builder builderForValue) {
      if (refLineBuilder_ == null) {
        ensureRefLineIsMutable();
        refLine_.set(index, builderForValue.build());
        onChanged();
      } else {
        refLineBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public Builder addRefLine(road.data.proto.Position3D value) {
      if (refLineBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRefLineIsMutable();
        refLine_.add(value);
        onChanged();
      } else {
        refLineBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public Builder addRefLine(
        int index, road.data.proto.Position3D value) {
      if (refLineBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRefLineIsMutable();
        refLine_.add(index, value);
        onChanged();
      } else {
        refLineBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public Builder addRefLine(
        road.data.proto.Position3D.Builder builderForValue) {
      if (refLineBuilder_ == null) {
        ensureRefLineIsMutable();
        refLine_.add(builderForValue.build());
        onChanged();
      } else {
        refLineBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public Builder addRefLine(
        int index, road.data.proto.Position3D.Builder builderForValue) {
      if (refLineBuilder_ == null) {
        ensureRefLineIsMutable();
        refLine_.add(index, builderForValue.build());
        onChanged();
      } else {
        refLineBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public Builder addAllRefLine(
        java.lang.Iterable<? extends road.data.proto.Position3D> values) {
      if (refLineBuilder_ == null) {
        ensureRefLineIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, refLine_);
        onChanged();
      } else {
        refLineBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public Builder clearRefLine() {
      if (refLineBuilder_ == null) {
        refLine_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        refLineBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public Builder removeRefLine(int index) {
      if (refLineBuilder_ == null) {
        ensureRefLineIsMutable();
        refLine_.remove(index);
        onChanged();
      } else {
        refLineBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public road.data.proto.Position3D.Builder getRefLineBuilder(
        int index) {
      return getRefLineFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public road.data.proto.Position3DOrBuilder getRefLineOrBuilder(
        int index) {
      if (refLineBuilder_ == null) {
        return refLine_.get(index);  } else {
        return refLineBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public java.util.List<? extends road.data.proto.Position3DOrBuilder> 
         getRefLineOrBuilderList() {
      if (refLineBuilder_ != null) {
        return refLineBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(refLine_);
      }
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public road.data.proto.Position3D.Builder addRefLineBuilder() {
      return getRefLineFieldBuilder().addBuilder(
          road.data.proto.Position3D.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public road.data.proto.Position3D.Builder addRefLineBuilder(
        int index) {
      return getRefLineFieldBuilder().addBuilder(
          index, road.data.proto.Position3D.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，此路段的参考线信息（0号车道中心线）
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
     */
    public java.util.List<road.data.proto.Position3D.Builder> 
         getRefLineBuilderList() {
      return getRefLineFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getRefLineFieldBuilder() {
      if (refLineBuilder_ == null) {
        refLineBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                refLine_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        refLine_ = null;
      }
      return refLineBuilder_;
    }

    private java.util.List<road.data.proto.MovementEx> movementsEx_ =
      java.util.Collections.emptyList();
    private void ensureMovementsExIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        movementsEx_ = new java.util.ArrayList<road.data.proto.MovementEx>(movementsEx_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.MovementEx, road.data.proto.MovementEx.Builder, road.data.proto.MovementExOrBuilder> movementsExBuilder_;

    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public java.util.List<road.data.proto.MovementEx> getMovementsExList() {
      if (movementsExBuilder_ == null) {
        return java.util.Collections.unmodifiableList(movementsEx_);
      } else {
        return movementsExBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public int getMovementsExCount() {
      if (movementsExBuilder_ == null) {
        return movementsEx_.size();
      } else {
        return movementsExBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public road.data.proto.MovementEx getMovementsEx(int index) {
      if (movementsExBuilder_ == null) {
        return movementsEx_.get(index);
      } else {
        return movementsExBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public Builder setMovementsEx(
        int index, road.data.proto.MovementEx value) {
      if (movementsExBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMovementsExIsMutable();
        movementsEx_.set(index, value);
        onChanged();
      } else {
        movementsExBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public Builder setMovementsEx(
        int index, road.data.proto.MovementEx.Builder builderForValue) {
      if (movementsExBuilder_ == null) {
        ensureMovementsExIsMutable();
        movementsEx_.set(index, builderForValue.build());
        onChanged();
      } else {
        movementsExBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public Builder addMovementsEx(road.data.proto.MovementEx value) {
      if (movementsExBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMovementsExIsMutable();
        movementsEx_.add(value);
        onChanged();
      } else {
        movementsExBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public Builder addMovementsEx(
        int index, road.data.proto.MovementEx value) {
      if (movementsExBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMovementsExIsMutable();
        movementsEx_.add(index, value);
        onChanged();
      } else {
        movementsExBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public Builder addMovementsEx(
        road.data.proto.MovementEx.Builder builderForValue) {
      if (movementsExBuilder_ == null) {
        ensureMovementsExIsMutable();
        movementsEx_.add(builderForValue.build());
        onChanged();
      } else {
        movementsExBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public Builder addMovementsEx(
        int index, road.data.proto.MovementEx.Builder builderForValue) {
      if (movementsExBuilder_ == null) {
        ensureMovementsExIsMutable();
        movementsEx_.add(index, builderForValue.build());
        onChanged();
      } else {
        movementsExBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public Builder addAllMovementsEx(
        java.lang.Iterable<? extends road.data.proto.MovementEx> values) {
      if (movementsExBuilder_ == null) {
        ensureMovementsExIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, movementsEx_);
        onChanged();
      } else {
        movementsExBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public Builder clearMovementsEx() {
      if (movementsExBuilder_ == null) {
        movementsEx_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        movementsExBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public Builder removeMovementsEx(int index) {
      if (movementsExBuilder_ == null) {
        ensureMovementsExIsMutable();
        movementsEx_.remove(index);
        onChanged();
      } else {
        movementsExBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public road.data.proto.MovementEx.Builder getMovementsExBuilder(
        int index) {
      return getMovementsExFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public road.data.proto.MovementExOrBuilder getMovementsExOrBuilder(
        int index) {
      if (movementsExBuilder_ == null) {
        return movementsEx_.get(index);  } else {
        return movementsExBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public java.util.List<? extends road.data.proto.MovementExOrBuilder> 
         getMovementsExOrBuilderList() {
      if (movementsExBuilder_ != null) {
        return movementsExBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(movementsEx_);
      }
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public road.data.proto.MovementEx.Builder addMovementsExBuilder() {
      return getMovementsExFieldBuilder().addBuilder(
          road.data.proto.MovementEx.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public road.data.proto.MovementEx.Builder addMovementsExBuilder(
        int index) {
      return getMovementsExFieldBuilder().addBuilder(
          index, road.data.proto.MovementEx.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，该路段转向拓展集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
     */
    public java.util.List<road.data.proto.MovementEx.Builder> 
         getMovementsExBuilderList() {
      return getMovementsExFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.MovementEx, road.data.proto.MovementEx.Builder, road.data.proto.MovementExOrBuilder> 
        getMovementsExFieldBuilder() {
      if (movementsExBuilder_ == null) {
        movementsExBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.MovementEx, road.data.proto.MovementEx.Builder, road.data.proto.MovementExOrBuilder>(
                movementsEx_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        movementsEx_ = null;
      }
      return movementsExBuilder_;
    }

    private java.util.List<road.data.proto.Section> sections_ =
      java.util.Collections.emptyList();
    private void ensureSectionsIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        sections_ = new java.util.ArrayList<road.data.proto.Section>(sections_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Section, road.data.proto.Section.Builder, road.data.proto.SectionOrBuilder> sectionsBuilder_;

    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public java.util.List<road.data.proto.Section> getSectionsList() {
      if (sectionsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(sections_);
      } else {
        return sectionsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public int getSectionsCount() {
      if (sectionsBuilder_ == null) {
        return sections_.size();
      } else {
        return sectionsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public road.data.proto.Section getSections(int index) {
      if (sectionsBuilder_ == null) {
        return sections_.get(index);
      } else {
        return sectionsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public Builder setSections(
        int index, road.data.proto.Section value) {
      if (sectionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSectionsIsMutable();
        sections_.set(index, value);
        onChanged();
      } else {
        sectionsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public Builder setSections(
        int index, road.data.proto.Section.Builder builderForValue) {
      if (sectionsBuilder_ == null) {
        ensureSectionsIsMutable();
        sections_.set(index, builderForValue.build());
        onChanged();
      } else {
        sectionsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public Builder addSections(road.data.proto.Section value) {
      if (sectionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSectionsIsMutable();
        sections_.add(value);
        onChanged();
      } else {
        sectionsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public Builder addSections(
        int index, road.data.proto.Section value) {
      if (sectionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSectionsIsMutable();
        sections_.add(index, value);
        onChanged();
      } else {
        sectionsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public Builder addSections(
        road.data.proto.Section.Builder builderForValue) {
      if (sectionsBuilder_ == null) {
        ensureSectionsIsMutable();
        sections_.add(builderForValue.build());
        onChanged();
      } else {
        sectionsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public Builder addSections(
        int index, road.data.proto.Section.Builder builderForValue) {
      if (sectionsBuilder_ == null) {
        ensureSectionsIsMutable();
        sections_.add(index, builderForValue.build());
        onChanged();
      } else {
        sectionsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public Builder addAllSections(
        java.lang.Iterable<? extends road.data.proto.Section> values) {
      if (sectionsBuilder_ == null) {
        ensureSectionsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, sections_);
        onChanged();
      } else {
        sectionsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public Builder clearSections() {
      if (sectionsBuilder_ == null) {
        sections_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        sectionsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public Builder removeSections(int index) {
      if (sectionsBuilder_ == null) {
        ensureSectionsIsMutable();
        sections_.remove(index);
        onChanged();
      } else {
        sectionsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public road.data.proto.Section.Builder getSectionsBuilder(
        int index) {
      return getSectionsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public road.data.proto.SectionOrBuilder getSectionsOrBuilder(
        int index) {
      if (sectionsBuilder_ == null) {
        return sections_.get(index);  } else {
        return sectionsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public java.util.List<? extends road.data.proto.SectionOrBuilder> 
         getSectionsOrBuilderList() {
      if (sectionsBuilder_ != null) {
        return sectionsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(sections_);
      }
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public road.data.proto.Section.Builder addSectionsBuilder() {
      return getSectionsFieldBuilder().addBuilder(
          road.data.proto.Section.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public road.data.proto.Section.Builder addSectionsBuilder(
        int index) {
      return getSectionsFieldBuilder().addBuilder(
          index, road.data.proto.Section.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，该路段包含的路段区域分段集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
     */
    public java.util.List<road.data.proto.Section.Builder> 
         getSectionsBuilderList() {
      return getSectionsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Section, road.data.proto.Section.Builder, road.data.proto.SectionOrBuilder> 
        getSectionsFieldBuilder() {
      if (sectionsBuilder_ == null) {
        sectionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.Section, road.data.proto.Section.Builder, road.data.proto.SectionOrBuilder>(
                sections_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        sections_ = null;
      }
      return sectionsBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.LinkEx)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.LinkEx)
  private static final road.data.proto.LinkEx DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.LinkEx();
  }

  public static road.data.proto.LinkEx getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LinkEx>
      PARSER = new com.google.protobuf.AbstractParser<LinkEx>() {
    @java.lang.Override
    public LinkEx parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new LinkEx(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<LinkEx> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LinkEx> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.LinkEx getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

