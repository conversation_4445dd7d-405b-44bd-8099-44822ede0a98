// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LinkExOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LinkEx)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 可选，名称
   * </pre>
   *
   * <code>string name = 1;</code>
   */
  java.lang.String getName();
  /**
   * <pre>
   * 可选，名称
   * </pre>
   *
   * <code>string name = 1;</code>
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * 上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
   */
  boolean hasUpstreamNodeId();
  /**
   * <pre>
   * 上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
   */
  road.data.proto.NodeReferenceId getUpstreamNodeId();
  /**
   * <pre>
   * 上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder();

  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  java.util.List<road.data.proto.RegulatorySpeedLimit> 
      getSpeedLimitsList();
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  road.data.proto.RegulatorySpeedLimit getSpeedLimits(int index);
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  int getSpeedLimitsCount();
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  java.util.List<? extends road.data.proto.RegulatorySpeedLimitOrBuilder> 
      getSpeedLimitsOrBuilderList();
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  road.data.proto.RegulatorySpeedLimitOrBuilder getSpeedLimitsOrBuilder(
      int index);

  /**
   * <pre>
   * 可选，车道宽度，分辨率为 1cm
   * </pre>
   *
   * <code>uint32 linkWidth = 4;</code>
   */
  int getLinkWidth();

  /**
   * <pre>
   * 可选，此路段的参考线信息（0号车道中心线）
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
   */
  java.util.List<road.data.proto.Position3D> 
      getRefLineList();
  /**
   * <pre>
   * 可选，此路段的参考线信息（0号车道中心线）
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
   */
  road.data.proto.Position3D getRefLine(int index);
  /**
   * <pre>
   * 可选，此路段的参考线信息（0号车道中心线）
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
   */
  int getRefLineCount();
  /**
   * <pre>
   * 可选，此路段的参考线信息（0号车道中心线）
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
   */
  java.util.List<? extends road.data.proto.Position3DOrBuilder> 
      getRefLineOrBuilderList();
  /**
   * <pre>
   * 可选，此路段的参考线信息（0号车道中心线）
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D refLine = 5;</code>
   */
  road.data.proto.Position3DOrBuilder getRefLineOrBuilder(
      int index);

  /**
   * <pre>
   *可选，该路段转向拓展集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
   */
  java.util.List<road.data.proto.MovementEx> 
      getMovementsExList();
  /**
   * <pre>
   *可选，该路段转向拓展集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
   */
  road.data.proto.MovementEx getMovementsEx(int index);
  /**
   * <pre>
   *可选，该路段转向拓展集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
   */
  int getMovementsExCount();
  /**
   * <pre>
   *可选，该路段转向拓展集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
   */
  java.util.List<? extends road.data.proto.MovementExOrBuilder> 
      getMovementsExOrBuilderList();
  /**
   * <pre>
   *可选，该路段转向拓展集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementsEx = 6;</code>
   */
  road.data.proto.MovementExOrBuilder getMovementsExOrBuilder(
      int index);

  /**
   * <pre>
   *可选，该路段包含的路段区域分段集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
   */
  java.util.List<road.data.proto.Section> 
      getSectionsList();
  /**
   * <pre>
   *可选，该路段包含的路段区域分段集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
   */
  road.data.proto.Section getSections(int index);
  /**
   * <pre>
   *可选，该路段包含的路段区域分段集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
   */
  int getSectionsCount();
  /**
   * <pre>
   *可选，该路段包含的路段区域分段集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
   */
  java.util.List<? extends road.data.proto.SectionOrBuilder> 
      getSectionsOrBuilderList();
  /**
   * <pre>
   *可选，该路段包含的路段区域分段集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Section sections = 7;</code>
   */
  road.data.proto.SectionOrBuilder getSectionsOrBuilder(
      int index);
}
