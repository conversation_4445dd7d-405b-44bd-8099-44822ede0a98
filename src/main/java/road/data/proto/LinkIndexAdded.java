// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *进口道级指标  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.LinkIndexAdded}
 */
public  final class LinkIndexAdded extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.LinkIndexAdded)
    LinkIndexAddedOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LinkIndexAdded.newBuilder() to construct.
  private LinkIndexAdded(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LinkIndexAdded() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LinkIndexAdded();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private LinkIndexAdded(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 16: {

            linkCapacity_ = input.readUInt32();
            break;
          }
          case 24: {

            linkSaturation_ = input.readUInt32();
            break;
          }
          case 32: {

            linkSpaceOccupy_ = input.readUInt32();
            break;
          }
          case 40: {

            linkTimeOccupy_ = input.readUInt32();
            break;
          }
          case 48: {

            linkAvgGrnQueue_ = input.readUInt32();
            break;
          }
          case 56: {

            linkGrnUtilization_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkIndexAdded_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkIndexAdded_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.LinkIndexAdded.class, road.data.proto.LinkIndexAdded.Builder.class);
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 1;
  private long timestamp_;
  /**
   * <pre>
   *数据时间 Unix timestamp数据时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 1;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int LINKCAPACITY_FIELD_NUMBER = 2;
  private int linkCapacity_;
  /**
   * <pre>
   *可选，进口道通行能力，0.01pcu/h
   * </pre>
   *
   * <code>uint32 linkCapacity = 2;</code>
   */
  public int getLinkCapacity() {
    return linkCapacity_;
  }

  public static final int LINKSATURATION_FIELD_NUMBER = 3;
  private int linkSaturation_;
  /**
   * <pre>
   *可选，进口道平均饱和度，0.01%
   * </pre>
   *
   * <code>uint32 linkSaturation = 3;</code>
   */
  public int getLinkSaturation() {
    return linkSaturation_;
  }

  public static final int LINKSPACEOCCUPY_FIELD_NUMBER = 4;
  private int linkSpaceOccupy_;
  /**
   * <pre>
   *可选，进口道平均车道空间占有率，0.01%
   * </pre>
   *
   * <code>uint32 linkSpaceOccupy = 4;</code>
   */
  public int getLinkSpaceOccupy() {
    return linkSpaceOccupy_;
  }

  public static final int LINKTIMEOCCUPY_FIELD_NUMBER = 5;
  private int linkTimeOccupy_;
  /**
   * <pre>
   *可选，进口道平均车道时间占有率，0.01%
   * </pre>
   *
   * <code>uint32 linkTimeOccupy = 5;</code>
   */
  public int getLinkTimeOccupy() {
    return linkTimeOccupy_;
  }

  public static final int LINKAVGGRNQUEUE_FIELD_NUMBER = 6;
  private int linkAvgGrnQueue_;
  /**
   * <pre>
   *可选，进口道绿初车辆平均排队长度，0.01m
   * </pre>
   *
   * <code>uint32 linkAvgGrnQueue = 6;</code>
   */
  public int getLinkAvgGrnQueue() {
    return linkAvgGrnQueue_;
  }

  public static final int LINKGRNUTILIZATION_FIELD_NUMBER = 7;
  private int linkGrnUtilization_;
  /**
   * <pre>
   *可选，时段内进口道平均绿灯利用率，0.01%
   * </pre>
   *
   * <code>uint32 linkGrnUtilization = 7;</code>
   */
  public int getLinkGrnUtilization() {
    return linkGrnUtilization_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (timestamp_ != 0L) {
      output.writeUInt64(1, timestamp_);
    }
    if (linkCapacity_ != 0) {
      output.writeUInt32(2, linkCapacity_);
    }
    if (linkSaturation_ != 0) {
      output.writeUInt32(3, linkSaturation_);
    }
    if (linkSpaceOccupy_ != 0) {
      output.writeUInt32(4, linkSpaceOccupy_);
    }
    if (linkTimeOccupy_ != 0) {
      output.writeUInt32(5, linkTimeOccupy_);
    }
    if (linkAvgGrnQueue_ != 0) {
      output.writeUInt32(6, linkAvgGrnQueue_);
    }
    if (linkGrnUtilization_ != 0) {
      output.writeUInt32(7, linkGrnUtilization_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, timestamp_);
    }
    if (linkCapacity_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, linkCapacity_);
    }
    if (linkSaturation_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, linkSaturation_);
    }
    if (linkSpaceOccupy_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, linkSpaceOccupy_);
    }
    if (linkTimeOccupy_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(5, linkTimeOccupy_);
    }
    if (linkAvgGrnQueue_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(6, linkAvgGrnQueue_);
    }
    if (linkGrnUtilization_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(7, linkGrnUtilization_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.LinkIndexAdded)) {
      return super.equals(obj);
    }
    road.data.proto.LinkIndexAdded other = (road.data.proto.LinkIndexAdded) obj;

    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (getLinkCapacity()
        != other.getLinkCapacity()) return false;
    if (getLinkSaturation()
        != other.getLinkSaturation()) return false;
    if (getLinkSpaceOccupy()
        != other.getLinkSpaceOccupy()) return false;
    if (getLinkTimeOccupy()
        != other.getLinkTimeOccupy()) return false;
    if (getLinkAvgGrnQueue()
        != other.getLinkAvgGrnQueue()) return false;
    if (getLinkGrnUtilization()
        != other.getLinkGrnUtilization()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    hash = (37 * hash) + LINKCAPACITY_FIELD_NUMBER;
    hash = (53 * hash) + getLinkCapacity();
    hash = (37 * hash) + LINKSATURATION_FIELD_NUMBER;
    hash = (53 * hash) + getLinkSaturation();
    hash = (37 * hash) + LINKSPACEOCCUPY_FIELD_NUMBER;
    hash = (53 * hash) + getLinkSpaceOccupy();
    hash = (37 * hash) + LINKTIMEOCCUPY_FIELD_NUMBER;
    hash = (53 * hash) + getLinkTimeOccupy();
    hash = (37 * hash) + LINKAVGGRNQUEUE_FIELD_NUMBER;
    hash = (53 * hash) + getLinkAvgGrnQueue();
    hash = (37 * hash) + LINKGRNUTILIZATION_FIELD_NUMBER;
    hash = (53 * hash) + getLinkGrnUtilization();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.LinkIndexAdded parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LinkIndexAdded parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LinkIndexAdded parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LinkIndexAdded parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LinkIndexAdded parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LinkIndexAdded parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LinkIndexAdded parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LinkIndexAdded parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LinkIndexAdded parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.LinkIndexAdded parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LinkIndexAdded parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LinkIndexAdded parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.LinkIndexAdded prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *进口道级指标  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.LinkIndexAdded}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.LinkIndexAdded)
      road.data.proto.LinkIndexAddedOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkIndexAdded_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkIndexAdded_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.LinkIndexAdded.class, road.data.proto.LinkIndexAdded.Builder.class);
    }

    // Construct using road.data.proto.LinkIndexAdded.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      timestamp_ = 0L;

      linkCapacity_ = 0;

      linkSaturation_ = 0;

      linkSpaceOccupy_ = 0;

      linkTimeOccupy_ = 0;

      linkAvgGrnQueue_ = 0;

      linkGrnUtilization_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkIndexAdded_descriptor;
    }

    @java.lang.Override
    public road.data.proto.LinkIndexAdded getDefaultInstanceForType() {
      return road.data.proto.LinkIndexAdded.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.LinkIndexAdded build() {
      road.data.proto.LinkIndexAdded result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.LinkIndexAdded buildPartial() {
      road.data.proto.LinkIndexAdded result = new road.data.proto.LinkIndexAdded(this);
      result.timestamp_ = timestamp_;
      result.linkCapacity_ = linkCapacity_;
      result.linkSaturation_ = linkSaturation_;
      result.linkSpaceOccupy_ = linkSpaceOccupy_;
      result.linkTimeOccupy_ = linkTimeOccupy_;
      result.linkAvgGrnQueue_ = linkAvgGrnQueue_;
      result.linkGrnUtilization_ = linkGrnUtilization_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.LinkIndexAdded) {
        return mergeFrom((road.data.proto.LinkIndexAdded)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.LinkIndexAdded other) {
      if (other == road.data.proto.LinkIndexAdded.getDefaultInstance()) return this;
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (other.getLinkCapacity() != 0) {
        setLinkCapacity(other.getLinkCapacity());
      }
      if (other.getLinkSaturation() != 0) {
        setLinkSaturation(other.getLinkSaturation());
      }
      if (other.getLinkSpaceOccupy() != 0) {
        setLinkSpaceOccupy(other.getLinkSpaceOccupy());
      }
      if (other.getLinkTimeOccupy() != 0) {
        setLinkTimeOccupy(other.getLinkTimeOccupy());
      }
      if (other.getLinkAvgGrnQueue() != 0) {
        setLinkAvgGrnQueue(other.getLinkAvgGrnQueue());
      }
      if (other.getLinkGrnUtilization() != 0) {
        setLinkGrnUtilization(other.getLinkGrnUtilization());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.LinkIndexAdded parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.LinkIndexAdded) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     *数据时间 Unix timestamp数据时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     *数据时间 Unix timestamp数据时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *数据时间 Unix timestamp数据时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private int linkCapacity_ ;
    /**
     * <pre>
     *可选，进口道通行能力，0.01pcu/h
     * </pre>
     *
     * <code>uint32 linkCapacity = 2;</code>
     */
    public int getLinkCapacity() {
      return linkCapacity_;
    }
    /**
     * <pre>
     *可选，进口道通行能力，0.01pcu/h
     * </pre>
     *
     * <code>uint32 linkCapacity = 2;</code>
     */
    public Builder setLinkCapacity(int value) {
      
      linkCapacity_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，进口道通行能力，0.01pcu/h
     * </pre>
     *
     * <code>uint32 linkCapacity = 2;</code>
     */
    public Builder clearLinkCapacity() {
      
      linkCapacity_ = 0;
      onChanged();
      return this;
    }

    private int linkSaturation_ ;
    /**
     * <pre>
     *可选，进口道平均饱和度，0.01%
     * </pre>
     *
     * <code>uint32 linkSaturation = 3;</code>
     */
    public int getLinkSaturation() {
      return linkSaturation_;
    }
    /**
     * <pre>
     *可选，进口道平均饱和度，0.01%
     * </pre>
     *
     * <code>uint32 linkSaturation = 3;</code>
     */
    public Builder setLinkSaturation(int value) {
      
      linkSaturation_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，进口道平均饱和度，0.01%
     * </pre>
     *
     * <code>uint32 linkSaturation = 3;</code>
     */
    public Builder clearLinkSaturation() {
      
      linkSaturation_ = 0;
      onChanged();
      return this;
    }

    private int linkSpaceOccupy_ ;
    /**
     * <pre>
     *可选，进口道平均车道空间占有率，0.01%
     * </pre>
     *
     * <code>uint32 linkSpaceOccupy = 4;</code>
     */
    public int getLinkSpaceOccupy() {
      return linkSpaceOccupy_;
    }
    /**
     * <pre>
     *可选，进口道平均车道空间占有率，0.01%
     * </pre>
     *
     * <code>uint32 linkSpaceOccupy = 4;</code>
     */
    public Builder setLinkSpaceOccupy(int value) {
      
      linkSpaceOccupy_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，进口道平均车道空间占有率，0.01%
     * </pre>
     *
     * <code>uint32 linkSpaceOccupy = 4;</code>
     */
    public Builder clearLinkSpaceOccupy() {
      
      linkSpaceOccupy_ = 0;
      onChanged();
      return this;
    }

    private int linkTimeOccupy_ ;
    /**
     * <pre>
     *可选，进口道平均车道时间占有率，0.01%
     * </pre>
     *
     * <code>uint32 linkTimeOccupy = 5;</code>
     */
    public int getLinkTimeOccupy() {
      return linkTimeOccupy_;
    }
    /**
     * <pre>
     *可选，进口道平均车道时间占有率，0.01%
     * </pre>
     *
     * <code>uint32 linkTimeOccupy = 5;</code>
     */
    public Builder setLinkTimeOccupy(int value) {
      
      linkTimeOccupy_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，进口道平均车道时间占有率，0.01%
     * </pre>
     *
     * <code>uint32 linkTimeOccupy = 5;</code>
     */
    public Builder clearLinkTimeOccupy() {
      
      linkTimeOccupy_ = 0;
      onChanged();
      return this;
    }

    private int linkAvgGrnQueue_ ;
    /**
     * <pre>
     *可选，进口道绿初车辆平均排队长度，0.01m
     * </pre>
     *
     * <code>uint32 linkAvgGrnQueue = 6;</code>
     */
    public int getLinkAvgGrnQueue() {
      return linkAvgGrnQueue_;
    }
    /**
     * <pre>
     *可选，进口道绿初车辆平均排队长度，0.01m
     * </pre>
     *
     * <code>uint32 linkAvgGrnQueue = 6;</code>
     */
    public Builder setLinkAvgGrnQueue(int value) {
      
      linkAvgGrnQueue_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，进口道绿初车辆平均排队长度，0.01m
     * </pre>
     *
     * <code>uint32 linkAvgGrnQueue = 6;</code>
     */
    public Builder clearLinkAvgGrnQueue() {
      
      linkAvgGrnQueue_ = 0;
      onChanged();
      return this;
    }

    private int linkGrnUtilization_ ;
    /**
     * <pre>
     *可选，时段内进口道平均绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 linkGrnUtilization = 7;</code>
     */
    public int getLinkGrnUtilization() {
      return linkGrnUtilization_;
    }
    /**
     * <pre>
     *可选，时段内进口道平均绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 linkGrnUtilization = 7;</code>
     */
    public Builder setLinkGrnUtilization(int value) {
      
      linkGrnUtilization_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，时段内进口道平均绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 linkGrnUtilization = 7;</code>
     */
    public Builder clearLinkGrnUtilization() {
      
      linkGrnUtilization_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.LinkIndexAdded)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.LinkIndexAdded)
  private static final road.data.proto.LinkIndexAdded DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.LinkIndexAdded();
  }

  public static road.data.proto.LinkIndexAdded getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LinkIndexAdded>
      PARSER = new com.google.protobuf.AbstractParser<LinkIndexAdded>() {
    @java.lang.Override
    public LinkIndexAdded parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new LinkIndexAdded(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<LinkIndexAdded> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LinkIndexAdded> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.LinkIndexAdded getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

