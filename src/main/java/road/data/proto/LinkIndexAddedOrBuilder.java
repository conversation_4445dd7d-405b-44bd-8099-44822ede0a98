// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LinkIndexAddedOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LinkIndexAdded)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *数据时间 Unix timestamp数据时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 1;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   *可选，进口道通行能力，0.01pcu/h
   * </pre>
   *
   * <code>uint32 linkCapacity = 2;</code>
   */
  int getLinkCapacity();

  /**
   * <pre>
   *可选，进口道平均饱和度，0.01%
   * </pre>
   *
   * <code>uint32 linkSaturation = 3;</code>
   */
  int getLinkSaturation();

  /**
   * <pre>
   *可选，进口道平均车道空间占有率，0.01%
   * </pre>
   *
   * <code>uint32 linkSpaceOccupy = 4;</code>
   */
  int getLinkSpaceOccupy();

  /**
   * <pre>
   *可选，进口道平均车道时间占有率，0.01%
   * </pre>
   *
   * <code>uint32 linkTimeOccupy = 5;</code>
   */
  int getLinkTimeOccupy();

  /**
   * <pre>
   *可选，进口道绿初车辆平均排队长度，0.01m
   * </pre>
   *
   * <code>uint32 linkAvgGrnQueue = 6;</code>
   */
  int getLinkAvgGrnQueue();

  /**
   * <pre>
   *可选，时段内进口道平均绿灯利用率，0.01%
   * </pre>
   *
   * <code>uint32 linkGrnUtilization = 7;</code>
   */
  int getLinkGrnUtilization();
}
