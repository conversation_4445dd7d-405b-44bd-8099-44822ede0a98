// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LinkOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.Link)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 可选，名称
   * </pre>
   *
   * <code>string name = 1;</code>
   */
  java.lang.String getName();
  /**
   * <pre>
   * 可选，名称
   * </pre>
   *
   * <code>string name = 1;</code>
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * 上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
   */
  boolean hasUpstreamNodeId();
  /**
   * <pre>
   * 上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
   */
  road.data.proto.NodeReferenceId getUpstreamNodeId();
  /**
   * <pre>
   * 上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 2;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder();

  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  java.util.List<road.data.proto.RegulatorySpeedLimit> 
      getSpeedLimitsList();
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  road.data.proto.RegulatorySpeedLimit getSpeedLimits(int index);
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  int getSpeedLimitsCount();
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  java.util.List<? extends road.data.proto.RegulatorySpeedLimitOrBuilder> 
      getSpeedLimitsOrBuilderList();
  /**
   * <pre>
   * 可选，限速集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RegulatorySpeedLimit speedLimits = 3;</code>
   */
  road.data.proto.RegulatorySpeedLimitOrBuilder getSpeedLimitsOrBuilder(
      int index);

  /**
   * <pre>
   * 可选，车道宽度，分辨率为 1cm
   * </pre>
   *
   * <code>uint32 linkWidth = 4;</code>
   */
  int getLinkWidth();

  /**
   * <pre>
   * 可选，此路段中心线信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
   */
  java.util.List<road.data.proto.Position3D> 
      getPointsList();
  /**
   * <pre>
   * 可选，此路段中心线信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
   */
  road.data.proto.Position3D getPoints(int index);
  /**
   * <pre>
   * 可选，此路段中心线信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
   */
  int getPointsCount();
  /**
   * <pre>
   * 可选，此路段中心线信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
   */
  java.util.List<? extends road.data.proto.Position3DOrBuilder> 
      getPointsOrBuilderList();
  /**
   * <pre>
   * 可选，此路段中心线信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D points = 5;</code>
   */
  road.data.proto.Position3DOrBuilder getPointsOrBuilder(
      int index);

  /**
   * <pre>
   *可选，该路段转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
   */
  java.util.List<road.data.proto.Movement> 
      getMovementsList();
  /**
   * <pre>
   *可选，该路段转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
   */
  road.data.proto.Movement getMovements(int index);
  /**
   * <pre>
   *可选，该路段转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
   */
  int getMovementsCount();
  /**
   * <pre>
   *可选，该路段转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
   */
  java.util.List<? extends road.data.proto.MovementOrBuilder> 
      getMovementsOrBuilderList();
  /**
   * <pre>
   *可选，该路段转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Movement movements = 6;</code>
   */
  road.data.proto.MovementOrBuilder getMovementsOrBuilder(
      int index);

  /**
   * <pre>
   * 定义车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
   */
  java.util.List<road.data.proto.Lane> 
      getLanesList();
  /**
   * <pre>
   * 定义车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
   */
  road.data.proto.Lane getLanes(int index);
  /**
   * <pre>
   * 定义车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
   */
  int getLanesCount();
  /**
   * <pre>
   * 定义车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
   */
  java.util.List<? extends road.data.proto.LaneOrBuilder> 
      getLanesOrBuilderList();
  /**
   * <pre>
   * 定义车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Lane lanes = 7;</code>
   */
  road.data.proto.LaneOrBuilder getLanesOrBuilder(
      int index);
}
