// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *有向路段对象  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.LinkStatInfo}
 */
public  final class LinkStatInfo extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.LinkStatInfo)
    LinkStatInfoOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LinkStatInfo.newBuilder() to construct.
  private LinkStatInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LinkStatInfo() {
    name_ = "";
    extId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LinkStatInfo();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private LinkStatInfo(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (upstreamNodeId_ != null) {
              subBuilder = upstreamNodeId_.toBuilder();
            }
            upstreamNodeId_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(upstreamNodeId_);
              upstreamNodeId_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 26: {
            road.data.proto.NodeStatInfo.Builder subBuilder = null;
            if (nodeStatInfo_ != null) {
              subBuilder = nodeStatInfo_.toBuilder();
            }
            nodeStatInfo_ = input.readMessage(road.data.proto.NodeStatInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(nodeStatInfo_);
              nodeStatInfo_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            extId_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkStatInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkStatInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.LinkStatInfo.class, road.data.proto.LinkStatInfo.Builder.class);
  }

  public static final int UPSTREAMNODEID_FIELD_NUMBER = 1;
  private road.data.proto.NodeReferenceId upstreamNodeId_;
  /**
   * <pre>
   * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
   */
  public boolean hasUpstreamNodeId() {
    return upstreamNodeId_ != null;
  }
  /**
   * <pre>
   * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
   */
  public road.data.proto.NodeReferenceId getUpstreamNodeId() {
    return upstreamNodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
  }
  /**
   * <pre>
   * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder() {
    return getUpstreamNodeId();
  }

  public static final int NAME_FIELD_NUMBER = 2;
  private volatile java.lang.Object name_;
  /**
   * <pre>
   * 由字符串表达的路段名称或者描述
   * </pre>
   *
   * <code>string name = 2;</code>
   */
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 由字符串表达的路段名称或者描述
   * </pre>
   *
   * <code>string name = 2;</code>
   */
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NODESTATINFO_FIELD_NUMBER = 3;
  private road.data.proto.NodeStatInfo nodeStatInfo_;
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
   */
  public boolean hasNodeStatInfo() {
    return nodeStatInfo_ != null;
  }
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
   */
  public road.data.proto.NodeStatInfo getNodeStatInfo() {
    return nodeStatInfo_ == null ? road.data.proto.NodeStatInfo.getDefaultInstance() : nodeStatInfo_;
  }
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
   */
  public road.data.proto.NodeStatInfoOrBuilder getNodeStatInfoOrBuilder() {
    return getNodeStatInfo();
  }

  public static final int EXTID_FIELD_NUMBER = 4;
  private volatile java.lang.Object extId_;
  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 4;</code>
   */
  public java.lang.String getExtId() {
    java.lang.Object ref = extId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      extId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 4;</code>
   */
  public com.google.protobuf.ByteString
      getExtIdBytes() {
    java.lang.Object ref = extId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      extId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (upstreamNodeId_ != null) {
      output.writeMessage(1, getUpstreamNodeId());
    }
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
    }
    if (nodeStatInfo_ != null) {
      output.writeMessage(3, getNodeStatInfo());
    }
    if (!getExtIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, extId_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (upstreamNodeId_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getUpstreamNodeId());
    }
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
    }
    if (nodeStatInfo_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getNodeStatInfo());
    }
    if (!getExtIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, extId_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.LinkStatInfo)) {
      return super.equals(obj);
    }
    road.data.proto.LinkStatInfo other = (road.data.proto.LinkStatInfo) obj;

    if (hasUpstreamNodeId() != other.hasUpstreamNodeId()) return false;
    if (hasUpstreamNodeId()) {
      if (!getUpstreamNodeId()
          .equals(other.getUpstreamNodeId())) return false;
    }
    if (!getName()
        .equals(other.getName())) return false;
    if (hasNodeStatInfo() != other.hasNodeStatInfo()) return false;
    if (hasNodeStatInfo()) {
      if (!getNodeStatInfo()
          .equals(other.getNodeStatInfo())) return false;
    }
    if (!getExtId()
        .equals(other.getExtId())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasUpstreamNodeId()) {
      hash = (37 * hash) + UPSTREAMNODEID_FIELD_NUMBER;
      hash = (53 * hash) + getUpstreamNodeId().hashCode();
    }
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    if (hasNodeStatInfo()) {
      hash = (37 * hash) + NODESTATINFO_FIELD_NUMBER;
      hash = (53 * hash) + getNodeStatInfo().hashCode();
    }
    hash = (37 * hash) + EXTID_FIELD_NUMBER;
    hash = (53 * hash) + getExtId().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.LinkStatInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LinkStatInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LinkStatInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LinkStatInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LinkStatInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LinkStatInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LinkStatInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LinkStatInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LinkStatInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.LinkStatInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LinkStatInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LinkStatInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.LinkStatInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *有向路段对象  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.LinkStatInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.LinkStatInfo)
      road.data.proto.LinkStatInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkStatInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkStatInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.LinkStatInfo.class, road.data.proto.LinkStatInfo.Builder.class);
    }

    // Construct using road.data.proto.LinkStatInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = null;
      } else {
        upstreamNodeId_ = null;
        upstreamNodeIdBuilder_ = null;
      }
      name_ = "";

      if (nodeStatInfoBuilder_ == null) {
        nodeStatInfo_ = null;
      } else {
        nodeStatInfo_ = null;
        nodeStatInfoBuilder_ = null;
      }
      extId_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LinkStatInfo_descriptor;
    }

    @java.lang.Override
    public road.data.proto.LinkStatInfo getDefaultInstanceForType() {
      return road.data.proto.LinkStatInfo.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.LinkStatInfo build() {
      road.data.proto.LinkStatInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.LinkStatInfo buildPartial() {
      road.data.proto.LinkStatInfo result = new road.data.proto.LinkStatInfo(this);
      if (upstreamNodeIdBuilder_ == null) {
        result.upstreamNodeId_ = upstreamNodeId_;
      } else {
        result.upstreamNodeId_ = upstreamNodeIdBuilder_.build();
      }
      result.name_ = name_;
      if (nodeStatInfoBuilder_ == null) {
        result.nodeStatInfo_ = nodeStatInfo_;
      } else {
        result.nodeStatInfo_ = nodeStatInfoBuilder_.build();
      }
      result.extId_ = extId_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.LinkStatInfo) {
        return mergeFrom((road.data.proto.LinkStatInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.LinkStatInfo other) {
      if (other == road.data.proto.LinkStatInfo.getDefaultInstance()) return this;
      if (other.hasUpstreamNodeId()) {
        mergeUpstreamNodeId(other.getUpstreamNodeId());
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (other.hasNodeStatInfo()) {
        mergeNodeStatInfo(other.getNodeStatInfo());
      }
      if (!other.getExtId().isEmpty()) {
        extId_ = other.extId_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.LinkStatInfo parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.LinkStatInfo) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.NodeReferenceId upstreamNodeId_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> upstreamNodeIdBuilder_;
    /**
     * <pre>
     * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public boolean hasUpstreamNodeId() {
      return upstreamNodeIdBuilder_ != null || upstreamNodeId_ != null;
    }
    /**
     * <pre>
     * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceId getUpstreamNodeId() {
      if (upstreamNodeIdBuilder_ == null) {
        return upstreamNodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
      } else {
        return upstreamNodeIdBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public Builder setUpstreamNodeId(road.data.proto.NodeReferenceId value) {
      if (upstreamNodeIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        upstreamNodeId_ = value;
        onChanged();
      } else {
        upstreamNodeIdBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public Builder setUpstreamNodeId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = builderForValue.build();
        onChanged();
      } else {
        upstreamNodeIdBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public Builder mergeUpstreamNodeId(road.data.proto.NodeReferenceId value) {
      if (upstreamNodeIdBuilder_ == null) {
        if (upstreamNodeId_ != null) {
          upstreamNodeId_ =
            road.data.proto.NodeReferenceId.newBuilder(upstreamNodeId_).mergeFrom(value).buildPartial();
        } else {
          upstreamNodeId_ = value;
        }
        onChanged();
      } else {
        upstreamNodeIdBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public Builder clearUpstreamNodeId() {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = null;
        onChanged();
      } else {
        upstreamNodeId_ = null;
        upstreamNodeIdBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getUpstreamNodeIdBuilder() {
      
      onChanged();
      return getUpstreamNodeIdFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder() {
      if (upstreamNodeIdBuilder_ != null) {
        return upstreamNodeIdBuilder_.getMessageOrBuilder();
      } else {
        return upstreamNodeId_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
      }
    }
    /**
     * <pre>
     * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getUpstreamNodeIdFieldBuilder() {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getUpstreamNodeId(),
                getParentForChildren(),
                isClean());
        upstreamNodeId_ = null;
      }
      return upstreamNodeIdBuilder_;
    }

    private java.lang.Object name_ = "";
    /**
     * <pre>
     * 由字符串表达的路段名称或者描述
     * </pre>
     *
     * <code>string name = 2;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 由字符串表达的路段名称或者描述
     * </pre>
     *
     * <code>string name = 2;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 由字符串表达的路段名称或者描述
     * </pre>
     *
     * <code>string name = 2;</code>
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 由字符串表达的路段名称或者描述
     * </pre>
     *
     * <code>string name = 2;</code>
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 由字符串表达的路段名称或者描述
     * </pre>
     *
     * <code>string name = 2;</code>
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private road.data.proto.NodeStatInfo nodeStatInfo_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeStatInfo, road.data.proto.NodeStatInfo.Builder, road.data.proto.NodeStatInfoOrBuilder> nodeStatInfoBuilder_;
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public boolean hasNodeStatInfo() {
      return nodeStatInfoBuilder_ != null || nodeStatInfo_ != null;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public road.data.proto.NodeStatInfo getNodeStatInfo() {
      if (nodeStatInfoBuilder_ == null) {
        return nodeStatInfo_ == null ? road.data.proto.NodeStatInfo.getDefaultInstance() : nodeStatInfo_;
      } else {
        return nodeStatInfoBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public Builder setNodeStatInfo(road.data.proto.NodeStatInfo value) {
      if (nodeStatInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        nodeStatInfo_ = value;
        onChanged();
      } else {
        nodeStatInfoBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public Builder setNodeStatInfo(
        road.data.proto.NodeStatInfo.Builder builderForValue) {
      if (nodeStatInfoBuilder_ == null) {
        nodeStatInfo_ = builderForValue.build();
        onChanged();
      } else {
        nodeStatInfoBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public Builder mergeNodeStatInfo(road.data.proto.NodeStatInfo value) {
      if (nodeStatInfoBuilder_ == null) {
        if (nodeStatInfo_ != null) {
          nodeStatInfo_ =
            road.data.proto.NodeStatInfo.newBuilder(nodeStatInfo_).mergeFrom(value).buildPartial();
        } else {
          nodeStatInfo_ = value;
        }
        onChanged();
      } else {
        nodeStatInfoBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public Builder clearNodeStatInfo() {
      if (nodeStatInfoBuilder_ == null) {
        nodeStatInfo_ = null;
        onChanged();
      } else {
        nodeStatInfo_ = null;
        nodeStatInfoBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public road.data.proto.NodeStatInfo.Builder getNodeStatInfoBuilder() {
      
      onChanged();
      return getNodeStatInfoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public road.data.proto.NodeStatInfoOrBuilder getNodeStatInfoOrBuilder() {
      if (nodeStatInfoBuilder_ != null) {
        return nodeStatInfoBuilder_.getMessageOrBuilder();
      } else {
        return nodeStatInfo_ == null ?
            road.data.proto.NodeStatInfo.getDefaultInstance() : nodeStatInfo_;
      }
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeStatInfo, road.data.proto.NodeStatInfo.Builder, road.data.proto.NodeStatInfoOrBuilder> 
        getNodeStatInfoFieldBuilder() {
      if (nodeStatInfoBuilder_ == null) {
        nodeStatInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeStatInfo, road.data.proto.NodeStatInfo.Builder, road.data.proto.NodeStatInfoOrBuilder>(
                getNodeStatInfo(),
                getParentForChildren(),
                isClean());
        nodeStatInfo_ = null;
      }
      return nodeStatInfoBuilder_;
    }

    private java.lang.Object extId_ = "";
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 4;</code>
     */
    public java.lang.String getExtId() {
      java.lang.Object ref = extId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        extId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 4;</code>
     */
    public com.google.protobuf.ByteString
        getExtIdBytes() {
      java.lang.Object ref = extId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 4;</code>
     */
    public Builder setExtId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      extId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 4;</code>
     */
    public Builder clearExtId() {
      
      extId_ = getDefaultInstance().getExtId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 4;</code>
     */
    public Builder setExtIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      extId_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.LinkStatInfo)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.LinkStatInfo)
  private static final road.data.proto.LinkStatInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.LinkStatInfo();
  }

  public static road.data.proto.LinkStatInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LinkStatInfo>
      PARSER = new com.google.protobuf.AbstractParser<LinkStatInfo>() {
    @java.lang.Override
    public LinkStatInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new LinkStatInfo(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<LinkStatInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LinkStatInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.LinkStatInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

