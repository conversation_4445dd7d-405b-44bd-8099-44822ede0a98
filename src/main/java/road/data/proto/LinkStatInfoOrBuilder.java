// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface LinkStatInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.LinkStatInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
   */
  boolean hasUpstreamNodeId();
  /**
   * <pre>
   * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
   */
  road.data.proto.NodeReferenceId getUpstreamNodeId();
  /**
   * <pre>
   * 节点 ID 是由一个全局唯一的地区 ID 和一个地区内部唯一的节点 ID 组成。此 ID 为关联 Link 的上游节点 ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder();

  /**
   * <pre>
   * 由字符串表达的路段名称或者描述
   * </pre>
   *
   * <code>string name = 2;</code>
   */
  java.lang.String getName();
  /**
   * <pre>
   * 由字符串表达的路段名称或者描述
   * </pre>
   *
   * <code>string name = 2;</code>
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
   */
  boolean hasNodeStatInfo();
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
   */
  road.data.proto.NodeStatInfo getNodeStatInfo();
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
   */
  road.data.proto.NodeStatInfoOrBuilder getNodeStatInfoOrBuilder();

  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 4;</code>
   */
  java.lang.String getExtId();
  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 4;</code>
   */
  com.google.protobuf.ByteString
      getExtIdBytes();
}
