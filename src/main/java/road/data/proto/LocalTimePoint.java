// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *信控方案执行时间      
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.LocalTimePoint}
 */
public  final class LocalTimePoint extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.LocalTimePoint)
    LocalTimePointOrBuilder {
private static final long serialVersionUID = 0L;
  // Use LocalTimePoint.newBuilder() to construct.
  private LocalTimePoint(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private LocalTimePoint() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new LocalTimePoint();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private LocalTimePoint(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            hh_ = input.readInt32();
            break;
          }
          case 16: {

            mm_ = input.readInt32();
            break;
          }
          case 24: {

            ss_ = input.readInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LocalTimePoint_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LocalTimePoint_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.LocalTimePoint.class, road.data.proto.LocalTimePoint.Builder.class);
  }

  public static final int HH_FIELD_NUMBER = 1;
  private int hh_;
  /**
   * <pre>
   *时
   * </pre>
   *
   * <code>int32 hh = 1;</code>
   */
  public int getHh() {
    return hh_;
  }

  public static final int MM_FIELD_NUMBER = 2;
  private int mm_;
  /**
   * <pre>
   *分
   * </pre>
   *
   * <code>int32 mm = 2;</code>
   */
  public int getMm() {
    return mm_;
  }

  public static final int SS_FIELD_NUMBER = 3;
  private int ss_;
  /**
   * <pre>
   *秒
   * </pre>
   *
   * <code>int32 ss = 3;</code>
   */
  public int getSs() {
    return ss_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (hh_ != 0) {
      output.writeInt32(1, hh_);
    }
    if (mm_ != 0) {
      output.writeInt32(2, mm_);
    }
    if (ss_ != 0) {
      output.writeInt32(3, ss_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (hh_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, hh_);
    }
    if (mm_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, mm_);
    }
    if (ss_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, ss_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.LocalTimePoint)) {
      return super.equals(obj);
    }
    road.data.proto.LocalTimePoint other = (road.data.proto.LocalTimePoint) obj;

    if (getHh()
        != other.getHh()) return false;
    if (getMm()
        != other.getMm()) return false;
    if (getSs()
        != other.getSs()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + HH_FIELD_NUMBER;
    hash = (53 * hash) + getHh();
    hash = (37 * hash) + MM_FIELD_NUMBER;
    hash = (53 * hash) + getMm();
    hash = (37 * hash) + SS_FIELD_NUMBER;
    hash = (53 * hash) + getSs();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.LocalTimePoint parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LocalTimePoint parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LocalTimePoint parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LocalTimePoint parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LocalTimePoint parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.LocalTimePoint parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.LocalTimePoint parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LocalTimePoint parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LocalTimePoint parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.LocalTimePoint parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.LocalTimePoint parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.LocalTimePoint parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.LocalTimePoint prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *信控方案执行时间      
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.LocalTimePoint}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.LocalTimePoint)
      road.data.proto.LocalTimePointOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LocalTimePoint_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LocalTimePoint_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.LocalTimePoint.class, road.data.proto.LocalTimePoint.Builder.class);
    }

    // Construct using road.data.proto.LocalTimePoint.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      hh_ = 0;

      mm_ = 0;

      ss_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_LocalTimePoint_descriptor;
    }

    @java.lang.Override
    public road.data.proto.LocalTimePoint getDefaultInstanceForType() {
      return road.data.proto.LocalTimePoint.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.LocalTimePoint build() {
      road.data.proto.LocalTimePoint result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.LocalTimePoint buildPartial() {
      road.data.proto.LocalTimePoint result = new road.data.proto.LocalTimePoint(this);
      result.hh_ = hh_;
      result.mm_ = mm_;
      result.ss_ = ss_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.LocalTimePoint) {
        return mergeFrom((road.data.proto.LocalTimePoint)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.LocalTimePoint other) {
      if (other == road.data.proto.LocalTimePoint.getDefaultInstance()) return this;
      if (other.getHh() != 0) {
        setHh(other.getHh());
      }
      if (other.getMm() != 0) {
        setMm(other.getMm());
      }
      if (other.getSs() != 0) {
        setSs(other.getSs());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.LocalTimePoint parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.LocalTimePoint) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int hh_ ;
    /**
     * <pre>
     *时
     * </pre>
     *
     * <code>int32 hh = 1;</code>
     */
    public int getHh() {
      return hh_;
    }
    /**
     * <pre>
     *时
     * </pre>
     *
     * <code>int32 hh = 1;</code>
     */
    public Builder setHh(int value) {
      
      hh_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *时
     * </pre>
     *
     * <code>int32 hh = 1;</code>
     */
    public Builder clearHh() {
      
      hh_ = 0;
      onChanged();
      return this;
    }

    private int mm_ ;
    /**
     * <pre>
     *分
     * </pre>
     *
     * <code>int32 mm = 2;</code>
     */
    public int getMm() {
      return mm_;
    }
    /**
     * <pre>
     *分
     * </pre>
     *
     * <code>int32 mm = 2;</code>
     */
    public Builder setMm(int value) {
      
      mm_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *分
     * </pre>
     *
     * <code>int32 mm = 2;</code>
     */
    public Builder clearMm() {
      
      mm_ = 0;
      onChanged();
      return this;
    }

    private int ss_ ;
    /**
     * <pre>
     *秒
     * </pre>
     *
     * <code>int32 ss = 3;</code>
     */
    public int getSs() {
      return ss_;
    }
    /**
     * <pre>
     *秒
     * </pre>
     *
     * <code>int32 ss = 3;</code>
     */
    public Builder setSs(int value) {
      
      ss_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *秒
     * </pre>
     *
     * <code>int32 ss = 3;</code>
     */
    public Builder clearSs() {
      
      ss_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.LocalTimePoint)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.LocalTimePoint)
  private static final road.data.proto.LocalTimePoint DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.LocalTimePoint();
  }

  public static road.data.proto.LocalTimePoint getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LocalTimePoint>
      PARSER = new com.google.protobuf.AbstractParser<LocalTimePoint>() {
    @java.lang.Override
    public LocalTimePoint parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new LocalTimePoint(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<LocalTimePoint> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LocalTimePoint> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.LocalTimePoint getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

