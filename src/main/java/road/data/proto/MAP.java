// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *地图数据MAP  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.MAP}
 */
public  final class MAP extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.MAP)
    MAPOrBuilder {
private static final long serialVersionUID = 0L;
  // Use MAP.newBuilder() to construct.
  private MAP(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private MAP() {
    nodes_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new MAP();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private MAP(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            timestamp_ = input.readUInt32();
            break;
          }
          case 18: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              nodes_ = new java.util.ArrayList<road.data.proto.Node>();
              mutable_bitField0_ |= 0x00000001;
            }
            nodes_.add(
                input.readMessage(road.data.proto.Node.parser(), extensionRegistry));
            break;
          }
          case 24: {

            msgCnt_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        nodes_ = java.util.Collections.unmodifiableList(nodes_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MAP_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MAP_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.MAP.class, road.data.proto.MAP.Builder.class);
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 1;
  private int timestamp_;
  /**
   * <pre>
   * 1970到现在的总分钟数
   * </pre>
   *
   * <code>uint32 timestamp = 1;</code>
   */
  public int getTimestamp() {
    return timestamp_;
  }

  public static final int NODES_FIELD_NUMBER = 2;
  private java.util.List<road.data.proto.Node> nodes_;
  /**
   * <pre>
   * 交叉口集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
   */
  public java.util.List<road.data.proto.Node> getNodesList() {
    return nodes_;
  }
  /**
   * <pre>
   * 交叉口集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
   */
  public java.util.List<? extends road.data.proto.NodeOrBuilder> 
      getNodesOrBuilderList() {
    return nodes_;
  }
  /**
   * <pre>
   * 交叉口集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
   */
  public int getNodesCount() {
    return nodes_.size();
  }
  /**
   * <pre>
   * 交叉口集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
   */
  public road.data.proto.Node getNodes(int index) {
    return nodes_.get(index);
  }
  /**
   * <pre>
   * 交叉口集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
   */
  public road.data.proto.NodeOrBuilder getNodesOrBuilder(
      int index) {
    return nodes_.get(index);
  }

  public static final int MSGCNT_FIELD_NUMBER = 3;
  private int msgCnt_;
  /**
   * <pre>
   *消息编号，循环使用
   * </pre>
   *
   * <code>uint32 msgCnt = 3;</code>
   */
  public int getMsgCnt() {
    return msgCnt_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (timestamp_ != 0) {
      output.writeUInt32(1, timestamp_);
    }
    for (int i = 0; i < nodes_.size(); i++) {
      output.writeMessage(2, nodes_.get(i));
    }
    if (msgCnt_ != 0) {
      output.writeUInt32(3, msgCnt_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (timestamp_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, timestamp_);
    }
    for (int i = 0; i < nodes_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, nodes_.get(i));
    }
    if (msgCnt_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, msgCnt_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.MAP)) {
      return super.equals(obj);
    }
    road.data.proto.MAP other = (road.data.proto.MAP) obj;

    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (!getNodesList()
        .equals(other.getNodesList())) return false;
    if (getMsgCnt()
        != other.getMsgCnt()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + getTimestamp();
    if (getNodesCount() > 0) {
      hash = (37 * hash) + NODES_FIELD_NUMBER;
      hash = (53 * hash) + getNodesList().hashCode();
    }
    hash = (37 * hash) + MSGCNT_FIELD_NUMBER;
    hash = (53 * hash) + getMsgCnt();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.MAP parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MAP parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MAP parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MAP parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MAP parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MAP parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MAP parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MAP parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MAP parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.MAP parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MAP parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MAP parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.MAP prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *地图数据MAP  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.MAP}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.MAP)
      road.data.proto.MAPOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MAP_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MAP_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.MAP.class, road.data.proto.MAP.Builder.class);
    }

    // Construct using road.data.proto.MAP.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getNodesFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      timestamp_ = 0;

      if (nodesBuilder_ == null) {
        nodes_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        nodesBuilder_.clear();
      }
      msgCnt_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MAP_descriptor;
    }

    @java.lang.Override
    public road.data.proto.MAP getDefaultInstanceForType() {
      return road.data.proto.MAP.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.MAP build() {
      road.data.proto.MAP result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.MAP buildPartial() {
      road.data.proto.MAP result = new road.data.proto.MAP(this);
      int from_bitField0_ = bitField0_;
      result.timestamp_ = timestamp_;
      if (nodesBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          nodes_ = java.util.Collections.unmodifiableList(nodes_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.nodes_ = nodes_;
      } else {
        result.nodes_ = nodesBuilder_.build();
      }
      result.msgCnt_ = msgCnt_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.MAP) {
        return mergeFrom((road.data.proto.MAP)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.MAP other) {
      if (other == road.data.proto.MAP.getDefaultInstance()) return this;
      if (other.getTimestamp() != 0) {
        setTimestamp(other.getTimestamp());
      }
      if (nodesBuilder_ == null) {
        if (!other.nodes_.isEmpty()) {
          if (nodes_.isEmpty()) {
            nodes_ = other.nodes_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureNodesIsMutable();
            nodes_.addAll(other.nodes_);
          }
          onChanged();
        }
      } else {
        if (!other.nodes_.isEmpty()) {
          if (nodesBuilder_.isEmpty()) {
            nodesBuilder_.dispose();
            nodesBuilder_ = null;
            nodes_ = other.nodes_;
            bitField0_ = (bitField0_ & ~0x00000001);
            nodesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getNodesFieldBuilder() : null;
          } else {
            nodesBuilder_.addAllMessages(other.nodes_);
          }
        }
      }
      if (other.getMsgCnt() != 0) {
        setMsgCnt(other.getMsgCnt());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.MAP parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.MAP) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int timestamp_ ;
    /**
     * <pre>
     * 1970到现在的总分钟数
     * </pre>
     *
     * <code>uint32 timestamp = 1;</code>
     */
    public int getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     * 1970到现在的总分钟数
     * </pre>
     *
     * <code>uint32 timestamp = 1;</code>
     */
    public Builder setTimestamp(int value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 1970到现在的总分钟数
     * </pre>
     *
     * <code>uint32 timestamp = 1;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.Node> nodes_ =
      java.util.Collections.emptyList();
    private void ensureNodesIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        nodes_ = new java.util.ArrayList<road.data.proto.Node>(nodes_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Node, road.data.proto.Node.Builder, road.data.proto.NodeOrBuilder> nodesBuilder_;

    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public java.util.List<road.data.proto.Node> getNodesList() {
      if (nodesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(nodes_);
      } else {
        return nodesBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public int getNodesCount() {
      if (nodesBuilder_ == null) {
        return nodes_.size();
      } else {
        return nodesBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public road.data.proto.Node getNodes(int index) {
      if (nodesBuilder_ == null) {
        return nodes_.get(index);
      } else {
        return nodesBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public Builder setNodes(
        int index, road.data.proto.Node value) {
      if (nodesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNodesIsMutable();
        nodes_.set(index, value);
        onChanged();
      } else {
        nodesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public Builder setNodes(
        int index, road.data.proto.Node.Builder builderForValue) {
      if (nodesBuilder_ == null) {
        ensureNodesIsMutable();
        nodes_.set(index, builderForValue.build());
        onChanged();
      } else {
        nodesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public Builder addNodes(road.data.proto.Node value) {
      if (nodesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNodesIsMutable();
        nodes_.add(value);
        onChanged();
      } else {
        nodesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public Builder addNodes(
        int index, road.data.proto.Node value) {
      if (nodesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNodesIsMutable();
        nodes_.add(index, value);
        onChanged();
      } else {
        nodesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public Builder addNodes(
        road.data.proto.Node.Builder builderForValue) {
      if (nodesBuilder_ == null) {
        ensureNodesIsMutable();
        nodes_.add(builderForValue.build());
        onChanged();
      } else {
        nodesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public Builder addNodes(
        int index, road.data.proto.Node.Builder builderForValue) {
      if (nodesBuilder_ == null) {
        ensureNodesIsMutable();
        nodes_.add(index, builderForValue.build());
        onChanged();
      } else {
        nodesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public Builder addAllNodes(
        java.lang.Iterable<? extends road.data.proto.Node> values) {
      if (nodesBuilder_ == null) {
        ensureNodesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, nodes_);
        onChanged();
      } else {
        nodesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public Builder clearNodes() {
      if (nodesBuilder_ == null) {
        nodes_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        nodesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public Builder removeNodes(int index) {
      if (nodesBuilder_ == null) {
        ensureNodesIsMutable();
        nodes_.remove(index);
        onChanged();
      } else {
        nodesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public road.data.proto.Node.Builder getNodesBuilder(
        int index) {
      return getNodesFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public road.data.proto.NodeOrBuilder getNodesOrBuilder(
        int index) {
      if (nodesBuilder_ == null) {
        return nodes_.get(index);  } else {
        return nodesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public java.util.List<? extends road.data.proto.NodeOrBuilder> 
         getNodesOrBuilderList() {
      if (nodesBuilder_ != null) {
        return nodesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(nodes_);
      }
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public road.data.proto.Node.Builder addNodesBuilder() {
      return getNodesFieldBuilder().addBuilder(
          road.data.proto.Node.getDefaultInstance());
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public road.data.proto.Node.Builder addNodesBuilder(
        int index) {
      return getNodesFieldBuilder().addBuilder(
          index, road.data.proto.Node.getDefaultInstance());
    }
    /**
     * <pre>
     * 交叉口集合
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
     */
    public java.util.List<road.data.proto.Node.Builder> 
         getNodesBuilderList() {
      return getNodesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Node, road.data.proto.Node.Builder, road.data.proto.NodeOrBuilder> 
        getNodesFieldBuilder() {
      if (nodesBuilder_ == null) {
        nodesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.Node, road.data.proto.Node.Builder, road.data.proto.NodeOrBuilder>(
                nodes_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        nodes_ = null;
      }
      return nodesBuilder_;
    }

    private int msgCnt_ ;
    /**
     * <pre>
     *消息编号，循环使用
     * </pre>
     *
     * <code>uint32 msgCnt = 3;</code>
     */
    public int getMsgCnt() {
      return msgCnt_;
    }
    /**
     * <pre>
     *消息编号，循环使用
     * </pre>
     *
     * <code>uint32 msgCnt = 3;</code>
     */
    public Builder setMsgCnt(int value) {
      
      msgCnt_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *消息编号，循环使用
     * </pre>
     *
     * <code>uint32 msgCnt = 3;</code>
     */
    public Builder clearMsgCnt() {
      
      msgCnt_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.MAP)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.MAP)
  private static final road.data.proto.MAP DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.MAP();
  }

  public static road.data.proto.MAP getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MAP>
      PARSER = new com.google.protobuf.AbstractParser<MAP>() {
    @java.lang.Override
    public MAP parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new MAP(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<MAP> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MAP> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.MAP getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

