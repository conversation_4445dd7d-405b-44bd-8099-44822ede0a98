// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface MAPOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.MAP)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 1970到现在的总分钟数
   * </pre>
   *
   * <code>uint32 timestamp = 1;</code>
   */
  int getTimestamp();

  /**
   * <pre>
   * 交叉口集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
   */
  java.util.List<road.data.proto.Node> 
      getNodesList();
  /**
   * <pre>
   * 交叉口集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
   */
  road.data.proto.Node getNodes(int index);
  /**
   * <pre>
   * 交叉口集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
   */
  int getNodesCount();
  /**
   * <pre>
   * 交叉口集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
   */
  java.util.List<? extends road.data.proto.NodeOrBuilder> 
      getNodesOrBuilderList();
  /**
   * <pre>
   * 交叉口集合
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Node nodes = 2;</code>
   */
  road.data.proto.NodeOrBuilder getNodesOrBuilder(
      int index);

  /**
   * <pre>
   *消息编号，循环使用
   * </pre>
   *
   * <code>uint32 msgCnt = 3;</code>
   */
  int getMsgCnt();
}
