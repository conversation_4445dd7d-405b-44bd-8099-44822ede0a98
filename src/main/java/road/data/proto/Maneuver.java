// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *转向信息  
 * </pre>
 *
 * Protobuf enum {@code cn.seisys.v2x.pb.Maneuver}
 */
public enum Maneuver
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *直走
   * </pre>
   *
   * <code>MANEUVER_STRAIGHT = 0;</code>
   */
  MANEUVER_STRAIGHT(0),
  /**
   * <pre>
   *左转
   * </pre>
   *
   * <code>MANEUVER_LEFT_TURN = 1;</code>
   */
  MANEUVER_LEFT_TURN(1),
  /**
   * <pre>
   *右转
   * </pre>
   *
   * <code>MANEUVER_RIGHT_TURN = 2;</code>
   */
  MANEUVER_RIGHT_TURN(2),
  /**
   * <pre>
   *掉头
   * </pre>
   *
   * <code>MANEUVER_UTURN = 3;</code>
   */
  MANEUVER_UTURN(3),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *直走
   * </pre>
   *
   * <code>MANEUVER_STRAIGHT = 0;</code>
   */
  public static final int MANEUVER_STRAIGHT_VALUE = 0;
  /**
   * <pre>
   *左转
   * </pre>
   *
   * <code>MANEUVER_LEFT_TURN = 1;</code>
   */
  public static final int MANEUVER_LEFT_TURN_VALUE = 1;
  /**
   * <pre>
   *右转
   * </pre>
   *
   * <code>MANEUVER_RIGHT_TURN = 2;</code>
   */
  public static final int MANEUVER_RIGHT_TURN_VALUE = 2;
  /**
   * <pre>
   *掉头
   * </pre>
   *
   * <code>MANEUVER_UTURN = 3;</code>
   */
  public static final int MANEUVER_UTURN_VALUE = 3;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static Maneuver valueOf(int value) {
    return forNumber(value);
  }

  public static Maneuver forNumber(int value) {
    switch (value) {
      case 0: return MANEUVER_STRAIGHT;
      case 1: return MANEUVER_LEFT_TURN;
      case 2: return MANEUVER_RIGHT_TURN;
      case 3: return MANEUVER_UTURN;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<Maneuver>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      Maneuver> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<Maneuver>() {
          public Maneuver findValueByNumber(int number) {
            return Maneuver.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return road.data.proto.V2X.getDescriptor().getEnumTypes().get(7);
  }

  private static final Maneuver[] VALUES = values();

  public static Maneuver valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private Maneuver(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.Maneuver)
}

