// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *地图消息  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.MapData}
 */
public  final class MapData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.MapData)
    MapDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use MapData.newBuilder() to construct.
  private MapData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private MapData() {
    mapSlice_ = "";
    eTag_ = "";
    seqNum_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new MapData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private MapData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            mapSlice_ = s;
            break;
          }
          case 18: {
            road.data.proto.MAP.Builder subBuilder = null;
            if (map_ != null) {
              subBuilder = map_.toBuilder();
            }
            map_ = input.readMessage(road.data.proto.MAP.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(map_);
              map_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            eTag_ = s;
            break;
          }
          case 32: {

            ack_ = input.readBool();
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            seqNum_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MapData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MapData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.MapData.class, road.data.proto.MapData.Builder.class);
  }

  public static final int MAPSLICE_FIELD_NUMBER = 1;
  private volatile java.lang.Object mapSlice_;
  /**
   * <pre>
   * MAP 切片
   * </pre>
   *
   * <code>string mapSlice = 1;</code>
   */
  public java.lang.String getMapSlice() {
    java.lang.Object ref = mapSlice_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      mapSlice_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * MAP 切片
   * </pre>
   *
   * <code>string mapSlice = 1;</code>
   */
  public com.google.protobuf.ByteString
      getMapSliceBytes() {
    java.lang.Object ref = mapSlice_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      mapSlice_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MAP_FIELD_NUMBER = 2;
  private road.data.proto.MAP map_;
  /**
   * <pre>
   * Map 数据
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
   */
  public boolean hasMap() {
    return map_ != null;
  }
  /**
   * <pre>
   * Map 数据
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
   */
  public road.data.proto.MAP getMap() {
    return map_ == null ? road.data.proto.MAP.getDefaultInstance() : map_;
  }
  /**
   * <pre>
   * Map 数据
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
   */
  public road.data.proto.MAPOrBuilder getMapOrBuilder() {
    return getMap();
  }

  public static final int ETAG_FIELD_NUMBER = 3;
  private volatile java.lang.Object eTag_;
  /**
   * <pre>
   *	标识 MAP 版本
   * </pre>
   *
   * <code>string eTag = 3;</code>
   */
  public java.lang.String getETag() {
    java.lang.Object ref = eTag_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      eTag_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *	标识 MAP 版本
   * </pre>
   *
   * <code>string eTag = 3;</code>
   */
  public com.google.protobuf.ByteString
      getETagBytes() {
    java.lang.Object ref = eTag_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      eTag_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACK_FIELD_NUMBER = 4;
  private boolean ack_;
  /**
   * <pre>
   * 可选，是否需要返回确认消息，true 需要，不带或 false 不需要此处需填 TRUE，用于业务判断设备是否成功接收。
   * </pre>
   *
   * <code>bool ack = 4;</code>
   */
  public boolean getAck() {
    return ack_;
  }

  public static final int SEQNUM_FIELD_NUMBER = 5;
  private volatile java.lang.Object seqNum_;
  /**
   * <pre>
   * 可选，会话唯一标识，当需要确认时必填，用于匹配响应
   * </pre>
   *
   * <code>string seqNum = 5;</code>
   */
  public java.lang.String getSeqNum() {
    java.lang.Object ref = seqNum_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      seqNum_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 可选，会话唯一标识，当需要确认时必填，用于匹配响应
   * </pre>
   *
   * <code>string seqNum = 5;</code>
   */
  public com.google.protobuf.ByteString
      getSeqNumBytes() {
    java.lang.Object ref = seqNum_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      seqNum_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getMapSliceBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, mapSlice_);
    }
    if (map_ != null) {
      output.writeMessage(2, getMap());
    }
    if (!getETagBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, eTag_);
    }
    if (ack_ != false) {
      output.writeBool(4, ack_);
    }
    if (!getSeqNumBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, seqNum_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getMapSliceBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, mapSlice_);
    }
    if (map_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getMap());
    }
    if (!getETagBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, eTag_);
    }
    if (ack_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(4, ack_);
    }
    if (!getSeqNumBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, seqNum_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.MapData)) {
      return super.equals(obj);
    }
    road.data.proto.MapData other = (road.data.proto.MapData) obj;

    if (!getMapSlice()
        .equals(other.getMapSlice())) return false;
    if (hasMap() != other.hasMap()) return false;
    if (hasMap()) {
      if (!getMap()
          .equals(other.getMap())) return false;
    }
    if (!getETag()
        .equals(other.getETag())) return false;
    if (getAck()
        != other.getAck()) return false;
    if (!getSeqNum()
        .equals(other.getSeqNum())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + MAPSLICE_FIELD_NUMBER;
    hash = (53 * hash) + getMapSlice().hashCode();
    if (hasMap()) {
      hash = (37 * hash) + MAP_FIELD_NUMBER;
      hash = (53 * hash) + getMap().hashCode();
    }
    hash = (37 * hash) + ETAG_FIELD_NUMBER;
    hash = (53 * hash) + getETag().hashCode();
    hash = (37 * hash) + ACK_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getAck());
    hash = (37 * hash) + SEQNUM_FIELD_NUMBER;
    hash = (53 * hash) + getSeqNum().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.MapData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MapData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MapData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MapData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MapData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MapData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MapData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MapData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MapData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.MapData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MapData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MapData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.MapData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *地图消息  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.MapData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.MapData)
      road.data.proto.MapDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MapData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MapData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.MapData.class, road.data.proto.MapData.Builder.class);
    }

    // Construct using road.data.proto.MapData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      mapSlice_ = "";

      if (mapBuilder_ == null) {
        map_ = null;
      } else {
        map_ = null;
        mapBuilder_ = null;
      }
      eTag_ = "";

      ack_ = false;

      seqNum_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MapData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.MapData getDefaultInstanceForType() {
      return road.data.proto.MapData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.MapData build() {
      road.data.proto.MapData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.MapData buildPartial() {
      road.data.proto.MapData result = new road.data.proto.MapData(this);
      result.mapSlice_ = mapSlice_;
      if (mapBuilder_ == null) {
        result.map_ = map_;
      } else {
        result.map_ = mapBuilder_.build();
      }
      result.eTag_ = eTag_;
      result.ack_ = ack_;
      result.seqNum_ = seqNum_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.MapData) {
        return mergeFrom((road.data.proto.MapData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.MapData other) {
      if (other == road.data.proto.MapData.getDefaultInstance()) return this;
      if (!other.getMapSlice().isEmpty()) {
        mapSlice_ = other.mapSlice_;
        onChanged();
      }
      if (other.hasMap()) {
        mergeMap(other.getMap());
      }
      if (!other.getETag().isEmpty()) {
        eTag_ = other.eTag_;
        onChanged();
      }
      if (other.getAck() != false) {
        setAck(other.getAck());
      }
      if (!other.getSeqNum().isEmpty()) {
        seqNum_ = other.seqNum_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.MapData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.MapData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object mapSlice_ = "";
    /**
     * <pre>
     * MAP 切片
     * </pre>
     *
     * <code>string mapSlice = 1;</code>
     */
    public java.lang.String getMapSlice() {
      java.lang.Object ref = mapSlice_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        mapSlice_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * MAP 切片
     * </pre>
     *
     * <code>string mapSlice = 1;</code>
     */
    public com.google.protobuf.ByteString
        getMapSliceBytes() {
      java.lang.Object ref = mapSlice_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        mapSlice_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * MAP 切片
     * </pre>
     *
     * <code>string mapSlice = 1;</code>
     */
    public Builder setMapSlice(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      mapSlice_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * MAP 切片
     * </pre>
     *
     * <code>string mapSlice = 1;</code>
     */
    public Builder clearMapSlice() {
      
      mapSlice_ = getDefaultInstance().getMapSlice();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * MAP 切片
     * </pre>
     *
     * <code>string mapSlice = 1;</code>
     */
    public Builder setMapSliceBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      mapSlice_ = value;
      onChanged();
      return this;
    }

    private road.data.proto.MAP map_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MAP, road.data.proto.MAP.Builder, road.data.proto.MAPOrBuilder> mapBuilder_;
    /**
     * <pre>
     * Map 数据
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
     */
    public boolean hasMap() {
      return mapBuilder_ != null || map_ != null;
    }
    /**
     * <pre>
     * Map 数据
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
     */
    public road.data.proto.MAP getMap() {
      if (mapBuilder_ == null) {
        return map_ == null ? road.data.proto.MAP.getDefaultInstance() : map_;
      } else {
        return mapBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * Map 数据
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
     */
    public Builder setMap(road.data.proto.MAP value) {
      if (mapBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        map_ = value;
        onChanged();
      } else {
        mapBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * Map 数据
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
     */
    public Builder setMap(
        road.data.proto.MAP.Builder builderForValue) {
      if (mapBuilder_ == null) {
        map_ = builderForValue.build();
        onChanged();
      } else {
        mapBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * Map 数据
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
     */
    public Builder mergeMap(road.data.proto.MAP value) {
      if (mapBuilder_ == null) {
        if (map_ != null) {
          map_ =
            road.data.proto.MAP.newBuilder(map_).mergeFrom(value).buildPartial();
        } else {
          map_ = value;
        }
        onChanged();
      } else {
        mapBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * Map 数据
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
     */
    public Builder clearMap() {
      if (mapBuilder_ == null) {
        map_ = null;
        onChanged();
      } else {
        map_ = null;
        mapBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * Map 数据
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
     */
    public road.data.proto.MAP.Builder getMapBuilder() {
      
      onChanged();
      return getMapFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * Map 数据
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
     */
    public road.data.proto.MAPOrBuilder getMapOrBuilder() {
      if (mapBuilder_ != null) {
        return mapBuilder_.getMessageOrBuilder();
      } else {
        return map_ == null ?
            road.data.proto.MAP.getDefaultInstance() : map_;
      }
    }
    /**
     * <pre>
     * Map 数据
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MAP, road.data.proto.MAP.Builder, road.data.proto.MAPOrBuilder> 
        getMapFieldBuilder() {
      if (mapBuilder_ == null) {
        mapBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.MAP, road.data.proto.MAP.Builder, road.data.proto.MAPOrBuilder>(
                getMap(),
                getParentForChildren(),
                isClean());
        map_ = null;
      }
      return mapBuilder_;
    }

    private java.lang.Object eTag_ = "";
    /**
     * <pre>
     *	标识 MAP 版本
     * </pre>
     *
     * <code>string eTag = 3;</code>
     */
    public java.lang.String getETag() {
      java.lang.Object ref = eTag_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        eTag_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *	标识 MAP 版本
     * </pre>
     *
     * <code>string eTag = 3;</code>
     */
    public com.google.protobuf.ByteString
        getETagBytes() {
      java.lang.Object ref = eTag_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        eTag_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *	标识 MAP 版本
     * </pre>
     *
     * <code>string eTag = 3;</code>
     */
    public Builder setETag(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      eTag_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *	标识 MAP 版本
     * </pre>
     *
     * <code>string eTag = 3;</code>
     */
    public Builder clearETag() {
      
      eTag_ = getDefaultInstance().getETag();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *	标识 MAP 版本
     * </pre>
     *
     * <code>string eTag = 3;</code>
     */
    public Builder setETagBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      eTag_ = value;
      onChanged();
      return this;
    }

    private boolean ack_ ;
    /**
     * <pre>
     * 可选，是否需要返回确认消息，true 需要，不带或 false 不需要此处需填 TRUE，用于业务判断设备是否成功接收。
     * </pre>
     *
     * <code>bool ack = 4;</code>
     */
    public boolean getAck() {
      return ack_;
    }
    /**
     * <pre>
     * 可选，是否需要返回确认消息，true 需要，不带或 false 不需要此处需填 TRUE，用于业务判断设备是否成功接收。
     * </pre>
     *
     * <code>bool ack = 4;</code>
     */
    public Builder setAck(boolean value) {
      
      ack_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，是否需要返回确认消息，true 需要，不带或 false 不需要此处需填 TRUE，用于业务判断设备是否成功接收。
     * </pre>
     *
     * <code>bool ack = 4;</code>
     */
    public Builder clearAck() {
      
      ack_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object seqNum_ = "";
    /**
     * <pre>
     * 可选，会话唯一标识，当需要确认时必填，用于匹配响应
     * </pre>
     *
     * <code>string seqNum = 5;</code>
     */
    public java.lang.String getSeqNum() {
      java.lang.Object ref = seqNum_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        seqNum_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 可选，会话唯一标识，当需要确认时必填，用于匹配响应
     * </pre>
     *
     * <code>string seqNum = 5;</code>
     */
    public com.google.protobuf.ByteString
        getSeqNumBytes() {
      java.lang.Object ref = seqNum_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        seqNum_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 可选，会话唯一标识，当需要确认时必填，用于匹配响应
     * </pre>
     *
     * <code>string seqNum = 5;</code>
     */
    public Builder setSeqNum(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      seqNum_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，会话唯一标识，当需要确认时必填，用于匹配响应
     * </pre>
     *
     * <code>string seqNum = 5;</code>
     */
    public Builder clearSeqNum() {
      
      seqNum_ = getDefaultInstance().getSeqNum();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，会话唯一标识，当需要确认时必填，用于匹配响应
     * </pre>
     *
     * <code>string seqNum = 5;</code>
     */
    public Builder setSeqNumBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      seqNum_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.MapData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.MapData)
  private static final road.data.proto.MapData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.MapData();
  }

  public static road.data.proto.MapData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MapData>
      PARSER = new com.google.protobuf.AbstractParser<MapData>() {
    @java.lang.Override
    public MapData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new MapData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<MapData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MapData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.MapData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

