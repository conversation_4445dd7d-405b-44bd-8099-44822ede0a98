// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface MapDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.MapData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * MAP 切片
   * </pre>
   *
   * <code>string mapSlice = 1;</code>
   */
  java.lang.String getMapSlice();
  /**
   * <pre>
   * MAP 切片
   * </pre>
   *
   * <code>string mapSlice = 1;</code>
   */
  com.google.protobuf.ByteString
      getMapSliceBytes();

  /**
   * <pre>
   * Map 数据
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
   */
  boolean hasMap();
  /**
   * <pre>
   * Map 数据
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
   */
  road.data.proto.MAP getMap();
  /**
   * <pre>
   * Map 数据
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MAP map = 2;</code>
   */
  road.data.proto.MAPOrBuilder getMapOrBuilder();

  /**
   * <pre>
   *	标识 MAP 版本
   * </pre>
   *
   * <code>string eTag = 3;</code>
   */
  java.lang.String getETag();
  /**
   * <pre>
   *	标识 MAP 版本
   * </pre>
   *
   * <code>string eTag = 3;</code>
   */
  com.google.protobuf.ByteString
      getETagBytes();

  /**
   * <pre>
   * 可选，是否需要返回确认消息，true 需要，不带或 false 不需要此处需填 TRUE，用于业务判断设备是否成功接收。
   * </pre>
   *
   * <code>bool ack = 4;</code>
   */
  boolean getAck();

  /**
   * <pre>
   * 可选，会话唯一标识，当需要确认时必填，用于匹配响应
   * </pre>
   *
   * <code>string seqNum = 5;</code>
   */
  java.lang.String getSeqNum();
  /**
   * <pre>
   * 可选，会话唯一标识，当需要确认时必填，用于匹配响应
   * </pre>
   *
   * <code>string seqNum = 5;</code>
   */
  com.google.protobuf.ByteString
      getSeqNumBytes();
}
