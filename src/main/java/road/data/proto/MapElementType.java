// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *路网元素类型
 * </pre>
 *
 * Protobuf enum {@code cn.seisys.v2x.pb.MapElementType}
 */
public enum MapElementType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *未知
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_UNKNOWN = 0;</code>
   */
  MAP_ELEMENT_TYPE_UNKNOWN(0),
  /**
   * <pre>
   *检测区对象
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_DETECTOR_AREA = 1;</code>
   */
  MAP_ELEMENT_TYPE_DETECTOR_AREA(1),
  /**
   * <pre>
   *车道对象
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_LANE = 2;</code>
   */
  MAP_ELEMENT_TYPE_LANE(2),
  /**
   * <pre>
   *路段分段对象
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_SECTION = 3;</code>
   */
  MAP_ELEMENT_TYPE_SECTION(3),
  /**
   * <pre>
   *有向路段对象
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_LINK = 4;</code>
   */
  MAP_ELEMENT_TYPE_LINK(4),
  /**
   * <pre>
   *路口对象
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_NODE = 5;</code>
   */
  MAP_ELEMENT_TYPE_NODE(5),
  /**
   * <pre>
   *一条路段与下游路段的连接关系
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_MOVEMENT = 6;</code>
   */
  MAP_ELEMENT_TYPE_MOVEMENT(6),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *未知
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_UNKNOWN = 0;</code>
   */
  public static final int MAP_ELEMENT_TYPE_UNKNOWN_VALUE = 0;
  /**
   * <pre>
   *检测区对象
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_DETECTOR_AREA = 1;</code>
   */
  public static final int MAP_ELEMENT_TYPE_DETECTOR_AREA_VALUE = 1;
  /**
   * <pre>
   *车道对象
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_LANE = 2;</code>
   */
  public static final int MAP_ELEMENT_TYPE_LANE_VALUE = 2;
  /**
   * <pre>
   *路段分段对象
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_SECTION = 3;</code>
   */
  public static final int MAP_ELEMENT_TYPE_SECTION_VALUE = 3;
  /**
   * <pre>
   *有向路段对象
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_LINK = 4;</code>
   */
  public static final int MAP_ELEMENT_TYPE_LINK_VALUE = 4;
  /**
   * <pre>
   *路口对象
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_NODE = 5;</code>
   */
  public static final int MAP_ELEMENT_TYPE_NODE_VALUE = 5;
  /**
   * <pre>
   *一条路段与下游路段的连接关系
   * </pre>
   *
   * <code>MAP_ELEMENT_TYPE_MOVEMENT = 6;</code>
   */
  public static final int MAP_ELEMENT_TYPE_MOVEMENT_VALUE = 6;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static MapElementType valueOf(int value) {
    return forNumber(value);
  }

  public static MapElementType forNumber(int value) {
    switch (value) {
      case 0: return MAP_ELEMENT_TYPE_UNKNOWN;
      case 1: return MAP_ELEMENT_TYPE_DETECTOR_AREA;
      case 2: return MAP_ELEMENT_TYPE_LANE;
      case 3: return MAP_ELEMENT_TYPE_SECTION;
      case 4: return MAP_ELEMENT_TYPE_LINK;
      case 5: return MAP_ELEMENT_TYPE_NODE;
      case 6: return MAP_ELEMENT_TYPE_MOVEMENT;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<MapElementType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      MapElementType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<MapElementType>() {
          public MapElementType findValueByNumber(int number) {
            return MapElementType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return road.data.proto.V2X.getDescriptor().getEnumTypes().get(8);
  }

  private static final MapElementType[] VALUES = values();

  public static MapElementType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private MapElementType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.MapElementType)
}

