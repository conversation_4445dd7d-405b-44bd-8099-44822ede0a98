// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *地图所在位置  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.MapLocation}
 */
public  final class MapLocation extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.MapLocation)
    MapLocationOrBuilder {
private static final long serialVersionUID = 0L;
  // Use MapLocation.newBuilder() to construct.
  private MapLocation(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private MapLocation() {
    linkName_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new MapLocation();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private MapLocation(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (nodeId_ != null) {
              subBuilder = nodeId_.toBuilder();
            }
            nodeId_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(nodeId_);
              nodeId_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            linkName_ = s;
            break;
          }
          case 26: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (upstreamNodeId_ != null) {
              subBuilder = upstreamNodeId_.toBuilder();
            }
            upstreamNodeId_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(upstreamNodeId_);
              upstreamNodeId_ = subBuilder.buildPartial();
            }

            break;
          }
          case 32: {

            sectionId_ = input.readUInt32();
            break;
          }
          case 40: {

            laneId_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MapLocation_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MapLocation_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.MapLocation.class, road.data.proto.MapLocation.Builder.class);
  }

  public static final int NODEID_FIELD_NUMBER = 1;
  private road.data.proto.NodeReferenceId nodeId_;
  /**
   * <pre>
   *可选，所在交叉路口id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  public boolean hasNodeId() {
    return nodeId_ != null;
  }
  /**
   * <pre>
   *可选，所在交叉路口id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  public road.data.proto.NodeReferenceId getNodeId() {
    return nodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
  }
  /**
   * <pre>
   *可选，所在交叉路口id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder() {
    return getNodeId();
  }

  public static final int LINKNAME_FIELD_NUMBER = 2;
  private volatile java.lang.Object linkName_;
  /**
   * <pre>
   *可选，所在路段，由字符串表达的路段名称或者描述
   * </pre>
   *
   * <code>string linkName = 2;</code>
   */
  public java.lang.String getLinkName() {
    java.lang.Object ref = linkName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      linkName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，所在路段，由字符串表达的路段名称或者描述
   * </pre>
   *
   * <code>string linkName = 2;</code>
   */
  public com.google.protobuf.ByteString
      getLinkNameBytes() {
    java.lang.Object ref = linkName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      linkName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int UPSTREAMNODEID_FIELD_NUMBER = 3;
  private road.data.proto.NodeReferenceId upstreamNodeId_;
  /**
   * <pre>
   *可选，所在路段的上游节点id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
   */
  public boolean hasUpstreamNodeId() {
    return upstreamNodeId_ != null;
  }
  /**
   * <pre>
   *可选，所在路段的上游节点id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
   */
  public road.data.proto.NodeReferenceId getUpstreamNodeId() {
    return upstreamNodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
  }
  /**
   * <pre>
   *可选，所在路段的上游节点id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder() {
    return getUpstreamNodeId();
  }

  public static final int SECTIONID_FIELD_NUMBER = 4;
  private int sectionId_;
  /**
   * <pre>
   *可选，所在的分段路段
   * </pre>
   *
   * <code>uint32 sectionId = 4;</code>
   */
  public int getSectionId() {
    return sectionId_;
  }

  public static final int LANEID_FIELD_NUMBER = 5;
  private int laneId_;
  /**
   * <pre>
   *可选，LaneId所在的车道
   * </pre>
   *
   * <code>uint32 laneId = 5;</code>
   */
  public int getLaneId() {
    return laneId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (nodeId_ != null) {
      output.writeMessage(1, getNodeId());
    }
    if (!getLinkNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, linkName_);
    }
    if (upstreamNodeId_ != null) {
      output.writeMessage(3, getUpstreamNodeId());
    }
    if (sectionId_ != 0) {
      output.writeUInt32(4, sectionId_);
    }
    if (laneId_ != 0) {
      output.writeUInt32(5, laneId_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (nodeId_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getNodeId());
    }
    if (!getLinkNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, linkName_);
    }
    if (upstreamNodeId_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getUpstreamNodeId());
    }
    if (sectionId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, sectionId_);
    }
    if (laneId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(5, laneId_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.MapLocation)) {
      return super.equals(obj);
    }
    road.data.proto.MapLocation other = (road.data.proto.MapLocation) obj;

    if (hasNodeId() != other.hasNodeId()) return false;
    if (hasNodeId()) {
      if (!getNodeId()
          .equals(other.getNodeId())) return false;
    }
    if (!getLinkName()
        .equals(other.getLinkName())) return false;
    if (hasUpstreamNodeId() != other.hasUpstreamNodeId()) return false;
    if (hasUpstreamNodeId()) {
      if (!getUpstreamNodeId()
          .equals(other.getUpstreamNodeId())) return false;
    }
    if (getSectionId()
        != other.getSectionId()) return false;
    if (getLaneId()
        != other.getLaneId()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasNodeId()) {
      hash = (37 * hash) + NODEID_FIELD_NUMBER;
      hash = (53 * hash) + getNodeId().hashCode();
    }
    hash = (37 * hash) + LINKNAME_FIELD_NUMBER;
    hash = (53 * hash) + getLinkName().hashCode();
    if (hasUpstreamNodeId()) {
      hash = (37 * hash) + UPSTREAMNODEID_FIELD_NUMBER;
      hash = (53 * hash) + getUpstreamNodeId().hashCode();
    }
    hash = (37 * hash) + SECTIONID_FIELD_NUMBER;
    hash = (53 * hash) + getSectionId();
    hash = (37 * hash) + LANEID_FIELD_NUMBER;
    hash = (53 * hash) + getLaneId();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.MapLocation parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MapLocation parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MapLocation parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MapLocation parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MapLocation parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MapLocation parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MapLocation parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MapLocation parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MapLocation parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.MapLocation parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MapLocation parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MapLocation parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.MapLocation prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *地图所在位置  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.MapLocation}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.MapLocation)
      road.data.proto.MapLocationOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MapLocation_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MapLocation_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.MapLocation.class, road.data.proto.MapLocation.Builder.class);
    }

    // Construct using road.data.proto.MapLocation.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (nodeIdBuilder_ == null) {
        nodeId_ = null;
      } else {
        nodeId_ = null;
        nodeIdBuilder_ = null;
      }
      linkName_ = "";

      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = null;
      } else {
        upstreamNodeId_ = null;
        upstreamNodeIdBuilder_ = null;
      }
      sectionId_ = 0;

      laneId_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MapLocation_descriptor;
    }

    @java.lang.Override
    public road.data.proto.MapLocation getDefaultInstanceForType() {
      return road.data.proto.MapLocation.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.MapLocation build() {
      road.data.proto.MapLocation result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.MapLocation buildPartial() {
      road.data.proto.MapLocation result = new road.data.proto.MapLocation(this);
      if (nodeIdBuilder_ == null) {
        result.nodeId_ = nodeId_;
      } else {
        result.nodeId_ = nodeIdBuilder_.build();
      }
      result.linkName_ = linkName_;
      if (upstreamNodeIdBuilder_ == null) {
        result.upstreamNodeId_ = upstreamNodeId_;
      } else {
        result.upstreamNodeId_ = upstreamNodeIdBuilder_.build();
      }
      result.sectionId_ = sectionId_;
      result.laneId_ = laneId_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.MapLocation) {
        return mergeFrom((road.data.proto.MapLocation)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.MapLocation other) {
      if (other == road.data.proto.MapLocation.getDefaultInstance()) return this;
      if (other.hasNodeId()) {
        mergeNodeId(other.getNodeId());
      }
      if (!other.getLinkName().isEmpty()) {
        linkName_ = other.linkName_;
        onChanged();
      }
      if (other.hasUpstreamNodeId()) {
        mergeUpstreamNodeId(other.getUpstreamNodeId());
      }
      if (other.getSectionId() != 0) {
        setSectionId(other.getSectionId());
      }
      if (other.getLaneId() != 0) {
        setLaneId(other.getLaneId());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.MapLocation parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.MapLocation) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.NodeReferenceId nodeId_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> nodeIdBuilder_;
    /**
     * <pre>
     *可选，所在交叉路口id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public boolean hasNodeId() {
      return nodeIdBuilder_ != null || nodeId_ != null;
    }
    /**
     * <pre>
     *可选，所在交叉路口id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceId getNodeId() {
      if (nodeIdBuilder_ == null) {
        return nodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
      } else {
        return nodeIdBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，所在交叉路口id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder setNodeId(road.data.proto.NodeReferenceId value) {
      if (nodeIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        nodeId_ = value;
        onChanged();
      } else {
        nodeIdBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在交叉路口id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder setNodeId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (nodeIdBuilder_ == null) {
        nodeId_ = builderForValue.build();
        onChanged();
      } else {
        nodeIdBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在交叉路口id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder mergeNodeId(road.data.proto.NodeReferenceId value) {
      if (nodeIdBuilder_ == null) {
        if (nodeId_ != null) {
          nodeId_ =
            road.data.proto.NodeReferenceId.newBuilder(nodeId_).mergeFrom(value).buildPartial();
        } else {
          nodeId_ = value;
        }
        onChanged();
      } else {
        nodeIdBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在交叉路口id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder clearNodeId() {
      if (nodeIdBuilder_ == null) {
        nodeId_ = null;
        onChanged();
      } else {
        nodeId_ = null;
        nodeIdBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在交叉路口id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getNodeIdBuilder() {
      
      onChanged();
      return getNodeIdFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，所在交叉路口id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder() {
      if (nodeIdBuilder_ != null) {
        return nodeIdBuilder_.getMessageOrBuilder();
      } else {
        return nodeId_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
      }
    }
    /**
     * <pre>
     *可选，所在交叉路口id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getNodeIdFieldBuilder() {
      if (nodeIdBuilder_ == null) {
        nodeIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getNodeId(),
                getParentForChildren(),
                isClean());
        nodeId_ = null;
      }
      return nodeIdBuilder_;
    }

    private java.lang.Object linkName_ = "";
    /**
     * <pre>
     *可选，所在路段，由字符串表达的路段名称或者描述
     * </pre>
     *
     * <code>string linkName = 2;</code>
     */
    public java.lang.String getLinkName() {
      java.lang.Object ref = linkName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        linkName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，所在路段，由字符串表达的路段名称或者描述
     * </pre>
     *
     * <code>string linkName = 2;</code>
     */
    public com.google.protobuf.ByteString
        getLinkNameBytes() {
      java.lang.Object ref = linkName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        linkName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，所在路段，由字符串表达的路段名称或者描述
     * </pre>
     *
     * <code>string linkName = 2;</code>
     */
    public Builder setLinkName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      linkName_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，所在路段，由字符串表达的路段名称或者描述
     * </pre>
     *
     * <code>string linkName = 2;</code>
     */
    public Builder clearLinkName() {
      
      linkName_ = getDefaultInstance().getLinkName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，所在路段，由字符串表达的路段名称或者描述
     * </pre>
     *
     * <code>string linkName = 2;</code>
     */
    public Builder setLinkNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      linkName_ = value;
      onChanged();
      return this;
    }

    private road.data.proto.NodeReferenceId upstreamNodeId_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> upstreamNodeIdBuilder_;
    /**
     * <pre>
     *可选，所在路段的上游节点id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
     */
    public boolean hasUpstreamNodeId() {
      return upstreamNodeIdBuilder_ != null || upstreamNodeId_ != null;
    }
    /**
     * <pre>
     *可选，所在路段的上游节点id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
     */
    public road.data.proto.NodeReferenceId getUpstreamNodeId() {
      if (upstreamNodeIdBuilder_ == null) {
        return upstreamNodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
      } else {
        return upstreamNodeIdBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，所在路段的上游节点id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
     */
    public Builder setUpstreamNodeId(road.data.proto.NodeReferenceId value) {
      if (upstreamNodeIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        upstreamNodeId_ = value;
        onChanged();
      } else {
        upstreamNodeIdBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在路段的上游节点id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
     */
    public Builder setUpstreamNodeId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = builderForValue.build();
        onChanged();
      } else {
        upstreamNodeIdBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在路段的上游节点id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
     */
    public Builder mergeUpstreamNodeId(road.data.proto.NodeReferenceId value) {
      if (upstreamNodeIdBuilder_ == null) {
        if (upstreamNodeId_ != null) {
          upstreamNodeId_ =
            road.data.proto.NodeReferenceId.newBuilder(upstreamNodeId_).mergeFrom(value).buildPartial();
        } else {
          upstreamNodeId_ = value;
        }
        onChanged();
      } else {
        upstreamNodeIdBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在路段的上游节点id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
     */
    public Builder clearUpstreamNodeId() {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = null;
        onChanged();
      } else {
        upstreamNodeId_ = null;
        upstreamNodeIdBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在路段的上游节点id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getUpstreamNodeIdBuilder() {
      
      onChanged();
      return getUpstreamNodeIdFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，所在路段的上游节点id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder() {
      if (upstreamNodeIdBuilder_ != null) {
        return upstreamNodeIdBuilder_.getMessageOrBuilder();
      } else {
        return upstreamNodeId_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
      }
    }
    /**
     * <pre>
     *可选，所在路段的上游节点id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getUpstreamNodeIdFieldBuilder() {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getUpstreamNodeId(),
                getParentForChildren(),
                isClean());
        upstreamNodeId_ = null;
      }
      return upstreamNodeIdBuilder_;
    }

    private int sectionId_ ;
    /**
     * <pre>
     *可选，所在的分段路段
     * </pre>
     *
     * <code>uint32 sectionId = 4;</code>
     */
    public int getSectionId() {
      return sectionId_;
    }
    /**
     * <pre>
     *可选，所在的分段路段
     * </pre>
     *
     * <code>uint32 sectionId = 4;</code>
     */
    public Builder setSectionId(int value) {
      
      sectionId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，所在的分段路段
     * </pre>
     *
     * <code>uint32 sectionId = 4;</code>
     */
    public Builder clearSectionId() {
      
      sectionId_ = 0;
      onChanged();
      return this;
    }

    private int laneId_ ;
    /**
     * <pre>
     *可选，LaneId所在的车道
     * </pre>
     *
     * <code>uint32 laneId = 5;</code>
     */
    public int getLaneId() {
      return laneId_;
    }
    /**
     * <pre>
     *可选，LaneId所在的车道
     * </pre>
     *
     * <code>uint32 laneId = 5;</code>
     */
    public Builder setLaneId(int value) {
      
      laneId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，LaneId所在的车道
     * </pre>
     *
     * <code>uint32 laneId = 5;</code>
     */
    public Builder clearLaneId() {
      
      laneId_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.MapLocation)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.MapLocation)
  private static final road.data.proto.MapLocation DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.MapLocation();
  }

  public static road.data.proto.MapLocation getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MapLocation>
      PARSER = new com.google.protobuf.AbstractParser<MapLocation>() {
    @java.lang.Override
    public MapLocation parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new MapLocation(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<MapLocation> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MapLocation> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.MapLocation getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

