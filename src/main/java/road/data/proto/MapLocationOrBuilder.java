// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface MapLocationOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.MapLocation)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *可选，所在交叉路口id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  boolean hasNodeId();
  /**
   * <pre>
   *可选，所在交叉路口id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  road.data.proto.NodeReferenceId getNodeId();
  /**
   * <pre>
   *可选，所在交叉路口id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder();

  /**
   * <pre>
   *可选，所在路段，由字符串表达的路段名称或者描述
   * </pre>
   *
   * <code>string linkName = 2;</code>
   */
  java.lang.String getLinkName();
  /**
   * <pre>
   *可选，所在路段，由字符串表达的路段名称或者描述
   * </pre>
   *
   * <code>string linkName = 2;</code>
   */
  com.google.protobuf.ByteString
      getLinkNameBytes();

  /**
   * <pre>
   *可选，所在路段的上游节点id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
   */
  boolean hasUpstreamNodeId();
  /**
   * <pre>
   *可选，所在路段的上游节点id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
   */
  road.data.proto.NodeReferenceId getUpstreamNodeId();
  /**
   * <pre>
   *可选，所在路段的上游节点id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 3;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder();

  /**
   * <pre>
   *可选，所在的分段路段
   * </pre>
   *
   * <code>uint32 sectionId = 4;</code>
   */
  int getSectionId();

  /**
   * <pre>
   *可选，LaneId所在的车道
   * </pre>
   *
   * <code>uint32 laneId = 5;</code>
   */
  int getLaneId();
}
