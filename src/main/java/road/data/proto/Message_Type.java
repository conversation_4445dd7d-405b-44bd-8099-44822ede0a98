// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * Protobuf enum {@code cn.seisys.v2x.pb.Message_Type}
 */
public enum Message_Type
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>UKNOWN_MSG = 0;</code>
   */
  UKNOWN_MSG(0),
  /**
   * <code>OBJECT_MSG = 1;</code>
   */
  OBJECT_MSG(1),
  /**
   * <code>EVENT_MSG = 2;</code>
   */
  EVENT_MSG(2),
  /**
   * <code>OBSTACLE_MSG = 3;</code>
   */
  OBSTACLE_MSG(3),
  /**
   * <code>STATUS_MSG = 4;</code>
   */
  STATUS_MSG(4),
  /**
   * <code>RTE_MSG = 5;</code>
   */
  RTE_MSG(5),
  /**
   * <code>RTS_MSG = 6;</code>
   */
  RTS_MSG(6),
  /**
   * <code>SPAT_MSG = 7;</code>
   */
  SPAT_MSG(7),
  /**
   * <code>MAP_MSG = 8;</code>
   */
  MAP_MSG(8),
  /**
   * <code>VIR_MSG = 9;</code>
   */
  VIR_MSG(9),
  /**
   * <code>RSC_MSG = 10;</code>
   */
  RSC_MSG(10),
  /**
   * <code>CAM_MSG = 11;</code>
   */
  CAM_MSG(11),
  /**
   * <code>DENM_MSG = 12;</code>
   */
  DENM_MSG(12),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>UKNOWN_MSG = 0;</code>
   */
  public static final int UKNOWN_MSG_VALUE = 0;
  /**
   * <code>OBJECT_MSG = 1;</code>
   */
  public static final int OBJECT_MSG_VALUE = 1;
  /**
   * <code>EVENT_MSG = 2;</code>
   */
  public static final int EVENT_MSG_VALUE = 2;
  /**
   * <code>OBSTACLE_MSG = 3;</code>
   */
  public static final int OBSTACLE_MSG_VALUE = 3;
  /**
   * <code>STATUS_MSG = 4;</code>
   */
  public static final int STATUS_MSG_VALUE = 4;
  /**
   * <code>RTE_MSG = 5;</code>
   */
  public static final int RTE_MSG_VALUE = 5;
  /**
   * <code>RTS_MSG = 6;</code>
   */
  public static final int RTS_MSG_VALUE = 6;
  /**
   * <code>SPAT_MSG = 7;</code>
   */
  public static final int SPAT_MSG_VALUE = 7;
  /**
   * <code>MAP_MSG = 8;</code>
   */
  public static final int MAP_MSG_VALUE = 8;
  /**
   * <code>VIR_MSG = 9;</code>
   */
  public static final int VIR_MSG_VALUE = 9;
  /**
   * <code>RSC_MSG = 10;</code>
   */
  public static final int RSC_MSG_VALUE = 10;
  /**
   * <code>CAM_MSG = 11;</code>
   */
  public static final int CAM_MSG_VALUE = 11;
  /**
   * <code>DENM_MSG = 12;</code>
   */
  public static final int DENM_MSG_VALUE = 12;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static Message_Type valueOf(int value) {
    return forNumber(value);
  }

  public static Message_Type forNumber(int value) {
    switch (value) {
      case 0: return UKNOWN_MSG;
      case 1: return OBJECT_MSG;
      case 2: return EVENT_MSG;
      case 3: return OBSTACLE_MSG;
      case 4: return STATUS_MSG;
      case 5: return RTE_MSG;
      case 6: return RTS_MSG;
      case 7: return SPAT_MSG;
      case 8: return MAP_MSG;
      case 9: return VIR_MSG;
      case 10: return RSC_MSG;
      case 11: return CAM_MSG;
      case 12: return DENM_MSG;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<Message_Type>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      Message_Type> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<Message_Type>() {
          public Message_Type findValueByNumber(int number) {
            return Message_Type.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return road.data.proto.V2X.getDescriptor().getEnumTypes().get(0);
  }

  private static final Message_Type[] VALUES = values();

  public static Message_Type valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private Message_Type(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.Message_Type)
}

