// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *监控统计消息 MonitorStatsData
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.MonitorStatsData}
 */
public  final class MonitorStatsData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.MonitorStatsData)
    MonitorStatsDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use MonitorStatsData.newBuilder() to construct.
  private MonitorStatsData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private MonitorStatsData() {
    deviceId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new MonitorStatsData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private MonitorStatsData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            deviceId_ = s;
            break;
          }
          case 24: {

            camNums_ = input.readUInt64();
            break;
          }
          case 32: {

            participantNums_ = input.readUInt64();
            break;
          }
          case 40: {

            rteNums_ = input.readUInt64();
            break;
          }
          case 48: {

            trafficflowNums_ = input.readUInt64();
            break;
          }
          case 56: {

            trafficflowStatNums_ = input.readUInt64();
            break;
          }
          case 64: {

            intersectionStatNums_ = input.readUInt64();
            break;
          }
          case 72: {

            phaseStatNums_ = input.readUInt64();
            break;
          }
          case 80: {

            rtsNums_ = input.readUInt64();
            break;
          }
          case 88: {

            cameraPathListNums_ = input.readUInt64();
            break;
          }
          case 96: {

            cameraPathNums_ = input.readUInt64();
            break;
          }
          case 104: {

            radarPathListNums_ = input.readUInt64();
            break;
          }
          case 112: {

            radarPathNums_ = input.readUInt64();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MonitorStatsData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MonitorStatsData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.MonitorStatsData.class, road.data.proto.MonitorStatsData.Builder.class);
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 1;
  private long timestamp_;
  /**
   * <pre>
   *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 1;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int DEVICEID_FIELD_NUMBER = 2;
  private volatile java.lang.Object deviceId_;
  /**
   * <pre>
   *mec设备的deviceId编号
   * </pre>
   *
   * <code>string deviceId = 2;</code>
   */
  public java.lang.String getDeviceId() {
    java.lang.Object ref = deviceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deviceId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *mec设备的deviceId编号
   * </pre>
   *
   * <code>string deviceId = 2;</code>
   */
  public com.google.protobuf.ByteString
      getDeviceIdBytes() {
    java.lang.Object ref = deviceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deviceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CAMNUMS_FIELD_NUMBER = 3;
  private long camNums_;
  /**
   * <pre>
   *当前时间整点到此刻时间间隔内累积传输cam消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 camNums = 3;</code>
   */
  public long getCamNums() {
    return camNums_;
  }

  public static final int PARTICIPANTNUMS_FIELD_NUMBER = 4;
  private long participantNums_;
  /**
   * <pre>
   *当前时间整点到此刻时间间隔内累积传输交通参与者消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 participantNums = 4;</code>
   */
  public long getParticipantNums() {
    return participantNums_;
  }

  public static final int RTENUMS_FIELD_NUMBER = 5;
  private long rteNums_;
  /**
   * <pre>
   *当前时间整点到此刻时间间隔内累积传输rte消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 rteNums = 5;</code>
   */
  public long getRteNums() {
    return rteNums_;
  }

  public static final int TRAFFICFLOWNUMS_FIELD_NUMBER = 6;
  private long trafficflowNums_;
  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输trafficflow消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 trafficflowNums = 6;</code>
   */
  public long getTrafficflowNums() {
    return trafficflowNums_;
  }

  public static final int TRAFFICFLOWSTATNUMS_FIELD_NUMBER = 7;
  private long trafficflowStatNums_;
  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输trafficflowStat消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 trafficflowStatNums = 7;</code>
   */
  public long getTrafficflowStatNums() {
    return trafficflowStatNums_;
  }

  public static final int INTERSECTIONSTATNUMS_FIELD_NUMBER = 8;
  private long intersectionStatNums_;
  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输intersectionStat消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 intersectionStatNums = 8;</code>
   */
  public long getIntersectionStatNums() {
    return intersectionStatNums_;
  }

  public static final int PHASESTATNUMS_FIELD_NUMBER = 9;
  private long phaseStatNums_;
  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输phaseStat消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 phaseStatNums = 9;</code>
   */
  public long getPhaseStatNums() {
    return phaseStatNums_;
  }

  public static final int RTSNUMS_FIELD_NUMBER = 10;
  private long rtsNums_;
  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输rts消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 rtsNums = 10;</code>
   */
  public long getRtsNums() {
    return rtsNums_;
  }

  public static final int CAMERAPATHLISTNUMS_FIELD_NUMBER = 11;
  private long cameraPathListNums_;
  /**
   * <pre>
   *当前时间整点到此刻时间间隔内累积传输cameraPathList消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 cameraPathListNums = 11;</code>
   */
  public long getCameraPathListNums() {
    return cameraPathListNums_;
  }

  public static final int CAMERAPATHNUMS_FIELD_NUMBER = 12;
  private long cameraPathNums_;
  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输cameraPath消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 cameraPathNums = 12;</code>
   */
  public long getCameraPathNums() {
    return cameraPathNums_;
  }

  public static final int RADARPATHLISTNUMS_FIELD_NUMBER = 13;
  private long radarPathListNums_;
  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输radarPathList消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 radarPathListNums = 13;</code>
   */
  public long getRadarPathListNums() {
    return radarPathListNums_;
  }

  public static final int RADARPATHNUMS_FIELD_NUMBER = 14;
  private long radarPathNums_;
  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输radarPath消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 radarPathNums = 14;</code>
   */
  public long getRadarPathNums() {
    return radarPathNums_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (timestamp_ != 0L) {
      output.writeUInt64(1, timestamp_);
    }
    if (!getDeviceIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, deviceId_);
    }
    if (camNums_ != 0L) {
      output.writeUInt64(3, camNums_);
    }
    if (participantNums_ != 0L) {
      output.writeUInt64(4, participantNums_);
    }
    if (rteNums_ != 0L) {
      output.writeUInt64(5, rteNums_);
    }
    if (trafficflowNums_ != 0L) {
      output.writeUInt64(6, trafficflowNums_);
    }
    if (trafficflowStatNums_ != 0L) {
      output.writeUInt64(7, trafficflowStatNums_);
    }
    if (intersectionStatNums_ != 0L) {
      output.writeUInt64(8, intersectionStatNums_);
    }
    if (phaseStatNums_ != 0L) {
      output.writeUInt64(9, phaseStatNums_);
    }
    if (rtsNums_ != 0L) {
      output.writeUInt64(10, rtsNums_);
    }
    if (cameraPathListNums_ != 0L) {
      output.writeUInt64(11, cameraPathListNums_);
    }
    if (cameraPathNums_ != 0L) {
      output.writeUInt64(12, cameraPathNums_);
    }
    if (radarPathListNums_ != 0L) {
      output.writeUInt64(13, radarPathListNums_);
    }
    if (radarPathNums_ != 0L) {
      output.writeUInt64(14, radarPathNums_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, timestamp_);
    }
    if (!getDeviceIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, deviceId_);
    }
    if (camNums_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, camNums_);
    }
    if (participantNums_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(4, participantNums_);
    }
    if (rteNums_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(5, rteNums_);
    }
    if (trafficflowNums_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(6, trafficflowNums_);
    }
    if (trafficflowStatNums_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(7, trafficflowStatNums_);
    }
    if (intersectionStatNums_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(8, intersectionStatNums_);
    }
    if (phaseStatNums_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(9, phaseStatNums_);
    }
    if (rtsNums_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(10, rtsNums_);
    }
    if (cameraPathListNums_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(11, cameraPathListNums_);
    }
    if (cameraPathNums_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(12, cameraPathNums_);
    }
    if (radarPathListNums_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(13, radarPathListNums_);
    }
    if (radarPathNums_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(14, radarPathNums_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.MonitorStatsData)) {
      return super.equals(obj);
    }
    road.data.proto.MonitorStatsData other = (road.data.proto.MonitorStatsData) obj;

    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (!getDeviceId()
        .equals(other.getDeviceId())) return false;
    if (getCamNums()
        != other.getCamNums()) return false;
    if (getParticipantNums()
        != other.getParticipantNums()) return false;
    if (getRteNums()
        != other.getRteNums()) return false;
    if (getTrafficflowNums()
        != other.getTrafficflowNums()) return false;
    if (getTrafficflowStatNums()
        != other.getTrafficflowStatNums()) return false;
    if (getIntersectionStatNums()
        != other.getIntersectionStatNums()) return false;
    if (getPhaseStatNums()
        != other.getPhaseStatNums()) return false;
    if (getRtsNums()
        != other.getRtsNums()) return false;
    if (getCameraPathListNums()
        != other.getCameraPathListNums()) return false;
    if (getCameraPathNums()
        != other.getCameraPathNums()) return false;
    if (getRadarPathListNums()
        != other.getRadarPathListNums()) return false;
    if (getRadarPathNums()
        != other.getRadarPathNums()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    hash = (37 * hash) + DEVICEID_FIELD_NUMBER;
    hash = (53 * hash) + getDeviceId().hashCode();
    hash = (37 * hash) + CAMNUMS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getCamNums());
    hash = (37 * hash) + PARTICIPANTNUMS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getParticipantNums());
    hash = (37 * hash) + RTENUMS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getRteNums());
    hash = (37 * hash) + TRAFFICFLOWNUMS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTrafficflowNums());
    hash = (37 * hash) + TRAFFICFLOWSTATNUMS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTrafficflowStatNums());
    hash = (37 * hash) + INTERSECTIONSTATNUMS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getIntersectionStatNums());
    hash = (37 * hash) + PHASESTATNUMS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getPhaseStatNums());
    hash = (37 * hash) + RTSNUMS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getRtsNums());
    hash = (37 * hash) + CAMERAPATHLISTNUMS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getCameraPathListNums());
    hash = (37 * hash) + CAMERAPATHNUMS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getCameraPathNums());
    hash = (37 * hash) + RADARPATHLISTNUMS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getRadarPathListNums());
    hash = (37 * hash) + RADARPATHNUMS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getRadarPathNums());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.MonitorStatsData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MonitorStatsData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MonitorStatsData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MonitorStatsData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MonitorStatsData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MonitorStatsData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MonitorStatsData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MonitorStatsData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MonitorStatsData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.MonitorStatsData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MonitorStatsData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MonitorStatsData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.MonitorStatsData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *监控统计消息 MonitorStatsData
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.MonitorStatsData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.MonitorStatsData)
      road.data.proto.MonitorStatsDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MonitorStatsData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MonitorStatsData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.MonitorStatsData.class, road.data.proto.MonitorStatsData.Builder.class);
    }

    // Construct using road.data.proto.MonitorStatsData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      timestamp_ = 0L;

      deviceId_ = "";

      camNums_ = 0L;

      participantNums_ = 0L;

      rteNums_ = 0L;

      trafficflowNums_ = 0L;

      trafficflowStatNums_ = 0L;

      intersectionStatNums_ = 0L;

      phaseStatNums_ = 0L;

      rtsNums_ = 0L;

      cameraPathListNums_ = 0L;

      cameraPathNums_ = 0L;

      radarPathListNums_ = 0L;

      radarPathNums_ = 0L;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MonitorStatsData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.MonitorStatsData getDefaultInstanceForType() {
      return road.data.proto.MonitorStatsData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.MonitorStatsData build() {
      road.data.proto.MonitorStatsData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.MonitorStatsData buildPartial() {
      road.data.proto.MonitorStatsData result = new road.data.proto.MonitorStatsData(this);
      result.timestamp_ = timestamp_;
      result.deviceId_ = deviceId_;
      result.camNums_ = camNums_;
      result.participantNums_ = participantNums_;
      result.rteNums_ = rteNums_;
      result.trafficflowNums_ = trafficflowNums_;
      result.trafficflowStatNums_ = trafficflowStatNums_;
      result.intersectionStatNums_ = intersectionStatNums_;
      result.phaseStatNums_ = phaseStatNums_;
      result.rtsNums_ = rtsNums_;
      result.cameraPathListNums_ = cameraPathListNums_;
      result.cameraPathNums_ = cameraPathNums_;
      result.radarPathListNums_ = radarPathListNums_;
      result.radarPathNums_ = radarPathNums_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.MonitorStatsData) {
        return mergeFrom((road.data.proto.MonitorStatsData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.MonitorStatsData other) {
      if (other == road.data.proto.MonitorStatsData.getDefaultInstance()) return this;
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (!other.getDeviceId().isEmpty()) {
        deviceId_ = other.deviceId_;
        onChanged();
      }
      if (other.getCamNums() != 0L) {
        setCamNums(other.getCamNums());
      }
      if (other.getParticipantNums() != 0L) {
        setParticipantNums(other.getParticipantNums());
      }
      if (other.getRteNums() != 0L) {
        setRteNums(other.getRteNums());
      }
      if (other.getTrafficflowNums() != 0L) {
        setTrafficflowNums(other.getTrafficflowNums());
      }
      if (other.getTrafficflowStatNums() != 0L) {
        setTrafficflowStatNums(other.getTrafficflowStatNums());
      }
      if (other.getIntersectionStatNums() != 0L) {
        setIntersectionStatNums(other.getIntersectionStatNums());
      }
      if (other.getPhaseStatNums() != 0L) {
        setPhaseStatNums(other.getPhaseStatNums());
      }
      if (other.getRtsNums() != 0L) {
        setRtsNums(other.getRtsNums());
      }
      if (other.getCameraPathListNums() != 0L) {
        setCameraPathListNums(other.getCameraPathListNums());
      }
      if (other.getCameraPathNums() != 0L) {
        setCameraPathNums(other.getCameraPathNums());
      }
      if (other.getRadarPathListNums() != 0L) {
        setRadarPathListNums(other.getRadarPathListNums());
      }
      if (other.getRadarPathNums() != 0L) {
        setRadarPathNums(other.getRadarPathNums());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.MonitorStatsData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.MonitorStatsData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object deviceId_ = "";
    /**
     * <pre>
     *mec设备的deviceId编号
     * </pre>
     *
     * <code>string deviceId = 2;</code>
     */
    public java.lang.String getDeviceId() {
      java.lang.Object ref = deviceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *mec设备的deviceId编号
     * </pre>
     *
     * <code>string deviceId = 2;</code>
     */
    public com.google.protobuf.ByteString
        getDeviceIdBytes() {
      java.lang.Object ref = deviceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *mec设备的deviceId编号
     * </pre>
     *
     * <code>string deviceId = 2;</code>
     */
    public Builder setDeviceId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deviceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *mec设备的deviceId编号
     * </pre>
     *
     * <code>string deviceId = 2;</code>
     */
    public Builder clearDeviceId() {
      
      deviceId_ = getDefaultInstance().getDeviceId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *mec设备的deviceId编号
     * </pre>
     *
     * <code>string deviceId = 2;</code>
     */
    public Builder setDeviceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deviceId_ = value;
      onChanged();
      return this;
    }

    private long camNums_ ;
    /**
     * <pre>
     *当前时间整点到此刻时间间隔内累积传输cam消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 camNums = 3;</code>
     */
    public long getCamNums() {
      return camNums_;
    }
    /**
     * <pre>
     *当前时间整点到此刻时间间隔内累积传输cam消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 camNums = 3;</code>
     */
    public Builder setCamNums(long value) {
      
      camNums_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *当前时间整点到此刻时间间隔内累积传输cam消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 camNums = 3;</code>
     */
    public Builder clearCamNums() {
      
      camNums_ = 0L;
      onChanged();
      return this;
    }

    private long participantNums_ ;
    /**
     * <pre>
     *当前时间整点到此刻时间间隔内累积传输交通参与者消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 participantNums = 4;</code>
     */
    public long getParticipantNums() {
      return participantNums_;
    }
    /**
     * <pre>
     *当前时间整点到此刻时间间隔内累积传输交通参与者消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 participantNums = 4;</code>
     */
    public Builder setParticipantNums(long value) {
      
      participantNums_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *当前时间整点到此刻时间间隔内累积传输交通参与者消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 participantNums = 4;</code>
     */
    public Builder clearParticipantNums() {
      
      participantNums_ = 0L;
      onChanged();
      return this;
    }

    private long rteNums_ ;
    /**
     * <pre>
     *当前时间整点到此刻时间间隔内累积传输rte消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 rteNums = 5;</code>
     */
    public long getRteNums() {
      return rteNums_;
    }
    /**
     * <pre>
     *当前时间整点到此刻时间间隔内累积传输rte消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 rteNums = 5;</code>
     */
    public Builder setRteNums(long value) {
      
      rteNums_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *当前时间整点到此刻时间间隔内累积传输rte消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 rteNums = 5;</code>
     */
    public Builder clearRteNums() {
      
      rteNums_ = 0L;
      onChanged();
      return this;
    }

    private long trafficflowNums_ ;
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输trafficflow消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 trafficflowNums = 6;</code>
     */
    public long getTrafficflowNums() {
      return trafficflowNums_;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输trafficflow消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 trafficflowNums = 6;</code>
     */
    public Builder setTrafficflowNums(long value) {
      
      trafficflowNums_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输trafficflow消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 trafficflowNums = 6;</code>
     */
    public Builder clearTrafficflowNums() {
      
      trafficflowNums_ = 0L;
      onChanged();
      return this;
    }

    private long trafficflowStatNums_ ;
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输trafficflowStat消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 trafficflowStatNums = 7;</code>
     */
    public long getTrafficflowStatNums() {
      return trafficflowStatNums_;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输trafficflowStat消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 trafficflowStatNums = 7;</code>
     */
    public Builder setTrafficflowStatNums(long value) {
      
      trafficflowStatNums_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输trafficflowStat消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 trafficflowStatNums = 7;</code>
     */
    public Builder clearTrafficflowStatNums() {
      
      trafficflowStatNums_ = 0L;
      onChanged();
      return this;
    }

    private long intersectionStatNums_ ;
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输intersectionStat消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 intersectionStatNums = 8;</code>
     */
    public long getIntersectionStatNums() {
      return intersectionStatNums_;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输intersectionStat消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 intersectionStatNums = 8;</code>
     */
    public Builder setIntersectionStatNums(long value) {
      
      intersectionStatNums_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输intersectionStat消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 intersectionStatNums = 8;</code>
     */
    public Builder clearIntersectionStatNums() {
      
      intersectionStatNums_ = 0L;
      onChanged();
      return this;
    }

    private long phaseStatNums_ ;
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输phaseStat消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 phaseStatNums = 9;</code>
     */
    public long getPhaseStatNums() {
      return phaseStatNums_;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输phaseStat消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 phaseStatNums = 9;</code>
     */
    public Builder setPhaseStatNums(long value) {
      
      phaseStatNums_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输phaseStat消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 phaseStatNums = 9;</code>
     */
    public Builder clearPhaseStatNums() {
      
      phaseStatNums_ = 0L;
      onChanged();
      return this;
    }

    private long rtsNums_ ;
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输rts消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 rtsNums = 10;</code>
     */
    public long getRtsNums() {
      return rtsNums_;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输rts消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 rtsNums = 10;</code>
     */
    public Builder setRtsNums(long value) {
      
      rtsNums_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输rts消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 rtsNums = 10;</code>
     */
    public Builder clearRtsNums() {
      
      rtsNums_ = 0L;
      onChanged();
      return this;
    }

    private long cameraPathListNums_ ;
    /**
     * <pre>
     *当前时间整点到此刻时间间隔内累积传输cameraPathList消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 cameraPathListNums = 11;</code>
     */
    public long getCameraPathListNums() {
      return cameraPathListNums_;
    }
    /**
     * <pre>
     *当前时间整点到此刻时间间隔内累积传输cameraPathList消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 cameraPathListNums = 11;</code>
     */
    public Builder setCameraPathListNums(long value) {
      
      cameraPathListNums_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *当前时间整点到此刻时间间隔内累积传输cameraPathList消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 cameraPathListNums = 11;</code>
     */
    public Builder clearCameraPathListNums() {
      
      cameraPathListNums_ = 0L;
      onChanged();
      return this;
    }

    private long cameraPathNums_ ;
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输cameraPath消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 cameraPathNums = 12;</code>
     */
    public long getCameraPathNums() {
      return cameraPathNums_;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输cameraPath消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 cameraPathNums = 12;</code>
     */
    public Builder setCameraPathNums(long value) {
      
      cameraPathNums_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输cameraPath消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 cameraPathNums = 12;</code>
     */
    public Builder clearCameraPathNums() {
      
      cameraPathNums_ = 0L;
      onChanged();
      return this;
    }

    private long radarPathListNums_ ;
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输radarPathList消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 radarPathListNums = 13;</code>
     */
    public long getRadarPathListNums() {
      return radarPathListNums_;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输radarPathList消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 radarPathListNums = 13;</code>
     */
    public Builder setRadarPathListNums(long value) {
      
      radarPathListNums_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输radarPathList消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 radarPathListNums = 13;</code>
     */
    public Builder clearRadarPathListNums() {
      
      radarPathListNums_ = 0L;
      onChanged();
      return this;
    }

    private long radarPathNums_ ;
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输radarPath消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 radarPathNums = 14;</code>
     */
    public long getRadarPathNums() {
      return radarPathNums_;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输radarPath消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 radarPathNums = 14;</code>
     */
    public Builder setRadarPathNums(long value) {
      
      radarPathNums_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前时间整点到此刻时间间隔内累积传输radarPath消息数量，最大时间间隔1h
     * </pre>
     *
     * <code>uint64 radarPathNums = 14;</code>
     */
    public Builder clearRadarPathNums() {
      
      radarPathNums_ = 0L;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.MonitorStatsData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.MonitorStatsData)
  private static final road.data.proto.MonitorStatsData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.MonitorStatsData();
  }

  public static road.data.proto.MonitorStatsData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MonitorStatsData>
      PARSER = new com.google.protobuf.AbstractParser<MonitorStatsData>() {
    @java.lang.Override
    public MonitorStatsData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new MonitorStatsData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<MonitorStatsData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MonitorStatsData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.MonitorStatsData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

