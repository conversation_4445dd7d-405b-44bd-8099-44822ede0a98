// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface MonitorStatsDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.MonitorStatsData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 1;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   *mec设备的deviceId编号
   * </pre>
   *
   * <code>string deviceId = 2;</code>
   */
  java.lang.String getDeviceId();
  /**
   * <pre>
   *mec设备的deviceId编号
   * </pre>
   *
   * <code>string deviceId = 2;</code>
   */
  com.google.protobuf.ByteString
      getDeviceIdBytes();

  /**
   * <pre>
   *当前时间整点到此刻时间间隔内累积传输cam消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 camNums = 3;</code>
   */
  long getCamNums();

  /**
   * <pre>
   *当前时间整点到此刻时间间隔内累积传输交通参与者消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 participantNums = 4;</code>
   */
  long getParticipantNums();

  /**
   * <pre>
   *当前时间整点到此刻时间间隔内累积传输rte消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 rteNums = 5;</code>
   */
  long getRteNums();

  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输trafficflow消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 trafficflowNums = 6;</code>
   */
  long getTrafficflowNums();

  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输trafficflowStat消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 trafficflowStatNums = 7;</code>
   */
  long getTrafficflowStatNums();

  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输intersectionStat消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 intersectionStatNums = 8;</code>
   */
  long getIntersectionStatNums();

  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输phaseStat消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 phaseStatNums = 9;</code>
   */
  long getPhaseStatNums();

  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输rts消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 rtsNums = 10;</code>
   */
  long getRtsNums();

  /**
   * <pre>
   *当前时间整点到此刻时间间隔内累积传输cameraPathList消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 cameraPathListNums = 11;</code>
   */
  long getCameraPathListNums();

  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输cameraPath消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 cameraPathNums = 12;</code>
   */
  long getCameraPathNums();

  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输radarPathList消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 radarPathListNums = 13;</code>
   */
  long getRadarPathListNums();

  /**
   * <pre>
   * 当前时间整点到此刻时间间隔内累积传输radarPath消息数量，最大时间间隔1h
   * </pre>
   *
   * <code>uint64 radarPathNums = 14;</code>
   */
  long getRadarPathNums();
}
