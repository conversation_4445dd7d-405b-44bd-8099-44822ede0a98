// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车辆运动运动状态精度  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.MotionConfidenceSet}
 */
public  final class MotionConfidenceSet extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.MotionConfidenceSet)
    MotionConfidenceSetOrBuilder {
private static final long serialVersionUID = 0L;
  // Use MotionConfidenceSet.newBuilder() to construct.
  private MotionConfidenceSet(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private MotionConfidenceSet() {
    speedCfd_ = 0;
    headingCfd_ = 0;
    steerCfd_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new MotionConfidenceSet();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private MotionConfidenceSet(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {
            int rawValue = input.readEnum();

            speedCfd_ = rawValue;
            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            headingCfd_ = rawValue;
            break;
          }
          case 24: {
            int rawValue = input.readEnum();

            steerCfd_ = rawValue;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MotionConfidenceSet_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MotionConfidenceSet_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.MotionConfidenceSet.class, road.data.proto.MotionConfidenceSet.Builder.class);
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.MotionConfidenceSet.SteeringWheelAngleConfidence}
   */
  public enum SteeringWheelAngleConfidence
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>STEERING_WHEEL_ANGLE_CONFID_UNAVAILABLE = 0;</code>
     */
    STEERING_WHEEL_ANGLE_CONFID_UNAVAILABLE(0),
    /**
     * <pre>
     *2度
     * </pre>
     *
     * <code>STEERING_WHEEL_ANGLE_CONFID_PREC2DEG = 1;</code>
     */
    STEERING_WHEEL_ANGLE_CONFID_PREC2DEG(1),
    /**
     * <pre>
     *1度
     * </pre>
     *
     * <code>STEERING_WHEEL_ANGLE_CONFID_PREC1DEG = 2;</code>
     */
    STEERING_WHEEL_ANGLE_CONFID_PREC1DEG(2),
    /**
     * <pre>
     *0.02度
     * </pre>
     *
     * <code>STEERING_WHEEL_ANGLE_CONFID_PREC0_02DEG = 3;</code>
     */
    STEERING_WHEEL_ANGLE_CONFID_PREC0_02DEG(3),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>STEERING_WHEEL_ANGLE_CONFID_UNAVAILABLE = 0;</code>
     */
    public static final int STEERING_WHEEL_ANGLE_CONFID_UNAVAILABLE_VALUE = 0;
    /**
     * <pre>
     *2度
     * </pre>
     *
     * <code>STEERING_WHEEL_ANGLE_CONFID_PREC2DEG = 1;</code>
     */
    public static final int STEERING_WHEEL_ANGLE_CONFID_PREC2DEG_VALUE = 1;
    /**
     * <pre>
     *1度
     * </pre>
     *
     * <code>STEERING_WHEEL_ANGLE_CONFID_PREC1DEG = 2;</code>
     */
    public static final int STEERING_WHEEL_ANGLE_CONFID_PREC1DEG_VALUE = 2;
    /**
     * <pre>
     *0.02度
     * </pre>
     *
     * <code>STEERING_WHEEL_ANGLE_CONFID_PREC0_02DEG = 3;</code>
     */
    public static final int STEERING_WHEEL_ANGLE_CONFID_PREC0_02DEG_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static SteeringWheelAngleConfidence valueOf(int value) {
      return forNumber(value);
    }

    public static SteeringWheelAngleConfidence forNumber(int value) {
      switch (value) {
        case 0: return STEERING_WHEEL_ANGLE_CONFID_UNAVAILABLE;
        case 1: return STEERING_WHEEL_ANGLE_CONFID_PREC2DEG;
        case 2: return STEERING_WHEEL_ANGLE_CONFID_PREC1DEG;
        case 3: return STEERING_WHEEL_ANGLE_CONFID_PREC0_02DEG;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SteeringWheelAngleConfidence>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        SteeringWheelAngleConfidence> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SteeringWheelAngleConfidence>() {
            public SteeringWheelAngleConfidence findValueByNumber(int number) {
              return SteeringWheelAngleConfidence.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.MotionConfidenceSet.getDescriptor().getEnumTypes().get(0);
    }

    private static final SteeringWheelAngleConfidence[] VALUES = values();

    public static SteeringWheelAngleConfidence valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private SteeringWheelAngleConfidence(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.MotionConfidenceSet.SteeringWheelAngleConfidence)
  }

  public static final int SPEEDCFD_FIELD_NUMBER = 1;
  private int speedCfd_;
  /**
   * <pre>
   * enum SpeedConfidence{
   *     SPEED_CONFID_UNAVAILABLE = 0; // __未配备或不可用
   *     SPEED_CONFID_100MS = 1; // __100 METERS/SEC
   *     SPEED_CONFID_10MS = 2;  // __10 METERS/SEE
   *     SPEED_CONFID_5MS = 3; // __5 METERS/SEC
   *     SPEED_CONFID_1MS = 4; // __1 METERS/SEC
   *     SPEED_CONFID_0_1MS = 5;  // __ 0.1 METERS/SEC
   *     SPEED_CONFID_0_05MS = 6; // __0.05 METERS/SEC
   *     SPEED_CONFID_0_01MS = 7;  // __0.01 METERS/SEC
   * };
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpeedConfidence speedCfd = 1;</code>
   */
  public int getSpeedCfdValue() {
    return speedCfd_;
  }
  /**
   * <pre>
   * enum SpeedConfidence{
   *     SPEED_CONFID_UNAVAILABLE = 0; // __未配备或不可用
   *     SPEED_CONFID_100MS = 1; // __100 METERS/SEC
   *     SPEED_CONFID_10MS = 2;  // __10 METERS/SEE
   *     SPEED_CONFID_5MS = 3; // __5 METERS/SEC
   *     SPEED_CONFID_1MS = 4; // __1 METERS/SEC
   *     SPEED_CONFID_0_1MS = 5;  // __ 0.1 METERS/SEC
   *     SPEED_CONFID_0_05MS = 6; // __0.05 METERS/SEC
   *     SPEED_CONFID_0_01MS = 7;  // __0.01 METERS/SEC
   * };
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpeedConfidence speedCfd = 1;</code>
   */
  public road.data.proto.SpeedConfidence getSpeedCfd() {
    @SuppressWarnings("deprecation")
    road.data.proto.SpeedConfidence result = road.data.proto.SpeedConfidence.valueOf(speedCfd_);
    return result == null ? road.data.proto.SpeedConfidence.UNRECOGNIZED : result;
  }

  public static final int HEADINGCFD_FIELD_NUMBER = 2;
  private int headingCfd_;
  /**
   * <pre>
   * enum HeadingConfidence{
   *     HEADING_CONFID_UNAVAILABLE= 0;
   *     HEADING_CONFID_PREC10DEG= 1;
   *     HEADING_CONFIDE_PREC05DEG= 2;
   *     HEADING_CONFIDE_PREC01DEG= 3;
   *     HEADING_CONFID_PREC_1DEG= 4;
   *     HEADING_CONFID_PREC0_05DEG= 5;
   *     HEADING_CONFID_PREC0_01DEG= 6;
   *     HEADING_CONFID_PREC0_0125DEG= 7;
   * };
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.HeadingConfidence headingCfd = 2;</code>
   */
  public int getHeadingCfdValue() {
    return headingCfd_;
  }
  /**
   * <pre>
   * enum HeadingConfidence{
   *     HEADING_CONFID_UNAVAILABLE= 0;
   *     HEADING_CONFID_PREC10DEG= 1;
   *     HEADING_CONFIDE_PREC05DEG= 2;
   *     HEADING_CONFIDE_PREC01DEG= 3;
   *     HEADING_CONFID_PREC_1DEG= 4;
   *     HEADING_CONFID_PREC0_05DEG= 5;
   *     HEADING_CONFID_PREC0_01DEG= 6;
   *     HEADING_CONFID_PREC0_0125DEG= 7;
   * };
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.HeadingConfidence headingCfd = 2;</code>
   */
  public road.data.proto.HeadingConfidence getHeadingCfd() {
    @SuppressWarnings("deprecation")
    road.data.proto.HeadingConfidence result = road.data.proto.HeadingConfidence.valueOf(headingCfd_);
    return result == null ? road.data.proto.HeadingConfidence.UNRECOGNIZED : result;
  }

  public static final int STEERCFD_FIELD_NUMBER = 3;
  private int steerCfd_;
  /**
   * <pre>
   *可选，方向盘转角精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet.SteeringWheelAngleConfidence steerCfd = 3;</code>
   */
  public int getSteerCfdValue() {
    return steerCfd_;
  }
  /**
   * <pre>
   *可选，方向盘转角精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet.SteeringWheelAngleConfidence steerCfd = 3;</code>
   */
  public road.data.proto.MotionConfidenceSet.SteeringWheelAngleConfidence getSteerCfd() {
    @SuppressWarnings("deprecation")
    road.data.proto.MotionConfidenceSet.SteeringWheelAngleConfidence result = road.data.proto.MotionConfidenceSet.SteeringWheelAngleConfidence.valueOf(steerCfd_);
    return result == null ? road.data.proto.MotionConfidenceSet.SteeringWheelAngleConfidence.UNRECOGNIZED : result;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (speedCfd_ != road.data.proto.SpeedConfidence.SPEED_CONFID_UNAVAILABLE.getNumber()) {
      output.writeEnum(1, speedCfd_);
    }
    if (headingCfd_ != road.data.proto.HeadingConfidence.HEADING_CONFID_UNAVAILABLE.getNumber()) {
      output.writeEnum(2, headingCfd_);
    }
    if (steerCfd_ != road.data.proto.MotionConfidenceSet.SteeringWheelAngleConfidence.STEERING_WHEEL_ANGLE_CONFID_UNAVAILABLE.getNumber()) {
      output.writeEnum(3, steerCfd_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (speedCfd_ != road.data.proto.SpeedConfidence.SPEED_CONFID_UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(1, speedCfd_);
    }
    if (headingCfd_ != road.data.proto.HeadingConfidence.HEADING_CONFID_UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, headingCfd_);
    }
    if (steerCfd_ != road.data.proto.MotionConfidenceSet.SteeringWheelAngleConfidence.STEERING_WHEEL_ANGLE_CONFID_UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, steerCfd_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.MotionConfidenceSet)) {
      return super.equals(obj);
    }
    road.data.proto.MotionConfidenceSet other = (road.data.proto.MotionConfidenceSet) obj;

    if (speedCfd_ != other.speedCfd_) return false;
    if (headingCfd_ != other.headingCfd_) return false;
    if (steerCfd_ != other.steerCfd_) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SPEEDCFD_FIELD_NUMBER;
    hash = (53 * hash) + speedCfd_;
    hash = (37 * hash) + HEADINGCFD_FIELD_NUMBER;
    hash = (53 * hash) + headingCfd_;
    hash = (37 * hash) + STEERCFD_FIELD_NUMBER;
    hash = (53 * hash) + steerCfd_;
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.MotionConfidenceSet parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MotionConfidenceSet parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MotionConfidenceSet parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MotionConfidenceSet parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MotionConfidenceSet parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MotionConfidenceSet parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MotionConfidenceSet parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MotionConfidenceSet parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MotionConfidenceSet parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.MotionConfidenceSet parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MotionConfidenceSet parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MotionConfidenceSet parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.MotionConfidenceSet prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *车辆运动运动状态精度  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.MotionConfidenceSet}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.MotionConfidenceSet)
      road.data.proto.MotionConfidenceSetOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MotionConfidenceSet_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MotionConfidenceSet_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.MotionConfidenceSet.class, road.data.proto.MotionConfidenceSet.Builder.class);
    }

    // Construct using road.data.proto.MotionConfidenceSet.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      speedCfd_ = 0;

      headingCfd_ = 0;

      steerCfd_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MotionConfidenceSet_descriptor;
    }

    @java.lang.Override
    public road.data.proto.MotionConfidenceSet getDefaultInstanceForType() {
      return road.data.proto.MotionConfidenceSet.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.MotionConfidenceSet build() {
      road.data.proto.MotionConfidenceSet result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.MotionConfidenceSet buildPartial() {
      road.data.proto.MotionConfidenceSet result = new road.data.proto.MotionConfidenceSet(this);
      result.speedCfd_ = speedCfd_;
      result.headingCfd_ = headingCfd_;
      result.steerCfd_ = steerCfd_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.MotionConfidenceSet) {
        return mergeFrom((road.data.proto.MotionConfidenceSet)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.MotionConfidenceSet other) {
      if (other == road.data.proto.MotionConfidenceSet.getDefaultInstance()) return this;
      if (other.speedCfd_ != 0) {
        setSpeedCfdValue(other.getSpeedCfdValue());
      }
      if (other.headingCfd_ != 0) {
        setHeadingCfdValue(other.getHeadingCfdValue());
      }
      if (other.steerCfd_ != 0) {
        setSteerCfdValue(other.getSteerCfdValue());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.MotionConfidenceSet parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.MotionConfidenceSet) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int speedCfd_ = 0;
    /**
     * <pre>
     * enum SpeedConfidence{
     *     SPEED_CONFID_UNAVAILABLE = 0; // __未配备或不可用
     *     SPEED_CONFID_100MS = 1; // __100 METERS/SEC
     *     SPEED_CONFID_10MS = 2;  // __10 METERS/SEE
     *     SPEED_CONFID_5MS = 3; // __5 METERS/SEC
     *     SPEED_CONFID_1MS = 4; // __1 METERS/SEC
     *     SPEED_CONFID_0_1MS = 5;  // __ 0.1 METERS/SEC
     *     SPEED_CONFID_0_05MS = 6; // __0.05 METERS/SEC
     *     SPEED_CONFID_0_01MS = 7;  // __0.01 METERS/SEC
     * };
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence speedCfd = 1;</code>
     */
    public int getSpeedCfdValue() {
      return speedCfd_;
    }
    /**
     * <pre>
     * enum SpeedConfidence{
     *     SPEED_CONFID_UNAVAILABLE = 0; // __未配备或不可用
     *     SPEED_CONFID_100MS = 1; // __100 METERS/SEC
     *     SPEED_CONFID_10MS = 2;  // __10 METERS/SEE
     *     SPEED_CONFID_5MS = 3; // __5 METERS/SEC
     *     SPEED_CONFID_1MS = 4; // __1 METERS/SEC
     *     SPEED_CONFID_0_1MS = 5;  // __ 0.1 METERS/SEC
     *     SPEED_CONFID_0_05MS = 6; // __0.05 METERS/SEC
     *     SPEED_CONFID_0_01MS = 7;  // __0.01 METERS/SEC
     * };
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence speedCfd = 1;</code>
     */
    public Builder setSpeedCfdValue(int value) {
      speedCfd_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * enum SpeedConfidence{
     *     SPEED_CONFID_UNAVAILABLE = 0; // __未配备或不可用
     *     SPEED_CONFID_100MS = 1; // __100 METERS/SEC
     *     SPEED_CONFID_10MS = 2;  // __10 METERS/SEE
     *     SPEED_CONFID_5MS = 3; // __5 METERS/SEC
     *     SPEED_CONFID_1MS = 4; // __1 METERS/SEC
     *     SPEED_CONFID_0_1MS = 5;  // __ 0.1 METERS/SEC
     *     SPEED_CONFID_0_05MS = 6; // __0.05 METERS/SEC
     *     SPEED_CONFID_0_01MS = 7;  // __0.01 METERS/SEC
     * };
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence speedCfd = 1;</code>
     */
    public road.data.proto.SpeedConfidence getSpeedCfd() {
      @SuppressWarnings("deprecation")
      road.data.proto.SpeedConfidence result = road.data.proto.SpeedConfidence.valueOf(speedCfd_);
      return result == null ? road.data.proto.SpeedConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * enum SpeedConfidence{
     *     SPEED_CONFID_UNAVAILABLE = 0; // __未配备或不可用
     *     SPEED_CONFID_100MS = 1; // __100 METERS/SEC
     *     SPEED_CONFID_10MS = 2;  // __10 METERS/SEE
     *     SPEED_CONFID_5MS = 3; // __5 METERS/SEC
     *     SPEED_CONFID_1MS = 4; // __1 METERS/SEC
     *     SPEED_CONFID_0_1MS = 5;  // __ 0.1 METERS/SEC
     *     SPEED_CONFID_0_05MS = 6; // __0.05 METERS/SEC
     *     SPEED_CONFID_0_01MS = 7;  // __0.01 METERS/SEC
     * };
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence speedCfd = 1;</code>
     */
    public Builder setSpeedCfd(road.data.proto.SpeedConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      speedCfd_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * enum SpeedConfidence{
     *     SPEED_CONFID_UNAVAILABLE = 0; // __未配备或不可用
     *     SPEED_CONFID_100MS = 1; // __100 METERS/SEC
     *     SPEED_CONFID_10MS = 2;  // __10 METERS/SEE
     *     SPEED_CONFID_5MS = 3; // __5 METERS/SEC
     *     SPEED_CONFID_1MS = 4; // __1 METERS/SEC
     *     SPEED_CONFID_0_1MS = 5;  // __ 0.1 METERS/SEC
     *     SPEED_CONFID_0_05MS = 6; // __0.05 METERS/SEC
     *     SPEED_CONFID_0_01MS = 7;  // __0.01 METERS/SEC
     * };
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence speedCfd = 1;</code>
     */
    public Builder clearSpeedCfd() {
      
      speedCfd_ = 0;
      onChanged();
      return this;
    }

    private int headingCfd_ = 0;
    /**
     * <pre>
     * enum HeadingConfidence{
     *     HEADING_CONFID_UNAVAILABLE= 0;
     *     HEADING_CONFID_PREC10DEG= 1;
     *     HEADING_CONFIDE_PREC05DEG= 2;
     *     HEADING_CONFIDE_PREC01DEG= 3;
     *     HEADING_CONFID_PREC_1DEG= 4;
     *     HEADING_CONFID_PREC0_05DEG= 5;
     *     HEADING_CONFID_PREC0_01DEG= 6;
     *     HEADING_CONFID_PREC0_0125DEG= 7;
     * };
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.HeadingConfidence headingCfd = 2;</code>
     */
    public int getHeadingCfdValue() {
      return headingCfd_;
    }
    /**
     * <pre>
     * enum HeadingConfidence{
     *     HEADING_CONFID_UNAVAILABLE= 0;
     *     HEADING_CONFID_PREC10DEG= 1;
     *     HEADING_CONFIDE_PREC05DEG= 2;
     *     HEADING_CONFIDE_PREC01DEG= 3;
     *     HEADING_CONFID_PREC_1DEG= 4;
     *     HEADING_CONFID_PREC0_05DEG= 5;
     *     HEADING_CONFID_PREC0_01DEG= 6;
     *     HEADING_CONFID_PREC0_0125DEG= 7;
     * };
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.HeadingConfidence headingCfd = 2;</code>
     */
    public Builder setHeadingCfdValue(int value) {
      headingCfd_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * enum HeadingConfidence{
     *     HEADING_CONFID_UNAVAILABLE= 0;
     *     HEADING_CONFID_PREC10DEG= 1;
     *     HEADING_CONFIDE_PREC05DEG= 2;
     *     HEADING_CONFIDE_PREC01DEG= 3;
     *     HEADING_CONFID_PREC_1DEG= 4;
     *     HEADING_CONFID_PREC0_05DEG= 5;
     *     HEADING_CONFID_PREC0_01DEG= 6;
     *     HEADING_CONFID_PREC0_0125DEG= 7;
     * };
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.HeadingConfidence headingCfd = 2;</code>
     */
    public road.data.proto.HeadingConfidence getHeadingCfd() {
      @SuppressWarnings("deprecation")
      road.data.proto.HeadingConfidence result = road.data.proto.HeadingConfidence.valueOf(headingCfd_);
      return result == null ? road.data.proto.HeadingConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * enum HeadingConfidence{
     *     HEADING_CONFID_UNAVAILABLE= 0;
     *     HEADING_CONFID_PREC10DEG= 1;
     *     HEADING_CONFIDE_PREC05DEG= 2;
     *     HEADING_CONFIDE_PREC01DEG= 3;
     *     HEADING_CONFID_PREC_1DEG= 4;
     *     HEADING_CONFID_PREC0_05DEG= 5;
     *     HEADING_CONFID_PREC0_01DEG= 6;
     *     HEADING_CONFID_PREC0_0125DEG= 7;
     * };
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.HeadingConfidence headingCfd = 2;</code>
     */
    public Builder setHeadingCfd(road.data.proto.HeadingConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      headingCfd_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * enum HeadingConfidence{
     *     HEADING_CONFID_UNAVAILABLE= 0;
     *     HEADING_CONFID_PREC10DEG= 1;
     *     HEADING_CONFIDE_PREC05DEG= 2;
     *     HEADING_CONFIDE_PREC01DEG= 3;
     *     HEADING_CONFID_PREC_1DEG= 4;
     *     HEADING_CONFID_PREC0_05DEG= 5;
     *     HEADING_CONFID_PREC0_01DEG= 6;
     *     HEADING_CONFID_PREC0_0125DEG= 7;
     * };
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.HeadingConfidence headingCfd = 2;</code>
     */
    public Builder clearHeadingCfd() {
      
      headingCfd_ = 0;
      onChanged();
      return this;
    }

    private int steerCfd_ = 0;
    /**
     * <pre>
     *可选，方向盘转角精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet.SteeringWheelAngleConfidence steerCfd = 3;</code>
     */
    public int getSteerCfdValue() {
      return steerCfd_;
    }
    /**
     * <pre>
     *可选，方向盘转角精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet.SteeringWheelAngleConfidence steerCfd = 3;</code>
     */
    public Builder setSteerCfdValue(int value) {
      steerCfd_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，方向盘转角精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet.SteeringWheelAngleConfidence steerCfd = 3;</code>
     */
    public road.data.proto.MotionConfidenceSet.SteeringWheelAngleConfidence getSteerCfd() {
      @SuppressWarnings("deprecation")
      road.data.proto.MotionConfidenceSet.SteeringWheelAngleConfidence result = road.data.proto.MotionConfidenceSet.SteeringWheelAngleConfidence.valueOf(steerCfd_);
      return result == null ? road.data.proto.MotionConfidenceSet.SteeringWheelAngleConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，方向盘转角精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet.SteeringWheelAngleConfidence steerCfd = 3;</code>
     */
    public Builder setSteerCfd(road.data.proto.MotionConfidenceSet.SteeringWheelAngleConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      steerCfd_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，方向盘转角精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet.SteeringWheelAngleConfidence steerCfd = 3;</code>
     */
    public Builder clearSteerCfd() {
      
      steerCfd_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.MotionConfidenceSet)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.MotionConfidenceSet)
  private static final road.data.proto.MotionConfidenceSet DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.MotionConfidenceSet();
  }

  public static road.data.proto.MotionConfidenceSet getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MotionConfidenceSet>
      PARSER = new com.google.protobuf.AbstractParser<MotionConfidenceSet>() {
    @java.lang.Override
    public MotionConfidenceSet parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new MotionConfidenceSet(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<MotionConfidenceSet> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MotionConfidenceSet> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.MotionConfidenceSet getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

