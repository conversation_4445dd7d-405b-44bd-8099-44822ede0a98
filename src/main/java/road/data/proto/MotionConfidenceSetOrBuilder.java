// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface MotionConfidenceSetOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.MotionConfidenceSet)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * enum SpeedConfidence{
   *     SPEED_CONFID_UNAVAILABLE = 0; // __未配备或不可用
   *     SPEED_CONFID_100MS = 1; // __100 METERS/SEC
   *     SPEED_CONFID_10MS = 2;  // __10 METERS/SEE
   *     SPEED_CONFID_5MS = 3; // __5 METERS/SEC
   *     SPEED_CONFID_1MS = 4; // __1 METERS/SEC
   *     SPEED_CONFID_0_1MS = 5;  // __ 0.1 METERS/SEC
   *     SPEED_CONFID_0_05MS = 6; // __0.05 METERS/SEC
   *     SPEED_CONFID_0_01MS = 7;  // __0.01 METERS/SEC
   * };
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpeedConfidence speedCfd = 1;</code>
   */
  int getSpeedCfdValue();
  /**
   * <pre>
   * enum SpeedConfidence{
   *     SPEED_CONFID_UNAVAILABLE = 0; // __未配备或不可用
   *     SPEED_CONFID_100MS = 1; // __100 METERS/SEC
   *     SPEED_CONFID_10MS = 2;  // __10 METERS/SEE
   *     SPEED_CONFID_5MS = 3; // __5 METERS/SEC
   *     SPEED_CONFID_1MS = 4; // __1 METERS/SEC
   *     SPEED_CONFID_0_1MS = 5;  // __ 0.1 METERS/SEC
   *     SPEED_CONFID_0_05MS = 6; // __0.05 METERS/SEC
   *     SPEED_CONFID_0_01MS = 7;  // __0.01 METERS/SEC
   * };
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpeedConfidence speedCfd = 1;</code>
   */
  road.data.proto.SpeedConfidence getSpeedCfd();

  /**
   * <pre>
   * enum HeadingConfidence{
   *     HEADING_CONFID_UNAVAILABLE= 0;
   *     HEADING_CONFID_PREC10DEG= 1;
   *     HEADING_CONFIDE_PREC05DEG= 2;
   *     HEADING_CONFIDE_PREC01DEG= 3;
   *     HEADING_CONFID_PREC_1DEG= 4;
   *     HEADING_CONFID_PREC0_05DEG= 5;
   *     HEADING_CONFID_PREC0_01DEG= 6;
   *     HEADING_CONFID_PREC0_0125DEG= 7;
   * };
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.HeadingConfidence headingCfd = 2;</code>
   */
  int getHeadingCfdValue();
  /**
   * <pre>
   * enum HeadingConfidence{
   *     HEADING_CONFID_UNAVAILABLE= 0;
   *     HEADING_CONFID_PREC10DEG= 1;
   *     HEADING_CONFIDE_PREC05DEG= 2;
   *     HEADING_CONFIDE_PREC01DEG= 3;
   *     HEADING_CONFID_PREC_1DEG= 4;
   *     HEADING_CONFID_PREC0_05DEG= 5;
   *     HEADING_CONFID_PREC0_01DEG= 6;
   *     HEADING_CONFID_PREC0_0125DEG= 7;
   * };
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.HeadingConfidence headingCfd = 2;</code>
   */
  road.data.proto.HeadingConfidence getHeadingCfd();

  /**
   * <pre>
   *可选，方向盘转角精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet.SteeringWheelAngleConfidence steerCfd = 3;</code>
   */
  int getSteerCfdValue();
  /**
   * <pre>
   *可选，方向盘转角精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet.SteeringWheelAngleConfidence steerCfd = 3;</code>
   */
  road.data.proto.MotionConfidenceSet.SteeringWheelAngleConfidence getSteerCfd();
}
