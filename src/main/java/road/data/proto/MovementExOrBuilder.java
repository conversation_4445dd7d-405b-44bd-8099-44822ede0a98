// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface MovementExOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.MovementEx)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *道路与下游路段的连接关系扩展信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  boolean hasRemoteIntersection();
  /**
   * <pre>
   *道路与下游路段的连接关系扩展信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  road.data.proto.NodeReferenceId getRemoteIntersection();
  /**
   * <pre>
   *道路与下游路段的连接关系扩展信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getRemoteIntersectionOrBuilder();

  /**
   * <pre>
   *可选，相位
   * </pre>
   *
   * <code>uint32 phaseId = 2;</code>
   */
  int getPhaseId();

  /**
   * <pre>
   *可选，允许的转弯行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 3;</code>
   */
  int getTurnDirectionValue();
  /**
   * <pre>
   *可选，允许的转弯行为
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 3;</code>
   */
  road.data.proto.Maneuver getTurnDirection();
}
