// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *转向级指标 MovementIndexAdded  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.MovementIndexAdded}
 */
public  final class MovementIndexAdded extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.MovementIndexAdded)
    MovementIndexAddedOrBuilder {
private static final long serialVersionUID = 0L;
  // Use MovementIndexAdded.newBuilder() to construct.
  private MovementIndexAdded(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private MovementIndexAdded() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new MovementIndexAdded();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private MovementIndexAdded(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 16: {

            movementCapacity_ = input.readUInt32();
            break;
          }
          case 24: {

            movementSaturation_ = input.readUInt32();
            break;
          }
          case 32: {

            movementSpaceOccupy_ = input.readUInt32();
            break;
          }
          case 40: {

            movementTimeOccupy_ = input.readUInt32();
            break;
          }
          case 48: {

            movementAvgGrnQueue_ = input.readUInt32();
            break;
          }
          case 56: {

            movementGrnUtilization_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MovementIndexAdded_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MovementIndexAdded_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.MovementIndexAdded.class, road.data.proto.MovementIndexAdded.Builder.class);
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 1;
  private long timestamp_;
  /**
   * <pre>
   *数据时间
   * </pre>
   *
   * <code>uint64 timestamp = 1;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int MOVEMENTCAPACITY_FIELD_NUMBER = 2;
  private int movementCapacity_;
  /**
   * <pre>
   *可选，转向通行能力，0.01pcu/h。解释同车道通行能力
   * </pre>
   *
   * <code>uint32 movementCapacity = 2;</code>
   */
  public int getMovementCapacity() {
    return movementCapacity_;
  }

  public static final int MOVEMENTSATURATION_FIELD_NUMBER = 3;
  private int movementSaturation_;
  /**
   * <pre>
   *可选，转向平均饱和度，0.01%
   * </pre>
   *
   * <code>uint32 movementSaturation = 3;</code>
   */
  public int getMovementSaturation() {
    return movementSaturation_;
  }

  public static final int MOVEMENTSPACEOCCUPY_FIELD_NUMBER = 4;
  private int movementSpaceOccupy_;
  /**
   * <pre>
   *可选，转向平均车道空间占有率，0.01%
   * </pre>
   *
   * <code>uint32 movementSpaceOccupy = 4;</code>
   */
  public int getMovementSpaceOccupy() {
    return movementSpaceOccupy_;
  }

  public static final int MOVEMENTTIMEOCCUPY_FIELD_NUMBER = 5;
  private int movementTimeOccupy_;
  /**
   * <pre>
   *可选，转向平均车道时间占有率，0.01%
   * </pre>
   *
   * <code>uint32 movementTimeOccupy = 5;</code>
   */
  public int getMovementTimeOccupy() {
    return movementTimeOccupy_;
  }

  public static final int MOVEMENTAVGGRNQUEUE_FIELD_NUMBER = 6;
  private int movementAvgGrnQueue_;
  /**
   * <pre>
   *可选，转向绿初车辆平均排队长度，0.01m
   * </pre>
   *
   * <code>uint32 movementAvgGrnQueue = 6;</code>
   */
  public int getMovementAvgGrnQueue() {
    return movementAvgGrnQueue_;
  }

  public static final int MOVEMENTGRNUTILIZATION_FIELD_NUMBER = 7;
  private int movementGrnUtilization_;
  /**
   * <pre>
   *可选，时段内转向平均绿灯利用率，0.01%
   * </pre>
   *
   * <code>uint32 movementGrnUtilization = 7;</code>
   */
  public int getMovementGrnUtilization() {
    return movementGrnUtilization_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (timestamp_ != 0L) {
      output.writeUInt64(1, timestamp_);
    }
    if (movementCapacity_ != 0) {
      output.writeUInt32(2, movementCapacity_);
    }
    if (movementSaturation_ != 0) {
      output.writeUInt32(3, movementSaturation_);
    }
    if (movementSpaceOccupy_ != 0) {
      output.writeUInt32(4, movementSpaceOccupy_);
    }
    if (movementTimeOccupy_ != 0) {
      output.writeUInt32(5, movementTimeOccupy_);
    }
    if (movementAvgGrnQueue_ != 0) {
      output.writeUInt32(6, movementAvgGrnQueue_);
    }
    if (movementGrnUtilization_ != 0) {
      output.writeUInt32(7, movementGrnUtilization_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, timestamp_);
    }
    if (movementCapacity_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, movementCapacity_);
    }
    if (movementSaturation_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, movementSaturation_);
    }
    if (movementSpaceOccupy_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, movementSpaceOccupy_);
    }
    if (movementTimeOccupy_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(5, movementTimeOccupy_);
    }
    if (movementAvgGrnQueue_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(6, movementAvgGrnQueue_);
    }
    if (movementGrnUtilization_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(7, movementGrnUtilization_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.MovementIndexAdded)) {
      return super.equals(obj);
    }
    road.data.proto.MovementIndexAdded other = (road.data.proto.MovementIndexAdded) obj;

    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (getMovementCapacity()
        != other.getMovementCapacity()) return false;
    if (getMovementSaturation()
        != other.getMovementSaturation()) return false;
    if (getMovementSpaceOccupy()
        != other.getMovementSpaceOccupy()) return false;
    if (getMovementTimeOccupy()
        != other.getMovementTimeOccupy()) return false;
    if (getMovementAvgGrnQueue()
        != other.getMovementAvgGrnQueue()) return false;
    if (getMovementGrnUtilization()
        != other.getMovementGrnUtilization()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    hash = (37 * hash) + MOVEMENTCAPACITY_FIELD_NUMBER;
    hash = (53 * hash) + getMovementCapacity();
    hash = (37 * hash) + MOVEMENTSATURATION_FIELD_NUMBER;
    hash = (53 * hash) + getMovementSaturation();
    hash = (37 * hash) + MOVEMENTSPACEOCCUPY_FIELD_NUMBER;
    hash = (53 * hash) + getMovementSpaceOccupy();
    hash = (37 * hash) + MOVEMENTTIMEOCCUPY_FIELD_NUMBER;
    hash = (53 * hash) + getMovementTimeOccupy();
    hash = (37 * hash) + MOVEMENTAVGGRNQUEUE_FIELD_NUMBER;
    hash = (53 * hash) + getMovementAvgGrnQueue();
    hash = (37 * hash) + MOVEMENTGRNUTILIZATION_FIELD_NUMBER;
    hash = (53 * hash) + getMovementGrnUtilization();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.MovementIndexAdded parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MovementIndexAdded parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MovementIndexAdded parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MovementIndexAdded parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MovementIndexAdded parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MovementIndexAdded parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MovementIndexAdded parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MovementIndexAdded parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MovementIndexAdded parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.MovementIndexAdded parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MovementIndexAdded parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MovementIndexAdded parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.MovementIndexAdded prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *转向级指标 MovementIndexAdded  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.MovementIndexAdded}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.MovementIndexAdded)
      road.data.proto.MovementIndexAddedOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MovementIndexAdded_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MovementIndexAdded_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.MovementIndexAdded.class, road.data.proto.MovementIndexAdded.Builder.class);
    }

    // Construct using road.data.proto.MovementIndexAdded.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      timestamp_ = 0L;

      movementCapacity_ = 0;

      movementSaturation_ = 0;

      movementSpaceOccupy_ = 0;

      movementTimeOccupy_ = 0;

      movementAvgGrnQueue_ = 0;

      movementGrnUtilization_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MovementIndexAdded_descriptor;
    }

    @java.lang.Override
    public road.data.proto.MovementIndexAdded getDefaultInstanceForType() {
      return road.data.proto.MovementIndexAdded.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.MovementIndexAdded build() {
      road.data.proto.MovementIndexAdded result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.MovementIndexAdded buildPartial() {
      road.data.proto.MovementIndexAdded result = new road.data.proto.MovementIndexAdded(this);
      result.timestamp_ = timestamp_;
      result.movementCapacity_ = movementCapacity_;
      result.movementSaturation_ = movementSaturation_;
      result.movementSpaceOccupy_ = movementSpaceOccupy_;
      result.movementTimeOccupy_ = movementTimeOccupy_;
      result.movementAvgGrnQueue_ = movementAvgGrnQueue_;
      result.movementGrnUtilization_ = movementGrnUtilization_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.MovementIndexAdded) {
        return mergeFrom((road.data.proto.MovementIndexAdded)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.MovementIndexAdded other) {
      if (other == road.data.proto.MovementIndexAdded.getDefaultInstance()) return this;
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (other.getMovementCapacity() != 0) {
        setMovementCapacity(other.getMovementCapacity());
      }
      if (other.getMovementSaturation() != 0) {
        setMovementSaturation(other.getMovementSaturation());
      }
      if (other.getMovementSpaceOccupy() != 0) {
        setMovementSpaceOccupy(other.getMovementSpaceOccupy());
      }
      if (other.getMovementTimeOccupy() != 0) {
        setMovementTimeOccupy(other.getMovementTimeOccupy());
      }
      if (other.getMovementAvgGrnQueue() != 0) {
        setMovementAvgGrnQueue(other.getMovementAvgGrnQueue());
      }
      if (other.getMovementGrnUtilization() != 0) {
        setMovementGrnUtilization(other.getMovementGrnUtilization());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.MovementIndexAdded parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.MovementIndexAdded) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     *数据时间
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     *数据时间
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *数据时间
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private int movementCapacity_ ;
    /**
     * <pre>
     *可选，转向通行能力，0.01pcu/h。解释同车道通行能力
     * </pre>
     *
     * <code>uint32 movementCapacity = 2;</code>
     */
    public int getMovementCapacity() {
      return movementCapacity_;
    }
    /**
     * <pre>
     *可选，转向通行能力，0.01pcu/h。解释同车道通行能力
     * </pre>
     *
     * <code>uint32 movementCapacity = 2;</code>
     */
    public Builder setMovementCapacity(int value) {
      
      movementCapacity_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，转向通行能力，0.01pcu/h。解释同车道通行能力
     * </pre>
     *
     * <code>uint32 movementCapacity = 2;</code>
     */
    public Builder clearMovementCapacity() {
      
      movementCapacity_ = 0;
      onChanged();
      return this;
    }

    private int movementSaturation_ ;
    /**
     * <pre>
     *可选，转向平均饱和度，0.01%
     * </pre>
     *
     * <code>uint32 movementSaturation = 3;</code>
     */
    public int getMovementSaturation() {
      return movementSaturation_;
    }
    /**
     * <pre>
     *可选，转向平均饱和度，0.01%
     * </pre>
     *
     * <code>uint32 movementSaturation = 3;</code>
     */
    public Builder setMovementSaturation(int value) {
      
      movementSaturation_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，转向平均饱和度，0.01%
     * </pre>
     *
     * <code>uint32 movementSaturation = 3;</code>
     */
    public Builder clearMovementSaturation() {
      
      movementSaturation_ = 0;
      onChanged();
      return this;
    }

    private int movementSpaceOccupy_ ;
    /**
     * <pre>
     *可选，转向平均车道空间占有率，0.01%
     * </pre>
     *
     * <code>uint32 movementSpaceOccupy = 4;</code>
     */
    public int getMovementSpaceOccupy() {
      return movementSpaceOccupy_;
    }
    /**
     * <pre>
     *可选，转向平均车道空间占有率，0.01%
     * </pre>
     *
     * <code>uint32 movementSpaceOccupy = 4;</code>
     */
    public Builder setMovementSpaceOccupy(int value) {
      
      movementSpaceOccupy_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，转向平均车道空间占有率，0.01%
     * </pre>
     *
     * <code>uint32 movementSpaceOccupy = 4;</code>
     */
    public Builder clearMovementSpaceOccupy() {
      
      movementSpaceOccupy_ = 0;
      onChanged();
      return this;
    }

    private int movementTimeOccupy_ ;
    /**
     * <pre>
     *可选，转向平均车道时间占有率，0.01%
     * </pre>
     *
     * <code>uint32 movementTimeOccupy = 5;</code>
     */
    public int getMovementTimeOccupy() {
      return movementTimeOccupy_;
    }
    /**
     * <pre>
     *可选，转向平均车道时间占有率，0.01%
     * </pre>
     *
     * <code>uint32 movementTimeOccupy = 5;</code>
     */
    public Builder setMovementTimeOccupy(int value) {
      
      movementTimeOccupy_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，转向平均车道时间占有率，0.01%
     * </pre>
     *
     * <code>uint32 movementTimeOccupy = 5;</code>
     */
    public Builder clearMovementTimeOccupy() {
      
      movementTimeOccupy_ = 0;
      onChanged();
      return this;
    }

    private int movementAvgGrnQueue_ ;
    /**
     * <pre>
     *可选，转向绿初车辆平均排队长度，0.01m
     * </pre>
     *
     * <code>uint32 movementAvgGrnQueue = 6;</code>
     */
    public int getMovementAvgGrnQueue() {
      return movementAvgGrnQueue_;
    }
    /**
     * <pre>
     *可选，转向绿初车辆平均排队长度，0.01m
     * </pre>
     *
     * <code>uint32 movementAvgGrnQueue = 6;</code>
     */
    public Builder setMovementAvgGrnQueue(int value) {
      
      movementAvgGrnQueue_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，转向绿初车辆平均排队长度，0.01m
     * </pre>
     *
     * <code>uint32 movementAvgGrnQueue = 6;</code>
     */
    public Builder clearMovementAvgGrnQueue() {
      
      movementAvgGrnQueue_ = 0;
      onChanged();
      return this;
    }

    private int movementGrnUtilization_ ;
    /**
     * <pre>
     *可选，时段内转向平均绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 movementGrnUtilization = 7;</code>
     */
    public int getMovementGrnUtilization() {
      return movementGrnUtilization_;
    }
    /**
     * <pre>
     *可选，时段内转向平均绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 movementGrnUtilization = 7;</code>
     */
    public Builder setMovementGrnUtilization(int value) {
      
      movementGrnUtilization_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，时段内转向平均绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 movementGrnUtilization = 7;</code>
     */
    public Builder clearMovementGrnUtilization() {
      
      movementGrnUtilization_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.MovementIndexAdded)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.MovementIndexAdded)
  private static final road.data.proto.MovementIndexAdded DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.MovementIndexAdded();
  }

  public static road.data.proto.MovementIndexAdded getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MovementIndexAdded>
      PARSER = new com.google.protobuf.AbstractParser<MovementIndexAdded>() {
    @java.lang.Override
    public MovementIndexAdded parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new MovementIndexAdded(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<MovementIndexAdded> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MovementIndexAdded> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.MovementIndexAdded getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

