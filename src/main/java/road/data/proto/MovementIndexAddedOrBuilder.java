// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface MovementIndexAddedOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.MovementIndexAdded)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *数据时间
   * </pre>
   *
   * <code>uint64 timestamp = 1;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   *可选，转向通行能力，0.01pcu/h。解释同车道通行能力
   * </pre>
   *
   * <code>uint32 movementCapacity = 2;</code>
   */
  int getMovementCapacity();

  /**
   * <pre>
   *可选，转向平均饱和度，0.01%
   * </pre>
   *
   * <code>uint32 movementSaturation = 3;</code>
   */
  int getMovementSaturation();

  /**
   * <pre>
   *可选，转向平均车道空间占有率，0.01%
   * </pre>
   *
   * <code>uint32 movementSpaceOccupy = 4;</code>
   */
  int getMovementSpaceOccupy();

  /**
   * <pre>
   *可选，转向平均车道时间占有率，0.01%
   * </pre>
   *
   * <code>uint32 movementTimeOccupy = 5;</code>
   */
  int getMovementTimeOccupy();

  /**
   * <pre>
   *可选，转向绿初车辆平均排队长度，0.01m
   * </pre>
   *
   * <code>uint32 movementAvgGrnQueue = 6;</code>
   */
  int getMovementAvgGrnQueue();

  /**
   * <pre>
   *可选，时段内转向平均绿灯利用率，0.01%
   * </pre>
   *
   * <code>uint32 movementGrnUtilization = 7;</code>
   */
  int getMovementGrnUtilization();
}
