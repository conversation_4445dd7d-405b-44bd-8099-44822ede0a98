// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface MovementOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.Movement)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  boolean hasRemoteIntersection();
  /**
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  road.data.proto.NodeReferenceId getRemoteIntersection();
  /**
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getRemoteIntersectionOrBuilder();

  /**
   * <pre>
   * 定义信号灯相位 ID，数值 0 表示无效 ID
   * </pre>
   *
   * <code>uint32 phaseId = 2;</code>
   */
  int getPhaseId();
}
