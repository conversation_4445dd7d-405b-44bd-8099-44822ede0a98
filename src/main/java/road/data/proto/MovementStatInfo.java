// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *转向对象  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.MovementStatInfo}
 */
public  final class MovementStatInfo extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.MovementStatInfo)
    MovementStatInfoOrBuilder {
private static final long serialVersionUID = 0L;
  // Use MovementStatInfo.newBuilder() to construct.
  private MovementStatInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private MovementStatInfo() {
    turnDirection_ = 0;
    extId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new MovementStatInfo();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private MovementStatInfo(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (remoteIntersection_ != null) {
              subBuilder = remoteIntersection_.toBuilder();
            }
            remoteIntersection_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(remoteIntersection_);
              remoteIntersection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            turnDirection_ = rawValue;
            break;
          }
          case 26: {
            road.data.proto.NodeStatInfo.Builder subBuilder = null;
            if (nodeStatInfo_ != null) {
              subBuilder = nodeStatInfo_.toBuilder();
            }
            nodeStatInfo_ = input.readMessage(road.data.proto.NodeStatInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(nodeStatInfo_);
              nodeStatInfo_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            extId_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MovementStatInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MovementStatInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.MovementStatInfo.class, road.data.proto.MovementStatInfo.Builder.class);
  }

  public static final int REMOTEINTERSECTION_FIELD_NUMBER = 1;
  private road.data.proto.NodeReferenceId remoteIntersection_;
  /**
   * <pre>
   *下游路口编号
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  public boolean hasRemoteIntersection() {
    return remoteIntersection_ != null;
  }
  /**
   * <pre>
   *下游路口编号
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  public road.data.proto.NodeReferenceId getRemoteIntersection() {
    return remoteIntersection_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : remoteIntersection_;
  }
  /**
   * <pre>
   *下游路口编号
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getRemoteIntersectionOrBuilder() {
    return getRemoteIntersection();
  }

  public static final int TURNDIRECTION_FIELD_NUMBER = 2;
  private int turnDirection_;
  /**
   * <pre>
   *转向信息 
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 2;</code>
   */
  public int getTurnDirectionValue() {
    return turnDirection_;
  }
  /**
   * <pre>
   *转向信息 
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 2;</code>
   */
  public road.data.proto.Maneuver getTurnDirection() {
    @SuppressWarnings("deprecation")
    road.data.proto.Maneuver result = road.data.proto.Maneuver.valueOf(turnDirection_);
    return result == null ? road.data.proto.Maneuver.UNRECOGNIZED : result;
  }

  public static final int NODESTATINFO_FIELD_NUMBER = 3;
  private road.data.proto.NodeStatInfo nodeStatInfo_;
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
   */
  public boolean hasNodeStatInfo() {
    return nodeStatInfo_ != null;
  }
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
   */
  public road.data.proto.NodeStatInfo getNodeStatInfo() {
    return nodeStatInfo_ == null ? road.data.proto.NodeStatInfo.getDefaultInstance() : nodeStatInfo_;
  }
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
   */
  public road.data.proto.NodeStatInfoOrBuilder getNodeStatInfoOrBuilder() {
    return getNodeStatInfo();
  }

  public static final int EXTID_FIELD_NUMBER = 4;
  private volatile java.lang.Object extId_;
  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 4;</code>
   */
  public java.lang.String getExtId() {
    java.lang.Object ref = extId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      extId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 4;</code>
   */
  public com.google.protobuf.ByteString
      getExtIdBytes() {
    java.lang.Object ref = extId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      extId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (remoteIntersection_ != null) {
      output.writeMessage(1, getRemoteIntersection());
    }
    if (turnDirection_ != road.data.proto.Maneuver.MANEUVER_STRAIGHT.getNumber()) {
      output.writeEnum(2, turnDirection_);
    }
    if (nodeStatInfo_ != null) {
      output.writeMessage(3, getNodeStatInfo());
    }
    if (!getExtIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, extId_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (remoteIntersection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getRemoteIntersection());
    }
    if (turnDirection_ != road.data.proto.Maneuver.MANEUVER_STRAIGHT.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, turnDirection_);
    }
    if (nodeStatInfo_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getNodeStatInfo());
    }
    if (!getExtIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, extId_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.MovementStatInfo)) {
      return super.equals(obj);
    }
    road.data.proto.MovementStatInfo other = (road.data.proto.MovementStatInfo) obj;

    if (hasRemoteIntersection() != other.hasRemoteIntersection()) return false;
    if (hasRemoteIntersection()) {
      if (!getRemoteIntersection()
          .equals(other.getRemoteIntersection())) return false;
    }
    if (turnDirection_ != other.turnDirection_) return false;
    if (hasNodeStatInfo() != other.hasNodeStatInfo()) return false;
    if (hasNodeStatInfo()) {
      if (!getNodeStatInfo()
          .equals(other.getNodeStatInfo())) return false;
    }
    if (!getExtId()
        .equals(other.getExtId())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRemoteIntersection()) {
      hash = (37 * hash) + REMOTEINTERSECTION_FIELD_NUMBER;
      hash = (53 * hash) + getRemoteIntersection().hashCode();
    }
    hash = (37 * hash) + TURNDIRECTION_FIELD_NUMBER;
    hash = (53 * hash) + turnDirection_;
    if (hasNodeStatInfo()) {
      hash = (37 * hash) + NODESTATINFO_FIELD_NUMBER;
      hash = (53 * hash) + getNodeStatInfo().hashCode();
    }
    hash = (37 * hash) + EXTID_FIELD_NUMBER;
    hash = (53 * hash) + getExtId().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.MovementStatInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MovementStatInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MovementStatInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MovementStatInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MovementStatInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.MovementStatInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.MovementStatInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MovementStatInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MovementStatInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.MovementStatInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.MovementStatInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.MovementStatInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.MovementStatInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *转向对象  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.MovementStatInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.MovementStatInfo)
      road.data.proto.MovementStatInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MovementStatInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MovementStatInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.MovementStatInfo.class, road.data.proto.MovementStatInfo.Builder.class);
    }

    // Construct using road.data.proto.MovementStatInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (remoteIntersectionBuilder_ == null) {
        remoteIntersection_ = null;
      } else {
        remoteIntersection_ = null;
        remoteIntersectionBuilder_ = null;
      }
      turnDirection_ = 0;

      if (nodeStatInfoBuilder_ == null) {
        nodeStatInfo_ = null;
      } else {
        nodeStatInfo_ = null;
        nodeStatInfoBuilder_ = null;
      }
      extId_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_MovementStatInfo_descriptor;
    }

    @java.lang.Override
    public road.data.proto.MovementStatInfo getDefaultInstanceForType() {
      return road.data.proto.MovementStatInfo.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.MovementStatInfo build() {
      road.data.proto.MovementStatInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.MovementStatInfo buildPartial() {
      road.data.proto.MovementStatInfo result = new road.data.proto.MovementStatInfo(this);
      if (remoteIntersectionBuilder_ == null) {
        result.remoteIntersection_ = remoteIntersection_;
      } else {
        result.remoteIntersection_ = remoteIntersectionBuilder_.build();
      }
      result.turnDirection_ = turnDirection_;
      if (nodeStatInfoBuilder_ == null) {
        result.nodeStatInfo_ = nodeStatInfo_;
      } else {
        result.nodeStatInfo_ = nodeStatInfoBuilder_.build();
      }
      result.extId_ = extId_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.MovementStatInfo) {
        return mergeFrom((road.data.proto.MovementStatInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.MovementStatInfo other) {
      if (other == road.data.proto.MovementStatInfo.getDefaultInstance()) return this;
      if (other.hasRemoteIntersection()) {
        mergeRemoteIntersection(other.getRemoteIntersection());
      }
      if (other.turnDirection_ != 0) {
        setTurnDirectionValue(other.getTurnDirectionValue());
      }
      if (other.hasNodeStatInfo()) {
        mergeNodeStatInfo(other.getNodeStatInfo());
      }
      if (!other.getExtId().isEmpty()) {
        extId_ = other.extId_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.MovementStatInfo parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.MovementStatInfo) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.NodeReferenceId remoteIntersection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> remoteIntersectionBuilder_;
    /**
     * <pre>
     *下游路口编号
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public boolean hasRemoteIntersection() {
      return remoteIntersectionBuilder_ != null || remoteIntersection_ != null;
    }
    /**
     * <pre>
     *下游路口编号
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public road.data.proto.NodeReferenceId getRemoteIntersection() {
      if (remoteIntersectionBuilder_ == null) {
        return remoteIntersection_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : remoteIntersection_;
      } else {
        return remoteIntersectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *下游路口编号
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public Builder setRemoteIntersection(road.data.proto.NodeReferenceId value) {
      if (remoteIntersectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        remoteIntersection_ = value;
        onChanged();
      } else {
        remoteIntersectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *下游路口编号
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public Builder setRemoteIntersection(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (remoteIntersectionBuilder_ == null) {
        remoteIntersection_ = builderForValue.build();
        onChanged();
      } else {
        remoteIntersectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *下游路口编号
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public Builder mergeRemoteIntersection(road.data.proto.NodeReferenceId value) {
      if (remoteIntersectionBuilder_ == null) {
        if (remoteIntersection_ != null) {
          remoteIntersection_ =
            road.data.proto.NodeReferenceId.newBuilder(remoteIntersection_).mergeFrom(value).buildPartial();
        } else {
          remoteIntersection_ = value;
        }
        onChanged();
      } else {
        remoteIntersectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *下游路口编号
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public Builder clearRemoteIntersection() {
      if (remoteIntersectionBuilder_ == null) {
        remoteIntersection_ = null;
        onChanged();
      } else {
        remoteIntersection_ = null;
        remoteIntersectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *下游路口编号
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getRemoteIntersectionBuilder() {
      
      onChanged();
      return getRemoteIntersectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *下游路口编号
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getRemoteIntersectionOrBuilder() {
      if (remoteIntersectionBuilder_ != null) {
        return remoteIntersectionBuilder_.getMessageOrBuilder();
      } else {
        return remoteIntersection_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : remoteIntersection_;
      }
    }
    /**
     * <pre>
     *下游路口编号
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getRemoteIntersectionFieldBuilder() {
      if (remoteIntersectionBuilder_ == null) {
        remoteIntersectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getRemoteIntersection(),
                getParentForChildren(),
                isClean());
        remoteIntersection_ = null;
      }
      return remoteIntersectionBuilder_;
    }

    private int turnDirection_ = 0;
    /**
     * <pre>
     *转向信息 
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 2;</code>
     */
    public int getTurnDirectionValue() {
      return turnDirection_;
    }
    /**
     * <pre>
     *转向信息 
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 2;</code>
     */
    public Builder setTurnDirectionValue(int value) {
      turnDirection_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *转向信息 
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 2;</code>
     */
    public road.data.proto.Maneuver getTurnDirection() {
      @SuppressWarnings("deprecation")
      road.data.proto.Maneuver result = road.data.proto.Maneuver.valueOf(turnDirection_);
      return result == null ? road.data.proto.Maneuver.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *转向信息 
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 2;</code>
     */
    public Builder setTurnDirection(road.data.proto.Maneuver value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      turnDirection_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *转向信息 
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 2;</code>
     */
    public Builder clearTurnDirection() {
      
      turnDirection_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.NodeStatInfo nodeStatInfo_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeStatInfo, road.data.proto.NodeStatInfo.Builder, road.data.proto.NodeStatInfoOrBuilder> nodeStatInfoBuilder_;
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public boolean hasNodeStatInfo() {
      return nodeStatInfoBuilder_ != null || nodeStatInfo_ != null;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public road.data.proto.NodeStatInfo getNodeStatInfo() {
      if (nodeStatInfoBuilder_ == null) {
        return nodeStatInfo_ == null ? road.data.proto.NodeStatInfo.getDefaultInstance() : nodeStatInfo_;
      } else {
        return nodeStatInfoBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public Builder setNodeStatInfo(road.data.proto.NodeStatInfo value) {
      if (nodeStatInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        nodeStatInfo_ = value;
        onChanged();
      } else {
        nodeStatInfoBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public Builder setNodeStatInfo(
        road.data.proto.NodeStatInfo.Builder builderForValue) {
      if (nodeStatInfoBuilder_ == null) {
        nodeStatInfo_ = builderForValue.build();
        onChanged();
      } else {
        nodeStatInfoBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public Builder mergeNodeStatInfo(road.data.proto.NodeStatInfo value) {
      if (nodeStatInfoBuilder_ == null) {
        if (nodeStatInfo_ != null) {
          nodeStatInfo_ =
            road.data.proto.NodeStatInfo.newBuilder(nodeStatInfo_).mergeFrom(value).buildPartial();
        } else {
          nodeStatInfo_ = value;
        }
        onChanged();
      } else {
        nodeStatInfoBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public Builder clearNodeStatInfo() {
      if (nodeStatInfoBuilder_ == null) {
        nodeStatInfo_ = null;
        onChanged();
      } else {
        nodeStatInfo_ = null;
        nodeStatInfoBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public road.data.proto.NodeStatInfo.Builder getNodeStatInfoBuilder() {
      
      onChanged();
      return getNodeStatInfoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    public road.data.proto.NodeStatInfoOrBuilder getNodeStatInfoOrBuilder() {
      if (nodeStatInfoBuilder_ != null) {
        return nodeStatInfoBuilder_.getMessageOrBuilder();
      } else {
        return nodeStatInfo_ == null ?
            road.data.proto.NodeStatInfo.getDefaultInstance() : nodeStatInfo_;
      }
    }
    /**
     * <pre>
     *本路口id，与TrafficFlow中nodeId相同
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeStatInfo, road.data.proto.NodeStatInfo.Builder, road.data.proto.NodeStatInfoOrBuilder> 
        getNodeStatInfoFieldBuilder() {
      if (nodeStatInfoBuilder_ == null) {
        nodeStatInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeStatInfo, road.data.proto.NodeStatInfo.Builder, road.data.proto.NodeStatInfoOrBuilder>(
                getNodeStatInfo(),
                getParentForChildren(),
                isClean());
        nodeStatInfo_ = null;
      }
      return nodeStatInfoBuilder_;
    }

    private java.lang.Object extId_ = "";
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 4;</code>
     */
    public java.lang.String getExtId() {
      java.lang.Object ref = extId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        extId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 4;</code>
     */
    public com.google.protobuf.ByteString
        getExtIdBytes() {
      java.lang.Object ref = extId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 4;</code>
     */
    public Builder setExtId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      extId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 4;</code>
     */
    public Builder clearExtId() {
      
      extId_ = getDefaultInstance().getExtId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 4;</code>
     */
    public Builder setExtIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      extId_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.MovementStatInfo)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.MovementStatInfo)
  private static final road.data.proto.MovementStatInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.MovementStatInfo();
  }

  public static road.data.proto.MovementStatInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MovementStatInfo>
      PARSER = new com.google.protobuf.AbstractParser<MovementStatInfo>() {
    @java.lang.Override
    public MovementStatInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new MovementStatInfo(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<MovementStatInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MovementStatInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.MovementStatInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

