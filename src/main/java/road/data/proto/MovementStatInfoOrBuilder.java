// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface MovementStatInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.MovementStatInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *下游路口编号
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  boolean hasRemoteIntersection();
  /**
   * <pre>
   *下游路口编号
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  road.data.proto.NodeReferenceId getRemoteIntersection();
  /**
   * <pre>
   *下游路口编号
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId remoteIntersection = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getRemoteIntersectionOrBuilder();

  /**
   * <pre>
   *转向信息 
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 2;</code>
   */
  int getTurnDirectionValue();
  /**
   * <pre>
   *转向信息 
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Maneuver turnDirection = 2;</code>
   */
  road.data.proto.Maneuver getTurnDirection();

  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
   */
  boolean hasNodeStatInfo();
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
   */
  road.data.proto.NodeStatInfo getNodeStatInfo();
  /**
   * <pre>
   *本路口id，与TrafficFlow中nodeId相同
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 3;</code>
   */
  road.data.proto.NodeStatInfoOrBuilder getNodeStatInfoOrBuilder();

  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 4;</code>
   */
  java.lang.String getExtId();
  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 4;</code>
   */
  com.google.protobuf.ByteString
      getExtIdBytes();
}
