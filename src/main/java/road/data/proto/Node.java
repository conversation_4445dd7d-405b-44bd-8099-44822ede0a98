// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *节点 交叉口     
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.Node}
 */
public  final class Node extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.Node)
    NodeOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Node.newBuilder() to construct.
  private Node(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Node() {
    name_ = "";
    inLinks_ = java.util.Collections.emptyList();
    inLinksEx_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Node();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Node(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 18: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (id_ != null) {
              subBuilder = id_.toBuilder();
            }
            id_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(id_);
              id_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (refPos_ != null) {
              subBuilder = refPos_.toBuilder();
            }
            refPos_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(refPos_);
              refPos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              inLinks_ = new java.util.ArrayList<road.data.proto.Link>();
              mutable_bitField0_ |= 0x00000001;
            }
            inLinks_.add(
                input.readMessage(road.data.proto.Link.parser(), extensionRegistry));
            break;
          }
          case 42: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              inLinksEx_ = new java.util.ArrayList<road.data.proto.LinkEx>();
              mutable_bitField0_ |= 0x00000002;
            }
            inLinksEx_.add(
                input.readMessage(road.data.proto.LinkEx.parser(), extensionRegistry));
            break;
          }
          case 50: {
            road.data.proto.ProhibitedZone.Builder subBuilder = null;
            if (prohibitedZone_ != null) {
              subBuilder = prohibitedZone_.toBuilder();
            }
            prohibitedZone_ = input.readMessage(road.data.proto.ProhibitedZone.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(prohibitedZone_);
              prohibitedZone_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        inLinks_ = java.util.Collections.unmodifiableList(inLinks_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        inLinksEx_ = java.util.Collections.unmodifiableList(inLinksEx_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Node_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Node_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.Node.class, road.data.proto.Node.Builder.class);
  }

  public static final int NAME_FIELD_NUMBER = 1;
  private volatile java.lang.Object name_;
  /**
   * <pre>
   * 交叉口名称
   * </pre>
   *
   * <code>string name = 1;</code>
   */
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 交叉口名称
   * </pre>
   *
   * <code>string name = 1;</code>
   */
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ID_FIELD_NUMBER = 2;
  private road.data.proto.NodeReferenceId id_;
  /**
   * <pre>
   * 交叉口ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
   */
  public boolean hasId() {
    return id_ != null;
  }
  /**
   * <pre>
   * 交叉口ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
   */
  public road.data.proto.NodeReferenceId getId() {
    return id_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : id_;
  }
  /**
   * <pre>
   * 交叉口ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getIdOrBuilder() {
    return getId();
  }

  public static final int REFPOS_FIELD_NUMBER = 3;
  private road.data.proto.Position3D refPos_;
  /**
   * <pre>
   * 交叉口位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
   */
  public boolean hasRefPos() {
    return refPos_ != null;
  }
  /**
   * <pre>
   * 交叉口位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
   */
  public road.data.proto.Position3D getRefPos() {
    return refPos_ == null ? road.data.proto.Position3D.getDefaultInstance() : refPos_;
  }
  /**
   * <pre>
   * 交叉口位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
   */
  public road.data.proto.Position3DOrBuilder getRefPosOrBuilder() {
    return getRefPos();
  }

  public static final int INLINKS_FIELD_NUMBER = 4;
  private java.util.List<road.data.proto.Link> inLinks_;
  /**
   * <pre>
   * 可选，交叉口所在路段
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
   */
  public java.util.List<road.data.proto.Link> getInLinksList() {
    return inLinks_;
  }
  /**
   * <pre>
   * 可选，交叉口所在路段
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
   */
  public java.util.List<? extends road.data.proto.LinkOrBuilder> 
      getInLinksOrBuilderList() {
    return inLinks_;
  }
  /**
   * <pre>
   * 可选，交叉口所在路段
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
   */
  public int getInLinksCount() {
    return inLinks_.size();
  }
  /**
   * <pre>
   * 可选，交叉口所在路段
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
   */
  public road.data.proto.Link getInLinks(int index) {
    return inLinks_.get(index);
  }
  /**
   * <pre>
   * 可选，交叉口所在路段
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
   */
  public road.data.proto.LinkOrBuilder getInLinksOrBuilder(
      int index) {
    return inLinks_.get(index);
  }

  public static final int INLINKSEX_FIELD_NUMBER = 5;
  private java.util.List<road.data.proto.LinkEx> inLinksEx_;
  /**
   * <pre>
   *可选，交叉口所在路段扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
   */
  public java.util.List<road.data.proto.LinkEx> getInLinksExList() {
    return inLinksEx_;
  }
  /**
   * <pre>
   *可选，交叉口所在路段扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
   */
  public java.util.List<? extends road.data.proto.LinkExOrBuilder> 
      getInLinksExOrBuilderList() {
    return inLinksEx_;
  }
  /**
   * <pre>
   *可选，交叉口所在路段扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
   */
  public int getInLinksExCount() {
    return inLinksEx_.size();
  }
  /**
   * <pre>
   *可选，交叉口所在路段扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
   */
  public road.data.proto.LinkEx getInLinksEx(int index) {
    return inLinksEx_.get(index);
  }
  /**
   * <pre>
   *可选，交叉口所在路段扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
   */
  public road.data.proto.LinkExOrBuilder getInLinksExOrBuilder(
      int index) {
    return inLinksEx_.get(index);
  }

  public static final int PROHIBITEDZONE_FIELD_NUMBER = 6;
  private road.data.proto.ProhibitedZone prohibitedZone_;
  /**
   * <pre>
   *可选，交叉口内的禁停区域
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
   */
  public boolean hasProhibitedZone() {
    return prohibitedZone_ != null;
  }
  /**
   * <pre>
   *可选，交叉口内的禁停区域
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
   */
  public road.data.proto.ProhibitedZone getProhibitedZone() {
    return prohibitedZone_ == null ? road.data.proto.ProhibitedZone.getDefaultInstance() : prohibitedZone_;
  }
  /**
   * <pre>
   *可选，交叉口内的禁停区域
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
   */
  public road.data.proto.ProhibitedZoneOrBuilder getProhibitedZoneOrBuilder() {
    return getProhibitedZone();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
    }
    if (id_ != null) {
      output.writeMessage(2, getId());
    }
    if (refPos_ != null) {
      output.writeMessage(3, getRefPos());
    }
    for (int i = 0; i < inLinks_.size(); i++) {
      output.writeMessage(4, inLinks_.get(i));
    }
    for (int i = 0; i < inLinksEx_.size(); i++) {
      output.writeMessage(5, inLinksEx_.get(i));
    }
    if (prohibitedZone_ != null) {
      output.writeMessage(6, getProhibitedZone());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
    }
    if (id_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getId());
    }
    if (refPos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getRefPos());
    }
    for (int i = 0; i < inLinks_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, inLinks_.get(i));
    }
    for (int i = 0; i < inLinksEx_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, inLinksEx_.get(i));
    }
    if (prohibitedZone_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getProhibitedZone());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.Node)) {
      return super.equals(obj);
    }
    road.data.proto.Node other = (road.data.proto.Node) obj;

    if (!getName()
        .equals(other.getName())) return false;
    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (!getId()
          .equals(other.getId())) return false;
    }
    if (hasRefPos() != other.hasRefPos()) return false;
    if (hasRefPos()) {
      if (!getRefPos()
          .equals(other.getRefPos())) return false;
    }
    if (!getInLinksList()
        .equals(other.getInLinksList())) return false;
    if (!getInLinksExList()
        .equals(other.getInLinksExList())) return false;
    if (hasProhibitedZone() != other.hasProhibitedZone()) return false;
    if (hasProhibitedZone()) {
      if (!getProhibitedZone()
          .equals(other.getProhibitedZone())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId().hashCode();
    }
    if (hasRefPos()) {
      hash = (37 * hash) + REFPOS_FIELD_NUMBER;
      hash = (53 * hash) + getRefPos().hashCode();
    }
    if (getInLinksCount() > 0) {
      hash = (37 * hash) + INLINKS_FIELD_NUMBER;
      hash = (53 * hash) + getInLinksList().hashCode();
    }
    if (getInLinksExCount() > 0) {
      hash = (37 * hash) + INLINKSEX_FIELD_NUMBER;
      hash = (53 * hash) + getInLinksExList().hashCode();
    }
    if (hasProhibitedZone()) {
      hash = (37 * hash) + PROHIBITEDZONE_FIELD_NUMBER;
      hash = (53 * hash) + getProhibitedZone().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.Node parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Node parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Node parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Node parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Node parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Node parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Node parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.Node parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.Node parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.Node parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.Node parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.Node parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.Node prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *节点 交叉口     
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.Node}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.Node)
      road.data.proto.NodeOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Node_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Node_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.Node.class, road.data.proto.Node.Builder.class);
    }

    // Construct using road.data.proto.Node.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getInLinksFieldBuilder();
        getInLinksExFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      name_ = "";

      if (idBuilder_ == null) {
        id_ = null;
      } else {
        id_ = null;
        idBuilder_ = null;
      }
      if (refPosBuilder_ == null) {
        refPos_ = null;
      } else {
        refPos_ = null;
        refPosBuilder_ = null;
      }
      if (inLinksBuilder_ == null) {
        inLinks_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        inLinksBuilder_.clear();
      }
      if (inLinksExBuilder_ == null) {
        inLinksEx_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        inLinksExBuilder_.clear();
      }
      if (prohibitedZoneBuilder_ == null) {
        prohibitedZone_ = null;
      } else {
        prohibitedZone_ = null;
        prohibitedZoneBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Node_descriptor;
    }

    @java.lang.Override
    public road.data.proto.Node getDefaultInstanceForType() {
      return road.data.proto.Node.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.Node build() {
      road.data.proto.Node result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.Node buildPartial() {
      road.data.proto.Node result = new road.data.proto.Node(this);
      int from_bitField0_ = bitField0_;
      result.name_ = name_;
      if (idBuilder_ == null) {
        result.id_ = id_;
      } else {
        result.id_ = idBuilder_.build();
      }
      if (refPosBuilder_ == null) {
        result.refPos_ = refPos_;
      } else {
        result.refPos_ = refPosBuilder_.build();
      }
      if (inLinksBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          inLinks_ = java.util.Collections.unmodifiableList(inLinks_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.inLinks_ = inLinks_;
      } else {
        result.inLinks_ = inLinksBuilder_.build();
      }
      if (inLinksExBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          inLinksEx_ = java.util.Collections.unmodifiableList(inLinksEx_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.inLinksEx_ = inLinksEx_;
      } else {
        result.inLinksEx_ = inLinksExBuilder_.build();
      }
      if (prohibitedZoneBuilder_ == null) {
        result.prohibitedZone_ = prohibitedZone_;
      } else {
        result.prohibitedZone_ = prohibitedZoneBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.Node) {
        return mergeFrom((road.data.proto.Node)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.Node other) {
      if (other == road.data.proto.Node.getDefaultInstance()) return this;
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (other.hasId()) {
        mergeId(other.getId());
      }
      if (other.hasRefPos()) {
        mergeRefPos(other.getRefPos());
      }
      if (inLinksBuilder_ == null) {
        if (!other.inLinks_.isEmpty()) {
          if (inLinks_.isEmpty()) {
            inLinks_ = other.inLinks_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureInLinksIsMutable();
            inLinks_.addAll(other.inLinks_);
          }
          onChanged();
        }
      } else {
        if (!other.inLinks_.isEmpty()) {
          if (inLinksBuilder_.isEmpty()) {
            inLinksBuilder_.dispose();
            inLinksBuilder_ = null;
            inLinks_ = other.inLinks_;
            bitField0_ = (bitField0_ & ~0x00000001);
            inLinksBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getInLinksFieldBuilder() : null;
          } else {
            inLinksBuilder_.addAllMessages(other.inLinks_);
          }
        }
      }
      if (inLinksExBuilder_ == null) {
        if (!other.inLinksEx_.isEmpty()) {
          if (inLinksEx_.isEmpty()) {
            inLinksEx_ = other.inLinksEx_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureInLinksExIsMutable();
            inLinksEx_.addAll(other.inLinksEx_);
          }
          onChanged();
        }
      } else {
        if (!other.inLinksEx_.isEmpty()) {
          if (inLinksExBuilder_.isEmpty()) {
            inLinksExBuilder_.dispose();
            inLinksExBuilder_ = null;
            inLinksEx_ = other.inLinksEx_;
            bitField0_ = (bitField0_ & ~0x00000002);
            inLinksExBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getInLinksExFieldBuilder() : null;
          } else {
            inLinksExBuilder_.addAllMessages(other.inLinksEx_);
          }
        }
      }
      if (other.hasProhibitedZone()) {
        mergeProhibitedZone(other.getProhibitedZone());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.Node parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.Node) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object name_ = "";
    /**
     * <pre>
     * 交叉口名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 交叉口名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 交叉口名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 交叉口名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 交叉口名称
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private road.data.proto.NodeReferenceId id_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> idBuilder_;
    /**
     * <pre>
     * 交叉口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
     */
    public boolean hasId() {
      return idBuilder_ != null || id_ != null;
    }
    /**
     * <pre>
     * 交叉口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
     */
    public road.data.proto.NodeReferenceId getId() {
      if (idBuilder_ == null) {
        return id_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : id_;
      } else {
        return idBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 交叉口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
     */
    public Builder setId(road.data.proto.NodeReferenceId value) {
      if (idBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        id_ = value;
        onChanged();
      } else {
        idBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 交叉口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
     */
    public Builder setId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (idBuilder_ == null) {
        id_ = builderForValue.build();
        onChanged();
      } else {
        idBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 交叉口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
     */
    public Builder mergeId(road.data.proto.NodeReferenceId value) {
      if (idBuilder_ == null) {
        if (id_ != null) {
          id_ =
            road.data.proto.NodeReferenceId.newBuilder(id_).mergeFrom(value).buildPartial();
        } else {
          id_ = value;
        }
        onChanged();
      } else {
        idBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 交叉口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
     */
    public Builder clearId() {
      if (idBuilder_ == null) {
        id_ = null;
        onChanged();
      } else {
        id_ = null;
        idBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 交叉口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getIdBuilder() {
      
      onChanged();
      return getIdFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 交叉口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getIdOrBuilder() {
      if (idBuilder_ != null) {
        return idBuilder_.getMessageOrBuilder();
      } else {
        return id_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : id_;
      }
    }
    /**
     * <pre>
     * 交叉口ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getIdFieldBuilder() {
      if (idBuilder_ == null) {
        idBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getId(),
                getParentForChildren(),
                isClean());
        id_ = null;
      }
      return idBuilder_;
    }

    private road.data.proto.Position3D refPos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> refPosBuilder_;
    /**
     * <pre>
     * 交叉口位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
     */
    public boolean hasRefPos() {
      return refPosBuilder_ != null || refPos_ != null;
    }
    /**
     * <pre>
     * 交叉口位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
     */
    public road.data.proto.Position3D getRefPos() {
      if (refPosBuilder_ == null) {
        return refPos_ == null ? road.data.proto.Position3D.getDefaultInstance() : refPos_;
      } else {
        return refPosBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 交叉口位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
     */
    public Builder setRefPos(road.data.proto.Position3D value) {
      if (refPosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        refPos_ = value;
        onChanged();
      } else {
        refPosBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 交叉口位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
     */
    public Builder setRefPos(
        road.data.proto.Position3D.Builder builderForValue) {
      if (refPosBuilder_ == null) {
        refPos_ = builderForValue.build();
        onChanged();
      } else {
        refPosBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 交叉口位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
     */
    public Builder mergeRefPos(road.data.proto.Position3D value) {
      if (refPosBuilder_ == null) {
        if (refPos_ != null) {
          refPos_ =
            road.data.proto.Position3D.newBuilder(refPos_).mergeFrom(value).buildPartial();
        } else {
          refPos_ = value;
        }
        onChanged();
      } else {
        refPosBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 交叉口位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
     */
    public Builder clearRefPos() {
      if (refPosBuilder_ == null) {
        refPos_ = null;
        onChanged();
      } else {
        refPos_ = null;
        refPosBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 交叉口位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
     */
    public road.data.proto.Position3D.Builder getRefPosBuilder() {
      
      onChanged();
      return getRefPosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 交叉口位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
     */
    public road.data.proto.Position3DOrBuilder getRefPosOrBuilder() {
      if (refPosBuilder_ != null) {
        return refPosBuilder_.getMessageOrBuilder();
      } else {
        return refPos_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : refPos_;
      }
    }
    /**
     * <pre>
     * 交叉口位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getRefPosFieldBuilder() {
      if (refPosBuilder_ == null) {
        refPosBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getRefPos(),
                getParentForChildren(),
                isClean());
        refPos_ = null;
      }
      return refPosBuilder_;
    }

    private java.util.List<road.data.proto.Link> inLinks_ =
      java.util.Collections.emptyList();
    private void ensureInLinksIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        inLinks_ = new java.util.ArrayList<road.data.proto.Link>(inLinks_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Link, road.data.proto.Link.Builder, road.data.proto.LinkOrBuilder> inLinksBuilder_;

    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public java.util.List<road.data.proto.Link> getInLinksList() {
      if (inLinksBuilder_ == null) {
        return java.util.Collections.unmodifiableList(inLinks_);
      } else {
        return inLinksBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public int getInLinksCount() {
      if (inLinksBuilder_ == null) {
        return inLinks_.size();
      } else {
        return inLinksBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public road.data.proto.Link getInLinks(int index) {
      if (inLinksBuilder_ == null) {
        return inLinks_.get(index);
      } else {
        return inLinksBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public Builder setInLinks(
        int index, road.data.proto.Link value) {
      if (inLinksBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInLinksIsMutable();
        inLinks_.set(index, value);
        onChanged();
      } else {
        inLinksBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public Builder setInLinks(
        int index, road.data.proto.Link.Builder builderForValue) {
      if (inLinksBuilder_ == null) {
        ensureInLinksIsMutable();
        inLinks_.set(index, builderForValue.build());
        onChanged();
      } else {
        inLinksBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public Builder addInLinks(road.data.proto.Link value) {
      if (inLinksBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInLinksIsMutable();
        inLinks_.add(value);
        onChanged();
      } else {
        inLinksBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public Builder addInLinks(
        int index, road.data.proto.Link value) {
      if (inLinksBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInLinksIsMutable();
        inLinks_.add(index, value);
        onChanged();
      } else {
        inLinksBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public Builder addInLinks(
        road.data.proto.Link.Builder builderForValue) {
      if (inLinksBuilder_ == null) {
        ensureInLinksIsMutable();
        inLinks_.add(builderForValue.build());
        onChanged();
      } else {
        inLinksBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public Builder addInLinks(
        int index, road.data.proto.Link.Builder builderForValue) {
      if (inLinksBuilder_ == null) {
        ensureInLinksIsMutable();
        inLinks_.add(index, builderForValue.build());
        onChanged();
      } else {
        inLinksBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public Builder addAllInLinks(
        java.lang.Iterable<? extends road.data.proto.Link> values) {
      if (inLinksBuilder_ == null) {
        ensureInLinksIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, inLinks_);
        onChanged();
      } else {
        inLinksBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public Builder clearInLinks() {
      if (inLinksBuilder_ == null) {
        inLinks_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        inLinksBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public Builder removeInLinks(int index) {
      if (inLinksBuilder_ == null) {
        ensureInLinksIsMutable();
        inLinks_.remove(index);
        onChanged();
      } else {
        inLinksBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public road.data.proto.Link.Builder getInLinksBuilder(
        int index) {
      return getInLinksFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public road.data.proto.LinkOrBuilder getInLinksOrBuilder(
        int index) {
      if (inLinksBuilder_ == null) {
        return inLinks_.get(index);  } else {
        return inLinksBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public java.util.List<? extends road.data.proto.LinkOrBuilder> 
         getInLinksOrBuilderList() {
      if (inLinksBuilder_ != null) {
        return inLinksBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(inLinks_);
      }
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public road.data.proto.Link.Builder addInLinksBuilder() {
      return getInLinksFieldBuilder().addBuilder(
          road.data.proto.Link.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public road.data.proto.Link.Builder addInLinksBuilder(
        int index) {
      return getInLinksFieldBuilder().addBuilder(
          index, road.data.proto.Link.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，交叉口所在路段
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
     */
    public java.util.List<road.data.proto.Link.Builder> 
         getInLinksBuilderList() {
      return getInLinksFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Link, road.data.proto.Link.Builder, road.data.proto.LinkOrBuilder> 
        getInLinksFieldBuilder() {
      if (inLinksBuilder_ == null) {
        inLinksBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.Link, road.data.proto.Link.Builder, road.data.proto.LinkOrBuilder>(
                inLinks_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        inLinks_ = null;
      }
      return inLinksBuilder_;
    }

    private java.util.List<road.data.proto.LinkEx> inLinksEx_ =
      java.util.Collections.emptyList();
    private void ensureInLinksExIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        inLinksEx_ = new java.util.ArrayList<road.data.proto.LinkEx>(inLinksEx_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.LinkEx, road.data.proto.LinkEx.Builder, road.data.proto.LinkExOrBuilder> inLinksExBuilder_;

    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public java.util.List<road.data.proto.LinkEx> getInLinksExList() {
      if (inLinksExBuilder_ == null) {
        return java.util.Collections.unmodifiableList(inLinksEx_);
      } else {
        return inLinksExBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public int getInLinksExCount() {
      if (inLinksExBuilder_ == null) {
        return inLinksEx_.size();
      } else {
        return inLinksExBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public road.data.proto.LinkEx getInLinksEx(int index) {
      if (inLinksExBuilder_ == null) {
        return inLinksEx_.get(index);
      } else {
        return inLinksExBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public Builder setInLinksEx(
        int index, road.data.proto.LinkEx value) {
      if (inLinksExBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInLinksExIsMutable();
        inLinksEx_.set(index, value);
        onChanged();
      } else {
        inLinksExBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public Builder setInLinksEx(
        int index, road.data.proto.LinkEx.Builder builderForValue) {
      if (inLinksExBuilder_ == null) {
        ensureInLinksExIsMutable();
        inLinksEx_.set(index, builderForValue.build());
        onChanged();
      } else {
        inLinksExBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public Builder addInLinksEx(road.data.proto.LinkEx value) {
      if (inLinksExBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInLinksExIsMutable();
        inLinksEx_.add(value);
        onChanged();
      } else {
        inLinksExBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public Builder addInLinksEx(
        int index, road.data.proto.LinkEx value) {
      if (inLinksExBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInLinksExIsMutable();
        inLinksEx_.add(index, value);
        onChanged();
      } else {
        inLinksExBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public Builder addInLinksEx(
        road.data.proto.LinkEx.Builder builderForValue) {
      if (inLinksExBuilder_ == null) {
        ensureInLinksExIsMutable();
        inLinksEx_.add(builderForValue.build());
        onChanged();
      } else {
        inLinksExBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public Builder addInLinksEx(
        int index, road.data.proto.LinkEx.Builder builderForValue) {
      if (inLinksExBuilder_ == null) {
        ensureInLinksExIsMutable();
        inLinksEx_.add(index, builderForValue.build());
        onChanged();
      } else {
        inLinksExBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public Builder addAllInLinksEx(
        java.lang.Iterable<? extends road.data.proto.LinkEx> values) {
      if (inLinksExBuilder_ == null) {
        ensureInLinksExIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, inLinksEx_);
        onChanged();
      } else {
        inLinksExBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public Builder clearInLinksEx() {
      if (inLinksExBuilder_ == null) {
        inLinksEx_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        inLinksExBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public Builder removeInLinksEx(int index) {
      if (inLinksExBuilder_ == null) {
        ensureInLinksExIsMutable();
        inLinksEx_.remove(index);
        onChanged();
      } else {
        inLinksExBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public road.data.proto.LinkEx.Builder getInLinksExBuilder(
        int index) {
      return getInLinksExFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public road.data.proto.LinkExOrBuilder getInLinksExOrBuilder(
        int index) {
      if (inLinksExBuilder_ == null) {
        return inLinksEx_.get(index);  } else {
        return inLinksExBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public java.util.List<? extends road.data.proto.LinkExOrBuilder> 
         getInLinksExOrBuilderList() {
      if (inLinksExBuilder_ != null) {
        return inLinksExBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(inLinksEx_);
      }
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public road.data.proto.LinkEx.Builder addInLinksExBuilder() {
      return getInLinksExFieldBuilder().addBuilder(
          road.data.proto.LinkEx.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public road.data.proto.LinkEx.Builder addInLinksExBuilder(
        int index) {
      return getInLinksExFieldBuilder().addBuilder(
          index, road.data.proto.LinkEx.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，交叉口所在路段扩展信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
     */
    public java.util.List<road.data.proto.LinkEx.Builder> 
         getInLinksExBuilderList() {
      return getInLinksExFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.LinkEx, road.data.proto.LinkEx.Builder, road.data.proto.LinkExOrBuilder> 
        getInLinksExFieldBuilder() {
      if (inLinksExBuilder_ == null) {
        inLinksExBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.LinkEx, road.data.proto.LinkEx.Builder, road.data.proto.LinkExOrBuilder>(
                inLinksEx_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        inLinksEx_ = null;
      }
      return inLinksExBuilder_;
    }

    private road.data.proto.ProhibitedZone prohibitedZone_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ProhibitedZone, road.data.proto.ProhibitedZone.Builder, road.data.proto.ProhibitedZoneOrBuilder> prohibitedZoneBuilder_;
    /**
     * <pre>
     *可选，交叉口内的禁停区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
     */
    public boolean hasProhibitedZone() {
      return prohibitedZoneBuilder_ != null || prohibitedZone_ != null;
    }
    /**
     * <pre>
     *可选，交叉口内的禁停区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
     */
    public road.data.proto.ProhibitedZone getProhibitedZone() {
      if (prohibitedZoneBuilder_ == null) {
        return prohibitedZone_ == null ? road.data.proto.ProhibitedZone.getDefaultInstance() : prohibitedZone_;
      } else {
        return prohibitedZoneBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，交叉口内的禁停区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
     */
    public Builder setProhibitedZone(road.data.proto.ProhibitedZone value) {
      if (prohibitedZoneBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        prohibitedZone_ = value;
        onChanged();
      } else {
        prohibitedZoneBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，交叉口内的禁停区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
     */
    public Builder setProhibitedZone(
        road.data.proto.ProhibitedZone.Builder builderForValue) {
      if (prohibitedZoneBuilder_ == null) {
        prohibitedZone_ = builderForValue.build();
        onChanged();
      } else {
        prohibitedZoneBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，交叉口内的禁停区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
     */
    public Builder mergeProhibitedZone(road.data.proto.ProhibitedZone value) {
      if (prohibitedZoneBuilder_ == null) {
        if (prohibitedZone_ != null) {
          prohibitedZone_ =
            road.data.proto.ProhibitedZone.newBuilder(prohibitedZone_).mergeFrom(value).buildPartial();
        } else {
          prohibitedZone_ = value;
        }
        onChanged();
      } else {
        prohibitedZoneBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，交叉口内的禁停区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
     */
    public Builder clearProhibitedZone() {
      if (prohibitedZoneBuilder_ == null) {
        prohibitedZone_ = null;
        onChanged();
      } else {
        prohibitedZone_ = null;
        prohibitedZoneBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，交叉口内的禁停区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
     */
    public road.data.proto.ProhibitedZone.Builder getProhibitedZoneBuilder() {
      
      onChanged();
      return getProhibitedZoneFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，交叉口内的禁停区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
     */
    public road.data.proto.ProhibitedZoneOrBuilder getProhibitedZoneOrBuilder() {
      if (prohibitedZoneBuilder_ != null) {
        return prohibitedZoneBuilder_.getMessageOrBuilder();
      } else {
        return prohibitedZone_ == null ?
            road.data.proto.ProhibitedZone.getDefaultInstance() : prohibitedZone_;
      }
    }
    /**
     * <pre>
     *可选，交叉口内的禁停区域
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ProhibitedZone, road.data.proto.ProhibitedZone.Builder, road.data.proto.ProhibitedZoneOrBuilder> 
        getProhibitedZoneFieldBuilder() {
      if (prohibitedZoneBuilder_ == null) {
        prohibitedZoneBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ProhibitedZone, road.data.proto.ProhibitedZone.Builder, road.data.proto.ProhibitedZoneOrBuilder>(
                getProhibitedZone(),
                getParentForChildren(),
                isClean());
        prohibitedZone_ = null;
      }
      return prohibitedZoneBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.Node)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.Node)
  private static final road.data.proto.Node DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.Node();
  }

  public static road.data.proto.Node getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Node>
      PARSER = new com.google.protobuf.AbstractParser<Node>() {
    @java.lang.Override
    public Node parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Node(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Node> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Node> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.Node getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

