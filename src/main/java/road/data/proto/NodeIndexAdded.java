// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *路口级指标  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.NodeIndexAdded}
 */
public  final class NodeIndexAdded extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.NodeIndexAdded)
    NodeIndexAddedOrBuilder {
private static final long serialVersionUID = 0L;
  // Use NodeIndexAdded.newBuilder() to construct.
  private NodeIndexAdded(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private NodeIndexAdded() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new NodeIndexAdded();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private NodeIndexAdded(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 16: {

            nodeSpaceOccupy_ = input.readUInt32();
            break;
          }
          case 24: {

            nodeTimeOccupy_ = input.readUInt32();
            break;
          }
          case 32: {

            nodeCapacity_ = input.readUInt64();
            break;
          }
          case 40: {

            nodeSaturation_ = input.readUInt32();
            break;
          }
          case 48: {

            nodeGrnUtilization_ = input.readUInt32();
            break;
          }
          case 56: {

            nodeAvgGrnQueue_ = input.readUInt32();
            break;
          }
          case 64: {

            demandIndex_ = input.readUInt32();
            break;
          }
          case 72: {

            supplyIndex_ = input.readUInt32();
            break;
          }
          case 80: {

            theoryIndex_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeIndexAdded_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeIndexAdded_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.NodeIndexAdded.class, road.data.proto.NodeIndexAdded.Builder.class);
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 1;
  private long timestamp_;
  /**
   * <pre>
   *数据时间
   * </pre>
   *
   * <code>uint64 timestamp = 1;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int NODESPACEOCCUPY_FIELD_NUMBER = 2;
  private int nodeSpaceOccupy_;
  /**
   * <pre>
   *可选，平均车道空间占有率取平均，0.01%
   * </pre>
   *
   * <code>uint32 nodeSpaceOccupy = 2;</code>
   */
  public int getNodeSpaceOccupy() {
    return nodeSpaceOccupy_;
  }

  public static final int NODETIMEOCCUPY_FIELD_NUMBER = 3;
  private int nodeTimeOccupy_;
  /**
   * <pre>
   *可选，平均车道时间占有率取平均，0.01%
   * </pre>
   *
   * <code>uint32 nodeTimeOccupy = 3;</code>
   */
  public int getNodeTimeOccupy() {
    return nodeTimeOccupy_;
  }

  public static final int NODECAPACITY_FIELD_NUMBER = 4;
  private long nodeCapacity_;
  /**
   * <pre>
   *可选，交叉口通行能力，0.01pcu/h
   * </pre>
   *
   * <code>uint64 nodeCapacity = 4;</code>
   */
  public long getNodeCapacity() {
    return nodeCapacity_;
  }

  public static final int NODESATURATION_FIELD_NUMBER = 5;
  private int nodeSaturation_;
  /**
   * <pre>
   *可选，交叉口平均饱和度，0.01%
   * </pre>
   *
   * <code>uint32 nodeSaturation = 5;</code>
   */
  public int getNodeSaturation() {
    return nodeSaturation_;
  }

  public static final int NODEGRNUTILIZATION_FIELD_NUMBER = 6;
  private int nodeGrnUtilization_;
  /**
   * <pre>
   *可选，时段内交叉口红绿灯利用率，0.01%
   * </pre>
   *
   * <code>uint32 nodeGrnUtilization = 6;</code>
   */
  public int getNodeGrnUtilization() {
    return nodeGrnUtilization_;
  }

  public static final int NODEAVGGRNQUEUE_FIELD_NUMBER = 7;
  private int nodeAvgGrnQueue_;
  /**
   * <pre>
   *可选，交叉口绿初车辆平均排队长度，0.01m
   * </pre>
   *
   * <code>uint32 nodeAvgGrnQueue = 7;</code>
   */
  public int getNodeAvgGrnQueue() {
    return nodeAvgGrnQueue_;
  }

  public static final int DEMANDINDEX_FIELD_NUMBER = 8;
  private int demandIndex_;
  /**
   * <pre>
   *可选，路口需求指数，0.01pcu/h。
   * </pre>
   *
   * <code>uint32 demandIndex = 8;</code>
   */
  public int getDemandIndex() {
    return demandIndex_;
  }

  public static final int SUPPLYINDEX_FIELD_NUMBER = 9;
  private int supplyIndex_;
  /**
   * <pre>
   *可选，路口供给指数，0.01pcu/h
   * </pre>
   *
   * <code>uint32 supplyIndex = 9;</code>
   */
  public int getSupplyIndex() {
    return supplyIndex_;
  }

  public static final int THEORYINDEX_FIELD_NUMBER = 10;
  private int theoryIndex_;
  /**
   * <pre>
   * 可选，路口理论供给指数，0.01pcu/h。
   * </pre>
   *
   * <code>uint32 theoryIndex = 10;</code>
   */
  public int getTheoryIndex() {
    return theoryIndex_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (timestamp_ != 0L) {
      output.writeUInt64(1, timestamp_);
    }
    if (nodeSpaceOccupy_ != 0) {
      output.writeUInt32(2, nodeSpaceOccupy_);
    }
    if (nodeTimeOccupy_ != 0) {
      output.writeUInt32(3, nodeTimeOccupy_);
    }
    if (nodeCapacity_ != 0L) {
      output.writeUInt64(4, nodeCapacity_);
    }
    if (nodeSaturation_ != 0) {
      output.writeUInt32(5, nodeSaturation_);
    }
    if (nodeGrnUtilization_ != 0) {
      output.writeUInt32(6, nodeGrnUtilization_);
    }
    if (nodeAvgGrnQueue_ != 0) {
      output.writeUInt32(7, nodeAvgGrnQueue_);
    }
    if (demandIndex_ != 0) {
      output.writeUInt32(8, demandIndex_);
    }
    if (supplyIndex_ != 0) {
      output.writeUInt32(9, supplyIndex_);
    }
    if (theoryIndex_ != 0) {
      output.writeUInt32(10, theoryIndex_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, timestamp_);
    }
    if (nodeSpaceOccupy_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, nodeSpaceOccupy_);
    }
    if (nodeTimeOccupy_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, nodeTimeOccupy_);
    }
    if (nodeCapacity_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(4, nodeCapacity_);
    }
    if (nodeSaturation_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(5, nodeSaturation_);
    }
    if (nodeGrnUtilization_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(6, nodeGrnUtilization_);
    }
    if (nodeAvgGrnQueue_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(7, nodeAvgGrnQueue_);
    }
    if (demandIndex_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(8, demandIndex_);
    }
    if (supplyIndex_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(9, supplyIndex_);
    }
    if (theoryIndex_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(10, theoryIndex_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.NodeIndexAdded)) {
      return super.equals(obj);
    }
    road.data.proto.NodeIndexAdded other = (road.data.proto.NodeIndexAdded) obj;

    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (getNodeSpaceOccupy()
        != other.getNodeSpaceOccupy()) return false;
    if (getNodeTimeOccupy()
        != other.getNodeTimeOccupy()) return false;
    if (getNodeCapacity()
        != other.getNodeCapacity()) return false;
    if (getNodeSaturation()
        != other.getNodeSaturation()) return false;
    if (getNodeGrnUtilization()
        != other.getNodeGrnUtilization()) return false;
    if (getNodeAvgGrnQueue()
        != other.getNodeAvgGrnQueue()) return false;
    if (getDemandIndex()
        != other.getDemandIndex()) return false;
    if (getSupplyIndex()
        != other.getSupplyIndex()) return false;
    if (getTheoryIndex()
        != other.getTheoryIndex()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    hash = (37 * hash) + NODESPACEOCCUPY_FIELD_NUMBER;
    hash = (53 * hash) + getNodeSpaceOccupy();
    hash = (37 * hash) + NODETIMEOCCUPY_FIELD_NUMBER;
    hash = (53 * hash) + getNodeTimeOccupy();
    hash = (37 * hash) + NODECAPACITY_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getNodeCapacity());
    hash = (37 * hash) + NODESATURATION_FIELD_NUMBER;
    hash = (53 * hash) + getNodeSaturation();
    hash = (37 * hash) + NODEGRNUTILIZATION_FIELD_NUMBER;
    hash = (53 * hash) + getNodeGrnUtilization();
    hash = (37 * hash) + NODEAVGGRNQUEUE_FIELD_NUMBER;
    hash = (53 * hash) + getNodeAvgGrnQueue();
    hash = (37 * hash) + DEMANDINDEX_FIELD_NUMBER;
    hash = (53 * hash) + getDemandIndex();
    hash = (37 * hash) + SUPPLYINDEX_FIELD_NUMBER;
    hash = (53 * hash) + getSupplyIndex();
    hash = (37 * hash) + THEORYINDEX_FIELD_NUMBER;
    hash = (53 * hash) + getTheoryIndex();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.NodeIndexAdded parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.NodeIndexAdded parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.NodeIndexAdded parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.NodeIndexAdded parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.NodeIndexAdded parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.NodeIndexAdded parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.NodeIndexAdded parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.NodeIndexAdded parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.NodeIndexAdded parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.NodeIndexAdded parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.NodeIndexAdded parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.NodeIndexAdded parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.NodeIndexAdded prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *路口级指标  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.NodeIndexAdded}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.NodeIndexAdded)
      road.data.proto.NodeIndexAddedOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeIndexAdded_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeIndexAdded_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.NodeIndexAdded.class, road.data.proto.NodeIndexAdded.Builder.class);
    }

    // Construct using road.data.proto.NodeIndexAdded.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      timestamp_ = 0L;

      nodeSpaceOccupy_ = 0;

      nodeTimeOccupy_ = 0;

      nodeCapacity_ = 0L;

      nodeSaturation_ = 0;

      nodeGrnUtilization_ = 0;

      nodeAvgGrnQueue_ = 0;

      demandIndex_ = 0;

      supplyIndex_ = 0;

      theoryIndex_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeIndexAdded_descriptor;
    }

    @java.lang.Override
    public road.data.proto.NodeIndexAdded getDefaultInstanceForType() {
      return road.data.proto.NodeIndexAdded.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.NodeIndexAdded build() {
      road.data.proto.NodeIndexAdded result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.NodeIndexAdded buildPartial() {
      road.data.proto.NodeIndexAdded result = new road.data.proto.NodeIndexAdded(this);
      result.timestamp_ = timestamp_;
      result.nodeSpaceOccupy_ = nodeSpaceOccupy_;
      result.nodeTimeOccupy_ = nodeTimeOccupy_;
      result.nodeCapacity_ = nodeCapacity_;
      result.nodeSaturation_ = nodeSaturation_;
      result.nodeGrnUtilization_ = nodeGrnUtilization_;
      result.nodeAvgGrnQueue_ = nodeAvgGrnQueue_;
      result.demandIndex_ = demandIndex_;
      result.supplyIndex_ = supplyIndex_;
      result.theoryIndex_ = theoryIndex_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.NodeIndexAdded) {
        return mergeFrom((road.data.proto.NodeIndexAdded)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.NodeIndexAdded other) {
      if (other == road.data.proto.NodeIndexAdded.getDefaultInstance()) return this;
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (other.getNodeSpaceOccupy() != 0) {
        setNodeSpaceOccupy(other.getNodeSpaceOccupy());
      }
      if (other.getNodeTimeOccupy() != 0) {
        setNodeTimeOccupy(other.getNodeTimeOccupy());
      }
      if (other.getNodeCapacity() != 0L) {
        setNodeCapacity(other.getNodeCapacity());
      }
      if (other.getNodeSaturation() != 0) {
        setNodeSaturation(other.getNodeSaturation());
      }
      if (other.getNodeGrnUtilization() != 0) {
        setNodeGrnUtilization(other.getNodeGrnUtilization());
      }
      if (other.getNodeAvgGrnQueue() != 0) {
        setNodeAvgGrnQueue(other.getNodeAvgGrnQueue());
      }
      if (other.getDemandIndex() != 0) {
        setDemandIndex(other.getDemandIndex());
      }
      if (other.getSupplyIndex() != 0) {
        setSupplyIndex(other.getSupplyIndex());
      }
      if (other.getTheoryIndex() != 0) {
        setTheoryIndex(other.getTheoryIndex());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.NodeIndexAdded parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.NodeIndexAdded) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     *数据时间
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     *数据时间
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *数据时间
     * </pre>
     *
     * <code>uint64 timestamp = 1;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private int nodeSpaceOccupy_ ;
    /**
     * <pre>
     *可选，平均车道空间占有率取平均，0.01%
     * </pre>
     *
     * <code>uint32 nodeSpaceOccupy = 2;</code>
     */
    public int getNodeSpaceOccupy() {
      return nodeSpaceOccupy_;
    }
    /**
     * <pre>
     *可选，平均车道空间占有率取平均，0.01%
     * </pre>
     *
     * <code>uint32 nodeSpaceOccupy = 2;</code>
     */
    public Builder setNodeSpaceOccupy(int value) {
      
      nodeSpaceOccupy_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，平均车道空间占有率取平均，0.01%
     * </pre>
     *
     * <code>uint32 nodeSpaceOccupy = 2;</code>
     */
    public Builder clearNodeSpaceOccupy() {
      
      nodeSpaceOccupy_ = 0;
      onChanged();
      return this;
    }

    private int nodeTimeOccupy_ ;
    /**
     * <pre>
     *可选，平均车道时间占有率取平均，0.01%
     * </pre>
     *
     * <code>uint32 nodeTimeOccupy = 3;</code>
     */
    public int getNodeTimeOccupy() {
      return nodeTimeOccupy_;
    }
    /**
     * <pre>
     *可选，平均车道时间占有率取平均，0.01%
     * </pre>
     *
     * <code>uint32 nodeTimeOccupy = 3;</code>
     */
    public Builder setNodeTimeOccupy(int value) {
      
      nodeTimeOccupy_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，平均车道时间占有率取平均，0.01%
     * </pre>
     *
     * <code>uint32 nodeTimeOccupy = 3;</code>
     */
    public Builder clearNodeTimeOccupy() {
      
      nodeTimeOccupy_ = 0;
      onChanged();
      return this;
    }

    private long nodeCapacity_ ;
    /**
     * <pre>
     *可选，交叉口通行能力，0.01pcu/h
     * </pre>
     *
     * <code>uint64 nodeCapacity = 4;</code>
     */
    public long getNodeCapacity() {
      return nodeCapacity_;
    }
    /**
     * <pre>
     *可选，交叉口通行能力，0.01pcu/h
     * </pre>
     *
     * <code>uint64 nodeCapacity = 4;</code>
     */
    public Builder setNodeCapacity(long value) {
      
      nodeCapacity_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，交叉口通行能力，0.01pcu/h
     * </pre>
     *
     * <code>uint64 nodeCapacity = 4;</code>
     */
    public Builder clearNodeCapacity() {
      
      nodeCapacity_ = 0L;
      onChanged();
      return this;
    }

    private int nodeSaturation_ ;
    /**
     * <pre>
     *可选，交叉口平均饱和度，0.01%
     * </pre>
     *
     * <code>uint32 nodeSaturation = 5;</code>
     */
    public int getNodeSaturation() {
      return nodeSaturation_;
    }
    /**
     * <pre>
     *可选，交叉口平均饱和度，0.01%
     * </pre>
     *
     * <code>uint32 nodeSaturation = 5;</code>
     */
    public Builder setNodeSaturation(int value) {
      
      nodeSaturation_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，交叉口平均饱和度，0.01%
     * </pre>
     *
     * <code>uint32 nodeSaturation = 5;</code>
     */
    public Builder clearNodeSaturation() {
      
      nodeSaturation_ = 0;
      onChanged();
      return this;
    }

    private int nodeGrnUtilization_ ;
    /**
     * <pre>
     *可选，时段内交叉口红绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 nodeGrnUtilization = 6;</code>
     */
    public int getNodeGrnUtilization() {
      return nodeGrnUtilization_;
    }
    /**
     * <pre>
     *可选，时段内交叉口红绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 nodeGrnUtilization = 6;</code>
     */
    public Builder setNodeGrnUtilization(int value) {
      
      nodeGrnUtilization_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，时段内交叉口红绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 nodeGrnUtilization = 6;</code>
     */
    public Builder clearNodeGrnUtilization() {
      
      nodeGrnUtilization_ = 0;
      onChanged();
      return this;
    }

    private int nodeAvgGrnQueue_ ;
    /**
     * <pre>
     *可选，交叉口绿初车辆平均排队长度，0.01m
     * </pre>
     *
     * <code>uint32 nodeAvgGrnQueue = 7;</code>
     */
    public int getNodeAvgGrnQueue() {
      return nodeAvgGrnQueue_;
    }
    /**
     * <pre>
     *可选，交叉口绿初车辆平均排队长度，0.01m
     * </pre>
     *
     * <code>uint32 nodeAvgGrnQueue = 7;</code>
     */
    public Builder setNodeAvgGrnQueue(int value) {
      
      nodeAvgGrnQueue_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，交叉口绿初车辆平均排队长度，0.01m
     * </pre>
     *
     * <code>uint32 nodeAvgGrnQueue = 7;</code>
     */
    public Builder clearNodeAvgGrnQueue() {
      
      nodeAvgGrnQueue_ = 0;
      onChanged();
      return this;
    }

    private int demandIndex_ ;
    /**
     * <pre>
     *可选，路口需求指数，0.01pcu/h。
     * </pre>
     *
     * <code>uint32 demandIndex = 8;</code>
     */
    public int getDemandIndex() {
      return demandIndex_;
    }
    /**
     * <pre>
     *可选，路口需求指数，0.01pcu/h。
     * </pre>
     *
     * <code>uint32 demandIndex = 8;</code>
     */
    public Builder setDemandIndex(int value) {
      
      demandIndex_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，路口需求指数，0.01pcu/h。
     * </pre>
     *
     * <code>uint32 demandIndex = 8;</code>
     */
    public Builder clearDemandIndex() {
      
      demandIndex_ = 0;
      onChanged();
      return this;
    }

    private int supplyIndex_ ;
    /**
     * <pre>
     *可选，路口供给指数，0.01pcu/h
     * </pre>
     *
     * <code>uint32 supplyIndex = 9;</code>
     */
    public int getSupplyIndex() {
      return supplyIndex_;
    }
    /**
     * <pre>
     *可选，路口供给指数，0.01pcu/h
     * </pre>
     *
     * <code>uint32 supplyIndex = 9;</code>
     */
    public Builder setSupplyIndex(int value) {
      
      supplyIndex_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，路口供给指数，0.01pcu/h
     * </pre>
     *
     * <code>uint32 supplyIndex = 9;</code>
     */
    public Builder clearSupplyIndex() {
      
      supplyIndex_ = 0;
      onChanged();
      return this;
    }

    private int theoryIndex_ ;
    /**
     * <pre>
     * 可选，路口理论供给指数，0.01pcu/h。
     * </pre>
     *
     * <code>uint32 theoryIndex = 10;</code>
     */
    public int getTheoryIndex() {
      return theoryIndex_;
    }
    /**
     * <pre>
     * 可选，路口理论供给指数，0.01pcu/h。
     * </pre>
     *
     * <code>uint32 theoryIndex = 10;</code>
     */
    public Builder setTheoryIndex(int value) {
      
      theoryIndex_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，路口理论供给指数，0.01pcu/h。
     * </pre>
     *
     * <code>uint32 theoryIndex = 10;</code>
     */
    public Builder clearTheoryIndex() {
      
      theoryIndex_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.NodeIndexAdded)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.NodeIndexAdded)
  private static final road.data.proto.NodeIndexAdded DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.NodeIndexAdded();
  }

  public static road.data.proto.NodeIndexAdded getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<NodeIndexAdded>
      PARSER = new com.google.protobuf.AbstractParser<NodeIndexAdded>() {
    @java.lang.Override
    public NodeIndexAdded parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new NodeIndexAdded(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<NodeIndexAdded> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<NodeIndexAdded> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.NodeIndexAdded getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

