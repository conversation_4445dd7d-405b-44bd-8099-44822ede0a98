// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface NodeIndexAddedOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.NodeIndexAdded)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *数据时间
   * </pre>
   *
   * <code>uint64 timestamp = 1;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   *可选，平均车道空间占有率取平均，0.01%
   * </pre>
   *
   * <code>uint32 nodeSpaceOccupy = 2;</code>
   */
  int getNodeSpaceOccupy();

  /**
   * <pre>
   *可选，平均车道时间占有率取平均，0.01%
   * </pre>
   *
   * <code>uint32 nodeTimeOccupy = 3;</code>
   */
  int getNodeTimeOccupy();

  /**
   * <pre>
   *可选，交叉口通行能力，0.01pcu/h
   * </pre>
   *
   * <code>uint64 nodeCapacity = 4;</code>
   */
  long getNodeCapacity();

  /**
   * <pre>
   *可选，交叉口平均饱和度，0.01%
   * </pre>
   *
   * <code>uint32 nodeSaturation = 5;</code>
   */
  int getNodeSaturation();

  /**
   * <pre>
   *可选，时段内交叉口红绿灯利用率，0.01%
   * </pre>
   *
   * <code>uint32 nodeGrnUtilization = 6;</code>
   */
  int getNodeGrnUtilization();

  /**
   * <pre>
   *可选，交叉口绿初车辆平均排队长度，0.01m
   * </pre>
   *
   * <code>uint32 nodeAvgGrnQueue = 7;</code>
   */
  int getNodeAvgGrnQueue();

  /**
   * <pre>
   *可选，路口需求指数，0.01pcu/h。
   * </pre>
   *
   * <code>uint32 demandIndex = 8;</code>
   */
  int getDemandIndex();

  /**
   * <pre>
   *可选，路口供给指数，0.01pcu/h
   * </pre>
   *
   * <code>uint32 supplyIndex = 9;</code>
   */
  int getSupplyIndex();

  /**
   * <pre>
   * 可选，路口理论供给指数，0.01pcu/h。
   * </pre>
   *
   * <code>uint32 theoryIndex = 10;</code>
   */
  int getTheoryIndex();
}
