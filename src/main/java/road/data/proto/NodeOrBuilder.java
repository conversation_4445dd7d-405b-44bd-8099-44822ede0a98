// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface NodeOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.Node)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 交叉口名称
   * </pre>
   *
   * <code>string name = 1;</code>
   */
  java.lang.String getName();
  /**
   * <pre>
   * 交叉口名称
   * </pre>
   *
   * <code>string name = 1;</code>
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * 交叉口ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
   */
  boolean hasId();
  /**
   * <pre>
   * 交叉口ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
   */
  road.data.proto.NodeReferenceId getId();
  /**
   * <pre>
   * 交叉口ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId id = 2;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getIdOrBuilder();

  /**
   * <pre>
   * 交叉口位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
   */
  boolean hasRefPos();
  /**
   * <pre>
   * 交叉口位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
   */
  road.data.proto.Position3D getRefPos();
  /**
   * <pre>
   * 交叉口位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D refPos = 3;</code>
   */
  road.data.proto.Position3DOrBuilder getRefPosOrBuilder();

  /**
   * <pre>
   * 可选，交叉口所在路段
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
   */
  java.util.List<road.data.proto.Link> 
      getInLinksList();
  /**
   * <pre>
   * 可选，交叉口所在路段
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
   */
  road.data.proto.Link getInLinks(int index);
  /**
   * <pre>
   * 可选，交叉口所在路段
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
   */
  int getInLinksCount();
  /**
   * <pre>
   * 可选，交叉口所在路段
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
   */
  java.util.List<? extends road.data.proto.LinkOrBuilder> 
      getInLinksOrBuilderList();
  /**
   * <pre>
   * 可选，交叉口所在路段
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Link inLinks = 4;</code>
   */
  road.data.proto.LinkOrBuilder getInLinksOrBuilder(
      int index);

  /**
   * <pre>
   *可选，交叉口所在路段扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
   */
  java.util.List<road.data.proto.LinkEx> 
      getInLinksExList();
  /**
   * <pre>
   *可选，交叉口所在路段扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
   */
  road.data.proto.LinkEx getInLinksEx(int index);
  /**
   * <pre>
   *可选，交叉口所在路段扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
   */
  int getInLinksExCount();
  /**
   * <pre>
   *可选，交叉口所在路段扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
   */
  java.util.List<? extends road.data.proto.LinkExOrBuilder> 
      getInLinksExOrBuilderList();
  /**
   * <pre>
   *可选，交叉口所在路段扩展信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkEx inLinksEx = 5;</code>
   */
  road.data.proto.LinkExOrBuilder getInLinksExOrBuilder(
      int index);

  /**
   * <pre>
   *可选，交叉口内的禁停区域
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
   */
  boolean hasProhibitedZone();
  /**
   * <pre>
   *可选，交叉口内的禁停区域
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
   */
  road.data.proto.ProhibitedZone getProhibitedZone();
  /**
   * <pre>
   *可选，交叉口内的禁停区域
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ProhibitedZone prohibitedZone = 6;</code>
   */
  road.data.proto.ProhibitedZoneOrBuilder getProhibitedZoneOrBuilder();
}
