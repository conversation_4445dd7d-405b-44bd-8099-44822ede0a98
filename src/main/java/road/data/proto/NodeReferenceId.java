// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *节点编号NodeReferenceId 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.NodeReferenceId}
 */
public  final class NodeReferenceId extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.NodeReferenceId)
    NodeReferenceIdOrBuilder {
private static final long serialVersionUID = 0L;
  // Use NodeReferenceId.newBuilder() to construct.
  private NodeReferenceId(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private NodeReferenceId() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new NodeReferenceId();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private NodeReferenceId(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            region_ = input.readUInt32();
            break;
          }
          case 16: {

            nodeId_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeReferenceId_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeReferenceId_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.NodeReferenceId.class, road.data.proto.NodeReferenceId.Builder.class);
  }

  public static final int REGION_FIELD_NUMBER = 1;
  private int region_;
  /**
   * <pre>
   *全局唯一的地区 ID，取经纬度小数点后2位共同为region 编号
   * </pre>
   *
   * <code>uint32 region = 1;</code>
   */
  public int getRegion() {
    return region_;
  }

  public static final int NODEID_FIELD_NUMBER = 2;
  private int nodeId_;
  /**
   * <pre>
   *地区内部唯一的节点 ID，取经纬度小数点后 3-4 位共同为 id编号
   * </pre>
   *
   * <code>uint32 nodeId = 2;</code>
   */
  public int getNodeId() {
    return nodeId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (region_ != 0) {
      output.writeUInt32(1, region_);
    }
    if (nodeId_ != 0) {
      output.writeUInt32(2, nodeId_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (region_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, region_);
    }
    if (nodeId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, nodeId_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.NodeReferenceId)) {
      return super.equals(obj);
    }
    road.data.proto.NodeReferenceId other = (road.data.proto.NodeReferenceId) obj;

    if (getRegion()
        != other.getRegion()) return false;
    if (getNodeId()
        != other.getNodeId()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + REGION_FIELD_NUMBER;
    hash = (53 * hash) + getRegion();
    hash = (37 * hash) + NODEID_FIELD_NUMBER;
    hash = (53 * hash) + getNodeId();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.NodeReferenceId parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.NodeReferenceId parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.NodeReferenceId parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.NodeReferenceId parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.NodeReferenceId parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.NodeReferenceId parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.NodeReferenceId parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.NodeReferenceId parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.NodeReferenceId parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.NodeReferenceId parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.NodeReferenceId parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.NodeReferenceId parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.NodeReferenceId prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *节点编号NodeReferenceId 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.NodeReferenceId}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.NodeReferenceId)
      road.data.proto.NodeReferenceIdOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeReferenceId_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeReferenceId_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.NodeReferenceId.class, road.data.proto.NodeReferenceId.Builder.class);
    }

    // Construct using road.data.proto.NodeReferenceId.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      region_ = 0;

      nodeId_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeReferenceId_descriptor;
    }

    @java.lang.Override
    public road.data.proto.NodeReferenceId getDefaultInstanceForType() {
      return road.data.proto.NodeReferenceId.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.NodeReferenceId build() {
      road.data.proto.NodeReferenceId result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.NodeReferenceId buildPartial() {
      road.data.proto.NodeReferenceId result = new road.data.proto.NodeReferenceId(this);
      result.region_ = region_;
      result.nodeId_ = nodeId_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.NodeReferenceId) {
        return mergeFrom((road.data.proto.NodeReferenceId)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.NodeReferenceId other) {
      if (other == road.data.proto.NodeReferenceId.getDefaultInstance()) return this;
      if (other.getRegion() != 0) {
        setRegion(other.getRegion());
      }
      if (other.getNodeId() != 0) {
        setNodeId(other.getNodeId());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.NodeReferenceId parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.NodeReferenceId) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int region_ ;
    /**
     * <pre>
     *全局唯一的地区 ID，取经纬度小数点后2位共同为region 编号
     * </pre>
     *
     * <code>uint32 region = 1;</code>
     */
    public int getRegion() {
      return region_;
    }
    /**
     * <pre>
     *全局唯一的地区 ID，取经纬度小数点后2位共同为region 编号
     * </pre>
     *
     * <code>uint32 region = 1;</code>
     */
    public Builder setRegion(int value) {
      
      region_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *全局唯一的地区 ID，取经纬度小数点后2位共同为region 编号
     * </pre>
     *
     * <code>uint32 region = 1;</code>
     */
    public Builder clearRegion() {
      
      region_ = 0;
      onChanged();
      return this;
    }

    private int nodeId_ ;
    /**
     * <pre>
     *地区内部唯一的节点 ID，取经纬度小数点后 3-4 位共同为 id编号
     * </pre>
     *
     * <code>uint32 nodeId = 2;</code>
     */
    public int getNodeId() {
      return nodeId_;
    }
    /**
     * <pre>
     *地区内部唯一的节点 ID，取经纬度小数点后 3-4 位共同为 id编号
     * </pre>
     *
     * <code>uint32 nodeId = 2;</code>
     */
    public Builder setNodeId(int value) {
      
      nodeId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *地区内部唯一的节点 ID，取经纬度小数点后 3-4 位共同为 id编号
     * </pre>
     *
     * <code>uint32 nodeId = 2;</code>
     */
    public Builder clearNodeId() {
      
      nodeId_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.NodeReferenceId)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.NodeReferenceId)
  private static final road.data.proto.NodeReferenceId DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.NodeReferenceId();
  }

  public static road.data.proto.NodeReferenceId getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<NodeReferenceId>
      PARSER = new com.google.protobuf.AbstractParser<NodeReferenceId>() {
    @java.lang.Override
    public NodeReferenceId parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new NodeReferenceId(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<NodeReferenceId> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<NodeReferenceId> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.NodeReferenceId getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

