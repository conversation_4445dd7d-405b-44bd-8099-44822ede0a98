// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface NodeReferenceIdOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.NodeReferenceId)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *全局唯一的地区 ID，取经纬度小数点后2位共同为region 编号
   * </pre>
   *
   * <code>uint32 region = 1;</code>
   */
  int getRegion();

  /**
   * <pre>
   *地区内部唯一的节点 ID，取经纬度小数点后 3-4 位共同为 id编号
   * </pre>
   *
   * <code>uint32 nodeId = 2;</code>
   */
  int getNodeId();
}
