// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *路口对象  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.NodeStatInfo}
 */
public  final class NodeStatInfo extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.NodeStatInfo)
    NodeStatInfoOrBuilder {
private static final long serialVersionUID = 0L;
  // Use NodeStatInfo.newBuilder() to construct.
  private NodeStatInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private NodeStatInfo() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new NodeStatInfo();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private NodeStatInfo(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (nodeId_ != null) {
              subBuilder = nodeId_.toBuilder();
            }
            nodeId_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(nodeId_);
              nodeId_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeStatInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeStatInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.NodeStatInfo.class, road.data.proto.NodeStatInfo.Builder.class);
  }

  public static final int NODEID_FIELD_NUMBER = 1;
  private road.data.proto.NodeReferenceId nodeId_;
  /**
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  public boolean hasNodeId() {
    return nodeId_ != null;
  }
  /**
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  public road.data.proto.NodeReferenceId getNodeId() {
    return nodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
  }
  /**
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder() {
    return getNodeId();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (nodeId_ != null) {
      output.writeMessage(1, getNodeId());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (nodeId_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getNodeId());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.NodeStatInfo)) {
      return super.equals(obj);
    }
    road.data.proto.NodeStatInfo other = (road.data.proto.NodeStatInfo) obj;

    if (hasNodeId() != other.hasNodeId()) return false;
    if (hasNodeId()) {
      if (!getNodeId()
          .equals(other.getNodeId())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasNodeId()) {
      hash = (37 * hash) + NODEID_FIELD_NUMBER;
      hash = (53 * hash) + getNodeId().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.NodeStatInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.NodeStatInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.NodeStatInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.NodeStatInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.NodeStatInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.NodeStatInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.NodeStatInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.NodeStatInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.NodeStatInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.NodeStatInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.NodeStatInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.NodeStatInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.NodeStatInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *路口对象  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.NodeStatInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.NodeStatInfo)
      road.data.proto.NodeStatInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeStatInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeStatInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.NodeStatInfo.class, road.data.proto.NodeStatInfo.Builder.class);
    }

    // Construct using road.data.proto.NodeStatInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (nodeIdBuilder_ == null) {
        nodeId_ = null;
      } else {
        nodeId_ = null;
        nodeIdBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_NodeStatInfo_descriptor;
    }

    @java.lang.Override
    public road.data.proto.NodeStatInfo getDefaultInstanceForType() {
      return road.data.proto.NodeStatInfo.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.NodeStatInfo build() {
      road.data.proto.NodeStatInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.NodeStatInfo buildPartial() {
      road.data.proto.NodeStatInfo result = new road.data.proto.NodeStatInfo(this);
      if (nodeIdBuilder_ == null) {
        result.nodeId_ = nodeId_;
      } else {
        result.nodeId_ = nodeIdBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.NodeStatInfo) {
        return mergeFrom((road.data.proto.NodeStatInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.NodeStatInfo other) {
      if (other == road.data.proto.NodeStatInfo.getDefaultInstance()) return this;
      if (other.hasNodeId()) {
        mergeNodeId(other.getNodeId());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.NodeStatInfo parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.NodeStatInfo) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.NodeReferenceId nodeId_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> nodeIdBuilder_;
    /**
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public boolean hasNodeId() {
      return nodeIdBuilder_ != null || nodeId_ != null;
    }
    /**
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceId getNodeId() {
      if (nodeIdBuilder_ == null) {
        return nodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
      } else {
        return nodeIdBuilder_.getMessage();
      }
    }
    /**
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder setNodeId(road.data.proto.NodeReferenceId value) {
      if (nodeIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        nodeId_ = value;
        onChanged();
      } else {
        nodeIdBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder setNodeId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (nodeIdBuilder_ == null) {
        nodeId_ = builderForValue.build();
        onChanged();
      } else {
        nodeIdBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder mergeNodeId(road.data.proto.NodeReferenceId value) {
      if (nodeIdBuilder_ == null) {
        if (nodeId_ != null) {
          nodeId_ =
            road.data.proto.NodeReferenceId.newBuilder(nodeId_).mergeFrom(value).buildPartial();
        } else {
          nodeId_ = value;
        }
        onChanged();
      } else {
        nodeIdBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder clearNodeId() {
      if (nodeIdBuilder_ == null) {
        nodeId_ = null;
        onChanged();
      } else {
        nodeId_ = null;
        nodeIdBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getNodeIdBuilder() {
      
      onChanged();
      return getNodeIdFieldBuilder().getBuilder();
    }
    /**
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder() {
      if (nodeIdBuilder_ != null) {
        return nodeIdBuilder_.getMessageOrBuilder();
      } else {
        return nodeId_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
      }
    }
    /**
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getNodeIdFieldBuilder() {
      if (nodeIdBuilder_ == null) {
        nodeIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getNodeId(),
                getParentForChildren(),
                isClean());
        nodeId_ = null;
      }
      return nodeIdBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.NodeStatInfo)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.NodeStatInfo)
  private static final road.data.proto.NodeStatInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.NodeStatInfo();
  }

  public static road.data.proto.NodeStatInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<NodeStatInfo>
      PARSER = new com.google.protobuf.AbstractParser<NodeStatInfo>() {
    @java.lang.Override
    public NodeStatInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new NodeStatInfo(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<NodeStatInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<NodeStatInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.NodeStatInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

