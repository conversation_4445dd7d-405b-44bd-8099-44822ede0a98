// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface NodeStatInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.NodeStatInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  boolean hasNodeId();
  /**
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  road.data.proto.NodeReferenceId getNodeId();
  /**
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder();
}
