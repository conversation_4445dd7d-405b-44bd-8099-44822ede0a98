// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *参与到交通事件中的物体编号 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ObjIdValue}
 */
public  final class ObjIdValue extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ObjIdValue)
    ObjIdValueOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ObjIdValue.newBuilder() to construct.
  private ObjIdValue(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ObjIdValue() {
    role_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ObjIdValue();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ObjIdValue(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            ptcId_ = input.readUInt64();
            break;
          }
          case 16: {

            obsId_ = input.readUInt64();
            break;
          }
          case 24: {
            int rawValue = input.readEnum();

            role_ = rawValue;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ObjIdValue_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ObjIdValue_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ObjIdValue.class, road.data.proto.ObjIdValue.Builder.class);
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.ObjIdValue.Role}
   */
  public enum Role
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *主动
     * </pre>
     *
     * <code>ACTIVE = 0;</code>
     */
    ACTIVE(0),
    /**
     * <pre>
     *被动
     * </pre>
     *
     * <code>PASSIVE = 1;</code>
     */
    PASSIVE(1),
    /**
     * <pre>
     * 不明原因
     * </pre>
     *
     * <code>NOTCLEAR = 2;</code>
     */
    NOTCLEAR(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *主动
     * </pre>
     *
     * <code>ACTIVE = 0;</code>
     */
    public static final int ACTIVE_VALUE = 0;
    /**
     * <pre>
     *被动
     * </pre>
     *
     * <code>PASSIVE = 1;</code>
     */
    public static final int PASSIVE_VALUE = 1;
    /**
     * <pre>
     * 不明原因
     * </pre>
     *
     * <code>NOTCLEAR = 2;</code>
     */
    public static final int NOTCLEAR_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static Role valueOf(int value) {
      return forNumber(value);
    }

    public static Role forNumber(int value) {
      switch (value) {
        case 0: return ACTIVE;
        case 1: return PASSIVE;
        case 2: return NOTCLEAR;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<Role>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        Role> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<Role>() {
            public Role findValueByNumber(int number) {
              return Role.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.ObjIdValue.getDescriptor().getEnumTypes().get(0);
    }

    private static final Role[] VALUES = values();

    public static Role valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private Role(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.ObjIdValue.Role)
  }

  public static final int PTCID_FIELD_NUMBER = 1;
  private long ptcId_;
  /**
   * <pre>
   *可选，与ParticipantData里的ptcId保持一致，全局唯一
   * </pre>
   *
   * <code>uint64 ptcId = 1;</code>
   */
  public long getPtcId() {
    return ptcId_;
  }

  public static final int OBSID_FIELD_NUMBER = 2;
  private long obsId_;
  /**
   * <pre>
   *可选，Obstacles编号，全局唯一
   * </pre>
   *
   * <code>uint64 obsId = 2;</code>
   */
  public long getObsId() {
    return obsId_;
  }

  public static final int ROLE_FIELD_NUMBER = 3;
  private int role_;
  /**
   * <pre>
   *可选
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ObjIdValue.Role role = 3;</code>
   */
  public int getRoleValue() {
    return role_;
  }
  /**
   * <pre>
   *可选
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ObjIdValue.Role role = 3;</code>
   */
  public road.data.proto.ObjIdValue.Role getRole() {
    @SuppressWarnings("deprecation")
    road.data.proto.ObjIdValue.Role result = road.data.proto.ObjIdValue.Role.valueOf(role_);
    return result == null ? road.data.proto.ObjIdValue.Role.UNRECOGNIZED : result;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (ptcId_ != 0L) {
      output.writeUInt64(1, ptcId_);
    }
    if (obsId_ != 0L) {
      output.writeUInt64(2, obsId_);
    }
    if (role_ != road.data.proto.ObjIdValue.Role.ACTIVE.getNumber()) {
      output.writeEnum(3, role_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (ptcId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, ptcId_);
    }
    if (obsId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(2, obsId_);
    }
    if (role_ != road.data.proto.ObjIdValue.Role.ACTIVE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, role_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ObjIdValue)) {
      return super.equals(obj);
    }
    road.data.proto.ObjIdValue other = (road.data.proto.ObjIdValue) obj;

    if (getPtcId()
        != other.getPtcId()) return false;
    if (getObsId()
        != other.getObsId()) return false;
    if (role_ != other.role_) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + PTCID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getPtcId());
    hash = (37 * hash) + OBSID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getObsId());
    hash = (37 * hash) + ROLE_FIELD_NUMBER;
    hash = (53 * hash) + role_;
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ObjIdValue parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ObjIdValue parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ObjIdValue parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ObjIdValue parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ObjIdValue parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ObjIdValue parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ObjIdValue parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ObjIdValue parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ObjIdValue parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ObjIdValue parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ObjIdValue parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ObjIdValue parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ObjIdValue prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *参与到交通事件中的物体编号 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ObjIdValue}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ObjIdValue)
      road.data.proto.ObjIdValueOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ObjIdValue_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ObjIdValue_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ObjIdValue.class, road.data.proto.ObjIdValue.Builder.class);
    }

    // Construct using road.data.proto.ObjIdValue.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      ptcId_ = 0L;

      obsId_ = 0L;

      role_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ObjIdValue_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ObjIdValue getDefaultInstanceForType() {
      return road.data.proto.ObjIdValue.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ObjIdValue build() {
      road.data.proto.ObjIdValue result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ObjIdValue buildPartial() {
      road.data.proto.ObjIdValue result = new road.data.proto.ObjIdValue(this);
      result.ptcId_ = ptcId_;
      result.obsId_ = obsId_;
      result.role_ = role_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ObjIdValue) {
        return mergeFrom((road.data.proto.ObjIdValue)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ObjIdValue other) {
      if (other == road.data.proto.ObjIdValue.getDefaultInstance()) return this;
      if (other.getPtcId() != 0L) {
        setPtcId(other.getPtcId());
      }
      if (other.getObsId() != 0L) {
        setObsId(other.getObsId());
      }
      if (other.role_ != 0) {
        setRoleValue(other.getRoleValue());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ObjIdValue parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ObjIdValue) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long ptcId_ ;
    /**
     * <pre>
     *可选，与ParticipantData里的ptcId保持一致，全局唯一
     * </pre>
     *
     * <code>uint64 ptcId = 1;</code>
     */
    public long getPtcId() {
      return ptcId_;
    }
    /**
     * <pre>
     *可选，与ParticipantData里的ptcId保持一致，全局唯一
     * </pre>
     *
     * <code>uint64 ptcId = 1;</code>
     */
    public Builder setPtcId(long value) {
      
      ptcId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，与ParticipantData里的ptcId保持一致，全局唯一
     * </pre>
     *
     * <code>uint64 ptcId = 1;</code>
     */
    public Builder clearPtcId() {
      
      ptcId_ = 0L;
      onChanged();
      return this;
    }

    private long obsId_ ;
    /**
     * <pre>
     *可选，Obstacles编号，全局唯一
     * </pre>
     *
     * <code>uint64 obsId = 2;</code>
     */
    public long getObsId() {
      return obsId_;
    }
    /**
     * <pre>
     *可选，Obstacles编号，全局唯一
     * </pre>
     *
     * <code>uint64 obsId = 2;</code>
     */
    public Builder setObsId(long value) {
      
      obsId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，Obstacles编号，全局唯一
     * </pre>
     *
     * <code>uint64 obsId = 2;</code>
     */
    public Builder clearObsId() {
      
      obsId_ = 0L;
      onChanged();
      return this;
    }

    private int role_ = 0;
    /**
     * <pre>
     *可选
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ObjIdValue.Role role = 3;</code>
     */
    public int getRoleValue() {
      return role_;
    }
    /**
     * <pre>
     *可选
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ObjIdValue.Role role = 3;</code>
     */
    public Builder setRoleValue(int value) {
      role_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ObjIdValue.Role role = 3;</code>
     */
    public road.data.proto.ObjIdValue.Role getRole() {
      @SuppressWarnings("deprecation")
      road.data.proto.ObjIdValue.Role result = road.data.proto.ObjIdValue.Role.valueOf(role_);
      return result == null ? road.data.proto.ObjIdValue.Role.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ObjIdValue.Role role = 3;</code>
     */
    public Builder setRole(road.data.proto.ObjIdValue.Role value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      role_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ObjIdValue.Role role = 3;</code>
     */
    public Builder clearRole() {
      
      role_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ObjIdValue)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ObjIdValue)
  private static final road.data.proto.ObjIdValue DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ObjIdValue();
  }

  public static road.data.proto.ObjIdValue getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ObjIdValue>
      PARSER = new com.google.protobuf.AbstractParser<ObjIdValue>() {
    @java.lang.Override
    public ObjIdValue parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ObjIdValue(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ObjIdValue> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ObjIdValue> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ObjIdValue getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

