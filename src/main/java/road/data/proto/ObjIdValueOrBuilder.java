// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ObjIdValueOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ObjIdValue)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *可选，与ParticipantData里的ptcId保持一致，全局唯一
   * </pre>
   *
   * <code>uint64 ptcId = 1;</code>
   */
  long getPtcId();

  /**
   * <pre>
   *可选，Obstacles编号，全局唯一
   * </pre>
   *
   * <code>uint64 obsId = 2;</code>
   */
  long getObsId();

  /**
   * <pre>
   *可选
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ObjIdValue.Role role = 3;</code>
   */
  int getRoleValue();
  /**
   * <pre>
   *可选
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ObjIdValue.Role role = 3;</code>
   */
  road.data.proto.ObjIdValue.Role getRole();
}
