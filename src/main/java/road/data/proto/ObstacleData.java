// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *障碍物信息ObstacleData    
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ObstacleData}
 */
public  final class ObstacleData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ObstacleData)
    ObstacleDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ObstacleData.newBuilder() to construct.
  private ObstacleData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ObstacleData() {
    obsType_ = 0;
    obsSource_ = 0;
    deviceIdList_ = "";
    verSpeedConfid_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ObstacleData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ObstacleData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            obsId_ = input.readUInt64();
            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            obsType_ = rawValue;
            break;
          }
          case 24: {

            obstypeCfd_ = input.readUInt32();
            break;
          }
          case 32: {
            int rawValue = input.readEnum();

            obsSource_ = rawValue;
            break;
          }
          case 40: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            deviceIdList_ = s;
            break;
          }
          case 58: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (obsPos_ != null) {
              subBuilder = obsPos_.toBuilder();
            }
            obsPos_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(obsPos_);
              obsPos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 66: {
            road.data.proto.PositionConfidenceSet.Builder subBuilder = null;
            if (posConfid_ != null) {
              subBuilder = posConfid_.toBuilder();
            }
            posConfid_ = input.readMessage(road.data.proto.PositionConfidenceSet.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(posConfid_);
              posConfid_ = subBuilder.buildPartial();
            }

            break;
          }
          case 74: {
            road.data.proto.MapLocation.Builder subBuilder = null;
            if (mapLocation_ != null) {
              subBuilder = mapLocation_.toBuilder();
            }
            mapLocation_ = input.readMessage(road.data.proto.MapLocation.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(mapLocation_);
              mapLocation_ = subBuilder.buildPartial();
            }

            break;
          }
          case 80: {

            speed_ = input.readUInt32();
            break;
          }
          case 88: {

            heading_ = input.readUInt32();
            break;
          }
          case 98: {
            road.data.proto.MotionConfidenceSet.Builder subBuilder = null;
            if (motionConfid_ != null) {
              subBuilder = motionConfid_.toBuilder();
            }
            motionConfid_ = input.readMessage(road.data.proto.MotionConfidenceSet.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(motionConfid_);
              motionConfid_ = subBuilder.buildPartial();
            }

            break;
          }
          case 104: {

            verSpeed_ = input.readUInt32();
            break;
          }
          case 112: {
            int rawValue = input.readEnum();

            verSpeedConfid_ = rawValue;
            break;
          }
          case 122: {
            road.data.proto.AccelerationSet4Way.Builder subBuilder = null;
            if (acceleration_ != null) {
              subBuilder = acceleration_.toBuilder();
            }
            acceleration_ = input.readMessage(road.data.proto.AccelerationSet4Way.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(acceleration_);
              acceleration_ = subBuilder.buildPartial();
            }

            break;
          }
          case 130: {
            road.data.proto.ParticipantSize.Builder subBuilder = null;
            if (size_ != null) {
              subBuilder = size_.toBuilder();
            }
            size_ = input.readMessage(road.data.proto.ParticipantSize.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(size_);
              size_ = subBuilder.buildPartial();
            }

            break;
          }
          case 138: {
            road.data.proto.ParticipantSizeConfidence.Builder subBuilder = null;
            if (obsSizeConfid_ != null) {
              subBuilder = obsSizeConfid_.toBuilder();
            }
            obsSizeConfid_ = input.readMessage(road.data.proto.ParticipantSizeConfidence.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(obsSizeConfid_);
              obsSizeConfid_ = subBuilder.buildPartial();
            }

            break;
          }
          case 144: {

            tracking_ = input.readUInt32();
            break;
          }
          case 154: {
            road.data.proto.Polygon.Builder subBuilder = null;
            if (polygon_ != null) {
              subBuilder = polygon_.toBuilder();
            }
            polygon_ = input.readMessage(road.data.proto.Polygon.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(polygon_);
              polygon_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ObstacleData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ObstacleData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ObstacleData.class, road.data.proto.ObstacleData.Builder.class);
  }

  public static final int OBSID_FIELD_NUMBER = 1;
  private long obsId_;
  /**
   * <pre>
   * 障碍物ID
   * </pre>
   *
   * <code>uint64 obsId = 1;</code>
   */
  public long getObsId() {
    return obsId_;
  }

  public static final int OBSTYPE_FIELD_NUMBER = 2;
  private int obsType_;
  /**
   * <pre>
   * 障碍物类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ObstaclesType obsType = 2;</code>
   */
  public int getObsTypeValue() {
    return obsType_;
  }
  /**
   * <pre>
   * 障碍物类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ObstaclesType obsType = 2;</code>
   */
  public road.data.proto.ObstaclesType getObsType() {
    @SuppressWarnings("deprecation")
    road.data.proto.ObstaclesType result = road.data.proto.ObstaclesType.valueOf(obsType_);
    return result == null ? road.data.proto.ObstaclesType.UNRECOGNIZED : result;
  }

  public static final int OBSTYPECFD_FIELD_NUMBER = 3;
  private int obstypeCfd_;
  /**
   * <pre>
   * 可选，定义障碍物类型的置信度;分辨率为0.005。
   * </pre>
   *
   * <code>uint32 obstypeCfd = 3;</code>
   */
  public int getObstypeCfd() {
    return obstypeCfd_;
  }

  public static final int OBSSOURCE_FIELD_NUMBER = 4;
  private int obsSource_;
  /**
   * <pre>
   * 障碍物数据来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource obsSource = 4;</code>
   */
  public int getObsSourceValue() {
    return obsSource_;
  }
  /**
   * <pre>
   * 障碍物数据来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource obsSource = 4;</code>
   */
  public road.data.proto.DataSource getObsSource() {
    @SuppressWarnings("deprecation")
    road.data.proto.DataSource result = road.data.proto.DataSource.valueOf(obsSource_);
    return result == null ? road.data.proto.DataSource.UNRECOGNIZED : result;
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 5;
  private long timestamp_;
  /**
   * <pre>
   *时间戳
   * </pre>
   *
   * <code>uint64 timestamp = 5;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int DEVICEIDLIST_FIELD_NUMBER = 6;
  private volatile java.lang.Object deviceIdList_;
  /**
   * <pre>
   *数据融合的来源设备id,json数组
   * </pre>
   *
   * <code>string deviceIdList = 6;</code>
   */
  public java.lang.String getDeviceIdList() {
    java.lang.Object ref = deviceIdList_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deviceIdList_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *数据融合的来源设备id,json数组
   * </pre>
   *
   * <code>string deviceIdList = 6;</code>
   */
  public com.google.protobuf.ByteString
      getDeviceIdListBytes() {
    java.lang.Object ref = deviceIdList_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deviceIdList_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int OBSPOS_FIELD_NUMBER = 7;
  private road.data.proto.Position3D obsPos_;
  /**
   * <pre>
   * 定义障碍物经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
   */
  public boolean hasObsPos() {
    return obsPos_ != null;
  }
  /**
   * <pre>
   * 定义障碍物经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
   */
  public road.data.proto.Position3D getObsPos() {
    return obsPos_ == null ? road.data.proto.Position3D.getDefaultInstance() : obsPos_;
  }
  /**
   * <pre>
   * 定义障碍物经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
   */
  public road.data.proto.Position3DOrBuilder getObsPosOrBuilder() {
    return getObsPos();
  }

  public static final int POSCONFID_FIELD_NUMBER = 8;
  private road.data.proto.PositionConfidenceSet posConfid_;
  /**
   * <pre>
   * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
   */
  public boolean hasPosConfid() {
    return posConfid_ != null;
  }
  /**
   * <pre>
   * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
   */
  public road.data.proto.PositionConfidenceSet getPosConfid() {
    return posConfid_ == null ? road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
  }
  /**
   * <pre>
   * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
   */
  public road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder() {
    return getPosConfid();
  }

  public static final int MAPLOCATION_FIELD_NUMBER = 9;
  private road.data.proto.MapLocation mapLocation_;
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
   */
  public boolean hasMapLocation() {
    return mapLocation_ != null;
  }
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
   */
  public road.data.proto.MapLocation getMapLocation() {
    return mapLocation_ == null ? road.data.proto.MapLocation.getDefaultInstance() : mapLocation_;
  }
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
   */
  public road.data.proto.MapLocationOrBuilder getMapLocationOrBuilder() {
    return getMapLocation();
  }

  public static final int SPEED_FIELD_NUMBER = 10;
  private int speed_;
  /**
   * <pre>
   * 障碍物速度，分辨率为0.02m/s，数值8191表示无效数值
   * </pre>
   *
   * <code>uint32 speed = 10;</code>
   */
  public int getSpeed() {
    return speed_;
  }

  public static final int HEADING_FIELD_NUMBER = 11;
  private int heading_;
  /**
   * <pre>
   * 障碍物航向角，运行方向与正北方向的顺时针夹角。分辨率为0.0125°
   * </pre>
   *
   * <code>uint32 heading = 11;</code>
   */
  public int getHeading() {
    return heading_;
  }

  public static final int MOTIONCONFID_FIELD_NUMBER = 12;
  private road.data.proto.MotionConfidenceSet motionConfid_;
  /**
   * <pre>
   *可选，运动状态置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  public boolean hasMotionConfid() {
    return motionConfid_ != null;
  }
  /**
   * <pre>
   *可选，运动状态置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  public road.data.proto.MotionConfidenceSet getMotionConfid() {
    return motionConfid_ == null ? road.data.proto.MotionConfidenceSet.getDefaultInstance() : motionConfid_;
  }
  /**
   * <pre>
   *可选，运动状态置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  public road.data.proto.MotionConfidenceSetOrBuilder getMotionConfidOrBuilder() {
    return getMotionConfid();
  }

  public static final int VERSPEED_FIELD_NUMBER = 13;
  private int verSpeed_;
  /**
   * <pre>
   *可选，障碍物垂直速度，分辨率为0.02m/s，数值8191表示无效数值
   * </pre>
   *
   * <code>uint32 verSpeed = 13;</code>
   */
  public int getVerSpeed() {
    return verSpeed_;
  }

  public static final int VERSPEEDCONFID_FIELD_NUMBER = 14;
  private int verSpeedConfid_;
  /**
   * <pre>
   * 可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpeedConfidence verSpeedConfid = 14;</code>
   */
  public int getVerSpeedConfidValue() {
    return verSpeedConfid_;
  }
  /**
   * <pre>
   * 可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpeedConfidence verSpeedConfid = 14;</code>
   */
  public road.data.proto.SpeedConfidence getVerSpeedConfid() {
    @SuppressWarnings("deprecation")
    road.data.proto.SpeedConfidence result = road.data.proto.SpeedConfidence.valueOf(verSpeedConfid_);
    return result == null ? road.data.proto.SpeedConfidence.UNRECOGNIZED : result;
  }

  public static final int ACCELERATION_FIELD_NUMBER = 15;
  private road.data.proto.AccelerationSet4Way acceleration_;
  /**
   * <pre>
   * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
   */
  public boolean hasAcceleration() {
    return acceleration_ != null;
  }
  /**
   * <pre>
   * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
   */
  public road.data.proto.AccelerationSet4Way getAcceleration() {
    return acceleration_ == null ? road.data.proto.AccelerationSet4Way.getDefaultInstance() : acceleration_;
  }
  /**
   * <pre>
   * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
   */
  public road.data.proto.AccelerationSet4WayOrBuilder getAccelerationOrBuilder() {
    return getAcceleration();
  }

  public static final int SIZE_FIELD_NUMBER = 16;
  private road.data.proto.ParticipantSize size_;
  /**
   * <pre>
   * 障碍物尺寸大小
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
   */
  public boolean hasSize() {
    return size_ != null;
  }
  /**
   * <pre>
   * 障碍物尺寸大小
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
   */
  public road.data.proto.ParticipantSize getSize() {
    return size_ == null ? road.data.proto.ParticipantSize.getDefaultInstance() : size_;
  }
  /**
   * <pre>
   * 障碍物尺寸大小
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
   */
  public road.data.proto.ParticipantSizeOrBuilder getSizeOrBuilder() {
    return getSize();
  }

  public static final int OBSSIZECONFID_FIELD_NUMBER = 17;
  private road.data.proto.ParticipantSizeConfidence obsSizeConfid_;
  /**
   * <pre>
   * 可选，障碍物尺寸大小置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
   */
  public boolean hasObsSizeConfid() {
    return obsSizeConfid_ != null;
  }
  /**
   * <pre>
   * 可选，障碍物尺寸大小置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
   */
  public road.data.proto.ParticipantSizeConfidence getObsSizeConfid() {
    return obsSizeConfid_ == null ? road.data.proto.ParticipantSizeConfidence.getDefaultInstance() : obsSizeConfid_;
  }
  /**
   * <pre>
   * 可选，障碍物尺寸大小置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
   */
  public road.data.proto.ParticipantSizeConfidenceOrBuilder getObsSizeConfidOrBuilder() {
    return getObsSizeConfid();
  }

  public static final int TRACKING_FIELD_NUMBER = 18;
  private int tracking_;
  /**
   * <pre>
   * 可选，障碍物追踪时间，单位s
   * </pre>
   *
   * <code>uint32 tracking = 18;</code>
   */
  public int getTracking() {
    return tracking_;
  }

  public static final int POLYGON_FIELD_NUMBER = 19;
  private road.data.proto.Polygon polygon_;
  /**
   * <pre>
   * 可选，障碍物影响区域点集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
   */
  public boolean hasPolygon() {
    return polygon_ != null;
  }
  /**
   * <pre>
   * 可选，障碍物影响区域点集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
   */
  public road.data.proto.Polygon getPolygon() {
    return polygon_ == null ? road.data.proto.Polygon.getDefaultInstance() : polygon_;
  }
  /**
   * <pre>
   * 可选，障碍物影响区域点集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
   */
  public road.data.proto.PolygonOrBuilder getPolygonOrBuilder() {
    return getPolygon();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (obsId_ != 0L) {
      output.writeUInt64(1, obsId_);
    }
    if (obsType_ != road.data.proto.ObstaclesType.UNKNOWN_OBSTACLES_TYPE.getNumber()) {
      output.writeEnum(2, obsType_);
    }
    if (obstypeCfd_ != 0) {
      output.writeUInt32(3, obstypeCfd_);
    }
    if (obsSource_ != road.data.proto.DataSource.DATA_SOURCE_UNKNOWN.getNumber()) {
      output.writeEnum(4, obsSource_);
    }
    if (timestamp_ != 0L) {
      output.writeUInt64(5, timestamp_);
    }
    if (!getDeviceIdListBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, deviceIdList_);
    }
    if (obsPos_ != null) {
      output.writeMessage(7, getObsPos());
    }
    if (posConfid_ != null) {
      output.writeMessage(8, getPosConfid());
    }
    if (mapLocation_ != null) {
      output.writeMessage(9, getMapLocation());
    }
    if (speed_ != 0) {
      output.writeUInt32(10, speed_);
    }
    if (heading_ != 0) {
      output.writeUInt32(11, heading_);
    }
    if (motionConfid_ != null) {
      output.writeMessage(12, getMotionConfid());
    }
    if (verSpeed_ != 0) {
      output.writeUInt32(13, verSpeed_);
    }
    if (verSpeedConfid_ != road.data.proto.SpeedConfidence.SPEED_CONFID_UNAVAILABLE.getNumber()) {
      output.writeEnum(14, verSpeedConfid_);
    }
    if (acceleration_ != null) {
      output.writeMessage(15, getAcceleration());
    }
    if (size_ != null) {
      output.writeMessage(16, getSize());
    }
    if (obsSizeConfid_ != null) {
      output.writeMessage(17, getObsSizeConfid());
    }
    if (tracking_ != 0) {
      output.writeUInt32(18, tracking_);
    }
    if (polygon_ != null) {
      output.writeMessage(19, getPolygon());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (obsId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, obsId_);
    }
    if (obsType_ != road.data.proto.ObstaclesType.UNKNOWN_OBSTACLES_TYPE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, obsType_);
    }
    if (obstypeCfd_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, obstypeCfd_);
    }
    if (obsSource_ != road.data.proto.DataSource.DATA_SOURCE_UNKNOWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(4, obsSource_);
    }
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(5, timestamp_);
    }
    if (!getDeviceIdListBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, deviceIdList_);
    }
    if (obsPos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getObsPos());
    }
    if (posConfid_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, getPosConfid());
    }
    if (mapLocation_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(9, getMapLocation());
    }
    if (speed_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(10, speed_);
    }
    if (heading_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(11, heading_);
    }
    if (motionConfid_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(12, getMotionConfid());
    }
    if (verSpeed_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(13, verSpeed_);
    }
    if (verSpeedConfid_ != road.data.proto.SpeedConfidence.SPEED_CONFID_UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(14, verSpeedConfid_);
    }
    if (acceleration_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(15, getAcceleration());
    }
    if (size_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(16, getSize());
    }
    if (obsSizeConfid_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(17, getObsSizeConfid());
    }
    if (tracking_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(18, tracking_);
    }
    if (polygon_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(19, getPolygon());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ObstacleData)) {
      return super.equals(obj);
    }
    road.data.proto.ObstacleData other = (road.data.proto.ObstacleData) obj;

    if (getObsId()
        != other.getObsId()) return false;
    if (obsType_ != other.obsType_) return false;
    if (getObstypeCfd()
        != other.getObstypeCfd()) return false;
    if (obsSource_ != other.obsSource_) return false;
    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (!getDeviceIdList()
        .equals(other.getDeviceIdList())) return false;
    if (hasObsPos() != other.hasObsPos()) return false;
    if (hasObsPos()) {
      if (!getObsPos()
          .equals(other.getObsPos())) return false;
    }
    if (hasPosConfid() != other.hasPosConfid()) return false;
    if (hasPosConfid()) {
      if (!getPosConfid()
          .equals(other.getPosConfid())) return false;
    }
    if (hasMapLocation() != other.hasMapLocation()) return false;
    if (hasMapLocation()) {
      if (!getMapLocation()
          .equals(other.getMapLocation())) return false;
    }
    if (getSpeed()
        != other.getSpeed()) return false;
    if (getHeading()
        != other.getHeading()) return false;
    if (hasMotionConfid() != other.hasMotionConfid()) return false;
    if (hasMotionConfid()) {
      if (!getMotionConfid()
          .equals(other.getMotionConfid())) return false;
    }
    if (getVerSpeed()
        != other.getVerSpeed()) return false;
    if (verSpeedConfid_ != other.verSpeedConfid_) return false;
    if (hasAcceleration() != other.hasAcceleration()) return false;
    if (hasAcceleration()) {
      if (!getAcceleration()
          .equals(other.getAcceleration())) return false;
    }
    if (hasSize() != other.hasSize()) return false;
    if (hasSize()) {
      if (!getSize()
          .equals(other.getSize())) return false;
    }
    if (hasObsSizeConfid() != other.hasObsSizeConfid()) return false;
    if (hasObsSizeConfid()) {
      if (!getObsSizeConfid()
          .equals(other.getObsSizeConfid())) return false;
    }
    if (getTracking()
        != other.getTracking()) return false;
    if (hasPolygon() != other.hasPolygon()) return false;
    if (hasPolygon()) {
      if (!getPolygon()
          .equals(other.getPolygon())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + OBSID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getObsId());
    hash = (37 * hash) + OBSTYPE_FIELD_NUMBER;
    hash = (53 * hash) + obsType_;
    hash = (37 * hash) + OBSTYPECFD_FIELD_NUMBER;
    hash = (53 * hash) + getObstypeCfd();
    hash = (37 * hash) + OBSSOURCE_FIELD_NUMBER;
    hash = (53 * hash) + obsSource_;
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    hash = (37 * hash) + DEVICEIDLIST_FIELD_NUMBER;
    hash = (53 * hash) + getDeviceIdList().hashCode();
    if (hasObsPos()) {
      hash = (37 * hash) + OBSPOS_FIELD_NUMBER;
      hash = (53 * hash) + getObsPos().hashCode();
    }
    if (hasPosConfid()) {
      hash = (37 * hash) + POSCONFID_FIELD_NUMBER;
      hash = (53 * hash) + getPosConfid().hashCode();
    }
    if (hasMapLocation()) {
      hash = (37 * hash) + MAPLOCATION_FIELD_NUMBER;
      hash = (53 * hash) + getMapLocation().hashCode();
    }
    hash = (37 * hash) + SPEED_FIELD_NUMBER;
    hash = (53 * hash) + getSpeed();
    hash = (37 * hash) + HEADING_FIELD_NUMBER;
    hash = (53 * hash) + getHeading();
    if (hasMotionConfid()) {
      hash = (37 * hash) + MOTIONCONFID_FIELD_NUMBER;
      hash = (53 * hash) + getMotionConfid().hashCode();
    }
    hash = (37 * hash) + VERSPEED_FIELD_NUMBER;
    hash = (53 * hash) + getVerSpeed();
    hash = (37 * hash) + VERSPEEDCONFID_FIELD_NUMBER;
    hash = (53 * hash) + verSpeedConfid_;
    if (hasAcceleration()) {
      hash = (37 * hash) + ACCELERATION_FIELD_NUMBER;
      hash = (53 * hash) + getAcceleration().hashCode();
    }
    if (hasSize()) {
      hash = (37 * hash) + SIZE_FIELD_NUMBER;
      hash = (53 * hash) + getSize().hashCode();
    }
    if (hasObsSizeConfid()) {
      hash = (37 * hash) + OBSSIZECONFID_FIELD_NUMBER;
      hash = (53 * hash) + getObsSizeConfid().hashCode();
    }
    hash = (37 * hash) + TRACKING_FIELD_NUMBER;
    hash = (53 * hash) + getTracking();
    if (hasPolygon()) {
      hash = (37 * hash) + POLYGON_FIELD_NUMBER;
      hash = (53 * hash) + getPolygon().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ObstacleData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ObstacleData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ObstacleData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ObstacleData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ObstacleData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ObstacleData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ObstacleData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ObstacleData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ObstacleData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ObstacleData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ObstacleData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ObstacleData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ObstacleData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *障碍物信息ObstacleData    
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ObstacleData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ObstacleData)
      road.data.proto.ObstacleDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ObstacleData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ObstacleData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ObstacleData.class, road.data.proto.ObstacleData.Builder.class);
    }

    // Construct using road.data.proto.ObstacleData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      obsId_ = 0L;

      obsType_ = 0;

      obstypeCfd_ = 0;

      obsSource_ = 0;

      timestamp_ = 0L;

      deviceIdList_ = "";

      if (obsPosBuilder_ == null) {
        obsPos_ = null;
      } else {
        obsPos_ = null;
        obsPosBuilder_ = null;
      }
      if (posConfidBuilder_ == null) {
        posConfid_ = null;
      } else {
        posConfid_ = null;
        posConfidBuilder_ = null;
      }
      if (mapLocationBuilder_ == null) {
        mapLocation_ = null;
      } else {
        mapLocation_ = null;
        mapLocationBuilder_ = null;
      }
      speed_ = 0;

      heading_ = 0;

      if (motionConfidBuilder_ == null) {
        motionConfid_ = null;
      } else {
        motionConfid_ = null;
        motionConfidBuilder_ = null;
      }
      verSpeed_ = 0;

      verSpeedConfid_ = 0;

      if (accelerationBuilder_ == null) {
        acceleration_ = null;
      } else {
        acceleration_ = null;
        accelerationBuilder_ = null;
      }
      if (sizeBuilder_ == null) {
        size_ = null;
      } else {
        size_ = null;
        sizeBuilder_ = null;
      }
      if (obsSizeConfidBuilder_ == null) {
        obsSizeConfid_ = null;
      } else {
        obsSizeConfid_ = null;
        obsSizeConfidBuilder_ = null;
      }
      tracking_ = 0;

      if (polygonBuilder_ == null) {
        polygon_ = null;
      } else {
        polygon_ = null;
        polygonBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ObstacleData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ObstacleData getDefaultInstanceForType() {
      return road.data.proto.ObstacleData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ObstacleData build() {
      road.data.proto.ObstacleData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ObstacleData buildPartial() {
      road.data.proto.ObstacleData result = new road.data.proto.ObstacleData(this);
      result.obsId_ = obsId_;
      result.obsType_ = obsType_;
      result.obstypeCfd_ = obstypeCfd_;
      result.obsSource_ = obsSource_;
      result.timestamp_ = timestamp_;
      result.deviceIdList_ = deviceIdList_;
      if (obsPosBuilder_ == null) {
        result.obsPos_ = obsPos_;
      } else {
        result.obsPos_ = obsPosBuilder_.build();
      }
      if (posConfidBuilder_ == null) {
        result.posConfid_ = posConfid_;
      } else {
        result.posConfid_ = posConfidBuilder_.build();
      }
      if (mapLocationBuilder_ == null) {
        result.mapLocation_ = mapLocation_;
      } else {
        result.mapLocation_ = mapLocationBuilder_.build();
      }
      result.speed_ = speed_;
      result.heading_ = heading_;
      if (motionConfidBuilder_ == null) {
        result.motionConfid_ = motionConfid_;
      } else {
        result.motionConfid_ = motionConfidBuilder_.build();
      }
      result.verSpeed_ = verSpeed_;
      result.verSpeedConfid_ = verSpeedConfid_;
      if (accelerationBuilder_ == null) {
        result.acceleration_ = acceleration_;
      } else {
        result.acceleration_ = accelerationBuilder_.build();
      }
      if (sizeBuilder_ == null) {
        result.size_ = size_;
      } else {
        result.size_ = sizeBuilder_.build();
      }
      if (obsSizeConfidBuilder_ == null) {
        result.obsSizeConfid_ = obsSizeConfid_;
      } else {
        result.obsSizeConfid_ = obsSizeConfidBuilder_.build();
      }
      result.tracking_ = tracking_;
      if (polygonBuilder_ == null) {
        result.polygon_ = polygon_;
      } else {
        result.polygon_ = polygonBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ObstacleData) {
        return mergeFrom((road.data.proto.ObstacleData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ObstacleData other) {
      if (other == road.data.proto.ObstacleData.getDefaultInstance()) return this;
      if (other.getObsId() != 0L) {
        setObsId(other.getObsId());
      }
      if (other.obsType_ != 0) {
        setObsTypeValue(other.getObsTypeValue());
      }
      if (other.getObstypeCfd() != 0) {
        setObstypeCfd(other.getObstypeCfd());
      }
      if (other.obsSource_ != 0) {
        setObsSourceValue(other.getObsSourceValue());
      }
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (!other.getDeviceIdList().isEmpty()) {
        deviceIdList_ = other.deviceIdList_;
        onChanged();
      }
      if (other.hasObsPos()) {
        mergeObsPos(other.getObsPos());
      }
      if (other.hasPosConfid()) {
        mergePosConfid(other.getPosConfid());
      }
      if (other.hasMapLocation()) {
        mergeMapLocation(other.getMapLocation());
      }
      if (other.getSpeed() != 0) {
        setSpeed(other.getSpeed());
      }
      if (other.getHeading() != 0) {
        setHeading(other.getHeading());
      }
      if (other.hasMotionConfid()) {
        mergeMotionConfid(other.getMotionConfid());
      }
      if (other.getVerSpeed() != 0) {
        setVerSpeed(other.getVerSpeed());
      }
      if (other.verSpeedConfid_ != 0) {
        setVerSpeedConfidValue(other.getVerSpeedConfidValue());
      }
      if (other.hasAcceleration()) {
        mergeAcceleration(other.getAcceleration());
      }
      if (other.hasSize()) {
        mergeSize(other.getSize());
      }
      if (other.hasObsSizeConfid()) {
        mergeObsSizeConfid(other.getObsSizeConfid());
      }
      if (other.getTracking() != 0) {
        setTracking(other.getTracking());
      }
      if (other.hasPolygon()) {
        mergePolygon(other.getPolygon());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ObstacleData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ObstacleData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long obsId_ ;
    /**
     * <pre>
     * 障碍物ID
     * </pre>
     *
     * <code>uint64 obsId = 1;</code>
     */
    public long getObsId() {
      return obsId_;
    }
    /**
     * <pre>
     * 障碍物ID
     * </pre>
     *
     * <code>uint64 obsId = 1;</code>
     */
    public Builder setObsId(long value) {
      
      obsId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 障碍物ID
     * </pre>
     *
     * <code>uint64 obsId = 1;</code>
     */
    public Builder clearObsId() {
      
      obsId_ = 0L;
      onChanged();
      return this;
    }

    private int obsType_ = 0;
    /**
     * <pre>
     * 障碍物类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ObstaclesType obsType = 2;</code>
     */
    public int getObsTypeValue() {
      return obsType_;
    }
    /**
     * <pre>
     * 障碍物类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ObstaclesType obsType = 2;</code>
     */
    public Builder setObsTypeValue(int value) {
      obsType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 障碍物类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ObstaclesType obsType = 2;</code>
     */
    public road.data.proto.ObstaclesType getObsType() {
      @SuppressWarnings("deprecation")
      road.data.proto.ObstaclesType result = road.data.proto.ObstaclesType.valueOf(obsType_);
      return result == null ? road.data.proto.ObstaclesType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 障碍物类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ObstaclesType obsType = 2;</code>
     */
    public Builder setObsType(road.data.proto.ObstaclesType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      obsType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 障碍物类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ObstaclesType obsType = 2;</code>
     */
    public Builder clearObsType() {
      
      obsType_ = 0;
      onChanged();
      return this;
    }

    private int obstypeCfd_ ;
    /**
     * <pre>
     * 可选，定义障碍物类型的置信度;分辨率为0.005。
     * </pre>
     *
     * <code>uint32 obstypeCfd = 3;</code>
     */
    public int getObstypeCfd() {
      return obstypeCfd_;
    }
    /**
     * <pre>
     * 可选，定义障碍物类型的置信度;分辨率为0.005。
     * </pre>
     *
     * <code>uint32 obstypeCfd = 3;</code>
     */
    public Builder setObstypeCfd(int value) {
      
      obstypeCfd_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，定义障碍物类型的置信度;分辨率为0.005。
     * </pre>
     *
     * <code>uint32 obstypeCfd = 3;</code>
     */
    public Builder clearObstypeCfd() {
      
      obstypeCfd_ = 0;
      onChanged();
      return this;
    }

    private int obsSource_ = 0;
    /**
     * <pre>
     * 障碍物数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource obsSource = 4;</code>
     */
    public int getObsSourceValue() {
      return obsSource_;
    }
    /**
     * <pre>
     * 障碍物数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource obsSource = 4;</code>
     */
    public Builder setObsSourceValue(int value) {
      obsSource_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 障碍物数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource obsSource = 4;</code>
     */
    public road.data.proto.DataSource getObsSource() {
      @SuppressWarnings("deprecation")
      road.data.proto.DataSource result = road.data.proto.DataSource.valueOf(obsSource_);
      return result == null ? road.data.proto.DataSource.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 障碍物数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource obsSource = 4;</code>
     */
    public Builder setObsSource(road.data.proto.DataSource value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      obsSource_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 障碍物数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource obsSource = 4;</code>
     */
    public Builder clearObsSource() {
      
      obsSource_ = 0;
      onChanged();
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     *时间戳
     * </pre>
     *
     * <code>uint64 timestamp = 5;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     *时间戳
     * </pre>
     *
     * <code>uint64 timestamp = 5;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *时间戳
     * </pre>
     *
     * <code>uint64 timestamp = 5;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object deviceIdList_ = "";
    /**
     * <pre>
     *数据融合的来源设备id,json数组
     * </pre>
     *
     * <code>string deviceIdList = 6;</code>
     */
    public java.lang.String getDeviceIdList() {
      java.lang.Object ref = deviceIdList_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceIdList_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *数据融合的来源设备id,json数组
     * </pre>
     *
     * <code>string deviceIdList = 6;</code>
     */
    public com.google.protobuf.ByteString
        getDeviceIdListBytes() {
      java.lang.Object ref = deviceIdList_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceIdList_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *数据融合的来源设备id,json数组
     * </pre>
     *
     * <code>string deviceIdList = 6;</code>
     */
    public Builder setDeviceIdList(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deviceIdList_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *数据融合的来源设备id,json数组
     * </pre>
     *
     * <code>string deviceIdList = 6;</code>
     */
    public Builder clearDeviceIdList() {
      
      deviceIdList_ = getDefaultInstance().getDeviceIdList();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *数据融合的来源设备id,json数组
     * </pre>
     *
     * <code>string deviceIdList = 6;</code>
     */
    public Builder setDeviceIdListBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deviceIdList_ = value;
      onChanged();
      return this;
    }

    private road.data.proto.Position3D obsPos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> obsPosBuilder_;
    /**
     * <pre>
     * 定义障碍物经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
     */
    public boolean hasObsPos() {
      return obsPosBuilder_ != null || obsPos_ != null;
    }
    /**
     * <pre>
     * 定义障碍物经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
     */
    public road.data.proto.Position3D getObsPos() {
      if (obsPosBuilder_ == null) {
        return obsPos_ == null ? road.data.proto.Position3D.getDefaultInstance() : obsPos_;
      } else {
        return obsPosBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 定义障碍物经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
     */
    public Builder setObsPos(road.data.proto.Position3D value) {
      if (obsPosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        obsPos_ = value;
        onChanged();
      } else {
        obsPosBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 定义障碍物经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
     */
    public Builder setObsPos(
        road.data.proto.Position3D.Builder builderForValue) {
      if (obsPosBuilder_ == null) {
        obsPos_ = builderForValue.build();
        onChanged();
      } else {
        obsPosBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 定义障碍物经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
     */
    public Builder mergeObsPos(road.data.proto.Position3D value) {
      if (obsPosBuilder_ == null) {
        if (obsPos_ != null) {
          obsPos_ =
            road.data.proto.Position3D.newBuilder(obsPos_).mergeFrom(value).buildPartial();
        } else {
          obsPos_ = value;
        }
        onChanged();
      } else {
        obsPosBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 定义障碍物经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
     */
    public Builder clearObsPos() {
      if (obsPosBuilder_ == null) {
        obsPos_ = null;
        onChanged();
      } else {
        obsPos_ = null;
        obsPosBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 定义障碍物经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
     */
    public road.data.proto.Position3D.Builder getObsPosBuilder() {
      
      onChanged();
      return getObsPosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 定义障碍物经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
     */
    public road.data.proto.Position3DOrBuilder getObsPosOrBuilder() {
      if (obsPosBuilder_ != null) {
        return obsPosBuilder_.getMessageOrBuilder();
      } else {
        return obsPos_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : obsPos_;
      }
    }
    /**
     * <pre>
     * 定义障碍物经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getObsPosFieldBuilder() {
      if (obsPosBuilder_ == null) {
        obsPosBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getObsPos(),
                getParentForChildren(),
                isClean());
        obsPos_ = null;
      }
      return obsPosBuilder_;
    }

    private road.data.proto.PositionConfidenceSet posConfid_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder> posConfidBuilder_;
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
     */
    public boolean hasPosConfid() {
      return posConfidBuilder_ != null || posConfid_ != null;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
     */
    public road.data.proto.PositionConfidenceSet getPosConfid() {
      if (posConfidBuilder_ == null) {
        return posConfid_ == null ? road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
      } else {
        return posConfidBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
     */
    public Builder setPosConfid(road.data.proto.PositionConfidenceSet value) {
      if (posConfidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        posConfid_ = value;
        onChanged();
      } else {
        posConfidBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
     */
    public Builder setPosConfid(
        road.data.proto.PositionConfidenceSet.Builder builderForValue) {
      if (posConfidBuilder_ == null) {
        posConfid_ = builderForValue.build();
        onChanged();
      } else {
        posConfidBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
     */
    public Builder mergePosConfid(road.data.proto.PositionConfidenceSet value) {
      if (posConfidBuilder_ == null) {
        if (posConfid_ != null) {
          posConfid_ =
            road.data.proto.PositionConfidenceSet.newBuilder(posConfid_).mergeFrom(value).buildPartial();
        } else {
          posConfid_ = value;
        }
        onChanged();
      } else {
        posConfidBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
     */
    public Builder clearPosConfid() {
      if (posConfidBuilder_ == null) {
        posConfid_ = null;
        onChanged();
      } else {
        posConfid_ = null;
        posConfidBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
     */
    public road.data.proto.PositionConfidenceSet.Builder getPosConfidBuilder() {
      
      onChanged();
      return getPosConfidFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
     */
    public road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder() {
      if (posConfidBuilder_ != null) {
        return posConfidBuilder_.getMessageOrBuilder();
      } else {
        return posConfid_ == null ?
            road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
      }
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder> 
        getPosConfidFieldBuilder() {
      if (posConfidBuilder_ == null) {
        posConfidBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder>(
                getPosConfid(),
                getParentForChildren(),
                isClean());
        posConfid_ = null;
      }
      return posConfidBuilder_;
    }

    private road.data.proto.MapLocation mapLocation_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MapLocation, road.data.proto.MapLocation.Builder, road.data.proto.MapLocationOrBuilder> mapLocationBuilder_;
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
     */
    public boolean hasMapLocation() {
      return mapLocationBuilder_ != null || mapLocation_ != null;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
     */
    public road.data.proto.MapLocation getMapLocation() {
      if (mapLocationBuilder_ == null) {
        return mapLocation_ == null ? road.data.proto.MapLocation.getDefaultInstance() : mapLocation_;
      } else {
        return mapLocationBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
     */
    public Builder setMapLocation(road.data.proto.MapLocation value) {
      if (mapLocationBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        mapLocation_ = value;
        onChanged();
      } else {
        mapLocationBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
     */
    public Builder setMapLocation(
        road.data.proto.MapLocation.Builder builderForValue) {
      if (mapLocationBuilder_ == null) {
        mapLocation_ = builderForValue.build();
        onChanged();
      } else {
        mapLocationBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
     */
    public Builder mergeMapLocation(road.data.proto.MapLocation value) {
      if (mapLocationBuilder_ == null) {
        if (mapLocation_ != null) {
          mapLocation_ =
            road.data.proto.MapLocation.newBuilder(mapLocation_).mergeFrom(value).buildPartial();
        } else {
          mapLocation_ = value;
        }
        onChanged();
      } else {
        mapLocationBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
     */
    public Builder clearMapLocation() {
      if (mapLocationBuilder_ == null) {
        mapLocation_ = null;
        onChanged();
      } else {
        mapLocation_ = null;
        mapLocationBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
     */
    public road.data.proto.MapLocation.Builder getMapLocationBuilder() {
      
      onChanged();
      return getMapLocationFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
     */
    public road.data.proto.MapLocationOrBuilder getMapLocationOrBuilder() {
      if (mapLocationBuilder_ != null) {
        return mapLocationBuilder_.getMessageOrBuilder();
      } else {
        return mapLocation_ == null ?
            road.data.proto.MapLocation.getDefaultInstance() : mapLocation_;
      }
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MapLocation, road.data.proto.MapLocation.Builder, road.data.proto.MapLocationOrBuilder> 
        getMapLocationFieldBuilder() {
      if (mapLocationBuilder_ == null) {
        mapLocationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.MapLocation, road.data.proto.MapLocation.Builder, road.data.proto.MapLocationOrBuilder>(
                getMapLocation(),
                getParentForChildren(),
                isClean());
        mapLocation_ = null;
      }
      return mapLocationBuilder_;
    }

    private int speed_ ;
    /**
     * <pre>
     * 障碍物速度，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 speed = 10;</code>
     */
    public int getSpeed() {
      return speed_;
    }
    /**
     * <pre>
     * 障碍物速度，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 speed = 10;</code>
     */
    public Builder setSpeed(int value) {
      
      speed_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 障碍物速度，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 speed = 10;</code>
     */
    public Builder clearSpeed() {
      
      speed_ = 0;
      onChanged();
      return this;
    }

    private int heading_ ;
    /**
     * <pre>
     * 障碍物航向角，运行方向与正北方向的顺时针夹角。分辨率为0.0125°
     * </pre>
     *
     * <code>uint32 heading = 11;</code>
     */
    public int getHeading() {
      return heading_;
    }
    /**
     * <pre>
     * 障碍物航向角，运行方向与正北方向的顺时针夹角。分辨率为0.0125°
     * </pre>
     *
     * <code>uint32 heading = 11;</code>
     */
    public Builder setHeading(int value) {
      
      heading_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 障碍物航向角，运行方向与正北方向的顺时针夹角。分辨率为0.0125°
     * </pre>
     *
     * <code>uint32 heading = 11;</code>
     */
    public Builder clearHeading() {
      
      heading_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.MotionConfidenceSet motionConfid_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MotionConfidenceSet, road.data.proto.MotionConfidenceSet.Builder, road.data.proto.MotionConfidenceSetOrBuilder> motionConfidBuilder_;
    /**
     * <pre>
     *可选，运动状态置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public boolean hasMotionConfid() {
      return motionConfidBuilder_ != null || motionConfid_ != null;
    }
    /**
     * <pre>
     *可选，运动状态置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public road.data.proto.MotionConfidenceSet getMotionConfid() {
      if (motionConfidBuilder_ == null) {
        return motionConfid_ == null ? road.data.proto.MotionConfidenceSet.getDefaultInstance() : motionConfid_;
      } else {
        return motionConfidBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，运动状态置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public Builder setMotionConfid(road.data.proto.MotionConfidenceSet value) {
      if (motionConfidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        motionConfid_ = value;
        onChanged();
      } else {
        motionConfidBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，运动状态置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public Builder setMotionConfid(
        road.data.proto.MotionConfidenceSet.Builder builderForValue) {
      if (motionConfidBuilder_ == null) {
        motionConfid_ = builderForValue.build();
        onChanged();
      } else {
        motionConfidBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，运动状态置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public Builder mergeMotionConfid(road.data.proto.MotionConfidenceSet value) {
      if (motionConfidBuilder_ == null) {
        if (motionConfid_ != null) {
          motionConfid_ =
            road.data.proto.MotionConfidenceSet.newBuilder(motionConfid_).mergeFrom(value).buildPartial();
        } else {
          motionConfid_ = value;
        }
        onChanged();
      } else {
        motionConfidBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，运动状态置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public Builder clearMotionConfid() {
      if (motionConfidBuilder_ == null) {
        motionConfid_ = null;
        onChanged();
      } else {
        motionConfid_ = null;
        motionConfidBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，运动状态置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public road.data.proto.MotionConfidenceSet.Builder getMotionConfidBuilder() {
      
      onChanged();
      return getMotionConfidFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，运动状态置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public road.data.proto.MotionConfidenceSetOrBuilder getMotionConfidOrBuilder() {
      if (motionConfidBuilder_ != null) {
        return motionConfidBuilder_.getMessageOrBuilder();
      } else {
        return motionConfid_ == null ?
            road.data.proto.MotionConfidenceSet.getDefaultInstance() : motionConfid_;
      }
    }
    /**
     * <pre>
     *可选，运动状态置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MotionConfidenceSet, road.data.proto.MotionConfidenceSet.Builder, road.data.proto.MotionConfidenceSetOrBuilder> 
        getMotionConfidFieldBuilder() {
      if (motionConfidBuilder_ == null) {
        motionConfidBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.MotionConfidenceSet, road.data.proto.MotionConfidenceSet.Builder, road.data.proto.MotionConfidenceSetOrBuilder>(
                getMotionConfid(),
                getParentForChildren(),
                isClean());
        motionConfid_ = null;
      }
      return motionConfidBuilder_;
    }

    private int verSpeed_ ;
    /**
     * <pre>
     *可选，障碍物垂直速度，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 verSpeed = 13;</code>
     */
    public int getVerSpeed() {
      return verSpeed_;
    }
    /**
     * <pre>
     *可选，障碍物垂直速度，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 verSpeed = 13;</code>
     */
    public Builder setVerSpeed(int value) {
      
      verSpeed_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，障碍物垂直速度，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 verSpeed = 13;</code>
     */
    public Builder clearVerSpeed() {
      
      verSpeed_ = 0;
      onChanged();
      return this;
    }

    private int verSpeedConfid_ = 0;
    /**
     * <pre>
     * 可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence verSpeedConfid = 14;</code>
     */
    public int getVerSpeedConfidValue() {
      return verSpeedConfid_;
    }
    /**
     * <pre>
     * 可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence verSpeedConfid = 14;</code>
     */
    public Builder setVerSpeedConfidValue(int value) {
      verSpeedConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence verSpeedConfid = 14;</code>
     */
    public road.data.proto.SpeedConfidence getVerSpeedConfid() {
      @SuppressWarnings("deprecation")
      road.data.proto.SpeedConfidence result = road.data.proto.SpeedConfidence.valueOf(verSpeedConfid_);
      return result == null ? road.data.proto.SpeedConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence verSpeedConfid = 14;</code>
     */
    public Builder setVerSpeedConfid(road.data.proto.SpeedConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      verSpeedConfid_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence verSpeedConfid = 14;</code>
     */
    public Builder clearVerSpeedConfid() {
      
      verSpeedConfid_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.AccelerationSet4Way acceleration_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AccelerationSet4Way, road.data.proto.AccelerationSet4Way.Builder, road.data.proto.AccelerationSet4WayOrBuilder> accelerationBuilder_;
    /**
     * <pre>
     * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
     */
    public boolean hasAcceleration() {
      return accelerationBuilder_ != null || acceleration_ != null;
    }
    /**
     * <pre>
     * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
     */
    public road.data.proto.AccelerationSet4Way getAcceleration() {
      if (accelerationBuilder_ == null) {
        return acceleration_ == null ? road.data.proto.AccelerationSet4Way.getDefaultInstance() : acceleration_;
      } else {
        return accelerationBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
     */
    public Builder setAcceleration(road.data.proto.AccelerationSet4Way value) {
      if (accelerationBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        acceleration_ = value;
        onChanged();
      } else {
        accelerationBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
     */
    public Builder setAcceleration(
        road.data.proto.AccelerationSet4Way.Builder builderForValue) {
      if (accelerationBuilder_ == null) {
        acceleration_ = builderForValue.build();
        onChanged();
      } else {
        accelerationBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
     */
    public Builder mergeAcceleration(road.data.proto.AccelerationSet4Way value) {
      if (accelerationBuilder_ == null) {
        if (acceleration_ != null) {
          acceleration_ =
            road.data.proto.AccelerationSet4Way.newBuilder(acceleration_).mergeFrom(value).buildPartial();
        } else {
          acceleration_ = value;
        }
        onChanged();
      } else {
        accelerationBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
     */
    public Builder clearAcceleration() {
      if (accelerationBuilder_ == null) {
        acceleration_ = null;
        onChanged();
      } else {
        acceleration_ = null;
        accelerationBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
     */
    public road.data.proto.AccelerationSet4Way.Builder getAccelerationBuilder() {
      
      onChanged();
      return getAccelerationFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
     */
    public road.data.proto.AccelerationSet4WayOrBuilder getAccelerationOrBuilder() {
      if (accelerationBuilder_ != null) {
        return accelerationBuilder_.getMessageOrBuilder();
      } else {
        return acceleration_ == null ?
            road.data.proto.AccelerationSet4Way.getDefaultInstance() : acceleration_;
      }
    }
    /**
     * <pre>
     * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AccelerationSet4Way, road.data.proto.AccelerationSet4Way.Builder, road.data.proto.AccelerationSet4WayOrBuilder> 
        getAccelerationFieldBuilder() {
      if (accelerationBuilder_ == null) {
        accelerationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.AccelerationSet4Way, road.data.proto.AccelerationSet4Way.Builder, road.data.proto.AccelerationSet4WayOrBuilder>(
                getAcceleration(),
                getParentForChildren(),
                isClean());
        acceleration_ = null;
      }
      return accelerationBuilder_;
    }

    private road.data.proto.ParticipantSize size_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ParticipantSize, road.data.proto.ParticipantSize.Builder, road.data.proto.ParticipantSizeOrBuilder> sizeBuilder_;
    /**
     * <pre>
     * 障碍物尺寸大小
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
     */
    public boolean hasSize() {
      return sizeBuilder_ != null || size_ != null;
    }
    /**
     * <pre>
     * 障碍物尺寸大小
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
     */
    public road.data.proto.ParticipantSize getSize() {
      if (sizeBuilder_ == null) {
        return size_ == null ? road.data.proto.ParticipantSize.getDefaultInstance() : size_;
      } else {
        return sizeBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 障碍物尺寸大小
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
     */
    public Builder setSize(road.data.proto.ParticipantSize value) {
      if (sizeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        size_ = value;
        onChanged();
      } else {
        sizeBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 障碍物尺寸大小
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
     */
    public Builder setSize(
        road.data.proto.ParticipantSize.Builder builderForValue) {
      if (sizeBuilder_ == null) {
        size_ = builderForValue.build();
        onChanged();
      } else {
        sizeBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 障碍物尺寸大小
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
     */
    public Builder mergeSize(road.data.proto.ParticipantSize value) {
      if (sizeBuilder_ == null) {
        if (size_ != null) {
          size_ =
            road.data.proto.ParticipantSize.newBuilder(size_).mergeFrom(value).buildPartial();
        } else {
          size_ = value;
        }
        onChanged();
      } else {
        sizeBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 障碍物尺寸大小
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
     */
    public Builder clearSize() {
      if (sizeBuilder_ == null) {
        size_ = null;
        onChanged();
      } else {
        size_ = null;
        sizeBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 障碍物尺寸大小
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
     */
    public road.data.proto.ParticipantSize.Builder getSizeBuilder() {
      
      onChanged();
      return getSizeFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 障碍物尺寸大小
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
     */
    public road.data.proto.ParticipantSizeOrBuilder getSizeOrBuilder() {
      if (sizeBuilder_ != null) {
        return sizeBuilder_.getMessageOrBuilder();
      } else {
        return size_ == null ?
            road.data.proto.ParticipantSize.getDefaultInstance() : size_;
      }
    }
    /**
     * <pre>
     * 障碍物尺寸大小
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ParticipantSize, road.data.proto.ParticipantSize.Builder, road.data.proto.ParticipantSizeOrBuilder> 
        getSizeFieldBuilder() {
      if (sizeBuilder_ == null) {
        sizeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ParticipantSize, road.data.proto.ParticipantSize.Builder, road.data.proto.ParticipantSizeOrBuilder>(
                getSize(),
                getParentForChildren(),
                isClean());
        size_ = null;
      }
      return sizeBuilder_;
    }

    private road.data.proto.ParticipantSizeConfidence obsSizeConfid_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ParticipantSizeConfidence, road.data.proto.ParticipantSizeConfidence.Builder, road.data.proto.ParticipantSizeConfidenceOrBuilder> obsSizeConfidBuilder_;
    /**
     * <pre>
     * 可选，障碍物尺寸大小置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
     */
    public boolean hasObsSizeConfid() {
      return obsSizeConfidBuilder_ != null || obsSizeConfid_ != null;
    }
    /**
     * <pre>
     * 可选，障碍物尺寸大小置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
     */
    public road.data.proto.ParticipantSizeConfidence getObsSizeConfid() {
      if (obsSizeConfidBuilder_ == null) {
        return obsSizeConfid_ == null ? road.data.proto.ParticipantSizeConfidence.getDefaultInstance() : obsSizeConfid_;
      } else {
        return obsSizeConfidBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，障碍物尺寸大小置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
     */
    public Builder setObsSizeConfid(road.data.proto.ParticipantSizeConfidence value) {
      if (obsSizeConfidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        obsSizeConfid_ = value;
        onChanged();
      } else {
        obsSizeConfidBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，障碍物尺寸大小置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
     */
    public Builder setObsSizeConfid(
        road.data.proto.ParticipantSizeConfidence.Builder builderForValue) {
      if (obsSizeConfidBuilder_ == null) {
        obsSizeConfid_ = builderForValue.build();
        onChanged();
      } else {
        obsSizeConfidBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，障碍物尺寸大小置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
     */
    public Builder mergeObsSizeConfid(road.data.proto.ParticipantSizeConfidence value) {
      if (obsSizeConfidBuilder_ == null) {
        if (obsSizeConfid_ != null) {
          obsSizeConfid_ =
            road.data.proto.ParticipantSizeConfidence.newBuilder(obsSizeConfid_).mergeFrom(value).buildPartial();
        } else {
          obsSizeConfid_ = value;
        }
        onChanged();
      } else {
        obsSizeConfidBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，障碍物尺寸大小置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
     */
    public Builder clearObsSizeConfid() {
      if (obsSizeConfidBuilder_ == null) {
        obsSizeConfid_ = null;
        onChanged();
      } else {
        obsSizeConfid_ = null;
        obsSizeConfidBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，障碍物尺寸大小置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
     */
    public road.data.proto.ParticipantSizeConfidence.Builder getObsSizeConfidBuilder() {
      
      onChanged();
      return getObsSizeConfidFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，障碍物尺寸大小置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
     */
    public road.data.proto.ParticipantSizeConfidenceOrBuilder getObsSizeConfidOrBuilder() {
      if (obsSizeConfidBuilder_ != null) {
        return obsSizeConfidBuilder_.getMessageOrBuilder();
      } else {
        return obsSizeConfid_ == null ?
            road.data.proto.ParticipantSizeConfidence.getDefaultInstance() : obsSizeConfid_;
      }
    }
    /**
     * <pre>
     * 可选，障碍物尺寸大小置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ParticipantSizeConfidence, road.data.proto.ParticipantSizeConfidence.Builder, road.data.proto.ParticipantSizeConfidenceOrBuilder> 
        getObsSizeConfidFieldBuilder() {
      if (obsSizeConfidBuilder_ == null) {
        obsSizeConfidBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ParticipantSizeConfidence, road.data.proto.ParticipantSizeConfidence.Builder, road.data.proto.ParticipantSizeConfidenceOrBuilder>(
                getObsSizeConfid(),
                getParentForChildren(),
                isClean());
        obsSizeConfid_ = null;
      }
      return obsSizeConfidBuilder_;
    }

    private int tracking_ ;
    /**
     * <pre>
     * 可选，障碍物追踪时间，单位s
     * </pre>
     *
     * <code>uint32 tracking = 18;</code>
     */
    public int getTracking() {
      return tracking_;
    }
    /**
     * <pre>
     * 可选，障碍物追踪时间，单位s
     * </pre>
     *
     * <code>uint32 tracking = 18;</code>
     */
    public Builder setTracking(int value) {
      
      tracking_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，障碍物追踪时间，单位s
     * </pre>
     *
     * <code>uint32 tracking = 18;</code>
     */
    public Builder clearTracking() {
      
      tracking_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.Polygon polygon_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> polygonBuilder_;
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
     */
    public boolean hasPolygon() {
      return polygonBuilder_ != null || polygon_ != null;
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
     */
    public road.data.proto.Polygon getPolygon() {
      if (polygonBuilder_ == null) {
        return polygon_ == null ? road.data.proto.Polygon.getDefaultInstance() : polygon_;
      } else {
        return polygonBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
     */
    public Builder setPolygon(road.data.proto.Polygon value) {
      if (polygonBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        polygon_ = value;
        onChanged();
      } else {
        polygonBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
     */
    public Builder setPolygon(
        road.data.proto.Polygon.Builder builderForValue) {
      if (polygonBuilder_ == null) {
        polygon_ = builderForValue.build();
        onChanged();
      } else {
        polygonBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
     */
    public Builder mergePolygon(road.data.proto.Polygon value) {
      if (polygonBuilder_ == null) {
        if (polygon_ != null) {
          polygon_ =
            road.data.proto.Polygon.newBuilder(polygon_).mergeFrom(value).buildPartial();
        } else {
          polygon_ = value;
        }
        onChanged();
      } else {
        polygonBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
     */
    public Builder clearPolygon() {
      if (polygonBuilder_ == null) {
        polygon_ = null;
        onChanged();
      } else {
        polygon_ = null;
        polygonBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
     */
    public road.data.proto.Polygon.Builder getPolygonBuilder() {
      
      onChanged();
      return getPolygonFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
     */
    public road.data.proto.PolygonOrBuilder getPolygonOrBuilder() {
      if (polygonBuilder_ != null) {
        return polygonBuilder_.getMessageOrBuilder();
      } else {
        return polygon_ == null ?
            road.data.proto.Polygon.getDefaultInstance() : polygon_;
      }
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> 
        getPolygonFieldBuilder() {
      if (polygonBuilder_ == null) {
        polygonBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder>(
                getPolygon(),
                getParentForChildren(),
                isClean());
        polygon_ = null;
      }
      return polygonBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ObstacleData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ObstacleData)
  private static final road.data.proto.ObstacleData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ObstacleData();
  }

  public static road.data.proto.ObstacleData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ObstacleData>
      PARSER = new com.google.protobuf.AbstractParser<ObstacleData>() {
    @java.lang.Override
    public ObstacleData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ObstacleData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ObstacleData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ObstacleData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ObstacleData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

