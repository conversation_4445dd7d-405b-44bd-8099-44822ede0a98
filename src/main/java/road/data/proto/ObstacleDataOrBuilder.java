// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ObstacleDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ObstacleData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 障碍物ID
   * </pre>
   *
   * <code>uint64 obsId = 1;</code>
   */
  long getObsId();

  /**
   * <pre>
   * 障碍物类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ObstaclesType obsType = 2;</code>
   */
  int getObsTypeValue();
  /**
   * <pre>
   * 障碍物类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ObstaclesType obsType = 2;</code>
   */
  road.data.proto.ObstaclesType getObsType();

  /**
   * <pre>
   * 可选，定义障碍物类型的置信度;分辨率为0.005。
   * </pre>
   *
   * <code>uint32 obstypeCfd = 3;</code>
   */
  int getObstypeCfd();

  /**
   * <pre>
   * 障碍物数据来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource obsSource = 4;</code>
   */
  int getObsSourceValue();
  /**
   * <pre>
   * 障碍物数据来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource obsSource = 4;</code>
   */
  road.data.proto.DataSource getObsSource();

  /**
   * <pre>
   *时间戳
   * </pre>
   *
   * <code>uint64 timestamp = 5;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   *数据融合的来源设备id,json数组
   * </pre>
   *
   * <code>string deviceIdList = 6;</code>
   */
  java.lang.String getDeviceIdList();
  /**
   * <pre>
   *数据融合的来源设备id,json数组
   * </pre>
   *
   * <code>string deviceIdList = 6;</code>
   */
  com.google.protobuf.ByteString
      getDeviceIdListBytes();

  /**
   * <pre>
   * 定义障碍物经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
   */
  boolean hasObsPos();
  /**
   * <pre>
   * 定义障碍物经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
   */
  road.data.proto.Position3D getObsPos();
  /**
   * <pre>
   * 定义障碍物经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D obsPos = 7;</code>
   */
  road.data.proto.Position3DOrBuilder getObsPosOrBuilder();

  /**
   * <pre>
   * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
   */
  boolean hasPosConfid();
  /**
   * <pre>
   * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
   */
  road.data.proto.PositionConfidenceSet getPosConfid();
  /**
   * <pre>
   * 可选，定义95%置信水平的障碍物位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 8;</code>
   */
  road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder();

  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
   */
  boolean hasMapLocation();
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
   */
  road.data.proto.MapLocation getMapLocation();
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 9;</code>
   */
  road.data.proto.MapLocationOrBuilder getMapLocationOrBuilder();

  /**
   * <pre>
   * 障碍物速度，分辨率为0.02m/s，数值8191表示无效数值
   * </pre>
   *
   * <code>uint32 speed = 10;</code>
   */
  int getSpeed();

  /**
   * <pre>
   * 障碍物航向角，运行方向与正北方向的顺时针夹角。分辨率为0.0125°
   * </pre>
   *
   * <code>uint32 heading = 11;</code>
   */
  int getHeading();

  /**
   * <pre>
   *可选，运动状态置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  boolean hasMotionConfid();
  /**
   * <pre>
   *可选，运动状态置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  road.data.proto.MotionConfidenceSet getMotionConfid();
  /**
   * <pre>
   *可选，运动状态置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  road.data.proto.MotionConfidenceSetOrBuilder getMotionConfidOrBuilder();

  /**
   * <pre>
   *可选，障碍物垂直速度，分辨率为0.02m/s，数值8191表示无效数值
   * </pre>
   *
   * <code>uint32 verSpeed = 13;</code>
   */
  int getVerSpeed();

  /**
   * <pre>
   * 可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpeedConfidence verSpeedConfid = 14;</code>
   */
  int getVerSpeedConfidValue();
  /**
   * <pre>
   * 可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpeedConfidence verSpeedConfid = 14;</code>
   */
  road.data.proto.SpeedConfidence getVerSpeedConfid();

  /**
   * <pre>
   * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
   */
  boolean hasAcceleration();
  /**
   * <pre>
   * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
   */
  road.data.proto.AccelerationSet4Way getAcceleration();
  /**
   * <pre>
   * 可选，定义四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 15;</code>
   */
  road.data.proto.AccelerationSet4WayOrBuilder getAccelerationOrBuilder();

  /**
   * <pre>
   * 障碍物尺寸大小
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
   */
  boolean hasSize();
  /**
   * <pre>
   * 障碍物尺寸大小
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
   */
  road.data.proto.ParticipantSize getSize();
  /**
   * <pre>
   * 障碍物尺寸大小
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSize size = 16;</code>
   */
  road.data.proto.ParticipantSizeOrBuilder getSizeOrBuilder();

  /**
   * <pre>
   * 可选，障碍物尺寸大小置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
   */
  boolean hasObsSizeConfid();
  /**
   * <pre>
   * 可选，障碍物尺寸大小置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
   */
  road.data.proto.ParticipantSizeConfidence getObsSizeConfid();
  /**
   * <pre>
   * 可选，障碍物尺寸大小置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence obsSizeConfid = 17;</code>
   */
  road.data.proto.ParticipantSizeConfidenceOrBuilder getObsSizeConfidOrBuilder();

  /**
   * <pre>
   * 可选，障碍物追踪时间，单位s
   * </pre>
   *
   * <code>uint32 tracking = 18;</code>
   */
  int getTracking();

  /**
   * <pre>
   * 可选，障碍物影响区域点集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
   */
  boolean hasPolygon();
  /**
   * <pre>
   * 可选，障碍物影响区域点集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
   */
  road.data.proto.Polygon getPolygon();
  /**
   * <pre>
   * 可选，障碍物影响区域点集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 19;</code>
   */
  road.data.proto.PolygonOrBuilder getPolygonOrBuilder();
}
