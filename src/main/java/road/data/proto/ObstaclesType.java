// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *障碍物类型     
 * </pre>
 *
 * Protobuf enum {@code cn.seisys.v2x.pb.ObstaclesType}
 */
public enum ObstaclesType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *未知 ;
   * </pre>
   *
   * <code>UNKNOWN_OBSTACLES_TYPE = 0;</code>
   */
  UNKNOWN_OBSTACLES_TYPE(0),
  /**
   * <pre>
   *崩岩;
   * </pre>
   *
   * <code>ROCKFALL = 1;</code>
   */
  ROCKFALL(1),
  /**
   * <pre>
   *滑坡;
   * </pre>
   *
   * <code>LANDSLIDE = 2;</code>
   */
  LANDSLIDE(2),
  /**
   * <pre>
   *动物入侵;
   * </pre>
   *
   * <code>ANIMAL_INTRUSION = 3;</code>
   */
  ANIMAL_INTRUSION(3),
  /**
   * <pre>
   *液体溢出;
   * </pre>
   *
   * <code>LIQUID_SPILL = 4;</code>
   */
  LIQUID_SPILL(4),
  /**
   * <pre>
   *货物散落;
   * </pre>
   *
   * <code>GOODS_SCATTERED = 5;</code>
   */
  GOODS_SCATTERED(5),
  /**
   * <pre>
   *交通;
   * </pre>
   *
   * <code>TRAFFICCONE = 6;</code>
   */
  TRAFFICCONE(6),
  /**
   * <pre>
   *三角牌;
   * </pre>
   *
   * <code>SAFETY_TRIANGLE = 7;</code>
   */
  SAFETY_TRIANGLE(7),
  /**
   * <pre>
   *交通路障;
   * </pre>
   *
   * <code>TRAFFIC_ROADBLOCK = 8;</code>
   */
  TRAFFIC_ROADBLOCK(8),
  /**
   * <pre>
   * 无盖井;
   * </pre>
   *
   * <code>INSPECTION_SHAFT_WITHOUT_COVER = 9;</code>
   */
  INSPECTION_SHAFT_WITHOUT_COVER(9),
  /**
   * <pre>
   *未知碎片;
   * </pre>
   *
   * <code>UNKNOWN_FRAGMENTS = 10;</code>
   */
  UNKNOWN_FRAGMENTS(10),
  /**
   * <pre>
   *未知硬物体;
   * </pre>
   *
   * <code>UNKNOWN_HARD_OBJECT = 11;</code>
   */
  UNKNOWN_HARD_OBJECT(11),
  /**
   * <pre>
   *未知软物体;
   * </pre>
   *
   * <code>UNKNOWN_SOFT_OBJECT = 12;</code>
   */
  UNKNOWN_SOFT_OBJECT(12),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *未知 ;
   * </pre>
   *
   * <code>UNKNOWN_OBSTACLES_TYPE = 0;</code>
   */
  public static final int UNKNOWN_OBSTACLES_TYPE_VALUE = 0;
  /**
   * <pre>
   *崩岩;
   * </pre>
   *
   * <code>ROCKFALL = 1;</code>
   */
  public static final int ROCKFALL_VALUE = 1;
  /**
   * <pre>
   *滑坡;
   * </pre>
   *
   * <code>LANDSLIDE = 2;</code>
   */
  public static final int LANDSLIDE_VALUE = 2;
  /**
   * <pre>
   *动物入侵;
   * </pre>
   *
   * <code>ANIMAL_INTRUSION = 3;</code>
   */
  public static final int ANIMAL_INTRUSION_VALUE = 3;
  /**
   * <pre>
   *液体溢出;
   * </pre>
   *
   * <code>LIQUID_SPILL = 4;</code>
   */
  public static final int LIQUID_SPILL_VALUE = 4;
  /**
   * <pre>
   *货物散落;
   * </pre>
   *
   * <code>GOODS_SCATTERED = 5;</code>
   */
  public static final int GOODS_SCATTERED_VALUE = 5;
  /**
   * <pre>
   *交通;
   * </pre>
   *
   * <code>TRAFFICCONE = 6;</code>
   */
  public static final int TRAFFICCONE_VALUE = 6;
  /**
   * <pre>
   *三角牌;
   * </pre>
   *
   * <code>SAFETY_TRIANGLE = 7;</code>
   */
  public static final int SAFETY_TRIANGLE_VALUE = 7;
  /**
   * <pre>
   *交通路障;
   * </pre>
   *
   * <code>TRAFFIC_ROADBLOCK = 8;</code>
   */
  public static final int TRAFFIC_ROADBLOCK_VALUE = 8;
  /**
   * <pre>
   * 无盖井;
   * </pre>
   *
   * <code>INSPECTION_SHAFT_WITHOUT_COVER = 9;</code>
   */
  public static final int INSPECTION_SHAFT_WITHOUT_COVER_VALUE = 9;
  /**
   * <pre>
   *未知碎片;
   * </pre>
   *
   * <code>UNKNOWN_FRAGMENTS = 10;</code>
   */
  public static final int UNKNOWN_FRAGMENTS_VALUE = 10;
  /**
   * <pre>
   *未知硬物体;
   * </pre>
   *
   * <code>UNKNOWN_HARD_OBJECT = 11;</code>
   */
  public static final int UNKNOWN_HARD_OBJECT_VALUE = 11;
  /**
   * <pre>
   *未知软物体;
   * </pre>
   *
   * <code>UNKNOWN_SOFT_OBJECT = 12;</code>
   */
  public static final int UNKNOWN_SOFT_OBJECT_VALUE = 12;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static ObstaclesType valueOf(int value) {
    return forNumber(value);
  }

  public static ObstaclesType forNumber(int value) {
    switch (value) {
      case 0: return UNKNOWN_OBSTACLES_TYPE;
      case 1: return ROCKFALL;
      case 2: return LANDSLIDE;
      case 3: return ANIMAL_INTRUSION;
      case 4: return LIQUID_SPILL;
      case 5: return GOODS_SCATTERED;
      case 6: return TRAFFICCONE;
      case 7: return SAFETY_TRIANGLE;
      case 8: return TRAFFIC_ROADBLOCK;
      case 9: return INSPECTION_SHAFT_WITHOUT_COVER;
      case 10: return UNKNOWN_FRAGMENTS;
      case 11: return UNKNOWN_HARD_OBJECT;
      case 12: return UNKNOWN_SOFT_OBJECT;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<ObstaclesType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      ObstaclesType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<ObstaclesType>() {
          public ObstaclesType findValueByNumber(int number) {
            return ObstaclesType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return road.data.proto.V2X.getDescriptor().getEnumTypes().get(10);
  }

  private static final ObstaclesType[] VALUES = values();

  public static ObstaclesType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private ObstaclesType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.ObstaclesType)
}

