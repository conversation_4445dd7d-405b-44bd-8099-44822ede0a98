// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *优化时段建议      
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.OptimData}
 */
public  final class OptimData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.OptimData)
    OptimDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use OptimData.newBuilder() to construct.
  private OptimData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private OptimData() {
    optimPhaseList_ = java.util.Collections.emptyList();
    coorPhase_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new OptimData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private OptimData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.OptimTimeType.Builder subBuilder = null;
            if (optimTimeType_ != null) {
              subBuilder = optimTimeType_.toBuilder();
            }
            optimTimeType_ = input.readMessage(road.data.proto.OptimTimeType.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(optimTimeType_);
              optimTimeType_ = subBuilder.buildPartial();
            }

            break;
          }
          case 16: {

            optimCycleTime_ = input.readUInt32();
            break;
          }
          case 24: {

            minCycleTime_ = input.readUInt32();
            break;
          }
          case 32: {

            maxCycleTime_ = input.readUInt32();
            break;
          }
          case 42: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              optimPhaseList_ = new java.util.ArrayList<road.data.proto.OptimPhase>();
              mutable_bitField0_ |= 0x00000001;
            }
            optimPhaseList_.add(
                input.readMessage(road.data.proto.OptimPhase.parser(), extensionRegistry));
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            coorPhase_ = s;
            break;
          }
          case 56: {

            offset_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        optimPhaseList_ = java.util.Collections.unmodifiableList(optimPhaseList_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.OptimData.class, road.data.proto.OptimData.Builder.class);
  }

  public static final int OPTIMTIMETYPE_FIELD_NUMBER = 1;
  private road.data.proto.OptimTimeType optimTimeType_;
  /**
   * <pre>
   *优化时段类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
   */
  public boolean hasOptimTimeType() {
    return optimTimeType_ != null;
  }
  /**
   * <pre>
   *优化时段类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
   */
  public road.data.proto.OptimTimeType getOptimTimeType() {
    return optimTimeType_ == null ? road.data.proto.OptimTimeType.getDefaultInstance() : optimTimeType_;
  }
  /**
   * <pre>
   *优化时段类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
   */
  public road.data.proto.OptimTimeTypeOrBuilder getOptimTimeTypeOrBuilder() {
    return getOptimTimeType();
  }

  public static final int OPTIMCYCLETIME_FIELD_NUMBER = 2;
  private int optimCycleTime_;
  /**
   * <pre>
   * 可选，优化周期长度
   * </pre>
   *
   * <code>uint32 optimCycleTime = 2;</code>
   */
  public int getOptimCycleTime() {
    return optimCycleTime_;
  }

  public static final int MINCYCLETIME_FIELD_NUMBER = 3;
  private int minCycleTime_;
  /**
   * <pre>
   * 可选，优化最小周期长度约束单位 秒
   * </pre>
   *
   * <code>uint32 minCycleTime = 3;</code>
   */
  public int getMinCycleTime() {
    return minCycleTime_;
  }

  public static final int MAXCYCLETIME_FIELD_NUMBER = 4;
  private int maxCycleTime_;
  /**
   * <pre>
   * 可选，优化最小周期长度约束单位 秒
   * </pre>
   *
   * <code>uint32 maxCycleTime = 4;</code>
   */
  public int getMaxCycleTime() {
    return maxCycleTime_;
  }

  public static final int OPTIMPHASELIST_FIELD_NUMBER = 5;
  private java.util.List<road.data.proto.OptimPhase> optimPhaseList_;
  /**
   * <pre>
   * 可选，优化后相位信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
   */
  public java.util.List<road.data.proto.OptimPhase> getOptimPhaseListList() {
    return optimPhaseList_;
  }
  /**
   * <pre>
   * 可选，优化后相位信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
   */
  public java.util.List<? extends road.data.proto.OptimPhaseOrBuilder> 
      getOptimPhaseListOrBuilderList() {
    return optimPhaseList_;
  }
  /**
   * <pre>
   * 可选，优化后相位信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
   */
  public int getOptimPhaseListCount() {
    return optimPhaseList_.size();
  }
  /**
   * <pre>
   * 可选，优化后相位信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
   */
  public road.data.proto.OptimPhase getOptimPhaseList(int index) {
    return optimPhaseList_.get(index);
  }
  /**
   * <pre>
   * 可选，优化后相位信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
   */
  public road.data.proto.OptimPhaseOrBuilder getOptimPhaseListOrBuilder(
      int index) {
    return optimPhaseList_.get(index);
  }

  public static final int COORPHASE_FIELD_NUMBER = 6;
  private volatile java.lang.Object coorPhase_;
  /**
   * <pre>
   * 可选，协调相位编号
   * </pre>
   *
   * <code>string coorPhase = 6;</code>
   */
  public java.lang.String getCoorPhase() {
    java.lang.Object ref = coorPhase_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      coorPhase_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 可选，协调相位编号
   * </pre>
   *
   * <code>string coorPhase = 6;</code>
   */
  public com.google.protobuf.ByteString
      getCoorPhaseBytes() {
    java.lang.Object ref = coorPhase_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      coorPhase_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int OFFSET_FIELD_NUMBER = 7;
  private int offset_;
  /**
   * <pre>
   * 可选，协调相位差.
   * </pre>
   *
   * <code>uint32 offset = 7;</code>
   */
  public int getOffset() {
    return offset_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (optimTimeType_ != null) {
      output.writeMessage(1, getOptimTimeType());
    }
    if (optimCycleTime_ != 0) {
      output.writeUInt32(2, optimCycleTime_);
    }
    if (minCycleTime_ != 0) {
      output.writeUInt32(3, minCycleTime_);
    }
    if (maxCycleTime_ != 0) {
      output.writeUInt32(4, maxCycleTime_);
    }
    for (int i = 0; i < optimPhaseList_.size(); i++) {
      output.writeMessage(5, optimPhaseList_.get(i));
    }
    if (!getCoorPhaseBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, coorPhase_);
    }
    if (offset_ != 0) {
      output.writeUInt32(7, offset_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (optimTimeType_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getOptimTimeType());
    }
    if (optimCycleTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, optimCycleTime_);
    }
    if (minCycleTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, minCycleTime_);
    }
    if (maxCycleTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, maxCycleTime_);
    }
    for (int i = 0; i < optimPhaseList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, optimPhaseList_.get(i));
    }
    if (!getCoorPhaseBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, coorPhase_);
    }
    if (offset_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(7, offset_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.OptimData)) {
      return super.equals(obj);
    }
    road.data.proto.OptimData other = (road.data.proto.OptimData) obj;

    if (hasOptimTimeType() != other.hasOptimTimeType()) return false;
    if (hasOptimTimeType()) {
      if (!getOptimTimeType()
          .equals(other.getOptimTimeType())) return false;
    }
    if (getOptimCycleTime()
        != other.getOptimCycleTime()) return false;
    if (getMinCycleTime()
        != other.getMinCycleTime()) return false;
    if (getMaxCycleTime()
        != other.getMaxCycleTime()) return false;
    if (!getOptimPhaseListList()
        .equals(other.getOptimPhaseListList())) return false;
    if (!getCoorPhase()
        .equals(other.getCoorPhase())) return false;
    if (getOffset()
        != other.getOffset()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasOptimTimeType()) {
      hash = (37 * hash) + OPTIMTIMETYPE_FIELD_NUMBER;
      hash = (53 * hash) + getOptimTimeType().hashCode();
    }
    hash = (37 * hash) + OPTIMCYCLETIME_FIELD_NUMBER;
    hash = (53 * hash) + getOptimCycleTime();
    hash = (37 * hash) + MINCYCLETIME_FIELD_NUMBER;
    hash = (53 * hash) + getMinCycleTime();
    hash = (37 * hash) + MAXCYCLETIME_FIELD_NUMBER;
    hash = (53 * hash) + getMaxCycleTime();
    if (getOptimPhaseListCount() > 0) {
      hash = (37 * hash) + OPTIMPHASELIST_FIELD_NUMBER;
      hash = (53 * hash) + getOptimPhaseListList().hashCode();
    }
    hash = (37 * hash) + COORPHASE_FIELD_NUMBER;
    hash = (53 * hash) + getCoorPhase().hashCode();
    hash = (37 * hash) + OFFSET_FIELD_NUMBER;
    hash = (53 * hash) + getOffset();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.OptimData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.OptimData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.OptimData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.OptimData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.OptimData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.OptimData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.OptimData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.OptimData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.OptimData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.OptimData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.OptimData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.OptimData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.OptimData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *优化时段建议      
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.OptimData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.OptimData)
      road.data.proto.OptimDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.OptimData.class, road.data.proto.OptimData.Builder.class);
    }

    // Construct using road.data.proto.OptimData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getOptimPhaseListFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (optimTimeTypeBuilder_ == null) {
        optimTimeType_ = null;
      } else {
        optimTimeType_ = null;
        optimTimeTypeBuilder_ = null;
      }
      optimCycleTime_ = 0;

      minCycleTime_ = 0;

      maxCycleTime_ = 0;

      if (optimPhaseListBuilder_ == null) {
        optimPhaseList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        optimPhaseListBuilder_.clear();
      }
      coorPhase_ = "";

      offset_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.OptimData getDefaultInstanceForType() {
      return road.data.proto.OptimData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.OptimData build() {
      road.data.proto.OptimData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.OptimData buildPartial() {
      road.data.proto.OptimData result = new road.data.proto.OptimData(this);
      int from_bitField0_ = bitField0_;
      if (optimTimeTypeBuilder_ == null) {
        result.optimTimeType_ = optimTimeType_;
      } else {
        result.optimTimeType_ = optimTimeTypeBuilder_.build();
      }
      result.optimCycleTime_ = optimCycleTime_;
      result.minCycleTime_ = minCycleTime_;
      result.maxCycleTime_ = maxCycleTime_;
      if (optimPhaseListBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          optimPhaseList_ = java.util.Collections.unmodifiableList(optimPhaseList_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.optimPhaseList_ = optimPhaseList_;
      } else {
        result.optimPhaseList_ = optimPhaseListBuilder_.build();
      }
      result.coorPhase_ = coorPhase_;
      result.offset_ = offset_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.OptimData) {
        return mergeFrom((road.data.proto.OptimData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.OptimData other) {
      if (other == road.data.proto.OptimData.getDefaultInstance()) return this;
      if (other.hasOptimTimeType()) {
        mergeOptimTimeType(other.getOptimTimeType());
      }
      if (other.getOptimCycleTime() != 0) {
        setOptimCycleTime(other.getOptimCycleTime());
      }
      if (other.getMinCycleTime() != 0) {
        setMinCycleTime(other.getMinCycleTime());
      }
      if (other.getMaxCycleTime() != 0) {
        setMaxCycleTime(other.getMaxCycleTime());
      }
      if (optimPhaseListBuilder_ == null) {
        if (!other.optimPhaseList_.isEmpty()) {
          if (optimPhaseList_.isEmpty()) {
            optimPhaseList_ = other.optimPhaseList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureOptimPhaseListIsMutable();
            optimPhaseList_.addAll(other.optimPhaseList_);
          }
          onChanged();
        }
      } else {
        if (!other.optimPhaseList_.isEmpty()) {
          if (optimPhaseListBuilder_.isEmpty()) {
            optimPhaseListBuilder_.dispose();
            optimPhaseListBuilder_ = null;
            optimPhaseList_ = other.optimPhaseList_;
            bitField0_ = (bitField0_ & ~0x00000001);
            optimPhaseListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getOptimPhaseListFieldBuilder() : null;
          } else {
            optimPhaseListBuilder_.addAllMessages(other.optimPhaseList_);
          }
        }
      }
      if (!other.getCoorPhase().isEmpty()) {
        coorPhase_ = other.coorPhase_;
        onChanged();
      }
      if (other.getOffset() != 0) {
        setOffset(other.getOffset());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.OptimData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.OptimData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private road.data.proto.OptimTimeType optimTimeType_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.OptimTimeType, road.data.proto.OptimTimeType.Builder, road.data.proto.OptimTimeTypeOrBuilder> optimTimeTypeBuilder_;
    /**
     * <pre>
     *优化时段类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
     */
    public boolean hasOptimTimeType() {
      return optimTimeTypeBuilder_ != null || optimTimeType_ != null;
    }
    /**
     * <pre>
     *优化时段类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
     */
    public road.data.proto.OptimTimeType getOptimTimeType() {
      if (optimTimeTypeBuilder_ == null) {
        return optimTimeType_ == null ? road.data.proto.OptimTimeType.getDefaultInstance() : optimTimeType_;
      } else {
        return optimTimeTypeBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *优化时段类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
     */
    public Builder setOptimTimeType(road.data.proto.OptimTimeType value) {
      if (optimTimeTypeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        optimTimeType_ = value;
        onChanged();
      } else {
        optimTimeTypeBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *优化时段类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
     */
    public Builder setOptimTimeType(
        road.data.proto.OptimTimeType.Builder builderForValue) {
      if (optimTimeTypeBuilder_ == null) {
        optimTimeType_ = builderForValue.build();
        onChanged();
      } else {
        optimTimeTypeBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *优化时段类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
     */
    public Builder mergeOptimTimeType(road.data.proto.OptimTimeType value) {
      if (optimTimeTypeBuilder_ == null) {
        if (optimTimeType_ != null) {
          optimTimeType_ =
            road.data.proto.OptimTimeType.newBuilder(optimTimeType_).mergeFrom(value).buildPartial();
        } else {
          optimTimeType_ = value;
        }
        onChanged();
      } else {
        optimTimeTypeBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *优化时段类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
     */
    public Builder clearOptimTimeType() {
      if (optimTimeTypeBuilder_ == null) {
        optimTimeType_ = null;
        onChanged();
      } else {
        optimTimeType_ = null;
        optimTimeTypeBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *优化时段类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
     */
    public road.data.proto.OptimTimeType.Builder getOptimTimeTypeBuilder() {
      
      onChanged();
      return getOptimTimeTypeFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *优化时段类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
     */
    public road.data.proto.OptimTimeTypeOrBuilder getOptimTimeTypeOrBuilder() {
      if (optimTimeTypeBuilder_ != null) {
        return optimTimeTypeBuilder_.getMessageOrBuilder();
      } else {
        return optimTimeType_ == null ?
            road.data.proto.OptimTimeType.getDefaultInstance() : optimTimeType_;
      }
    }
    /**
     * <pre>
     *优化时段类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.OptimTimeType, road.data.proto.OptimTimeType.Builder, road.data.proto.OptimTimeTypeOrBuilder> 
        getOptimTimeTypeFieldBuilder() {
      if (optimTimeTypeBuilder_ == null) {
        optimTimeTypeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.OptimTimeType, road.data.proto.OptimTimeType.Builder, road.data.proto.OptimTimeTypeOrBuilder>(
                getOptimTimeType(),
                getParentForChildren(),
                isClean());
        optimTimeType_ = null;
      }
      return optimTimeTypeBuilder_;
    }

    private int optimCycleTime_ ;
    /**
     * <pre>
     * 可选，优化周期长度
     * </pre>
     *
     * <code>uint32 optimCycleTime = 2;</code>
     */
    public int getOptimCycleTime() {
      return optimCycleTime_;
    }
    /**
     * <pre>
     * 可选，优化周期长度
     * </pre>
     *
     * <code>uint32 optimCycleTime = 2;</code>
     */
    public Builder setOptimCycleTime(int value) {
      
      optimCycleTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，优化周期长度
     * </pre>
     *
     * <code>uint32 optimCycleTime = 2;</code>
     */
    public Builder clearOptimCycleTime() {
      
      optimCycleTime_ = 0;
      onChanged();
      return this;
    }

    private int minCycleTime_ ;
    /**
     * <pre>
     * 可选，优化最小周期长度约束单位 秒
     * </pre>
     *
     * <code>uint32 minCycleTime = 3;</code>
     */
    public int getMinCycleTime() {
      return minCycleTime_;
    }
    /**
     * <pre>
     * 可选，优化最小周期长度约束单位 秒
     * </pre>
     *
     * <code>uint32 minCycleTime = 3;</code>
     */
    public Builder setMinCycleTime(int value) {
      
      minCycleTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，优化最小周期长度约束单位 秒
     * </pre>
     *
     * <code>uint32 minCycleTime = 3;</code>
     */
    public Builder clearMinCycleTime() {
      
      minCycleTime_ = 0;
      onChanged();
      return this;
    }

    private int maxCycleTime_ ;
    /**
     * <pre>
     * 可选，优化最小周期长度约束单位 秒
     * </pre>
     *
     * <code>uint32 maxCycleTime = 4;</code>
     */
    public int getMaxCycleTime() {
      return maxCycleTime_;
    }
    /**
     * <pre>
     * 可选，优化最小周期长度约束单位 秒
     * </pre>
     *
     * <code>uint32 maxCycleTime = 4;</code>
     */
    public Builder setMaxCycleTime(int value) {
      
      maxCycleTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，优化最小周期长度约束单位 秒
     * </pre>
     *
     * <code>uint32 maxCycleTime = 4;</code>
     */
    public Builder clearMaxCycleTime() {
      
      maxCycleTime_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.OptimPhase> optimPhaseList_ =
      java.util.Collections.emptyList();
    private void ensureOptimPhaseListIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        optimPhaseList_ = new java.util.ArrayList<road.data.proto.OptimPhase>(optimPhaseList_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.OptimPhase, road.data.proto.OptimPhase.Builder, road.data.proto.OptimPhaseOrBuilder> optimPhaseListBuilder_;

    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public java.util.List<road.data.proto.OptimPhase> getOptimPhaseListList() {
      if (optimPhaseListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(optimPhaseList_);
      } else {
        return optimPhaseListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public int getOptimPhaseListCount() {
      if (optimPhaseListBuilder_ == null) {
        return optimPhaseList_.size();
      } else {
        return optimPhaseListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public road.data.proto.OptimPhase getOptimPhaseList(int index) {
      if (optimPhaseListBuilder_ == null) {
        return optimPhaseList_.get(index);
      } else {
        return optimPhaseListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public Builder setOptimPhaseList(
        int index, road.data.proto.OptimPhase value) {
      if (optimPhaseListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOptimPhaseListIsMutable();
        optimPhaseList_.set(index, value);
        onChanged();
      } else {
        optimPhaseListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public Builder setOptimPhaseList(
        int index, road.data.proto.OptimPhase.Builder builderForValue) {
      if (optimPhaseListBuilder_ == null) {
        ensureOptimPhaseListIsMutable();
        optimPhaseList_.set(index, builderForValue.build());
        onChanged();
      } else {
        optimPhaseListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public Builder addOptimPhaseList(road.data.proto.OptimPhase value) {
      if (optimPhaseListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOptimPhaseListIsMutable();
        optimPhaseList_.add(value);
        onChanged();
      } else {
        optimPhaseListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public Builder addOptimPhaseList(
        int index, road.data.proto.OptimPhase value) {
      if (optimPhaseListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOptimPhaseListIsMutable();
        optimPhaseList_.add(index, value);
        onChanged();
      } else {
        optimPhaseListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public Builder addOptimPhaseList(
        road.data.proto.OptimPhase.Builder builderForValue) {
      if (optimPhaseListBuilder_ == null) {
        ensureOptimPhaseListIsMutable();
        optimPhaseList_.add(builderForValue.build());
        onChanged();
      } else {
        optimPhaseListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public Builder addOptimPhaseList(
        int index, road.data.proto.OptimPhase.Builder builderForValue) {
      if (optimPhaseListBuilder_ == null) {
        ensureOptimPhaseListIsMutable();
        optimPhaseList_.add(index, builderForValue.build());
        onChanged();
      } else {
        optimPhaseListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public Builder addAllOptimPhaseList(
        java.lang.Iterable<? extends road.data.proto.OptimPhase> values) {
      if (optimPhaseListBuilder_ == null) {
        ensureOptimPhaseListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, optimPhaseList_);
        onChanged();
      } else {
        optimPhaseListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public Builder clearOptimPhaseList() {
      if (optimPhaseListBuilder_ == null) {
        optimPhaseList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        optimPhaseListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public Builder removeOptimPhaseList(int index) {
      if (optimPhaseListBuilder_ == null) {
        ensureOptimPhaseListIsMutable();
        optimPhaseList_.remove(index);
        onChanged();
      } else {
        optimPhaseListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public road.data.proto.OptimPhase.Builder getOptimPhaseListBuilder(
        int index) {
      return getOptimPhaseListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public road.data.proto.OptimPhaseOrBuilder getOptimPhaseListOrBuilder(
        int index) {
      if (optimPhaseListBuilder_ == null) {
        return optimPhaseList_.get(index);  } else {
        return optimPhaseListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public java.util.List<? extends road.data.proto.OptimPhaseOrBuilder> 
         getOptimPhaseListOrBuilderList() {
      if (optimPhaseListBuilder_ != null) {
        return optimPhaseListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(optimPhaseList_);
      }
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public road.data.proto.OptimPhase.Builder addOptimPhaseListBuilder() {
      return getOptimPhaseListFieldBuilder().addBuilder(
          road.data.proto.OptimPhase.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public road.data.proto.OptimPhase.Builder addOptimPhaseListBuilder(
        int index) {
      return getOptimPhaseListFieldBuilder().addBuilder(
          index, road.data.proto.OptimPhase.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，优化后相位信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
     */
    public java.util.List<road.data.proto.OptimPhase.Builder> 
         getOptimPhaseListBuilderList() {
      return getOptimPhaseListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.OptimPhase, road.data.proto.OptimPhase.Builder, road.data.proto.OptimPhaseOrBuilder> 
        getOptimPhaseListFieldBuilder() {
      if (optimPhaseListBuilder_ == null) {
        optimPhaseListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.OptimPhase, road.data.proto.OptimPhase.Builder, road.data.proto.OptimPhaseOrBuilder>(
                optimPhaseList_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        optimPhaseList_ = null;
      }
      return optimPhaseListBuilder_;
    }

    private java.lang.Object coorPhase_ = "";
    /**
     * <pre>
     * 可选，协调相位编号
     * </pre>
     *
     * <code>string coorPhase = 6;</code>
     */
    public java.lang.String getCoorPhase() {
      java.lang.Object ref = coorPhase_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        coorPhase_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 可选，协调相位编号
     * </pre>
     *
     * <code>string coorPhase = 6;</code>
     */
    public com.google.protobuf.ByteString
        getCoorPhaseBytes() {
      java.lang.Object ref = coorPhase_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        coorPhase_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 可选，协调相位编号
     * </pre>
     *
     * <code>string coorPhase = 6;</code>
     */
    public Builder setCoorPhase(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      coorPhase_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，协调相位编号
     * </pre>
     *
     * <code>string coorPhase = 6;</code>
     */
    public Builder clearCoorPhase() {
      
      coorPhase_ = getDefaultInstance().getCoorPhase();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，协调相位编号
     * </pre>
     *
     * <code>string coorPhase = 6;</code>
     */
    public Builder setCoorPhaseBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      coorPhase_ = value;
      onChanged();
      return this;
    }

    private int offset_ ;
    /**
     * <pre>
     * 可选，协调相位差.
     * </pre>
     *
     * <code>uint32 offset = 7;</code>
     */
    public int getOffset() {
      return offset_;
    }
    /**
     * <pre>
     * 可选，协调相位差.
     * </pre>
     *
     * <code>uint32 offset = 7;</code>
     */
    public Builder setOffset(int value) {
      
      offset_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，协调相位差.
     * </pre>
     *
     * <code>uint32 offset = 7;</code>
     */
    public Builder clearOffset() {
      
      offset_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.OptimData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.OptimData)
  private static final road.data.proto.OptimData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.OptimData();
  }

  public static road.data.proto.OptimData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<OptimData>
      PARSER = new com.google.protobuf.AbstractParser<OptimData>() {
    @java.lang.Override
    public OptimData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new OptimData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<OptimData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<OptimData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.OptimData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

