// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface OptimDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.OptimData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *优化时段类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
   */
  boolean hasOptimTimeType();
  /**
   * <pre>
   *优化时段类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
   */
  road.data.proto.OptimTimeType getOptimTimeType();
  /**
   * <pre>
   *优化时段类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.OptimTimeType optimTimeType = 1;</code>
   */
  road.data.proto.OptimTimeTypeOrBuilder getOptimTimeTypeOrBuilder();

  /**
   * <pre>
   * 可选，优化周期长度
   * </pre>
   *
   * <code>uint32 optimCycleTime = 2;</code>
   */
  int getOptimCycleTime();

  /**
   * <pre>
   * 可选，优化最小周期长度约束单位 秒
   * </pre>
   *
   * <code>uint32 minCycleTime = 3;</code>
   */
  int getMinCycleTime();

  /**
   * <pre>
   * 可选，优化最小周期长度约束单位 秒
   * </pre>
   *
   * <code>uint32 maxCycleTime = 4;</code>
   */
  int getMaxCycleTime();

  /**
   * <pre>
   * 可选，优化后相位信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
   */
  java.util.List<road.data.proto.OptimPhase> 
      getOptimPhaseListList();
  /**
   * <pre>
   * 可选，优化后相位信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
   */
  road.data.proto.OptimPhase getOptimPhaseList(int index);
  /**
   * <pre>
   * 可选，优化后相位信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
   */
  int getOptimPhaseListCount();
  /**
   * <pre>
   * 可选，优化后相位信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
   */
  java.util.List<? extends road.data.proto.OptimPhaseOrBuilder> 
      getOptimPhaseListOrBuilderList();
  /**
   * <pre>
   * 可选，优化后相位信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimPhase optimPhaseList = 5;</code>
   */
  road.data.proto.OptimPhaseOrBuilder getOptimPhaseListOrBuilder(
      int index);

  /**
   * <pre>
   * 可选，协调相位编号
   * </pre>
   *
   * <code>string coorPhase = 6;</code>
   */
  java.lang.String getCoorPhase();
  /**
   * <pre>
   * 可选，协调相位编号
   * </pre>
   *
   * <code>string coorPhase = 6;</code>
   */
  com.google.protobuf.ByteString
      getCoorPhaseBytes();

  /**
   * <pre>
   * 可选，协调相位差.
   * </pre>
   *
   * <code>uint32 offset = 7;</code>
   */
  int getOffset();
}
