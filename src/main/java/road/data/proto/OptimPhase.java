// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *优化后相位信息OptimPhase  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.OptimPhase}
 */
public  final class OptimPhase extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.OptimPhase)
    OptimPhaseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use OptimPhase.newBuilder() to construct.
  private OptimPhase(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private OptimPhase() {
    movementId_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new OptimPhase();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private OptimPhase(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            phaseId_ = input.readUInt32();
            break;
          }
          case 16: {

            order_ = input.readUInt32();
            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              movementId_ = new java.util.ArrayList<road.data.proto.MovementEx>();
              mutable_bitField0_ |= 0x00000001;
            }
            movementId_.add(
                input.readMessage(road.data.proto.MovementEx.parser(), extensionRegistry));
            break;
          }
          case 32: {

            phaseTime_ = input.readUInt32();
            break;
          }
          case 40: {

            green_ = input.readUInt32();
            break;
          }
          case 48: {

            phaseYellowTime_ = input.readUInt32();
            break;
          }
          case 56: {

            phaseAllRedTime_ = input.readUInt32();
            break;
          }
          case 64: {

            minGreen_ = input.readUInt32();
            break;
          }
          case 72: {

            maxGreen_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        movementId_ = java.util.Collections.unmodifiableList(movementId_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimPhase_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimPhase_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.OptimPhase.class, road.data.proto.OptimPhase.Builder.class);
  }

  public static final int PHASEID_FIELD_NUMBER = 1;
  private int phaseId_;
  /**
   * <pre>
   * 相位编号
   * </pre>
   *
   * <code>uint32 phaseId = 1;</code>
   */
  public int getPhaseId() {
    return phaseId_;
  }

  public static final int ORDER_FIELD_NUMBER = 2;
  private int order_;
  /**
   * <pre>
   *相序
   * </pre>
   *
   * <code>uint32 order = 2;</code>
   */
  public int getOrder() {
    return order_;
  }

  public static final int MOVEMENTID_FIELD_NUMBER = 3;
  private java.util.List<road.data.proto.MovementEx> movementId_;
  /**
   * <pre>
   *优化方案对应转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
   */
  public java.util.List<road.data.proto.MovementEx> getMovementIdList() {
    return movementId_;
  }
  /**
   * <pre>
   *优化方案对应转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
   */
  public java.util.List<? extends road.data.proto.MovementExOrBuilder> 
      getMovementIdOrBuilderList() {
    return movementId_;
  }
  /**
   * <pre>
   *优化方案对应转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
   */
  public int getMovementIdCount() {
    return movementId_.size();
  }
  /**
   * <pre>
   *优化方案对应转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
   */
  public road.data.proto.MovementEx getMovementId(int index) {
    return movementId_.get(index);
  }
  /**
   * <pre>
   *优化方案对应转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
   */
  public road.data.proto.MovementExOrBuilder getMovementIdOrBuilder(
      int index) {
    return movementId_.get(index);
  }

  public static final int PHASETIME_FIELD_NUMBER = 4;
  private int phaseTime_;
  /**
   * <pre>
   * 相位时间(包括所有的时间）
   * </pre>
   *
   * <code>uint32 phaseTime = 4;</code>
   */
  public int getPhaseTime() {
    return phaseTime_;
  }

  public static final int GREEN_FIELD_NUMBER = 5;
  private int green_;
  /**
   * <pre>
   *绿灯长度 单位sec
   * </pre>
   *
   * <code>uint32 green = 5;</code>
   */
  public int getGreen() {
    return green_;
  }

  public static final int PHASEYELLOWTIME_FIELD_NUMBER = 6;
  private int phaseYellowTime_;
  /**
   * <pre>
   * 相位黄灯时间
   * </pre>
   *
   * <code>uint32 phaseYellowTime = 6;</code>
   */
  public int getPhaseYellowTime() {
    return phaseYellowTime_;
  }

  public static final int PHASEALLREDTIME_FIELD_NUMBER = 7;
  private int phaseAllRedTime_;
  /**
   * <pre>
   * 相位全红时间
   * </pre>
   *
   * <code>uint32 phaseAllRedTime = 7;</code>
   */
  public int getPhaseAllRedTime() {
    return phaseAllRedTime_;
  }

  public static final int MINGREEN_FIELD_NUMBER = 8;
  private int minGreen_;
  /**
   * <pre>
   *最小时间约束 单位sec
   * </pre>
   *
   * <code>uint32 minGreen = 8;</code>
   */
  public int getMinGreen() {
    return minGreen_;
  }

  public static final int MAXGREEN_FIELD_NUMBER = 9;
  private int maxGreen_;
  /**
   * <pre>
   *最大绿地呢个时间约束 单位sec
   * </pre>
   *
   * <code>uint32 maxGreen = 9;</code>
   */
  public int getMaxGreen() {
    return maxGreen_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (phaseId_ != 0) {
      output.writeUInt32(1, phaseId_);
    }
    if (order_ != 0) {
      output.writeUInt32(2, order_);
    }
    for (int i = 0; i < movementId_.size(); i++) {
      output.writeMessage(3, movementId_.get(i));
    }
    if (phaseTime_ != 0) {
      output.writeUInt32(4, phaseTime_);
    }
    if (green_ != 0) {
      output.writeUInt32(5, green_);
    }
    if (phaseYellowTime_ != 0) {
      output.writeUInt32(6, phaseYellowTime_);
    }
    if (phaseAllRedTime_ != 0) {
      output.writeUInt32(7, phaseAllRedTime_);
    }
    if (minGreen_ != 0) {
      output.writeUInt32(8, minGreen_);
    }
    if (maxGreen_ != 0) {
      output.writeUInt32(9, maxGreen_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (phaseId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, phaseId_);
    }
    if (order_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, order_);
    }
    for (int i = 0; i < movementId_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, movementId_.get(i));
    }
    if (phaseTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, phaseTime_);
    }
    if (green_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(5, green_);
    }
    if (phaseYellowTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(6, phaseYellowTime_);
    }
    if (phaseAllRedTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(7, phaseAllRedTime_);
    }
    if (minGreen_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(8, minGreen_);
    }
    if (maxGreen_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(9, maxGreen_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.OptimPhase)) {
      return super.equals(obj);
    }
    road.data.proto.OptimPhase other = (road.data.proto.OptimPhase) obj;

    if (getPhaseId()
        != other.getPhaseId()) return false;
    if (getOrder()
        != other.getOrder()) return false;
    if (!getMovementIdList()
        .equals(other.getMovementIdList())) return false;
    if (getPhaseTime()
        != other.getPhaseTime()) return false;
    if (getGreen()
        != other.getGreen()) return false;
    if (getPhaseYellowTime()
        != other.getPhaseYellowTime()) return false;
    if (getPhaseAllRedTime()
        != other.getPhaseAllRedTime()) return false;
    if (getMinGreen()
        != other.getMinGreen()) return false;
    if (getMaxGreen()
        != other.getMaxGreen()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + PHASEID_FIELD_NUMBER;
    hash = (53 * hash) + getPhaseId();
    hash = (37 * hash) + ORDER_FIELD_NUMBER;
    hash = (53 * hash) + getOrder();
    if (getMovementIdCount() > 0) {
      hash = (37 * hash) + MOVEMENTID_FIELD_NUMBER;
      hash = (53 * hash) + getMovementIdList().hashCode();
    }
    hash = (37 * hash) + PHASETIME_FIELD_NUMBER;
    hash = (53 * hash) + getPhaseTime();
    hash = (37 * hash) + GREEN_FIELD_NUMBER;
    hash = (53 * hash) + getGreen();
    hash = (37 * hash) + PHASEYELLOWTIME_FIELD_NUMBER;
    hash = (53 * hash) + getPhaseYellowTime();
    hash = (37 * hash) + PHASEALLREDTIME_FIELD_NUMBER;
    hash = (53 * hash) + getPhaseAllRedTime();
    hash = (37 * hash) + MINGREEN_FIELD_NUMBER;
    hash = (53 * hash) + getMinGreen();
    hash = (37 * hash) + MAXGREEN_FIELD_NUMBER;
    hash = (53 * hash) + getMaxGreen();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.OptimPhase parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.OptimPhase parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.OptimPhase parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.OptimPhase parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.OptimPhase parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.OptimPhase parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.OptimPhase parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.OptimPhase parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.OptimPhase parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.OptimPhase parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.OptimPhase parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.OptimPhase parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.OptimPhase prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *优化后相位信息OptimPhase  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.OptimPhase}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.OptimPhase)
      road.data.proto.OptimPhaseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimPhase_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimPhase_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.OptimPhase.class, road.data.proto.OptimPhase.Builder.class);
    }

    // Construct using road.data.proto.OptimPhase.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getMovementIdFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      phaseId_ = 0;

      order_ = 0;

      if (movementIdBuilder_ == null) {
        movementId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        movementIdBuilder_.clear();
      }
      phaseTime_ = 0;

      green_ = 0;

      phaseYellowTime_ = 0;

      phaseAllRedTime_ = 0;

      minGreen_ = 0;

      maxGreen_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimPhase_descriptor;
    }

    @java.lang.Override
    public road.data.proto.OptimPhase getDefaultInstanceForType() {
      return road.data.proto.OptimPhase.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.OptimPhase build() {
      road.data.proto.OptimPhase result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.OptimPhase buildPartial() {
      road.data.proto.OptimPhase result = new road.data.proto.OptimPhase(this);
      int from_bitField0_ = bitField0_;
      result.phaseId_ = phaseId_;
      result.order_ = order_;
      if (movementIdBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          movementId_ = java.util.Collections.unmodifiableList(movementId_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.movementId_ = movementId_;
      } else {
        result.movementId_ = movementIdBuilder_.build();
      }
      result.phaseTime_ = phaseTime_;
      result.green_ = green_;
      result.phaseYellowTime_ = phaseYellowTime_;
      result.phaseAllRedTime_ = phaseAllRedTime_;
      result.minGreen_ = minGreen_;
      result.maxGreen_ = maxGreen_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.OptimPhase) {
        return mergeFrom((road.data.proto.OptimPhase)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.OptimPhase other) {
      if (other == road.data.proto.OptimPhase.getDefaultInstance()) return this;
      if (other.getPhaseId() != 0) {
        setPhaseId(other.getPhaseId());
      }
      if (other.getOrder() != 0) {
        setOrder(other.getOrder());
      }
      if (movementIdBuilder_ == null) {
        if (!other.movementId_.isEmpty()) {
          if (movementId_.isEmpty()) {
            movementId_ = other.movementId_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureMovementIdIsMutable();
            movementId_.addAll(other.movementId_);
          }
          onChanged();
        }
      } else {
        if (!other.movementId_.isEmpty()) {
          if (movementIdBuilder_.isEmpty()) {
            movementIdBuilder_.dispose();
            movementIdBuilder_ = null;
            movementId_ = other.movementId_;
            bitField0_ = (bitField0_ & ~0x00000001);
            movementIdBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getMovementIdFieldBuilder() : null;
          } else {
            movementIdBuilder_.addAllMessages(other.movementId_);
          }
        }
      }
      if (other.getPhaseTime() != 0) {
        setPhaseTime(other.getPhaseTime());
      }
      if (other.getGreen() != 0) {
        setGreen(other.getGreen());
      }
      if (other.getPhaseYellowTime() != 0) {
        setPhaseYellowTime(other.getPhaseYellowTime());
      }
      if (other.getPhaseAllRedTime() != 0) {
        setPhaseAllRedTime(other.getPhaseAllRedTime());
      }
      if (other.getMinGreen() != 0) {
        setMinGreen(other.getMinGreen());
      }
      if (other.getMaxGreen() != 0) {
        setMaxGreen(other.getMaxGreen());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.OptimPhase parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.OptimPhase) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int phaseId_ ;
    /**
     * <pre>
     * 相位编号
     * </pre>
     *
     * <code>uint32 phaseId = 1;</code>
     */
    public int getPhaseId() {
      return phaseId_;
    }
    /**
     * <pre>
     * 相位编号
     * </pre>
     *
     * <code>uint32 phaseId = 1;</code>
     */
    public Builder setPhaseId(int value) {
      
      phaseId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 相位编号
     * </pre>
     *
     * <code>uint32 phaseId = 1;</code>
     */
    public Builder clearPhaseId() {
      
      phaseId_ = 0;
      onChanged();
      return this;
    }

    private int order_ ;
    /**
     * <pre>
     *相序
     * </pre>
     *
     * <code>uint32 order = 2;</code>
     */
    public int getOrder() {
      return order_;
    }
    /**
     * <pre>
     *相序
     * </pre>
     *
     * <code>uint32 order = 2;</code>
     */
    public Builder setOrder(int value) {
      
      order_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *相序
     * </pre>
     *
     * <code>uint32 order = 2;</code>
     */
    public Builder clearOrder() {
      
      order_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.MovementEx> movementId_ =
      java.util.Collections.emptyList();
    private void ensureMovementIdIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        movementId_ = new java.util.ArrayList<road.data.proto.MovementEx>(movementId_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.MovementEx, road.data.proto.MovementEx.Builder, road.data.proto.MovementExOrBuilder> movementIdBuilder_;

    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public java.util.List<road.data.proto.MovementEx> getMovementIdList() {
      if (movementIdBuilder_ == null) {
        return java.util.Collections.unmodifiableList(movementId_);
      } else {
        return movementIdBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public int getMovementIdCount() {
      if (movementIdBuilder_ == null) {
        return movementId_.size();
      } else {
        return movementIdBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public road.data.proto.MovementEx getMovementId(int index) {
      if (movementIdBuilder_ == null) {
        return movementId_.get(index);
      } else {
        return movementIdBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public Builder setMovementId(
        int index, road.data.proto.MovementEx value) {
      if (movementIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMovementIdIsMutable();
        movementId_.set(index, value);
        onChanged();
      } else {
        movementIdBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public Builder setMovementId(
        int index, road.data.proto.MovementEx.Builder builderForValue) {
      if (movementIdBuilder_ == null) {
        ensureMovementIdIsMutable();
        movementId_.set(index, builderForValue.build());
        onChanged();
      } else {
        movementIdBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public Builder addMovementId(road.data.proto.MovementEx value) {
      if (movementIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMovementIdIsMutable();
        movementId_.add(value);
        onChanged();
      } else {
        movementIdBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public Builder addMovementId(
        int index, road.data.proto.MovementEx value) {
      if (movementIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMovementIdIsMutable();
        movementId_.add(index, value);
        onChanged();
      } else {
        movementIdBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public Builder addMovementId(
        road.data.proto.MovementEx.Builder builderForValue) {
      if (movementIdBuilder_ == null) {
        ensureMovementIdIsMutable();
        movementId_.add(builderForValue.build());
        onChanged();
      } else {
        movementIdBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public Builder addMovementId(
        int index, road.data.proto.MovementEx.Builder builderForValue) {
      if (movementIdBuilder_ == null) {
        ensureMovementIdIsMutable();
        movementId_.add(index, builderForValue.build());
        onChanged();
      } else {
        movementIdBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public Builder addAllMovementId(
        java.lang.Iterable<? extends road.data.proto.MovementEx> values) {
      if (movementIdBuilder_ == null) {
        ensureMovementIdIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, movementId_);
        onChanged();
      } else {
        movementIdBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public Builder clearMovementId() {
      if (movementIdBuilder_ == null) {
        movementId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        movementIdBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public Builder removeMovementId(int index) {
      if (movementIdBuilder_ == null) {
        ensureMovementIdIsMutable();
        movementId_.remove(index);
        onChanged();
      } else {
        movementIdBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public road.data.proto.MovementEx.Builder getMovementIdBuilder(
        int index) {
      return getMovementIdFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public road.data.proto.MovementExOrBuilder getMovementIdOrBuilder(
        int index) {
      if (movementIdBuilder_ == null) {
        return movementId_.get(index);  } else {
        return movementIdBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public java.util.List<? extends road.data.proto.MovementExOrBuilder> 
         getMovementIdOrBuilderList() {
      if (movementIdBuilder_ != null) {
        return movementIdBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(movementId_);
      }
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public road.data.proto.MovementEx.Builder addMovementIdBuilder() {
      return getMovementIdFieldBuilder().addBuilder(
          road.data.proto.MovementEx.getDefaultInstance());
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public road.data.proto.MovementEx.Builder addMovementIdBuilder(
        int index) {
      return getMovementIdFieldBuilder().addBuilder(
          index, road.data.proto.MovementEx.getDefaultInstance());
    }
    /**
     * <pre>
     *优化方案对应转向信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
     */
    public java.util.List<road.data.proto.MovementEx.Builder> 
         getMovementIdBuilderList() {
      return getMovementIdFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.MovementEx, road.data.proto.MovementEx.Builder, road.data.proto.MovementExOrBuilder> 
        getMovementIdFieldBuilder() {
      if (movementIdBuilder_ == null) {
        movementIdBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.MovementEx, road.data.proto.MovementEx.Builder, road.data.proto.MovementExOrBuilder>(
                movementId_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        movementId_ = null;
      }
      return movementIdBuilder_;
    }

    private int phaseTime_ ;
    /**
     * <pre>
     * 相位时间(包括所有的时间）
     * </pre>
     *
     * <code>uint32 phaseTime = 4;</code>
     */
    public int getPhaseTime() {
      return phaseTime_;
    }
    /**
     * <pre>
     * 相位时间(包括所有的时间）
     * </pre>
     *
     * <code>uint32 phaseTime = 4;</code>
     */
    public Builder setPhaseTime(int value) {
      
      phaseTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 相位时间(包括所有的时间）
     * </pre>
     *
     * <code>uint32 phaseTime = 4;</code>
     */
    public Builder clearPhaseTime() {
      
      phaseTime_ = 0;
      onChanged();
      return this;
    }

    private int green_ ;
    /**
     * <pre>
     *绿灯长度 单位sec
     * </pre>
     *
     * <code>uint32 green = 5;</code>
     */
    public int getGreen() {
      return green_;
    }
    /**
     * <pre>
     *绿灯长度 单位sec
     * </pre>
     *
     * <code>uint32 green = 5;</code>
     */
    public Builder setGreen(int value) {
      
      green_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *绿灯长度 单位sec
     * </pre>
     *
     * <code>uint32 green = 5;</code>
     */
    public Builder clearGreen() {
      
      green_ = 0;
      onChanged();
      return this;
    }

    private int phaseYellowTime_ ;
    /**
     * <pre>
     * 相位黄灯时间
     * </pre>
     *
     * <code>uint32 phaseYellowTime = 6;</code>
     */
    public int getPhaseYellowTime() {
      return phaseYellowTime_;
    }
    /**
     * <pre>
     * 相位黄灯时间
     * </pre>
     *
     * <code>uint32 phaseYellowTime = 6;</code>
     */
    public Builder setPhaseYellowTime(int value) {
      
      phaseYellowTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 相位黄灯时间
     * </pre>
     *
     * <code>uint32 phaseYellowTime = 6;</code>
     */
    public Builder clearPhaseYellowTime() {
      
      phaseYellowTime_ = 0;
      onChanged();
      return this;
    }

    private int phaseAllRedTime_ ;
    /**
     * <pre>
     * 相位全红时间
     * </pre>
     *
     * <code>uint32 phaseAllRedTime = 7;</code>
     */
    public int getPhaseAllRedTime() {
      return phaseAllRedTime_;
    }
    /**
     * <pre>
     * 相位全红时间
     * </pre>
     *
     * <code>uint32 phaseAllRedTime = 7;</code>
     */
    public Builder setPhaseAllRedTime(int value) {
      
      phaseAllRedTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 相位全红时间
     * </pre>
     *
     * <code>uint32 phaseAllRedTime = 7;</code>
     */
    public Builder clearPhaseAllRedTime() {
      
      phaseAllRedTime_ = 0;
      onChanged();
      return this;
    }

    private int minGreen_ ;
    /**
     * <pre>
     *最小时间约束 单位sec
     * </pre>
     *
     * <code>uint32 minGreen = 8;</code>
     */
    public int getMinGreen() {
      return minGreen_;
    }
    /**
     * <pre>
     *最小时间约束 单位sec
     * </pre>
     *
     * <code>uint32 minGreen = 8;</code>
     */
    public Builder setMinGreen(int value) {
      
      minGreen_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *最小时间约束 单位sec
     * </pre>
     *
     * <code>uint32 minGreen = 8;</code>
     */
    public Builder clearMinGreen() {
      
      minGreen_ = 0;
      onChanged();
      return this;
    }

    private int maxGreen_ ;
    /**
     * <pre>
     *最大绿地呢个时间约束 单位sec
     * </pre>
     *
     * <code>uint32 maxGreen = 9;</code>
     */
    public int getMaxGreen() {
      return maxGreen_;
    }
    /**
     * <pre>
     *最大绿地呢个时间约束 单位sec
     * </pre>
     *
     * <code>uint32 maxGreen = 9;</code>
     */
    public Builder setMaxGreen(int value) {
      
      maxGreen_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *最大绿地呢个时间约束 单位sec
     * </pre>
     *
     * <code>uint32 maxGreen = 9;</code>
     */
    public Builder clearMaxGreen() {
      
      maxGreen_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.OptimPhase)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.OptimPhase)
  private static final road.data.proto.OptimPhase DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.OptimPhase();
  }

  public static road.data.proto.OptimPhase getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<OptimPhase>
      PARSER = new com.google.protobuf.AbstractParser<OptimPhase>() {
    @java.lang.Override
    public OptimPhase parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new OptimPhase(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<OptimPhase> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<OptimPhase> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.OptimPhase getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

