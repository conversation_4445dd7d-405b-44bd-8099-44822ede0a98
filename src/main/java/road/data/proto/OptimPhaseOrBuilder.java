// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface OptimPhaseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.OptimPhase)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 相位编号
   * </pre>
   *
   * <code>uint32 phaseId = 1;</code>
   */
  int getPhaseId();

  /**
   * <pre>
   *相序
   * </pre>
   *
   * <code>uint32 order = 2;</code>
   */
  int getOrder();

  /**
   * <pre>
   *优化方案对应转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
   */
  java.util.List<road.data.proto.MovementEx> 
      getMovementIdList();
  /**
   * <pre>
   *优化方案对应转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
   */
  road.data.proto.MovementEx getMovementId(int index);
  /**
   * <pre>
   *优化方案对应转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
   */
  int getMovementIdCount();
  /**
   * <pre>
   *优化方案对应转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
   */
  java.util.List<? extends road.data.proto.MovementExOrBuilder> 
      getMovementIdOrBuilderList();
  /**
   * <pre>
   *优化方案对应转向信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementEx movementId = 3;</code>
   */
  road.data.proto.MovementExOrBuilder getMovementIdOrBuilder(
      int index);

  /**
   * <pre>
   * 相位时间(包括所有的时间）
   * </pre>
   *
   * <code>uint32 phaseTime = 4;</code>
   */
  int getPhaseTime();

  /**
   * <pre>
   *绿灯长度 单位sec
   * </pre>
   *
   * <code>uint32 green = 5;</code>
   */
  int getGreen();

  /**
   * <pre>
   * 相位黄灯时间
   * </pre>
   *
   * <code>uint32 phaseYellowTime = 6;</code>
   */
  int getPhaseYellowTime();

  /**
   * <pre>
   * 相位全红时间
   * </pre>
   *
   * <code>uint32 phaseAllRedTime = 7;</code>
   */
  int getPhaseAllRedTime();

  /**
   * <pre>
   *最小时间约束 单位sec
   * </pre>
   *
   * <code>uint32 minGreen = 8;</code>
   */
  int getMinGreen();

  /**
   * <pre>
   *最大绿地呢个时间约束 单位sec
   * </pre>
   *
   * <code>uint32 maxGreen = 9;</code>
   */
  int getMaxGreen();
}
