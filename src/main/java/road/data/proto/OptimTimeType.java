// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *优化时段类型 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.OptimTimeType}
 */
public  final class OptimTimeType extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.OptimTimeType)
    OptimTimeTypeOrBuilder {
private static final long serialVersionUID = 0L;
  // Use OptimTimeType.newBuilder() to construct.
  private OptimTimeType(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private OptimTimeType() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new OptimTimeType();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private OptimTimeType(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.SingleTimeSpan.Builder subBuilder = null;
            if (optimTimeTypeOneOfCase_ == 1) {
              subBuilder = ((road.data.proto.SingleTimeSpan) optimTimeTypeOneOf_).toBuilder();
            }
            optimTimeTypeOneOf_ =
                input.readMessage(road.data.proto.SingleTimeSpan.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.SingleTimeSpan) optimTimeTypeOneOf_);
              optimTimeTypeOneOf_ = subBuilder.buildPartial();
            }
            optimTimeTypeOneOfCase_ = 1;
            break;
          }
          case 18: {
            road.data.proto.PeriodictimeSpan.Builder subBuilder = null;
            if (optimTimeTypeOneOfCase_ == 2) {
              subBuilder = ((road.data.proto.PeriodictimeSpan) optimTimeTypeOneOf_).toBuilder();
            }
            optimTimeTypeOneOf_ =
                input.readMessage(road.data.proto.PeriodictimeSpan.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.PeriodictimeSpan) optimTimeTypeOneOf_);
              optimTimeTypeOneOf_ = subBuilder.buildPartial();
            }
            optimTimeTypeOneOfCase_ = 2;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimTimeType_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimTimeType_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.OptimTimeType.class, road.data.proto.OptimTimeType.Builder.class);
  }

  private int optimTimeTypeOneOfCase_ = 0;
  private java.lang.Object optimTimeTypeOneOf_;
  public enum OptimTimeTypeOneOfCase
      implements com.google.protobuf.Internal.EnumLite {
    SINGLE(1),
    PERIODIC(2),
    OPTIMTIMETYPEONEOF_NOT_SET(0);
    private final int value;
    private OptimTimeTypeOneOfCase(int value) {
      this.value = value;
    }
    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static OptimTimeTypeOneOfCase valueOf(int value) {
      return forNumber(value);
    }

    public static OptimTimeTypeOneOfCase forNumber(int value) {
      switch (value) {
        case 1: return SINGLE;
        case 2: return PERIODIC;
        case 0: return OPTIMTIMETYPEONEOF_NOT_SET;
        default: return null;
      }
    }
    public int getNumber() {
      return this.value;
    }
  };

  public OptimTimeTypeOneOfCase
  getOptimTimeTypeOneOfCase() {
    return OptimTimeTypeOneOfCase.forNumber(
        optimTimeTypeOneOfCase_);
  }

  public static final int SINGLE_FIELD_NUMBER = 1;
  /**
   * <pre>
   *可选，单次优化时段
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
   */
  public boolean hasSingle() {
    return optimTimeTypeOneOfCase_ == 1;
  }
  /**
   * <pre>
   *可选，单次优化时段
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
   */
  public road.data.proto.SingleTimeSpan getSingle() {
    if (optimTimeTypeOneOfCase_ == 1) {
       return (road.data.proto.SingleTimeSpan) optimTimeTypeOneOf_;
    }
    return road.data.proto.SingleTimeSpan.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，单次优化时段
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
   */
  public road.data.proto.SingleTimeSpanOrBuilder getSingleOrBuilder() {
    if (optimTimeTypeOneOfCase_ == 1) {
       return (road.data.proto.SingleTimeSpan) optimTimeTypeOneOf_;
    }
    return road.data.proto.SingleTimeSpan.getDefaultInstance();
  }

  public static final int PERIODIC_FIELD_NUMBER = 2;
  /**
   * <pre>
   *可选，周期优化时段划分
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
   */
  public boolean hasPeriodic() {
    return optimTimeTypeOneOfCase_ == 2;
  }
  /**
   * <pre>
   *可选，周期优化时段划分
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
   */
  public road.data.proto.PeriodictimeSpan getPeriodic() {
    if (optimTimeTypeOneOfCase_ == 2) {
       return (road.data.proto.PeriodictimeSpan) optimTimeTypeOneOf_;
    }
    return road.data.proto.PeriodictimeSpan.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，周期优化时段划分
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
   */
  public road.data.proto.PeriodictimeSpanOrBuilder getPeriodicOrBuilder() {
    if (optimTimeTypeOneOfCase_ == 2) {
       return (road.data.proto.PeriodictimeSpan) optimTimeTypeOneOf_;
    }
    return road.data.proto.PeriodictimeSpan.getDefaultInstance();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (optimTimeTypeOneOfCase_ == 1) {
      output.writeMessage(1, (road.data.proto.SingleTimeSpan) optimTimeTypeOneOf_);
    }
    if (optimTimeTypeOneOfCase_ == 2) {
      output.writeMessage(2, (road.data.proto.PeriodictimeSpan) optimTimeTypeOneOf_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (optimTimeTypeOneOfCase_ == 1) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, (road.data.proto.SingleTimeSpan) optimTimeTypeOneOf_);
    }
    if (optimTimeTypeOneOfCase_ == 2) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, (road.data.proto.PeriodictimeSpan) optimTimeTypeOneOf_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.OptimTimeType)) {
      return super.equals(obj);
    }
    road.data.proto.OptimTimeType other = (road.data.proto.OptimTimeType) obj;

    if (!getOptimTimeTypeOneOfCase().equals(other.getOptimTimeTypeOneOfCase())) return false;
    switch (optimTimeTypeOneOfCase_) {
      case 1:
        if (!getSingle()
            .equals(other.getSingle())) return false;
        break;
      case 2:
        if (!getPeriodic()
            .equals(other.getPeriodic())) return false;
        break;
      case 0:
      default:
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    switch (optimTimeTypeOneOfCase_) {
      case 1:
        hash = (37 * hash) + SINGLE_FIELD_NUMBER;
        hash = (53 * hash) + getSingle().hashCode();
        break;
      case 2:
        hash = (37 * hash) + PERIODIC_FIELD_NUMBER;
        hash = (53 * hash) + getPeriodic().hashCode();
        break;
      case 0:
      default:
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.OptimTimeType parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.OptimTimeType parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.OptimTimeType parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.OptimTimeType parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.OptimTimeType parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.OptimTimeType parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.OptimTimeType parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.OptimTimeType parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.OptimTimeType parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.OptimTimeType parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.OptimTimeType parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.OptimTimeType parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.OptimTimeType prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *优化时段类型 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.OptimTimeType}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.OptimTimeType)
      road.data.proto.OptimTimeTypeOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimTimeType_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimTimeType_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.OptimTimeType.class, road.data.proto.OptimTimeType.Builder.class);
    }

    // Construct using road.data.proto.OptimTimeType.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      optimTimeTypeOneOfCase_ = 0;
      optimTimeTypeOneOf_ = null;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_OptimTimeType_descriptor;
    }

    @java.lang.Override
    public road.data.proto.OptimTimeType getDefaultInstanceForType() {
      return road.data.proto.OptimTimeType.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.OptimTimeType build() {
      road.data.proto.OptimTimeType result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.OptimTimeType buildPartial() {
      road.data.proto.OptimTimeType result = new road.data.proto.OptimTimeType(this);
      if (optimTimeTypeOneOfCase_ == 1) {
        if (singleBuilder_ == null) {
          result.optimTimeTypeOneOf_ = optimTimeTypeOneOf_;
        } else {
          result.optimTimeTypeOneOf_ = singleBuilder_.build();
        }
      }
      if (optimTimeTypeOneOfCase_ == 2) {
        if (periodicBuilder_ == null) {
          result.optimTimeTypeOneOf_ = optimTimeTypeOneOf_;
        } else {
          result.optimTimeTypeOneOf_ = periodicBuilder_.build();
        }
      }
      result.optimTimeTypeOneOfCase_ = optimTimeTypeOneOfCase_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.OptimTimeType) {
        return mergeFrom((road.data.proto.OptimTimeType)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.OptimTimeType other) {
      if (other == road.data.proto.OptimTimeType.getDefaultInstance()) return this;
      switch (other.getOptimTimeTypeOneOfCase()) {
        case SINGLE: {
          mergeSingle(other.getSingle());
          break;
        }
        case PERIODIC: {
          mergePeriodic(other.getPeriodic());
          break;
        }
        case OPTIMTIMETYPEONEOF_NOT_SET: {
          break;
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.OptimTimeType parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.OptimTimeType) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int optimTimeTypeOneOfCase_ = 0;
    private java.lang.Object optimTimeTypeOneOf_;
    public OptimTimeTypeOneOfCase
        getOptimTimeTypeOneOfCase() {
      return OptimTimeTypeOneOfCase.forNumber(
          optimTimeTypeOneOfCase_);
    }

    public Builder clearOptimTimeTypeOneOf() {
      optimTimeTypeOneOfCase_ = 0;
      optimTimeTypeOneOf_ = null;
      onChanged();
      return this;
    }


    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.SingleTimeSpan, road.data.proto.SingleTimeSpan.Builder, road.data.proto.SingleTimeSpanOrBuilder> singleBuilder_;
    /**
     * <pre>
     *可选，单次优化时段
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
     */
    public boolean hasSingle() {
      return optimTimeTypeOneOfCase_ == 1;
    }
    /**
     * <pre>
     *可选，单次优化时段
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
     */
    public road.data.proto.SingleTimeSpan getSingle() {
      if (singleBuilder_ == null) {
        if (optimTimeTypeOneOfCase_ == 1) {
          return (road.data.proto.SingleTimeSpan) optimTimeTypeOneOf_;
        }
        return road.data.proto.SingleTimeSpan.getDefaultInstance();
      } else {
        if (optimTimeTypeOneOfCase_ == 1) {
          return singleBuilder_.getMessage();
        }
        return road.data.proto.SingleTimeSpan.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，单次优化时段
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
     */
    public Builder setSingle(road.data.proto.SingleTimeSpan value) {
      if (singleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        optimTimeTypeOneOf_ = value;
        onChanged();
      } else {
        singleBuilder_.setMessage(value);
      }
      optimTimeTypeOneOfCase_ = 1;
      return this;
    }
    /**
     * <pre>
     *可选，单次优化时段
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
     */
    public Builder setSingle(
        road.data.proto.SingleTimeSpan.Builder builderForValue) {
      if (singleBuilder_ == null) {
        optimTimeTypeOneOf_ = builderForValue.build();
        onChanged();
      } else {
        singleBuilder_.setMessage(builderForValue.build());
      }
      optimTimeTypeOneOfCase_ = 1;
      return this;
    }
    /**
     * <pre>
     *可选，单次优化时段
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
     */
    public Builder mergeSingle(road.data.proto.SingleTimeSpan value) {
      if (singleBuilder_ == null) {
        if (optimTimeTypeOneOfCase_ == 1 &&
            optimTimeTypeOneOf_ != road.data.proto.SingleTimeSpan.getDefaultInstance()) {
          optimTimeTypeOneOf_ = road.data.proto.SingleTimeSpan.newBuilder((road.data.proto.SingleTimeSpan) optimTimeTypeOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          optimTimeTypeOneOf_ = value;
        }
        onChanged();
      } else {
        if (optimTimeTypeOneOfCase_ == 1) {
          singleBuilder_.mergeFrom(value);
        }
        singleBuilder_.setMessage(value);
      }
      optimTimeTypeOneOfCase_ = 1;
      return this;
    }
    /**
     * <pre>
     *可选，单次优化时段
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
     */
    public Builder clearSingle() {
      if (singleBuilder_ == null) {
        if (optimTimeTypeOneOfCase_ == 1) {
          optimTimeTypeOneOfCase_ = 0;
          optimTimeTypeOneOf_ = null;
          onChanged();
        }
      } else {
        if (optimTimeTypeOneOfCase_ == 1) {
          optimTimeTypeOneOfCase_ = 0;
          optimTimeTypeOneOf_ = null;
        }
        singleBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，单次优化时段
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
     */
    public road.data.proto.SingleTimeSpan.Builder getSingleBuilder() {
      return getSingleFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，单次优化时段
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
     */
    public road.data.proto.SingleTimeSpanOrBuilder getSingleOrBuilder() {
      if ((optimTimeTypeOneOfCase_ == 1) && (singleBuilder_ != null)) {
        return singleBuilder_.getMessageOrBuilder();
      } else {
        if (optimTimeTypeOneOfCase_ == 1) {
          return (road.data.proto.SingleTimeSpan) optimTimeTypeOneOf_;
        }
        return road.data.proto.SingleTimeSpan.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，单次优化时段
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.SingleTimeSpan, road.data.proto.SingleTimeSpan.Builder, road.data.proto.SingleTimeSpanOrBuilder> 
        getSingleFieldBuilder() {
      if (singleBuilder_ == null) {
        if (!(optimTimeTypeOneOfCase_ == 1)) {
          optimTimeTypeOneOf_ = road.data.proto.SingleTimeSpan.getDefaultInstance();
        }
        singleBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.SingleTimeSpan, road.data.proto.SingleTimeSpan.Builder, road.data.proto.SingleTimeSpanOrBuilder>(
                (road.data.proto.SingleTimeSpan) optimTimeTypeOneOf_,
                getParentForChildren(),
                isClean());
        optimTimeTypeOneOf_ = null;
      }
      optimTimeTypeOneOfCase_ = 1;
      onChanged();;
      return singleBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PeriodictimeSpan, road.data.proto.PeriodictimeSpan.Builder, road.data.proto.PeriodictimeSpanOrBuilder> periodicBuilder_;
    /**
     * <pre>
     *可选，周期优化时段划分
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
     */
    public boolean hasPeriodic() {
      return optimTimeTypeOneOfCase_ == 2;
    }
    /**
     * <pre>
     *可选，周期优化时段划分
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
     */
    public road.data.proto.PeriodictimeSpan getPeriodic() {
      if (periodicBuilder_ == null) {
        if (optimTimeTypeOneOfCase_ == 2) {
          return (road.data.proto.PeriodictimeSpan) optimTimeTypeOneOf_;
        }
        return road.data.proto.PeriodictimeSpan.getDefaultInstance();
      } else {
        if (optimTimeTypeOneOfCase_ == 2) {
          return periodicBuilder_.getMessage();
        }
        return road.data.proto.PeriodictimeSpan.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，周期优化时段划分
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
     */
    public Builder setPeriodic(road.data.proto.PeriodictimeSpan value) {
      if (periodicBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        optimTimeTypeOneOf_ = value;
        onChanged();
      } else {
        periodicBuilder_.setMessage(value);
      }
      optimTimeTypeOneOfCase_ = 2;
      return this;
    }
    /**
     * <pre>
     *可选，周期优化时段划分
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
     */
    public Builder setPeriodic(
        road.data.proto.PeriodictimeSpan.Builder builderForValue) {
      if (periodicBuilder_ == null) {
        optimTimeTypeOneOf_ = builderForValue.build();
        onChanged();
      } else {
        periodicBuilder_.setMessage(builderForValue.build());
      }
      optimTimeTypeOneOfCase_ = 2;
      return this;
    }
    /**
     * <pre>
     *可选，周期优化时段划分
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
     */
    public Builder mergePeriodic(road.data.proto.PeriodictimeSpan value) {
      if (periodicBuilder_ == null) {
        if (optimTimeTypeOneOfCase_ == 2 &&
            optimTimeTypeOneOf_ != road.data.proto.PeriodictimeSpan.getDefaultInstance()) {
          optimTimeTypeOneOf_ = road.data.proto.PeriodictimeSpan.newBuilder((road.data.proto.PeriodictimeSpan) optimTimeTypeOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          optimTimeTypeOneOf_ = value;
        }
        onChanged();
      } else {
        if (optimTimeTypeOneOfCase_ == 2) {
          periodicBuilder_.mergeFrom(value);
        }
        periodicBuilder_.setMessage(value);
      }
      optimTimeTypeOneOfCase_ = 2;
      return this;
    }
    /**
     * <pre>
     *可选，周期优化时段划分
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
     */
    public Builder clearPeriodic() {
      if (periodicBuilder_ == null) {
        if (optimTimeTypeOneOfCase_ == 2) {
          optimTimeTypeOneOfCase_ = 0;
          optimTimeTypeOneOf_ = null;
          onChanged();
        }
      } else {
        if (optimTimeTypeOneOfCase_ == 2) {
          optimTimeTypeOneOfCase_ = 0;
          optimTimeTypeOneOf_ = null;
        }
        periodicBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，周期优化时段划分
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
     */
    public road.data.proto.PeriodictimeSpan.Builder getPeriodicBuilder() {
      return getPeriodicFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，周期优化时段划分
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
     */
    public road.data.proto.PeriodictimeSpanOrBuilder getPeriodicOrBuilder() {
      if ((optimTimeTypeOneOfCase_ == 2) && (periodicBuilder_ != null)) {
        return periodicBuilder_.getMessageOrBuilder();
      } else {
        if (optimTimeTypeOneOfCase_ == 2) {
          return (road.data.proto.PeriodictimeSpan) optimTimeTypeOneOf_;
        }
        return road.data.proto.PeriodictimeSpan.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，周期优化时段划分
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PeriodictimeSpan, road.data.proto.PeriodictimeSpan.Builder, road.data.proto.PeriodictimeSpanOrBuilder> 
        getPeriodicFieldBuilder() {
      if (periodicBuilder_ == null) {
        if (!(optimTimeTypeOneOfCase_ == 2)) {
          optimTimeTypeOneOf_ = road.data.proto.PeriodictimeSpan.getDefaultInstance();
        }
        periodicBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.PeriodictimeSpan, road.data.proto.PeriodictimeSpan.Builder, road.data.proto.PeriodictimeSpanOrBuilder>(
                (road.data.proto.PeriodictimeSpan) optimTimeTypeOneOf_,
                getParentForChildren(),
                isClean());
        optimTimeTypeOneOf_ = null;
      }
      optimTimeTypeOneOfCase_ = 2;
      onChanged();;
      return periodicBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.OptimTimeType)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.OptimTimeType)
  private static final road.data.proto.OptimTimeType DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.OptimTimeType();
  }

  public static road.data.proto.OptimTimeType getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<OptimTimeType>
      PARSER = new com.google.protobuf.AbstractParser<OptimTimeType>() {
    @java.lang.Override
    public OptimTimeType parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new OptimTimeType(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<OptimTimeType> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<OptimTimeType> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.OptimTimeType getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

