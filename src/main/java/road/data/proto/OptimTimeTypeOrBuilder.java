// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface OptimTimeTypeOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.OptimTimeType)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *可选，单次优化时段
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
   */
  boolean hasSingle();
  /**
   * <pre>
   *可选，单次优化时段
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
   */
  road.data.proto.SingleTimeSpan getSingle();
  /**
   * <pre>
   *可选，单次优化时段
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SingleTimeSpan single = 1;</code>
   */
  road.data.proto.SingleTimeSpanOrBuilder getSingleOrBuilder();

  /**
   * <pre>
   *可选，周期优化时段划分
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
   */
  boolean hasPeriodic();
  /**
   * <pre>
   *可选，周期优化时段划分
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
   */
  road.data.proto.PeriodictimeSpan getPeriodic();
  /**
   * <pre>
   *可选，周期优化时段划分
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PeriodictimeSpan periodic = 2;</code>
   */
  road.data.proto.PeriodictimeSpanOrBuilder getPeriodicOrBuilder();

  public road.data.proto.OptimTimeType.OptimTimeTypeOneOfCase getOptimTimeTypeOneOfCase();
}
