// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *交通参与者信息Participant   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ParticipantData}
 */
public  final class ParticipantData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ParticipantData)
    ParticipantDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ParticipantData.newBuilder() to construct.
  private ParticipantData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ParticipantData() {
    ptcType_ = 0;
    dataSource_ = 0;
    deviceIdList_ = "";
    timeConfidence_ = 0;
    vehicleBand_ = "";
    vehicleType_ = 0;
    plateNo_ = "";
    plateType_ = 0;
    plateColor_ = 0;
    vehicleColor_ = 0;
    ptcTypeExt_ = 0;
    pathHistory_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ParticipantData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ParticipantData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            ptcId_ = input.readUInt64();
            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            ptcType_ = rawValue;
            break;
          }
          case 24: {
            int rawValue = input.readEnum();

            dataSource_ = rawValue;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            deviceIdList_ = s;
            break;
          }
          case 40: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 48: {
            int rawValue = input.readEnum();

            timeConfidence_ = rawValue;
            break;
          }
          case 58: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (ptcPos_ != null) {
              subBuilder = ptcPos_.toBuilder();
            }
            ptcPos_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(ptcPos_);
              ptcPos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 66: {
            road.data.proto.MapLocation.Builder subBuilder = null;
            if (mapLocation_ != null) {
              subBuilder = mapLocation_.toBuilder();
            }
            mapLocation_ = input.readMessage(road.data.proto.MapLocation.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(mapLocation_);
              mapLocation_ = subBuilder.buildPartial();
            }

            break;
          }
          case 74: {
            road.data.proto.PositionConfidenceSet.Builder subBuilder = null;
            if (posConfid_ != null) {
              subBuilder = posConfid_.toBuilder();
            }
            posConfid_ = input.readMessage(road.data.proto.PositionConfidenceSet.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(posConfid_);
              posConfid_ = subBuilder.buildPartial();
            }

            break;
          }
          case 80: {

            speed_ = input.readUInt32();
            break;
          }
          case 88: {

            heading_ = input.readUInt32();
            break;
          }
          case 98: {
            road.data.proto.MotionConfidenceSet.Builder subBuilder = null;
            if (motionConfid_ != null) {
              subBuilder = motionConfid_.toBuilder();
            }
            motionConfid_ = input.readMessage(road.data.proto.MotionConfidenceSet.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(motionConfid_);
              motionConfid_ = subBuilder.buildPartial();
            }

            break;
          }
          case 106: {
            road.data.proto.AccelerationSet4Way.Builder subBuilder = null;
            if (accelSet_ != null) {
              subBuilder = accelSet_.toBuilder();
            }
            accelSet_ = input.readMessage(road.data.proto.AccelerationSet4Way.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(accelSet_);
              accelSet_ = subBuilder.buildPartial();
            }

            break;
          }
          case 114: {
            road.data.proto.AccelerationConfidence.Builder subBuilder = null;
            if (accelerationConfid_ != null) {
              subBuilder = accelerationConfid_.toBuilder();
            }
            accelerationConfid_ = input.readMessage(road.data.proto.AccelerationConfidence.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(accelerationConfid_);
              accelerationConfid_ = subBuilder.buildPartial();
            }

            break;
          }
          case 122: {
            road.data.proto.ParticipantSize.Builder subBuilder = null;
            if (ptcSize_ != null) {
              subBuilder = ptcSize_.toBuilder();
            }
            ptcSize_ = input.readMessage(road.data.proto.ParticipantSize.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(ptcSize_);
              ptcSize_ = subBuilder.buildPartial();
            }

            break;
          }
          case 130: {
            java.lang.String s = input.readStringRequireUtf8();

            vehicleBand_ = s;
            break;
          }
          case 136: {
            int rawValue = input.readEnum();

            vehicleType_ = rawValue;
            break;
          }
          case 146: {
            java.lang.String s = input.readStringRequireUtf8();

            plateNo_ = s;
            break;
          }
          case 152: {
            int rawValue = input.readEnum();

            plateType_ = rawValue;
            break;
          }
          case 160: {
            int rawValue = input.readEnum();

            plateColor_ = rawValue;
            break;
          }
          case 168: {
            int rawValue = input.readEnum();

            vehicleColor_ = rawValue;
            break;
          }
          case 178: {
            road.data.proto.ParticipantSizeConfidence.Builder subBuilder = null;
            if (ptcSizeConfid_ != null) {
              subBuilder = ptcSizeConfid_.toBuilder();
            }
            ptcSizeConfid_ = input.readMessage(road.data.proto.ParticipantSizeConfidence.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(ptcSizeConfid_);
              ptcSizeConfid_ = subBuilder.buildPartial();
            }

            break;
          }
          case 184: {
            int rawValue = input.readEnum();

            ptcTypeExt_ = rawValue;
            break;
          }
          case 192: {

            ptcTypeExtConfid_ = input.readUInt32();
            break;
          }
          case 200: {

            statusDuration_ = input.readUInt32();
            break;
          }
          case 210: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              pathHistory_ = new java.util.ArrayList<road.data.proto.PathHistoryPoint>();
              mutable_bitField0_ |= 0x00000001;
            }
            pathHistory_.add(
                input.readMessage(road.data.proto.PathHistoryPoint.parser(), extensionRegistry));
            break;
          }
          case 216: {

            tracking_ = input.readUInt32();
            break;
          }
          case 226: {
            road.data.proto.Polygon.Builder subBuilder = null;
            if (polygon_ != null) {
              subBuilder = polygon_.toBuilder();
            }
            polygon_ = input.readMessage(road.data.proto.Polygon.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(polygon_);
              polygon_ = subBuilder.buildPartial();
            }

            break;
          }
          case 232: {

            id_ = input.readUInt64();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        pathHistory_ = java.util.Collections.unmodifiableList(pathHistory_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ParticipantData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ParticipantData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ParticipantData.class, road.data.proto.ParticipantData.Builder.class);
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.ParticipantData.PlateColor}
   */
  public enum PlateColor
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *未知
     * </pre>
     *
     * <code>UNKNOWN_PLATE_COLOR = 0;</code>
     */
    UNKNOWN_PLATE_COLOR(0),
    /**
     * <pre>
     *蓝;
     * </pre>
     *
     * <code>BLUE_PLATE = 1;</code>
     */
    BLUE_PLATE(1),
    /**
     * <pre>
     *黄;
     * </pre>
     *
     * <code>YELLOW_PLATE = 2;</code>
     */
    YELLOW_PLATE(2),
    /**
     * <pre>
     *白;
     * </pre>
     *
     * <code>WHITE_PLATE = 3;</code>
     */
    WHITE_PLATE(3),
    /**
     * <pre>
     *黑;
     * </pre>
     *
     * <code>BLACK_PLATE = 4;</code>
     */
    BLACK_PLATE(4),
    /**
     * <pre>
     *黄绿双色;
     * </pre>
     *
     * <code>YELLOW_GREEN_PLATE = 5;</code>
     */
    YELLOW_GREEN_PLATE(5),
    /**
     * <pre>
     *渐变绿
     * </pre>
     *
     * <code>GRADIENT_GREEN_PLATE = 6;</code>
     */
    GRADIENT_GREEN_PLATE(6),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *未知
     * </pre>
     *
     * <code>UNKNOWN_PLATE_COLOR = 0;</code>
     */
    public static final int UNKNOWN_PLATE_COLOR_VALUE = 0;
    /**
     * <pre>
     *蓝;
     * </pre>
     *
     * <code>BLUE_PLATE = 1;</code>
     */
    public static final int BLUE_PLATE_VALUE = 1;
    /**
     * <pre>
     *黄;
     * </pre>
     *
     * <code>YELLOW_PLATE = 2;</code>
     */
    public static final int YELLOW_PLATE_VALUE = 2;
    /**
     * <pre>
     *白;
     * </pre>
     *
     * <code>WHITE_PLATE = 3;</code>
     */
    public static final int WHITE_PLATE_VALUE = 3;
    /**
     * <pre>
     *黑;
     * </pre>
     *
     * <code>BLACK_PLATE = 4;</code>
     */
    public static final int BLACK_PLATE_VALUE = 4;
    /**
     * <pre>
     *黄绿双色;
     * </pre>
     *
     * <code>YELLOW_GREEN_PLATE = 5;</code>
     */
    public static final int YELLOW_GREEN_PLATE_VALUE = 5;
    /**
     * <pre>
     *渐变绿
     * </pre>
     *
     * <code>GRADIENT_GREEN_PLATE = 6;</code>
     */
    public static final int GRADIENT_GREEN_PLATE_VALUE = 6;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static PlateColor valueOf(int value) {
      return forNumber(value);
    }

    public static PlateColor forNumber(int value) {
      switch (value) {
        case 0: return UNKNOWN_PLATE_COLOR;
        case 1: return BLUE_PLATE;
        case 2: return YELLOW_PLATE;
        case 3: return WHITE_PLATE;
        case 4: return BLACK_PLATE;
        case 5: return YELLOW_GREEN_PLATE;
        case 6: return GRADIENT_GREEN_PLATE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<PlateColor>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        PlateColor> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<PlateColor>() {
            public PlateColor findValueByNumber(int number) {
              return PlateColor.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.ParticipantData.getDescriptor().getEnumTypes().get(0);
    }

    private static final PlateColor[] VALUES = values();

    public static PlateColor valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private PlateColor(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.ParticipantData.PlateColor)
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.ParticipantData.VehicleColor}
   */
  public enum VehicleColor
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *未知
     * </pre>
     *
     * <code>UNKNOWN_VEHICEL_COLOR = 0;</code>
     */
    UNKNOWN_VEHICEL_COLOR(0),
    /**
     * <pre>
     *白色
     * </pre>
     *
     * <code>WHITE = 1;</code>
     */
    WHITE(1),
    /**
     * <pre>
     *灰色
     * </pre>
     *
     * <code>GRAY = 2;</code>
     */
    GRAY(2),
    /**
     * <pre>
     *黄色
     * </pre>
     *
     * <code>YELLOW = 3;</code>
     */
    YELLOW(3),
    /**
     * <pre>
     *粉色
     * </pre>
     *
     * <code>PINK = 4;</code>
     */
    PINK(4),
    /**
     * <pre>
     *红色
     * </pre>
     *
     * <code>RED = 5;</code>
     */
    RED(5),
    /**
     * <pre>
     *绿色
     * </pre>
     *
     * <code>GREEN = 6;</code>
     */
    GREEN(6),
    /**
     * <pre>
     *蓝色
     * </pre>
     *
     * <code>BLUE = 7;</code>
     */
    BLUE(7),
    /**
     * <pre>
     *棕色
     * </pre>
     *
     * <code>BROWN = 8;</code>
     */
    BROWN(8),
    /**
     * <pre>
     *黑色
     * </pre>
     *
     * <code>BLACK = 9;</code>
     */
    BLACK(9),
    /**
     * <pre>
     *紫色
     * </pre>
     *
     * <code>PURPLE = 10;</code>
     */
    PURPLE(10),
    /**
     * <pre>
     *其他
     * </pre>
     *
     * <code>OTHER = 11;</code>
     */
    OTHER(11),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *未知
     * </pre>
     *
     * <code>UNKNOWN_VEHICEL_COLOR = 0;</code>
     */
    public static final int UNKNOWN_VEHICEL_COLOR_VALUE = 0;
    /**
     * <pre>
     *白色
     * </pre>
     *
     * <code>WHITE = 1;</code>
     */
    public static final int WHITE_VALUE = 1;
    /**
     * <pre>
     *灰色
     * </pre>
     *
     * <code>GRAY = 2;</code>
     */
    public static final int GRAY_VALUE = 2;
    /**
     * <pre>
     *黄色
     * </pre>
     *
     * <code>YELLOW = 3;</code>
     */
    public static final int YELLOW_VALUE = 3;
    /**
     * <pre>
     *粉色
     * </pre>
     *
     * <code>PINK = 4;</code>
     */
    public static final int PINK_VALUE = 4;
    /**
     * <pre>
     *红色
     * </pre>
     *
     * <code>RED = 5;</code>
     */
    public static final int RED_VALUE = 5;
    /**
     * <pre>
     *绿色
     * </pre>
     *
     * <code>GREEN = 6;</code>
     */
    public static final int GREEN_VALUE = 6;
    /**
     * <pre>
     *蓝色
     * </pre>
     *
     * <code>BLUE = 7;</code>
     */
    public static final int BLUE_VALUE = 7;
    /**
     * <pre>
     *棕色
     * </pre>
     *
     * <code>BROWN = 8;</code>
     */
    public static final int BROWN_VALUE = 8;
    /**
     * <pre>
     *黑色
     * </pre>
     *
     * <code>BLACK = 9;</code>
     */
    public static final int BLACK_VALUE = 9;
    /**
     * <pre>
     *紫色
     * </pre>
     *
     * <code>PURPLE = 10;</code>
     */
    public static final int PURPLE_VALUE = 10;
    /**
     * <pre>
     *其他
     * </pre>
     *
     * <code>OTHER = 11;</code>
     */
    public static final int OTHER_VALUE = 11;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static VehicleColor valueOf(int value) {
      return forNumber(value);
    }

    public static VehicleColor forNumber(int value) {
      switch (value) {
        case 0: return UNKNOWN_VEHICEL_COLOR;
        case 1: return WHITE;
        case 2: return GRAY;
        case 3: return YELLOW;
        case 4: return PINK;
        case 5: return RED;
        case 6: return GREEN;
        case 7: return BLUE;
        case 8: return BROWN;
        case 9: return BLACK;
        case 10: return PURPLE;
        case 11: return OTHER;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<VehicleColor>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        VehicleColor> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<VehicleColor>() {
            public VehicleColor findValueByNumber(int number) {
              return VehicleColor.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.ParticipantData.getDescriptor().getEnumTypes().get(1);
    }

    private static final VehicleColor[] VALUES = values();

    public static VehicleColor valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private VehicleColor(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.ParticipantData.VehicleColor)
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.ParticipantData.ParticipantTypeExt}
   */
  public enum ParticipantTypeExt
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 未知障碍物
     * </pre>
     *
     * <code>UNKNOWN_OBJECT_TYPE_EXT = 0;</code>
     */
    UNKNOWN_OBJECT_TYPE_EXT(0),
    /**
     * <pre>
     * 未知可移动障碍物
     * </pre>
     *
     * <code>UNKNOWN_MOVABLE = 1;</code>
     */
    UNKNOWN_MOVABLE(1),
    /**
     * <pre>
     * 未知不可移动障碍物
     * </pre>
     *
     * <code>UNKNOWN_UNMOVABLE = 2;</code>
     */
    UNKNOWN_UNMOVABLE(2),
    /**
     * <pre>
     * 轿车、SUV
     * </pre>
     *
     * <code>CAR = 3;</code>
     */
    CAR(3),
    /**
     * <pre>
     * 面包车
     * </pre>
     *
     * <code>VAN = 4;</code>
     */
    VAN(4),
    /**
     * <pre>
     * 卡车
     * </pre>
     *
     * <code>TRUCK = 5;</code>
     */
    TRUCK(5),
    /**
     * <pre>
     * 大巴
     * </pre>
     *
     * <code>BUS = 6;</code>
     */
    BUS(6),
    /**
     * <pre>
     * 自行车
     * </pre>
     *
     * <code>CYCLIST = 7;</code>
     */
    CYCLIST(7),
    /**
     * <pre>
     *摩托车
     * </pre>
     *
     * <code>MOTORCYCLIST = 8;</code>
     */
    MOTORCYCLIST(8),
    /**
     * <pre>
     *三轮车、老年人代步车
     * </pre>
     *
     * <code>TRICYCLIST = 9;</code>
     */
    TRICYCLIST(9),
    /**
     * <pre>
     *行人
     * </pre>
     *
     * <code>PEDESTRIAN = 10;</code>
     */
    PEDESTRIAN(10),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * 未知障碍物
     * </pre>
     *
     * <code>UNKNOWN_OBJECT_TYPE_EXT = 0;</code>
     */
    public static final int UNKNOWN_OBJECT_TYPE_EXT_VALUE = 0;
    /**
     * <pre>
     * 未知可移动障碍物
     * </pre>
     *
     * <code>UNKNOWN_MOVABLE = 1;</code>
     */
    public static final int UNKNOWN_MOVABLE_VALUE = 1;
    /**
     * <pre>
     * 未知不可移动障碍物
     * </pre>
     *
     * <code>UNKNOWN_UNMOVABLE = 2;</code>
     */
    public static final int UNKNOWN_UNMOVABLE_VALUE = 2;
    /**
     * <pre>
     * 轿车、SUV
     * </pre>
     *
     * <code>CAR = 3;</code>
     */
    public static final int CAR_VALUE = 3;
    /**
     * <pre>
     * 面包车
     * </pre>
     *
     * <code>VAN = 4;</code>
     */
    public static final int VAN_VALUE = 4;
    /**
     * <pre>
     * 卡车
     * </pre>
     *
     * <code>TRUCK = 5;</code>
     */
    public static final int TRUCK_VALUE = 5;
    /**
     * <pre>
     * 大巴
     * </pre>
     *
     * <code>BUS = 6;</code>
     */
    public static final int BUS_VALUE = 6;
    /**
     * <pre>
     * 自行车
     * </pre>
     *
     * <code>CYCLIST = 7;</code>
     */
    public static final int CYCLIST_VALUE = 7;
    /**
     * <pre>
     *摩托车
     * </pre>
     *
     * <code>MOTORCYCLIST = 8;</code>
     */
    public static final int MOTORCYCLIST_VALUE = 8;
    /**
     * <pre>
     *三轮车、老年人代步车
     * </pre>
     *
     * <code>TRICYCLIST = 9;</code>
     */
    public static final int TRICYCLIST_VALUE = 9;
    /**
     * <pre>
     *行人
     * </pre>
     *
     * <code>PEDESTRIAN = 10;</code>
     */
    public static final int PEDESTRIAN_VALUE = 10;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ParticipantTypeExt valueOf(int value) {
      return forNumber(value);
    }

    public static ParticipantTypeExt forNumber(int value) {
      switch (value) {
        case 0: return UNKNOWN_OBJECT_TYPE_EXT;
        case 1: return UNKNOWN_MOVABLE;
        case 2: return UNKNOWN_UNMOVABLE;
        case 3: return CAR;
        case 4: return VAN;
        case 5: return TRUCK;
        case 6: return BUS;
        case 7: return CYCLIST;
        case 8: return MOTORCYCLIST;
        case 9: return TRICYCLIST;
        case 10: return PEDESTRIAN;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ParticipantTypeExt>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ParticipantTypeExt> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ParticipantTypeExt>() {
            public ParticipantTypeExt findValueByNumber(int number) {
              return ParticipantTypeExt.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.ParticipantData.getDescriptor().getEnumTypes().get(2);
    }

    private static final ParticipantTypeExt[] VALUES = values();

    public static ParticipantTypeExt valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ParticipantTypeExt(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.ParticipantData.ParticipantTypeExt)
  }

  public static final int PTCID_FIELD_NUMBER = 1;
  private long ptcId_;
  /**
   * <pre>
   * 目标对象ID,相同ID表示同一个目标物。
   * </pre>
   *
   * <code>uint64 ptcId = 1;</code>
   */
  public long getPtcId() {
    return ptcId_;
  }

  public static final int PTCTYPE_FIELD_NUMBER = 2;
  private int ptcType_;
  /**
   * <pre>
   *路侧单元检测到的交通参与者类型。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 2;</code>
   */
  public int getPtcTypeValue() {
    return ptcType_;
  }
  /**
   * <pre>
   *路侧单元检测到的交通参与者类型。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 2;</code>
   */
  public road.data.proto.ParticipantType getPtcType() {
    @SuppressWarnings("deprecation")
    road.data.proto.ParticipantType result = road.data.proto.ParticipantType.valueOf(ptcType_);
    return result == null ? road.data.proto.ParticipantType.UNRECOGNIZED : result;
  }

  public static final int DATASOURCE_FIELD_NUMBER = 3;
  private int dataSource_;
  /**
   * <pre>
   * 0、未知数据源类型; 1、RSU 自身信息; 2、来源于参与者自身的v2x广播消息;3、来源于视频传感器; 4、来源于微波雷达传感器; 5、来源于地磁线圈传感器; 6、来源于激光雷达传感器; 7、2 类或以上感知数据的融合结果; 8~255 保留
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
   */
  public int getDataSourceValue() {
    return dataSource_;
  }
  /**
   * <pre>
   * 0、未知数据源类型; 1、RSU 自身信息; 2、来源于参与者自身的v2x广播消息;3、来源于视频传感器; 4、来源于微波雷达传感器; 5、来源于地磁线圈传感器; 6、来源于激光雷达传感器; 7、2 类或以上感知数据的融合结果; 8~255 保留
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
   */
  public road.data.proto.DataSource getDataSource() {
    @SuppressWarnings("deprecation")
    road.data.proto.DataSource result = road.data.proto.DataSource.valueOf(dataSource_);
    return result == null ? road.data.proto.DataSource.UNRECOGNIZED : result;
  }

  public static final int DEVICEIDLIST_FIELD_NUMBER = 4;
  private volatile java.lang.Object deviceIdList_;
  /**
   * <pre>
   *  数据融合的来源设备id，json数组
   * </pre>
   *
   * <code>string deviceIdList = 4;</code>
   */
  public java.lang.String getDeviceIdList() {
    java.lang.Object ref = deviceIdList_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deviceIdList_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *  数据融合的来源设备id，json数组
   * </pre>
   *
   * <code>string deviceIdList = 4;</code>
   */
  public com.google.protobuf.ByteString
      getDeviceIdListBytes() {
    java.lang.Object ref = deviceIdList_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deviceIdList_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 5;
  private long timestamp_;
  /**
   * <pre>
   *  时间戳
   * </pre>
   *
   * <code>uint64 timestamp = 5;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int TIMECONFIDENCE_FIELD_NUMBER = 6;
  private int timeConfidence_;
  /**
   * <pre>
   *可选，事件置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 6;</code>
   */
  public int getTimeConfidenceValue() {
    return timeConfidence_;
  }
  /**
   * <pre>
   *可选，事件置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 6;</code>
   */
  public road.data.proto.TimeConfidence getTimeConfidence() {
    @SuppressWarnings("deprecation")
    road.data.proto.TimeConfidence result = road.data.proto.TimeConfidence.valueOf(timeConfidence_);
    return result == null ? road.data.proto.TimeConfidence.UNRECOGNIZED : result;
  }

  public static final int PTCPOS_FIELD_NUMBER = 7;
  private road.data.proto.Position3D ptcPos_;
  /**
   * <pre>
   * 定义经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
   */
  public boolean hasPtcPos() {
    return ptcPos_ != null;
  }
  /**
   * <pre>
   * 定义经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
   */
  public road.data.proto.Position3D getPtcPos() {
    return ptcPos_ == null ? road.data.proto.Position3D.getDefaultInstance() : ptcPos_;
  }
  /**
   * <pre>
   * 定义经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
   */
  public road.data.proto.Position3DOrBuilder getPtcPosOrBuilder() {
    return getPtcPos();
  }

  public static final int MAPLOCATION_FIELD_NUMBER = 8;
  private road.data.proto.MapLocation mapLocation_;
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
   */
  public boolean hasMapLocation() {
    return mapLocation_ != null;
  }
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
   */
  public road.data.proto.MapLocation getMapLocation() {
    return mapLocation_ == null ? road.data.proto.MapLocation.getDefaultInstance() : mapLocation_;
  }
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
   */
  public road.data.proto.MapLocationOrBuilder getMapLocationOrBuilder() {
    return getMapLocation();
  }

  public static final int POSCONFID_FIELD_NUMBER = 9;
  private road.data.proto.PositionConfidenceSet posConfid_;
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
   */
  public boolean hasPosConfid() {
    return posConfid_ != null;
  }
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
   */
  public road.data.proto.PositionConfidenceSet getPosConfid() {
    return posConfid_ == null ? road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
  }
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
   */
  public road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder() {
    return getPosConfid();
  }

  public static final int SPEED_FIELD_NUMBER = 10;
  private int speed_;
  /**
   * <pre>
   * 单位为0.02 m/s
   * </pre>
   *
   * <code>uint32 speed = 10;</code>
   */
  public int getSpeed() {
    return speed_;
  }

  public static final int HEADING_FIELD_NUMBER = 11;
  private int heading_;
  /**
   * <pre>
   *车辆航向角。为车头方向与正北方向的顺时针夹角。分辨率0.0125度，范围0到359.9875度
   * </pre>
   *
   * <code>uint32 heading = 11;</code>
   */
  public int getHeading() {
    return heading_;
  }

  public static final int MOTIONCONFID_FIELD_NUMBER = 12;
  private road.data.proto.MotionConfidenceSet motionConfid_;
  /**
   * <pre>
   *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  public boolean hasMotionConfid() {
    return motionConfid_ != null;
  }
  /**
   * <pre>
   *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  public road.data.proto.MotionConfidenceSet getMotionConfid() {
    return motionConfid_ == null ? road.data.proto.MotionConfidenceSet.getDefaultInstance() : motionConfid_;
  }
  /**
   * <pre>
   *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  public road.data.proto.MotionConfidenceSetOrBuilder getMotionConfidOrBuilder() {
    return getMotionConfid();
  }

  public static final int ACCELSET_FIELD_NUMBER = 13;
  private road.data.proto.AccelerationSet4Way accelSet_;
  /**
   * <pre>
   * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
   */
  public boolean hasAccelSet() {
    return accelSet_ != null;
  }
  /**
   * <pre>
   * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
   */
  public road.data.proto.AccelerationSet4Way getAccelSet() {
    return accelSet_ == null ? road.data.proto.AccelerationSet4Way.getDefaultInstance() : accelSet_;
  }
  /**
   * <pre>
   * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
   */
  public road.data.proto.AccelerationSet4WayOrBuilder getAccelSetOrBuilder() {
    return getAccelSet();
  }

  public static final int ACCELERATIONCONFID_FIELD_NUMBER = 14;
  private road.data.proto.AccelerationConfidence accelerationConfid_;
  /**
   * <pre>
   *可选，目标四轴加速度置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
   */
  public boolean hasAccelerationConfid() {
    return accelerationConfid_ != null;
  }
  /**
   * <pre>
   *可选，目标四轴加速度置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
   */
  public road.data.proto.AccelerationConfidence getAccelerationConfid() {
    return accelerationConfid_ == null ? road.data.proto.AccelerationConfidence.getDefaultInstance() : accelerationConfid_;
  }
  /**
   * <pre>
   *可选，目标四轴加速度置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
   */
  public road.data.proto.AccelerationConfidenceOrBuilder getAccelerationConfidOrBuilder() {
    return getAccelerationConfid();
  }

  public static final int PTCSIZE_FIELD_NUMBER = 15;
  private road.data.proto.ParticipantSize ptcSize_;
  /**
   * <pre>
   *可选，交通参与者尺寸信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
   */
  public boolean hasPtcSize() {
    return ptcSize_ != null;
  }
  /**
   * <pre>
   *可选，交通参与者尺寸信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
   */
  public road.data.proto.ParticipantSize getPtcSize() {
    return ptcSize_ == null ? road.data.proto.ParticipantSize.getDefaultInstance() : ptcSize_;
  }
  /**
   * <pre>
   *可选，交通参与者尺寸信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
   */
  public road.data.proto.ParticipantSizeOrBuilder getPtcSizeOrBuilder() {
    return getPtcSize();
  }

  public static final int VEHICLEBAND_FIELD_NUMBER = 16;
  private volatile java.lang.Object vehicleBand_;
  /**
   * <pre>
   *可选，车辆品牌
   * </pre>
   *
   * <code>string vehicleBand = 16;</code>
   */
  public java.lang.String getVehicleBand() {
    java.lang.Object ref = vehicleBand_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      vehicleBand_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，车辆品牌
   * </pre>
   *
   * <code>string vehicleBand = 16;</code>
   */
  public com.google.protobuf.ByteString
      getVehicleBandBytes() {
    java.lang.Object ref = vehicleBand_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      vehicleBand_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int VEHICLETYPE_FIELD_NUMBER = 17;
  private int vehicleType_;
  /**
   * <pre>
   *可选，车型类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 17;</code>
   */
  public int getVehicleTypeValue() {
    return vehicleType_;
  }
  /**
   * <pre>
   *可选，车型类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 17;</code>
   */
  public road.data.proto.VehicleType getVehicleType() {
    @SuppressWarnings("deprecation")
    road.data.proto.VehicleType result = road.data.proto.VehicleType.valueOf(vehicleType_);
    return result == null ? road.data.proto.VehicleType.UNRECOGNIZED : result;
  }

  public static final int PLATENO_FIELD_NUMBER = 18;
  private volatile java.lang.Object plateNo_;
  /**
   * <pre>
   *可选，车牌号，字符串，最大为36个字符，支持中文和数字
   * </pre>
   *
   * <code>string plateNo = 18;</code>
   */
  public java.lang.String getPlateNo() {
    java.lang.Object ref = plateNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      plateNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，车牌号，字符串，最大为36个字符，支持中文和数字
   * </pre>
   *
   * <code>string plateNo = 18;</code>
   */
  public com.google.protobuf.ByteString
      getPlateNoBytes() {
    java.lang.Object ref = plateNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      plateNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PLATETYPE_FIELD_NUMBER = 19;
  private int plateType_;
  /**
   * <pre>
   *可选，车牌类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PlateType plateType = 19;</code>
   */
  public int getPlateTypeValue() {
    return plateType_;
  }
  /**
   * <pre>
   *可选，车牌类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PlateType plateType = 19;</code>
   */
  public road.data.proto.PlateType getPlateType() {
    @SuppressWarnings("deprecation")
    road.data.proto.PlateType result = road.data.proto.PlateType.valueOf(plateType_);
    return result == null ? road.data.proto.PlateType.UNRECOGNIZED : result;
  }

  public static final int PLATECOLOR_FIELD_NUMBER = 20;
  private int plateColor_;
  /**
   * <pre>
   *可选，车牌颜色
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantData.PlateColor plateColor = 20;</code>
   */
  public int getPlateColorValue() {
    return plateColor_;
  }
  /**
   * <pre>
   *可选，车牌颜色
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantData.PlateColor plateColor = 20;</code>
   */
  public road.data.proto.ParticipantData.PlateColor getPlateColor() {
    @SuppressWarnings("deprecation")
    road.data.proto.ParticipantData.PlateColor result = road.data.proto.ParticipantData.PlateColor.valueOf(plateColor_);
    return result == null ? road.data.proto.ParticipantData.PlateColor.UNRECOGNIZED : result;
  }

  public static final int VEHICLECOLOR_FIELD_NUMBER = 21;
  private int vehicleColor_;
  /**
   * <pre>
   *可选，车辆颜色
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantData.VehicleColor vehicleColor = 21;</code>
   */
  public int getVehicleColorValue() {
    return vehicleColor_;
  }
  /**
   * <pre>
   *可选，车辆颜色
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantData.VehicleColor vehicleColor = 21;</code>
   */
  public road.data.proto.ParticipantData.VehicleColor getVehicleColor() {
    @SuppressWarnings("deprecation")
    road.data.proto.ParticipantData.VehicleColor result = road.data.proto.ParticipantData.VehicleColor.valueOf(vehicleColor_);
    return result == null ? road.data.proto.ParticipantData.VehicleColor.UNRECOGNIZED : result;
  }

  public static final int PTCSIZECONFID_FIELD_NUMBER = 22;
  private road.data.proto.ParticipantSizeConfidence ptcSizeConfid_;
  /**
   * <pre>
   * 可选，目标尺寸置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
   */
  public boolean hasPtcSizeConfid() {
    return ptcSizeConfid_ != null;
  }
  /**
   * <pre>
   * 可选，目标尺寸置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
   */
  public road.data.proto.ParticipantSizeConfidence getPtcSizeConfid() {
    return ptcSizeConfid_ == null ? road.data.proto.ParticipantSizeConfidence.getDefaultInstance() : ptcSizeConfid_;
  }
  /**
   * <pre>
   * 可选，目标尺寸置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
   */
  public road.data.proto.ParticipantSizeConfidenceOrBuilder getPtcSizeConfidOrBuilder() {
    return getPtcSizeConfid();
  }

  public static final int PTCTYPEEXT_FIELD_NUMBER = 23;
  private int ptcTypeExt_;
  /**
   * <pre>
   * 可选，目标类型扩展
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantData.ParticipantTypeExt ptcTypeExt = 23;</code>
   */
  public int getPtcTypeExtValue() {
    return ptcTypeExt_;
  }
  /**
   * <pre>
   * 可选，目标类型扩展
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantData.ParticipantTypeExt ptcTypeExt = 23;</code>
   */
  public road.data.proto.ParticipantData.ParticipantTypeExt getPtcTypeExt() {
    @SuppressWarnings("deprecation")
    road.data.proto.ParticipantData.ParticipantTypeExt result = road.data.proto.ParticipantData.ParticipantTypeExt.valueOf(ptcTypeExt_);
    return result == null ? road.data.proto.ParticipantData.ParticipantTypeExt.UNRECOGNIZED : result;
  }

  public static final int PTCTYPEEXTCONFID_FIELD_NUMBER = 24;
  private int ptcTypeExtConfid_;
  /**
   * <pre>
   * 可选，定义目标类型扩展的置信度;分辨率为0.005。
   * </pre>
   *
   * <code>uint32 ptcTypeExtConfid = 24;</code>
   */
  public int getPtcTypeExtConfid() {
    return ptcTypeExtConfid_;
  }

  public static final int STATUSDURATION_FIELD_NUMBER = 25;
  private int statusDuration_;
  /**
   * <pre>
   * 可选，以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据。
   * </pre>
   *
   * <code>uint32 statusDuration = 25;</code>
   */
  public int getStatusDuration() {
    return statusDuration_;
  }

  public static final int PATHHISTORY_FIELD_NUMBER = 26;
  private java.util.List<road.data.proto.PathHistoryPoint> pathHistory_;
  /**
   * <pre>
   * 可选，目标历史轨迹
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
   */
  public java.util.List<road.data.proto.PathHistoryPoint> getPathHistoryList() {
    return pathHistory_;
  }
  /**
   * <pre>
   * 可选，目标历史轨迹
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
   */
  public java.util.List<? extends road.data.proto.PathHistoryPointOrBuilder> 
      getPathHistoryOrBuilderList() {
    return pathHistory_;
  }
  /**
   * <pre>
   * 可选，目标历史轨迹
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
   */
  public int getPathHistoryCount() {
    return pathHistory_.size();
  }
  /**
   * <pre>
   * 可选，目标历史轨迹
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
   */
  public road.data.proto.PathHistoryPoint getPathHistory(int index) {
    return pathHistory_.get(index);
  }
  /**
   * <pre>
   * 可选，目标历史轨迹
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
   */
  public road.data.proto.PathHistoryPointOrBuilder getPathHistoryOrBuilder(
      int index) {
    return pathHistory_.get(index);
  }

  public static final int TRACKING_FIELD_NUMBER = 27;
  private int tracking_;
  /**
   * <pre>
   * 可选，目标追踪时间，单位s
   * </pre>
   *
   * <code>uint32 tracking = 27;</code>
   */
  public int getTracking() {
    return tracking_;
  }

  public static final int POLYGON_FIELD_NUMBER = 28;
  private road.data.proto.Polygon polygon_;
  /**
   * <pre>
   * 可选，障碍物影响区域点集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
   */
  public boolean hasPolygon() {
    return polygon_ != null;
  }
  /**
   * <pre>
   * 可选，障碍物影响区域点集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
   */
  public road.data.proto.Polygon getPolygon() {
    return polygon_ == null ? road.data.proto.Polygon.getDefaultInstance() : polygon_;
  }
  /**
   * <pre>
   * 可选，障碍物影响区域点集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
   */
  public road.data.proto.PolygonOrBuilder getPolygonOrBuilder() {
    return getPolygon();
  }

  public static final int ID_FIELD_NUMBER = 29;
  private long id_;
  /**
   * <pre>
   *可选，数据唯一标识id
   * </pre>
   *
   * <code>uint64 id = 29;</code>
   */
  public long getId() {
    return id_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (ptcId_ != 0L) {
      output.writeUInt64(1, ptcId_);
    }
    if (ptcType_ != road.data.proto.ParticipantType.OBJECTTYPE_UNKNOWN.getNumber()) {
      output.writeEnum(2, ptcType_);
    }
    if (dataSource_ != road.data.proto.DataSource.DATA_SOURCE_UNKNOWN.getNumber()) {
      output.writeEnum(3, dataSource_);
    }
    if (!getDeviceIdListBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, deviceIdList_);
    }
    if (timestamp_ != 0L) {
      output.writeUInt64(5, timestamp_);
    }
    if (timeConfidence_ != road.data.proto.TimeConfidence.UNAVAILABLE.getNumber()) {
      output.writeEnum(6, timeConfidence_);
    }
    if (ptcPos_ != null) {
      output.writeMessage(7, getPtcPos());
    }
    if (mapLocation_ != null) {
      output.writeMessage(8, getMapLocation());
    }
    if (posConfid_ != null) {
      output.writeMessage(9, getPosConfid());
    }
    if (speed_ != 0) {
      output.writeUInt32(10, speed_);
    }
    if (heading_ != 0) {
      output.writeUInt32(11, heading_);
    }
    if (motionConfid_ != null) {
      output.writeMessage(12, getMotionConfid());
    }
    if (accelSet_ != null) {
      output.writeMessage(13, getAccelSet());
    }
    if (accelerationConfid_ != null) {
      output.writeMessage(14, getAccelerationConfid());
    }
    if (ptcSize_ != null) {
      output.writeMessage(15, getPtcSize());
    }
    if (!getVehicleBandBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 16, vehicleBand_);
    }
    if (vehicleType_ != road.data.proto.VehicleType.UNKNOWN_VEHICLE_CLASS.getNumber()) {
      output.writeEnum(17, vehicleType_);
    }
    if (!getPlateNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 18, plateNo_);
    }
    if (plateType_ != road.data.proto.PlateType.UNKNOWN_PLATE.getNumber()) {
      output.writeEnum(19, plateType_);
    }
    if (plateColor_ != road.data.proto.ParticipantData.PlateColor.UNKNOWN_PLATE_COLOR.getNumber()) {
      output.writeEnum(20, plateColor_);
    }
    if (vehicleColor_ != road.data.proto.ParticipantData.VehicleColor.UNKNOWN_VEHICEL_COLOR.getNumber()) {
      output.writeEnum(21, vehicleColor_);
    }
    if (ptcSizeConfid_ != null) {
      output.writeMessage(22, getPtcSizeConfid());
    }
    if (ptcTypeExt_ != road.data.proto.ParticipantData.ParticipantTypeExt.UNKNOWN_OBJECT_TYPE_EXT.getNumber()) {
      output.writeEnum(23, ptcTypeExt_);
    }
    if (ptcTypeExtConfid_ != 0) {
      output.writeUInt32(24, ptcTypeExtConfid_);
    }
    if (statusDuration_ != 0) {
      output.writeUInt32(25, statusDuration_);
    }
    for (int i = 0; i < pathHistory_.size(); i++) {
      output.writeMessage(26, pathHistory_.get(i));
    }
    if (tracking_ != 0) {
      output.writeUInt32(27, tracking_);
    }
    if (polygon_ != null) {
      output.writeMessage(28, getPolygon());
    }
    if (id_ != 0L) {
      output.writeUInt64(29, id_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (ptcId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, ptcId_);
    }
    if (ptcType_ != road.data.proto.ParticipantType.OBJECTTYPE_UNKNOWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, ptcType_);
    }
    if (dataSource_ != road.data.proto.DataSource.DATA_SOURCE_UNKNOWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, dataSource_);
    }
    if (!getDeviceIdListBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, deviceIdList_);
    }
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(5, timestamp_);
    }
    if (timeConfidence_ != road.data.proto.TimeConfidence.UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(6, timeConfidence_);
    }
    if (ptcPos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getPtcPos());
    }
    if (mapLocation_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, getMapLocation());
    }
    if (posConfid_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(9, getPosConfid());
    }
    if (speed_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(10, speed_);
    }
    if (heading_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(11, heading_);
    }
    if (motionConfid_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(12, getMotionConfid());
    }
    if (accelSet_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(13, getAccelSet());
    }
    if (accelerationConfid_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(14, getAccelerationConfid());
    }
    if (ptcSize_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(15, getPtcSize());
    }
    if (!getVehicleBandBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, vehicleBand_);
    }
    if (vehicleType_ != road.data.proto.VehicleType.UNKNOWN_VEHICLE_CLASS.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(17, vehicleType_);
    }
    if (!getPlateNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, plateNo_);
    }
    if (plateType_ != road.data.proto.PlateType.UNKNOWN_PLATE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(19, plateType_);
    }
    if (plateColor_ != road.data.proto.ParticipantData.PlateColor.UNKNOWN_PLATE_COLOR.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(20, plateColor_);
    }
    if (vehicleColor_ != road.data.proto.ParticipantData.VehicleColor.UNKNOWN_VEHICEL_COLOR.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(21, vehicleColor_);
    }
    if (ptcSizeConfid_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(22, getPtcSizeConfid());
    }
    if (ptcTypeExt_ != road.data.proto.ParticipantData.ParticipantTypeExt.UNKNOWN_OBJECT_TYPE_EXT.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(23, ptcTypeExt_);
    }
    if (ptcTypeExtConfid_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(24, ptcTypeExtConfid_);
    }
    if (statusDuration_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(25, statusDuration_);
    }
    for (int i = 0; i < pathHistory_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(26, pathHistory_.get(i));
    }
    if (tracking_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(27, tracking_);
    }
    if (polygon_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(28, getPolygon());
    }
    if (id_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(29, id_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ParticipantData)) {
      return super.equals(obj);
    }
    road.data.proto.ParticipantData other = (road.data.proto.ParticipantData) obj;

    if (getPtcId()
        != other.getPtcId()) return false;
    if (ptcType_ != other.ptcType_) return false;
    if (dataSource_ != other.dataSource_) return false;
    if (!getDeviceIdList()
        .equals(other.getDeviceIdList())) return false;
    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (timeConfidence_ != other.timeConfidence_) return false;
    if (hasPtcPos() != other.hasPtcPos()) return false;
    if (hasPtcPos()) {
      if (!getPtcPos()
          .equals(other.getPtcPos())) return false;
    }
    if (hasMapLocation() != other.hasMapLocation()) return false;
    if (hasMapLocation()) {
      if (!getMapLocation()
          .equals(other.getMapLocation())) return false;
    }
    if (hasPosConfid() != other.hasPosConfid()) return false;
    if (hasPosConfid()) {
      if (!getPosConfid()
          .equals(other.getPosConfid())) return false;
    }
    if (getSpeed()
        != other.getSpeed()) return false;
    if (getHeading()
        != other.getHeading()) return false;
    if (hasMotionConfid() != other.hasMotionConfid()) return false;
    if (hasMotionConfid()) {
      if (!getMotionConfid()
          .equals(other.getMotionConfid())) return false;
    }
    if (hasAccelSet() != other.hasAccelSet()) return false;
    if (hasAccelSet()) {
      if (!getAccelSet()
          .equals(other.getAccelSet())) return false;
    }
    if (hasAccelerationConfid() != other.hasAccelerationConfid()) return false;
    if (hasAccelerationConfid()) {
      if (!getAccelerationConfid()
          .equals(other.getAccelerationConfid())) return false;
    }
    if (hasPtcSize() != other.hasPtcSize()) return false;
    if (hasPtcSize()) {
      if (!getPtcSize()
          .equals(other.getPtcSize())) return false;
    }
    if (!getVehicleBand()
        .equals(other.getVehicleBand())) return false;
    if (vehicleType_ != other.vehicleType_) return false;
    if (!getPlateNo()
        .equals(other.getPlateNo())) return false;
    if (plateType_ != other.plateType_) return false;
    if (plateColor_ != other.plateColor_) return false;
    if (vehicleColor_ != other.vehicleColor_) return false;
    if (hasPtcSizeConfid() != other.hasPtcSizeConfid()) return false;
    if (hasPtcSizeConfid()) {
      if (!getPtcSizeConfid()
          .equals(other.getPtcSizeConfid())) return false;
    }
    if (ptcTypeExt_ != other.ptcTypeExt_) return false;
    if (getPtcTypeExtConfid()
        != other.getPtcTypeExtConfid()) return false;
    if (getStatusDuration()
        != other.getStatusDuration()) return false;
    if (!getPathHistoryList()
        .equals(other.getPathHistoryList())) return false;
    if (getTracking()
        != other.getTracking()) return false;
    if (hasPolygon() != other.hasPolygon()) return false;
    if (hasPolygon()) {
      if (!getPolygon()
          .equals(other.getPolygon())) return false;
    }
    if (getId()
        != other.getId()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + PTCID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getPtcId());
    hash = (37 * hash) + PTCTYPE_FIELD_NUMBER;
    hash = (53 * hash) + ptcType_;
    hash = (37 * hash) + DATASOURCE_FIELD_NUMBER;
    hash = (53 * hash) + dataSource_;
    hash = (37 * hash) + DEVICEIDLIST_FIELD_NUMBER;
    hash = (53 * hash) + getDeviceIdList().hashCode();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    hash = (37 * hash) + TIMECONFIDENCE_FIELD_NUMBER;
    hash = (53 * hash) + timeConfidence_;
    if (hasPtcPos()) {
      hash = (37 * hash) + PTCPOS_FIELD_NUMBER;
      hash = (53 * hash) + getPtcPos().hashCode();
    }
    if (hasMapLocation()) {
      hash = (37 * hash) + MAPLOCATION_FIELD_NUMBER;
      hash = (53 * hash) + getMapLocation().hashCode();
    }
    if (hasPosConfid()) {
      hash = (37 * hash) + POSCONFID_FIELD_NUMBER;
      hash = (53 * hash) + getPosConfid().hashCode();
    }
    hash = (37 * hash) + SPEED_FIELD_NUMBER;
    hash = (53 * hash) + getSpeed();
    hash = (37 * hash) + HEADING_FIELD_NUMBER;
    hash = (53 * hash) + getHeading();
    if (hasMotionConfid()) {
      hash = (37 * hash) + MOTIONCONFID_FIELD_NUMBER;
      hash = (53 * hash) + getMotionConfid().hashCode();
    }
    if (hasAccelSet()) {
      hash = (37 * hash) + ACCELSET_FIELD_NUMBER;
      hash = (53 * hash) + getAccelSet().hashCode();
    }
    if (hasAccelerationConfid()) {
      hash = (37 * hash) + ACCELERATIONCONFID_FIELD_NUMBER;
      hash = (53 * hash) + getAccelerationConfid().hashCode();
    }
    if (hasPtcSize()) {
      hash = (37 * hash) + PTCSIZE_FIELD_NUMBER;
      hash = (53 * hash) + getPtcSize().hashCode();
    }
    hash = (37 * hash) + VEHICLEBAND_FIELD_NUMBER;
    hash = (53 * hash) + getVehicleBand().hashCode();
    hash = (37 * hash) + VEHICLETYPE_FIELD_NUMBER;
    hash = (53 * hash) + vehicleType_;
    hash = (37 * hash) + PLATENO_FIELD_NUMBER;
    hash = (53 * hash) + getPlateNo().hashCode();
    hash = (37 * hash) + PLATETYPE_FIELD_NUMBER;
    hash = (53 * hash) + plateType_;
    hash = (37 * hash) + PLATECOLOR_FIELD_NUMBER;
    hash = (53 * hash) + plateColor_;
    hash = (37 * hash) + VEHICLECOLOR_FIELD_NUMBER;
    hash = (53 * hash) + vehicleColor_;
    if (hasPtcSizeConfid()) {
      hash = (37 * hash) + PTCSIZECONFID_FIELD_NUMBER;
      hash = (53 * hash) + getPtcSizeConfid().hashCode();
    }
    hash = (37 * hash) + PTCTYPEEXT_FIELD_NUMBER;
    hash = (53 * hash) + ptcTypeExt_;
    hash = (37 * hash) + PTCTYPEEXTCONFID_FIELD_NUMBER;
    hash = (53 * hash) + getPtcTypeExtConfid();
    hash = (37 * hash) + STATUSDURATION_FIELD_NUMBER;
    hash = (53 * hash) + getStatusDuration();
    if (getPathHistoryCount() > 0) {
      hash = (37 * hash) + PATHHISTORY_FIELD_NUMBER;
      hash = (53 * hash) + getPathHistoryList().hashCode();
    }
    hash = (37 * hash) + TRACKING_FIELD_NUMBER;
    hash = (53 * hash) + getTracking();
    if (hasPolygon()) {
      hash = (37 * hash) + POLYGON_FIELD_NUMBER;
      hash = (53 * hash) + getPolygon().hashCode();
    }
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getId());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ParticipantData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ParticipantData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ParticipantData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ParticipantData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ParticipantData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ParticipantData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ParticipantData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ParticipantData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ParticipantData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ParticipantData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ParticipantData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ParticipantData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ParticipantData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *交通参与者信息Participant   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ParticipantData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ParticipantData)
      road.data.proto.ParticipantDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ParticipantData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ParticipantData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ParticipantData.class, road.data.proto.ParticipantData.Builder.class);
    }

    // Construct using road.data.proto.ParticipantData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getPathHistoryFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      ptcId_ = 0L;

      ptcType_ = 0;

      dataSource_ = 0;

      deviceIdList_ = "";

      timestamp_ = 0L;

      timeConfidence_ = 0;

      if (ptcPosBuilder_ == null) {
        ptcPos_ = null;
      } else {
        ptcPos_ = null;
        ptcPosBuilder_ = null;
      }
      if (mapLocationBuilder_ == null) {
        mapLocation_ = null;
      } else {
        mapLocation_ = null;
        mapLocationBuilder_ = null;
      }
      if (posConfidBuilder_ == null) {
        posConfid_ = null;
      } else {
        posConfid_ = null;
        posConfidBuilder_ = null;
      }
      speed_ = 0;

      heading_ = 0;

      if (motionConfidBuilder_ == null) {
        motionConfid_ = null;
      } else {
        motionConfid_ = null;
        motionConfidBuilder_ = null;
      }
      if (accelSetBuilder_ == null) {
        accelSet_ = null;
      } else {
        accelSet_ = null;
        accelSetBuilder_ = null;
      }
      if (accelerationConfidBuilder_ == null) {
        accelerationConfid_ = null;
      } else {
        accelerationConfid_ = null;
        accelerationConfidBuilder_ = null;
      }
      if (ptcSizeBuilder_ == null) {
        ptcSize_ = null;
      } else {
        ptcSize_ = null;
        ptcSizeBuilder_ = null;
      }
      vehicleBand_ = "";

      vehicleType_ = 0;

      plateNo_ = "";

      plateType_ = 0;

      plateColor_ = 0;

      vehicleColor_ = 0;

      if (ptcSizeConfidBuilder_ == null) {
        ptcSizeConfid_ = null;
      } else {
        ptcSizeConfid_ = null;
        ptcSizeConfidBuilder_ = null;
      }
      ptcTypeExt_ = 0;

      ptcTypeExtConfid_ = 0;

      statusDuration_ = 0;

      if (pathHistoryBuilder_ == null) {
        pathHistory_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        pathHistoryBuilder_.clear();
      }
      tracking_ = 0;

      if (polygonBuilder_ == null) {
        polygon_ = null;
      } else {
        polygon_ = null;
        polygonBuilder_ = null;
      }
      id_ = 0L;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ParticipantData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ParticipantData getDefaultInstanceForType() {
      return road.data.proto.ParticipantData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ParticipantData build() {
      road.data.proto.ParticipantData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ParticipantData buildPartial() {
      road.data.proto.ParticipantData result = new road.data.proto.ParticipantData(this);
      int from_bitField0_ = bitField0_;
      result.ptcId_ = ptcId_;
      result.ptcType_ = ptcType_;
      result.dataSource_ = dataSource_;
      result.deviceIdList_ = deviceIdList_;
      result.timestamp_ = timestamp_;
      result.timeConfidence_ = timeConfidence_;
      if (ptcPosBuilder_ == null) {
        result.ptcPos_ = ptcPos_;
      } else {
        result.ptcPos_ = ptcPosBuilder_.build();
      }
      if (mapLocationBuilder_ == null) {
        result.mapLocation_ = mapLocation_;
      } else {
        result.mapLocation_ = mapLocationBuilder_.build();
      }
      if (posConfidBuilder_ == null) {
        result.posConfid_ = posConfid_;
      } else {
        result.posConfid_ = posConfidBuilder_.build();
      }
      result.speed_ = speed_;
      result.heading_ = heading_;
      if (motionConfidBuilder_ == null) {
        result.motionConfid_ = motionConfid_;
      } else {
        result.motionConfid_ = motionConfidBuilder_.build();
      }
      if (accelSetBuilder_ == null) {
        result.accelSet_ = accelSet_;
      } else {
        result.accelSet_ = accelSetBuilder_.build();
      }
      if (accelerationConfidBuilder_ == null) {
        result.accelerationConfid_ = accelerationConfid_;
      } else {
        result.accelerationConfid_ = accelerationConfidBuilder_.build();
      }
      if (ptcSizeBuilder_ == null) {
        result.ptcSize_ = ptcSize_;
      } else {
        result.ptcSize_ = ptcSizeBuilder_.build();
      }
      result.vehicleBand_ = vehicleBand_;
      result.vehicleType_ = vehicleType_;
      result.plateNo_ = plateNo_;
      result.plateType_ = plateType_;
      result.plateColor_ = plateColor_;
      result.vehicleColor_ = vehicleColor_;
      if (ptcSizeConfidBuilder_ == null) {
        result.ptcSizeConfid_ = ptcSizeConfid_;
      } else {
        result.ptcSizeConfid_ = ptcSizeConfidBuilder_.build();
      }
      result.ptcTypeExt_ = ptcTypeExt_;
      result.ptcTypeExtConfid_ = ptcTypeExtConfid_;
      result.statusDuration_ = statusDuration_;
      if (pathHistoryBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          pathHistory_ = java.util.Collections.unmodifiableList(pathHistory_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.pathHistory_ = pathHistory_;
      } else {
        result.pathHistory_ = pathHistoryBuilder_.build();
      }
      result.tracking_ = tracking_;
      if (polygonBuilder_ == null) {
        result.polygon_ = polygon_;
      } else {
        result.polygon_ = polygonBuilder_.build();
      }
      result.id_ = id_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ParticipantData) {
        return mergeFrom((road.data.proto.ParticipantData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ParticipantData other) {
      if (other == road.data.proto.ParticipantData.getDefaultInstance()) return this;
      if (other.getPtcId() != 0L) {
        setPtcId(other.getPtcId());
      }
      if (other.ptcType_ != 0) {
        setPtcTypeValue(other.getPtcTypeValue());
      }
      if (other.dataSource_ != 0) {
        setDataSourceValue(other.getDataSourceValue());
      }
      if (!other.getDeviceIdList().isEmpty()) {
        deviceIdList_ = other.deviceIdList_;
        onChanged();
      }
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (other.timeConfidence_ != 0) {
        setTimeConfidenceValue(other.getTimeConfidenceValue());
      }
      if (other.hasPtcPos()) {
        mergePtcPos(other.getPtcPos());
      }
      if (other.hasMapLocation()) {
        mergeMapLocation(other.getMapLocation());
      }
      if (other.hasPosConfid()) {
        mergePosConfid(other.getPosConfid());
      }
      if (other.getSpeed() != 0) {
        setSpeed(other.getSpeed());
      }
      if (other.getHeading() != 0) {
        setHeading(other.getHeading());
      }
      if (other.hasMotionConfid()) {
        mergeMotionConfid(other.getMotionConfid());
      }
      if (other.hasAccelSet()) {
        mergeAccelSet(other.getAccelSet());
      }
      if (other.hasAccelerationConfid()) {
        mergeAccelerationConfid(other.getAccelerationConfid());
      }
      if (other.hasPtcSize()) {
        mergePtcSize(other.getPtcSize());
      }
      if (!other.getVehicleBand().isEmpty()) {
        vehicleBand_ = other.vehicleBand_;
        onChanged();
      }
      if (other.vehicleType_ != 0) {
        setVehicleTypeValue(other.getVehicleTypeValue());
      }
      if (!other.getPlateNo().isEmpty()) {
        plateNo_ = other.plateNo_;
        onChanged();
      }
      if (other.plateType_ != 0) {
        setPlateTypeValue(other.getPlateTypeValue());
      }
      if (other.plateColor_ != 0) {
        setPlateColorValue(other.getPlateColorValue());
      }
      if (other.vehicleColor_ != 0) {
        setVehicleColorValue(other.getVehicleColorValue());
      }
      if (other.hasPtcSizeConfid()) {
        mergePtcSizeConfid(other.getPtcSizeConfid());
      }
      if (other.ptcTypeExt_ != 0) {
        setPtcTypeExtValue(other.getPtcTypeExtValue());
      }
      if (other.getPtcTypeExtConfid() != 0) {
        setPtcTypeExtConfid(other.getPtcTypeExtConfid());
      }
      if (other.getStatusDuration() != 0) {
        setStatusDuration(other.getStatusDuration());
      }
      if (pathHistoryBuilder_ == null) {
        if (!other.pathHistory_.isEmpty()) {
          if (pathHistory_.isEmpty()) {
            pathHistory_ = other.pathHistory_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePathHistoryIsMutable();
            pathHistory_.addAll(other.pathHistory_);
          }
          onChanged();
        }
      } else {
        if (!other.pathHistory_.isEmpty()) {
          if (pathHistoryBuilder_.isEmpty()) {
            pathHistoryBuilder_.dispose();
            pathHistoryBuilder_ = null;
            pathHistory_ = other.pathHistory_;
            bitField0_ = (bitField0_ & ~0x00000001);
            pathHistoryBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getPathHistoryFieldBuilder() : null;
          } else {
            pathHistoryBuilder_.addAllMessages(other.pathHistory_);
          }
        }
      }
      if (other.getTracking() != 0) {
        setTracking(other.getTracking());
      }
      if (other.hasPolygon()) {
        mergePolygon(other.getPolygon());
      }
      if (other.getId() != 0L) {
        setId(other.getId());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ParticipantData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ParticipantData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private long ptcId_ ;
    /**
     * <pre>
     * 目标对象ID,相同ID表示同一个目标物。
     * </pre>
     *
     * <code>uint64 ptcId = 1;</code>
     */
    public long getPtcId() {
      return ptcId_;
    }
    /**
     * <pre>
     * 目标对象ID,相同ID表示同一个目标物。
     * </pre>
     *
     * <code>uint64 ptcId = 1;</code>
     */
    public Builder setPtcId(long value) {
      
      ptcId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 目标对象ID,相同ID表示同一个目标物。
     * </pre>
     *
     * <code>uint64 ptcId = 1;</code>
     */
    public Builder clearPtcId() {
      
      ptcId_ = 0L;
      onChanged();
      return this;
    }

    private int ptcType_ = 0;
    /**
     * <pre>
     *路侧单元检测到的交通参与者类型。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 2;</code>
     */
    public int getPtcTypeValue() {
      return ptcType_;
    }
    /**
     * <pre>
     *路侧单元检测到的交通参与者类型。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 2;</code>
     */
    public Builder setPtcTypeValue(int value) {
      ptcType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *路侧单元检测到的交通参与者类型。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 2;</code>
     */
    public road.data.proto.ParticipantType getPtcType() {
      @SuppressWarnings("deprecation")
      road.data.proto.ParticipantType result = road.data.proto.ParticipantType.valueOf(ptcType_);
      return result == null ? road.data.proto.ParticipantType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *路侧单元检测到的交通参与者类型。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 2;</code>
     */
    public Builder setPtcType(road.data.proto.ParticipantType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      ptcType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *路侧单元检测到的交通参与者类型。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 2;</code>
     */
    public Builder clearPtcType() {
      
      ptcType_ = 0;
      onChanged();
      return this;
    }

    private int dataSource_ = 0;
    /**
     * <pre>
     * 0、未知数据源类型; 1、RSU 自身信息; 2、来源于参与者自身的v2x广播消息;3、来源于视频传感器; 4、来源于微波雷达传感器; 5、来源于地磁线圈传感器; 6、来源于激光雷达传感器; 7、2 类或以上感知数据的融合结果; 8~255 保留
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
     */
    public int getDataSourceValue() {
      return dataSource_;
    }
    /**
     * <pre>
     * 0、未知数据源类型; 1、RSU 自身信息; 2、来源于参与者自身的v2x广播消息;3、来源于视频传感器; 4、来源于微波雷达传感器; 5、来源于地磁线圈传感器; 6、来源于激光雷达传感器; 7、2 类或以上感知数据的融合结果; 8~255 保留
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
     */
    public Builder setDataSourceValue(int value) {
      dataSource_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 0、未知数据源类型; 1、RSU 自身信息; 2、来源于参与者自身的v2x广播消息;3、来源于视频传感器; 4、来源于微波雷达传感器; 5、来源于地磁线圈传感器; 6、来源于激光雷达传感器; 7、2 类或以上感知数据的融合结果; 8~255 保留
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
     */
    public road.data.proto.DataSource getDataSource() {
      @SuppressWarnings("deprecation")
      road.data.proto.DataSource result = road.data.proto.DataSource.valueOf(dataSource_);
      return result == null ? road.data.proto.DataSource.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 0、未知数据源类型; 1、RSU 自身信息; 2、来源于参与者自身的v2x广播消息;3、来源于视频传感器; 4、来源于微波雷达传感器; 5、来源于地磁线圈传感器; 6、来源于激光雷达传感器; 7、2 类或以上感知数据的融合结果; 8~255 保留
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
     */
    public Builder setDataSource(road.data.proto.DataSource value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      dataSource_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 0、未知数据源类型; 1、RSU 自身信息; 2、来源于参与者自身的v2x广播消息;3、来源于视频传感器; 4、来源于微波雷达传感器; 5、来源于地磁线圈传感器; 6、来源于激光雷达传感器; 7、2 类或以上感知数据的融合结果; 8~255 保留
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
     */
    public Builder clearDataSource() {
      
      dataSource_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object deviceIdList_ = "";
    /**
     * <pre>
     *  数据融合的来源设备id，json数组
     * </pre>
     *
     * <code>string deviceIdList = 4;</code>
     */
    public java.lang.String getDeviceIdList() {
      java.lang.Object ref = deviceIdList_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceIdList_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *  数据融合的来源设备id，json数组
     * </pre>
     *
     * <code>string deviceIdList = 4;</code>
     */
    public com.google.protobuf.ByteString
        getDeviceIdListBytes() {
      java.lang.Object ref = deviceIdList_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceIdList_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *  数据融合的来源设备id，json数组
     * </pre>
     *
     * <code>string deviceIdList = 4;</code>
     */
    public Builder setDeviceIdList(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deviceIdList_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *  数据融合的来源设备id，json数组
     * </pre>
     *
     * <code>string deviceIdList = 4;</code>
     */
    public Builder clearDeviceIdList() {
      
      deviceIdList_ = getDefaultInstance().getDeviceIdList();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *  数据融合的来源设备id，json数组
     * </pre>
     *
     * <code>string deviceIdList = 4;</code>
     */
    public Builder setDeviceIdListBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deviceIdList_ = value;
      onChanged();
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     *  时间戳
     * </pre>
     *
     * <code>uint64 timestamp = 5;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     *  时间戳
     * </pre>
     *
     * <code>uint64 timestamp = 5;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *  时间戳
     * </pre>
     *
     * <code>uint64 timestamp = 5;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private int timeConfidence_ = 0;
    /**
     * <pre>
     *可选，事件置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 6;</code>
     */
    public int getTimeConfidenceValue() {
      return timeConfidence_;
    }
    /**
     * <pre>
     *可选，事件置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 6;</code>
     */
    public Builder setTimeConfidenceValue(int value) {
      timeConfidence_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，事件置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 6;</code>
     */
    public road.data.proto.TimeConfidence getTimeConfidence() {
      @SuppressWarnings("deprecation")
      road.data.proto.TimeConfidence result = road.data.proto.TimeConfidence.valueOf(timeConfidence_);
      return result == null ? road.data.proto.TimeConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，事件置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 6;</code>
     */
    public Builder setTimeConfidence(road.data.proto.TimeConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      timeConfidence_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，事件置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 6;</code>
     */
    public Builder clearTimeConfidence() {
      
      timeConfidence_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.Position3D ptcPos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> ptcPosBuilder_;
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
     */
    public boolean hasPtcPos() {
      return ptcPosBuilder_ != null || ptcPos_ != null;
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
     */
    public road.data.proto.Position3D getPtcPos() {
      if (ptcPosBuilder_ == null) {
        return ptcPos_ == null ? road.data.proto.Position3D.getDefaultInstance() : ptcPos_;
      } else {
        return ptcPosBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
     */
    public Builder setPtcPos(road.data.proto.Position3D value) {
      if (ptcPosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ptcPos_ = value;
        onChanged();
      } else {
        ptcPosBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
     */
    public Builder setPtcPos(
        road.data.proto.Position3D.Builder builderForValue) {
      if (ptcPosBuilder_ == null) {
        ptcPos_ = builderForValue.build();
        onChanged();
      } else {
        ptcPosBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
     */
    public Builder mergePtcPos(road.data.proto.Position3D value) {
      if (ptcPosBuilder_ == null) {
        if (ptcPos_ != null) {
          ptcPos_ =
            road.data.proto.Position3D.newBuilder(ptcPos_).mergeFrom(value).buildPartial();
        } else {
          ptcPos_ = value;
        }
        onChanged();
      } else {
        ptcPosBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
     */
    public Builder clearPtcPos() {
      if (ptcPosBuilder_ == null) {
        ptcPos_ = null;
        onChanged();
      } else {
        ptcPos_ = null;
        ptcPosBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
     */
    public road.data.proto.Position3D.Builder getPtcPosBuilder() {
      
      onChanged();
      return getPtcPosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
     */
    public road.data.proto.Position3DOrBuilder getPtcPosOrBuilder() {
      if (ptcPosBuilder_ != null) {
        return ptcPosBuilder_.getMessageOrBuilder();
      } else {
        return ptcPos_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : ptcPos_;
      }
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getPtcPosFieldBuilder() {
      if (ptcPosBuilder_ == null) {
        ptcPosBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getPtcPos(),
                getParentForChildren(),
                isClean());
        ptcPos_ = null;
      }
      return ptcPosBuilder_;
    }

    private road.data.proto.MapLocation mapLocation_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MapLocation, road.data.proto.MapLocation.Builder, road.data.proto.MapLocationOrBuilder> mapLocationBuilder_;
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public boolean hasMapLocation() {
      return mapLocationBuilder_ != null || mapLocation_ != null;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public road.data.proto.MapLocation getMapLocation() {
      if (mapLocationBuilder_ == null) {
        return mapLocation_ == null ? road.data.proto.MapLocation.getDefaultInstance() : mapLocation_;
      } else {
        return mapLocationBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public Builder setMapLocation(road.data.proto.MapLocation value) {
      if (mapLocationBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        mapLocation_ = value;
        onChanged();
      } else {
        mapLocationBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public Builder setMapLocation(
        road.data.proto.MapLocation.Builder builderForValue) {
      if (mapLocationBuilder_ == null) {
        mapLocation_ = builderForValue.build();
        onChanged();
      } else {
        mapLocationBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public Builder mergeMapLocation(road.data.proto.MapLocation value) {
      if (mapLocationBuilder_ == null) {
        if (mapLocation_ != null) {
          mapLocation_ =
            road.data.proto.MapLocation.newBuilder(mapLocation_).mergeFrom(value).buildPartial();
        } else {
          mapLocation_ = value;
        }
        onChanged();
      } else {
        mapLocationBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public Builder clearMapLocation() {
      if (mapLocationBuilder_ == null) {
        mapLocation_ = null;
        onChanged();
      } else {
        mapLocation_ = null;
        mapLocationBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public road.data.proto.MapLocation.Builder getMapLocationBuilder() {
      
      onChanged();
      return getMapLocationFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public road.data.proto.MapLocationOrBuilder getMapLocationOrBuilder() {
      if (mapLocationBuilder_ != null) {
        return mapLocationBuilder_.getMessageOrBuilder();
      } else {
        return mapLocation_ == null ?
            road.data.proto.MapLocation.getDefaultInstance() : mapLocation_;
      }
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MapLocation, road.data.proto.MapLocation.Builder, road.data.proto.MapLocationOrBuilder> 
        getMapLocationFieldBuilder() {
      if (mapLocationBuilder_ == null) {
        mapLocationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.MapLocation, road.data.proto.MapLocation.Builder, road.data.proto.MapLocationOrBuilder>(
                getMapLocation(),
                getParentForChildren(),
                isClean());
        mapLocation_ = null;
      }
      return mapLocationBuilder_;
    }

    private road.data.proto.PositionConfidenceSet posConfid_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder> posConfidBuilder_;
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
     */
    public boolean hasPosConfid() {
      return posConfidBuilder_ != null || posConfid_ != null;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
     */
    public road.data.proto.PositionConfidenceSet getPosConfid() {
      if (posConfidBuilder_ == null) {
        return posConfid_ == null ? road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
      } else {
        return posConfidBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
     */
    public Builder setPosConfid(road.data.proto.PositionConfidenceSet value) {
      if (posConfidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        posConfid_ = value;
        onChanged();
      } else {
        posConfidBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
     */
    public Builder setPosConfid(
        road.data.proto.PositionConfidenceSet.Builder builderForValue) {
      if (posConfidBuilder_ == null) {
        posConfid_ = builderForValue.build();
        onChanged();
      } else {
        posConfidBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
     */
    public Builder mergePosConfid(road.data.proto.PositionConfidenceSet value) {
      if (posConfidBuilder_ == null) {
        if (posConfid_ != null) {
          posConfid_ =
            road.data.proto.PositionConfidenceSet.newBuilder(posConfid_).mergeFrom(value).buildPartial();
        } else {
          posConfid_ = value;
        }
        onChanged();
      } else {
        posConfidBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
     */
    public Builder clearPosConfid() {
      if (posConfidBuilder_ == null) {
        posConfid_ = null;
        onChanged();
      } else {
        posConfid_ = null;
        posConfidBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
     */
    public road.data.proto.PositionConfidenceSet.Builder getPosConfidBuilder() {
      
      onChanged();
      return getPosConfidFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
     */
    public road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder() {
      if (posConfidBuilder_ != null) {
        return posConfidBuilder_.getMessageOrBuilder();
      } else {
        return posConfid_ == null ?
            road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
      }
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder> 
        getPosConfidFieldBuilder() {
      if (posConfidBuilder_ == null) {
        posConfidBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder>(
                getPosConfid(),
                getParentForChildren(),
                isClean());
        posConfid_ = null;
      }
      return posConfidBuilder_;
    }

    private int speed_ ;
    /**
     * <pre>
     * 单位为0.02 m/s
     * </pre>
     *
     * <code>uint32 speed = 10;</code>
     */
    public int getSpeed() {
      return speed_;
    }
    /**
     * <pre>
     * 单位为0.02 m/s
     * </pre>
     *
     * <code>uint32 speed = 10;</code>
     */
    public Builder setSpeed(int value) {
      
      speed_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 单位为0.02 m/s
     * </pre>
     *
     * <code>uint32 speed = 10;</code>
     */
    public Builder clearSpeed() {
      
      speed_ = 0;
      onChanged();
      return this;
    }

    private int heading_ ;
    /**
     * <pre>
     *车辆航向角。为车头方向与正北方向的顺时针夹角。分辨率0.0125度，范围0到359.9875度
     * </pre>
     *
     * <code>uint32 heading = 11;</code>
     */
    public int getHeading() {
      return heading_;
    }
    /**
     * <pre>
     *车辆航向角。为车头方向与正北方向的顺时针夹角。分辨率0.0125度，范围0到359.9875度
     * </pre>
     *
     * <code>uint32 heading = 11;</code>
     */
    public Builder setHeading(int value) {
      
      heading_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *车辆航向角。为车头方向与正北方向的顺时针夹角。分辨率0.0125度，范围0到359.9875度
     * </pre>
     *
     * <code>uint32 heading = 11;</code>
     */
    public Builder clearHeading() {
      
      heading_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.MotionConfidenceSet motionConfid_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MotionConfidenceSet, road.data.proto.MotionConfidenceSet.Builder, road.data.proto.MotionConfidenceSetOrBuilder> motionConfidBuilder_;
    /**
     * <pre>
     *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public boolean hasMotionConfid() {
      return motionConfidBuilder_ != null || motionConfid_ != null;
    }
    /**
     * <pre>
     *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public road.data.proto.MotionConfidenceSet getMotionConfid() {
      if (motionConfidBuilder_ == null) {
        return motionConfid_ == null ? road.data.proto.MotionConfidenceSet.getDefaultInstance() : motionConfid_;
      } else {
        return motionConfidBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public Builder setMotionConfid(road.data.proto.MotionConfidenceSet value) {
      if (motionConfidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        motionConfid_ = value;
        onChanged();
      } else {
        motionConfidBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public Builder setMotionConfid(
        road.data.proto.MotionConfidenceSet.Builder builderForValue) {
      if (motionConfidBuilder_ == null) {
        motionConfid_ = builderForValue.build();
        onChanged();
      } else {
        motionConfidBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public Builder mergeMotionConfid(road.data.proto.MotionConfidenceSet value) {
      if (motionConfidBuilder_ == null) {
        if (motionConfid_ != null) {
          motionConfid_ =
            road.data.proto.MotionConfidenceSet.newBuilder(motionConfid_).mergeFrom(value).buildPartial();
        } else {
          motionConfid_ = value;
        }
        onChanged();
      } else {
        motionConfidBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public Builder clearMotionConfid() {
      if (motionConfidBuilder_ == null) {
        motionConfid_ = null;
        onChanged();
      } else {
        motionConfid_ = null;
        motionConfidBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public road.data.proto.MotionConfidenceSet.Builder getMotionConfidBuilder() {
      
      onChanged();
      return getMotionConfidFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    public road.data.proto.MotionConfidenceSetOrBuilder getMotionConfidOrBuilder() {
      if (motionConfidBuilder_ != null) {
        return motionConfidBuilder_.getMessageOrBuilder();
      } else {
        return motionConfid_ == null ?
            road.data.proto.MotionConfidenceSet.getDefaultInstance() : motionConfid_;
      }
    }
    /**
     * <pre>
     *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MotionConfidenceSet, road.data.proto.MotionConfidenceSet.Builder, road.data.proto.MotionConfidenceSetOrBuilder> 
        getMotionConfidFieldBuilder() {
      if (motionConfidBuilder_ == null) {
        motionConfidBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.MotionConfidenceSet, road.data.proto.MotionConfidenceSet.Builder, road.data.proto.MotionConfidenceSetOrBuilder>(
                getMotionConfid(),
                getParentForChildren(),
                isClean());
        motionConfid_ = null;
      }
      return motionConfidBuilder_;
    }

    private road.data.proto.AccelerationSet4Way accelSet_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AccelerationSet4Way, road.data.proto.AccelerationSet4Way.Builder, road.data.proto.AccelerationSet4WayOrBuilder> accelSetBuilder_;
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
     */
    public boolean hasAccelSet() {
      return accelSetBuilder_ != null || accelSet_ != null;
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
     */
    public road.data.proto.AccelerationSet4Way getAccelSet() {
      if (accelSetBuilder_ == null) {
        return accelSet_ == null ? road.data.proto.AccelerationSet4Way.getDefaultInstance() : accelSet_;
      } else {
        return accelSetBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
     */
    public Builder setAccelSet(road.data.proto.AccelerationSet4Way value) {
      if (accelSetBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        accelSet_ = value;
        onChanged();
      } else {
        accelSetBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
     */
    public Builder setAccelSet(
        road.data.proto.AccelerationSet4Way.Builder builderForValue) {
      if (accelSetBuilder_ == null) {
        accelSet_ = builderForValue.build();
        onChanged();
      } else {
        accelSetBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
     */
    public Builder mergeAccelSet(road.data.proto.AccelerationSet4Way value) {
      if (accelSetBuilder_ == null) {
        if (accelSet_ != null) {
          accelSet_ =
            road.data.proto.AccelerationSet4Way.newBuilder(accelSet_).mergeFrom(value).buildPartial();
        } else {
          accelSet_ = value;
        }
        onChanged();
      } else {
        accelSetBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
     */
    public Builder clearAccelSet() {
      if (accelSetBuilder_ == null) {
        accelSet_ = null;
        onChanged();
      } else {
        accelSet_ = null;
        accelSetBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
     */
    public road.data.proto.AccelerationSet4Way.Builder getAccelSetBuilder() {
      
      onChanged();
      return getAccelSetFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
     */
    public road.data.proto.AccelerationSet4WayOrBuilder getAccelSetOrBuilder() {
      if (accelSetBuilder_ != null) {
        return accelSetBuilder_.getMessageOrBuilder();
      } else {
        return accelSet_ == null ?
            road.data.proto.AccelerationSet4Way.getDefaultInstance() : accelSet_;
      }
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AccelerationSet4Way, road.data.proto.AccelerationSet4Way.Builder, road.data.proto.AccelerationSet4WayOrBuilder> 
        getAccelSetFieldBuilder() {
      if (accelSetBuilder_ == null) {
        accelSetBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.AccelerationSet4Way, road.data.proto.AccelerationSet4Way.Builder, road.data.proto.AccelerationSet4WayOrBuilder>(
                getAccelSet(),
                getParentForChildren(),
                isClean());
        accelSet_ = null;
      }
      return accelSetBuilder_;
    }

    private road.data.proto.AccelerationConfidence accelerationConfid_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AccelerationConfidence, road.data.proto.AccelerationConfidence.Builder, road.data.proto.AccelerationConfidenceOrBuilder> accelerationConfidBuilder_;
    /**
     * <pre>
     *可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
     */
    public boolean hasAccelerationConfid() {
      return accelerationConfidBuilder_ != null || accelerationConfid_ != null;
    }
    /**
     * <pre>
     *可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
     */
    public road.data.proto.AccelerationConfidence getAccelerationConfid() {
      if (accelerationConfidBuilder_ == null) {
        return accelerationConfid_ == null ? road.data.proto.AccelerationConfidence.getDefaultInstance() : accelerationConfid_;
      } else {
        return accelerationConfidBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
     */
    public Builder setAccelerationConfid(road.data.proto.AccelerationConfidence value) {
      if (accelerationConfidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        accelerationConfid_ = value;
        onChanged();
      } else {
        accelerationConfidBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
     */
    public Builder setAccelerationConfid(
        road.data.proto.AccelerationConfidence.Builder builderForValue) {
      if (accelerationConfidBuilder_ == null) {
        accelerationConfid_ = builderForValue.build();
        onChanged();
      } else {
        accelerationConfidBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
     */
    public Builder mergeAccelerationConfid(road.data.proto.AccelerationConfidence value) {
      if (accelerationConfidBuilder_ == null) {
        if (accelerationConfid_ != null) {
          accelerationConfid_ =
            road.data.proto.AccelerationConfidence.newBuilder(accelerationConfid_).mergeFrom(value).buildPartial();
        } else {
          accelerationConfid_ = value;
        }
        onChanged();
      } else {
        accelerationConfidBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
     */
    public Builder clearAccelerationConfid() {
      if (accelerationConfidBuilder_ == null) {
        accelerationConfid_ = null;
        onChanged();
      } else {
        accelerationConfid_ = null;
        accelerationConfidBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
     */
    public road.data.proto.AccelerationConfidence.Builder getAccelerationConfidBuilder() {
      
      onChanged();
      return getAccelerationConfidFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
     */
    public road.data.proto.AccelerationConfidenceOrBuilder getAccelerationConfidOrBuilder() {
      if (accelerationConfidBuilder_ != null) {
        return accelerationConfidBuilder_.getMessageOrBuilder();
      } else {
        return accelerationConfid_ == null ?
            road.data.proto.AccelerationConfidence.getDefaultInstance() : accelerationConfid_;
      }
    }
    /**
     * <pre>
     *可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AccelerationConfidence, road.data.proto.AccelerationConfidence.Builder, road.data.proto.AccelerationConfidenceOrBuilder> 
        getAccelerationConfidFieldBuilder() {
      if (accelerationConfidBuilder_ == null) {
        accelerationConfidBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.AccelerationConfidence, road.data.proto.AccelerationConfidence.Builder, road.data.proto.AccelerationConfidenceOrBuilder>(
                getAccelerationConfid(),
                getParentForChildren(),
                isClean());
        accelerationConfid_ = null;
      }
      return accelerationConfidBuilder_;
    }

    private road.data.proto.ParticipantSize ptcSize_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ParticipantSize, road.data.proto.ParticipantSize.Builder, road.data.proto.ParticipantSizeOrBuilder> ptcSizeBuilder_;
    /**
     * <pre>
     *可选，交通参与者尺寸信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
     */
    public boolean hasPtcSize() {
      return ptcSizeBuilder_ != null || ptcSize_ != null;
    }
    /**
     * <pre>
     *可选，交通参与者尺寸信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
     */
    public road.data.proto.ParticipantSize getPtcSize() {
      if (ptcSizeBuilder_ == null) {
        return ptcSize_ == null ? road.data.proto.ParticipantSize.getDefaultInstance() : ptcSize_;
      } else {
        return ptcSizeBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，交通参与者尺寸信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
     */
    public Builder setPtcSize(road.data.proto.ParticipantSize value) {
      if (ptcSizeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ptcSize_ = value;
        onChanged();
      } else {
        ptcSizeBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，交通参与者尺寸信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
     */
    public Builder setPtcSize(
        road.data.proto.ParticipantSize.Builder builderForValue) {
      if (ptcSizeBuilder_ == null) {
        ptcSize_ = builderForValue.build();
        onChanged();
      } else {
        ptcSizeBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，交通参与者尺寸信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
     */
    public Builder mergePtcSize(road.data.proto.ParticipantSize value) {
      if (ptcSizeBuilder_ == null) {
        if (ptcSize_ != null) {
          ptcSize_ =
            road.data.proto.ParticipantSize.newBuilder(ptcSize_).mergeFrom(value).buildPartial();
        } else {
          ptcSize_ = value;
        }
        onChanged();
      } else {
        ptcSizeBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，交通参与者尺寸信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
     */
    public Builder clearPtcSize() {
      if (ptcSizeBuilder_ == null) {
        ptcSize_ = null;
        onChanged();
      } else {
        ptcSize_ = null;
        ptcSizeBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，交通参与者尺寸信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
     */
    public road.data.proto.ParticipantSize.Builder getPtcSizeBuilder() {
      
      onChanged();
      return getPtcSizeFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，交通参与者尺寸信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
     */
    public road.data.proto.ParticipantSizeOrBuilder getPtcSizeOrBuilder() {
      if (ptcSizeBuilder_ != null) {
        return ptcSizeBuilder_.getMessageOrBuilder();
      } else {
        return ptcSize_ == null ?
            road.data.proto.ParticipantSize.getDefaultInstance() : ptcSize_;
      }
    }
    /**
     * <pre>
     *可选，交通参与者尺寸信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ParticipantSize, road.data.proto.ParticipantSize.Builder, road.data.proto.ParticipantSizeOrBuilder> 
        getPtcSizeFieldBuilder() {
      if (ptcSizeBuilder_ == null) {
        ptcSizeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ParticipantSize, road.data.proto.ParticipantSize.Builder, road.data.proto.ParticipantSizeOrBuilder>(
                getPtcSize(),
                getParentForChildren(),
                isClean());
        ptcSize_ = null;
      }
      return ptcSizeBuilder_;
    }

    private java.lang.Object vehicleBand_ = "";
    /**
     * <pre>
     *可选，车辆品牌
     * </pre>
     *
     * <code>string vehicleBand = 16;</code>
     */
    public java.lang.String getVehicleBand() {
      java.lang.Object ref = vehicleBand_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        vehicleBand_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，车辆品牌
     * </pre>
     *
     * <code>string vehicleBand = 16;</code>
     */
    public com.google.protobuf.ByteString
        getVehicleBandBytes() {
      java.lang.Object ref = vehicleBand_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        vehicleBand_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，车辆品牌
     * </pre>
     *
     * <code>string vehicleBand = 16;</code>
     */
    public Builder setVehicleBand(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      vehicleBand_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车辆品牌
     * </pre>
     *
     * <code>string vehicleBand = 16;</code>
     */
    public Builder clearVehicleBand() {
      
      vehicleBand_ = getDefaultInstance().getVehicleBand();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车辆品牌
     * </pre>
     *
     * <code>string vehicleBand = 16;</code>
     */
    public Builder setVehicleBandBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      vehicleBand_ = value;
      onChanged();
      return this;
    }

    private int vehicleType_ = 0;
    /**
     * <pre>
     *可选，车型类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 17;</code>
     */
    public int getVehicleTypeValue() {
      return vehicleType_;
    }
    /**
     * <pre>
     *可选，车型类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 17;</code>
     */
    public Builder setVehicleTypeValue(int value) {
      vehicleType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车型类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 17;</code>
     */
    public road.data.proto.VehicleType getVehicleType() {
      @SuppressWarnings("deprecation")
      road.data.proto.VehicleType result = road.data.proto.VehicleType.valueOf(vehicleType_);
      return result == null ? road.data.proto.VehicleType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，车型类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 17;</code>
     */
    public Builder setVehicleType(road.data.proto.VehicleType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      vehicleType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车型类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 17;</code>
     */
    public Builder clearVehicleType() {
      
      vehicleType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object plateNo_ = "";
    /**
     * <pre>
     *可选，车牌号，字符串，最大为36个字符，支持中文和数字
     * </pre>
     *
     * <code>string plateNo = 18;</code>
     */
    public java.lang.String getPlateNo() {
      java.lang.Object ref = plateNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        plateNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，车牌号，字符串，最大为36个字符，支持中文和数字
     * </pre>
     *
     * <code>string plateNo = 18;</code>
     */
    public com.google.protobuf.ByteString
        getPlateNoBytes() {
      java.lang.Object ref = plateNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        plateNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，车牌号，字符串，最大为36个字符，支持中文和数字
     * </pre>
     *
     * <code>string plateNo = 18;</code>
     */
    public Builder setPlateNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      plateNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车牌号，字符串，最大为36个字符，支持中文和数字
     * </pre>
     *
     * <code>string plateNo = 18;</code>
     */
    public Builder clearPlateNo() {
      
      plateNo_ = getDefaultInstance().getPlateNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车牌号，字符串，最大为36个字符，支持中文和数字
     * </pre>
     *
     * <code>string plateNo = 18;</code>
     */
    public Builder setPlateNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      plateNo_ = value;
      onChanged();
      return this;
    }

    private int plateType_ = 0;
    /**
     * <pre>
     *可选，车牌类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PlateType plateType = 19;</code>
     */
    public int getPlateTypeValue() {
      return plateType_;
    }
    /**
     * <pre>
     *可选，车牌类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PlateType plateType = 19;</code>
     */
    public Builder setPlateTypeValue(int value) {
      plateType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车牌类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PlateType plateType = 19;</code>
     */
    public road.data.proto.PlateType getPlateType() {
      @SuppressWarnings("deprecation")
      road.data.proto.PlateType result = road.data.proto.PlateType.valueOf(plateType_);
      return result == null ? road.data.proto.PlateType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，车牌类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PlateType plateType = 19;</code>
     */
    public Builder setPlateType(road.data.proto.PlateType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      plateType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车牌类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PlateType plateType = 19;</code>
     */
    public Builder clearPlateType() {
      
      plateType_ = 0;
      onChanged();
      return this;
    }

    private int plateColor_ = 0;
    /**
     * <pre>
     *可选，车牌颜色
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.PlateColor plateColor = 20;</code>
     */
    public int getPlateColorValue() {
      return plateColor_;
    }
    /**
     * <pre>
     *可选，车牌颜色
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.PlateColor plateColor = 20;</code>
     */
    public Builder setPlateColorValue(int value) {
      plateColor_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车牌颜色
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.PlateColor plateColor = 20;</code>
     */
    public road.data.proto.ParticipantData.PlateColor getPlateColor() {
      @SuppressWarnings("deprecation")
      road.data.proto.ParticipantData.PlateColor result = road.data.proto.ParticipantData.PlateColor.valueOf(plateColor_);
      return result == null ? road.data.proto.ParticipantData.PlateColor.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，车牌颜色
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.PlateColor plateColor = 20;</code>
     */
    public Builder setPlateColor(road.data.proto.ParticipantData.PlateColor value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      plateColor_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车牌颜色
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.PlateColor plateColor = 20;</code>
     */
    public Builder clearPlateColor() {
      
      plateColor_ = 0;
      onChanged();
      return this;
    }

    private int vehicleColor_ = 0;
    /**
     * <pre>
     *可选，车辆颜色
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.VehicleColor vehicleColor = 21;</code>
     */
    public int getVehicleColorValue() {
      return vehicleColor_;
    }
    /**
     * <pre>
     *可选，车辆颜色
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.VehicleColor vehicleColor = 21;</code>
     */
    public Builder setVehicleColorValue(int value) {
      vehicleColor_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车辆颜色
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.VehicleColor vehicleColor = 21;</code>
     */
    public road.data.proto.ParticipantData.VehicleColor getVehicleColor() {
      @SuppressWarnings("deprecation")
      road.data.proto.ParticipantData.VehicleColor result = road.data.proto.ParticipantData.VehicleColor.valueOf(vehicleColor_);
      return result == null ? road.data.proto.ParticipantData.VehicleColor.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，车辆颜色
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.VehicleColor vehicleColor = 21;</code>
     */
    public Builder setVehicleColor(road.data.proto.ParticipantData.VehicleColor value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      vehicleColor_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车辆颜色
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.VehicleColor vehicleColor = 21;</code>
     */
    public Builder clearVehicleColor() {
      
      vehicleColor_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.ParticipantSizeConfidence ptcSizeConfid_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ParticipantSizeConfidence, road.data.proto.ParticipantSizeConfidence.Builder, road.data.proto.ParticipantSizeConfidenceOrBuilder> ptcSizeConfidBuilder_;
    /**
     * <pre>
     * 可选，目标尺寸置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
     */
    public boolean hasPtcSizeConfid() {
      return ptcSizeConfidBuilder_ != null || ptcSizeConfid_ != null;
    }
    /**
     * <pre>
     * 可选，目标尺寸置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
     */
    public road.data.proto.ParticipantSizeConfidence getPtcSizeConfid() {
      if (ptcSizeConfidBuilder_ == null) {
        return ptcSizeConfid_ == null ? road.data.proto.ParticipantSizeConfidence.getDefaultInstance() : ptcSizeConfid_;
      } else {
        return ptcSizeConfidBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，目标尺寸置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
     */
    public Builder setPtcSizeConfid(road.data.proto.ParticipantSizeConfidence value) {
      if (ptcSizeConfidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ptcSizeConfid_ = value;
        onChanged();
      } else {
        ptcSizeConfidBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，目标尺寸置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
     */
    public Builder setPtcSizeConfid(
        road.data.proto.ParticipantSizeConfidence.Builder builderForValue) {
      if (ptcSizeConfidBuilder_ == null) {
        ptcSizeConfid_ = builderForValue.build();
        onChanged();
      } else {
        ptcSizeConfidBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，目标尺寸置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
     */
    public Builder mergePtcSizeConfid(road.data.proto.ParticipantSizeConfidence value) {
      if (ptcSizeConfidBuilder_ == null) {
        if (ptcSizeConfid_ != null) {
          ptcSizeConfid_ =
            road.data.proto.ParticipantSizeConfidence.newBuilder(ptcSizeConfid_).mergeFrom(value).buildPartial();
        } else {
          ptcSizeConfid_ = value;
        }
        onChanged();
      } else {
        ptcSizeConfidBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，目标尺寸置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
     */
    public Builder clearPtcSizeConfid() {
      if (ptcSizeConfidBuilder_ == null) {
        ptcSizeConfid_ = null;
        onChanged();
      } else {
        ptcSizeConfid_ = null;
        ptcSizeConfidBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，目标尺寸置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
     */
    public road.data.proto.ParticipantSizeConfidence.Builder getPtcSizeConfidBuilder() {
      
      onChanged();
      return getPtcSizeConfidFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，目标尺寸置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
     */
    public road.data.proto.ParticipantSizeConfidenceOrBuilder getPtcSizeConfidOrBuilder() {
      if (ptcSizeConfidBuilder_ != null) {
        return ptcSizeConfidBuilder_.getMessageOrBuilder();
      } else {
        return ptcSizeConfid_ == null ?
            road.data.proto.ParticipantSizeConfidence.getDefaultInstance() : ptcSizeConfid_;
      }
    }
    /**
     * <pre>
     * 可选，目标尺寸置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ParticipantSizeConfidence, road.data.proto.ParticipantSizeConfidence.Builder, road.data.proto.ParticipantSizeConfidenceOrBuilder> 
        getPtcSizeConfidFieldBuilder() {
      if (ptcSizeConfidBuilder_ == null) {
        ptcSizeConfidBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ParticipantSizeConfidence, road.data.proto.ParticipantSizeConfidence.Builder, road.data.proto.ParticipantSizeConfidenceOrBuilder>(
                getPtcSizeConfid(),
                getParentForChildren(),
                isClean());
        ptcSizeConfid_ = null;
      }
      return ptcSizeConfidBuilder_;
    }

    private int ptcTypeExt_ = 0;
    /**
     * <pre>
     * 可选，目标类型扩展
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.ParticipantTypeExt ptcTypeExt = 23;</code>
     */
    public int getPtcTypeExtValue() {
      return ptcTypeExt_;
    }
    /**
     * <pre>
     * 可选，目标类型扩展
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.ParticipantTypeExt ptcTypeExt = 23;</code>
     */
    public Builder setPtcTypeExtValue(int value) {
      ptcTypeExt_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，目标类型扩展
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.ParticipantTypeExt ptcTypeExt = 23;</code>
     */
    public road.data.proto.ParticipantData.ParticipantTypeExt getPtcTypeExt() {
      @SuppressWarnings("deprecation")
      road.data.proto.ParticipantData.ParticipantTypeExt result = road.data.proto.ParticipantData.ParticipantTypeExt.valueOf(ptcTypeExt_);
      return result == null ? road.data.proto.ParticipantData.ParticipantTypeExt.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，目标类型扩展
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.ParticipantTypeExt ptcTypeExt = 23;</code>
     */
    public Builder setPtcTypeExt(road.data.proto.ParticipantData.ParticipantTypeExt value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      ptcTypeExt_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，目标类型扩展
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantData.ParticipantTypeExt ptcTypeExt = 23;</code>
     */
    public Builder clearPtcTypeExt() {
      
      ptcTypeExt_ = 0;
      onChanged();
      return this;
    }

    private int ptcTypeExtConfid_ ;
    /**
     * <pre>
     * 可选，定义目标类型扩展的置信度;分辨率为0.005。
     * </pre>
     *
     * <code>uint32 ptcTypeExtConfid = 24;</code>
     */
    public int getPtcTypeExtConfid() {
      return ptcTypeExtConfid_;
    }
    /**
     * <pre>
     * 可选，定义目标类型扩展的置信度;分辨率为0.005。
     * </pre>
     *
     * <code>uint32 ptcTypeExtConfid = 24;</code>
     */
    public Builder setPtcTypeExtConfid(int value) {
      
      ptcTypeExtConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，定义目标类型扩展的置信度;分辨率为0.005。
     * </pre>
     *
     * <code>uint32 ptcTypeExtConfid = 24;</code>
     */
    public Builder clearPtcTypeExtConfid() {
      
      ptcTypeExtConfid_ = 0;
      onChanged();
      return this;
    }

    private int statusDuration_ ;
    /**
     * <pre>
     * 可选，以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据。
     * </pre>
     *
     * <code>uint32 statusDuration = 25;</code>
     */
    public int getStatusDuration() {
      return statusDuration_;
    }
    /**
     * <pre>
     * 可选，以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据。
     * </pre>
     *
     * <code>uint32 statusDuration = 25;</code>
     */
    public Builder setStatusDuration(int value) {
      
      statusDuration_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据。
     * </pre>
     *
     * <code>uint32 statusDuration = 25;</code>
     */
    public Builder clearStatusDuration() {
      
      statusDuration_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.PathHistoryPoint> pathHistory_ =
      java.util.Collections.emptyList();
    private void ensurePathHistoryIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        pathHistory_ = new java.util.ArrayList<road.data.proto.PathHistoryPoint>(pathHistory_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.PathHistoryPoint, road.data.proto.PathHistoryPoint.Builder, road.data.proto.PathHistoryPointOrBuilder> pathHistoryBuilder_;

    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public java.util.List<road.data.proto.PathHistoryPoint> getPathHistoryList() {
      if (pathHistoryBuilder_ == null) {
        return java.util.Collections.unmodifiableList(pathHistory_);
      } else {
        return pathHistoryBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public int getPathHistoryCount() {
      if (pathHistoryBuilder_ == null) {
        return pathHistory_.size();
      } else {
        return pathHistoryBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public road.data.proto.PathHistoryPoint getPathHistory(int index) {
      if (pathHistoryBuilder_ == null) {
        return pathHistory_.get(index);
      } else {
        return pathHistoryBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public Builder setPathHistory(
        int index, road.data.proto.PathHistoryPoint value) {
      if (pathHistoryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePathHistoryIsMutable();
        pathHistory_.set(index, value);
        onChanged();
      } else {
        pathHistoryBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public Builder setPathHistory(
        int index, road.data.proto.PathHistoryPoint.Builder builderForValue) {
      if (pathHistoryBuilder_ == null) {
        ensurePathHistoryIsMutable();
        pathHistory_.set(index, builderForValue.build());
        onChanged();
      } else {
        pathHistoryBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public Builder addPathHistory(road.data.proto.PathHistoryPoint value) {
      if (pathHistoryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePathHistoryIsMutable();
        pathHistory_.add(value);
        onChanged();
      } else {
        pathHistoryBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public Builder addPathHistory(
        int index, road.data.proto.PathHistoryPoint value) {
      if (pathHistoryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePathHistoryIsMutable();
        pathHistory_.add(index, value);
        onChanged();
      } else {
        pathHistoryBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public Builder addPathHistory(
        road.data.proto.PathHistoryPoint.Builder builderForValue) {
      if (pathHistoryBuilder_ == null) {
        ensurePathHistoryIsMutable();
        pathHistory_.add(builderForValue.build());
        onChanged();
      } else {
        pathHistoryBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public Builder addPathHistory(
        int index, road.data.proto.PathHistoryPoint.Builder builderForValue) {
      if (pathHistoryBuilder_ == null) {
        ensurePathHistoryIsMutable();
        pathHistory_.add(index, builderForValue.build());
        onChanged();
      } else {
        pathHistoryBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public Builder addAllPathHistory(
        java.lang.Iterable<? extends road.data.proto.PathHistoryPoint> values) {
      if (pathHistoryBuilder_ == null) {
        ensurePathHistoryIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, pathHistory_);
        onChanged();
      } else {
        pathHistoryBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public Builder clearPathHistory() {
      if (pathHistoryBuilder_ == null) {
        pathHistory_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        pathHistoryBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public Builder removePathHistory(int index) {
      if (pathHistoryBuilder_ == null) {
        ensurePathHistoryIsMutable();
        pathHistory_.remove(index);
        onChanged();
      } else {
        pathHistoryBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public road.data.proto.PathHistoryPoint.Builder getPathHistoryBuilder(
        int index) {
      return getPathHistoryFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public road.data.proto.PathHistoryPointOrBuilder getPathHistoryOrBuilder(
        int index) {
      if (pathHistoryBuilder_ == null) {
        return pathHistory_.get(index);  } else {
        return pathHistoryBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public java.util.List<? extends road.data.proto.PathHistoryPointOrBuilder> 
         getPathHistoryOrBuilderList() {
      if (pathHistoryBuilder_ != null) {
        return pathHistoryBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(pathHistory_);
      }
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public road.data.proto.PathHistoryPoint.Builder addPathHistoryBuilder() {
      return getPathHistoryFieldBuilder().addBuilder(
          road.data.proto.PathHistoryPoint.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public road.data.proto.PathHistoryPoint.Builder addPathHistoryBuilder(
        int index) {
      return getPathHistoryFieldBuilder().addBuilder(
          index, road.data.proto.PathHistoryPoint.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，目标历史轨迹
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
     */
    public java.util.List<road.data.proto.PathHistoryPoint.Builder> 
         getPathHistoryBuilderList() {
      return getPathHistoryFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.PathHistoryPoint, road.data.proto.PathHistoryPoint.Builder, road.data.proto.PathHistoryPointOrBuilder> 
        getPathHistoryFieldBuilder() {
      if (pathHistoryBuilder_ == null) {
        pathHistoryBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.PathHistoryPoint, road.data.proto.PathHistoryPoint.Builder, road.data.proto.PathHistoryPointOrBuilder>(
                pathHistory_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        pathHistory_ = null;
      }
      return pathHistoryBuilder_;
    }

    private int tracking_ ;
    /**
     * <pre>
     * 可选，目标追踪时间，单位s
     * </pre>
     *
     * <code>uint32 tracking = 27;</code>
     */
    public int getTracking() {
      return tracking_;
    }
    /**
     * <pre>
     * 可选，目标追踪时间，单位s
     * </pre>
     *
     * <code>uint32 tracking = 27;</code>
     */
    public Builder setTracking(int value) {
      
      tracking_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，目标追踪时间，单位s
     * </pre>
     *
     * <code>uint32 tracking = 27;</code>
     */
    public Builder clearTracking() {
      
      tracking_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.Polygon polygon_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> polygonBuilder_;
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
     */
    public boolean hasPolygon() {
      return polygonBuilder_ != null || polygon_ != null;
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
     */
    public road.data.proto.Polygon getPolygon() {
      if (polygonBuilder_ == null) {
        return polygon_ == null ? road.data.proto.Polygon.getDefaultInstance() : polygon_;
      } else {
        return polygonBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
     */
    public Builder setPolygon(road.data.proto.Polygon value) {
      if (polygonBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        polygon_ = value;
        onChanged();
      } else {
        polygonBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
     */
    public Builder setPolygon(
        road.data.proto.Polygon.Builder builderForValue) {
      if (polygonBuilder_ == null) {
        polygon_ = builderForValue.build();
        onChanged();
      } else {
        polygonBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
     */
    public Builder mergePolygon(road.data.proto.Polygon value) {
      if (polygonBuilder_ == null) {
        if (polygon_ != null) {
          polygon_ =
            road.data.proto.Polygon.newBuilder(polygon_).mergeFrom(value).buildPartial();
        } else {
          polygon_ = value;
        }
        onChanged();
      } else {
        polygonBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
     */
    public Builder clearPolygon() {
      if (polygonBuilder_ == null) {
        polygon_ = null;
        onChanged();
      } else {
        polygon_ = null;
        polygonBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
     */
    public road.data.proto.Polygon.Builder getPolygonBuilder() {
      
      onChanged();
      return getPolygonFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
     */
    public road.data.proto.PolygonOrBuilder getPolygonOrBuilder() {
      if (polygonBuilder_ != null) {
        return polygonBuilder_.getMessageOrBuilder();
      } else {
        return polygon_ == null ?
            road.data.proto.Polygon.getDefaultInstance() : polygon_;
      }
    }
    /**
     * <pre>
     * 可选，障碍物影响区域点集合
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> 
        getPolygonFieldBuilder() {
      if (polygonBuilder_ == null) {
        polygonBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder>(
                getPolygon(),
                getParentForChildren(),
                isClean());
        polygon_ = null;
      }
      return polygonBuilder_;
    }

    private long id_ ;
    /**
     * <pre>
     *可选，数据唯一标识id
     * </pre>
     *
     * <code>uint64 id = 29;</code>
     */
    public long getId() {
      return id_;
    }
    /**
     * <pre>
     *可选，数据唯一标识id
     * </pre>
     *
     * <code>uint64 id = 29;</code>
     */
    public Builder setId(long value) {
      
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，数据唯一标识id
     * </pre>
     *
     * <code>uint64 id = 29;</code>
     */
    public Builder clearId() {
      
      id_ = 0L;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ParticipantData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ParticipantData)
  private static final road.data.proto.ParticipantData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ParticipantData();
  }

  public static road.data.proto.ParticipantData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ParticipantData>
      PARSER = new com.google.protobuf.AbstractParser<ParticipantData>() {
    @java.lang.Override
    public ParticipantData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ParticipantData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ParticipantData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ParticipantData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ParticipantData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

