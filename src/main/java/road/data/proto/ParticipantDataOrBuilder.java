// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ParticipantDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ParticipantData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 目标对象ID,相同ID表示同一个目标物。
   * </pre>
   *
   * <code>uint64 ptcId = 1;</code>
   */
  long getPtcId();

  /**
   * <pre>
   *路侧单元检测到的交通参与者类型。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 2;</code>
   */
  int getPtcTypeValue();
  /**
   * <pre>
   *路侧单元检测到的交通参与者类型。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 2;</code>
   */
  road.data.proto.ParticipantType getPtcType();

  /**
   * <pre>
   * 0、未知数据源类型; 1、RSU 自身信息; 2、来源于参与者自身的v2x广播消息;3、来源于视频传感器; 4、来源于微波雷达传感器; 5、来源于地磁线圈传感器; 6、来源于激光雷达传感器; 7、2 类或以上感知数据的融合结果; 8~255 保留
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
   */
  int getDataSourceValue();
  /**
   * <pre>
   * 0、未知数据源类型; 1、RSU 自身信息; 2、来源于参与者自身的v2x广播消息;3、来源于视频传感器; 4、来源于微波雷达传感器; 5、来源于地磁线圈传感器; 6、来源于激光雷达传感器; 7、2 类或以上感知数据的融合结果; 8~255 保留
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
   */
  road.data.proto.DataSource getDataSource();

  /**
   * <pre>
   *  数据融合的来源设备id，json数组
   * </pre>
   *
   * <code>string deviceIdList = 4;</code>
   */
  java.lang.String getDeviceIdList();
  /**
   * <pre>
   *  数据融合的来源设备id，json数组
   * </pre>
   *
   * <code>string deviceIdList = 4;</code>
   */
  com.google.protobuf.ByteString
      getDeviceIdListBytes();

  /**
   * <pre>
   *  时间戳
   * </pre>
   *
   * <code>uint64 timestamp = 5;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   *可选，事件置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 6;</code>
   */
  int getTimeConfidenceValue();
  /**
   * <pre>
   *可选，事件置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 6;</code>
   */
  road.data.proto.TimeConfidence getTimeConfidence();

  /**
   * <pre>
   * 定义经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
   */
  boolean hasPtcPos();
  /**
   * <pre>
   * 定义经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
   */
  road.data.proto.Position3D getPtcPos();
  /**
   * <pre>
   * 定义经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D ptcPos = 7;</code>
   */
  road.data.proto.Position3DOrBuilder getPtcPosOrBuilder();

  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
   */
  boolean hasMapLocation();
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
   */
  road.data.proto.MapLocation getMapLocation();
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
   */
  road.data.proto.MapLocationOrBuilder getMapLocationOrBuilder();

  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
   */
  boolean hasPosConfid();
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
   */
  road.data.proto.PositionConfidenceSet getPosConfid();
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 9;</code>
   */
  road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder();

  /**
   * <pre>
   * 单位为0.02 m/s
   * </pre>
   *
   * <code>uint32 speed = 10;</code>
   */
  int getSpeed();

  /**
   * <pre>
   *车辆航向角。为车头方向与正北方向的顺时针夹角。分辨率0.0125度，范围0到359.9875度
   * </pre>
   *
   * <code>uint32 heading = 11;</code>
   */
  int getHeading();

  /**
   * <pre>
   *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  boolean hasMotionConfid();
  /**
   * <pre>
   *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  road.data.proto.MotionConfidenceSet getMotionConfid();
  /**
   * <pre>
   *可选，运动状态精度，ptc中包括speedConfidence和headingConfid
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MotionConfidenceSet motionConfid = 12;</code>
   */
  road.data.proto.MotionConfidenceSetOrBuilder getMotionConfidOrBuilder();

  /**
   * <pre>
   * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
   */
  boolean hasAccelSet();
  /**
   * <pre>
   * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
   */
  road.data.proto.AccelerationSet4Way getAccelSet();
  /**
   * <pre>
   * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way accelSet = 13;</code>
   */
  road.data.proto.AccelerationSet4WayOrBuilder getAccelSetOrBuilder();

  /**
   * <pre>
   *可选，目标四轴加速度置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
   */
  boolean hasAccelerationConfid();
  /**
   * <pre>
   *可选，目标四轴加速度置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
   */
  road.data.proto.AccelerationConfidence getAccelerationConfid();
  /**
   * <pre>
   *可选，目标四轴加速度置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 14;</code>
   */
  road.data.proto.AccelerationConfidenceOrBuilder getAccelerationConfidOrBuilder();

  /**
   * <pre>
   *可选，交通参与者尺寸信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
   */
  boolean hasPtcSize();
  /**
   * <pre>
   *可选，交通参与者尺寸信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
   */
  road.data.proto.ParticipantSize getPtcSize();
  /**
   * <pre>
   *可选，交通参与者尺寸信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSize ptcSize = 15;</code>
   */
  road.data.proto.ParticipantSizeOrBuilder getPtcSizeOrBuilder();

  /**
   * <pre>
   *可选，车辆品牌
   * </pre>
   *
   * <code>string vehicleBand = 16;</code>
   */
  java.lang.String getVehicleBand();
  /**
   * <pre>
   *可选，车辆品牌
   * </pre>
   *
   * <code>string vehicleBand = 16;</code>
   */
  com.google.protobuf.ByteString
      getVehicleBandBytes();

  /**
   * <pre>
   *可选，车型类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 17;</code>
   */
  int getVehicleTypeValue();
  /**
   * <pre>
   *可选，车型类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 17;</code>
   */
  road.data.proto.VehicleType getVehicleType();

  /**
   * <pre>
   *可选，车牌号，字符串，最大为36个字符，支持中文和数字
   * </pre>
   *
   * <code>string plateNo = 18;</code>
   */
  java.lang.String getPlateNo();
  /**
   * <pre>
   *可选，车牌号，字符串，最大为36个字符，支持中文和数字
   * </pre>
   *
   * <code>string plateNo = 18;</code>
   */
  com.google.protobuf.ByteString
      getPlateNoBytes();

  /**
   * <pre>
   *可选，车牌类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PlateType plateType = 19;</code>
   */
  int getPlateTypeValue();
  /**
   * <pre>
   *可选，车牌类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PlateType plateType = 19;</code>
   */
  road.data.proto.PlateType getPlateType();

  /**
   * <pre>
   *可选，车牌颜色
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantData.PlateColor plateColor = 20;</code>
   */
  int getPlateColorValue();
  /**
   * <pre>
   *可选，车牌颜色
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantData.PlateColor plateColor = 20;</code>
   */
  road.data.proto.ParticipantData.PlateColor getPlateColor();

  /**
   * <pre>
   *可选，车辆颜色
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantData.VehicleColor vehicleColor = 21;</code>
   */
  int getVehicleColorValue();
  /**
   * <pre>
   *可选，车辆颜色
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantData.VehicleColor vehicleColor = 21;</code>
   */
  road.data.proto.ParticipantData.VehicleColor getVehicleColor();

  /**
   * <pre>
   * 可选，目标尺寸置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
   */
  boolean hasPtcSizeConfid();
  /**
   * <pre>
   * 可选，目标尺寸置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
   */
  road.data.proto.ParticipantSizeConfidence getPtcSizeConfid();
  /**
   * <pre>
   * 可选，目标尺寸置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence ptcSizeConfid = 22;</code>
   */
  road.data.proto.ParticipantSizeConfidenceOrBuilder getPtcSizeConfidOrBuilder();

  /**
   * <pre>
   * 可选，目标类型扩展
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantData.ParticipantTypeExt ptcTypeExt = 23;</code>
   */
  int getPtcTypeExtValue();
  /**
   * <pre>
   * 可选，目标类型扩展
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantData.ParticipantTypeExt ptcTypeExt = 23;</code>
   */
  road.data.proto.ParticipantData.ParticipantTypeExt getPtcTypeExt();

  /**
   * <pre>
   * 可选，定义目标类型扩展的置信度;分辨率为0.005。
   * </pre>
   *
   * <code>uint32 ptcTypeExtConfid = 24;</code>
   */
  int getPtcTypeExtConfid();

  /**
   * <pre>
   * 可选，以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据。
   * </pre>
   *
   * <code>uint32 statusDuration = 25;</code>
   */
  int getStatusDuration();

  /**
   * <pre>
   * 可选，目标历史轨迹
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
   */
  java.util.List<road.data.proto.PathHistoryPoint> 
      getPathHistoryList();
  /**
   * <pre>
   * 可选，目标历史轨迹
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
   */
  road.data.proto.PathHistoryPoint getPathHistory(int index);
  /**
   * <pre>
   * 可选，目标历史轨迹
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
   */
  int getPathHistoryCount();
  /**
   * <pre>
   * 可选，目标历史轨迹
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
   */
  java.util.List<? extends road.data.proto.PathHistoryPointOrBuilder> 
      getPathHistoryOrBuilderList();
  /**
   * <pre>
   * 可选，目标历史轨迹
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathHistoryPoint pathHistory = 26;</code>
   */
  road.data.proto.PathHistoryPointOrBuilder getPathHistoryOrBuilder(
      int index);

  /**
   * <pre>
   * 可选，目标追踪时间，单位s
   * </pre>
   *
   * <code>uint32 tracking = 27;</code>
   */
  int getTracking();

  /**
   * <pre>
   * 可选，障碍物影响区域点集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
   */
  boolean hasPolygon();
  /**
   * <pre>
   * 可选，障碍物影响区域点集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
   */
  road.data.proto.Polygon getPolygon();
  /**
   * <pre>
   * 可选，障碍物影响区域点集合
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon polygon = 28;</code>
   */
  road.data.proto.PolygonOrBuilder getPolygonOrBuilder();

  /**
   * <pre>
   *可选，数据唯一标识id
   * </pre>
   *
   * <code>uint64 id = 29;</code>
   */
  long getId();
}
