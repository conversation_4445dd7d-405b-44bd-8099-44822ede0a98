// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 * 物体尺寸精度 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ParticipantSizeConfidence}
 */
public  final class ParticipantSizeConfidence extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ParticipantSizeConfidence)
    ParticipantSizeConfidenceOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ParticipantSizeConfidence.newBuilder() to construct.
  private ParticipantSizeConfidence(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ParticipantSizeConfidence() {
    widthConfid_ = 0;
    lengthConfid_ = 0;
    heightConfid_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ParticipantSizeConfidence();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ParticipantSizeConfidence(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {
            int rawValue = input.readEnum();

            widthConfid_ = rawValue;
            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            lengthConfid_ = rawValue;
            break;
          }
          case 24: {
            int rawValue = input.readEnum();

            heightConfid_ = rawValue;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ParticipantSizeConfidence_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ParticipantSizeConfidence_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ParticipantSizeConfidence.class, road.data.proto.ParticipantSizeConfidence.Builder.class);
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence}
   */
  public enum SizeValueConfidence
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>SIZE_CONFID_UNAVAILABLE = 0;</code>
     */
    SIZE_CONFID_UNAVAILABLE(0),
    /**
     * <pre>
     * (100 M)
     * </pre>
     *
     * <code>SIZE_CONFID_100_00 = 1;</code>
     */
    SIZE_CONFID_100_00(1),
    /**
     * <pre>
     * (50 M)
     * </pre>
     *
     * <code>SIZE_CONFID_050_00 = 2;</code>
     */
    SIZE_CONFID_050_00(2),
    /**
     * <pre>
     * (20 M)
     * </pre>
     *
     * <code>SIZE_CONFID_020_00 = 3;</code>
     */
    SIZE_CONFID_020_00(3),
    /**
     * <pre>
     * (10 M)
     * </pre>
     *
     * <code>SIZE_CONFID_010_00 = 4;</code>
     */
    SIZE_CONFID_010_00(4),
    /**
     * <pre>
     * (5 M)
     * </pre>
     *
     * <code>SIZE_CONFID_005_00 = 5;</code>
     */
    SIZE_CONFID_005_00(5),
    /**
     * <pre>
     * (2 M)
     * </pre>
     *
     * <code>SIZE_CONFID_002_00 = 6;</code>
     */
    SIZE_CONFID_002_00(6),
    /**
     * <pre>
     * (1 M)
     * </pre>
     *
     * <code>SIZE_CONFID_001_00 = 7;</code>
     */
    SIZE_CONFID_001_00(7),
    /**
     * <pre>
     * (50 CM)
     * </pre>
     *
     * <code>SIZE_CONFID_000_50 = 8;</code>
     */
    SIZE_CONFID_000_50(8),
    /**
     * <pre>
     * (20 CM)
     * </pre>
     *
     * <code>SIZE_CONFID_000_20 = 9;</code>
     */
    SIZE_CONFID_000_20(9),
    /**
     * <pre>
     * (10 CM)
     * </pre>
     *
     * <code>SIZE_CONFID_000_10 = 10;</code>
     */
    SIZE_CONFID_000_10(10),
    /**
     * <pre>
     * (5 CM)
     * </pre>
     *
     * <code>SIZE_CONFID_000_05 = 11;</code>
     */
    SIZE_CONFID_000_05(11),
    /**
     * <pre>
     * (2 CM)
     * </pre>
     *
     * <code>SIZE_CONFID_000_02 = 12;</code>
     */
    SIZE_CONFID_000_02(12),
    /**
     * <pre>
     *(1 CM)
     * </pre>
     *
     * <code>SIZE_CONFID_000_01 = 13;</code>
     */
    SIZE_CONFID_000_01(13),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>SIZE_CONFID_UNAVAILABLE = 0;</code>
     */
    public static final int SIZE_CONFID_UNAVAILABLE_VALUE = 0;
    /**
     * <pre>
     * (100 M)
     * </pre>
     *
     * <code>SIZE_CONFID_100_00 = 1;</code>
     */
    public static final int SIZE_CONFID_100_00_VALUE = 1;
    /**
     * <pre>
     * (50 M)
     * </pre>
     *
     * <code>SIZE_CONFID_050_00 = 2;</code>
     */
    public static final int SIZE_CONFID_050_00_VALUE = 2;
    /**
     * <pre>
     * (20 M)
     * </pre>
     *
     * <code>SIZE_CONFID_020_00 = 3;</code>
     */
    public static final int SIZE_CONFID_020_00_VALUE = 3;
    /**
     * <pre>
     * (10 M)
     * </pre>
     *
     * <code>SIZE_CONFID_010_00 = 4;</code>
     */
    public static final int SIZE_CONFID_010_00_VALUE = 4;
    /**
     * <pre>
     * (5 M)
     * </pre>
     *
     * <code>SIZE_CONFID_005_00 = 5;</code>
     */
    public static final int SIZE_CONFID_005_00_VALUE = 5;
    /**
     * <pre>
     * (2 M)
     * </pre>
     *
     * <code>SIZE_CONFID_002_00 = 6;</code>
     */
    public static final int SIZE_CONFID_002_00_VALUE = 6;
    /**
     * <pre>
     * (1 M)
     * </pre>
     *
     * <code>SIZE_CONFID_001_00 = 7;</code>
     */
    public static final int SIZE_CONFID_001_00_VALUE = 7;
    /**
     * <pre>
     * (50 CM)
     * </pre>
     *
     * <code>SIZE_CONFID_000_50 = 8;</code>
     */
    public static final int SIZE_CONFID_000_50_VALUE = 8;
    /**
     * <pre>
     * (20 CM)
     * </pre>
     *
     * <code>SIZE_CONFID_000_20 = 9;</code>
     */
    public static final int SIZE_CONFID_000_20_VALUE = 9;
    /**
     * <pre>
     * (10 CM)
     * </pre>
     *
     * <code>SIZE_CONFID_000_10 = 10;</code>
     */
    public static final int SIZE_CONFID_000_10_VALUE = 10;
    /**
     * <pre>
     * (5 CM)
     * </pre>
     *
     * <code>SIZE_CONFID_000_05 = 11;</code>
     */
    public static final int SIZE_CONFID_000_05_VALUE = 11;
    /**
     * <pre>
     * (2 CM)
     * </pre>
     *
     * <code>SIZE_CONFID_000_02 = 12;</code>
     */
    public static final int SIZE_CONFID_000_02_VALUE = 12;
    /**
     * <pre>
     *(1 CM)
     * </pre>
     *
     * <code>SIZE_CONFID_000_01 = 13;</code>
     */
    public static final int SIZE_CONFID_000_01_VALUE = 13;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static SizeValueConfidence valueOf(int value) {
      return forNumber(value);
    }

    public static SizeValueConfidence forNumber(int value) {
      switch (value) {
        case 0: return SIZE_CONFID_UNAVAILABLE;
        case 1: return SIZE_CONFID_100_00;
        case 2: return SIZE_CONFID_050_00;
        case 3: return SIZE_CONFID_020_00;
        case 4: return SIZE_CONFID_010_00;
        case 5: return SIZE_CONFID_005_00;
        case 6: return SIZE_CONFID_002_00;
        case 7: return SIZE_CONFID_001_00;
        case 8: return SIZE_CONFID_000_50;
        case 9: return SIZE_CONFID_000_20;
        case 10: return SIZE_CONFID_000_10;
        case 11: return SIZE_CONFID_000_05;
        case 12: return SIZE_CONFID_000_02;
        case 13: return SIZE_CONFID_000_01;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SizeValueConfidence>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        SizeValueConfidence> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SizeValueConfidence>() {
            public SizeValueConfidence findValueByNumber(int number) {
              return SizeValueConfidence.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.ParticipantSizeConfidence.getDescriptor().getEnumTypes().get(0);
    }

    private static final SizeValueConfidence[] VALUES = values();

    public static SizeValueConfidence valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private SizeValueConfidence(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence)
  }

  public static final int WIDTHCONFID_FIELD_NUMBER = 1;
  private int widthConfid_;
  /**
   * <pre>
   * 物体宽度置信度。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence widthConfid = 1;</code>
   */
  public int getWidthConfidValue() {
    return widthConfid_;
  }
  /**
   * <pre>
   * 物体宽度置信度。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence widthConfid = 1;</code>
   */
  public road.data.proto.ParticipantSizeConfidence.SizeValueConfidence getWidthConfid() {
    @SuppressWarnings("deprecation")
    road.data.proto.ParticipantSizeConfidence.SizeValueConfidence result = road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.valueOf(widthConfid_);
    return result == null ? road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.UNRECOGNIZED : result;
  }

  public static final int LENGTHCONFID_FIELD_NUMBER = 2;
  private int lengthConfid_;
  /**
   * <pre>
   * 物体长度置信度。取值同上。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence lengthConfid = 2;</code>
   */
  public int getLengthConfidValue() {
    return lengthConfid_;
  }
  /**
   * <pre>
   * 物体长度置信度。取值同上。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence lengthConfid = 2;</code>
   */
  public road.data.proto.ParticipantSizeConfidence.SizeValueConfidence getLengthConfid() {
    @SuppressWarnings("deprecation")
    road.data.proto.ParticipantSizeConfidence.SizeValueConfidence result = road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.valueOf(lengthConfid_);
    return result == null ? road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.UNRECOGNIZED : result;
  }

  public static final int HEIGHTCONFID_FIELD_NUMBER = 3;
  private int heightConfid_;
  /**
   * <pre>
   * 可选，物体高度置信度。取值同上。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence heightConfid = 3;</code>
   */
  public int getHeightConfidValue() {
    return heightConfid_;
  }
  /**
   * <pre>
   * 可选，物体高度置信度。取值同上。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence heightConfid = 3;</code>
   */
  public road.data.proto.ParticipantSizeConfidence.SizeValueConfidence getHeightConfid() {
    @SuppressWarnings("deprecation")
    road.data.proto.ParticipantSizeConfidence.SizeValueConfidence result = road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.valueOf(heightConfid_);
    return result == null ? road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.UNRECOGNIZED : result;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (widthConfid_ != road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.SIZE_CONFID_UNAVAILABLE.getNumber()) {
      output.writeEnum(1, widthConfid_);
    }
    if (lengthConfid_ != road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.SIZE_CONFID_UNAVAILABLE.getNumber()) {
      output.writeEnum(2, lengthConfid_);
    }
    if (heightConfid_ != road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.SIZE_CONFID_UNAVAILABLE.getNumber()) {
      output.writeEnum(3, heightConfid_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (widthConfid_ != road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.SIZE_CONFID_UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(1, widthConfid_);
    }
    if (lengthConfid_ != road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.SIZE_CONFID_UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, lengthConfid_);
    }
    if (heightConfid_ != road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.SIZE_CONFID_UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, heightConfid_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ParticipantSizeConfidence)) {
      return super.equals(obj);
    }
    road.data.proto.ParticipantSizeConfidence other = (road.data.proto.ParticipantSizeConfidence) obj;

    if (widthConfid_ != other.widthConfid_) return false;
    if (lengthConfid_ != other.lengthConfid_) return false;
    if (heightConfid_ != other.heightConfid_) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + WIDTHCONFID_FIELD_NUMBER;
    hash = (53 * hash) + widthConfid_;
    hash = (37 * hash) + LENGTHCONFID_FIELD_NUMBER;
    hash = (53 * hash) + lengthConfid_;
    hash = (37 * hash) + HEIGHTCONFID_FIELD_NUMBER;
    hash = (53 * hash) + heightConfid_;
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ParticipantSizeConfidence parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ParticipantSizeConfidence parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ParticipantSizeConfidence parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ParticipantSizeConfidence parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ParticipantSizeConfidence parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ParticipantSizeConfidence parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ParticipantSizeConfidence parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ParticipantSizeConfidence parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ParticipantSizeConfidence parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ParticipantSizeConfidence parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ParticipantSizeConfidence parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ParticipantSizeConfidence parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ParticipantSizeConfidence prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 物体尺寸精度 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ParticipantSizeConfidence}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ParticipantSizeConfidence)
      road.data.proto.ParticipantSizeConfidenceOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ParticipantSizeConfidence_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ParticipantSizeConfidence_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ParticipantSizeConfidence.class, road.data.proto.ParticipantSizeConfidence.Builder.class);
    }

    // Construct using road.data.proto.ParticipantSizeConfidence.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      widthConfid_ = 0;

      lengthConfid_ = 0;

      heightConfid_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ParticipantSizeConfidence_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ParticipantSizeConfidence getDefaultInstanceForType() {
      return road.data.proto.ParticipantSizeConfidence.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ParticipantSizeConfidence build() {
      road.data.proto.ParticipantSizeConfidence result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ParticipantSizeConfidence buildPartial() {
      road.data.proto.ParticipantSizeConfidence result = new road.data.proto.ParticipantSizeConfidence(this);
      result.widthConfid_ = widthConfid_;
      result.lengthConfid_ = lengthConfid_;
      result.heightConfid_ = heightConfid_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ParticipantSizeConfidence) {
        return mergeFrom((road.data.proto.ParticipantSizeConfidence)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ParticipantSizeConfidence other) {
      if (other == road.data.proto.ParticipantSizeConfidence.getDefaultInstance()) return this;
      if (other.widthConfid_ != 0) {
        setWidthConfidValue(other.getWidthConfidValue());
      }
      if (other.lengthConfid_ != 0) {
        setLengthConfidValue(other.getLengthConfidValue());
      }
      if (other.heightConfid_ != 0) {
        setHeightConfidValue(other.getHeightConfidValue());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ParticipantSizeConfidence parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ParticipantSizeConfidence) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int widthConfid_ = 0;
    /**
     * <pre>
     * 物体宽度置信度。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence widthConfid = 1;</code>
     */
    public int getWidthConfidValue() {
      return widthConfid_;
    }
    /**
     * <pre>
     * 物体宽度置信度。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence widthConfid = 1;</code>
     */
    public Builder setWidthConfidValue(int value) {
      widthConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 物体宽度置信度。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence widthConfid = 1;</code>
     */
    public road.data.proto.ParticipantSizeConfidence.SizeValueConfidence getWidthConfid() {
      @SuppressWarnings("deprecation")
      road.data.proto.ParticipantSizeConfidence.SizeValueConfidence result = road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.valueOf(widthConfid_);
      return result == null ? road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 物体宽度置信度。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence widthConfid = 1;</code>
     */
    public Builder setWidthConfid(road.data.proto.ParticipantSizeConfidence.SizeValueConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      widthConfid_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 物体宽度置信度。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence widthConfid = 1;</code>
     */
    public Builder clearWidthConfid() {
      
      widthConfid_ = 0;
      onChanged();
      return this;
    }

    private int lengthConfid_ = 0;
    /**
     * <pre>
     * 物体长度置信度。取值同上。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence lengthConfid = 2;</code>
     */
    public int getLengthConfidValue() {
      return lengthConfid_;
    }
    /**
     * <pre>
     * 物体长度置信度。取值同上。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence lengthConfid = 2;</code>
     */
    public Builder setLengthConfidValue(int value) {
      lengthConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 物体长度置信度。取值同上。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence lengthConfid = 2;</code>
     */
    public road.data.proto.ParticipantSizeConfidence.SizeValueConfidence getLengthConfid() {
      @SuppressWarnings("deprecation")
      road.data.proto.ParticipantSizeConfidence.SizeValueConfidence result = road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.valueOf(lengthConfid_);
      return result == null ? road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 物体长度置信度。取值同上。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence lengthConfid = 2;</code>
     */
    public Builder setLengthConfid(road.data.proto.ParticipantSizeConfidence.SizeValueConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      lengthConfid_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 物体长度置信度。取值同上。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence lengthConfid = 2;</code>
     */
    public Builder clearLengthConfid() {
      
      lengthConfid_ = 0;
      onChanged();
      return this;
    }

    private int heightConfid_ = 0;
    /**
     * <pre>
     * 可选，物体高度置信度。取值同上。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence heightConfid = 3;</code>
     */
    public int getHeightConfidValue() {
      return heightConfid_;
    }
    /**
     * <pre>
     * 可选，物体高度置信度。取值同上。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence heightConfid = 3;</code>
     */
    public Builder setHeightConfidValue(int value) {
      heightConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，物体高度置信度。取值同上。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence heightConfid = 3;</code>
     */
    public road.data.proto.ParticipantSizeConfidence.SizeValueConfidence getHeightConfid() {
      @SuppressWarnings("deprecation")
      road.data.proto.ParticipantSizeConfidence.SizeValueConfidence result = road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.valueOf(heightConfid_);
      return result == null ? road.data.proto.ParticipantSizeConfidence.SizeValueConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，物体高度置信度。取值同上。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence heightConfid = 3;</code>
     */
    public Builder setHeightConfid(road.data.proto.ParticipantSizeConfidence.SizeValueConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      heightConfid_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，物体高度置信度。取值同上。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence heightConfid = 3;</code>
     */
    public Builder clearHeightConfid() {
      
      heightConfid_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ParticipantSizeConfidence)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ParticipantSizeConfidence)
  private static final road.data.proto.ParticipantSizeConfidence DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ParticipantSizeConfidence();
  }

  public static road.data.proto.ParticipantSizeConfidence getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ParticipantSizeConfidence>
      PARSER = new com.google.protobuf.AbstractParser<ParticipantSizeConfidence>() {
    @java.lang.Override
    public ParticipantSizeConfidence parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ParticipantSizeConfidence(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ParticipantSizeConfidence> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ParticipantSizeConfidence> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ParticipantSizeConfidence getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

