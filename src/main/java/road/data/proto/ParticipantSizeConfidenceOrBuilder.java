// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ParticipantSizeConfidenceOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ParticipantSizeConfidence)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 物体宽度置信度。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence widthConfid = 1;</code>
   */
  int getWidthConfidValue();
  /**
   * <pre>
   * 物体宽度置信度。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence widthConfid = 1;</code>
   */
  road.data.proto.ParticipantSizeConfidence.SizeValueConfidence getWidthConfid();

  /**
   * <pre>
   * 物体长度置信度。取值同上。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence lengthConfid = 2;</code>
   */
  int getLengthConfidValue();
  /**
   * <pre>
   * 物体长度置信度。取值同上。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence lengthConfid = 2;</code>
   */
  road.data.proto.ParticipantSizeConfidence.SizeValueConfidence getLengthConfid();

  /**
   * <pre>
   * 可选，物体高度置信度。取值同上。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence heightConfid = 3;</code>
   */
  int getHeightConfidValue();
  /**
   * <pre>
   * 可选，物体高度置信度。取值同上。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantSizeConfidence.SizeValueConfidence heightConfid = 3;</code>
   */
  road.data.proto.ParticipantSizeConfidence.SizeValueConfidence getHeightConfid();
}
