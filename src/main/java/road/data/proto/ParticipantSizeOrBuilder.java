// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ParticipantSizeOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ParticipantSize)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 宽度。分辨率为1cm。数值0表示无效数据。
   * </pre>
   *
   * <code>uint32 width = 1;</code>
   */
  int getWidth();

  /**
   * <pre>
   * 长度。分辨率为1cm。数值0表示无效数据。
   * </pre>
   *
   * <code>uint32 length = 2;</code>
   */
  int getLength();

  /**
   * <pre>
   * 可选，高度。分辨率为5cm。数值0表示无效数据。
   * </pre>
   *
   * <code>uint32 height = 3;</code>
   */
  int getHeight();
}
