// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *物体类型 
 * </pre>
 *
 * Protobuf enum {@code cn.seisys.v2x.pb.ParticipantType}
 */
public enum ParticipantType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *未知
   * </pre>
   *
   * <code>OBJECTTYPE_UNKNOWN = 0;</code>
   */
  OBJECTTYPE_UNKNOWN(0),
  /**
   * <pre>
   *机动车
   * </pre>
   *
   * <code>OBJECTTYPE_MOTOR = 1;</code>
   */
  OBJECTTYPE_MOTOR(1),
  /**
   * <pre>
   *非机动车
   * </pre>
   *
   * <code>OBJECTTYPE_NON_MOTOR = 2;</code>
   */
  OBJECTTYPE_NON_MOTOR(2),
  /**
   * <pre>
   *行人
   * </pre>
   *
   * <code>OBJECTTYPE_PEDESTRIAN = 3;</code>
   */
  OBJECTTYPE_PEDESTRIAN(3),
  /**
   * <pre>
   *自身
   * </pre>
   *
   * <code>OBJECTTYPE_RSU = 4;</code>
   */
  OBJECTTYPE_RSU(4),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *未知
   * </pre>
   *
   * <code>OBJECTTYPE_UNKNOWN = 0;</code>
   */
  public static final int OBJECTTYPE_UNKNOWN_VALUE = 0;
  /**
   * <pre>
   *机动车
   * </pre>
   *
   * <code>OBJECTTYPE_MOTOR = 1;</code>
   */
  public static final int OBJECTTYPE_MOTOR_VALUE = 1;
  /**
   * <pre>
   *非机动车
   * </pre>
   *
   * <code>OBJECTTYPE_NON_MOTOR = 2;</code>
   */
  public static final int OBJECTTYPE_NON_MOTOR_VALUE = 2;
  /**
   * <pre>
   *行人
   * </pre>
   *
   * <code>OBJECTTYPE_PEDESTRIAN = 3;</code>
   */
  public static final int OBJECTTYPE_PEDESTRIAN_VALUE = 3;
  /**
   * <pre>
   *自身
   * </pre>
   *
   * <code>OBJECTTYPE_RSU = 4;</code>
   */
  public static final int OBJECTTYPE_RSU_VALUE = 4;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static ParticipantType valueOf(int value) {
    return forNumber(value);
  }

  public static ParticipantType forNumber(int value) {
    switch (value) {
      case 0: return OBJECTTYPE_UNKNOWN;
      case 1: return OBJECTTYPE_MOTOR;
      case 2: return OBJECTTYPE_NON_MOTOR;
      case 3: return OBJECTTYPE_PEDESTRIAN;
      case 4: return OBJECTTYPE_RSU;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<ParticipantType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      ParticipantType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<ParticipantType>() {
          public ParticipantType findValueByNumber(int number) {
            return ParticipantType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return road.data.proto.V2X.getDescriptor().getEnumTypes().get(3);
  }

  private static final ParticipantType[] VALUES = values();

  public static ParticipantType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private ParticipantType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.ParticipantType)
}

