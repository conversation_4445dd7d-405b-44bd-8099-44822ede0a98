// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *历史轨迹PathHistory   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.PathHistoryPoint}
 */
public  final class PathHistoryPoint extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.PathHistoryPoint)
    PathHistoryPointOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PathHistoryPoint.newBuilder() to construct.
  private PathHistoryPoint(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PathHistoryPoint() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PathHistoryPoint();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private PathHistoryPoint(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (pos_ != null) {
              subBuilder = pos_.toBuilder();
            }
            pos_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(pos_);
              pos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 16: {

            timeOffset_ = input.readUInt32();
            break;
          }
          case 24: {

            speed_ = input.readUInt32();
            break;
          }
          case 34: {
            road.data.proto.PositionConfidenceSet.Builder subBuilder = null;
            if (posConfid_ != null) {
              subBuilder = posConfid_.toBuilder();
            }
            posConfid_ = input.readMessage(road.data.proto.PositionConfidenceSet.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(posConfid_);
              posConfid_ = subBuilder.buildPartial();
            }

            break;
          }
          case 40: {

            heading_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathHistoryPoint_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathHistoryPoint_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.PathHistoryPoint.class, road.data.proto.PathHistoryPoint.Builder.class);
  }

  public static final int POS_FIELD_NUMBER = 1;
  private road.data.proto.Position3D pos_;
  /**
   * <pre>
   * 轨迹点位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  public boolean hasPos() {
    return pos_ != null;
  }
  /**
   * <pre>
   * 轨迹点位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  public road.data.proto.Position3D getPos() {
    return pos_ == null ? road.data.proto.Position3D.getDefaultInstance() : pos_;
  }
  /**
   * <pre>
   * 轨迹点位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  public road.data.proto.Position3DOrBuilder getPosOrBuilder() {
    return getPos();
  }

  public static final int TIMEOFFSET_FIELD_NUMBER = 2;
  private int timeOffset_;
  /**
   * <pre>
   * 以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据。
   * </pre>
   *
   * <code>uint32 timeOffset = 2;</code>
   */
  public int getTimeOffset() {
    return timeOffset_;
  }

  public static final int SPEED_FIELD_NUMBER = 3;
  private int speed_;
  /**
   * <pre>
   * 可选，定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
   * </pre>
   *
   * <code>uint32 speed = 3;</code>
   */
  public int getSpeed() {
    return speed_;
  }

  public static final int POSCONFID_FIELD_NUMBER = 4;
  private road.data.proto.PositionConfidenceSet posConfid_;
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
   */
  public boolean hasPosConfid() {
    return posConfid_ != null;
  }
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
   */
  public road.data.proto.PositionConfidenceSet getPosConfid() {
    return posConfid_ == null ? road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
  }
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
   */
  public road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder() {
    return getPosConfid();
  }

  public static final int HEADING_FIELD_NUMBER = 5;
  private int heading_;
  /**
   * <pre>
   * 可选，航向角，分辨率为0.0125°
   * </pre>
   *
   * <code>uint32 heading = 5;</code>
   */
  public int getHeading() {
    return heading_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (pos_ != null) {
      output.writeMessage(1, getPos());
    }
    if (timeOffset_ != 0) {
      output.writeUInt32(2, timeOffset_);
    }
    if (speed_ != 0) {
      output.writeUInt32(3, speed_);
    }
    if (posConfid_ != null) {
      output.writeMessage(4, getPosConfid());
    }
    if (heading_ != 0) {
      output.writeUInt32(5, heading_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (pos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getPos());
    }
    if (timeOffset_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, timeOffset_);
    }
    if (speed_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, speed_);
    }
    if (posConfid_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getPosConfid());
    }
    if (heading_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(5, heading_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.PathHistoryPoint)) {
      return super.equals(obj);
    }
    road.data.proto.PathHistoryPoint other = (road.data.proto.PathHistoryPoint) obj;

    if (hasPos() != other.hasPos()) return false;
    if (hasPos()) {
      if (!getPos()
          .equals(other.getPos())) return false;
    }
    if (getTimeOffset()
        != other.getTimeOffset()) return false;
    if (getSpeed()
        != other.getSpeed()) return false;
    if (hasPosConfid() != other.hasPosConfid()) return false;
    if (hasPosConfid()) {
      if (!getPosConfid()
          .equals(other.getPosConfid())) return false;
    }
    if (getHeading()
        != other.getHeading()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPos()) {
      hash = (37 * hash) + POS_FIELD_NUMBER;
      hash = (53 * hash) + getPos().hashCode();
    }
    hash = (37 * hash) + TIMEOFFSET_FIELD_NUMBER;
    hash = (53 * hash) + getTimeOffset();
    hash = (37 * hash) + SPEED_FIELD_NUMBER;
    hash = (53 * hash) + getSpeed();
    if (hasPosConfid()) {
      hash = (37 * hash) + POSCONFID_FIELD_NUMBER;
      hash = (53 * hash) + getPosConfid().hashCode();
    }
    hash = (37 * hash) + HEADING_FIELD_NUMBER;
    hash = (53 * hash) + getHeading();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.PathHistoryPoint parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PathHistoryPoint parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PathHistoryPoint parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PathHistoryPoint parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PathHistoryPoint parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PathHistoryPoint parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PathHistoryPoint parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PathHistoryPoint parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PathHistoryPoint parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.PathHistoryPoint parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PathHistoryPoint parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PathHistoryPoint parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.PathHistoryPoint prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *历史轨迹PathHistory   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.PathHistoryPoint}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.PathHistoryPoint)
      road.data.proto.PathHistoryPointOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathHistoryPoint_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathHistoryPoint_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.PathHistoryPoint.class, road.data.proto.PathHistoryPoint.Builder.class);
    }

    // Construct using road.data.proto.PathHistoryPoint.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (posBuilder_ == null) {
        pos_ = null;
      } else {
        pos_ = null;
        posBuilder_ = null;
      }
      timeOffset_ = 0;

      speed_ = 0;

      if (posConfidBuilder_ == null) {
        posConfid_ = null;
      } else {
        posConfid_ = null;
        posConfidBuilder_ = null;
      }
      heading_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathHistoryPoint_descriptor;
    }

    @java.lang.Override
    public road.data.proto.PathHistoryPoint getDefaultInstanceForType() {
      return road.data.proto.PathHistoryPoint.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.PathHistoryPoint build() {
      road.data.proto.PathHistoryPoint result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.PathHistoryPoint buildPartial() {
      road.data.proto.PathHistoryPoint result = new road.data.proto.PathHistoryPoint(this);
      if (posBuilder_ == null) {
        result.pos_ = pos_;
      } else {
        result.pos_ = posBuilder_.build();
      }
      result.timeOffset_ = timeOffset_;
      result.speed_ = speed_;
      if (posConfidBuilder_ == null) {
        result.posConfid_ = posConfid_;
      } else {
        result.posConfid_ = posConfidBuilder_.build();
      }
      result.heading_ = heading_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.PathHistoryPoint) {
        return mergeFrom((road.data.proto.PathHistoryPoint)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.PathHistoryPoint other) {
      if (other == road.data.proto.PathHistoryPoint.getDefaultInstance()) return this;
      if (other.hasPos()) {
        mergePos(other.getPos());
      }
      if (other.getTimeOffset() != 0) {
        setTimeOffset(other.getTimeOffset());
      }
      if (other.getSpeed() != 0) {
        setSpeed(other.getSpeed());
      }
      if (other.hasPosConfid()) {
        mergePosConfid(other.getPosConfid());
      }
      if (other.getHeading() != 0) {
        setHeading(other.getHeading());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.PathHistoryPoint parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.PathHistoryPoint) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.Position3D pos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> posBuilder_;
    /**
     * <pre>
     * 轨迹点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public boolean hasPos() {
      return posBuilder_ != null || pos_ != null;
    }
    /**
     * <pre>
     * 轨迹点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public road.data.proto.Position3D getPos() {
      if (posBuilder_ == null) {
        return pos_ == null ? road.data.proto.Position3D.getDefaultInstance() : pos_;
      } else {
        return posBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 轨迹点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public Builder setPos(road.data.proto.Position3D value) {
      if (posBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        pos_ = value;
        onChanged();
      } else {
        posBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 轨迹点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public Builder setPos(
        road.data.proto.Position3D.Builder builderForValue) {
      if (posBuilder_ == null) {
        pos_ = builderForValue.build();
        onChanged();
      } else {
        posBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 轨迹点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public Builder mergePos(road.data.proto.Position3D value) {
      if (posBuilder_ == null) {
        if (pos_ != null) {
          pos_ =
            road.data.proto.Position3D.newBuilder(pos_).mergeFrom(value).buildPartial();
        } else {
          pos_ = value;
        }
        onChanged();
      } else {
        posBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 轨迹点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public Builder clearPos() {
      if (posBuilder_ == null) {
        pos_ = null;
        onChanged();
      } else {
        pos_ = null;
        posBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 轨迹点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public road.data.proto.Position3D.Builder getPosBuilder() {
      
      onChanged();
      return getPosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 轨迹点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public road.data.proto.Position3DOrBuilder getPosOrBuilder() {
      if (posBuilder_ != null) {
        return posBuilder_.getMessageOrBuilder();
      } else {
        return pos_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : pos_;
      }
    }
    /**
     * <pre>
     * 轨迹点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getPosFieldBuilder() {
      if (posBuilder_ == null) {
        posBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getPos(),
                getParentForChildren(),
                isClean());
        pos_ = null;
      }
      return posBuilder_;
    }

    private int timeOffset_ ;
    /**
     * <pre>
     * 以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据。
     * </pre>
     *
     * <code>uint32 timeOffset = 2;</code>
     */
    public int getTimeOffset() {
      return timeOffset_;
    }
    /**
     * <pre>
     * 以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据。
     * </pre>
     *
     * <code>uint32 timeOffset = 2;</code>
     */
    public Builder setTimeOffset(int value) {
      
      timeOffset_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据。
     * </pre>
     *
     * <code>uint32 timeOffset = 2;</code>
     */
    public Builder clearTimeOffset() {
      
      timeOffset_ = 0;
      onChanged();
      return this;
    }

    private int speed_ ;
    /**
     * <pre>
     * 可选，定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 speed = 3;</code>
     */
    public int getSpeed() {
      return speed_;
    }
    /**
     * <pre>
     * 可选，定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 speed = 3;</code>
     */
    public Builder setSpeed(int value) {
      
      speed_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 speed = 3;</code>
     */
    public Builder clearSpeed() {
      
      speed_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.PositionConfidenceSet posConfid_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder> posConfidBuilder_;
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
     */
    public boolean hasPosConfid() {
      return posConfidBuilder_ != null || posConfid_ != null;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
     */
    public road.data.proto.PositionConfidenceSet getPosConfid() {
      if (posConfidBuilder_ == null) {
        return posConfid_ == null ? road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
      } else {
        return posConfidBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
     */
    public Builder setPosConfid(road.data.proto.PositionConfidenceSet value) {
      if (posConfidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        posConfid_ = value;
        onChanged();
      } else {
        posConfidBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
     */
    public Builder setPosConfid(
        road.data.proto.PositionConfidenceSet.Builder builderForValue) {
      if (posConfidBuilder_ == null) {
        posConfid_ = builderForValue.build();
        onChanged();
      } else {
        posConfidBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
     */
    public Builder mergePosConfid(road.data.proto.PositionConfidenceSet value) {
      if (posConfidBuilder_ == null) {
        if (posConfid_ != null) {
          posConfid_ =
            road.data.proto.PositionConfidenceSet.newBuilder(posConfid_).mergeFrom(value).buildPartial();
        } else {
          posConfid_ = value;
        }
        onChanged();
      } else {
        posConfidBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
     */
    public Builder clearPosConfid() {
      if (posConfidBuilder_ == null) {
        posConfid_ = null;
        onChanged();
      } else {
        posConfid_ = null;
        posConfidBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
     */
    public road.data.proto.PositionConfidenceSet.Builder getPosConfidBuilder() {
      
      onChanged();
      return getPosConfidFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
     */
    public road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder() {
      if (posConfidBuilder_ != null) {
        return posConfidBuilder_.getMessageOrBuilder();
      } else {
        return posConfid_ == null ?
            road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
      }
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder> 
        getPosConfidFieldBuilder() {
      if (posConfidBuilder_ == null) {
        posConfidBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder>(
                getPosConfid(),
                getParentForChildren(),
                isClean());
        posConfid_ = null;
      }
      return posConfidBuilder_;
    }

    private int heading_ ;
    /**
     * <pre>
     * 可选，航向角，分辨率为0.0125°
     * </pre>
     *
     * <code>uint32 heading = 5;</code>
     */
    public int getHeading() {
      return heading_;
    }
    /**
     * <pre>
     * 可选，航向角，分辨率为0.0125°
     * </pre>
     *
     * <code>uint32 heading = 5;</code>
     */
    public Builder setHeading(int value) {
      
      heading_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，航向角，分辨率为0.0125°
     * </pre>
     *
     * <code>uint32 heading = 5;</code>
     */
    public Builder clearHeading() {
      
      heading_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.PathHistoryPoint)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.PathHistoryPoint)
  private static final road.data.proto.PathHistoryPoint DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.PathHistoryPoint();
  }

  public static road.data.proto.PathHistoryPoint getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PathHistoryPoint>
      PARSER = new com.google.protobuf.AbstractParser<PathHistoryPoint>() {
    @java.lang.Override
    public PathHistoryPoint parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new PathHistoryPoint(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<PathHistoryPoint> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PathHistoryPoint> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.PathHistoryPoint getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

