// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface PathHistoryPointOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.PathHistoryPoint)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 轨迹点位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  boolean hasPos();
  /**
   * <pre>
   * 轨迹点位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  road.data.proto.Position3D getPos();
  /**
   * <pre>
   * 轨迹点位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  road.data.proto.Position3DOrBuilder getPosOrBuilder();

  /**
   * <pre>
   * 以10毫秒为单位，定义当前描述时刻（较早）相对于参考时间点（较晚）的偏差。用于车辆历史轨迹点的表达。值65535表示无效数据。
   * </pre>
   *
   * <code>uint32 timeOffset = 2;</code>
   */
  int getTimeOffset();

  /**
   * <pre>
   * 可选，定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
   * </pre>
   *
   * <code>uint32 speed = 3;</code>
   */
  int getSpeed();

  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
   */
  boolean hasPosConfid();
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
   */
  road.data.proto.PositionConfidenceSet getPosConfid();
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 4;</code>
   */
  road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder();

  /**
   * <pre>
   * 可选，航向角，分辨率为0.0125°
   * </pre>
   *
   * <code>uint32 heading = 5;</code>
   */
  int getHeading();
}
