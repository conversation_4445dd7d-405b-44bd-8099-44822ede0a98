// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 * // 规划路径PathPlanning    
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.PathPlanning}
 */
public  final class PathPlanning extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.PathPlanning)
    PathPlanningOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PathPlanning.newBuilder() to construct.
  private PathPlanning(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PathPlanning() {
    pathPlanning_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PathPlanning();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private PathPlanning(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              pathPlanning_ = new java.util.ArrayList<road.data.proto.PathPlanningPoint>();
              mutable_bitField0_ |= 0x00000001;
            }
            pathPlanning_.add(
                input.readMessage(road.data.proto.PathPlanningPoint.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        pathPlanning_ = java.util.Collections.unmodifiableList(pathPlanning_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathPlanning_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathPlanning_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.PathPlanning.class, road.data.proto.PathPlanning.Builder.class);
  }

  public static final int PATHPLANNING_FIELD_NUMBER = 1;
  private java.util.List<road.data.proto.PathPlanningPoint> pathPlanning_;
  /**
   * <pre>
   * 路径规划信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
   */
  public java.util.List<road.data.proto.PathPlanningPoint> getPathPlanningList() {
    return pathPlanning_;
  }
  /**
   * <pre>
   * 路径规划信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
   */
  public java.util.List<? extends road.data.proto.PathPlanningPointOrBuilder> 
      getPathPlanningOrBuilderList() {
    return pathPlanning_;
  }
  /**
   * <pre>
   * 路径规划信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
   */
  public int getPathPlanningCount() {
    return pathPlanning_.size();
  }
  /**
   * <pre>
   * 路径规划信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
   */
  public road.data.proto.PathPlanningPoint getPathPlanning(int index) {
    return pathPlanning_.get(index);
  }
  /**
   * <pre>
   * 路径规划信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
   */
  public road.data.proto.PathPlanningPointOrBuilder getPathPlanningOrBuilder(
      int index) {
    return pathPlanning_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < pathPlanning_.size(); i++) {
      output.writeMessage(1, pathPlanning_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < pathPlanning_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, pathPlanning_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.PathPlanning)) {
      return super.equals(obj);
    }
    road.data.proto.PathPlanning other = (road.data.proto.PathPlanning) obj;

    if (!getPathPlanningList()
        .equals(other.getPathPlanningList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getPathPlanningCount() > 0) {
      hash = (37 * hash) + PATHPLANNING_FIELD_NUMBER;
      hash = (53 * hash) + getPathPlanningList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.PathPlanning parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PathPlanning parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PathPlanning parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PathPlanning parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PathPlanning parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PathPlanning parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PathPlanning parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PathPlanning parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PathPlanning parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.PathPlanning parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PathPlanning parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PathPlanning parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.PathPlanning prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * // 规划路径PathPlanning    
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.PathPlanning}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.PathPlanning)
      road.data.proto.PathPlanningOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathPlanning_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathPlanning_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.PathPlanning.class, road.data.proto.PathPlanning.Builder.class);
    }

    // Construct using road.data.proto.PathPlanning.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getPathPlanningFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (pathPlanningBuilder_ == null) {
        pathPlanning_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        pathPlanningBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathPlanning_descriptor;
    }

    @java.lang.Override
    public road.data.proto.PathPlanning getDefaultInstanceForType() {
      return road.data.proto.PathPlanning.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.PathPlanning build() {
      road.data.proto.PathPlanning result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.PathPlanning buildPartial() {
      road.data.proto.PathPlanning result = new road.data.proto.PathPlanning(this);
      int from_bitField0_ = bitField0_;
      if (pathPlanningBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          pathPlanning_ = java.util.Collections.unmodifiableList(pathPlanning_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.pathPlanning_ = pathPlanning_;
      } else {
        result.pathPlanning_ = pathPlanningBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.PathPlanning) {
        return mergeFrom((road.data.proto.PathPlanning)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.PathPlanning other) {
      if (other == road.data.proto.PathPlanning.getDefaultInstance()) return this;
      if (pathPlanningBuilder_ == null) {
        if (!other.pathPlanning_.isEmpty()) {
          if (pathPlanning_.isEmpty()) {
            pathPlanning_ = other.pathPlanning_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePathPlanningIsMutable();
            pathPlanning_.addAll(other.pathPlanning_);
          }
          onChanged();
        }
      } else {
        if (!other.pathPlanning_.isEmpty()) {
          if (pathPlanningBuilder_.isEmpty()) {
            pathPlanningBuilder_.dispose();
            pathPlanningBuilder_ = null;
            pathPlanning_ = other.pathPlanning_;
            bitField0_ = (bitField0_ & ~0x00000001);
            pathPlanningBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getPathPlanningFieldBuilder() : null;
          } else {
            pathPlanningBuilder_.addAllMessages(other.pathPlanning_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.PathPlanning parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.PathPlanning) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.util.List<road.data.proto.PathPlanningPoint> pathPlanning_ =
      java.util.Collections.emptyList();
    private void ensurePathPlanningIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        pathPlanning_ = new java.util.ArrayList<road.data.proto.PathPlanningPoint>(pathPlanning_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.PathPlanningPoint, road.data.proto.PathPlanningPoint.Builder, road.data.proto.PathPlanningPointOrBuilder> pathPlanningBuilder_;

    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public java.util.List<road.data.proto.PathPlanningPoint> getPathPlanningList() {
      if (pathPlanningBuilder_ == null) {
        return java.util.Collections.unmodifiableList(pathPlanning_);
      } else {
        return pathPlanningBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public int getPathPlanningCount() {
      if (pathPlanningBuilder_ == null) {
        return pathPlanning_.size();
      } else {
        return pathPlanningBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public road.data.proto.PathPlanningPoint getPathPlanning(int index) {
      if (pathPlanningBuilder_ == null) {
        return pathPlanning_.get(index);
      } else {
        return pathPlanningBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public Builder setPathPlanning(
        int index, road.data.proto.PathPlanningPoint value) {
      if (pathPlanningBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePathPlanningIsMutable();
        pathPlanning_.set(index, value);
        onChanged();
      } else {
        pathPlanningBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public Builder setPathPlanning(
        int index, road.data.proto.PathPlanningPoint.Builder builderForValue) {
      if (pathPlanningBuilder_ == null) {
        ensurePathPlanningIsMutable();
        pathPlanning_.set(index, builderForValue.build());
        onChanged();
      } else {
        pathPlanningBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public Builder addPathPlanning(road.data.proto.PathPlanningPoint value) {
      if (pathPlanningBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePathPlanningIsMutable();
        pathPlanning_.add(value);
        onChanged();
      } else {
        pathPlanningBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public Builder addPathPlanning(
        int index, road.data.proto.PathPlanningPoint value) {
      if (pathPlanningBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePathPlanningIsMutable();
        pathPlanning_.add(index, value);
        onChanged();
      } else {
        pathPlanningBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public Builder addPathPlanning(
        road.data.proto.PathPlanningPoint.Builder builderForValue) {
      if (pathPlanningBuilder_ == null) {
        ensurePathPlanningIsMutable();
        pathPlanning_.add(builderForValue.build());
        onChanged();
      } else {
        pathPlanningBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public Builder addPathPlanning(
        int index, road.data.proto.PathPlanningPoint.Builder builderForValue) {
      if (pathPlanningBuilder_ == null) {
        ensurePathPlanningIsMutable();
        pathPlanning_.add(index, builderForValue.build());
        onChanged();
      } else {
        pathPlanningBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public Builder addAllPathPlanning(
        java.lang.Iterable<? extends road.data.proto.PathPlanningPoint> values) {
      if (pathPlanningBuilder_ == null) {
        ensurePathPlanningIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, pathPlanning_);
        onChanged();
      } else {
        pathPlanningBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public Builder clearPathPlanning() {
      if (pathPlanningBuilder_ == null) {
        pathPlanning_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        pathPlanningBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public Builder removePathPlanning(int index) {
      if (pathPlanningBuilder_ == null) {
        ensurePathPlanningIsMutable();
        pathPlanning_.remove(index);
        onChanged();
      } else {
        pathPlanningBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public road.data.proto.PathPlanningPoint.Builder getPathPlanningBuilder(
        int index) {
      return getPathPlanningFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public road.data.proto.PathPlanningPointOrBuilder getPathPlanningOrBuilder(
        int index) {
      if (pathPlanningBuilder_ == null) {
        return pathPlanning_.get(index);  } else {
        return pathPlanningBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public java.util.List<? extends road.data.proto.PathPlanningPointOrBuilder> 
         getPathPlanningOrBuilderList() {
      if (pathPlanningBuilder_ != null) {
        return pathPlanningBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(pathPlanning_);
      }
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public road.data.proto.PathPlanningPoint.Builder addPathPlanningBuilder() {
      return getPathPlanningFieldBuilder().addBuilder(
          road.data.proto.PathPlanningPoint.getDefaultInstance());
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public road.data.proto.PathPlanningPoint.Builder addPathPlanningBuilder(
        int index) {
      return getPathPlanningFieldBuilder().addBuilder(
          index, road.data.proto.PathPlanningPoint.getDefaultInstance());
    }
    /**
     * <pre>
     * 路径规划信息
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
     */
    public java.util.List<road.data.proto.PathPlanningPoint.Builder> 
         getPathPlanningBuilderList() {
      return getPathPlanningFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.PathPlanningPoint, road.data.proto.PathPlanningPoint.Builder, road.data.proto.PathPlanningPointOrBuilder> 
        getPathPlanningFieldBuilder() {
      if (pathPlanningBuilder_ == null) {
        pathPlanningBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.PathPlanningPoint, road.data.proto.PathPlanningPoint.Builder, road.data.proto.PathPlanningPointOrBuilder>(
                pathPlanning_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        pathPlanning_ = null;
      }
      return pathPlanningBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.PathPlanning)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.PathPlanning)
  private static final road.data.proto.PathPlanning DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.PathPlanning();
  }

  public static road.data.proto.PathPlanning getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PathPlanning>
      PARSER = new com.google.protobuf.AbstractParser<PathPlanning>() {
    @java.lang.Override
    public PathPlanning parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new PathPlanning(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<PathPlanning> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PathPlanning> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.PathPlanning getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

