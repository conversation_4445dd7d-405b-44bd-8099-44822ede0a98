// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface PathPlanningOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.PathPlanning)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 路径规划信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
   */
  java.util.List<road.data.proto.PathPlanningPoint> 
      getPathPlanningList();
  /**
   * <pre>
   * 路径规划信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
   */
  road.data.proto.PathPlanningPoint getPathPlanning(int index);
  /**
   * <pre>
   * 路径规划信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
   */
  int getPathPlanningCount();
  /**
   * <pre>
   * 路径规划信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
   */
  java.util.List<? extends road.data.proto.PathPlanningPointOrBuilder> 
      getPathPlanningOrBuilderList();
  /**
   * <pre>
   * 路径规划信息
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PathPlanningPoint pathPlanning = 1;</code>
   */
  road.data.proto.PathPlanningPointOrBuilder getPathPlanningOrBuilder(
      int index);
}
