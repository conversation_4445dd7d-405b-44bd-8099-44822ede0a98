// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *规划路径点信息    
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.PathPlanningPoint}
 */
public  final class PathPlanningPoint extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.PathPlanningPoint)
    PathPlanningPointOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PathPlanningPoint.newBuilder() to construct.
  private PathPlanningPoint(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PathPlanningPoint() {
    speedConfid_ = 0;
    headingConfid_ = 0;
    timeConfidence_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PathPlanningPoint();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private PathPlanningPoint(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (pos_ != null) {
              subBuilder = pos_.toBuilder();
            }
            pos_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(pos_);
              pos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            road.data.proto.PositionConfidenceSet.Builder subBuilder = null;
            if (posConfid_ != null) {
              subBuilder = posConfid_.toBuilder();
            }
            posConfid_ = input.readMessage(road.data.proto.PositionConfidenceSet.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(posConfid_);
              posConfid_ = subBuilder.buildPartial();
            }

            break;
          }
          case 24: {

            speed_ = input.readUInt32();
            break;
          }
          case 32: {

            heading_ = input.readUInt32();
            break;
          }
          case 40: {
            int rawValue = input.readEnum();

            speedConfid_ = rawValue;
            break;
          }
          case 48: {
            int rawValue = input.readEnum();

            headingConfid_ = rawValue;
            break;
          }
          case 58: {
            road.data.proto.AccelerationSet4Way.Builder subBuilder = null;
            if (acceleration_ != null) {
              subBuilder = acceleration_.toBuilder();
            }
            acceleration_ = input.readMessage(road.data.proto.AccelerationSet4Way.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(acceleration_);
              acceleration_ = subBuilder.buildPartial();
            }

            break;
          }
          case 66: {
            road.data.proto.AccelerationConfidence.Builder subBuilder = null;
            if (accelerationConfid_ != null) {
              subBuilder = accelerationConfid_.toBuilder();
            }
            accelerationConfid_ = input.readMessage(road.data.proto.AccelerationConfidence.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(accelerationConfid_);
              accelerationConfid_ = subBuilder.buildPartial();
            }

            break;
          }
          case 72: {

            estimatedTime_ = input.readUInt32();
            break;
          }
          case 80: {
            int rawValue = input.readEnum();

            timeConfidence_ = rawValue;
            break;
          }
          case 90: {
            road.data.proto.ReferenceLink.Builder subBuilder = null;
            if (posInMap_ != null) {
              subBuilder = posInMap_.toBuilder();
            }
            posInMap_ = input.readMessage(road.data.proto.ReferenceLink.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(posInMap_);
              posInMap_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathPlanningPoint_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathPlanningPoint_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.PathPlanningPoint.class, road.data.proto.PathPlanningPoint.Builder.class);
  }

  public static final int POS_FIELD_NUMBER = 1;
  private road.data.proto.Position3D pos_;
  /**
   * <pre>
   * 定义经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  public boolean hasPos() {
    return pos_ != null;
  }
  /**
   * <pre>
   * 定义经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  public road.data.proto.Position3D getPos() {
    return pos_ == null ? road.data.proto.Position3D.getDefaultInstance() : pos_;
  }
  /**
   * <pre>
   * 定义经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  public road.data.proto.Position3DOrBuilder getPosOrBuilder() {
    return getPos();
  }

  public static final int POSCONFID_FIELD_NUMBER = 2;
  private road.data.proto.PositionConfidenceSet posConfid_;
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
   */
  public boolean hasPosConfid() {
    return posConfid_ != null;
  }
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
   */
  public road.data.proto.PositionConfidenceSet getPosConfid() {
    return posConfid_ == null ? road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
  }
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
   */
  public road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder() {
    return getPosConfid();
  }

  public static final int SPEED_FIELD_NUMBER = 3;
  private int speed_;
  /**
   * <pre>
   * 可选，定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
   * </pre>
   *
   * <code>uint32 speed = 3;</code>
   */
  public int getSpeed() {
    return speed_;
  }

  public static final int HEADING_FIELD_NUMBER = 4;
  private int heading_;
  /**
   * <pre>
   * 可选，车辆航向角。为车头方向与正北方向的顺时针夹角。分辨率为0.0125°。
   * </pre>
   *
   * <code>uint32 heading = 4;</code>
   */
  public int getHeading() {
    return heading_;
  }

  public static final int SPEEDCONFID_FIELD_NUMBER = 5;
  private int speedConfid_;
  /**
   * <pre>
   *可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpeedConfidence speedConfid = 5;</code>
   */
  public int getSpeedConfidValue() {
    return speedConfid_;
  }
  /**
   * <pre>
   *可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpeedConfidence speedConfid = 5;</code>
   */
  public road.data.proto.SpeedConfidence getSpeedConfid() {
    @SuppressWarnings("deprecation")
    road.data.proto.SpeedConfidence result = road.data.proto.SpeedConfidence.valueOf(speedConfid_);
    return result == null ? road.data.proto.SpeedConfidence.UNRECOGNIZED : result;
  }

  public static final int HEADINGCONFID_FIELD_NUMBER = 6;
  private int headingConfid_;
  /**
   * <pre>
   * 可选，定义95%置信水平的航向精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.HeadingConfidence headingConfid = 6;</code>
   */
  public int getHeadingConfidValue() {
    return headingConfid_;
  }
  /**
   * <pre>
   * 可选，定义95%置信水平的航向精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.HeadingConfidence headingConfid = 6;</code>
   */
  public road.data.proto.HeadingConfidence getHeadingConfid() {
    @SuppressWarnings("deprecation")
    road.data.proto.HeadingConfidence result = road.data.proto.HeadingConfidence.valueOf(headingConfid_);
    return result == null ? road.data.proto.HeadingConfidence.UNRECOGNIZED : result;
  }

  public static final int ACCELERATION_FIELD_NUMBER = 7;
  private road.data.proto.AccelerationSet4Way acceleration_;
  /**
   * <pre>
   * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
   */
  public boolean hasAcceleration() {
    return acceleration_ != null;
  }
  /**
   * <pre>
   * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
   */
  public road.data.proto.AccelerationSet4Way getAcceleration() {
    return acceleration_ == null ? road.data.proto.AccelerationSet4Way.getDefaultInstance() : acceleration_;
  }
  /**
   * <pre>
   * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
   */
  public road.data.proto.AccelerationSet4WayOrBuilder getAccelerationOrBuilder() {
    return getAcceleration();
  }

  public static final int ACCELERATIONCONFID_FIELD_NUMBER = 8;
  private road.data.proto.AccelerationConfidence accelerationConfid_;
  /**
   * <pre>
   * 可选，目标四轴加速度置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
   */
  public boolean hasAccelerationConfid() {
    return accelerationConfid_ != null;
  }
  /**
   * <pre>
   * 可选，目标四轴加速度置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
   */
  public road.data.proto.AccelerationConfidence getAccelerationConfid() {
    return accelerationConfid_ == null ? road.data.proto.AccelerationConfidence.getDefaultInstance() : accelerationConfid_;
  }
  /**
   * <pre>
   * 可选，目标四轴加速度置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
   */
  public road.data.proto.AccelerationConfidenceOrBuilder getAccelerationConfidOrBuilder() {
    return getAccelerationConfid();
  }

  public static final int ESTIMATEDTIME_FIELD_NUMBER = 9;
  private int estimatedTime_;
  /**
   * <pre>
   * 可选，目标到达目标位置的时间，分辨率为10ms
   * </pre>
   *
   * <code>uint32 estimatedTime = 9;</code>
   */
  public int getEstimatedTime() {
    return estimatedTime_;
  }

  public static final int TIMECONFIDENCE_FIELD_NUMBER = 10;
  private int timeConfidence_;
  /**
   * <pre>
   * 可选，定义事件的置信度;分辨率为0.005。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 10;</code>
   */
  public int getTimeConfidenceValue() {
    return timeConfidence_;
  }
  /**
   * <pre>
   * 可选，定义事件的置信度;分辨率为0.005。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 10;</code>
   */
  public road.data.proto.TimeConfidence getTimeConfidence() {
    @SuppressWarnings("deprecation")
    road.data.proto.TimeConfidence result = road.data.proto.TimeConfidence.valueOf(timeConfidence_);
    return result == null ? road.data.proto.TimeConfidence.UNRECOGNIZED : result;
  }

  public static final int POSINMAP_FIELD_NUMBER = 11;
  private road.data.proto.ReferenceLink posInMap_;
  /**
   * <pre>
   *可选，与 MAP 相关的车道和链接位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
   */
  public boolean hasPosInMap() {
    return posInMap_ != null;
  }
  /**
   * <pre>
   *可选，与 MAP 相关的车道和链接位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
   */
  public road.data.proto.ReferenceLink getPosInMap() {
    return posInMap_ == null ? road.data.proto.ReferenceLink.getDefaultInstance() : posInMap_;
  }
  /**
   * <pre>
   *可选，与 MAP 相关的车道和链接位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
   */
  public road.data.proto.ReferenceLinkOrBuilder getPosInMapOrBuilder() {
    return getPosInMap();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (pos_ != null) {
      output.writeMessage(1, getPos());
    }
    if (posConfid_ != null) {
      output.writeMessage(2, getPosConfid());
    }
    if (speed_ != 0) {
      output.writeUInt32(3, speed_);
    }
    if (heading_ != 0) {
      output.writeUInt32(4, heading_);
    }
    if (speedConfid_ != road.data.proto.SpeedConfidence.SPEED_CONFID_UNAVAILABLE.getNumber()) {
      output.writeEnum(5, speedConfid_);
    }
    if (headingConfid_ != road.data.proto.HeadingConfidence.HEADING_CONFID_UNAVAILABLE.getNumber()) {
      output.writeEnum(6, headingConfid_);
    }
    if (acceleration_ != null) {
      output.writeMessage(7, getAcceleration());
    }
    if (accelerationConfid_ != null) {
      output.writeMessage(8, getAccelerationConfid());
    }
    if (estimatedTime_ != 0) {
      output.writeUInt32(9, estimatedTime_);
    }
    if (timeConfidence_ != road.data.proto.TimeConfidence.UNAVAILABLE.getNumber()) {
      output.writeEnum(10, timeConfidence_);
    }
    if (posInMap_ != null) {
      output.writeMessage(11, getPosInMap());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (pos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getPos());
    }
    if (posConfid_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getPosConfid());
    }
    if (speed_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, speed_);
    }
    if (heading_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, heading_);
    }
    if (speedConfid_ != road.data.proto.SpeedConfidence.SPEED_CONFID_UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(5, speedConfid_);
    }
    if (headingConfid_ != road.data.proto.HeadingConfidence.HEADING_CONFID_UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(6, headingConfid_);
    }
    if (acceleration_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getAcceleration());
    }
    if (accelerationConfid_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, getAccelerationConfid());
    }
    if (estimatedTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(9, estimatedTime_);
    }
    if (timeConfidence_ != road.data.proto.TimeConfidence.UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(10, timeConfidence_);
    }
    if (posInMap_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(11, getPosInMap());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.PathPlanningPoint)) {
      return super.equals(obj);
    }
    road.data.proto.PathPlanningPoint other = (road.data.proto.PathPlanningPoint) obj;

    if (hasPos() != other.hasPos()) return false;
    if (hasPos()) {
      if (!getPos()
          .equals(other.getPos())) return false;
    }
    if (hasPosConfid() != other.hasPosConfid()) return false;
    if (hasPosConfid()) {
      if (!getPosConfid()
          .equals(other.getPosConfid())) return false;
    }
    if (getSpeed()
        != other.getSpeed()) return false;
    if (getHeading()
        != other.getHeading()) return false;
    if (speedConfid_ != other.speedConfid_) return false;
    if (headingConfid_ != other.headingConfid_) return false;
    if (hasAcceleration() != other.hasAcceleration()) return false;
    if (hasAcceleration()) {
      if (!getAcceleration()
          .equals(other.getAcceleration())) return false;
    }
    if (hasAccelerationConfid() != other.hasAccelerationConfid()) return false;
    if (hasAccelerationConfid()) {
      if (!getAccelerationConfid()
          .equals(other.getAccelerationConfid())) return false;
    }
    if (getEstimatedTime()
        != other.getEstimatedTime()) return false;
    if (timeConfidence_ != other.timeConfidence_) return false;
    if (hasPosInMap() != other.hasPosInMap()) return false;
    if (hasPosInMap()) {
      if (!getPosInMap()
          .equals(other.getPosInMap())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPos()) {
      hash = (37 * hash) + POS_FIELD_NUMBER;
      hash = (53 * hash) + getPos().hashCode();
    }
    if (hasPosConfid()) {
      hash = (37 * hash) + POSCONFID_FIELD_NUMBER;
      hash = (53 * hash) + getPosConfid().hashCode();
    }
    hash = (37 * hash) + SPEED_FIELD_NUMBER;
    hash = (53 * hash) + getSpeed();
    hash = (37 * hash) + HEADING_FIELD_NUMBER;
    hash = (53 * hash) + getHeading();
    hash = (37 * hash) + SPEEDCONFID_FIELD_NUMBER;
    hash = (53 * hash) + speedConfid_;
    hash = (37 * hash) + HEADINGCONFID_FIELD_NUMBER;
    hash = (53 * hash) + headingConfid_;
    if (hasAcceleration()) {
      hash = (37 * hash) + ACCELERATION_FIELD_NUMBER;
      hash = (53 * hash) + getAcceleration().hashCode();
    }
    if (hasAccelerationConfid()) {
      hash = (37 * hash) + ACCELERATIONCONFID_FIELD_NUMBER;
      hash = (53 * hash) + getAccelerationConfid().hashCode();
    }
    hash = (37 * hash) + ESTIMATEDTIME_FIELD_NUMBER;
    hash = (53 * hash) + getEstimatedTime();
    hash = (37 * hash) + TIMECONFIDENCE_FIELD_NUMBER;
    hash = (53 * hash) + timeConfidence_;
    if (hasPosInMap()) {
      hash = (37 * hash) + POSINMAP_FIELD_NUMBER;
      hash = (53 * hash) + getPosInMap().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.PathPlanningPoint parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PathPlanningPoint parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PathPlanningPoint parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PathPlanningPoint parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PathPlanningPoint parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PathPlanningPoint parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PathPlanningPoint parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PathPlanningPoint parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PathPlanningPoint parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.PathPlanningPoint parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PathPlanningPoint parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PathPlanningPoint parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.PathPlanningPoint prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *规划路径点信息    
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.PathPlanningPoint}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.PathPlanningPoint)
      road.data.proto.PathPlanningPointOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathPlanningPoint_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathPlanningPoint_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.PathPlanningPoint.class, road.data.proto.PathPlanningPoint.Builder.class);
    }

    // Construct using road.data.proto.PathPlanningPoint.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (posBuilder_ == null) {
        pos_ = null;
      } else {
        pos_ = null;
        posBuilder_ = null;
      }
      if (posConfidBuilder_ == null) {
        posConfid_ = null;
      } else {
        posConfid_ = null;
        posConfidBuilder_ = null;
      }
      speed_ = 0;

      heading_ = 0;

      speedConfid_ = 0;

      headingConfid_ = 0;

      if (accelerationBuilder_ == null) {
        acceleration_ = null;
      } else {
        acceleration_ = null;
        accelerationBuilder_ = null;
      }
      if (accelerationConfidBuilder_ == null) {
        accelerationConfid_ = null;
      } else {
        accelerationConfid_ = null;
        accelerationConfidBuilder_ = null;
      }
      estimatedTime_ = 0;

      timeConfidence_ = 0;

      if (posInMapBuilder_ == null) {
        posInMap_ = null;
      } else {
        posInMap_ = null;
        posInMapBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PathPlanningPoint_descriptor;
    }

    @java.lang.Override
    public road.data.proto.PathPlanningPoint getDefaultInstanceForType() {
      return road.data.proto.PathPlanningPoint.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.PathPlanningPoint build() {
      road.data.proto.PathPlanningPoint result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.PathPlanningPoint buildPartial() {
      road.data.proto.PathPlanningPoint result = new road.data.proto.PathPlanningPoint(this);
      if (posBuilder_ == null) {
        result.pos_ = pos_;
      } else {
        result.pos_ = posBuilder_.build();
      }
      if (posConfidBuilder_ == null) {
        result.posConfid_ = posConfid_;
      } else {
        result.posConfid_ = posConfidBuilder_.build();
      }
      result.speed_ = speed_;
      result.heading_ = heading_;
      result.speedConfid_ = speedConfid_;
      result.headingConfid_ = headingConfid_;
      if (accelerationBuilder_ == null) {
        result.acceleration_ = acceleration_;
      } else {
        result.acceleration_ = accelerationBuilder_.build();
      }
      if (accelerationConfidBuilder_ == null) {
        result.accelerationConfid_ = accelerationConfid_;
      } else {
        result.accelerationConfid_ = accelerationConfidBuilder_.build();
      }
      result.estimatedTime_ = estimatedTime_;
      result.timeConfidence_ = timeConfidence_;
      if (posInMapBuilder_ == null) {
        result.posInMap_ = posInMap_;
      } else {
        result.posInMap_ = posInMapBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.PathPlanningPoint) {
        return mergeFrom((road.data.proto.PathPlanningPoint)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.PathPlanningPoint other) {
      if (other == road.data.proto.PathPlanningPoint.getDefaultInstance()) return this;
      if (other.hasPos()) {
        mergePos(other.getPos());
      }
      if (other.hasPosConfid()) {
        mergePosConfid(other.getPosConfid());
      }
      if (other.getSpeed() != 0) {
        setSpeed(other.getSpeed());
      }
      if (other.getHeading() != 0) {
        setHeading(other.getHeading());
      }
      if (other.speedConfid_ != 0) {
        setSpeedConfidValue(other.getSpeedConfidValue());
      }
      if (other.headingConfid_ != 0) {
        setHeadingConfidValue(other.getHeadingConfidValue());
      }
      if (other.hasAcceleration()) {
        mergeAcceleration(other.getAcceleration());
      }
      if (other.hasAccelerationConfid()) {
        mergeAccelerationConfid(other.getAccelerationConfid());
      }
      if (other.getEstimatedTime() != 0) {
        setEstimatedTime(other.getEstimatedTime());
      }
      if (other.timeConfidence_ != 0) {
        setTimeConfidenceValue(other.getTimeConfidenceValue());
      }
      if (other.hasPosInMap()) {
        mergePosInMap(other.getPosInMap());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.PathPlanningPoint parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.PathPlanningPoint) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.Position3D pos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> posBuilder_;
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public boolean hasPos() {
      return posBuilder_ != null || pos_ != null;
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public road.data.proto.Position3D getPos() {
      if (posBuilder_ == null) {
        return pos_ == null ? road.data.proto.Position3D.getDefaultInstance() : pos_;
      } else {
        return posBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public Builder setPos(road.data.proto.Position3D value) {
      if (posBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        pos_ = value;
        onChanged();
      } else {
        posBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public Builder setPos(
        road.data.proto.Position3D.Builder builderForValue) {
      if (posBuilder_ == null) {
        pos_ = builderForValue.build();
        onChanged();
      } else {
        posBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public Builder mergePos(road.data.proto.Position3D value) {
      if (posBuilder_ == null) {
        if (pos_ != null) {
          pos_ =
            road.data.proto.Position3D.newBuilder(pos_).mergeFrom(value).buildPartial();
        } else {
          pos_ = value;
        }
        onChanged();
      } else {
        posBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public Builder clearPos() {
      if (posBuilder_ == null) {
        pos_ = null;
        onChanged();
      } else {
        pos_ = null;
        posBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public road.data.proto.Position3D.Builder getPosBuilder() {
      
      onChanged();
      return getPosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    public road.data.proto.Position3DOrBuilder getPosOrBuilder() {
      if (posBuilder_ != null) {
        return posBuilder_.getMessageOrBuilder();
      } else {
        return pos_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : pos_;
      }
    }
    /**
     * <pre>
     * 定义经纬度和高，绝对位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getPosFieldBuilder() {
      if (posBuilder_ == null) {
        posBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getPos(),
                getParentForChildren(),
                isClean());
        pos_ = null;
      }
      return posBuilder_;
    }

    private road.data.proto.PositionConfidenceSet posConfid_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder> posConfidBuilder_;
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
     */
    public boolean hasPosConfid() {
      return posConfidBuilder_ != null || posConfid_ != null;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
     */
    public road.data.proto.PositionConfidenceSet getPosConfid() {
      if (posConfidBuilder_ == null) {
        return posConfid_ == null ? road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
      } else {
        return posConfidBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
     */
    public Builder setPosConfid(road.data.proto.PositionConfidenceSet value) {
      if (posConfidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        posConfid_ = value;
        onChanged();
      } else {
        posConfidBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
     */
    public Builder setPosConfid(
        road.data.proto.PositionConfidenceSet.Builder builderForValue) {
      if (posConfidBuilder_ == null) {
        posConfid_ = builderForValue.build();
        onChanged();
      } else {
        posConfidBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
     */
    public Builder mergePosConfid(road.data.proto.PositionConfidenceSet value) {
      if (posConfidBuilder_ == null) {
        if (posConfid_ != null) {
          posConfid_ =
            road.data.proto.PositionConfidenceSet.newBuilder(posConfid_).mergeFrom(value).buildPartial();
        } else {
          posConfid_ = value;
        }
        onChanged();
      } else {
        posConfidBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
     */
    public Builder clearPosConfid() {
      if (posConfidBuilder_ == null) {
        posConfid_ = null;
        onChanged();
      } else {
        posConfid_ = null;
        posConfidBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
     */
    public road.data.proto.PositionConfidenceSet.Builder getPosConfidBuilder() {
      
      onChanged();
      return getPosConfidFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
     */
    public road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder() {
      if (posConfidBuilder_ != null) {
        return posConfidBuilder_.getMessageOrBuilder();
      } else {
        return posConfid_ == null ?
            road.data.proto.PositionConfidenceSet.getDefaultInstance() : posConfid_;
      }
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder> 
        getPosConfidFieldBuilder() {
      if (posConfidBuilder_ == null) {
        posConfidBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.PositionConfidenceSet, road.data.proto.PositionConfidenceSet.Builder, road.data.proto.PositionConfidenceSetOrBuilder>(
                getPosConfid(),
                getParentForChildren(),
                isClean());
        posConfid_ = null;
      }
      return posConfidBuilder_;
    }

    private int speed_ ;
    /**
     * <pre>
     * 可选，定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 speed = 3;</code>
     */
    public int getSpeed() {
      return speed_;
    }
    /**
     * <pre>
     * 可选，定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 speed = 3;</code>
     */
    public Builder setSpeed(int value) {
      
      speed_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
     * </pre>
     *
     * <code>uint32 speed = 3;</code>
     */
    public Builder clearSpeed() {
      
      speed_ = 0;
      onChanged();
      return this;
    }

    private int heading_ ;
    /**
     * <pre>
     * 可选，车辆航向角。为车头方向与正北方向的顺时针夹角。分辨率为0.0125°。
     * </pre>
     *
     * <code>uint32 heading = 4;</code>
     */
    public int getHeading() {
      return heading_;
    }
    /**
     * <pre>
     * 可选，车辆航向角。为车头方向与正北方向的顺时针夹角。分辨率为0.0125°。
     * </pre>
     *
     * <code>uint32 heading = 4;</code>
     */
    public Builder setHeading(int value) {
      
      heading_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，车辆航向角。为车头方向与正北方向的顺时针夹角。分辨率为0.0125°。
     * </pre>
     *
     * <code>uint32 heading = 4;</code>
     */
    public Builder clearHeading() {
      
      heading_ = 0;
      onChanged();
      return this;
    }

    private int speedConfid_ = 0;
    /**
     * <pre>
     *可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence speedConfid = 5;</code>
     */
    public int getSpeedConfidValue() {
      return speedConfid_;
    }
    /**
     * <pre>
     *可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence speedConfid = 5;</code>
     */
    public Builder setSpeedConfidValue(int value) {
      speedConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence speedConfid = 5;</code>
     */
    public road.data.proto.SpeedConfidence getSpeedConfid() {
      @SuppressWarnings("deprecation")
      road.data.proto.SpeedConfidence result = road.data.proto.SpeedConfidence.valueOf(speedConfid_);
      return result == null ? road.data.proto.SpeedConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence speedConfid = 5;</code>
     */
    public Builder setSpeedConfid(road.data.proto.SpeedConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      speedConfid_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SpeedConfidence speedConfid = 5;</code>
     */
    public Builder clearSpeedConfid() {
      
      speedConfid_ = 0;
      onChanged();
      return this;
    }

    private int headingConfid_ = 0;
    /**
     * <pre>
     * 可选，定义95%置信水平的航向精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.HeadingConfidence headingConfid = 6;</code>
     */
    public int getHeadingConfidValue() {
      return headingConfid_;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的航向精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.HeadingConfidence headingConfid = 6;</code>
     */
    public Builder setHeadingConfidValue(int value) {
      headingConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的航向精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.HeadingConfidence headingConfid = 6;</code>
     */
    public road.data.proto.HeadingConfidence getHeadingConfid() {
      @SuppressWarnings("deprecation")
      road.data.proto.HeadingConfidence result = road.data.proto.HeadingConfidence.valueOf(headingConfid_);
      return result == null ? road.data.proto.HeadingConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的航向精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.HeadingConfidence headingConfid = 6;</code>
     */
    public Builder setHeadingConfid(road.data.proto.HeadingConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      headingConfid_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，定义95%置信水平的航向精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.HeadingConfidence headingConfid = 6;</code>
     */
    public Builder clearHeadingConfid() {
      
      headingConfid_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.AccelerationSet4Way acceleration_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AccelerationSet4Way, road.data.proto.AccelerationSet4Way.Builder, road.data.proto.AccelerationSet4WayOrBuilder> accelerationBuilder_;
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public boolean hasAcceleration() {
      return accelerationBuilder_ != null || acceleration_ != null;
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public road.data.proto.AccelerationSet4Way getAcceleration() {
      if (accelerationBuilder_ == null) {
        return acceleration_ == null ? road.data.proto.AccelerationSet4Way.getDefaultInstance() : acceleration_;
      } else {
        return accelerationBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public Builder setAcceleration(road.data.proto.AccelerationSet4Way value) {
      if (accelerationBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        acceleration_ = value;
        onChanged();
      } else {
        accelerationBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public Builder setAcceleration(
        road.data.proto.AccelerationSet4Way.Builder builderForValue) {
      if (accelerationBuilder_ == null) {
        acceleration_ = builderForValue.build();
        onChanged();
      } else {
        accelerationBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public Builder mergeAcceleration(road.data.proto.AccelerationSet4Way value) {
      if (accelerationBuilder_ == null) {
        if (acceleration_ != null) {
          acceleration_ =
            road.data.proto.AccelerationSet4Way.newBuilder(acceleration_).mergeFrom(value).buildPartial();
        } else {
          acceleration_ = value;
        }
        onChanged();
      } else {
        accelerationBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public Builder clearAcceleration() {
      if (accelerationBuilder_ == null) {
        acceleration_ = null;
        onChanged();
      } else {
        acceleration_ = null;
        accelerationBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public road.data.proto.AccelerationSet4Way.Builder getAccelerationBuilder() {
      
      onChanged();
      return getAccelerationFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    public road.data.proto.AccelerationSet4WayOrBuilder getAccelerationOrBuilder() {
      if (accelerationBuilder_ != null) {
        return accelerationBuilder_.getMessageOrBuilder();
      } else {
        return acceleration_ == null ?
            road.data.proto.AccelerationSet4Way.getDefaultInstance() : acceleration_;
      }
    }
    /**
     * <pre>
     * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AccelerationSet4Way, road.data.proto.AccelerationSet4Way.Builder, road.data.proto.AccelerationSet4WayOrBuilder> 
        getAccelerationFieldBuilder() {
      if (accelerationBuilder_ == null) {
        accelerationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.AccelerationSet4Way, road.data.proto.AccelerationSet4Way.Builder, road.data.proto.AccelerationSet4WayOrBuilder>(
                getAcceleration(),
                getParentForChildren(),
                isClean());
        acceleration_ = null;
      }
      return accelerationBuilder_;
    }

    private road.data.proto.AccelerationConfidence accelerationConfid_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AccelerationConfidence, road.data.proto.AccelerationConfidence.Builder, road.data.proto.AccelerationConfidenceOrBuilder> accelerationConfidBuilder_;
    /**
     * <pre>
     * 可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
     */
    public boolean hasAccelerationConfid() {
      return accelerationConfidBuilder_ != null || accelerationConfid_ != null;
    }
    /**
     * <pre>
     * 可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
     */
    public road.data.proto.AccelerationConfidence getAccelerationConfid() {
      if (accelerationConfidBuilder_ == null) {
        return accelerationConfid_ == null ? road.data.proto.AccelerationConfidence.getDefaultInstance() : accelerationConfid_;
      } else {
        return accelerationConfidBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
     */
    public Builder setAccelerationConfid(road.data.proto.AccelerationConfidence value) {
      if (accelerationConfidBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        accelerationConfid_ = value;
        onChanged();
      } else {
        accelerationConfidBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
     */
    public Builder setAccelerationConfid(
        road.data.proto.AccelerationConfidence.Builder builderForValue) {
      if (accelerationConfidBuilder_ == null) {
        accelerationConfid_ = builderForValue.build();
        onChanged();
      } else {
        accelerationConfidBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
     */
    public Builder mergeAccelerationConfid(road.data.proto.AccelerationConfidence value) {
      if (accelerationConfidBuilder_ == null) {
        if (accelerationConfid_ != null) {
          accelerationConfid_ =
            road.data.proto.AccelerationConfidence.newBuilder(accelerationConfid_).mergeFrom(value).buildPartial();
        } else {
          accelerationConfid_ = value;
        }
        onChanged();
      } else {
        accelerationConfidBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
     */
    public Builder clearAccelerationConfid() {
      if (accelerationConfidBuilder_ == null) {
        accelerationConfid_ = null;
        onChanged();
      } else {
        accelerationConfid_ = null;
        accelerationConfidBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
     */
    public road.data.proto.AccelerationConfidence.Builder getAccelerationConfidBuilder() {
      
      onChanged();
      return getAccelerationConfidFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
     */
    public road.data.proto.AccelerationConfidenceOrBuilder getAccelerationConfidOrBuilder() {
      if (accelerationConfidBuilder_ != null) {
        return accelerationConfidBuilder_.getMessageOrBuilder();
      } else {
        return accelerationConfid_ == null ?
            road.data.proto.AccelerationConfidence.getDefaultInstance() : accelerationConfid_;
      }
    }
    /**
     * <pre>
     * 可选，目标四轴加速度置信度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.AccelerationConfidence, road.data.proto.AccelerationConfidence.Builder, road.data.proto.AccelerationConfidenceOrBuilder> 
        getAccelerationConfidFieldBuilder() {
      if (accelerationConfidBuilder_ == null) {
        accelerationConfidBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.AccelerationConfidence, road.data.proto.AccelerationConfidence.Builder, road.data.proto.AccelerationConfidenceOrBuilder>(
                getAccelerationConfid(),
                getParentForChildren(),
                isClean());
        accelerationConfid_ = null;
      }
      return accelerationConfidBuilder_;
    }

    private int estimatedTime_ ;
    /**
     * <pre>
     * 可选，目标到达目标位置的时间，分辨率为10ms
     * </pre>
     *
     * <code>uint32 estimatedTime = 9;</code>
     */
    public int getEstimatedTime() {
      return estimatedTime_;
    }
    /**
     * <pre>
     * 可选，目标到达目标位置的时间，分辨率为10ms
     * </pre>
     *
     * <code>uint32 estimatedTime = 9;</code>
     */
    public Builder setEstimatedTime(int value) {
      
      estimatedTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，目标到达目标位置的时间，分辨率为10ms
     * </pre>
     *
     * <code>uint32 estimatedTime = 9;</code>
     */
    public Builder clearEstimatedTime() {
      
      estimatedTime_ = 0;
      onChanged();
      return this;
    }

    private int timeConfidence_ = 0;
    /**
     * <pre>
     * 可选，定义事件的置信度;分辨率为0.005。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 10;</code>
     */
    public int getTimeConfidenceValue() {
      return timeConfidence_;
    }
    /**
     * <pre>
     * 可选，定义事件的置信度;分辨率为0.005。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 10;</code>
     */
    public Builder setTimeConfidenceValue(int value) {
      timeConfidence_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，定义事件的置信度;分辨率为0.005。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 10;</code>
     */
    public road.data.proto.TimeConfidence getTimeConfidence() {
      @SuppressWarnings("deprecation")
      road.data.proto.TimeConfidence result = road.data.proto.TimeConfidence.valueOf(timeConfidence_);
      return result == null ? road.data.proto.TimeConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，定义事件的置信度;分辨率为0.005。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 10;</code>
     */
    public Builder setTimeConfidence(road.data.proto.TimeConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      timeConfidence_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，定义事件的置信度;分辨率为0.005。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 10;</code>
     */
    public Builder clearTimeConfidence() {
      
      timeConfidence_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.ReferenceLink posInMap_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder> posInMapBuilder_;
    /**
     * <pre>
     *可选，与 MAP 相关的车道和链接位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
     */
    public boolean hasPosInMap() {
      return posInMapBuilder_ != null || posInMap_ != null;
    }
    /**
     * <pre>
     *可选，与 MAP 相关的车道和链接位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
     */
    public road.data.proto.ReferenceLink getPosInMap() {
      if (posInMapBuilder_ == null) {
        return posInMap_ == null ? road.data.proto.ReferenceLink.getDefaultInstance() : posInMap_;
      } else {
        return posInMapBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，与 MAP 相关的车道和链接位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
     */
    public Builder setPosInMap(road.data.proto.ReferenceLink value) {
      if (posInMapBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        posInMap_ = value;
        onChanged();
      } else {
        posInMapBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，与 MAP 相关的车道和链接位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
     */
    public Builder setPosInMap(
        road.data.proto.ReferenceLink.Builder builderForValue) {
      if (posInMapBuilder_ == null) {
        posInMap_ = builderForValue.build();
        onChanged();
      } else {
        posInMapBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，与 MAP 相关的车道和链接位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
     */
    public Builder mergePosInMap(road.data.proto.ReferenceLink value) {
      if (posInMapBuilder_ == null) {
        if (posInMap_ != null) {
          posInMap_ =
            road.data.proto.ReferenceLink.newBuilder(posInMap_).mergeFrom(value).buildPartial();
        } else {
          posInMap_ = value;
        }
        onChanged();
      } else {
        posInMapBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，与 MAP 相关的车道和链接位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
     */
    public Builder clearPosInMap() {
      if (posInMapBuilder_ == null) {
        posInMap_ = null;
        onChanged();
      } else {
        posInMap_ = null;
        posInMapBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，与 MAP 相关的车道和链接位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
     */
    public road.data.proto.ReferenceLink.Builder getPosInMapBuilder() {
      
      onChanged();
      return getPosInMapFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，与 MAP 相关的车道和链接位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
     */
    public road.data.proto.ReferenceLinkOrBuilder getPosInMapOrBuilder() {
      if (posInMapBuilder_ != null) {
        return posInMapBuilder_.getMessageOrBuilder();
      } else {
        return posInMap_ == null ?
            road.data.proto.ReferenceLink.getDefaultInstance() : posInMap_;
      }
    }
    /**
     * <pre>
     *可选，与 MAP 相关的车道和链接位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder> 
        getPosInMapFieldBuilder() {
      if (posInMapBuilder_ == null) {
        posInMapBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder>(
                getPosInMap(),
                getParentForChildren(),
                isClean());
        posInMap_ = null;
      }
      return posInMapBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.PathPlanningPoint)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.PathPlanningPoint)
  private static final road.data.proto.PathPlanningPoint DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.PathPlanningPoint();
  }

  public static road.data.proto.PathPlanningPoint getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PathPlanningPoint>
      PARSER = new com.google.protobuf.AbstractParser<PathPlanningPoint>() {
    @java.lang.Override
    public PathPlanningPoint parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new PathPlanningPoint(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<PathPlanningPoint> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PathPlanningPoint> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.PathPlanningPoint getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

