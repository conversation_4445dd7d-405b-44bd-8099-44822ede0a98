// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface PathPlanningPointOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.PathPlanningPoint)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 定义经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  boolean hasPos();
  /**
   * <pre>
   * 定义经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  road.data.proto.Position3D getPos();
  /**
   * <pre>
   * 定义经纬度和高，绝对位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  road.data.proto.Position3DOrBuilder getPosOrBuilder();

  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
   */
  boolean hasPosConfid();
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
   */
  road.data.proto.PositionConfidenceSet getPosConfid();
  /**
   * <pre>
   * 可选，定义95%置信水平的位置（经纬度和高度）综合精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet posConfid = 2;</code>
   */
  road.data.proto.PositionConfidenceSetOrBuilder getPosConfidOrBuilder();

  /**
   * <pre>
   * 可选，定义车速大小，分辨率为0.02m/s，数值8191表示无效数值
   * </pre>
   *
   * <code>uint32 speed = 3;</code>
   */
  int getSpeed();

  /**
   * <pre>
   * 可选，车辆航向角。为车头方向与正北方向的顺时针夹角。分辨率为0.0125°。
   * </pre>
   *
   * <code>uint32 heading = 4;</code>
   */
  int getHeading();

  /**
   * <pre>
   *可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpeedConfidence speedConfid = 5;</code>
   */
  int getSpeedConfidValue();
  /**
   * <pre>
   *可选，数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SpeedConfidence speedConfid = 5;</code>
   */
  road.data.proto.SpeedConfidence getSpeedConfid();

  /**
   * <pre>
   * 可选，定义95%置信水平的航向精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.HeadingConfidence headingConfid = 6;</code>
   */
  int getHeadingConfidValue();
  /**
   * <pre>
   * 可选，定义95%置信水平的航向精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.HeadingConfidence headingConfid = 6;</code>
   */
  road.data.proto.HeadingConfidence getHeadingConfid();

  /**
   * <pre>
   * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
   */
  boolean hasAcceleration();
  /**
   * <pre>
   * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
   */
  road.data.proto.AccelerationSet4Way getAcceleration();
  /**
   * <pre>
   * 可选，定义车辆四轴加速度：纵/横/垂直加速度，横摆角速度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationSet4Way acceleration = 7;</code>
   */
  road.data.proto.AccelerationSet4WayOrBuilder getAccelerationOrBuilder();

  /**
   * <pre>
   * 可选，目标四轴加速度置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
   */
  boolean hasAccelerationConfid();
  /**
   * <pre>
   * 可选，目标四轴加速度置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
   */
  road.data.proto.AccelerationConfidence getAccelerationConfid();
  /**
   * <pre>
   * 可选，目标四轴加速度置信度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.AccelerationConfidence accelerationConfid = 8;</code>
   */
  road.data.proto.AccelerationConfidenceOrBuilder getAccelerationConfidOrBuilder();

  /**
   * <pre>
   * 可选，目标到达目标位置的时间，分辨率为10ms
   * </pre>
   *
   * <code>uint32 estimatedTime = 9;</code>
   */
  int getEstimatedTime();

  /**
   * <pre>
   * 可选，定义事件的置信度;分辨率为0.005。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 10;</code>
   */
  int getTimeConfidenceValue();
  /**
   * <pre>
   * 可选，定义事件的置信度;分辨率为0.005。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 10;</code>
   */
  road.data.proto.TimeConfidence getTimeConfidence();

  /**
   * <pre>
   *可选，与 MAP 相关的车道和链接位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
   */
  boolean hasPosInMap();
  /**
   * <pre>
   *可选，与 MAP 相关的车道和链接位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
   */
  road.data.proto.ReferenceLink getPosInMap();
  /**
   * <pre>
   *可选，与 MAP 相关的车道和链接位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLink posInMap = 11;</code>
   */
  road.data.proto.ReferenceLinkOrBuilder getPosInMapOrBuilder();
}
