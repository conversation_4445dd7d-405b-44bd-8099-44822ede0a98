// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: perception.proto

package road.data.proto;

public final class Percep {
  private Percep() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface RsPerceptionInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:road.data.proto.RsPerceptionInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *渠道来源
     * </pre>
     *
     * <code>uint32 channel_id = 1;</code>
     */
    int getChannelId();

    /**
     * <pre>
     *固定8位的RCU编号
     * </pre>
     *
     * <code>string rcu_id = 2;</code>
     */
    String getRcuId();
    /**
     * <pre>
     *固定8位的RCU编号
     * </pre>
     *
     * <code>string rcu_id = 2;</code>
     */
    com.google.protobuf.ByteString
        getRcuIdBytes();

    /**
     * <pre>
     *0：未知来源，1：融合结果，2：摄像头，3：毫米波雷达，4：激光雷达,    5:相机融合，6:多杆融合 7: 单杆预测
     * </pre>
     *
     * <code>uint32 device_type = 3;</code>
     */
    int getDeviceType();

    /**
     * <pre>
     *当 deviceType 等于 0 或 1 时，此值等于0x0000000000000000000000（11 字节，每字节值等于 0）。
     *否则，此值等于对应感知设备的编号。
     *感知设备编号为22位数字字符串，每两位数字字符转换为一个字节的正整数，共计传输11个字节数据。
     * </pre>
     *
     * <code>string device_id = 4;</code>
     */
    String getDeviceId();
    /**
     * <pre>
     *当 deviceType 等于 0 或 1 时，此值等于0x0000000000000000000000（11 字节，每字节值等于 0）。
     *否则，此值等于对应感知设备的编号。
     *感知设备编号为22位数字字符串，每两位数字字符转换为一个字节的正整数，共计传输11个字节数据。
     * </pre>
     *
     * <code>string device_id = 4;</code>
     */
    com.google.protobuf.ByteString
        getDeviceIdBytes();

    /**
     * <pre>
     *感知/传感/采集器件原始数据帧输出时间戳 t0时间
     * </pre>
     *
     * <code>uint64 timestamp_of_dev_out = 5;</code>
     */
    long getTimestampOfDevOut();

    /**
     * <pre>
     *原始数据帧进入路侧融合计算应用的时间戳
     * </pre>
     *
     * <code>uint64 timestamp_of_det_in = 6;</code>
     */
    long getTimestampOfDetIn();

    /**
     * <pre>
     *路侧融合计算应用输出结构化结果的时间戳 t6
     * </pre>
     *
     * <code>uint64 timestamp_of_det_out = 7;</code>
     */
    long getTimestampOfDetOut();

    /**
     * <pre>
     *[0..10]，0：GCJ02坐标系；1：自定义独立坐标系；2-10：预留，不可缺省
     * </pre>
     *
     * <code>uint32 gnss_type = 8;</code>
     */
    int getGnssType();

    /**
     * <pre>
     *感知对象列表
     * </pre>
     *
     * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
     */
    java.util.List<PerceptionObject>
        getObjectiveList();
    /**
     * <pre>
     *感知对象列表
     * </pre>
     *
     * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
     */
    PerceptionObject getObjective(int index);
    /**
     * <pre>
     *感知对象列表
     * </pre>
     *
     * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
     */
    int getObjectiveCount();
    /**
     * <pre>
     *感知对象列表
     * </pre>
     *
     * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
     */
    java.util.List<? extends PerceptionObjectOrBuilder>
        getObjectiveOrBuilderList();
    /**
     * <pre>
     *感知对象列表
     * </pre>
     *
     * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
     */
    PerceptionObjectOrBuilder getObjectiveOrBuilder(
        int index);

    /**
     * <pre>
     *0：相机近顺；1：相机近逆；2：相机远顺；3：相机远逆；8：鱼眼；11:激光逆；12:激光顺；15:毫米波逆；16:毫米波顺；27: 单杆预测；28:数字孪生；29:多杆融合；30:相机融合；31:单杆融合
     * </pre>
     *
     * <code>uint32 slot = 10;</code>
     */
    int getSlot();

    /**
     * <pre>
     *杆位号
     * </pre>
     *
     * <code>string pole = 11;</code>
     */
    String getPole();
    /**
     * <pre>
     *杆位号
     * </pre>
     *
     * <code>string pole = 11;</code>
     */
    com.google.protobuf.ByteString
        getPoleBytes();

    /**
     * <pre>
     *原始数据帧出驱动的时间戳 t3
     * </pre>
     *
     * <code>uint64 timestamp_of_driver_out = 12;</code>
     */
    long getTimestampOfDriverOut();

    /**
     * <pre>
     *原始数据帧进入路侧感知计算应用的时间戳
     * </pre>
     *
     * <code>uint64 timestamp_of_per_in = 13;</code>
     */
    long getTimestampOfPerIn();

    /**
     * <pre>
     *原始数据帧输出路侧感知计算应用的时间戳
     * </pre>
     *
     * <code>uint64 timestamp_of_per_out = 14;</code>
     */
    long getTimestampOfPerOut();

    /**
     * <pre>
     *边缘云编号
     * </pre>
     *
     * <code>string edge_cloud_id = 15;</code>
     */
    String getEdgeCloudId();
    /**
     * <pre>
     *边缘云编号
     * </pre>
     *
     * <code>string edge_cloud_id = 15;</code>
     */
    com.google.protobuf.ByteString
        getEdgeCloudIdBytes();

    /**
     * <pre>
     *上传时间戳
     * </pre>
     *
     * <code>uint64 edge_timestamp = 16;</code>
     */
    long getEdgeTimestamp();

    /**
     * <pre>
     *单杆融合结果进入路侧多杆融合计算应用的时间戳
     * </pre>
     *
     * <code>uint64 timestamp_of_multi_poles_det_in = 17;</code>
     */
    long getTimestampOfMultiPolesDetIn();

    /**
     * <pre>
     *单杆融合结果进入路侧多杆融合计算应用的时间戳
     * </pre>
     *
     * <code>uint64 timestamp_of_multi_poles_det_out = 18;</code>
     */
    long getTimestampOfMultiPolesDetOut();
  }
  /**
   * Protobuf type {@code road.data.proto.RsPerceptionInfo}
   */
  public  static final class RsPerceptionInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:road.data.proto.RsPerceptionInfo)
      RsPerceptionInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RsPerceptionInfo.newBuilder() to construct.
    private RsPerceptionInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RsPerceptionInfo() {
      channelId_ = 0;
      rcuId_ = "";
      deviceType_ = 0;
      deviceId_ = "";
      timestampOfDevOut_ = 0L;
      timestampOfDetIn_ = 0L;
      timestampOfDetOut_ = 0L;
      gnssType_ = 0;
      objective_ = java.util.Collections.emptyList();
      slot_ = 0;
      pole_ = "";
      timestampOfDriverOut_ = 0L;
      timestampOfPerIn_ = 0L;
      timestampOfPerOut_ = 0L;
      edgeCloudId_ = "";
      edgeTimestamp_ = 0L;
      timestampOfMultiPolesDetIn_ = 0L;
      timestampOfMultiPolesDetOut_ = 0L;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RsPerceptionInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              channelId_ = input.readUInt32();
              break;
            }
            case 18: {
              String s = input.readStringRequireUtf8();

              rcuId_ = s;
              break;
            }
            case 24: {

              deviceType_ = input.readUInt32();
              break;
            }
            case 34: {
              String s = input.readStringRequireUtf8();

              deviceId_ = s;
              break;
            }
            case 40: {

              timestampOfDevOut_ = input.readUInt64();
              break;
            }
            case 48: {

              timestampOfDetIn_ = input.readUInt64();
              break;
            }
            case 56: {

              timestampOfDetOut_ = input.readUInt64();
              break;
            }
            case 64: {

              gnssType_ = input.readUInt32();
              break;
            }
            case 74: {
              if (!((mutable_bitField0_ & 0x00000100) == 0x00000100)) {
                objective_ = new java.util.ArrayList<PerceptionObject>();
                mutable_bitField0_ |= 0x00000100;
              }
              objective_.add(
                  input.readMessage(PerceptionObject.parser(), extensionRegistry));
              break;
            }
            case 80: {

              slot_ = input.readUInt32();
              break;
            }
            case 90: {
              String s = input.readStringRequireUtf8();

              pole_ = s;
              break;
            }
            case 96: {

              timestampOfDriverOut_ = input.readUInt64();
              break;
            }
            case 104: {

              timestampOfPerIn_ = input.readUInt64();
              break;
            }
            case 112: {

              timestampOfPerOut_ = input.readUInt64();
              break;
            }
            case 122: {
              String s = input.readStringRequireUtf8();

              edgeCloudId_ = s;
              break;
            }
            case 128: {

              edgeTimestamp_ = input.readUInt64();
              break;
            }
            case 136: {

              timestampOfMultiPolesDetIn_ = input.readUInt64();
              break;
            }
            case 144: {

              timestampOfMultiPolesDetOut_ = input.readUInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000100) == 0x00000100)) {
          objective_ = java.util.Collections.unmodifiableList(objective_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return Percep.internal_static_road_data_proto_RsPerceptionInfo_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return Percep.internal_static_road_data_proto_RsPerceptionInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              RsPerceptionInfo.class, Builder.class);
    }

    private int bitField0_;
    public static final int CHANNEL_ID_FIELD_NUMBER = 1;
    private int channelId_;
    /**
     * <pre>
     *渠道来源
     * </pre>
     *
     * <code>uint32 channel_id = 1;</code>
     */
    public int getChannelId() {
      return channelId_;
    }

    public static final int RCU_ID_FIELD_NUMBER = 2;
    private volatile Object rcuId_;
    /**
     * <pre>
     *固定8位的RCU编号
     * </pre>
     *
     * <code>string rcu_id = 2;</code>
     */
    public String getRcuId() {
      Object ref = rcuId_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        rcuId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *固定8位的RCU编号
     * </pre>
     *
     * <code>string rcu_id = 2;</code>
     */
    public com.google.protobuf.ByteString
        getRcuIdBytes() {
      Object ref = rcuId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        rcuId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DEVICE_TYPE_FIELD_NUMBER = 3;
    private int deviceType_;
    /**
     * <pre>
     *0：未知来源，1：融合结果，2：摄像头，3：毫米波雷达，4：激光雷达,    5:相机融合，6:多杆融合 7: 单杆预测
     * </pre>
     *
     * <code>uint32 device_type = 3;</code>
     */
    public int getDeviceType() {
      return deviceType_;
    }

    public static final int DEVICE_ID_FIELD_NUMBER = 4;
    private volatile Object deviceId_;
    /**
     * <pre>
     *当 deviceType 等于 0 或 1 时，此值等于0x0000000000000000000000（11 字节，每字节值等于 0）。
     *否则，此值等于对应感知设备的编号。
     *感知设备编号为22位数字字符串，每两位数字字符转换为一个字节的正整数，共计传输11个字节数据。
     * </pre>
     *
     * <code>string device_id = 4;</code>
     */
    public String getDeviceId() {
      Object ref = deviceId_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        deviceId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *当 deviceType 等于 0 或 1 时，此值等于0x0000000000000000000000（11 字节，每字节值等于 0）。
     *否则，此值等于对应感知设备的编号。
     *感知设备编号为22位数字字符串，每两位数字字符转换为一个字节的正整数，共计传输11个字节数据。
     * </pre>
     *
     * <code>string device_id = 4;</code>
     */
    public com.google.protobuf.ByteString
        getDeviceIdBytes() {
      Object ref = deviceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        deviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TIMESTAMP_OF_DEV_OUT_FIELD_NUMBER = 5;
    private long timestampOfDevOut_;
    /**
     * <pre>
     *感知/传感/采集器件原始数据帧输出时间戳 t0时间
     * </pre>
     *
     * <code>uint64 timestamp_of_dev_out = 5;</code>
     */
    public long getTimestampOfDevOut() {
      return timestampOfDevOut_;
    }

    public static final int TIMESTAMP_OF_DET_IN_FIELD_NUMBER = 6;
    private long timestampOfDetIn_;
    /**
     * <pre>
     *原始数据帧进入路侧融合计算应用的时间戳
     * </pre>
     *
     * <code>uint64 timestamp_of_det_in = 6;</code>
     */
    public long getTimestampOfDetIn() {
      return timestampOfDetIn_;
    }

    public static final int TIMESTAMP_OF_DET_OUT_FIELD_NUMBER = 7;
    private long timestampOfDetOut_;
    /**
     * <pre>
     *路侧融合计算应用输出结构化结果的时间戳 t6
     * </pre>
     *
     * <code>uint64 timestamp_of_det_out = 7;</code>
     */
    public long getTimestampOfDetOut() {
      return timestampOfDetOut_;
    }

    public static final int GNSS_TYPE_FIELD_NUMBER = 8;
    private int gnssType_;
    /**
     * <pre>
     *[0..10]，0：GCJ02坐标系；1：自定义独立坐标系；2-10：预留，不可缺省
     * </pre>
     *
     * <code>uint32 gnss_type = 8;</code>
     */
    public int getGnssType() {
      return gnssType_;
    }

    public static final int OBJECTIVE_FIELD_NUMBER = 9;
    private java.util.List<PerceptionObject> objective_;
    /**
     * <pre>
     *感知对象列表
     * </pre>
     *
     * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
     */
    public java.util.List<PerceptionObject> getObjectiveList() {
      return objective_;
    }
    /**
     * <pre>
     *感知对象列表
     * </pre>
     *
     * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
     */
    public java.util.List<? extends PerceptionObjectOrBuilder>
        getObjectiveOrBuilderList() {
      return objective_;
    }
    /**
     * <pre>
     *感知对象列表
     * </pre>
     *
     * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
     */
    public int getObjectiveCount() {
      return objective_.size();
    }
    /**
     * <pre>
     *感知对象列表
     * </pre>
     *
     * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
     */
    public PerceptionObject getObjective(int index) {
      return objective_.get(index);
    }
    /**
     * <pre>
     *感知对象列表
     * </pre>
     *
     * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
     */
    public PerceptionObjectOrBuilder getObjectiveOrBuilder(
        int index) {
      return objective_.get(index);
    }

    public static final int SLOT_FIELD_NUMBER = 10;
    private int slot_;
    /**
     * <pre>
     *0：相机近顺；1：相机近逆；2：相机远顺；3：相机远逆；8：鱼眼；11:激光逆；12:激光顺；15:毫米波逆；16:毫米波顺；27: 单杆预测；28:数字孪生；29:多杆融合；30:相机融合；31:单杆融合
     * </pre>
     *
     * <code>uint32 slot = 10;</code>
     */
    public int getSlot() {
      return slot_;
    }

    public static final int POLE_FIELD_NUMBER = 11;
    private volatile Object pole_;
    /**
     * <pre>
     *杆位号
     * </pre>
     *
     * <code>string pole = 11;</code>
     */
    public String getPole() {
      Object ref = pole_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        pole_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *杆位号
     * </pre>
     *
     * <code>string pole = 11;</code>
     */
    public com.google.protobuf.ByteString
        getPoleBytes() {
      Object ref = pole_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        pole_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TIMESTAMP_OF_DRIVER_OUT_FIELD_NUMBER = 12;
    private long timestampOfDriverOut_;
    /**
     * <pre>
     *原始数据帧出驱动的时间戳 t3
     * </pre>
     *
     * <code>uint64 timestamp_of_driver_out = 12;</code>
     */
    public long getTimestampOfDriverOut() {
      return timestampOfDriverOut_;
    }

    public static final int TIMESTAMP_OF_PER_IN_FIELD_NUMBER = 13;
    private long timestampOfPerIn_;
    /**
     * <pre>
     *原始数据帧进入路侧感知计算应用的时间戳
     * </pre>
     *
     * <code>uint64 timestamp_of_per_in = 13;</code>
     */
    public long getTimestampOfPerIn() {
      return timestampOfPerIn_;
    }

    public static final int TIMESTAMP_OF_PER_OUT_FIELD_NUMBER = 14;
    private long timestampOfPerOut_;
    /**
     * <pre>
     *原始数据帧输出路侧感知计算应用的时间戳
     * </pre>
     *
     * <code>uint64 timestamp_of_per_out = 14;</code>
     */
    public long getTimestampOfPerOut() {
      return timestampOfPerOut_;
    }

    public static final int EDGE_CLOUD_ID_FIELD_NUMBER = 15;
    private volatile Object edgeCloudId_;
    /**
     * <pre>
     *边缘云编号
     * </pre>
     *
     * <code>string edge_cloud_id = 15;</code>
     */
    public String getEdgeCloudId() {
      Object ref = edgeCloudId_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        edgeCloudId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *边缘云编号
     * </pre>
     *
     * <code>string edge_cloud_id = 15;</code>
     */
    public com.google.protobuf.ByteString
        getEdgeCloudIdBytes() {
      Object ref = edgeCloudId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        edgeCloudId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EDGE_TIMESTAMP_FIELD_NUMBER = 16;
    private long edgeTimestamp_;
    /**
     * <pre>
     *上传时间戳
     * </pre>
     *
     * <code>uint64 edge_timestamp = 16;</code>
     */
    public long getEdgeTimestamp() {
      return edgeTimestamp_;
    }

    public static final int TIMESTAMP_OF_MULTI_POLES_DET_IN_FIELD_NUMBER = 17;
    private long timestampOfMultiPolesDetIn_;
    /**
     * <pre>
     *单杆融合结果进入路侧多杆融合计算应用的时间戳
     * </pre>
     *
     * <code>uint64 timestamp_of_multi_poles_det_in = 17;</code>
     */
    public long getTimestampOfMultiPolesDetIn() {
      return timestampOfMultiPolesDetIn_;
    }

    public static final int TIMESTAMP_OF_MULTI_POLES_DET_OUT_FIELD_NUMBER = 18;
    private long timestampOfMultiPolesDetOut_;
    /**
     * <pre>
     *单杆融合结果进入路侧多杆融合计算应用的时间戳
     * </pre>
     *
     * <code>uint64 timestamp_of_multi_poles_det_out = 18;</code>
     */
    public long getTimestampOfMultiPolesDetOut() {
      return timestampOfMultiPolesDetOut_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (channelId_ != 0) {
        output.writeUInt32(1, channelId_);
      }
      if (!getRcuIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, rcuId_);
      }
      if (deviceType_ != 0) {
        output.writeUInt32(3, deviceType_);
      }
      if (!getDeviceIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, deviceId_);
      }
      if (timestampOfDevOut_ != 0L) {
        output.writeUInt64(5, timestampOfDevOut_);
      }
      if (timestampOfDetIn_ != 0L) {
        output.writeUInt64(6, timestampOfDetIn_);
      }
      if (timestampOfDetOut_ != 0L) {
        output.writeUInt64(7, timestampOfDetOut_);
      }
      if (gnssType_ != 0) {
        output.writeUInt32(8, gnssType_);
      }
      for (int i = 0; i < objective_.size(); i++) {
        output.writeMessage(9, objective_.get(i));
      }
      if (slot_ != 0) {
        output.writeUInt32(10, slot_);
      }
      if (!getPoleBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, pole_);
      }
      if (timestampOfDriverOut_ != 0L) {
        output.writeUInt64(12, timestampOfDriverOut_);
      }
      if (timestampOfPerIn_ != 0L) {
        output.writeUInt64(13, timestampOfPerIn_);
      }
      if (timestampOfPerOut_ != 0L) {
        output.writeUInt64(14, timestampOfPerOut_);
      }
      if (!getEdgeCloudIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 15, edgeCloudId_);
      }
      if (edgeTimestamp_ != 0L) {
        output.writeUInt64(16, edgeTimestamp_);
      }
      if (timestampOfMultiPolesDetIn_ != 0L) {
        output.writeUInt64(17, timestampOfMultiPolesDetIn_);
      }
      if (timestampOfMultiPolesDetOut_ != 0L) {
        output.writeUInt64(18, timestampOfMultiPolesDetOut_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (channelId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, channelId_);
      }
      if (!getRcuIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, rcuId_);
      }
      if (deviceType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, deviceType_);
      }
      if (!getDeviceIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, deviceId_);
      }
      if (timestampOfDevOut_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(5, timestampOfDevOut_);
      }
      if (timestampOfDetIn_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(6, timestampOfDetIn_);
      }
      if (timestampOfDetOut_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(7, timestampOfDetOut_);
      }
      if (gnssType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(8, gnssType_);
      }
      for (int i = 0; i < objective_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, objective_.get(i));
      }
      if (slot_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(10, slot_);
      }
      if (!getPoleBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, pole_);
      }
      if (timestampOfDriverOut_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(12, timestampOfDriverOut_);
      }
      if (timestampOfPerIn_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(13, timestampOfPerIn_);
      }
      if (timestampOfPerOut_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(14, timestampOfPerOut_);
      }
      if (!getEdgeCloudIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, edgeCloudId_);
      }
      if (edgeTimestamp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(16, edgeTimestamp_);
      }
      if (timestampOfMultiPolesDetIn_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(17, timestampOfMultiPolesDetIn_);
      }
      if (timestampOfMultiPolesDetOut_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(18, timestampOfMultiPolesDetOut_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof RsPerceptionInfo)) {
        return super.equals(obj);
      }
      RsPerceptionInfo other = (RsPerceptionInfo) obj;

      boolean result = true;
      result = result && (getChannelId()
          == other.getChannelId());
      result = result && getRcuId()
          .equals(other.getRcuId());
      result = result && (getDeviceType()
          == other.getDeviceType());
      result = result && getDeviceId()
          .equals(other.getDeviceId());
      result = result && (getTimestampOfDevOut()
          == other.getTimestampOfDevOut());
      result = result && (getTimestampOfDetIn()
          == other.getTimestampOfDetIn());
      result = result && (getTimestampOfDetOut()
          == other.getTimestampOfDetOut());
      result = result && (getGnssType()
          == other.getGnssType());
      result = result && getObjectiveList()
          .equals(other.getObjectiveList());
      result = result && (getSlot()
          == other.getSlot());
      result = result && getPole()
          .equals(other.getPole());
      result = result && (getTimestampOfDriverOut()
          == other.getTimestampOfDriverOut());
      result = result && (getTimestampOfPerIn()
          == other.getTimestampOfPerIn());
      result = result && (getTimestampOfPerOut()
          == other.getTimestampOfPerOut());
      result = result && getEdgeCloudId()
          .equals(other.getEdgeCloudId());
      result = result && (getEdgeTimestamp()
          == other.getEdgeTimestamp());
      result = result && (getTimestampOfMultiPolesDetIn()
          == other.getTimestampOfMultiPolesDetIn());
      result = result && (getTimestampOfMultiPolesDetOut()
          == other.getTimestampOfMultiPolesDetOut());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHANNEL_ID_FIELD_NUMBER;
      hash = (53 * hash) + getChannelId();
      hash = (37 * hash) + RCU_ID_FIELD_NUMBER;
      hash = (53 * hash) + getRcuId().hashCode();
      hash = (37 * hash) + DEVICE_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceType();
      hash = (37 * hash) + DEVICE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceId().hashCode();
      hash = (37 * hash) + TIMESTAMP_OF_DEV_OUT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestampOfDevOut());
      hash = (37 * hash) + TIMESTAMP_OF_DET_IN_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestampOfDetIn());
      hash = (37 * hash) + TIMESTAMP_OF_DET_OUT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestampOfDetOut());
      hash = (37 * hash) + GNSS_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getGnssType();
      if (getObjectiveCount() > 0) {
        hash = (37 * hash) + OBJECTIVE_FIELD_NUMBER;
        hash = (53 * hash) + getObjectiveList().hashCode();
      }
      hash = (37 * hash) + SLOT_FIELD_NUMBER;
      hash = (53 * hash) + getSlot();
      hash = (37 * hash) + POLE_FIELD_NUMBER;
      hash = (53 * hash) + getPole().hashCode();
      hash = (37 * hash) + TIMESTAMP_OF_DRIVER_OUT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestampOfDriverOut());
      hash = (37 * hash) + TIMESTAMP_OF_PER_IN_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestampOfPerIn());
      hash = (37 * hash) + TIMESTAMP_OF_PER_OUT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestampOfPerOut());
      hash = (37 * hash) + EDGE_CLOUD_ID_FIELD_NUMBER;
      hash = (53 * hash) + getEdgeCloudId().hashCode();
      hash = (37 * hash) + EDGE_TIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEdgeTimestamp());
      hash = (37 * hash) + TIMESTAMP_OF_MULTI_POLES_DET_IN_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestampOfMultiPolesDetIn());
      hash = (37 * hash) + TIMESTAMP_OF_MULTI_POLES_DET_OUT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestampOfMultiPolesDetOut());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static RsPerceptionInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RsPerceptionInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RsPerceptionInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RsPerceptionInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RsPerceptionInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RsPerceptionInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RsPerceptionInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static RsPerceptionInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static RsPerceptionInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static RsPerceptionInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static RsPerceptionInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static RsPerceptionInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(RsPerceptionInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code road.data.proto.RsPerceptionInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:road.data.proto.RsPerceptionInfo)
        RsPerceptionInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return Percep.internal_static_road_data_proto_RsPerceptionInfo_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return Percep.internal_static_road_data_proto_RsPerceptionInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                RsPerceptionInfo.class, Builder.class);
      }

      // Construct using road.data.proto.Percep.RsPerceptionInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getObjectiveFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        channelId_ = 0;

        rcuId_ = "";

        deviceType_ = 0;

        deviceId_ = "";

        timestampOfDevOut_ = 0L;

        timestampOfDetIn_ = 0L;

        timestampOfDetOut_ = 0L;

        gnssType_ = 0;

        if (objectiveBuilder_ == null) {
          objective_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000100);
        } else {
          objectiveBuilder_.clear();
        }
        slot_ = 0;

        pole_ = "";

        timestampOfDriverOut_ = 0L;

        timestampOfPerIn_ = 0L;

        timestampOfPerOut_ = 0L;

        edgeCloudId_ = "";

        edgeTimestamp_ = 0L;

        timestampOfMultiPolesDetIn_ = 0L;

        timestampOfMultiPolesDetOut_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return Percep.internal_static_road_data_proto_RsPerceptionInfo_descriptor;
      }

      public RsPerceptionInfo getDefaultInstanceForType() {
        return RsPerceptionInfo.getDefaultInstance();
      }

      public RsPerceptionInfo build() {
        RsPerceptionInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public RsPerceptionInfo buildPartial() {
        RsPerceptionInfo result = new RsPerceptionInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.channelId_ = channelId_;
        result.rcuId_ = rcuId_;
        result.deviceType_ = deviceType_;
        result.deviceId_ = deviceId_;
        result.timestampOfDevOut_ = timestampOfDevOut_;
        result.timestampOfDetIn_ = timestampOfDetIn_;
        result.timestampOfDetOut_ = timestampOfDetOut_;
        result.gnssType_ = gnssType_;
        if (objectiveBuilder_ == null) {
          if (((bitField0_ & 0x00000100) == 0x00000100)) {
            objective_ = java.util.Collections.unmodifiableList(objective_);
            bitField0_ = (bitField0_ & ~0x00000100);
          }
          result.objective_ = objective_;
        } else {
          result.objective_ = objectiveBuilder_.build();
        }
        result.slot_ = slot_;
        result.pole_ = pole_;
        result.timestampOfDriverOut_ = timestampOfDriverOut_;
        result.timestampOfPerIn_ = timestampOfPerIn_;
        result.timestampOfPerOut_ = timestampOfPerOut_;
        result.edgeCloudId_ = edgeCloudId_;
        result.edgeTimestamp_ = edgeTimestamp_;
        result.timestampOfMultiPolesDetIn_ = timestampOfMultiPolesDetIn_;
        result.timestampOfMultiPolesDetOut_ = timestampOfMultiPolesDetOut_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof RsPerceptionInfo) {
          return mergeFrom((RsPerceptionInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(RsPerceptionInfo other) {
        if (other == RsPerceptionInfo.getDefaultInstance()) return this;
        if (other.getChannelId() != 0) {
          setChannelId(other.getChannelId());
        }
        if (!other.getRcuId().isEmpty()) {
          rcuId_ = other.rcuId_;
          onChanged();
        }
        if (other.getDeviceType() != 0) {
          setDeviceType(other.getDeviceType());
        }
        if (!other.getDeviceId().isEmpty()) {
          deviceId_ = other.deviceId_;
          onChanged();
        }
        if (other.getTimestampOfDevOut() != 0L) {
          setTimestampOfDevOut(other.getTimestampOfDevOut());
        }
        if (other.getTimestampOfDetIn() != 0L) {
          setTimestampOfDetIn(other.getTimestampOfDetIn());
        }
        if (other.getTimestampOfDetOut() != 0L) {
          setTimestampOfDetOut(other.getTimestampOfDetOut());
        }
        if (other.getGnssType() != 0) {
          setGnssType(other.getGnssType());
        }
        if (objectiveBuilder_ == null) {
          if (!other.objective_.isEmpty()) {
            if (objective_.isEmpty()) {
              objective_ = other.objective_;
              bitField0_ = (bitField0_ & ~0x00000100);
            } else {
              ensureObjectiveIsMutable();
              objective_.addAll(other.objective_);
            }
            onChanged();
          }
        } else {
          if (!other.objective_.isEmpty()) {
            if (objectiveBuilder_.isEmpty()) {
              objectiveBuilder_.dispose();
              objectiveBuilder_ = null;
              objective_ = other.objective_;
              bitField0_ = (bitField0_ & ~0x00000100);
              objectiveBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getObjectiveFieldBuilder() : null;
            } else {
              objectiveBuilder_.addAllMessages(other.objective_);
            }
          }
        }
        if (other.getSlot() != 0) {
          setSlot(other.getSlot());
        }
        if (!other.getPole().isEmpty()) {
          pole_ = other.pole_;
          onChanged();
        }
        if (other.getTimestampOfDriverOut() != 0L) {
          setTimestampOfDriverOut(other.getTimestampOfDriverOut());
        }
        if (other.getTimestampOfPerIn() != 0L) {
          setTimestampOfPerIn(other.getTimestampOfPerIn());
        }
        if (other.getTimestampOfPerOut() != 0L) {
          setTimestampOfPerOut(other.getTimestampOfPerOut());
        }
        if (!other.getEdgeCloudId().isEmpty()) {
          edgeCloudId_ = other.edgeCloudId_;
          onChanged();
        }
        if (other.getEdgeTimestamp() != 0L) {
          setEdgeTimestamp(other.getEdgeTimestamp());
        }
        if (other.getTimestampOfMultiPolesDetIn() != 0L) {
          setTimestampOfMultiPolesDetIn(other.getTimestampOfMultiPolesDetIn());
        }
        if (other.getTimestampOfMultiPolesDetOut() != 0L) {
          setTimestampOfMultiPolesDetOut(other.getTimestampOfMultiPolesDetOut());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        RsPerceptionInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (RsPerceptionInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int channelId_ ;
      /**
       * <pre>
       *渠道来源
       * </pre>
       *
       * <code>uint32 channel_id = 1;</code>
       */
      public int getChannelId() {
        return channelId_;
      }
      /**
       * <pre>
       *渠道来源
       * </pre>
       *
       * <code>uint32 channel_id = 1;</code>
       */
      public Builder setChannelId(int value) {
        
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *渠道来源
       * </pre>
       *
       * <code>uint32 channel_id = 1;</code>
       */
      public Builder clearChannelId() {
        
        channelId_ = 0;
        onChanged();
        return this;
      }

      private Object rcuId_ = "";
      /**
       * <pre>
       *固定8位的RCU编号
       * </pre>
       *
       * <code>string rcu_id = 2;</code>
       */
      public String getRcuId() {
        Object ref = rcuId_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          rcuId_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       *固定8位的RCU编号
       * </pre>
       *
       * <code>string rcu_id = 2;</code>
       */
      public com.google.protobuf.ByteString
          getRcuIdBytes() {
        Object ref = rcuId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          rcuId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *固定8位的RCU编号
       * </pre>
       *
       * <code>string rcu_id = 2;</code>
       */
      public Builder setRcuId(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        rcuId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *固定8位的RCU编号
       * </pre>
       *
       * <code>string rcu_id = 2;</code>
       */
      public Builder clearRcuId() {
        
        rcuId_ = getDefaultInstance().getRcuId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *固定8位的RCU编号
       * </pre>
       *
       * <code>string rcu_id = 2;</code>
       */
      public Builder setRcuIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        rcuId_ = value;
        onChanged();
        return this;
      }

      private int deviceType_ ;
      /**
       * <pre>
       *0：未知来源，1：融合结果，2：摄像头，3：毫米波雷达，4：激光雷达,    5:相机融合，6:多杆融合 7: 单杆预测
       * </pre>
       *
       * <code>uint32 device_type = 3;</code>
       */
      public int getDeviceType() {
        return deviceType_;
      }
      /**
       * <pre>
       *0：未知来源，1：融合结果，2：摄像头，3：毫米波雷达，4：激光雷达,    5:相机融合，6:多杆融合 7: 单杆预测
       * </pre>
       *
       * <code>uint32 device_type = 3;</code>
       */
      public Builder setDeviceType(int value) {
        
        deviceType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0：未知来源，1：融合结果，2：摄像头，3：毫米波雷达，4：激光雷达,    5:相机融合，6:多杆融合 7: 单杆预测
       * </pre>
       *
       * <code>uint32 device_type = 3;</code>
       */
      public Builder clearDeviceType() {
        
        deviceType_ = 0;
        onChanged();
        return this;
      }

      private Object deviceId_ = "";
      /**
       * <pre>
       *当 deviceType 等于 0 或 1 时，此值等于0x0000000000000000000000（11 字节，每字节值等于 0）。
       *否则，此值等于对应感知设备的编号。
       *感知设备编号为22位数字字符串，每两位数字字符转换为一个字节的正整数，共计传输11个字节数据。
       * </pre>
       *
       * <code>string device_id = 4;</code>
       */
      public String getDeviceId() {
        Object ref = deviceId_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          deviceId_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       *当 deviceType 等于 0 或 1 时，此值等于0x0000000000000000000000（11 字节，每字节值等于 0）。
       *否则，此值等于对应感知设备的编号。
       *感知设备编号为22位数字字符串，每两位数字字符转换为一个字节的正整数，共计传输11个字节数据。
       * </pre>
       *
       * <code>string device_id = 4;</code>
       */
      public com.google.protobuf.ByteString
          getDeviceIdBytes() {
        Object ref = deviceId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          deviceId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *当 deviceType 等于 0 或 1 时，此值等于0x0000000000000000000000（11 字节，每字节值等于 0）。
       *否则，此值等于对应感知设备的编号。
       *感知设备编号为22位数字字符串，每两位数字字符转换为一个字节的正整数，共计传输11个字节数据。
       * </pre>
       *
       * <code>string device_id = 4;</code>
       */
      public Builder setDeviceId(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        deviceId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当 deviceType 等于 0 或 1 时，此值等于0x0000000000000000000000（11 字节，每字节值等于 0）。
       *否则，此值等于对应感知设备的编号。
       *感知设备编号为22位数字字符串，每两位数字字符转换为一个字节的正整数，共计传输11个字节数据。
       * </pre>
       *
       * <code>string device_id = 4;</code>
       */
      public Builder clearDeviceId() {
        
        deviceId_ = getDefaultInstance().getDeviceId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当 deviceType 等于 0 或 1 时，此值等于0x0000000000000000000000（11 字节，每字节值等于 0）。
       *否则，此值等于对应感知设备的编号。
       *感知设备编号为22位数字字符串，每两位数字字符转换为一个字节的正整数，共计传输11个字节数据。
       * </pre>
       *
       * <code>string device_id = 4;</code>
       */
      public Builder setDeviceIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        deviceId_ = value;
        onChanged();
        return this;
      }

      private long timestampOfDevOut_ ;
      /**
       * <pre>
       *感知/传感/采集器件原始数据帧输出时间戳 t0时间
       * </pre>
       *
       * <code>uint64 timestamp_of_dev_out = 5;</code>
       */
      public long getTimestampOfDevOut() {
        return timestampOfDevOut_;
      }
      /**
       * <pre>
       *感知/传感/采集器件原始数据帧输出时间戳 t0时间
       * </pre>
       *
       * <code>uint64 timestamp_of_dev_out = 5;</code>
       */
      public Builder setTimestampOfDevOut(long value) {
        
        timestampOfDevOut_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *感知/传感/采集器件原始数据帧输出时间戳 t0时间
       * </pre>
       *
       * <code>uint64 timestamp_of_dev_out = 5;</code>
       */
      public Builder clearTimestampOfDevOut() {
        
        timestampOfDevOut_ = 0L;
        onChanged();
        return this;
      }

      private long timestampOfDetIn_ ;
      /**
       * <pre>
       *原始数据帧进入路侧融合计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_det_in = 6;</code>
       */
      public long getTimestampOfDetIn() {
        return timestampOfDetIn_;
      }
      /**
       * <pre>
       *原始数据帧进入路侧融合计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_det_in = 6;</code>
       */
      public Builder setTimestampOfDetIn(long value) {
        
        timestampOfDetIn_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *原始数据帧进入路侧融合计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_det_in = 6;</code>
       */
      public Builder clearTimestampOfDetIn() {
        
        timestampOfDetIn_ = 0L;
        onChanged();
        return this;
      }

      private long timestampOfDetOut_ ;
      /**
       * <pre>
       *路侧融合计算应用输出结构化结果的时间戳 t6
       * </pre>
       *
       * <code>uint64 timestamp_of_det_out = 7;</code>
       */
      public long getTimestampOfDetOut() {
        return timestampOfDetOut_;
      }
      /**
       * <pre>
       *路侧融合计算应用输出结构化结果的时间戳 t6
       * </pre>
       *
       * <code>uint64 timestamp_of_det_out = 7;</code>
       */
      public Builder setTimestampOfDetOut(long value) {
        
        timestampOfDetOut_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *路侧融合计算应用输出结构化结果的时间戳 t6
       * </pre>
       *
       * <code>uint64 timestamp_of_det_out = 7;</code>
       */
      public Builder clearTimestampOfDetOut() {
        
        timestampOfDetOut_ = 0L;
        onChanged();
        return this;
      }

      private int gnssType_ ;
      /**
       * <pre>
       *[0..10]，0：GCJ02坐标系；1：自定义独立坐标系；2-10：预留，不可缺省
       * </pre>
       *
       * <code>uint32 gnss_type = 8;</code>
       */
      public int getGnssType() {
        return gnssType_;
      }
      /**
       * <pre>
       *[0..10]，0：GCJ02坐标系；1：自定义独立坐标系；2-10：预留，不可缺省
       * </pre>
       *
       * <code>uint32 gnss_type = 8;</code>
       */
      public Builder setGnssType(int value) {
        
        gnssType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *[0..10]，0：GCJ02坐标系；1：自定义独立坐标系；2-10：预留，不可缺省
       * </pre>
       *
       * <code>uint32 gnss_type = 8;</code>
       */
      public Builder clearGnssType() {
        
        gnssType_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<PerceptionObject> objective_ =
        java.util.Collections.emptyList();
      private void ensureObjectiveIsMutable() {
        if (!((bitField0_ & 0x00000100) == 0x00000100)) {
          objective_ = new java.util.ArrayList<PerceptionObject>(objective_);
          bitField0_ |= 0x00000100;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          PerceptionObject, PerceptionObject.Builder, PerceptionObjectOrBuilder> objectiveBuilder_;

      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public java.util.List<PerceptionObject> getObjectiveList() {
        if (objectiveBuilder_ == null) {
          return java.util.Collections.unmodifiableList(objective_);
        } else {
          return objectiveBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public int getObjectiveCount() {
        if (objectiveBuilder_ == null) {
          return objective_.size();
        } else {
          return objectiveBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public PerceptionObject getObjective(int index) {
        if (objectiveBuilder_ == null) {
          return objective_.get(index);
        } else {
          return objectiveBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public Builder setObjective(
          int index, PerceptionObject value) {
        if (objectiveBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureObjectiveIsMutable();
          objective_.set(index, value);
          onChanged();
        } else {
          objectiveBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public Builder setObjective(
          int index, PerceptionObject.Builder builderForValue) {
        if (objectiveBuilder_ == null) {
          ensureObjectiveIsMutable();
          objective_.set(index, builderForValue.build());
          onChanged();
        } else {
          objectiveBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public Builder addObjective(PerceptionObject value) {
        if (objectiveBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureObjectiveIsMutable();
          objective_.add(value);
          onChanged();
        } else {
          objectiveBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public Builder addObjective(
          int index, PerceptionObject value) {
        if (objectiveBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureObjectiveIsMutable();
          objective_.add(index, value);
          onChanged();
        } else {
          objectiveBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public Builder addObjective(
          PerceptionObject.Builder builderForValue) {
        if (objectiveBuilder_ == null) {
          ensureObjectiveIsMutable();
          objective_.add(builderForValue.build());
          onChanged();
        } else {
          objectiveBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public Builder addObjective(
          int index, PerceptionObject.Builder builderForValue) {
        if (objectiveBuilder_ == null) {
          ensureObjectiveIsMutable();
          objective_.add(index, builderForValue.build());
          onChanged();
        } else {
          objectiveBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public Builder addAllObjective(
          Iterable<? extends PerceptionObject> values) {
        if (objectiveBuilder_ == null) {
          ensureObjectiveIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, objective_);
          onChanged();
        } else {
          objectiveBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public Builder clearObjective() {
        if (objectiveBuilder_ == null) {
          objective_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000100);
          onChanged();
        } else {
          objectiveBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public Builder removeObjective(int index) {
        if (objectiveBuilder_ == null) {
          ensureObjectiveIsMutable();
          objective_.remove(index);
          onChanged();
        } else {
          objectiveBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public PerceptionObject.Builder getObjectiveBuilder(
          int index) {
        return getObjectiveFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public PerceptionObjectOrBuilder getObjectiveOrBuilder(
          int index) {
        if (objectiveBuilder_ == null) {
          return objective_.get(index);  } else {
          return objectiveBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public java.util.List<? extends PerceptionObjectOrBuilder>
           getObjectiveOrBuilderList() {
        if (objectiveBuilder_ != null) {
          return objectiveBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(objective_);
        }
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public PerceptionObject.Builder addObjectiveBuilder() {
        return getObjectiveFieldBuilder().addBuilder(
            PerceptionObject.getDefaultInstance());
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public PerceptionObject.Builder addObjectiveBuilder(
          int index) {
        return getObjectiveFieldBuilder().addBuilder(
            index, PerceptionObject.getDefaultInstance());
      }
      /**
       * <pre>
       *感知对象列表
       * </pre>
       *
       * <code>repeated .road.data.proto.PerceptionObject objective = 9;</code>
       */
      public java.util.List<PerceptionObject.Builder>
           getObjectiveBuilderList() {
        return getObjectiveFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          PerceptionObject, PerceptionObject.Builder, PerceptionObjectOrBuilder>
          getObjectiveFieldBuilder() {
        if (objectiveBuilder_ == null) {
          objectiveBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              PerceptionObject, PerceptionObject.Builder, PerceptionObjectOrBuilder>(
                  objective_,
                  ((bitField0_ & 0x00000100) == 0x00000100),
                  getParentForChildren(),
                  isClean());
          objective_ = null;
        }
        return objectiveBuilder_;
      }

      private int slot_ ;
      /**
       * <pre>
       *0：相机近顺；1：相机近逆；2：相机远顺；3：相机远逆；8：鱼眼；11:激光逆；12:激光顺；15:毫米波逆；16:毫米波顺；27: 单杆预测；28:数字孪生；29:多杆融合；30:相机融合；31:单杆融合
       * </pre>
       *
       * <code>uint32 slot = 10;</code>
       */
      public int getSlot() {
        return slot_;
      }
      /**
       * <pre>
       *0：相机近顺；1：相机近逆；2：相机远顺；3：相机远逆；8：鱼眼；11:激光逆；12:激光顺；15:毫米波逆；16:毫米波顺；27: 单杆预测；28:数字孪生；29:多杆融合；30:相机融合；31:单杆融合
       * </pre>
       *
       * <code>uint32 slot = 10;</code>
       */
      public Builder setSlot(int value) {
        
        slot_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0：相机近顺；1：相机近逆；2：相机远顺；3：相机远逆；8：鱼眼；11:激光逆；12:激光顺；15:毫米波逆；16:毫米波顺；27: 单杆预测；28:数字孪生；29:多杆融合；30:相机融合；31:单杆融合
       * </pre>
       *
       * <code>uint32 slot = 10;</code>
       */
      public Builder clearSlot() {
        
        slot_ = 0;
        onChanged();
        return this;
      }

      private Object pole_ = "";
      /**
       * <pre>
       *杆位号
       * </pre>
       *
       * <code>string pole = 11;</code>
       */
      public String getPole() {
        Object ref = pole_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          pole_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       *杆位号
       * </pre>
       *
       * <code>string pole = 11;</code>
       */
      public com.google.protobuf.ByteString
          getPoleBytes() {
        Object ref = pole_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          pole_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *杆位号
       * </pre>
       *
       * <code>string pole = 11;</code>
       */
      public Builder setPole(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        pole_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *杆位号
       * </pre>
       *
       * <code>string pole = 11;</code>
       */
      public Builder clearPole() {
        
        pole_ = getDefaultInstance().getPole();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *杆位号
       * </pre>
       *
       * <code>string pole = 11;</code>
       */
      public Builder setPoleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        pole_ = value;
        onChanged();
        return this;
      }

      private long timestampOfDriverOut_ ;
      /**
       * <pre>
       *原始数据帧出驱动的时间戳 t3
       * </pre>
       *
       * <code>uint64 timestamp_of_driver_out = 12;</code>
       */
      public long getTimestampOfDriverOut() {
        return timestampOfDriverOut_;
      }
      /**
       * <pre>
       *原始数据帧出驱动的时间戳 t3
       * </pre>
       *
       * <code>uint64 timestamp_of_driver_out = 12;</code>
       */
      public Builder setTimestampOfDriverOut(long value) {
        
        timestampOfDriverOut_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *原始数据帧出驱动的时间戳 t3
       * </pre>
       *
       * <code>uint64 timestamp_of_driver_out = 12;</code>
       */
      public Builder clearTimestampOfDriverOut() {
        
        timestampOfDriverOut_ = 0L;
        onChanged();
        return this;
      }

      private long timestampOfPerIn_ ;
      /**
       * <pre>
       *原始数据帧进入路侧感知计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_per_in = 13;</code>
       */
      public long getTimestampOfPerIn() {
        return timestampOfPerIn_;
      }
      /**
       * <pre>
       *原始数据帧进入路侧感知计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_per_in = 13;</code>
       */
      public Builder setTimestampOfPerIn(long value) {
        
        timestampOfPerIn_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *原始数据帧进入路侧感知计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_per_in = 13;</code>
       */
      public Builder clearTimestampOfPerIn() {
        
        timestampOfPerIn_ = 0L;
        onChanged();
        return this;
      }

      private long timestampOfPerOut_ ;
      /**
       * <pre>
       *原始数据帧输出路侧感知计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_per_out = 14;</code>
       */
      public long getTimestampOfPerOut() {
        return timestampOfPerOut_;
      }
      /**
       * <pre>
       *原始数据帧输出路侧感知计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_per_out = 14;</code>
       */
      public Builder setTimestampOfPerOut(long value) {
        
        timestampOfPerOut_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *原始数据帧输出路侧感知计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_per_out = 14;</code>
       */
      public Builder clearTimestampOfPerOut() {
        
        timestampOfPerOut_ = 0L;
        onChanged();
        return this;
      }

      private Object edgeCloudId_ = "";
      /**
       * <pre>
       *边缘云编号
       * </pre>
       *
       * <code>string edge_cloud_id = 15;</code>
       */
      public String getEdgeCloudId() {
        Object ref = edgeCloudId_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          edgeCloudId_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       *边缘云编号
       * </pre>
       *
       * <code>string edge_cloud_id = 15;</code>
       */
      public com.google.protobuf.ByteString
          getEdgeCloudIdBytes() {
        Object ref = edgeCloudId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          edgeCloudId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *边缘云编号
       * </pre>
       *
       * <code>string edge_cloud_id = 15;</code>
       */
      public Builder setEdgeCloudId(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        edgeCloudId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *边缘云编号
       * </pre>
       *
       * <code>string edge_cloud_id = 15;</code>
       */
      public Builder clearEdgeCloudId() {
        
        edgeCloudId_ = getDefaultInstance().getEdgeCloudId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *边缘云编号
       * </pre>
       *
       * <code>string edge_cloud_id = 15;</code>
       */
      public Builder setEdgeCloudIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        edgeCloudId_ = value;
        onChanged();
        return this;
      }

      private long edgeTimestamp_ ;
      /**
       * <pre>
       *上传时间戳
       * </pre>
       *
       * <code>uint64 edge_timestamp = 16;</code>
       */
      public long getEdgeTimestamp() {
        return edgeTimestamp_;
      }
      /**
       * <pre>
       *上传时间戳
       * </pre>
       *
       * <code>uint64 edge_timestamp = 16;</code>
       */
      public Builder setEdgeTimestamp(long value) {
        
        edgeTimestamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *上传时间戳
       * </pre>
       *
       * <code>uint64 edge_timestamp = 16;</code>
       */
      public Builder clearEdgeTimestamp() {
        
        edgeTimestamp_ = 0L;
        onChanged();
        return this;
      }

      private long timestampOfMultiPolesDetIn_ ;
      /**
       * <pre>
       *单杆融合结果进入路侧多杆融合计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_multi_poles_det_in = 17;</code>
       */
      public long getTimestampOfMultiPolesDetIn() {
        return timestampOfMultiPolesDetIn_;
      }
      /**
       * <pre>
       *单杆融合结果进入路侧多杆融合计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_multi_poles_det_in = 17;</code>
       */
      public Builder setTimestampOfMultiPolesDetIn(long value) {
        
        timestampOfMultiPolesDetIn_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *单杆融合结果进入路侧多杆融合计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_multi_poles_det_in = 17;</code>
       */
      public Builder clearTimestampOfMultiPolesDetIn() {
        
        timestampOfMultiPolesDetIn_ = 0L;
        onChanged();
        return this;
      }

      private long timestampOfMultiPolesDetOut_ ;
      /**
       * <pre>
       *单杆融合结果进入路侧多杆融合计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_multi_poles_det_out = 18;</code>
       */
      public long getTimestampOfMultiPolesDetOut() {
        return timestampOfMultiPolesDetOut_;
      }
      /**
       * <pre>
       *单杆融合结果进入路侧多杆融合计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_multi_poles_det_out = 18;</code>
       */
      public Builder setTimestampOfMultiPolesDetOut(long value) {
        
        timestampOfMultiPolesDetOut_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *单杆融合结果进入路侧多杆融合计算应用的时间戳
       * </pre>
       *
       * <code>uint64 timestamp_of_multi_poles_det_out = 18;</code>
       */
      public Builder clearTimestampOfMultiPolesDetOut() {
        
        timestampOfMultiPolesDetOut_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:road.data.proto.RsPerceptionInfo)
    }

    // @@protoc_insertion_point(class_scope:road.data.proto.RsPerceptionInfo)
    private static final RsPerceptionInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new RsPerceptionInfo();
    }

    public static RsPerceptionInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RsPerceptionInfo>
        PARSER = new com.google.protobuf.AbstractParser<RsPerceptionInfo>() {
      public RsPerceptionInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RsPerceptionInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RsPerceptionInfo> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<RsPerceptionInfo> getParserForType() {
      return PARSER;
    }

    public RsPerceptionInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PerceptionObjectOrBuilder extends
      // @@protoc_insertion_point(interface_extends:road.data.proto.PerceptionObject)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string uuid = 1;</code>
     */
    String getUuid();
    /**
     * <code>string uuid = 1;</code>
     */
    com.google.protobuf.ByteString
        getUuidBytes();

    /**
     * <pre>
     *每个对象在本数据中的顺序号
     * </pre>
     *
     * <code>uint32 obj_id = 2;</code>
     */
    int getObjId();

    /**
     * <pre>
     *0：行人，1：自行车，2：乘用车，3：摩托车，4：特殊用车辆，5：公交车，6：有轨道车，7：卡车，8：三轮车，9：交通信号灯
     *10：交通标识，15：动物，60：路障，61：交通锥，254：其它类型，255：未获取，101:骑行者，102:观光车
     *103：施工牌，104：施工堆，105：水马围栏，106：事故三角板，107：抛洒物
     * </pre>
     *
     * <code>uint32 type = 3;</code>
     */
    int getType();

    /**
     * <pre>
     *0：静止，1：运动
     * </pre>
     *
     * <code>uint32 status = 4;</code>
     */
    int getStatus();

    /**
     * <pre>
     *单位：m
     * </pre>
     *
     * <code>float len = 5;</code>
     */
    float getLen();

    /**
     * <pre>
     *单位：m
     * </pre>
     *
     * <code>float width = 6;</code>
     */
    float getWidth();

    /**
     * <pre>
     *单位：m
     * </pre>
     *
     * <code>float height = 7;</code>
     */
    float getHeight();

    /**
     * <code>double longitude = 8;</code>
     */
    double getLongitude();

    /**
     * <code>double latitude = 9;</code>
     */
    double getLatitude();

    /**
     * <pre>
     *单位：m
     * </pre>
     *
     * <code>float loc_east = 10;</code>
     */
    float getLocEast();

    /**
     * <pre>
     *单位：m
     * </pre>
     *
     * <code>float loc_north = 11;</code>
     */
    float getLocNorth();

    /**
     * <pre>
     *[0..255]，0xFF表示无效，定义应符合附录J的规定。
     * </pre>
     *
     * <code>uint32 pos_confidence = 12;</code>
     */
    int getPosConfidence();

    /**
     * <pre>
     *高程（海拔）
     * </pre>
     *
     * <code>double elevation = 13;</code>
     */
    double getElevation();

    /**
     * <pre>
     *高程精度
     * </pre>
     *
     * <code>uint32 elev_confidence = 14;</code>
     */
    int getElevConfidence();

    /**
     * <pre>
     *单位：m/s
     * </pre>
     *
     * <code>float speed = 15;</code>
     */
    float getSpeed();

    /**
     * <pre>
     *速度精度等级
     * </pre>
     *
     * <code>uint32 speed_confidence = 16;</code>
     */
    int getSpeedConfidence();

    /**
     * <pre>
     *单位：m/s
     * </pre>
     *
     * <code>float speed_east = 17;</code>
     */
    float getSpeedEast();

    /**
     * <pre>
     *东西向速度精度等级
     * </pre>
     *
     * <code>uint32 speed_east_confidence = 18;</code>
     */
    int getSpeedEastConfidence();

    /**
     * <pre>
     *单位：m/s
     * </pre>
     *
     * <code>float speed_north = 19;</code>
     */
    float getSpeedNorth();

    /**
     * <pre>
     *南北向速度精度等级
     * </pre>
     *
     * <code>uint32 speed_north_confidence = 20;</code>
     */
    int getSpeedNorthConfidence();

    /**
     * <pre>
     *正北方向与运动方向顺时针夹角
     * </pre>
     *
     * <code>float heading = 21;</code>
     */
    float getHeading();

    /**
     * <pre>
     *航向角精度等级
     * </pre>
     *
     * <code>uint32 head_confidence = 22;</code>
     */
    int getHeadConfidence();

    /**
     * <pre>
     *单位：m/s2
     * </pre>
     *
     * <code>float accel_vert = 23;</code>
     */
    float getAccelVert();

    /**
     * <pre>
     *目标纵向加速度置精度等级
     * </pre>
     *
     * <code>uint32 accel_vert_confidence = 24;</code>
     */
    int getAccelVertConfidence();

    /**
     * <pre>
     *目标跟踪时长
     * </pre>
     *
     * <code>uint32 tracked_times = 25;</code>
     */
    int getTrackedTimes();

    /**
     * <pre>
     *目标历史轨迹数量
     * </pre>
     *
     * <code>uint32 hist_loc_num = 26;</code>
     */
    int getHistLocNum();

    /**
     * <pre>
     *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
     */
    java.util.List<HistLoc>
        getHistLocsList();
    /**
     * <pre>
     *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
     */
    HistLoc getHistLocs(int index);
    /**
     * <pre>
     *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
     */
    int getHistLocsCount();
    /**
     * <pre>
     *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
     */
    java.util.List<? extends HistLocOrBuilder>
        getHistLocsOrBuilderList();
    /**
     * <pre>
     *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
     */
    HistLocOrBuilder getHistLocsOrBuilder(
        int index);

    /**
     * <pre>
     *目标预测轨迹数量
     * </pre>
     *
     * <code>uint32 pred_loc_num = 28;</code>
     */
    int getPredLocNum();

    /**
     * <pre>
     *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
     */
    java.util.List<HistLoc>
        getPredLocsList();
    /**
     * <pre>
     *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
     */
    HistLoc getPredLocs(int index);
    /**
     * <pre>
     *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
     */
    int getPredLocsCount();
    /**
     * <pre>
     *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
     */
    java.util.List<? extends HistLocOrBuilder>
        getPredLocsOrBuilderList();
    /**
     * <pre>
     *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
     */
    HistLocOrBuilder getPredLocsOrBuilder(
        int index);

    /**
     * <pre>
     *目标所在车道编号
     * </pre>
     *
     * <code>int32 lane_id = 30;</code>
     */
    int getLaneId();

    /**
     * <pre>
     *0：无效；1：卡尔曼滤波信息；2～255：预留。
     *当值为1时，传输卡尔曼滤波信息字段，其余值均不发送。
     * </pre>
     *
     * <code>uint32 filter_info_type = 31;</code>
     */
    int getFilterInfoType();

    /**
     * <pre>
     *卡尔曼滤波信息
     * </pre>
     *
     * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
     */
    boolean hasFilterInfo();
    /**
     * <pre>
     *卡尔曼滤波信息
     * </pre>
     *
     * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
     */
    FilterInfo getFilterInfo();
    /**
     * <pre>
     *卡尔曼滤波信息
     * </pre>
     *
     * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
     */
    FilterInfoOrBuilder getFilterInfoOrBuilder();

    /**
     * <pre>
     *车牌号字节数
     * </pre>
     *
     * <code>uint32 lenplate_no = 33;</code>
     */
    int getLenplateNo();

    /**
     * <pre>
     *车牌号
     * </pre>
     *
     * <code>string plate_no = 34;</code>
     */
    String getPlateNo();
    /**
     * <pre>
     *车牌号
     * </pre>
     *
     * <code>string plate_no = 34;</code>
     */
    com.google.protobuf.ByteString
        getPlateNoBytes();

    /**
     * <pre>
     *1：大型汽车；2：挂车；3：大型新能源汽车；4：小型汽车；5：小型新能源汽车；6：使馆汽车；7：领馆汽车；8：港澳入出境车；
     * 9：教练汽车；10：警用汽车；11：普通摩托车；12：轻便摩托车；13：使馆摩托车；14：领馆摩托车；15：教练摩托车；
     * 16：警用摩托车；17：低速车；18：临时行驶车；19：临时入境汽车；20：临时入境摩托车；21：拖拉机；22：其他；
     *“0xFE”表示异常，“0xFF”表示无效
     * </pre>
     *
     * <code>uint32 plate_type = 35;</code>
     */
    int getPlateType();

    /**
     * <pre>
     *1：黄；2：蓝；3：黑；4：白；5：绿（农用车）；6：红；7：黄绿；8：渐变绿；20：天（酞）蓝；21：棕黄；22：其他；
     *“0xFE”表示异常，“0xFF”表示无效。
     * </pre>
     *
     * <code>uint32 plate_color = 36;</code>
     */
    int getPlateColor();

    /**
     * <pre>
     *1：白；4：灰；7：黄；10：粉；13：红；16：紫；19：绿；22：蓝；25：棕；28：黑；31：橙；34：青；37：银；40：银白；43：其他；
     *其中，（值+1）表示浅色，（值+2）表示深色。例如：22表示蓝色，（22+1）即23表示浅蓝色，（22+2）即24表示深蓝色。
     *“0xFE”表示异常，“0xFF”表示无效。
     * </pre>
     *
     * <code>uint32 obj_color = 37;</code>
     */
    int getObjColor();

    /**
     * <pre>
     *槽位集合
     * </pre>
     *
     * <code>uint32 device_idx = 38;</code>
     */
    int getDeviceIdx();

    /**
     * <pre>
     *高精地图roadPkId
     * </pre>
     *
     * <code>uint32 road_id = 39;</code>
     */
    int getRoadId();

    /**
     * <pre>
     *到mec距离
     * </pre>
     *
     * <code>float distance = 40;</code>
     */
    float getDistance();

    /**
     * <pre>
     *停车时长，单位:ms
     * </pre>
     *
     * <code>uint32 parking_duration = 41;</code>
     */
    int getParkingDuration();

    /**
     * <pre>
     *杆位号
     * </pre>
     *
     * <code>string pole = 42;</code>
     */
    String getPole();
    /**
     * <pre>
     *杆位号
     * </pre>
     *
     * <code>string pole = 42;</code>
     */
    com.google.protobuf.ByteString
        getPoleBytes();
  }
  /**
   * <pre>
   *感知对象信息
   * </pre>
   *
   * Protobuf type {@code road.data.proto.PerceptionObject}
   */
  public  static final class PerceptionObject extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:road.data.proto.PerceptionObject)
      PerceptionObjectOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PerceptionObject.newBuilder() to construct.
    private PerceptionObject(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PerceptionObject() {
      uuid_ = "";
      objId_ = 0;
      type_ = 0;
      status_ = 0;
      len_ = 0F;
      width_ = 0F;
      height_ = 0F;
      longitude_ = 0D;
      latitude_ = 0D;
      locEast_ = 0F;
      locNorth_ = 0F;
      posConfidence_ = 0;
      elevation_ = 0D;
      elevConfidence_ = 0;
      speed_ = 0F;
      speedConfidence_ = 0;
      speedEast_ = 0F;
      speedEastConfidence_ = 0;
      speedNorth_ = 0F;
      speedNorthConfidence_ = 0;
      heading_ = 0F;
      headConfidence_ = 0;
      accelVert_ = 0F;
      accelVertConfidence_ = 0;
      trackedTimes_ = 0;
      histLocNum_ = 0;
      histLocs_ = java.util.Collections.emptyList();
      predLocNum_ = 0;
      predLocs_ = java.util.Collections.emptyList();
      laneId_ = 0;
      filterInfoType_ = 0;
      lenplateNo_ = 0;
      plateNo_ = "";
      plateType_ = 0;
      plateColor_ = 0;
      objColor_ = 0;
      deviceIdx_ = 0;
      roadId_ = 0;
      distance_ = 0F;
      parkingDuration_ = 0;
      pole_ = "";
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PerceptionObject(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      int mutable_bitField1_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              String s = input.readStringRequireUtf8();

              uuid_ = s;
              break;
            }
            case 16: {

              objId_ = input.readUInt32();
              break;
            }
            case 24: {

              type_ = input.readUInt32();
              break;
            }
            case 32: {

              status_ = input.readUInt32();
              break;
            }
            case 45: {

              len_ = input.readFloat();
              break;
            }
            case 53: {

              width_ = input.readFloat();
              break;
            }
            case 61: {

              height_ = input.readFloat();
              break;
            }
            case 65: {

              longitude_ = input.readDouble();
              break;
            }
            case 73: {

              latitude_ = input.readDouble();
              break;
            }
            case 85: {

              locEast_ = input.readFloat();
              break;
            }
            case 93: {

              locNorth_ = input.readFloat();
              break;
            }
            case 96: {

              posConfidence_ = input.readUInt32();
              break;
            }
            case 105: {

              elevation_ = input.readDouble();
              break;
            }
            case 112: {

              elevConfidence_ = input.readUInt32();
              break;
            }
            case 125: {

              speed_ = input.readFloat();
              break;
            }
            case 128: {

              speedConfidence_ = input.readUInt32();
              break;
            }
            case 141: {

              speedEast_ = input.readFloat();
              break;
            }
            case 144: {

              speedEastConfidence_ = input.readUInt32();
              break;
            }
            case 157: {

              speedNorth_ = input.readFloat();
              break;
            }
            case 160: {

              speedNorthConfidence_ = input.readUInt32();
              break;
            }
            case 173: {

              heading_ = input.readFloat();
              break;
            }
            case 176: {

              headConfidence_ = input.readUInt32();
              break;
            }
            case 189: {

              accelVert_ = input.readFloat();
              break;
            }
            case 192: {

              accelVertConfidence_ = input.readUInt32();
              break;
            }
            case 200: {

              trackedTimes_ = input.readUInt32();
              break;
            }
            case 208: {

              histLocNum_ = input.readUInt32();
              break;
            }
            case 218: {
              if (!((mutable_bitField0_ & 0x04000000) == 0x04000000)) {
                histLocs_ = new java.util.ArrayList<HistLoc>();
                mutable_bitField0_ |= 0x04000000;
              }
              histLocs_.add(
                  input.readMessage(HistLoc.parser(), extensionRegistry));
              break;
            }
            case 224: {

              predLocNum_ = input.readUInt32();
              break;
            }
            case 234: {
              if (!((mutable_bitField0_ & 0x10000000) == 0x10000000)) {
                predLocs_ = new java.util.ArrayList<HistLoc>();
                mutable_bitField0_ |= 0x10000000;
              }
              predLocs_.add(
                  input.readMessage(HistLoc.parser(), extensionRegistry));
              break;
            }
            case 240: {

              laneId_ = input.readInt32();
              break;
            }
            case 248: {

              filterInfoType_ = input.readUInt32();
              break;
            }
            case 258: {
              FilterInfo.Builder subBuilder = null;
              if (filterInfo_ != null) {
                subBuilder = filterInfo_.toBuilder();
              }
              filterInfo_ = input.readMessage(FilterInfo.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(filterInfo_);
                filterInfo_ = subBuilder.buildPartial();
              }

              break;
            }
            case 264: {

              lenplateNo_ = input.readUInt32();
              break;
            }
            case 274: {
              String s = input.readStringRequireUtf8();

              plateNo_ = s;
              break;
            }
            case 280: {

              plateType_ = input.readUInt32();
              break;
            }
            case 288: {

              plateColor_ = input.readUInt32();
              break;
            }
            case 296: {

              objColor_ = input.readUInt32();
              break;
            }
            case 304: {

              deviceIdx_ = input.readUInt32();
              break;
            }
            case 312: {

              roadId_ = input.readUInt32();
              break;
            }
            case 325: {

              distance_ = input.readFloat();
              break;
            }
            case 328: {

              parkingDuration_ = input.readUInt32();
              break;
            }
            case 338: {
              String s = input.readStringRequireUtf8();

              pole_ = s;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x04000000) == 0x04000000)) {
          histLocs_ = java.util.Collections.unmodifiableList(histLocs_);
        }
        if (((mutable_bitField0_ & 0x10000000) == 0x10000000)) {
          predLocs_ = java.util.Collections.unmodifiableList(predLocs_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return Percep.internal_static_road_data_proto_PerceptionObject_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return Percep.internal_static_road_data_proto_PerceptionObject_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              PerceptionObject.class, Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    public static final int UUID_FIELD_NUMBER = 1;
    private volatile Object uuid_;
    /**
     * <code>string uuid = 1;</code>
     */
    public String getUuid() {
      Object ref = uuid_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        uuid_ = s;
        return s;
      }
    }
    /**
     * <code>string uuid = 1;</code>
     */
    public com.google.protobuf.ByteString
        getUuidBytes() {
      Object ref = uuid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        uuid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OBJ_ID_FIELD_NUMBER = 2;
    private int objId_;
    /**
     * <pre>
     *每个对象在本数据中的顺序号
     * </pre>
     *
     * <code>uint32 obj_id = 2;</code>
     */
    public int getObjId() {
      return objId_;
    }

    public static final int TYPE_FIELD_NUMBER = 3;
    private int type_;
    /**
     * <pre>
     *0：行人，1：自行车，2：乘用车，3：摩托车，4：特殊用车辆，5：公交车，6：有轨道车，7：卡车，8：三轮车，9：交通信号灯
     *10：交通标识，15：动物，60：路障，61：交通锥，254：其它类型，255：未获取，101:骑行者，102:观光车
     *103：施工牌，104：施工堆，105：水马围栏，106：事故三角板，107：抛洒物
     * </pre>
     *
     * <code>uint32 type = 3;</code>
     */
    public int getType() {
      return type_;
    }

    public static final int STATUS_FIELD_NUMBER = 4;
    private int status_;
    /**
     * <pre>
     *0：静止，1：运动
     * </pre>
     *
     * <code>uint32 status = 4;</code>
     */
    public int getStatus() {
      return status_;
    }

    public static final int LEN_FIELD_NUMBER = 5;
    private float len_;
    /**
     * <pre>
     *单位：m
     * </pre>
     *
     * <code>float len = 5;</code>
     */
    public float getLen() {
      return len_;
    }

    public static final int WIDTH_FIELD_NUMBER = 6;
    private float width_;
    /**
     * <pre>
     *单位：m
     * </pre>
     *
     * <code>float width = 6;</code>
     */
    public float getWidth() {
      return width_;
    }

    public static final int HEIGHT_FIELD_NUMBER = 7;
    private float height_;
    /**
     * <pre>
     *单位：m
     * </pre>
     *
     * <code>float height = 7;</code>
     */
    public float getHeight() {
      return height_;
    }

    public static final int LONGITUDE_FIELD_NUMBER = 8;
    private double longitude_;
    /**
     * <code>double longitude = 8;</code>
     */
    public double getLongitude() {
      return longitude_;
    }

    public static final int LATITUDE_FIELD_NUMBER = 9;
    private double latitude_;
    /**
     * <code>double latitude = 9;</code>
     */
    public double getLatitude() {
      return latitude_;
    }

    public static final int LOC_EAST_FIELD_NUMBER = 10;
    private float locEast_;
    /**
     * <pre>
     *单位：m
     * </pre>
     *
     * <code>float loc_east = 10;</code>
     */
    public float getLocEast() {
      return locEast_;
    }

    public static final int LOC_NORTH_FIELD_NUMBER = 11;
    private float locNorth_;
    /**
     * <pre>
     *单位：m
     * </pre>
     *
     * <code>float loc_north = 11;</code>
     */
    public float getLocNorth() {
      return locNorth_;
    }

    public static final int POS_CONFIDENCE_FIELD_NUMBER = 12;
    private int posConfidence_;
    /**
     * <pre>
     *[0..255]，0xFF表示无效，定义应符合附录J的规定。
     * </pre>
     *
     * <code>uint32 pos_confidence = 12;</code>
     */
    public int getPosConfidence() {
      return posConfidence_;
    }

    public static final int ELEVATION_FIELD_NUMBER = 13;
    private double elevation_;
    /**
     * <pre>
     *高程（海拔）
     * </pre>
     *
     * <code>double elevation = 13;</code>
     */
    public double getElevation() {
      return elevation_;
    }

    public static final int ELEV_CONFIDENCE_FIELD_NUMBER = 14;
    private int elevConfidence_;
    /**
     * <pre>
     *高程精度
     * </pre>
     *
     * <code>uint32 elev_confidence = 14;</code>
     */
    public int getElevConfidence() {
      return elevConfidence_;
    }

    public static final int SPEED_FIELD_NUMBER = 15;
    private float speed_;
    /**
     * <pre>
     *单位：m/s
     * </pre>
     *
     * <code>float speed = 15;</code>
     */
    public float getSpeed() {
      return speed_;
    }

    public static final int SPEED_CONFIDENCE_FIELD_NUMBER = 16;
    private int speedConfidence_;
    /**
     * <pre>
     *速度精度等级
     * </pre>
     *
     * <code>uint32 speed_confidence = 16;</code>
     */
    public int getSpeedConfidence() {
      return speedConfidence_;
    }

    public static final int SPEED_EAST_FIELD_NUMBER = 17;
    private float speedEast_;
    /**
     * <pre>
     *单位：m/s
     * </pre>
     *
     * <code>float speed_east = 17;</code>
     */
    public float getSpeedEast() {
      return speedEast_;
    }

    public static final int SPEED_EAST_CONFIDENCE_FIELD_NUMBER = 18;
    private int speedEastConfidence_;
    /**
     * <pre>
     *东西向速度精度等级
     * </pre>
     *
     * <code>uint32 speed_east_confidence = 18;</code>
     */
    public int getSpeedEastConfidence() {
      return speedEastConfidence_;
    }

    public static final int SPEED_NORTH_FIELD_NUMBER = 19;
    private float speedNorth_;
    /**
     * <pre>
     *单位：m/s
     * </pre>
     *
     * <code>float speed_north = 19;</code>
     */
    public float getSpeedNorth() {
      return speedNorth_;
    }

    public static final int SPEED_NORTH_CONFIDENCE_FIELD_NUMBER = 20;
    private int speedNorthConfidence_;
    /**
     * <pre>
     *南北向速度精度等级
     * </pre>
     *
     * <code>uint32 speed_north_confidence = 20;</code>
     */
    public int getSpeedNorthConfidence() {
      return speedNorthConfidence_;
    }

    public static final int HEADING_FIELD_NUMBER = 21;
    private float heading_;
    /**
     * <pre>
     *正北方向与运动方向顺时针夹角
     * </pre>
     *
     * <code>float heading = 21;</code>
     */
    public float getHeading() {
      return heading_;
    }

    public static final int HEAD_CONFIDENCE_FIELD_NUMBER = 22;
    private int headConfidence_;
    /**
     * <pre>
     *航向角精度等级
     * </pre>
     *
     * <code>uint32 head_confidence = 22;</code>
     */
    public int getHeadConfidence() {
      return headConfidence_;
    }

    public static final int ACCEL_VERT_FIELD_NUMBER = 23;
    private float accelVert_;
    /**
     * <pre>
     *单位：m/s2
     * </pre>
     *
     * <code>float accel_vert = 23;</code>
     */
    public float getAccelVert() {
      return accelVert_;
    }

    public static final int ACCEL_VERT_CONFIDENCE_FIELD_NUMBER = 24;
    private int accelVertConfidence_;
    /**
     * <pre>
     *目标纵向加速度置精度等级
     * </pre>
     *
     * <code>uint32 accel_vert_confidence = 24;</code>
     */
    public int getAccelVertConfidence() {
      return accelVertConfidence_;
    }

    public static final int TRACKED_TIMES_FIELD_NUMBER = 25;
    private int trackedTimes_;
    /**
     * <pre>
     *目标跟踪时长
     * </pre>
     *
     * <code>uint32 tracked_times = 25;</code>
     */
    public int getTrackedTimes() {
      return trackedTimes_;
    }

    public static final int HIST_LOC_NUM_FIELD_NUMBER = 26;
    private int histLocNum_;
    /**
     * <pre>
     *目标历史轨迹数量
     * </pre>
     *
     * <code>uint32 hist_loc_num = 26;</code>
     */
    public int getHistLocNum() {
      return histLocNum_;
    }

    public static final int HIST_LOCS_FIELD_NUMBER = 27;
    private java.util.List<HistLoc> histLocs_;
    /**
     * <pre>
     *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
     */
    public java.util.List<HistLoc> getHistLocsList() {
      return histLocs_;
    }
    /**
     * <pre>
     *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
     */
    public java.util.List<? extends HistLocOrBuilder>
        getHistLocsOrBuilderList() {
      return histLocs_;
    }
    /**
     * <pre>
     *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
     */
    public int getHistLocsCount() {
      return histLocs_.size();
    }
    /**
     * <pre>
     *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
     */
    public HistLoc getHistLocs(int index) {
      return histLocs_.get(index);
    }
    /**
     * <pre>
     *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
     */
    public HistLocOrBuilder getHistLocsOrBuilder(
        int index) {
      return histLocs_.get(index);
    }

    public static final int PRED_LOC_NUM_FIELD_NUMBER = 28;
    private int predLocNum_;
    /**
     * <pre>
     *目标预测轨迹数量
     * </pre>
     *
     * <code>uint32 pred_loc_num = 28;</code>
     */
    public int getPredLocNum() {
      return predLocNum_;
    }

    public static final int PRED_LOCS_FIELD_NUMBER = 29;
    private java.util.List<HistLoc> predLocs_;
    /**
     * <pre>
     *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
     */
    public java.util.List<HistLoc> getPredLocsList() {
      return predLocs_;
    }
    /**
     * <pre>
     *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
     */
    public java.util.List<? extends HistLocOrBuilder>
        getPredLocsOrBuilderList() {
      return predLocs_;
    }
    /**
     * <pre>
     *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
     */
    public int getPredLocsCount() {
      return predLocs_.size();
    }
    /**
     * <pre>
     *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
     */
    public HistLoc getPredLocs(int index) {
      return predLocs_.get(index);
    }
    /**
     * <pre>
     *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
     *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
     */
    public HistLocOrBuilder getPredLocsOrBuilder(
        int index) {
      return predLocs_.get(index);
    }

    public static final int LANE_ID_FIELD_NUMBER = 30;
    private int laneId_;
    /**
     * <pre>
     *目标所在车道编号
     * </pre>
     *
     * <code>int32 lane_id = 30;</code>
     */
    public int getLaneId() {
      return laneId_;
    }

    public static final int FILTER_INFO_TYPE_FIELD_NUMBER = 31;
    private int filterInfoType_;
    /**
     * <pre>
     *0：无效；1：卡尔曼滤波信息；2～255：预留。
     *当值为1时，传输卡尔曼滤波信息字段，其余值均不发送。
     * </pre>
     *
     * <code>uint32 filter_info_type = 31;</code>
     */
    public int getFilterInfoType() {
      return filterInfoType_;
    }

    public static final int FILTER_INFO_FIELD_NUMBER = 32;
    private FilterInfo filterInfo_;
    /**
     * <pre>
     *卡尔曼滤波信息
     * </pre>
     *
     * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
     */
    public boolean hasFilterInfo() {
      return filterInfo_ != null;
    }
    /**
     * <pre>
     *卡尔曼滤波信息
     * </pre>
     *
     * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
     */
    public FilterInfo getFilterInfo() {
      return filterInfo_ == null ? FilterInfo.getDefaultInstance() : filterInfo_;
    }
    /**
     * <pre>
     *卡尔曼滤波信息
     * </pre>
     *
     * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
     */
    public FilterInfoOrBuilder getFilterInfoOrBuilder() {
      return getFilterInfo();
    }

    public static final int LENPLATE_NO_FIELD_NUMBER = 33;
    private int lenplateNo_;
    /**
     * <pre>
     *车牌号字节数
     * </pre>
     *
     * <code>uint32 lenplate_no = 33;</code>
     */
    public int getLenplateNo() {
      return lenplateNo_;
    }

    public static final int PLATE_NO_FIELD_NUMBER = 34;
    private volatile Object plateNo_;
    /**
     * <pre>
     *车牌号
     * </pre>
     *
     * <code>string plate_no = 34;</code>
     */
    public String getPlateNo() {
      Object ref = plateNo_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        plateNo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *车牌号
     * </pre>
     *
     * <code>string plate_no = 34;</code>
     */
    public com.google.protobuf.ByteString
        getPlateNoBytes() {
      Object ref = plateNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        plateNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PLATE_TYPE_FIELD_NUMBER = 35;
    private int plateType_;
    /**
     * <pre>
     *1：大型汽车；2：挂车；3：大型新能源汽车；4：小型汽车；5：小型新能源汽车；6：使馆汽车；7：领馆汽车；8：港澳入出境车；
     * 9：教练汽车；10：警用汽车；11：普通摩托车；12：轻便摩托车；13：使馆摩托车；14：领馆摩托车；15：教练摩托车；
     * 16：警用摩托车；17：低速车；18：临时行驶车；19：临时入境汽车；20：临时入境摩托车；21：拖拉机；22：其他；
     *“0xFE”表示异常，“0xFF”表示无效
     * </pre>
     *
     * <code>uint32 plate_type = 35;</code>
     */
    public int getPlateType() {
      return plateType_;
    }

    public static final int PLATE_COLOR_FIELD_NUMBER = 36;
    private int plateColor_;
    /**
     * <pre>
     *1：黄；2：蓝；3：黑；4：白；5：绿（农用车）；6：红；7：黄绿；8：渐变绿；20：天（酞）蓝；21：棕黄；22：其他；
     *“0xFE”表示异常，“0xFF”表示无效。
     * </pre>
     *
     * <code>uint32 plate_color = 36;</code>
     */
    public int getPlateColor() {
      return plateColor_;
    }

    public static final int OBJ_COLOR_FIELD_NUMBER = 37;
    private int objColor_;
    /**
     * <pre>
     *1：白；4：灰；7：黄；10：粉；13：红；16：紫；19：绿；22：蓝；25：棕；28：黑；31：橙；34：青；37：银；40：银白；43：其他；
     *其中，（值+1）表示浅色，（值+2）表示深色。例如：22表示蓝色，（22+1）即23表示浅蓝色，（22+2）即24表示深蓝色。
     *“0xFE”表示异常，“0xFF”表示无效。
     * </pre>
     *
     * <code>uint32 obj_color = 37;</code>
     */
    public int getObjColor() {
      return objColor_;
    }

    public static final int DEVICE_IDX_FIELD_NUMBER = 38;
    private int deviceIdx_;
    /**
     * <pre>
     *槽位集合
     * </pre>
     *
     * <code>uint32 device_idx = 38;</code>
     */
    public int getDeviceIdx() {
      return deviceIdx_;
    }

    public static final int ROAD_ID_FIELD_NUMBER = 39;
    private int roadId_;
    /**
     * <pre>
     *高精地图roadPkId
     * </pre>
     *
     * <code>uint32 road_id = 39;</code>
     */
    public int getRoadId() {
      return roadId_;
    }

    public static final int DISTANCE_FIELD_NUMBER = 40;
    private float distance_;
    /**
     * <pre>
     *到mec距离
     * </pre>
     *
     * <code>float distance = 40;</code>
     */
    public float getDistance() {
      return distance_;
    }

    public static final int PARKING_DURATION_FIELD_NUMBER = 41;
    private int parkingDuration_;
    /**
     * <pre>
     *停车时长，单位:ms
     * </pre>
     *
     * <code>uint32 parking_duration = 41;</code>
     */
    public int getParkingDuration() {
      return parkingDuration_;
    }

    public static final int POLE_FIELD_NUMBER = 42;
    private volatile Object pole_;
    /**
     * <pre>
     *杆位号
     * </pre>
     *
     * <code>string pole = 42;</code>
     */
    public String getPole() {
      Object ref = pole_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        pole_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *杆位号
     * </pre>
     *
     * <code>string pole = 42;</code>
     */
    public com.google.protobuf.ByteString
        getPoleBytes() {
      Object ref = pole_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        pole_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getUuidBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, uuid_);
      }
      if (objId_ != 0) {
        output.writeUInt32(2, objId_);
      }
      if (type_ != 0) {
        output.writeUInt32(3, type_);
      }
      if (status_ != 0) {
        output.writeUInt32(4, status_);
      }
      if (len_ != 0F) {
        output.writeFloat(5, len_);
      }
      if (width_ != 0F) {
        output.writeFloat(6, width_);
      }
      if (height_ != 0F) {
        output.writeFloat(7, height_);
      }
      if (longitude_ != 0D) {
        output.writeDouble(8, longitude_);
      }
      if (latitude_ != 0D) {
        output.writeDouble(9, latitude_);
      }
      if (locEast_ != 0F) {
        output.writeFloat(10, locEast_);
      }
      if (locNorth_ != 0F) {
        output.writeFloat(11, locNorth_);
      }
      if (posConfidence_ != 0) {
        output.writeUInt32(12, posConfidence_);
      }
      if (elevation_ != 0D) {
        output.writeDouble(13, elevation_);
      }
      if (elevConfidence_ != 0) {
        output.writeUInt32(14, elevConfidence_);
      }
      if (speed_ != 0F) {
        output.writeFloat(15, speed_);
      }
      if (speedConfidence_ != 0) {
        output.writeUInt32(16, speedConfidence_);
      }
      if (speedEast_ != 0F) {
        output.writeFloat(17, speedEast_);
      }
      if (speedEastConfidence_ != 0) {
        output.writeUInt32(18, speedEastConfidence_);
      }
      if (speedNorth_ != 0F) {
        output.writeFloat(19, speedNorth_);
      }
      if (speedNorthConfidence_ != 0) {
        output.writeUInt32(20, speedNorthConfidence_);
      }
      if (heading_ != 0F) {
        output.writeFloat(21, heading_);
      }
      if (headConfidence_ != 0) {
        output.writeUInt32(22, headConfidence_);
      }
      if (accelVert_ != 0F) {
        output.writeFloat(23, accelVert_);
      }
      if (accelVertConfidence_ != 0) {
        output.writeUInt32(24, accelVertConfidence_);
      }
      if (trackedTimes_ != 0) {
        output.writeUInt32(25, trackedTimes_);
      }
      if (histLocNum_ != 0) {
        output.writeUInt32(26, histLocNum_);
      }
      for (int i = 0; i < histLocs_.size(); i++) {
        output.writeMessage(27, histLocs_.get(i));
      }
      if (predLocNum_ != 0) {
        output.writeUInt32(28, predLocNum_);
      }
      for (int i = 0; i < predLocs_.size(); i++) {
        output.writeMessage(29, predLocs_.get(i));
      }
      if (laneId_ != 0) {
        output.writeInt32(30, laneId_);
      }
      if (filterInfoType_ != 0) {
        output.writeUInt32(31, filterInfoType_);
      }
      if (filterInfo_ != null) {
        output.writeMessage(32, getFilterInfo());
      }
      if (lenplateNo_ != 0) {
        output.writeUInt32(33, lenplateNo_);
      }
      if (!getPlateNoBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 34, plateNo_);
      }
      if (plateType_ != 0) {
        output.writeUInt32(35, plateType_);
      }
      if (plateColor_ != 0) {
        output.writeUInt32(36, plateColor_);
      }
      if (objColor_ != 0) {
        output.writeUInt32(37, objColor_);
      }
      if (deviceIdx_ != 0) {
        output.writeUInt32(38, deviceIdx_);
      }
      if (roadId_ != 0) {
        output.writeUInt32(39, roadId_);
      }
      if (distance_ != 0F) {
        output.writeFloat(40, distance_);
      }
      if (parkingDuration_ != 0) {
        output.writeUInt32(41, parkingDuration_);
      }
      if (!getPoleBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 42, pole_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getUuidBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, uuid_);
      }
      if (objId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, objId_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, type_);
      }
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, status_);
      }
      if (len_ != 0F) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(5, len_);
      }
      if (width_ != 0F) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(6, width_);
      }
      if (height_ != 0F) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(7, height_);
      }
      if (longitude_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(8, longitude_);
      }
      if (latitude_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(9, latitude_);
      }
      if (locEast_ != 0F) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(10, locEast_);
      }
      if (locNorth_ != 0F) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(11, locNorth_);
      }
      if (posConfidence_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(12, posConfidence_);
      }
      if (elevation_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(13, elevation_);
      }
      if (elevConfidence_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(14, elevConfidence_);
      }
      if (speed_ != 0F) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(15, speed_);
      }
      if (speedConfidence_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(16, speedConfidence_);
      }
      if (speedEast_ != 0F) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(17, speedEast_);
      }
      if (speedEastConfidence_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, speedEastConfidence_);
      }
      if (speedNorth_ != 0F) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(19, speedNorth_);
      }
      if (speedNorthConfidence_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, speedNorthConfidence_);
      }
      if (heading_ != 0F) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(21, heading_);
      }
      if (headConfidence_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(22, headConfidence_);
      }
      if (accelVert_ != 0F) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(23, accelVert_);
      }
      if (accelVertConfidence_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(24, accelVertConfidence_);
      }
      if (trackedTimes_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(25, trackedTimes_);
      }
      if (histLocNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(26, histLocNum_);
      }
      for (int i = 0; i < histLocs_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(27, histLocs_.get(i));
      }
      if (predLocNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(28, predLocNum_);
      }
      for (int i = 0; i < predLocs_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(29, predLocs_.get(i));
      }
      if (laneId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(30, laneId_);
      }
      if (filterInfoType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(31, filterInfoType_);
      }
      if (filterInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(32, getFilterInfo());
      }
      if (lenplateNo_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(33, lenplateNo_);
      }
      if (!getPlateNoBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(34, plateNo_);
      }
      if (plateType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(35, plateType_);
      }
      if (plateColor_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(36, plateColor_);
      }
      if (objColor_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(37, objColor_);
      }
      if (deviceIdx_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(38, deviceIdx_);
      }
      if (roadId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(39, roadId_);
      }
      if (distance_ != 0F) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(40, distance_);
      }
      if (parkingDuration_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(41, parkingDuration_);
      }
      if (!getPoleBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(42, pole_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof PerceptionObject)) {
        return super.equals(obj);
      }
      PerceptionObject other = (PerceptionObject) obj;

      boolean result = true;
      result = result && getUuid()
          .equals(other.getUuid());
      result = result && (getObjId()
          == other.getObjId());
      result = result && (getType()
          == other.getType());
      result = result && (getStatus()
          == other.getStatus());
      result = result && (
          Float.floatToIntBits(getLen())
          == Float.floatToIntBits(
              other.getLen()));
      result = result && (
          Float.floatToIntBits(getWidth())
          == Float.floatToIntBits(
              other.getWidth()));
      result = result && (
          Float.floatToIntBits(getHeight())
          == Float.floatToIntBits(
              other.getHeight()));
      result = result && (
          Double.doubleToLongBits(getLongitude())
          == Double.doubleToLongBits(
              other.getLongitude()));
      result = result && (
          Double.doubleToLongBits(getLatitude())
          == Double.doubleToLongBits(
              other.getLatitude()));
      result = result && (
          Float.floatToIntBits(getLocEast())
          == Float.floatToIntBits(
              other.getLocEast()));
      result = result && (
          Float.floatToIntBits(getLocNorth())
          == Float.floatToIntBits(
              other.getLocNorth()));
      result = result && (getPosConfidence()
          == other.getPosConfidence());
      result = result && (
          Double.doubleToLongBits(getElevation())
          == Double.doubleToLongBits(
              other.getElevation()));
      result = result && (getElevConfidence()
          == other.getElevConfidence());
      result = result && (
          Float.floatToIntBits(getSpeed())
          == Float.floatToIntBits(
              other.getSpeed()));
      result = result && (getSpeedConfidence()
          == other.getSpeedConfidence());
      result = result && (
          Float.floatToIntBits(getSpeedEast())
          == Float.floatToIntBits(
              other.getSpeedEast()));
      result = result && (getSpeedEastConfidence()
          == other.getSpeedEastConfidence());
      result = result && (
          Float.floatToIntBits(getSpeedNorth())
          == Float.floatToIntBits(
              other.getSpeedNorth()));
      result = result && (getSpeedNorthConfidence()
          == other.getSpeedNorthConfidence());
      result = result && (
          Float.floatToIntBits(getHeading())
          == Float.floatToIntBits(
              other.getHeading()));
      result = result && (getHeadConfidence()
          == other.getHeadConfidence());
      result = result && (
          Float.floatToIntBits(getAccelVert())
          == Float.floatToIntBits(
              other.getAccelVert()));
      result = result && (getAccelVertConfidence()
          == other.getAccelVertConfidence());
      result = result && (getTrackedTimes()
          == other.getTrackedTimes());
      result = result && (getHistLocNum()
          == other.getHistLocNum());
      result = result && getHistLocsList()
          .equals(other.getHistLocsList());
      result = result && (getPredLocNum()
          == other.getPredLocNum());
      result = result && getPredLocsList()
          .equals(other.getPredLocsList());
      result = result && (getLaneId()
          == other.getLaneId());
      result = result && (getFilterInfoType()
          == other.getFilterInfoType());
      result = result && (hasFilterInfo() == other.hasFilterInfo());
      if (hasFilterInfo()) {
        result = result && getFilterInfo()
            .equals(other.getFilterInfo());
      }
      result = result && (getLenplateNo()
          == other.getLenplateNo());
      result = result && getPlateNo()
          .equals(other.getPlateNo());
      result = result && (getPlateType()
          == other.getPlateType());
      result = result && (getPlateColor()
          == other.getPlateColor());
      result = result && (getObjColor()
          == other.getObjColor());
      result = result && (getDeviceIdx()
          == other.getDeviceIdx());
      result = result && (getRoadId()
          == other.getRoadId());
      result = result && (
          Float.floatToIntBits(getDistance())
          == Float.floatToIntBits(
              other.getDistance()));
      result = result && (getParkingDuration()
          == other.getParkingDuration());
      result = result && getPole()
          .equals(other.getPole());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + UUID_FIELD_NUMBER;
      hash = (53 * hash) + getUuid().hashCode();
      hash = (37 * hash) + OBJ_ID_FIELD_NUMBER;
      hash = (53 * hash) + getObjId();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (37 * hash) + LEN_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getLen());
      hash = (37 * hash) + WIDTH_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getWidth());
      hash = (37 * hash) + HEIGHT_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getHeight());
      hash = (37 * hash) + LONGITUDE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          Double.doubleToLongBits(getLongitude()));
      hash = (37 * hash) + LATITUDE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          Double.doubleToLongBits(getLatitude()));
      hash = (37 * hash) + LOC_EAST_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getLocEast());
      hash = (37 * hash) + LOC_NORTH_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getLocNorth());
      hash = (37 * hash) + POS_CONFIDENCE_FIELD_NUMBER;
      hash = (53 * hash) + getPosConfidence();
      hash = (37 * hash) + ELEVATION_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          Double.doubleToLongBits(getElevation()));
      hash = (37 * hash) + ELEV_CONFIDENCE_FIELD_NUMBER;
      hash = (53 * hash) + getElevConfidence();
      hash = (37 * hash) + SPEED_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getSpeed());
      hash = (37 * hash) + SPEED_CONFIDENCE_FIELD_NUMBER;
      hash = (53 * hash) + getSpeedConfidence();
      hash = (37 * hash) + SPEED_EAST_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getSpeedEast());
      hash = (37 * hash) + SPEED_EAST_CONFIDENCE_FIELD_NUMBER;
      hash = (53 * hash) + getSpeedEastConfidence();
      hash = (37 * hash) + SPEED_NORTH_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getSpeedNorth());
      hash = (37 * hash) + SPEED_NORTH_CONFIDENCE_FIELD_NUMBER;
      hash = (53 * hash) + getSpeedNorthConfidence();
      hash = (37 * hash) + HEADING_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getHeading());
      hash = (37 * hash) + HEAD_CONFIDENCE_FIELD_NUMBER;
      hash = (53 * hash) + getHeadConfidence();
      hash = (37 * hash) + ACCEL_VERT_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getAccelVert());
      hash = (37 * hash) + ACCEL_VERT_CONFIDENCE_FIELD_NUMBER;
      hash = (53 * hash) + getAccelVertConfidence();
      hash = (37 * hash) + TRACKED_TIMES_FIELD_NUMBER;
      hash = (53 * hash) + getTrackedTimes();
      hash = (37 * hash) + HIST_LOC_NUM_FIELD_NUMBER;
      hash = (53 * hash) + getHistLocNum();
      if (getHistLocsCount() > 0) {
        hash = (37 * hash) + HIST_LOCS_FIELD_NUMBER;
        hash = (53 * hash) + getHistLocsList().hashCode();
      }
      hash = (37 * hash) + PRED_LOC_NUM_FIELD_NUMBER;
      hash = (53 * hash) + getPredLocNum();
      if (getPredLocsCount() > 0) {
        hash = (37 * hash) + PRED_LOCS_FIELD_NUMBER;
        hash = (53 * hash) + getPredLocsList().hashCode();
      }
      hash = (37 * hash) + LANE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getLaneId();
      hash = (37 * hash) + FILTER_INFO_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getFilterInfoType();
      if (hasFilterInfo()) {
        hash = (37 * hash) + FILTER_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getFilterInfo().hashCode();
      }
      hash = (37 * hash) + LENPLATE_NO_FIELD_NUMBER;
      hash = (53 * hash) + getLenplateNo();
      hash = (37 * hash) + PLATE_NO_FIELD_NUMBER;
      hash = (53 * hash) + getPlateNo().hashCode();
      hash = (37 * hash) + PLATE_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getPlateType();
      hash = (37 * hash) + PLATE_COLOR_FIELD_NUMBER;
      hash = (53 * hash) + getPlateColor();
      hash = (37 * hash) + OBJ_COLOR_FIELD_NUMBER;
      hash = (53 * hash) + getObjColor();
      hash = (37 * hash) + DEVICE_IDX_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceIdx();
      hash = (37 * hash) + ROAD_ID_FIELD_NUMBER;
      hash = (53 * hash) + getRoadId();
      hash = (37 * hash) + DISTANCE_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getDistance());
      hash = (37 * hash) + PARKING_DURATION_FIELD_NUMBER;
      hash = (53 * hash) + getParkingDuration();
      hash = (37 * hash) + POLE_FIELD_NUMBER;
      hash = (53 * hash) + getPole().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static PerceptionObject parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PerceptionObject parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PerceptionObject parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PerceptionObject parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PerceptionObject parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PerceptionObject parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PerceptionObject parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PerceptionObject parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static PerceptionObject parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static PerceptionObject parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static PerceptionObject parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PerceptionObject parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(PerceptionObject prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *感知对象信息
     * </pre>
     *
     * Protobuf type {@code road.data.proto.PerceptionObject}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:road.data.proto.PerceptionObject)
        PerceptionObjectOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return Percep.internal_static_road_data_proto_PerceptionObject_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return Percep.internal_static_road_data_proto_PerceptionObject_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PerceptionObject.class, Builder.class);
      }

      // Construct using road.data.proto.Percep.PerceptionObject.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getHistLocsFieldBuilder();
          getPredLocsFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        uuid_ = "";

        objId_ = 0;

        type_ = 0;

        status_ = 0;

        len_ = 0F;

        width_ = 0F;

        height_ = 0F;

        longitude_ = 0D;

        latitude_ = 0D;

        locEast_ = 0F;

        locNorth_ = 0F;

        posConfidence_ = 0;

        elevation_ = 0D;

        elevConfidence_ = 0;

        speed_ = 0F;

        speedConfidence_ = 0;

        speedEast_ = 0F;

        speedEastConfidence_ = 0;

        speedNorth_ = 0F;

        speedNorthConfidence_ = 0;

        heading_ = 0F;

        headConfidence_ = 0;

        accelVert_ = 0F;

        accelVertConfidence_ = 0;

        trackedTimes_ = 0;

        histLocNum_ = 0;

        if (histLocsBuilder_ == null) {
          histLocs_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x04000000);
        } else {
          histLocsBuilder_.clear();
        }
        predLocNum_ = 0;

        if (predLocsBuilder_ == null) {
          predLocs_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x10000000);
        } else {
          predLocsBuilder_.clear();
        }
        laneId_ = 0;

        filterInfoType_ = 0;

        if (filterInfoBuilder_ == null) {
          filterInfo_ = null;
        } else {
          filterInfo_ = null;
          filterInfoBuilder_ = null;
        }
        lenplateNo_ = 0;

        plateNo_ = "";

        plateType_ = 0;

        plateColor_ = 0;

        objColor_ = 0;

        deviceIdx_ = 0;

        roadId_ = 0;

        distance_ = 0F;

        parkingDuration_ = 0;

        pole_ = "";

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return Percep.internal_static_road_data_proto_PerceptionObject_descriptor;
      }

      public PerceptionObject getDefaultInstanceForType() {
        return PerceptionObject.getDefaultInstance();
      }

      public PerceptionObject build() {
        PerceptionObject result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public PerceptionObject buildPartial() {
        PerceptionObject result = new PerceptionObject(this);
        int from_bitField0_ = bitField0_;
        int from_bitField1_ = bitField1_;
        int to_bitField0_ = 0;
        int to_bitField1_ = 0;
        result.uuid_ = uuid_;
        result.objId_ = objId_;
        result.type_ = type_;
        result.status_ = status_;
        result.len_ = len_;
        result.width_ = width_;
        result.height_ = height_;
        result.longitude_ = longitude_;
        result.latitude_ = latitude_;
        result.locEast_ = locEast_;
        result.locNorth_ = locNorth_;
        result.posConfidence_ = posConfidence_;
        result.elevation_ = elevation_;
        result.elevConfidence_ = elevConfidence_;
        result.speed_ = speed_;
        result.speedConfidence_ = speedConfidence_;
        result.speedEast_ = speedEast_;
        result.speedEastConfidence_ = speedEastConfidence_;
        result.speedNorth_ = speedNorth_;
        result.speedNorthConfidence_ = speedNorthConfidence_;
        result.heading_ = heading_;
        result.headConfidence_ = headConfidence_;
        result.accelVert_ = accelVert_;
        result.accelVertConfidence_ = accelVertConfidence_;
        result.trackedTimes_ = trackedTimes_;
        result.histLocNum_ = histLocNum_;
        if (histLocsBuilder_ == null) {
          if (((bitField0_ & 0x04000000) == 0x04000000)) {
            histLocs_ = java.util.Collections.unmodifiableList(histLocs_);
            bitField0_ = (bitField0_ & ~0x04000000);
          }
          result.histLocs_ = histLocs_;
        } else {
          result.histLocs_ = histLocsBuilder_.build();
        }
        result.predLocNum_ = predLocNum_;
        if (predLocsBuilder_ == null) {
          if (((bitField0_ & 0x10000000) == 0x10000000)) {
            predLocs_ = java.util.Collections.unmodifiableList(predLocs_);
            bitField0_ = (bitField0_ & ~0x10000000);
          }
          result.predLocs_ = predLocs_;
        } else {
          result.predLocs_ = predLocsBuilder_.build();
        }
        result.laneId_ = laneId_;
        result.filterInfoType_ = filterInfoType_;
        if (filterInfoBuilder_ == null) {
          result.filterInfo_ = filterInfo_;
        } else {
          result.filterInfo_ = filterInfoBuilder_.build();
        }
        result.lenplateNo_ = lenplateNo_;
        result.plateNo_ = plateNo_;
        result.plateType_ = plateType_;
        result.plateColor_ = plateColor_;
        result.objColor_ = objColor_;
        result.deviceIdx_ = deviceIdx_;
        result.roadId_ = roadId_;
        result.distance_ = distance_;
        result.parkingDuration_ = parkingDuration_;
        result.pole_ = pole_;
        result.bitField0_ = to_bitField0_;
        result.bitField1_ = to_bitField1_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof PerceptionObject) {
          return mergeFrom((PerceptionObject)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(PerceptionObject other) {
        if (other == PerceptionObject.getDefaultInstance()) return this;
        if (!other.getUuid().isEmpty()) {
          uuid_ = other.uuid_;
          onChanged();
        }
        if (other.getObjId() != 0) {
          setObjId(other.getObjId());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        if (other.getLen() != 0F) {
          setLen(other.getLen());
        }
        if (other.getWidth() != 0F) {
          setWidth(other.getWidth());
        }
        if (other.getHeight() != 0F) {
          setHeight(other.getHeight());
        }
        if (other.getLongitude() != 0D) {
          setLongitude(other.getLongitude());
        }
        if (other.getLatitude() != 0D) {
          setLatitude(other.getLatitude());
        }
        if (other.getLocEast() != 0F) {
          setLocEast(other.getLocEast());
        }
        if (other.getLocNorth() != 0F) {
          setLocNorth(other.getLocNorth());
        }
        if (other.getPosConfidence() != 0) {
          setPosConfidence(other.getPosConfidence());
        }
        if (other.getElevation() != 0D) {
          setElevation(other.getElevation());
        }
        if (other.getElevConfidence() != 0) {
          setElevConfidence(other.getElevConfidence());
        }
        if (other.getSpeed() != 0F) {
          setSpeed(other.getSpeed());
        }
        if (other.getSpeedConfidence() != 0) {
          setSpeedConfidence(other.getSpeedConfidence());
        }
        if (other.getSpeedEast() != 0F) {
          setSpeedEast(other.getSpeedEast());
        }
        if (other.getSpeedEastConfidence() != 0) {
          setSpeedEastConfidence(other.getSpeedEastConfidence());
        }
        if (other.getSpeedNorth() != 0F) {
          setSpeedNorth(other.getSpeedNorth());
        }
        if (other.getSpeedNorthConfidence() != 0) {
          setSpeedNorthConfidence(other.getSpeedNorthConfidence());
        }
        if (other.getHeading() != 0F) {
          setHeading(other.getHeading());
        }
        if (other.getHeadConfidence() != 0) {
          setHeadConfidence(other.getHeadConfidence());
        }
        if (other.getAccelVert() != 0F) {
          setAccelVert(other.getAccelVert());
        }
        if (other.getAccelVertConfidence() != 0) {
          setAccelVertConfidence(other.getAccelVertConfidence());
        }
        if (other.getTrackedTimes() != 0) {
          setTrackedTimes(other.getTrackedTimes());
        }
        if (other.getHistLocNum() != 0) {
          setHistLocNum(other.getHistLocNum());
        }
        if (histLocsBuilder_ == null) {
          if (!other.histLocs_.isEmpty()) {
            if (histLocs_.isEmpty()) {
              histLocs_ = other.histLocs_;
              bitField0_ = (bitField0_ & ~0x04000000);
            } else {
              ensureHistLocsIsMutable();
              histLocs_.addAll(other.histLocs_);
            }
            onChanged();
          }
        } else {
          if (!other.histLocs_.isEmpty()) {
            if (histLocsBuilder_.isEmpty()) {
              histLocsBuilder_.dispose();
              histLocsBuilder_ = null;
              histLocs_ = other.histLocs_;
              bitField0_ = (bitField0_ & ~0x04000000);
              histLocsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getHistLocsFieldBuilder() : null;
            } else {
              histLocsBuilder_.addAllMessages(other.histLocs_);
            }
          }
        }
        if (other.getPredLocNum() != 0) {
          setPredLocNum(other.getPredLocNum());
        }
        if (predLocsBuilder_ == null) {
          if (!other.predLocs_.isEmpty()) {
            if (predLocs_.isEmpty()) {
              predLocs_ = other.predLocs_;
              bitField0_ = (bitField0_ & ~0x10000000);
            } else {
              ensurePredLocsIsMutable();
              predLocs_.addAll(other.predLocs_);
            }
            onChanged();
          }
        } else {
          if (!other.predLocs_.isEmpty()) {
            if (predLocsBuilder_.isEmpty()) {
              predLocsBuilder_.dispose();
              predLocsBuilder_ = null;
              predLocs_ = other.predLocs_;
              bitField0_ = (bitField0_ & ~0x10000000);
              predLocsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPredLocsFieldBuilder() : null;
            } else {
              predLocsBuilder_.addAllMessages(other.predLocs_);
            }
          }
        }
        if (other.getLaneId() != 0) {
          setLaneId(other.getLaneId());
        }
        if (other.getFilterInfoType() != 0) {
          setFilterInfoType(other.getFilterInfoType());
        }
        if (other.hasFilterInfo()) {
          mergeFilterInfo(other.getFilterInfo());
        }
        if (other.getLenplateNo() != 0) {
          setLenplateNo(other.getLenplateNo());
        }
        if (!other.getPlateNo().isEmpty()) {
          plateNo_ = other.plateNo_;
          onChanged();
        }
        if (other.getPlateType() != 0) {
          setPlateType(other.getPlateType());
        }
        if (other.getPlateColor() != 0) {
          setPlateColor(other.getPlateColor());
        }
        if (other.getObjColor() != 0) {
          setObjColor(other.getObjColor());
        }
        if (other.getDeviceIdx() != 0) {
          setDeviceIdx(other.getDeviceIdx());
        }
        if (other.getRoadId() != 0) {
          setRoadId(other.getRoadId());
        }
        if (other.getDistance() != 0F) {
          setDistance(other.getDistance());
        }
        if (other.getParkingDuration() != 0) {
          setParkingDuration(other.getParkingDuration());
        }
        if (!other.getPole().isEmpty()) {
          pole_ = other.pole_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        PerceptionObject parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (PerceptionObject) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;
      private int bitField1_;

      private Object uuid_ = "";
      /**
       * <code>string uuid = 1;</code>
       */
      public String getUuid() {
        Object ref = uuid_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          uuid_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string uuid = 1;</code>
       */
      public com.google.protobuf.ByteString
          getUuidBytes() {
        Object ref = uuid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          uuid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string uuid = 1;</code>
       */
      public Builder setUuid(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        uuid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string uuid = 1;</code>
       */
      public Builder clearUuid() {
        
        uuid_ = getDefaultInstance().getUuid();
        onChanged();
        return this;
      }
      /**
       * <code>string uuid = 1;</code>
       */
      public Builder setUuidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        uuid_ = value;
        onChanged();
        return this;
      }

      private int objId_ ;
      /**
       * <pre>
       *每个对象在本数据中的顺序号
       * </pre>
       *
       * <code>uint32 obj_id = 2;</code>
       */
      public int getObjId() {
        return objId_;
      }
      /**
       * <pre>
       *每个对象在本数据中的顺序号
       * </pre>
       *
       * <code>uint32 obj_id = 2;</code>
       */
      public Builder setObjId(int value) {
        
        objId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *每个对象在本数据中的顺序号
       * </pre>
       *
       * <code>uint32 obj_id = 2;</code>
       */
      public Builder clearObjId() {
        
        objId_ = 0;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *0：行人，1：自行车，2：乘用车，3：摩托车，4：特殊用车辆，5：公交车，6：有轨道车，7：卡车，8：三轮车，9：交通信号灯
       *10：交通标识，15：动物，60：路障，61：交通锥，254：其它类型，255：未获取，101:骑行者，102:观光车
       *103：施工牌，104：施工堆，105：水马围栏，106：事故三角板，107：抛洒物
       * </pre>
       *
       * <code>uint32 type = 3;</code>
       */
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *0：行人，1：自行车，2：乘用车，3：摩托车，4：特殊用车辆，5：公交车，6：有轨道车，7：卡车，8：三轮车，9：交通信号灯
       *10：交通标识，15：动物，60：路障，61：交通锥，254：其它类型，255：未获取，101:骑行者，102:观光车
       *103：施工牌，104：施工堆，105：水马围栏，106：事故三角板，107：抛洒物
       * </pre>
       *
       * <code>uint32 type = 3;</code>
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0：行人，1：自行车，2：乘用车，3：摩托车，4：特殊用车辆，5：公交车，6：有轨道车，7：卡车，8：三轮车，9：交通信号灯
       *10：交通标识，15：动物，60：路障，61：交通锥，254：其它类型，255：未获取，101:骑行者，102:观光车
       *103：施工牌，104：施工堆，105：水马围栏，106：事故三角板，107：抛洒物
       * </pre>
       *
       * <code>uint32 type = 3;</code>
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private int status_ ;
      /**
       * <pre>
       *0：静止，1：运动
       * </pre>
       *
       * <code>uint32 status = 4;</code>
       */
      public int getStatus() {
        return status_;
      }
      /**
       * <pre>
       *0：静止，1：运动
       * </pre>
       *
       * <code>uint32 status = 4;</code>
       */
      public Builder setStatus(int value) {
        
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0：静止，1：运动
       * </pre>
       *
       * <code>uint32 status = 4;</code>
       */
      public Builder clearStatus() {
        
        status_ = 0;
        onChanged();
        return this;
      }

      private float len_ ;
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float len = 5;</code>
       */
      public float getLen() {
        return len_;
      }
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float len = 5;</code>
       */
      public Builder setLen(float value) {
        
        len_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float len = 5;</code>
       */
      public Builder clearLen() {
        
        len_ = 0F;
        onChanged();
        return this;
      }

      private float width_ ;
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float width = 6;</code>
       */
      public float getWidth() {
        return width_;
      }
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float width = 6;</code>
       */
      public Builder setWidth(float value) {
        
        width_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float width = 6;</code>
       */
      public Builder clearWidth() {
        
        width_ = 0F;
        onChanged();
        return this;
      }

      private float height_ ;
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float height = 7;</code>
       */
      public float getHeight() {
        return height_;
      }
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float height = 7;</code>
       */
      public Builder setHeight(float value) {
        
        height_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float height = 7;</code>
       */
      public Builder clearHeight() {
        
        height_ = 0F;
        onChanged();
        return this;
      }

      private double longitude_ ;
      /**
       * <code>double longitude = 8;</code>
       */
      public double getLongitude() {
        return longitude_;
      }
      /**
       * <code>double longitude = 8;</code>
       */
      public Builder setLongitude(double value) {
        
        longitude_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>double longitude = 8;</code>
       */
      public Builder clearLongitude() {
        
        longitude_ = 0D;
        onChanged();
        return this;
      }

      private double latitude_ ;
      /**
       * <code>double latitude = 9;</code>
       */
      public double getLatitude() {
        return latitude_;
      }
      /**
       * <code>double latitude = 9;</code>
       */
      public Builder setLatitude(double value) {
        
        latitude_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>double latitude = 9;</code>
       */
      public Builder clearLatitude() {
        
        latitude_ = 0D;
        onChanged();
        return this;
      }

      private float locEast_ ;
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float loc_east = 10;</code>
       */
      public float getLocEast() {
        return locEast_;
      }
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float loc_east = 10;</code>
       */
      public Builder setLocEast(float value) {
        
        locEast_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float loc_east = 10;</code>
       */
      public Builder clearLocEast() {
        
        locEast_ = 0F;
        onChanged();
        return this;
      }

      private float locNorth_ ;
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float loc_north = 11;</code>
       */
      public float getLocNorth() {
        return locNorth_;
      }
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float loc_north = 11;</code>
       */
      public Builder setLocNorth(float value) {
        
        locNorth_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *单位：m
       * </pre>
       *
       * <code>float loc_north = 11;</code>
       */
      public Builder clearLocNorth() {
        
        locNorth_ = 0F;
        onChanged();
        return this;
      }

      private int posConfidence_ ;
      /**
       * <pre>
       *[0..255]，0xFF表示无效，定义应符合附录J的规定。
       * </pre>
       *
       * <code>uint32 pos_confidence = 12;</code>
       */
      public int getPosConfidence() {
        return posConfidence_;
      }
      /**
       * <pre>
       *[0..255]，0xFF表示无效，定义应符合附录J的规定。
       * </pre>
       *
       * <code>uint32 pos_confidence = 12;</code>
       */
      public Builder setPosConfidence(int value) {
        
        posConfidence_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *[0..255]，0xFF表示无效，定义应符合附录J的规定。
       * </pre>
       *
       * <code>uint32 pos_confidence = 12;</code>
       */
      public Builder clearPosConfidence() {
        
        posConfidence_ = 0;
        onChanged();
        return this;
      }

      private double elevation_ ;
      /**
       * <pre>
       *高程（海拔）
       * </pre>
       *
       * <code>double elevation = 13;</code>
       */
      public double getElevation() {
        return elevation_;
      }
      /**
       * <pre>
       *高程（海拔）
       * </pre>
       *
       * <code>double elevation = 13;</code>
       */
      public Builder setElevation(double value) {
        
        elevation_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *高程（海拔）
       * </pre>
       *
       * <code>double elevation = 13;</code>
       */
      public Builder clearElevation() {
        
        elevation_ = 0D;
        onChanged();
        return this;
      }

      private int elevConfidence_ ;
      /**
       * <pre>
       *高程精度
       * </pre>
       *
       * <code>uint32 elev_confidence = 14;</code>
       */
      public int getElevConfidence() {
        return elevConfidence_;
      }
      /**
       * <pre>
       *高程精度
       * </pre>
       *
       * <code>uint32 elev_confidence = 14;</code>
       */
      public Builder setElevConfidence(int value) {
        
        elevConfidence_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *高程精度
       * </pre>
       *
       * <code>uint32 elev_confidence = 14;</code>
       */
      public Builder clearElevConfidence() {
        
        elevConfidence_ = 0;
        onChanged();
        return this;
      }

      private float speed_ ;
      /**
       * <pre>
       *单位：m/s
       * </pre>
       *
       * <code>float speed = 15;</code>
       */
      public float getSpeed() {
        return speed_;
      }
      /**
       * <pre>
       *单位：m/s
       * </pre>
       *
       * <code>float speed = 15;</code>
       */
      public Builder setSpeed(float value) {
        
        speed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *单位：m/s
       * </pre>
       *
       * <code>float speed = 15;</code>
       */
      public Builder clearSpeed() {
        
        speed_ = 0F;
        onChanged();
        return this;
      }

      private int speedConfidence_ ;
      /**
       * <pre>
       *速度精度等级
       * </pre>
       *
       * <code>uint32 speed_confidence = 16;</code>
       */
      public int getSpeedConfidence() {
        return speedConfidence_;
      }
      /**
       * <pre>
       *速度精度等级
       * </pre>
       *
       * <code>uint32 speed_confidence = 16;</code>
       */
      public Builder setSpeedConfidence(int value) {
        
        speedConfidence_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *速度精度等级
       * </pre>
       *
       * <code>uint32 speed_confidence = 16;</code>
       */
      public Builder clearSpeedConfidence() {
        
        speedConfidence_ = 0;
        onChanged();
        return this;
      }

      private float speedEast_ ;
      /**
       * <pre>
       *单位：m/s
       * </pre>
       *
       * <code>float speed_east = 17;</code>
       */
      public float getSpeedEast() {
        return speedEast_;
      }
      /**
       * <pre>
       *单位：m/s
       * </pre>
       *
       * <code>float speed_east = 17;</code>
       */
      public Builder setSpeedEast(float value) {
        
        speedEast_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *单位：m/s
       * </pre>
       *
       * <code>float speed_east = 17;</code>
       */
      public Builder clearSpeedEast() {
        
        speedEast_ = 0F;
        onChanged();
        return this;
      }

      private int speedEastConfidence_ ;
      /**
       * <pre>
       *东西向速度精度等级
       * </pre>
       *
       * <code>uint32 speed_east_confidence = 18;</code>
       */
      public int getSpeedEastConfidence() {
        return speedEastConfidence_;
      }
      /**
       * <pre>
       *东西向速度精度等级
       * </pre>
       *
       * <code>uint32 speed_east_confidence = 18;</code>
       */
      public Builder setSpeedEastConfidence(int value) {
        
        speedEastConfidence_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *东西向速度精度等级
       * </pre>
       *
       * <code>uint32 speed_east_confidence = 18;</code>
       */
      public Builder clearSpeedEastConfidence() {
        
        speedEastConfidence_ = 0;
        onChanged();
        return this;
      }

      private float speedNorth_ ;
      /**
       * <pre>
       *单位：m/s
       * </pre>
       *
       * <code>float speed_north = 19;</code>
       */
      public float getSpeedNorth() {
        return speedNorth_;
      }
      /**
       * <pre>
       *单位：m/s
       * </pre>
       *
       * <code>float speed_north = 19;</code>
       */
      public Builder setSpeedNorth(float value) {
        
        speedNorth_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *单位：m/s
       * </pre>
       *
       * <code>float speed_north = 19;</code>
       */
      public Builder clearSpeedNorth() {
        
        speedNorth_ = 0F;
        onChanged();
        return this;
      }

      private int speedNorthConfidence_ ;
      /**
       * <pre>
       *南北向速度精度等级
       * </pre>
       *
       * <code>uint32 speed_north_confidence = 20;</code>
       */
      public int getSpeedNorthConfidence() {
        return speedNorthConfidence_;
      }
      /**
       * <pre>
       *南北向速度精度等级
       * </pre>
       *
       * <code>uint32 speed_north_confidence = 20;</code>
       */
      public Builder setSpeedNorthConfidence(int value) {
        
        speedNorthConfidence_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *南北向速度精度等级
       * </pre>
       *
       * <code>uint32 speed_north_confidence = 20;</code>
       */
      public Builder clearSpeedNorthConfidence() {
        
        speedNorthConfidence_ = 0;
        onChanged();
        return this;
      }

      private float heading_ ;
      /**
       * <pre>
       *正北方向与运动方向顺时针夹角
       * </pre>
       *
       * <code>float heading = 21;</code>
       */
      public float getHeading() {
        return heading_;
      }
      /**
       * <pre>
       *正北方向与运动方向顺时针夹角
       * </pre>
       *
       * <code>float heading = 21;</code>
       */
      public Builder setHeading(float value) {
        
        heading_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *正北方向与运动方向顺时针夹角
       * </pre>
       *
       * <code>float heading = 21;</code>
       */
      public Builder clearHeading() {
        
        heading_ = 0F;
        onChanged();
        return this;
      }

      private int headConfidence_ ;
      /**
       * <pre>
       *航向角精度等级
       * </pre>
       *
       * <code>uint32 head_confidence = 22;</code>
       */
      public int getHeadConfidence() {
        return headConfidence_;
      }
      /**
       * <pre>
       *航向角精度等级
       * </pre>
       *
       * <code>uint32 head_confidence = 22;</code>
       */
      public Builder setHeadConfidence(int value) {
        
        headConfidence_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *航向角精度等级
       * </pre>
       *
       * <code>uint32 head_confidence = 22;</code>
       */
      public Builder clearHeadConfidence() {
        
        headConfidence_ = 0;
        onChanged();
        return this;
      }

      private float accelVert_ ;
      /**
       * <pre>
       *单位：m/s2
       * </pre>
       *
       * <code>float accel_vert = 23;</code>
       */
      public float getAccelVert() {
        return accelVert_;
      }
      /**
       * <pre>
       *单位：m/s2
       * </pre>
       *
       * <code>float accel_vert = 23;</code>
       */
      public Builder setAccelVert(float value) {
        
        accelVert_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *单位：m/s2
       * </pre>
       *
       * <code>float accel_vert = 23;</code>
       */
      public Builder clearAccelVert() {
        
        accelVert_ = 0F;
        onChanged();
        return this;
      }

      private int accelVertConfidence_ ;
      /**
       * <pre>
       *目标纵向加速度置精度等级
       * </pre>
       *
       * <code>uint32 accel_vert_confidence = 24;</code>
       */
      public int getAccelVertConfidence() {
        return accelVertConfidence_;
      }
      /**
       * <pre>
       *目标纵向加速度置精度等级
       * </pre>
       *
       * <code>uint32 accel_vert_confidence = 24;</code>
       */
      public Builder setAccelVertConfidence(int value) {
        
        accelVertConfidence_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *目标纵向加速度置精度等级
       * </pre>
       *
       * <code>uint32 accel_vert_confidence = 24;</code>
       */
      public Builder clearAccelVertConfidence() {
        
        accelVertConfidence_ = 0;
        onChanged();
        return this;
      }

      private int trackedTimes_ ;
      /**
       * <pre>
       *目标跟踪时长
       * </pre>
       *
       * <code>uint32 tracked_times = 25;</code>
       */
      public int getTrackedTimes() {
        return trackedTimes_;
      }
      /**
       * <pre>
       *目标跟踪时长
       * </pre>
       *
       * <code>uint32 tracked_times = 25;</code>
       */
      public Builder setTrackedTimes(int value) {
        
        trackedTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *目标跟踪时长
       * </pre>
       *
       * <code>uint32 tracked_times = 25;</code>
       */
      public Builder clearTrackedTimes() {
        
        trackedTimes_ = 0;
        onChanged();
        return this;
      }

      private int histLocNum_ ;
      /**
       * <pre>
       *目标历史轨迹数量
       * </pre>
       *
       * <code>uint32 hist_loc_num = 26;</code>
       */
      public int getHistLocNum() {
        return histLocNum_;
      }
      /**
       * <pre>
       *目标历史轨迹数量
       * </pre>
       *
       * <code>uint32 hist_loc_num = 26;</code>
       */
      public Builder setHistLocNum(int value) {
        
        histLocNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *目标历史轨迹数量
       * </pre>
       *
       * <code>uint32 hist_loc_num = 26;</code>
       */
      public Builder clearHistLocNum() {
        
        histLocNum_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<HistLoc> histLocs_ =
        java.util.Collections.emptyList();
      private void ensureHistLocsIsMutable() {
        if (!((bitField0_ & 0x04000000) == 0x04000000)) {
          histLocs_ = new java.util.ArrayList<HistLoc>(histLocs_);
          bitField0_ |= 0x04000000;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          HistLoc, HistLoc.Builder, HistLocOrBuilder> histLocsBuilder_;

      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public java.util.List<HistLoc> getHistLocsList() {
        if (histLocsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(histLocs_);
        } else {
          return histLocsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public int getHistLocsCount() {
        if (histLocsBuilder_ == null) {
          return histLocs_.size();
        } else {
          return histLocsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public HistLoc getHistLocs(int index) {
        if (histLocsBuilder_ == null) {
          return histLocs_.get(index);
        } else {
          return histLocsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public Builder setHistLocs(
          int index, HistLoc value) {
        if (histLocsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHistLocsIsMutable();
          histLocs_.set(index, value);
          onChanged();
        } else {
          histLocsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public Builder setHistLocs(
          int index, HistLoc.Builder builderForValue) {
        if (histLocsBuilder_ == null) {
          ensureHistLocsIsMutable();
          histLocs_.set(index, builderForValue.build());
          onChanged();
        } else {
          histLocsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public Builder addHistLocs(HistLoc value) {
        if (histLocsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHistLocsIsMutable();
          histLocs_.add(value);
          onChanged();
        } else {
          histLocsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public Builder addHistLocs(
          int index, HistLoc value) {
        if (histLocsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHistLocsIsMutable();
          histLocs_.add(index, value);
          onChanged();
        } else {
          histLocsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public Builder addHistLocs(
          HistLoc.Builder builderForValue) {
        if (histLocsBuilder_ == null) {
          ensureHistLocsIsMutable();
          histLocs_.add(builderForValue.build());
          onChanged();
        } else {
          histLocsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public Builder addHistLocs(
          int index, HistLoc.Builder builderForValue) {
        if (histLocsBuilder_ == null) {
          ensureHistLocsIsMutable();
          histLocs_.add(index, builderForValue.build());
          onChanged();
        } else {
          histLocsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public Builder addAllHistLocs(
          Iterable<? extends HistLoc> values) {
        if (histLocsBuilder_ == null) {
          ensureHistLocsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, histLocs_);
          onChanged();
        } else {
          histLocsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public Builder clearHistLocs() {
        if (histLocsBuilder_ == null) {
          histLocs_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x04000000);
          onChanged();
        } else {
          histLocsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public Builder removeHistLocs(int index) {
        if (histLocsBuilder_ == null) {
          ensureHistLocsIsMutable();
          histLocs_.remove(index);
          onChanged();
        } else {
          histLocsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public HistLoc.Builder getHistLocsBuilder(
          int index) {
        return getHistLocsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public HistLocOrBuilder getHistLocsOrBuilder(
          int index) {
        if (histLocsBuilder_ == null) {
          return histLocs_.get(index);  } else {
          return histLocsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public java.util.List<? extends HistLocOrBuilder>
           getHistLocsOrBuilderList() {
        if (histLocsBuilder_ != null) {
          return histLocsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(histLocs_);
        }
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public HistLoc.Builder addHistLocsBuilder() {
        return getHistLocsFieldBuilder().addBuilder(
            HistLoc.getDefaultInstance());
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public HistLoc.Builder addHistLocsBuilder(
          int index) {
        return getHistLocsFieldBuilder().addBuilder(
            index, HistLoc.getDefaultInstance());
      }
      /**
       * <pre>
       *历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc hist_locs = 27;</code>
       */
      public java.util.List<HistLoc.Builder>
           getHistLocsBuilderList() {
        return getHistLocsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          HistLoc, HistLoc.Builder, HistLocOrBuilder>
          getHistLocsFieldBuilder() {
        if (histLocsBuilder_ == null) {
          histLocsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              HistLoc, HistLoc.Builder, HistLocOrBuilder>(
                  histLocs_,
                  ((bitField0_ & 0x04000000) == 0x04000000),
                  getParentForChildren(),
                  isClean());
          histLocs_ = null;
        }
        return histLocsBuilder_;
      }

      private int predLocNum_ ;
      /**
       * <pre>
       *目标预测轨迹数量
       * </pre>
       *
       * <code>uint32 pred_loc_num = 28;</code>
       */
      public int getPredLocNum() {
        return predLocNum_;
      }
      /**
       * <pre>
       *目标预测轨迹数量
       * </pre>
       *
       * <code>uint32 pred_loc_num = 28;</code>
       */
      public Builder setPredLocNum(int value) {
        
        predLocNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *目标预测轨迹数量
       * </pre>
       *
       * <code>uint32 pred_loc_num = 28;</code>
       */
      public Builder clearPredLocNum() {
        
        predLocNum_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<HistLoc> predLocs_ =
        java.util.Collections.emptyList();
      private void ensurePredLocsIsMutable() {
        if (!((bitField0_ & 0x10000000) == 0x10000000)) {
          predLocs_ = new java.util.ArrayList<HistLoc>(predLocs_);
          bitField0_ |= 0x10000000;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          HistLoc, HistLoc.Builder, HistLocOrBuilder> predLocsBuilder_;

      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public java.util.List<HistLoc> getPredLocsList() {
        if (predLocsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(predLocs_);
        } else {
          return predLocsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public int getPredLocsCount() {
        if (predLocsBuilder_ == null) {
          return predLocs_.size();
        } else {
          return predLocsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public HistLoc getPredLocs(int index) {
        if (predLocsBuilder_ == null) {
          return predLocs_.get(index);
        } else {
          return predLocsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public Builder setPredLocs(
          int index, HistLoc value) {
        if (predLocsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePredLocsIsMutable();
          predLocs_.set(index, value);
          onChanged();
        } else {
          predLocsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public Builder setPredLocs(
          int index, HistLoc.Builder builderForValue) {
        if (predLocsBuilder_ == null) {
          ensurePredLocsIsMutable();
          predLocs_.set(index, builderForValue.build());
          onChanged();
        } else {
          predLocsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public Builder addPredLocs(HistLoc value) {
        if (predLocsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePredLocsIsMutable();
          predLocs_.add(value);
          onChanged();
        } else {
          predLocsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public Builder addPredLocs(
          int index, HistLoc value) {
        if (predLocsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePredLocsIsMutable();
          predLocs_.add(index, value);
          onChanged();
        } else {
          predLocsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public Builder addPredLocs(
          HistLoc.Builder builderForValue) {
        if (predLocsBuilder_ == null) {
          ensurePredLocsIsMutable();
          predLocs_.add(builderForValue.build());
          onChanged();
        } else {
          predLocsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public Builder addPredLocs(
          int index, HistLoc.Builder builderForValue) {
        if (predLocsBuilder_ == null) {
          ensurePredLocsIsMutable();
          predLocs_.add(index, builderForValue.build());
          onChanged();
        } else {
          predLocsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public Builder addAllPredLocs(
          Iterable<? extends HistLoc> values) {
        if (predLocsBuilder_ == null) {
          ensurePredLocsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, predLocs_);
          onChanged();
        } else {
          predLocsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public Builder clearPredLocs() {
        if (predLocsBuilder_ == null) {
          predLocs_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x10000000);
          onChanged();
        } else {
          predLocsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public Builder removePredLocs(int index) {
        if (predLocsBuilder_ == null) {
          ensurePredLocsIsMutable();
          predLocs_.remove(index);
          onChanged();
        } else {
          predLocsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public HistLoc.Builder getPredLocsBuilder(
          int index) {
        return getPredLocsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public HistLocOrBuilder getPredLocsOrBuilder(
          int index) {
        if (predLocsBuilder_ == null) {
          return predLocs_.get(index);  } else {
          return predLocsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public java.util.List<? extends HistLocOrBuilder>
           getPredLocsOrBuilderList() {
        if (predLocsBuilder_ != null) {
          return predLocsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(predLocs_);
        }
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public HistLoc.Builder addPredLocsBuilder() {
        return getPredLocsFieldBuilder().addBuilder(
            HistLoc.getDefaultInstance());
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public HistLoc.Builder addPredLocsBuilder(
          int index) {
        return getPredLocsFieldBuilder().addBuilder(
            index, HistLoc.getDefaultInstance());
      }
      /**
       * <pre>
       *预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
       *距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.HistLoc pred_locs = 29;</code>
       */
      public java.util.List<HistLoc.Builder>
           getPredLocsBuilderList() {
        return getPredLocsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          HistLoc, HistLoc.Builder, HistLocOrBuilder>
          getPredLocsFieldBuilder() {
        if (predLocsBuilder_ == null) {
          predLocsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              HistLoc, HistLoc.Builder, HistLocOrBuilder>(
                  predLocs_,
                  ((bitField0_ & 0x10000000) == 0x10000000),
                  getParentForChildren(),
                  isClean());
          predLocs_ = null;
        }
        return predLocsBuilder_;
      }

      private int laneId_ ;
      /**
       * <pre>
       *目标所在车道编号
       * </pre>
       *
       * <code>int32 lane_id = 30;</code>
       */
      public int getLaneId() {
        return laneId_;
      }
      /**
       * <pre>
       *目标所在车道编号
       * </pre>
       *
       * <code>int32 lane_id = 30;</code>
       */
      public Builder setLaneId(int value) {
        
        laneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *目标所在车道编号
       * </pre>
       *
       * <code>int32 lane_id = 30;</code>
       */
      public Builder clearLaneId() {
        
        laneId_ = 0;
        onChanged();
        return this;
      }

      private int filterInfoType_ ;
      /**
       * <pre>
       *0：无效；1：卡尔曼滤波信息；2～255：预留。
       *当值为1时，传输卡尔曼滤波信息字段，其余值均不发送。
       * </pre>
       *
       * <code>uint32 filter_info_type = 31;</code>
       */
      public int getFilterInfoType() {
        return filterInfoType_;
      }
      /**
       * <pre>
       *0：无效；1：卡尔曼滤波信息；2～255：预留。
       *当值为1时，传输卡尔曼滤波信息字段，其余值均不发送。
       * </pre>
       *
       * <code>uint32 filter_info_type = 31;</code>
       */
      public Builder setFilterInfoType(int value) {
        
        filterInfoType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0：无效；1：卡尔曼滤波信息；2～255：预留。
       *当值为1时，传输卡尔曼滤波信息字段，其余值均不发送。
       * </pre>
       *
       * <code>uint32 filter_info_type = 31;</code>
       */
      public Builder clearFilterInfoType() {
        
        filterInfoType_ = 0;
        onChanged();
        return this;
      }

      private FilterInfo filterInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          FilterInfo, FilterInfo.Builder, FilterInfoOrBuilder> filterInfoBuilder_;
      /**
       * <pre>
       *卡尔曼滤波信息
       * </pre>
       *
       * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
       */
      public boolean hasFilterInfo() {
        return filterInfoBuilder_ != null || filterInfo_ != null;
      }
      /**
       * <pre>
       *卡尔曼滤波信息
       * </pre>
       *
       * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
       */
      public FilterInfo getFilterInfo() {
        if (filterInfoBuilder_ == null) {
          return filterInfo_ == null ? FilterInfo.getDefaultInstance() : filterInfo_;
        } else {
          return filterInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *卡尔曼滤波信息
       * </pre>
       *
       * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
       */
      public Builder setFilterInfo(FilterInfo value) {
        if (filterInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          filterInfo_ = value;
          onChanged();
        } else {
          filterInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *卡尔曼滤波信息
       * </pre>
       *
       * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
       */
      public Builder setFilterInfo(
          FilterInfo.Builder builderForValue) {
        if (filterInfoBuilder_ == null) {
          filterInfo_ = builderForValue.build();
          onChanged();
        } else {
          filterInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *卡尔曼滤波信息
       * </pre>
       *
       * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
       */
      public Builder mergeFilterInfo(FilterInfo value) {
        if (filterInfoBuilder_ == null) {
          if (filterInfo_ != null) {
            filterInfo_ =
              FilterInfo.newBuilder(filterInfo_).mergeFrom(value).buildPartial();
          } else {
            filterInfo_ = value;
          }
          onChanged();
        } else {
          filterInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *卡尔曼滤波信息
       * </pre>
       *
       * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
       */
      public Builder clearFilterInfo() {
        if (filterInfoBuilder_ == null) {
          filterInfo_ = null;
          onChanged();
        } else {
          filterInfo_ = null;
          filterInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *卡尔曼滤波信息
       * </pre>
       *
       * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
       */
      public FilterInfo.Builder getFilterInfoBuilder() {
        
        onChanged();
        return getFilterInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *卡尔曼滤波信息
       * </pre>
       *
       * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
       */
      public FilterInfoOrBuilder getFilterInfoOrBuilder() {
        if (filterInfoBuilder_ != null) {
          return filterInfoBuilder_.getMessageOrBuilder();
        } else {
          return filterInfo_ == null ?
              FilterInfo.getDefaultInstance() : filterInfo_;
        }
      }
      /**
       * <pre>
       *卡尔曼滤波信息
       * </pre>
       *
       * <code>.road.data.proto.FilterInfo filter_info = 32;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          FilterInfo, FilterInfo.Builder, FilterInfoOrBuilder>
          getFilterInfoFieldBuilder() {
        if (filterInfoBuilder_ == null) {
          filterInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              FilterInfo, FilterInfo.Builder, FilterInfoOrBuilder>(
                  getFilterInfo(),
                  getParentForChildren(),
                  isClean());
          filterInfo_ = null;
        }
        return filterInfoBuilder_;
      }

      private int lenplateNo_ ;
      /**
       * <pre>
       *车牌号字节数
       * </pre>
       *
       * <code>uint32 lenplate_no = 33;</code>
       */
      public int getLenplateNo() {
        return lenplateNo_;
      }
      /**
       * <pre>
       *车牌号字节数
       * </pre>
       *
       * <code>uint32 lenplate_no = 33;</code>
       */
      public Builder setLenplateNo(int value) {
        
        lenplateNo_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *车牌号字节数
       * </pre>
       *
       * <code>uint32 lenplate_no = 33;</code>
       */
      public Builder clearLenplateNo() {
        
        lenplateNo_ = 0;
        onChanged();
        return this;
      }

      private Object plateNo_ = "";
      /**
       * <pre>
       *车牌号
       * </pre>
       *
       * <code>string plate_no = 34;</code>
       */
      public String getPlateNo() {
        Object ref = plateNo_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          plateNo_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       *车牌号
       * </pre>
       *
       * <code>string plate_no = 34;</code>
       */
      public com.google.protobuf.ByteString
          getPlateNoBytes() {
        Object ref = plateNo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          plateNo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *车牌号
       * </pre>
       *
       * <code>string plate_no = 34;</code>
       */
      public Builder setPlateNo(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        plateNo_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *车牌号
       * </pre>
       *
       * <code>string plate_no = 34;</code>
       */
      public Builder clearPlateNo() {
        
        plateNo_ = getDefaultInstance().getPlateNo();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *车牌号
       * </pre>
       *
       * <code>string plate_no = 34;</code>
       */
      public Builder setPlateNoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        plateNo_ = value;
        onChanged();
        return this;
      }

      private int plateType_ ;
      /**
       * <pre>
       *1：大型汽车；2：挂车；3：大型新能源汽车；4：小型汽车；5：小型新能源汽车；6：使馆汽车；7：领馆汽车；8：港澳入出境车；
       * 9：教练汽车；10：警用汽车；11：普通摩托车；12：轻便摩托车；13：使馆摩托车；14：领馆摩托车；15：教练摩托车；
       * 16：警用摩托车；17：低速车；18：临时行驶车；19：临时入境汽车；20：临时入境摩托车；21：拖拉机；22：其他；
       *“0xFE”表示异常，“0xFF”表示无效
       * </pre>
       *
       * <code>uint32 plate_type = 35;</code>
       */
      public int getPlateType() {
        return plateType_;
      }
      /**
       * <pre>
       *1：大型汽车；2：挂车；3：大型新能源汽车；4：小型汽车；5：小型新能源汽车；6：使馆汽车；7：领馆汽车；8：港澳入出境车；
       * 9：教练汽车；10：警用汽车；11：普通摩托车；12：轻便摩托车；13：使馆摩托车；14：领馆摩托车；15：教练摩托车；
       * 16：警用摩托车；17：低速车；18：临时行驶车；19：临时入境汽车；20：临时入境摩托车；21：拖拉机；22：其他；
       *“0xFE”表示异常，“0xFF”表示无效
       * </pre>
       *
       * <code>uint32 plate_type = 35;</code>
       */
      public Builder setPlateType(int value) {
        
        plateType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *1：大型汽车；2：挂车；3：大型新能源汽车；4：小型汽车；5：小型新能源汽车；6：使馆汽车；7：领馆汽车；8：港澳入出境车；
       * 9：教练汽车；10：警用汽车；11：普通摩托车；12：轻便摩托车；13：使馆摩托车；14：领馆摩托车；15：教练摩托车；
       * 16：警用摩托车；17：低速车；18：临时行驶车；19：临时入境汽车；20：临时入境摩托车；21：拖拉机；22：其他；
       *“0xFE”表示异常，“0xFF”表示无效
       * </pre>
       *
       * <code>uint32 plate_type = 35;</code>
       */
      public Builder clearPlateType() {
        
        plateType_ = 0;
        onChanged();
        return this;
      }

      private int plateColor_ ;
      /**
       * <pre>
       *1：黄；2：蓝；3：黑；4：白；5：绿（农用车）；6：红；7：黄绿；8：渐变绿；20：天（酞）蓝；21：棕黄；22：其他；
       *“0xFE”表示异常，“0xFF”表示无效。
       * </pre>
       *
       * <code>uint32 plate_color = 36;</code>
       */
      public int getPlateColor() {
        return plateColor_;
      }
      /**
       * <pre>
       *1：黄；2：蓝；3：黑；4：白；5：绿（农用车）；6：红；7：黄绿；8：渐变绿；20：天（酞）蓝；21：棕黄；22：其他；
       *“0xFE”表示异常，“0xFF”表示无效。
       * </pre>
       *
       * <code>uint32 plate_color = 36;</code>
       */
      public Builder setPlateColor(int value) {
        
        plateColor_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *1：黄；2：蓝；3：黑；4：白；5：绿（农用车）；6：红；7：黄绿；8：渐变绿；20：天（酞）蓝；21：棕黄；22：其他；
       *“0xFE”表示异常，“0xFF”表示无效。
       * </pre>
       *
       * <code>uint32 plate_color = 36;</code>
       */
      public Builder clearPlateColor() {
        
        plateColor_ = 0;
        onChanged();
        return this;
      }

      private int objColor_ ;
      /**
       * <pre>
       *1：白；4：灰；7：黄；10：粉；13：红；16：紫；19：绿；22：蓝；25：棕；28：黑；31：橙；34：青；37：银；40：银白；43：其他；
       *其中，（值+1）表示浅色，（值+2）表示深色。例如：22表示蓝色，（22+1）即23表示浅蓝色，（22+2）即24表示深蓝色。
       *“0xFE”表示异常，“0xFF”表示无效。
       * </pre>
       *
       * <code>uint32 obj_color = 37;</code>
       */
      public int getObjColor() {
        return objColor_;
      }
      /**
       * <pre>
       *1：白；4：灰；7：黄；10：粉；13：红；16：紫；19：绿；22：蓝；25：棕；28：黑；31：橙；34：青；37：银；40：银白；43：其他；
       *其中，（值+1）表示浅色，（值+2）表示深色。例如：22表示蓝色，（22+1）即23表示浅蓝色，（22+2）即24表示深蓝色。
       *“0xFE”表示异常，“0xFF”表示无效。
       * </pre>
       *
       * <code>uint32 obj_color = 37;</code>
       */
      public Builder setObjColor(int value) {
        
        objColor_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *1：白；4：灰；7：黄；10：粉；13：红；16：紫；19：绿；22：蓝；25：棕；28：黑；31：橙；34：青；37：银；40：银白；43：其他；
       *其中，（值+1）表示浅色，（值+2）表示深色。例如：22表示蓝色，（22+1）即23表示浅蓝色，（22+2）即24表示深蓝色。
       *“0xFE”表示异常，“0xFF”表示无效。
       * </pre>
       *
       * <code>uint32 obj_color = 37;</code>
       */
      public Builder clearObjColor() {
        
        objColor_ = 0;
        onChanged();
        return this;
      }

      private int deviceIdx_ ;
      /**
       * <pre>
       *槽位集合
       * </pre>
       *
       * <code>uint32 device_idx = 38;</code>
       */
      public int getDeviceIdx() {
        return deviceIdx_;
      }
      /**
       * <pre>
       *槽位集合
       * </pre>
       *
       * <code>uint32 device_idx = 38;</code>
       */
      public Builder setDeviceIdx(int value) {
        
        deviceIdx_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *槽位集合
       * </pre>
       *
       * <code>uint32 device_idx = 38;</code>
       */
      public Builder clearDeviceIdx() {
        
        deviceIdx_ = 0;
        onChanged();
        return this;
      }

      private int roadId_ ;
      /**
       * <pre>
       *高精地图roadPkId
       * </pre>
       *
       * <code>uint32 road_id = 39;</code>
       */
      public int getRoadId() {
        return roadId_;
      }
      /**
       * <pre>
       *高精地图roadPkId
       * </pre>
       *
       * <code>uint32 road_id = 39;</code>
       */
      public Builder setRoadId(int value) {
        
        roadId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *高精地图roadPkId
       * </pre>
       *
       * <code>uint32 road_id = 39;</code>
       */
      public Builder clearRoadId() {
        
        roadId_ = 0;
        onChanged();
        return this;
      }

      private float distance_ ;
      /**
       * <pre>
       *到mec距离
       * </pre>
       *
       * <code>float distance = 40;</code>
       */
      public float getDistance() {
        return distance_;
      }
      /**
       * <pre>
       *到mec距离
       * </pre>
       *
       * <code>float distance = 40;</code>
       */
      public Builder setDistance(float value) {
        
        distance_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *到mec距离
       * </pre>
       *
       * <code>float distance = 40;</code>
       */
      public Builder clearDistance() {
        
        distance_ = 0F;
        onChanged();
        return this;
      }

      private int parkingDuration_ ;
      /**
       * <pre>
       *停车时长，单位:ms
       * </pre>
       *
       * <code>uint32 parking_duration = 41;</code>
       */
      public int getParkingDuration() {
        return parkingDuration_;
      }
      /**
       * <pre>
       *停车时长，单位:ms
       * </pre>
       *
       * <code>uint32 parking_duration = 41;</code>
       */
      public Builder setParkingDuration(int value) {
        
        parkingDuration_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *停车时长，单位:ms
       * </pre>
       *
       * <code>uint32 parking_duration = 41;</code>
       */
      public Builder clearParkingDuration() {
        
        parkingDuration_ = 0;
        onChanged();
        return this;
      }

      private Object pole_ = "";
      /**
       * <pre>
       *杆位号
       * </pre>
       *
       * <code>string pole = 42;</code>
       */
      public String getPole() {
        Object ref = pole_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          pole_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       *杆位号
       * </pre>
       *
       * <code>string pole = 42;</code>
       */
      public com.google.protobuf.ByteString
          getPoleBytes() {
        Object ref = pole_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          pole_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *杆位号
       * </pre>
       *
       * <code>string pole = 42;</code>
       */
      public Builder setPole(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        pole_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *杆位号
       * </pre>
       *
       * <code>string pole = 42;</code>
       */
      public Builder clearPole() {
        
        pole_ = getDefaultInstance().getPole();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *杆位号
       * </pre>
       *
       * <code>string pole = 42;</code>
       */
      public Builder setPoleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        pole_ = value;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:road.data.proto.PerceptionObject)
    }

    // @@protoc_insertion_point(class_scope:road.data.proto.PerceptionObject)
    private static final PerceptionObject DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new PerceptionObject();
    }

    public static PerceptionObject getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PerceptionObject>
        PARSER = new com.google.protobuf.AbstractParser<PerceptionObject>() {
      public PerceptionObject parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PerceptionObject(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PerceptionObject> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<PerceptionObject> getParserForType() {
      return PARSER;
    }

    public PerceptionObject getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface HistLocOrBuilder extends
      // @@protoc_insertion_point(interface_extends:road.data.proto.HistLoc)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>double longitude = 1;</code>
     */
    double getLongitude();

    /**
     * <code>double latitude = 2;</code>
     */
    double getLatitude();

    /**
     * <pre>
     *位置精度等级
     * </pre>
     *
     * <code>uint32 pos_confidence = 3;</code>
     */
    int getPosConfidence();

    /**
     * <pre>
     *单位：m/s
     * </pre>
     *
     * <code>float speed = 4;</code>
     */
    float getSpeed();

    /**
     * <pre>
     *速度精度等级
     * </pre>
     *
     * <code>uint32 speed_confidence = 5;</code>
     */
    int getSpeedConfidence();

    /**
     * <pre>
     *航向角
     * </pre>
     *
     * <code>float heading = 6;</code>
     */
    float getHeading();

    /**
     * <pre>
     *航向角精度等级
     * </pre>
     *
     * <code>uint32 head_confidence = 7;</code>
     */
    int getHeadConfidence();
  }
  /**
   * <pre>
   *轨迹点数据
   * </pre>
   *
   * Protobuf type {@code road.data.proto.HistLoc}
   */
  public  static final class HistLoc extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:road.data.proto.HistLoc)
      HistLocOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use HistLoc.newBuilder() to construct.
    private HistLoc(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private HistLoc() {
      longitude_ = 0D;
      latitude_ = 0D;
      posConfidence_ = 0;
      speed_ = 0F;
      speedConfidence_ = 0;
      heading_ = 0F;
      headConfidence_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private HistLoc(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 9: {

              longitude_ = input.readDouble();
              break;
            }
            case 17: {

              latitude_ = input.readDouble();
              break;
            }
            case 24: {

              posConfidence_ = input.readUInt32();
              break;
            }
            case 37: {

              speed_ = input.readFloat();
              break;
            }
            case 40: {

              speedConfidence_ = input.readUInt32();
              break;
            }
            case 53: {

              heading_ = input.readFloat();
              break;
            }
            case 56: {

              headConfidence_ = input.readUInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return Percep.internal_static_road_data_proto_HistLoc_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return Percep.internal_static_road_data_proto_HistLoc_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              HistLoc.class, Builder.class);
    }

    public static final int LONGITUDE_FIELD_NUMBER = 1;
    private double longitude_;
    /**
     * <code>double longitude = 1;</code>
     */
    public double getLongitude() {
      return longitude_;
    }

    public static final int LATITUDE_FIELD_NUMBER = 2;
    private double latitude_;
    /**
     * <code>double latitude = 2;</code>
     */
    public double getLatitude() {
      return latitude_;
    }

    public static final int POS_CONFIDENCE_FIELD_NUMBER = 3;
    private int posConfidence_;
    /**
     * <pre>
     *位置精度等级
     * </pre>
     *
     * <code>uint32 pos_confidence = 3;</code>
     */
    public int getPosConfidence() {
      return posConfidence_;
    }

    public static final int SPEED_FIELD_NUMBER = 4;
    private float speed_;
    /**
     * <pre>
     *单位：m/s
     * </pre>
     *
     * <code>float speed = 4;</code>
     */
    public float getSpeed() {
      return speed_;
    }

    public static final int SPEED_CONFIDENCE_FIELD_NUMBER = 5;
    private int speedConfidence_;
    /**
     * <pre>
     *速度精度等级
     * </pre>
     *
     * <code>uint32 speed_confidence = 5;</code>
     */
    public int getSpeedConfidence() {
      return speedConfidence_;
    }

    public static final int HEADING_FIELD_NUMBER = 6;
    private float heading_;
    /**
     * <pre>
     *航向角
     * </pre>
     *
     * <code>float heading = 6;</code>
     */
    public float getHeading() {
      return heading_;
    }

    public static final int HEAD_CONFIDENCE_FIELD_NUMBER = 7;
    private int headConfidence_;
    /**
     * <pre>
     *航向角精度等级
     * </pre>
     *
     * <code>uint32 head_confidence = 7;</code>
     */
    public int getHeadConfidence() {
      return headConfidence_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (longitude_ != 0D) {
        output.writeDouble(1, longitude_);
      }
      if (latitude_ != 0D) {
        output.writeDouble(2, latitude_);
      }
      if (posConfidence_ != 0) {
        output.writeUInt32(3, posConfidence_);
      }
      if (speed_ != 0F) {
        output.writeFloat(4, speed_);
      }
      if (speedConfidence_ != 0) {
        output.writeUInt32(5, speedConfidence_);
      }
      if (heading_ != 0F) {
        output.writeFloat(6, heading_);
      }
      if (headConfidence_ != 0) {
        output.writeUInt32(7, headConfidence_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (longitude_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(1, longitude_);
      }
      if (latitude_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(2, latitude_);
      }
      if (posConfidence_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, posConfidence_);
      }
      if (speed_ != 0F) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(4, speed_);
      }
      if (speedConfidence_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, speedConfidence_);
      }
      if (heading_ != 0F) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(6, heading_);
      }
      if (headConfidence_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, headConfidence_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof HistLoc)) {
        return super.equals(obj);
      }
      HistLoc other = (HistLoc) obj;

      boolean result = true;
      result = result && (
          Double.doubleToLongBits(getLongitude())
          == Double.doubleToLongBits(
              other.getLongitude()));
      result = result && (
          Double.doubleToLongBits(getLatitude())
          == Double.doubleToLongBits(
              other.getLatitude()));
      result = result && (getPosConfidence()
          == other.getPosConfidence());
      result = result && (
          Float.floatToIntBits(getSpeed())
          == Float.floatToIntBits(
              other.getSpeed()));
      result = result && (getSpeedConfidence()
          == other.getSpeedConfidence());
      result = result && (
          Float.floatToIntBits(getHeading())
          == Float.floatToIntBits(
              other.getHeading()));
      result = result && (getHeadConfidence()
          == other.getHeadConfidence());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + LONGITUDE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          Double.doubleToLongBits(getLongitude()));
      hash = (37 * hash) + LATITUDE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          Double.doubleToLongBits(getLatitude()));
      hash = (37 * hash) + POS_CONFIDENCE_FIELD_NUMBER;
      hash = (53 * hash) + getPosConfidence();
      hash = (37 * hash) + SPEED_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getSpeed());
      hash = (37 * hash) + SPEED_CONFIDENCE_FIELD_NUMBER;
      hash = (53 * hash) + getSpeedConfidence();
      hash = (37 * hash) + HEADING_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getHeading());
      hash = (37 * hash) + HEAD_CONFIDENCE_FIELD_NUMBER;
      hash = (53 * hash) + getHeadConfidence();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static HistLoc parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static HistLoc parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static HistLoc parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static HistLoc parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static HistLoc parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static HistLoc parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static HistLoc parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static HistLoc parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static HistLoc parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static HistLoc parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static HistLoc parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static HistLoc parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(HistLoc prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *轨迹点数据
     * </pre>
     *
     * Protobuf type {@code road.data.proto.HistLoc}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:road.data.proto.HistLoc)
        HistLocOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return Percep.internal_static_road_data_proto_HistLoc_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return Percep.internal_static_road_data_proto_HistLoc_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                HistLoc.class, Builder.class);
      }

      // Construct using road.data.proto.Percep.HistLoc.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        longitude_ = 0D;

        latitude_ = 0D;

        posConfidence_ = 0;

        speed_ = 0F;

        speedConfidence_ = 0;

        heading_ = 0F;

        headConfidence_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return Percep.internal_static_road_data_proto_HistLoc_descriptor;
      }

      public HistLoc getDefaultInstanceForType() {
        return HistLoc.getDefaultInstance();
      }

      public HistLoc build() {
        HistLoc result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public HistLoc buildPartial() {
        HistLoc result = new HistLoc(this);
        result.longitude_ = longitude_;
        result.latitude_ = latitude_;
        result.posConfidence_ = posConfidence_;
        result.speed_ = speed_;
        result.speedConfidence_ = speedConfidence_;
        result.heading_ = heading_;
        result.headConfidence_ = headConfidence_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof HistLoc) {
          return mergeFrom((HistLoc)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(HistLoc other) {
        if (other == HistLoc.getDefaultInstance()) return this;
        if (other.getLongitude() != 0D) {
          setLongitude(other.getLongitude());
        }
        if (other.getLatitude() != 0D) {
          setLatitude(other.getLatitude());
        }
        if (other.getPosConfidence() != 0) {
          setPosConfidence(other.getPosConfidence());
        }
        if (other.getSpeed() != 0F) {
          setSpeed(other.getSpeed());
        }
        if (other.getSpeedConfidence() != 0) {
          setSpeedConfidence(other.getSpeedConfidence());
        }
        if (other.getHeading() != 0F) {
          setHeading(other.getHeading());
        }
        if (other.getHeadConfidence() != 0) {
          setHeadConfidence(other.getHeadConfidence());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        HistLoc parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (HistLoc) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private double longitude_ ;
      /**
       * <code>double longitude = 1;</code>
       */
      public double getLongitude() {
        return longitude_;
      }
      /**
       * <code>double longitude = 1;</code>
       */
      public Builder setLongitude(double value) {
        
        longitude_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>double longitude = 1;</code>
       */
      public Builder clearLongitude() {
        
        longitude_ = 0D;
        onChanged();
        return this;
      }

      private double latitude_ ;
      /**
       * <code>double latitude = 2;</code>
       */
      public double getLatitude() {
        return latitude_;
      }
      /**
       * <code>double latitude = 2;</code>
       */
      public Builder setLatitude(double value) {
        
        latitude_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>double latitude = 2;</code>
       */
      public Builder clearLatitude() {
        
        latitude_ = 0D;
        onChanged();
        return this;
      }

      private int posConfidence_ ;
      /**
       * <pre>
       *位置精度等级
       * </pre>
       *
       * <code>uint32 pos_confidence = 3;</code>
       */
      public int getPosConfidence() {
        return posConfidence_;
      }
      /**
       * <pre>
       *位置精度等级
       * </pre>
       *
       * <code>uint32 pos_confidence = 3;</code>
       */
      public Builder setPosConfidence(int value) {
        
        posConfidence_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *位置精度等级
       * </pre>
       *
       * <code>uint32 pos_confidence = 3;</code>
       */
      public Builder clearPosConfidence() {
        
        posConfidence_ = 0;
        onChanged();
        return this;
      }

      private float speed_ ;
      /**
       * <pre>
       *单位：m/s
       * </pre>
       *
       * <code>float speed = 4;</code>
       */
      public float getSpeed() {
        return speed_;
      }
      /**
       * <pre>
       *单位：m/s
       * </pre>
       *
       * <code>float speed = 4;</code>
       */
      public Builder setSpeed(float value) {
        
        speed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *单位：m/s
       * </pre>
       *
       * <code>float speed = 4;</code>
       */
      public Builder clearSpeed() {
        
        speed_ = 0F;
        onChanged();
        return this;
      }

      private int speedConfidence_ ;
      /**
       * <pre>
       *速度精度等级
       * </pre>
       *
       * <code>uint32 speed_confidence = 5;</code>
       */
      public int getSpeedConfidence() {
        return speedConfidence_;
      }
      /**
       * <pre>
       *速度精度等级
       * </pre>
       *
       * <code>uint32 speed_confidence = 5;</code>
       */
      public Builder setSpeedConfidence(int value) {
        
        speedConfidence_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *速度精度等级
       * </pre>
       *
       * <code>uint32 speed_confidence = 5;</code>
       */
      public Builder clearSpeedConfidence() {
        
        speedConfidence_ = 0;
        onChanged();
        return this;
      }

      private float heading_ ;
      /**
       * <pre>
       *航向角
       * </pre>
       *
       * <code>float heading = 6;</code>
       */
      public float getHeading() {
        return heading_;
      }
      /**
       * <pre>
       *航向角
       * </pre>
       *
       * <code>float heading = 6;</code>
       */
      public Builder setHeading(float value) {
        
        heading_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *航向角
       * </pre>
       *
       * <code>float heading = 6;</code>
       */
      public Builder clearHeading() {
        
        heading_ = 0F;
        onChanged();
        return this;
      }

      private int headConfidence_ ;
      /**
       * <pre>
       *航向角精度等级
       * </pre>
       *
       * <code>uint32 head_confidence = 7;</code>
       */
      public int getHeadConfidence() {
        return headConfidence_;
      }
      /**
       * <pre>
       *航向角精度等级
       * </pre>
       *
       * <code>uint32 head_confidence = 7;</code>
       */
      public Builder setHeadConfidence(int value) {
        
        headConfidence_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *航向角精度等级
       * </pre>
       *
       * <code>uint32 head_confidence = 7;</code>
       */
      public Builder clearHeadConfidence() {
        
        headConfidence_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:road.data.proto.HistLoc)
    }

    // @@protoc_insertion_point(class_scope:road.data.proto.HistLoc)
    private static final HistLoc DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new HistLoc();
    }

    public static HistLoc getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<HistLoc>
        PARSER = new com.google.protobuf.AbstractParser<HistLoc>() {
      public HistLoc parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new HistLoc(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<HistLoc> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<HistLoc> getParserForType() {
      return PARSER;
    }

    public HistLoc getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FilterInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:road.data.proto.FilterInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *表示后续的协方差矩阵由 N 个状态量构建而成。
     *无法给出协方差矩阵时，此值值为 0，后续状态量所在序号、状态量协方差数据长度为 0（无该区域的数据）。
     * </pre>
     *
     * <code>uint32 dimension = 1;</code>
     */
    int getDimension();

    /**
     * <pre>
     *构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
     *每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
     *如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
     *因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
     * </pre>
     *
     * <code>repeated uint32 var_n_index = 2;</code>
     */
    java.util.List<Integer> getVarNIndexList();
    /**
     * <pre>
     *构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
     *每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
     *如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
     *因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
     * </pre>
     *
     * <code>repeated uint32 var_n_index = 2;</code>
     */
    int getVarNIndexCount();
    /**
     * <pre>
     *构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
     *每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
     *如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
     *因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
     * </pre>
     *
     * <code>repeated uint32 var_n_index = 2;</code>
     */
    int getVarNIndex(int index);

    /**
     * <pre>
     *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
     *取的数值从上向下、从左向右顺序排列
     * </pre>
     *
     * <code>.road.data.proto.Cov covs = 3;</code>
     */
    boolean hasCovs();
    /**
     * <pre>
     *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
     *取的数值从上向下、从左向右顺序排列
     * </pre>
     *
     * <code>.road.data.proto.Cov covs = 3;</code>
     */
    Cov getCovs();
    /**
     * <pre>
     *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
     *取的数值从上向下、从左向右顺序排列
     * </pre>
     *
     * <code>.road.data.proto.Cov covs = 3;</code>
     */
    CovOrBuilder getCovsOrBuilder();

    /**
     * <pre>
     *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
     * </pre>
     *
     * <code>.road.data.proto.Cov covs_pred = 4;</code>
     */
    boolean hasCovsPred();
    /**
     * <pre>
     *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
     * </pre>
     *
     * <code>.road.data.proto.Cov covs_pred = 4;</code>
     */
    Cov getCovsPred();
    /**
     * <pre>
     *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
     * </pre>
     *
     * <code>.road.data.proto.Cov covs_pred = 4;</code>
     */
    CovOrBuilder getCovsPredOrBuilder();

    /**
     * <pre>
     *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
     *状态量确定了此处共计的数据长度。中定义的类型相同。
     *当无法提供此数据域的值时，总长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
     */
    java.util.List<Cov>
        getVarPredList();
    /**
     * <pre>
     *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
     *状态量确定了此处共计的数据长度。中定义的类型相同。
     *当无法提供此数据域的值时，总长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
     */
    Cov getVarPred(int index);
    /**
     * <pre>
     *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
     *状态量确定了此处共计的数据长度。中定义的类型相同。
     *当无法提供此数据域的值时，总长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
     */
    int getVarPredCount();
    /**
     * <pre>
     *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
     *状态量确定了此处共计的数据长度。中定义的类型相同。
     *当无法提供此数据域的值时，总长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
     */
    java.util.List<? extends CovOrBuilder>
        getVarPredOrBuilderList();
    /**
     * <pre>
     *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
     *状态量确定了此处共计的数据长度。中定义的类型相同。
     *当无法提供此数据域的值时，总长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
     */
    CovOrBuilder getVarPredOrBuilder(
        int index);
  }
  /**
   * <pre>
   *滤波信息数据
   * </pre>
   *
   * Protobuf type {@code road.data.proto.FilterInfo}
   */
  public  static final class FilterInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:road.data.proto.FilterInfo)
      FilterInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FilterInfo.newBuilder() to construct.
    private FilterInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FilterInfo() {
      dimension_ = 0;
      varNIndex_ = java.util.Collections.emptyList();
      varPred_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FilterInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              dimension_ = input.readUInt32();
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                varNIndex_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000002;
              }
              varNIndex_.add(input.readUInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002) && input.getBytesUntilLimit() > 0) {
                varNIndex_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                varNIndex_.add(input.readUInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 26: {
              Cov.Builder subBuilder = null;
              if (covs_ != null) {
                subBuilder = covs_.toBuilder();
              }
              covs_ = input.readMessage(Cov.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(covs_);
                covs_ = subBuilder.buildPartial();
              }

              break;
            }
            case 34: {
              Cov.Builder subBuilder = null;
              if (covsPred_ != null) {
                subBuilder = covsPred_.toBuilder();
              }
              covsPred_ = input.readMessage(Cov.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(covsPred_);
                covsPred_ = subBuilder.buildPartial();
              }

              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                varPred_ = new java.util.ArrayList<Cov>();
                mutable_bitField0_ |= 0x00000010;
              }
              varPred_.add(
                  input.readMessage(Cov.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          varNIndex_ = java.util.Collections.unmodifiableList(varNIndex_);
        }
        if (((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
          varPred_ = java.util.Collections.unmodifiableList(varPred_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return Percep.internal_static_road_data_proto_FilterInfo_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return Percep.internal_static_road_data_proto_FilterInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              FilterInfo.class, Builder.class);
    }

    private int bitField0_;
    public static final int DIMENSION_FIELD_NUMBER = 1;
    private int dimension_;
    /**
     * <pre>
     *表示后续的协方差矩阵由 N 个状态量构建而成。
     *无法给出协方差矩阵时，此值值为 0，后续状态量所在序号、状态量协方差数据长度为 0（无该区域的数据）。
     * </pre>
     *
     * <code>uint32 dimension = 1;</code>
     */
    public int getDimension() {
      return dimension_;
    }

    public static final int VAR_N_INDEX_FIELD_NUMBER = 2;
    private java.util.List<Integer> varNIndex_;
    /**
     * <pre>
     *构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
     *每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
     *如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
     *因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
     * </pre>
     *
     * <code>repeated uint32 var_n_index = 2;</code>
     */
    public java.util.List<Integer>
        getVarNIndexList() {
      return varNIndex_;
    }
    /**
     * <pre>
     *构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
     *每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
     *如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
     *因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
     * </pre>
     *
     * <code>repeated uint32 var_n_index = 2;</code>
     */
    public int getVarNIndexCount() {
      return varNIndex_.size();
    }
    /**
     * <pre>
     *构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
     *每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
     *如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
     *因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
     * </pre>
     *
     * <code>repeated uint32 var_n_index = 2;</code>
     */
    public int getVarNIndex(int index) {
      return varNIndex_.get(index);
    }
    private int varNIndexMemoizedSerializedSize = -1;

    public static final int COVS_FIELD_NUMBER = 3;
    private Cov covs_;
    /**
     * <pre>
     *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
     *取的数值从上向下、从左向右顺序排列
     * </pre>
     *
     * <code>.road.data.proto.Cov covs = 3;</code>
     */
    public boolean hasCovs() {
      return covs_ != null;
    }
    /**
     * <pre>
     *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
     *取的数值从上向下、从左向右顺序排列
     * </pre>
     *
     * <code>.road.data.proto.Cov covs = 3;</code>
     */
    public Cov getCovs() {
      return covs_ == null ? Cov.getDefaultInstance() : covs_;
    }
    /**
     * <pre>
     *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
     *取的数值从上向下、从左向右顺序排列
     * </pre>
     *
     * <code>.road.data.proto.Cov covs = 3;</code>
     */
    public CovOrBuilder getCovsOrBuilder() {
      return getCovs();
    }

    public static final int COVS_PRED_FIELD_NUMBER = 4;
    private Cov covsPred_;
    /**
     * <pre>
     *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
     * </pre>
     *
     * <code>.road.data.proto.Cov covs_pred = 4;</code>
     */
    public boolean hasCovsPred() {
      return covsPred_ != null;
    }
    /**
     * <pre>
     *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
     * </pre>
     *
     * <code>.road.data.proto.Cov covs_pred = 4;</code>
     */
    public Cov getCovsPred() {
      return covsPred_ == null ? Cov.getDefaultInstance() : covsPred_;
    }
    /**
     * <pre>
     *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
     * </pre>
     *
     * <code>.road.data.proto.Cov covs_pred = 4;</code>
     */
    public CovOrBuilder getCovsPredOrBuilder() {
      return getCovsPred();
    }

    public static final int VAR_PRED_FIELD_NUMBER = 5;
    private java.util.List<Cov> varPred_;
    /**
     * <pre>
     *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
     *状态量确定了此处共计的数据长度。中定义的类型相同。
     *当无法提供此数据域的值时，总长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
     */
    public java.util.List<Cov> getVarPredList() {
      return varPred_;
    }
    /**
     * <pre>
     *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
     *状态量确定了此处共计的数据长度。中定义的类型相同。
     *当无法提供此数据域的值时，总长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
     */
    public java.util.List<? extends CovOrBuilder>
        getVarPredOrBuilderList() {
      return varPred_;
    }
    /**
     * <pre>
     *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
     *状态量确定了此处共计的数据长度。中定义的类型相同。
     *当无法提供此数据域的值时，总长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
     */
    public int getVarPredCount() {
      return varPred_.size();
    }
    /**
     * <pre>
     *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
     *状态量确定了此处共计的数据长度。中定义的类型相同。
     *当无法提供此数据域的值时，总长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
     */
    public Cov getVarPred(int index) {
      return varPred_.get(index);
    }
    /**
     * <pre>
     *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
     *状态量确定了此处共计的数据长度。中定义的类型相同。
     *当无法提供此数据域的值时，总长度为0。
     * </pre>
     *
     * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
     */
    public CovOrBuilder getVarPredOrBuilder(
        int index) {
      return varPred_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (dimension_ != 0) {
        output.writeUInt32(1, dimension_);
      }
      if (getVarNIndexList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(varNIndexMemoizedSerializedSize);
      }
      for (int i = 0; i < varNIndex_.size(); i++) {
        output.writeUInt32NoTag(varNIndex_.get(i));
      }
      if (covs_ != null) {
        output.writeMessage(3, getCovs());
      }
      if (covsPred_ != null) {
        output.writeMessage(4, getCovsPred());
      }
      for (int i = 0; i < varPred_.size(); i++) {
        output.writeMessage(5, varPred_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (dimension_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, dimension_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < varNIndex_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(varNIndex_.get(i));
        }
        size += dataSize;
        if (!getVarNIndexList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        varNIndexMemoizedSerializedSize = dataSize;
      }
      if (covs_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getCovs());
      }
      if (covsPred_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getCovsPred());
      }
      for (int i = 0; i < varPred_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, varPred_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof FilterInfo)) {
        return super.equals(obj);
      }
      FilterInfo other = (FilterInfo) obj;

      boolean result = true;
      result = result && (getDimension()
          == other.getDimension());
      result = result && getVarNIndexList()
          .equals(other.getVarNIndexList());
      result = result && (hasCovs() == other.hasCovs());
      if (hasCovs()) {
        result = result && getCovs()
            .equals(other.getCovs());
      }
      result = result && (hasCovsPred() == other.hasCovsPred());
      if (hasCovsPred()) {
        result = result && getCovsPred()
            .equals(other.getCovsPred());
      }
      result = result && getVarPredList()
          .equals(other.getVarPredList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + DIMENSION_FIELD_NUMBER;
      hash = (53 * hash) + getDimension();
      if (getVarNIndexCount() > 0) {
        hash = (37 * hash) + VAR_N_INDEX_FIELD_NUMBER;
        hash = (53 * hash) + getVarNIndexList().hashCode();
      }
      if (hasCovs()) {
        hash = (37 * hash) + COVS_FIELD_NUMBER;
        hash = (53 * hash) + getCovs().hashCode();
      }
      if (hasCovsPred()) {
        hash = (37 * hash) + COVS_PRED_FIELD_NUMBER;
        hash = (53 * hash) + getCovsPred().hashCode();
      }
      if (getVarPredCount() > 0) {
        hash = (37 * hash) + VAR_PRED_FIELD_NUMBER;
        hash = (53 * hash) + getVarPredList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static FilterInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static FilterInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static FilterInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static FilterInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static FilterInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static FilterInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static FilterInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static FilterInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static FilterInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static FilterInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static FilterInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static FilterInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(FilterInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *滤波信息数据
     * </pre>
     *
     * Protobuf type {@code road.data.proto.FilterInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:road.data.proto.FilterInfo)
        FilterInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return Percep.internal_static_road_data_proto_FilterInfo_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return Percep.internal_static_road_data_proto_FilterInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                FilterInfo.class, Builder.class);
      }

      // Construct using road.data.proto.Percep.FilterInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getVarPredFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        dimension_ = 0;

        varNIndex_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        if (covsBuilder_ == null) {
          covs_ = null;
        } else {
          covs_ = null;
          covsBuilder_ = null;
        }
        if (covsPredBuilder_ == null) {
          covsPred_ = null;
        } else {
          covsPred_ = null;
          covsPredBuilder_ = null;
        }
        if (varPredBuilder_ == null) {
          varPred_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
        } else {
          varPredBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return Percep.internal_static_road_data_proto_FilterInfo_descriptor;
      }

      public FilterInfo getDefaultInstanceForType() {
        return FilterInfo.getDefaultInstance();
      }

      public FilterInfo build() {
        FilterInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public FilterInfo buildPartial() {
        FilterInfo result = new FilterInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.dimension_ = dimension_;
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          varNIndex_ = java.util.Collections.unmodifiableList(varNIndex_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.varNIndex_ = varNIndex_;
        if (covsBuilder_ == null) {
          result.covs_ = covs_;
        } else {
          result.covs_ = covsBuilder_.build();
        }
        if (covsPredBuilder_ == null) {
          result.covsPred_ = covsPred_;
        } else {
          result.covsPred_ = covsPredBuilder_.build();
        }
        if (varPredBuilder_ == null) {
          if (((bitField0_ & 0x00000010) == 0x00000010)) {
            varPred_ = java.util.Collections.unmodifiableList(varPred_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.varPred_ = varPred_;
        } else {
          result.varPred_ = varPredBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof FilterInfo) {
          return mergeFrom((FilterInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(FilterInfo other) {
        if (other == FilterInfo.getDefaultInstance()) return this;
        if (other.getDimension() != 0) {
          setDimension(other.getDimension());
        }
        if (!other.varNIndex_.isEmpty()) {
          if (varNIndex_.isEmpty()) {
            varNIndex_ = other.varNIndex_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureVarNIndexIsMutable();
            varNIndex_.addAll(other.varNIndex_);
          }
          onChanged();
        }
        if (other.hasCovs()) {
          mergeCovs(other.getCovs());
        }
        if (other.hasCovsPred()) {
          mergeCovsPred(other.getCovsPred());
        }
        if (varPredBuilder_ == null) {
          if (!other.varPred_.isEmpty()) {
            if (varPred_.isEmpty()) {
              varPred_ = other.varPred_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureVarPredIsMutable();
              varPred_.addAll(other.varPred_);
            }
            onChanged();
          }
        } else {
          if (!other.varPred_.isEmpty()) {
            if (varPredBuilder_.isEmpty()) {
              varPredBuilder_.dispose();
              varPredBuilder_ = null;
              varPred_ = other.varPred_;
              bitField0_ = (bitField0_ & ~0x00000010);
              varPredBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getVarPredFieldBuilder() : null;
            } else {
              varPredBuilder_.addAllMessages(other.varPred_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        FilterInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (FilterInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int dimension_ ;
      /**
       * <pre>
       *表示后续的协方差矩阵由 N 个状态量构建而成。
       *无法给出协方差矩阵时，此值值为 0，后续状态量所在序号、状态量协方差数据长度为 0（无该区域的数据）。
       * </pre>
       *
       * <code>uint32 dimension = 1;</code>
       */
      public int getDimension() {
        return dimension_;
      }
      /**
       * <pre>
       *表示后续的协方差矩阵由 N 个状态量构建而成。
       *无法给出协方差矩阵时，此值值为 0，后续状态量所在序号、状态量协方差数据长度为 0（无该区域的数据）。
       * </pre>
       *
       * <code>uint32 dimension = 1;</code>
       */
      public Builder setDimension(int value) {
        
        dimension_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *表示后续的协方差矩阵由 N 个状态量构建而成。
       *无法给出协方差矩阵时，此值值为 0，后续状态量所在序号、状态量协方差数据长度为 0（无该区域的数据）。
       * </pre>
       *
       * <code>uint32 dimension = 1;</code>
       */
      public Builder clearDimension() {
        
        dimension_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<Integer> varNIndex_ = java.util.Collections.emptyList();
      private void ensureVarNIndexIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          varNIndex_ = new java.util.ArrayList<Integer>(varNIndex_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <pre>
       *构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
       *每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
       *如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
       *因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
       * </pre>
       *
       * <code>repeated uint32 var_n_index = 2;</code>
       */
      public java.util.List<Integer>
          getVarNIndexList() {
        return java.util.Collections.unmodifiableList(varNIndex_);
      }
      /**
       * <pre>
       *构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
       *每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
       *如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
       *因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
       * </pre>
       *
       * <code>repeated uint32 var_n_index = 2;</code>
       */
      public int getVarNIndexCount() {
        return varNIndex_.size();
      }
      /**
       * <pre>
       *构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
       *每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
       *如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
       *因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
       * </pre>
       *
       * <code>repeated uint32 var_n_index = 2;</code>
       */
      public int getVarNIndex(int index) {
        return varNIndex_.get(index);
      }
      /**
       * <pre>
       *构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
       *每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
       *如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
       *因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
       * </pre>
       *
       * <code>repeated uint32 var_n_index = 2;</code>
       */
      public Builder setVarNIndex(
          int index, int value) {
        ensureVarNIndexIsMutable();
        varNIndex_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
       *每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
       *如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
       *因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
       * </pre>
       *
       * <code>repeated uint32 var_n_index = 2;</code>
       */
      public Builder addVarNIndex(int value) {
        ensureVarNIndexIsMutable();
        varNIndex_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
       *每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
       *如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
       *因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
       * </pre>
       *
       * <code>repeated uint32 var_n_index = 2;</code>
       */
      public Builder addAllVarNIndex(
          Iterable<? extends Integer> values) {
        ensureVarNIndexIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, varNIndex_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
       *每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
       *如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
       *因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
       * </pre>
       *
       * <code>repeated uint32 var_n_index = 2;</code>
       */
      public Builder clearVarNIndex() {
        varNIndex_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private Cov covs_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          Cov, Cov.Builder, CovOrBuilder> covsBuilder_;
      /**
       * <pre>
       *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
       *取的数值从上向下、从左向右顺序排列
       * </pre>
       *
       * <code>.road.data.proto.Cov covs = 3;</code>
       */
      public boolean hasCovs() {
        return covsBuilder_ != null || covs_ != null;
      }
      /**
       * <pre>
       *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
       *取的数值从上向下、从左向右顺序排列
       * </pre>
       *
       * <code>.road.data.proto.Cov covs = 3;</code>
       */
      public Cov getCovs() {
        if (covsBuilder_ == null) {
          return covs_ == null ? Cov.getDefaultInstance() : covs_;
        } else {
          return covsBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
       *取的数值从上向下、从左向右顺序排列
       * </pre>
       *
       * <code>.road.data.proto.Cov covs = 3;</code>
       */
      public Builder setCovs(Cov value) {
        if (covsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          covs_ = value;
          onChanged();
        } else {
          covsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
       *取的数值从上向下、从左向右顺序排列
       * </pre>
       *
       * <code>.road.data.proto.Cov covs = 3;</code>
       */
      public Builder setCovs(
          Cov.Builder builderForValue) {
        if (covsBuilder_ == null) {
          covs_ = builderForValue.build();
          onChanged();
        } else {
          covsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
       *取的数值从上向下、从左向右顺序排列
       * </pre>
       *
       * <code>.road.data.proto.Cov covs = 3;</code>
       */
      public Builder mergeCovs(Cov value) {
        if (covsBuilder_ == null) {
          if (covs_ != null) {
            covs_ =
              Cov.newBuilder(covs_).mergeFrom(value).buildPartial();
          } else {
            covs_ = value;
          }
          onChanged();
        } else {
          covsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
       *取的数值从上向下、从左向右顺序排列
       * </pre>
       *
       * <code>.road.data.proto.Cov covs = 3;</code>
       */
      public Builder clearCovs() {
        if (covsBuilder_ == null) {
          covs_ = null;
          onChanged();
        } else {
          covs_ = null;
          covsBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
       *取的数值从上向下、从左向右顺序排列
       * </pre>
       *
       * <code>.road.data.proto.Cov covs = 3;</code>
       */
      public Cov.Builder getCovsBuilder() {
        
        onChanged();
        return getCovsFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
       *取的数值从上向下、从左向右顺序排列
       * </pre>
       *
       * <code>.road.data.proto.Cov covs = 3;</code>
       */
      public CovOrBuilder getCovsOrBuilder() {
        if (covsBuilder_ != null) {
          return covsBuilder_.getMessageOrBuilder();
        } else {
          return covs_ == null ?
              Cov.getDefaultInstance() : covs_;
        }
      }
      /**
       * <pre>
       *由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
       *取的数值从上向下、从左向右顺序排列
       * </pre>
       *
       * <code>.road.data.proto.Cov covs = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          Cov, Cov.Builder, CovOrBuilder>
          getCovsFieldBuilder() {
        if (covsBuilder_ == null) {
          covsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              Cov, Cov.Builder, CovOrBuilder>(
                  getCovs(),
                  getParentForChildren(),
                  isClean());
          covs_ = null;
        }
        return covsBuilder_;
      }

      private Cov covsPred_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          Cov, Cov.Builder, CovOrBuilder> covsPredBuilder_;
      /**
       * <pre>
       *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
       * </pre>
       *
       * <code>.road.data.proto.Cov covs_pred = 4;</code>
       */
      public boolean hasCovsPred() {
        return covsPredBuilder_ != null || covsPred_ != null;
      }
      /**
       * <pre>
       *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
       * </pre>
       *
       * <code>.road.data.proto.Cov covs_pred = 4;</code>
       */
      public Cov getCovsPred() {
        if (covsPredBuilder_ == null) {
          return covsPred_ == null ? Cov.getDefaultInstance() : covsPred_;
        } else {
          return covsPredBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
       * </pre>
       *
       * <code>.road.data.proto.Cov covs_pred = 4;</code>
       */
      public Builder setCovsPred(Cov value) {
        if (covsPredBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          covsPred_ = value;
          onChanged();
        } else {
          covsPredBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
       * </pre>
       *
       * <code>.road.data.proto.Cov covs_pred = 4;</code>
       */
      public Builder setCovsPred(
          Cov.Builder builderForValue) {
        if (covsPredBuilder_ == null) {
          covsPred_ = builderForValue.build();
          onChanged();
        } else {
          covsPredBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
       * </pre>
       *
       * <code>.road.data.proto.Cov covs_pred = 4;</code>
       */
      public Builder mergeCovsPred(Cov value) {
        if (covsPredBuilder_ == null) {
          if (covsPred_ != null) {
            covsPred_ =
              Cov.newBuilder(covsPred_).mergeFrom(value).buildPartial();
          } else {
            covsPred_ = value;
          }
          onChanged();
        } else {
          covsPredBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
       * </pre>
       *
       * <code>.road.data.proto.Cov covs_pred = 4;</code>
       */
      public Builder clearCovsPred() {
        if (covsPredBuilder_ == null) {
          covsPred_ = null;
          onChanged();
        } else {
          covsPred_ = null;
          covsPredBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
       * </pre>
       *
       * <code>.road.data.proto.Cov covs_pred = 4;</code>
       */
      public Cov.Builder getCovsPredBuilder() {
        
        onChanged();
        return getCovsPredFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
       * </pre>
       *
       * <code>.road.data.proto.Cov covs_pred = 4;</code>
       */
      public CovOrBuilder getCovsPredOrBuilder() {
        if (covsPredBuilder_ != null) {
          return covsPredBuilder_.getMessageOrBuilder();
        } else {
          return covsPred_ == null ?
              Cov.getDefaultInstance() : covsPred_;
        }
      }
      /**
       * <pre>
       *矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
       * </pre>
       *
       * <code>.road.data.proto.Cov covs_pred = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          Cov, Cov.Builder, CovOrBuilder>
          getCovsPredFieldBuilder() {
        if (covsPredBuilder_ == null) {
          covsPredBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              Cov, Cov.Builder, CovOrBuilder>(
                  getCovsPred(),
                  getParentForChildren(),
                  isClean());
          covsPred_ = null;
        }
        return covsPredBuilder_;
      }

      private java.util.List<Cov> varPred_ =
        java.util.Collections.emptyList();
      private void ensureVarPredIsMutable() {
        if (!((bitField0_ & 0x00000010) == 0x00000010)) {
          varPred_ = new java.util.ArrayList<Cov>(varPred_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          Cov, Cov.Builder, CovOrBuilder> varPredBuilder_;

      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public java.util.List<Cov> getVarPredList() {
        if (varPredBuilder_ == null) {
          return java.util.Collections.unmodifiableList(varPred_);
        } else {
          return varPredBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public int getVarPredCount() {
        if (varPredBuilder_ == null) {
          return varPred_.size();
        } else {
          return varPredBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public Cov getVarPred(int index) {
        if (varPredBuilder_ == null) {
          return varPred_.get(index);
        } else {
          return varPredBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public Builder setVarPred(
          int index, Cov value) {
        if (varPredBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureVarPredIsMutable();
          varPred_.set(index, value);
          onChanged();
        } else {
          varPredBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public Builder setVarPred(
          int index, Cov.Builder builderForValue) {
        if (varPredBuilder_ == null) {
          ensureVarPredIsMutable();
          varPred_.set(index, builderForValue.build());
          onChanged();
        } else {
          varPredBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public Builder addVarPred(Cov value) {
        if (varPredBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureVarPredIsMutable();
          varPred_.add(value);
          onChanged();
        } else {
          varPredBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public Builder addVarPred(
          int index, Cov value) {
        if (varPredBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureVarPredIsMutable();
          varPred_.add(index, value);
          onChanged();
        } else {
          varPredBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public Builder addVarPred(
          Cov.Builder builderForValue) {
        if (varPredBuilder_ == null) {
          ensureVarPredIsMutable();
          varPred_.add(builderForValue.build());
          onChanged();
        } else {
          varPredBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public Builder addVarPred(
          int index, Cov.Builder builderForValue) {
        if (varPredBuilder_ == null) {
          ensureVarPredIsMutable();
          varPred_.add(index, builderForValue.build());
          onChanged();
        } else {
          varPredBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public Builder addAllVarPred(
          Iterable<? extends Cov> values) {
        if (varPredBuilder_ == null) {
          ensureVarPredIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, varPred_);
          onChanged();
        } else {
          varPredBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public Builder clearVarPred() {
        if (varPredBuilder_ == null) {
          varPred_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          varPredBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public Builder removeVarPred(int index) {
        if (varPredBuilder_ == null) {
          ensureVarPredIsMutable();
          varPred_.remove(index);
          onChanged();
        } else {
          varPredBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public Cov.Builder getVarPredBuilder(
          int index) {
        return getVarPredFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public CovOrBuilder getVarPredOrBuilder(
          int index) {
        if (varPredBuilder_ == null) {
          return varPred_.get(index);  } else {
          return varPredBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public java.util.List<? extends CovOrBuilder>
           getVarPredOrBuilderList() {
        if (varPredBuilder_ != null) {
          return varPredBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(varPred_);
        }
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public Cov.Builder addVarPredBuilder() {
        return getVarPredFieldBuilder().addBuilder(
            Cov.getDefaultInstance());
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public Cov.Builder addVarPredBuilder(
          int index) {
        return getVarPredFieldBuilder().addBuilder(
            index, Cov.getDefaultInstance());
      }
      /**
       * <pre>
       *状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
       *状态量确定了此处共计的数据长度。中定义的类型相同。
       *当无法提供此数据域的值时，总长度为0。
       * </pre>
       *
       * <code>repeated .road.data.proto.Cov var_pred = 5;</code>
       */
      public java.util.List<Cov.Builder>
           getVarPredBuilderList() {
        return getVarPredFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          Cov, Cov.Builder, CovOrBuilder>
          getVarPredFieldBuilder() {
        if (varPredBuilder_ == null) {
          varPredBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              Cov, Cov.Builder, CovOrBuilder>(
                  varPred_,
                  ((bitField0_ & 0x00000010) == 0x00000010),
                  getParentForChildren(),
                  isClean());
          varPred_ = null;
        }
        return varPredBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:road.data.proto.FilterInfo)
    }

    // @@protoc_insertion_point(class_scope:road.data.proto.FilterInfo)
    private static final FilterInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new FilterInfo();
    }

    public static FilterInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FilterInfo>
        PARSER = new com.google.protobuf.AbstractParser<FilterInfo>() {
      public FilterInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FilterInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FilterInfo> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<FilterInfo> getParserForType() {
      return PARSER;
    }

    public FilterInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CovOrBuilder extends
      // @@protoc_insertion_point(interface_extends:road.data.proto.Cov)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated float covs = 1;</code>
     */
    java.util.List<Float> getCovsList();
    /**
     * <code>repeated float covs = 1;</code>
     */
    int getCovsCount();
    /**
     * <code>repeated float covs = 1;</code>
     */
    float getCovs(int index);
  }
  /**
   * <pre>
   *状态量协方差数据
   * </pre>
   *
   * Protobuf type {@code road.data.proto.Cov}
   */
  public  static final class Cov extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:road.data.proto.Cov)
      CovOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Cov.newBuilder() to construct.
    private Cov(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Cov() {
      covs_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Cov(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 13: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                covs_ = new java.util.ArrayList<Float>();
                mutable_bitField0_ |= 0x00000001;
              }
              covs_.add(input.readFloat());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                covs_ = new java.util.ArrayList<Float>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                covs_.add(input.readFloat());
              }
              input.popLimit(limit);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          covs_ = java.util.Collections.unmodifiableList(covs_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return Percep.internal_static_road_data_proto_Cov_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return Percep.internal_static_road_data_proto_Cov_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Cov.class, Builder.class);
    }

    public static final int COVS_FIELD_NUMBER = 1;
    private java.util.List<Float> covs_;
    /**
     * <code>repeated float covs = 1;</code>
     */
    public java.util.List<Float>
        getCovsList() {
      return covs_;
    }
    /**
     * <code>repeated float covs = 1;</code>
     */
    public int getCovsCount() {
      return covs_.size();
    }
    /**
     * <code>repeated float covs = 1;</code>
     */
    public float getCovs(int index) {
      return covs_.get(index);
    }
    private int covsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getCovsList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(covsMemoizedSerializedSize);
      }
      for (int i = 0; i < covs_.size(); i++) {
        output.writeFloatNoTag(covs_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        dataSize = 4 * getCovsList().size();
        size += dataSize;
        if (!getCovsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        covsMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof Cov)) {
        return super.equals(obj);
      }
      Cov other = (Cov) obj;

      boolean result = true;
      result = result && getCovsList()
          .equals(other.getCovsList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getCovsCount() > 0) {
        hash = (37 * hash) + COVS_FIELD_NUMBER;
        hash = (53 * hash) + getCovsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static Cov parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Cov parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Cov parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Cov parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Cov parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Cov parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Cov parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static Cov parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static Cov parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static Cov parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static Cov parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static Cov parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(Cov prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *状态量协方差数据
     * </pre>
     *
     * Protobuf type {@code road.data.proto.Cov}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:road.data.proto.Cov)
        CovOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return Percep.internal_static_road_data_proto_Cov_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return Percep.internal_static_road_data_proto_Cov_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                Cov.class, Builder.class);
      }

      // Construct using road.data.proto.Percep.Cov.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        covs_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return Percep.internal_static_road_data_proto_Cov_descriptor;
      }

      public Cov getDefaultInstanceForType() {
        return Cov.getDefaultInstance();
      }

      public Cov build() {
        Cov result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public Cov buildPartial() {
        Cov result = new Cov(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          covs_ = java.util.Collections.unmodifiableList(covs_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.covs_ = covs_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof Cov) {
          return mergeFrom((Cov)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(Cov other) {
        if (other == Cov.getDefaultInstance()) return this;
        if (!other.covs_.isEmpty()) {
          if (covs_.isEmpty()) {
            covs_ = other.covs_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureCovsIsMutable();
            covs_.addAll(other.covs_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        Cov parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (Cov) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<Float> covs_ = java.util.Collections.emptyList();
      private void ensureCovsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          covs_ = new java.util.ArrayList<Float>(covs_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated float covs = 1;</code>
       */
      public java.util.List<Float>
          getCovsList() {
        return java.util.Collections.unmodifiableList(covs_);
      }
      /**
       * <code>repeated float covs = 1;</code>
       */
      public int getCovsCount() {
        return covs_.size();
      }
      /**
       * <code>repeated float covs = 1;</code>
       */
      public float getCovs(int index) {
        return covs_.get(index);
      }
      /**
       * <code>repeated float covs = 1;</code>
       */
      public Builder setCovs(
          int index, float value) {
        ensureCovsIsMutable();
        covs_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated float covs = 1;</code>
       */
      public Builder addCovs(float value) {
        ensureCovsIsMutable();
        covs_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated float covs = 1;</code>
       */
      public Builder addAllCovs(
          Iterable<? extends Float> values) {
        ensureCovsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, covs_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated float covs = 1;</code>
       */
      public Builder clearCovs() {
        covs_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:road.data.proto.Cov)
    }

    // @@protoc_insertion_point(class_scope:road.data.proto.Cov)
    private static final Cov DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new Cov();
    }

    public static Cov getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Cov>
        PARSER = new com.google.protobuf.AbstractParser<Cov>() {
      public Cov parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Cov(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Cov> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<Cov> getParserForType() {
      return PARSER;
    }

    public Cov getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_road_data_proto_RsPerceptionInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_road_data_proto_RsPerceptionInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_road_data_proto_PerceptionObject_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_road_data_proto_PerceptionObject_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_road_data_proto_HistLoc_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_road_data_proto_HistLoc_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_road_data_proto_FilterInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_road_data_proto_FilterInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_road_data_proto_Cov_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_road_data_proto_Cov_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\020perception.proto\022\017road.data.proto\"\372\003\n\020" +
      "RsPerceptionInfo\022\022\n\nchannel_id\030\001 \001(\r\022\016\n\006" +
      "rcu_id\030\002 \001(\t\022\023\n\013device_type\030\003 \001(\r\022\021\n\tdev" +
      "ice_id\030\004 \001(\t\022\034\n\024timestamp_of_dev_out\030\005 \001" +
      "(\004\022\033\n\023timestamp_of_det_in\030\006 \001(\004\022\034\n\024times" +
      "tamp_of_det_out\030\007 \001(\004\022\021\n\tgnss_type\030\010 \001(\r" +
      "\0224\n\tobjective\030\t \003(\0132!.road.data.proto.Pe" +
      "rceptionObject\022\014\n\004slot\030\n \001(\r\022\014\n\004pole\030\013 \001" +
      "(\t\022\037\n\027timestamp_of_driver_out\030\014 \001(\004\022\033\n\023t" +
      "imestamp_of_per_in\030\r \001(\004\022\034\n\024timestamp_of" +
      "_per_out\030\016 \001(\004\022\025\n\redge_cloud_id\030\017 \001(\t\022\026\n" +
      "\016edge_timestamp\030\020 \001(\004\022\'\n\037timestamp_of_mu" +
      "lti_poles_det_in\030\021 \001(\004\022(\n timestamp_of_m" +
      "ulti_poles_det_out\030\022 \001(\004\"\262\007\n\020PerceptionO" +
      "bject\022\014\n\004uuid\030\001 \001(\t\022\016\n\006obj_id\030\002 \001(\r\022\014\n\004t" +
      "ype\030\003 \001(\r\022\016\n\006status\030\004 \001(\r\022\013\n\003len\030\005 \001(\002\022\r" +
      "\n\005width\030\006 \001(\002\022\016\n\006height\030\007 \001(\002\022\021\n\tlongitu" +
      "de\030\010 \001(\001\022\020\n\010latitude\030\t \001(\001\022\020\n\010loc_east\030\n" +
      " \001(\002\022\021\n\tloc_north\030\013 \001(\002\022\026\n\016pos_confidenc" +
      "e\030\014 \001(\r\022\021\n\televation\030\r \001(\001\022\027\n\017elev_confi" +
      "dence\030\016 \001(\r\022\r\n\005speed\030\017 \001(\002\022\030\n\020speed_conf" +
      "idence\030\020 \001(\r\022\022\n\nspeed_east\030\021 \001(\002\022\035\n\025spee" +
      "d_east_confidence\030\022 \001(\r\022\023\n\013speed_north\030\023" +
      " \001(\002\022\036\n\026speed_north_confidence\030\024 \001(\r\022\017\n\007" +
      "heading\030\025 \001(\002\022\027\n\017head_confidence\030\026 \001(\r\022\022" +
      "\n\naccel_vert\030\027 \001(\002\022\035\n\025accel_vert_confide" +
      "nce\030\030 \001(\r\022\025\n\rtracked_times\030\031 \001(\r\022\024\n\014hist" +
      "_loc_num\030\032 \001(\r\022+\n\thist_locs\030\033 \003(\0132\030.road" +
      ".data.proto.HistLoc\022\024\n\014pred_loc_num\030\034 \001(" +
      "\r\022+\n\tpred_locs\030\035 \003(\0132\030.road.data.proto.H" +
      "istLoc\022\017\n\007lane_id\030\036 \001(\005\022\030\n\020filter_info_t" +
      "ype\030\037 \001(\r\0220\n\013filter_info\030  \001(\0132\033.road.da" +
      "ta.proto.FilterInfo\022\023\n\013lenplate_no\030! \001(\r" +
      "\022\020\n\010plate_no\030\" \001(\t\022\022\n\nplate_type\030# \001(\r\022\023" +
      "\n\013plate_color\030$ \001(\r\022\021\n\tobj_color\030% \001(\r\022\022" +
      "\n\ndevice_idx\030& \001(\r\022\017\n\007road_id\030\' \001(\r\022\020\n\010d" +
      "istance\030( \001(\002\022\030\n\020parking_duration\030) \001(\r\022" +
      "\014\n\004pole\030* \001(\t\"\231\001\n\007HistLoc\022\021\n\tlongitude\030\001" +
      " \001(\001\022\020\n\010latitude\030\002 \001(\001\022\026\n\016pos_confidence" +
      "\030\003 \001(\r\022\r\n\005speed\030\004 \001(\002\022\030\n\020speed_confidenc" +
      "e\030\005 \001(\r\022\017\n\007heading\030\006 \001(\002\022\027\n\017head_confide" +
      "nce\030\007 \001(\r\"\251\001\n\nFilterInfo\022\021\n\tdimension\030\001 " +
      "\001(\r\022\023\n\013var_n_index\030\002 \003(\r\022\"\n\004covs\030\003 \001(\0132\024" +
      ".road.data.proto.Cov\022\'\n\tcovs_pred\030\004 \001(\0132" +
      "\024.road.data.proto.Cov\022&\n\010var_pred\030\005 \003(\0132" +
      "\024.road.data.proto.Cov\"\023\n\003Cov\022\014\n\004covs\030\001 \003" +
      "(\002b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_road_data_proto_RsPerceptionInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_road_data_proto_RsPerceptionInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_road_data_proto_RsPerceptionInfo_descriptor,
        new String[] { "ChannelId", "RcuId", "DeviceType", "DeviceId", "TimestampOfDevOut", "TimestampOfDetIn", "TimestampOfDetOut", "GnssType", "Objective", "Slot", "Pole", "TimestampOfDriverOut", "TimestampOfPerIn", "TimestampOfPerOut", "EdgeCloudId", "EdgeTimestamp", "TimestampOfMultiPolesDetIn", "TimestampOfMultiPolesDetOut", });
    internal_static_road_data_proto_PerceptionObject_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_road_data_proto_PerceptionObject_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_road_data_proto_PerceptionObject_descriptor,
        new String[] { "Uuid", "ObjId", "Type", "Status", "Len", "Width", "Height", "Longitude", "Latitude", "LocEast", "LocNorth", "PosConfidence", "Elevation", "ElevConfidence", "Speed", "SpeedConfidence", "SpeedEast", "SpeedEastConfidence", "SpeedNorth", "SpeedNorthConfidence", "Heading", "HeadConfidence", "AccelVert", "AccelVertConfidence", "TrackedTimes", "HistLocNum", "HistLocs", "PredLocNum", "PredLocs", "LaneId", "FilterInfoType", "FilterInfo", "LenplateNo", "PlateNo", "PlateType", "PlateColor", "ObjColor", "DeviceIdx", "RoadId", "Distance", "ParkingDuration", "Pole", });
    internal_static_road_data_proto_HistLoc_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_road_data_proto_HistLoc_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_road_data_proto_HistLoc_descriptor,
        new String[] { "Longitude", "Latitude", "PosConfidence", "Speed", "SpeedConfidence", "Heading", "HeadConfidence", });
    internal_static_road_data_proto_FilterInfo_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_road_data_proto_FilterInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_road_data_proto_FilterInfo_descriptor,
        new String[] { "Dimension", "VarNIndex", "Covs", "CovsPred", "VarPred", });
    internal_static_road_data_proto_Cov_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_road_data_proto_Cov_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_road_data_proto_Cov_descriptor,
        new String[] { "Covs", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
