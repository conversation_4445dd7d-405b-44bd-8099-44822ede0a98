// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *时段划分  DateTimeFilter    
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.PeriodictimeSpan}
 */
public  final class PeriodictimeSpan extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.PeriodictimeSpan)
    PeriodictimeSpanOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PeriodictimeSpan.newBuilder() to construct.
  private PeriodictimeSpan(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PeriodictimeSpan() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PeriodictimeSpan();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private PeriodictimeSpan(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            monthFilter_ = input.readInt32();
            break;
          }
          case 16: {

            dayFilter_ = input.readInt32();
            break;
          }
          case 24: {

            weekdayFilter_ = input.readInt32();
            break;
          }
          case 34: {
            road.data.proto.LocalTimePoint.Builder subBuilder = null;
            if (fromTimePoint_ != null) {
              subBuilder = fromTimePoint_.toBuilder();
            }
            fromTimePoint_ = input.readMessage(road.data.proto.LocalTimePoint.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(fromTimePoint_);
              fromTimePoint_ = subBuilder.buildPartial();
            }

            break;
          }
          case 42: {
            road.data.proto.LocalTimePoint.Builder subBuilder = null;
            if (toTimePoint_ != null) {
              subBuilder = toTimePoint_.toBuilder();
            }
            toTimePoint_ = input.readMessage(road.data.proto.LocalTimePoint.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(toTimePoint_);
              toTimePoint_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PeriodictimeSpan_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PeriodictimeSpan_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.PeriodictimeSpan.class, road.data.proto.PeriodictimeSpan.Builder.class);
  }

  public static final int MONTHFILTER_FIELD_NUMBER = 1;
  private int monthFilter_;
  /**
   * <pre>
   *包含所有符合条件的月份的集合
   * </pre>
   *
   * <code>int32 monthFilter = 1;</code>
   */
  public int getMonthFilter() {
    return monthFilter_;
  }

  public static final int DAYFILTER_FIELD_NUMBER = 2;
  private int dayFilter_;
  /**
   * <pre>
   *转化为二进制后，二进制左起第x位数字为1对应的含义：
   *RESERVED(0), JAN(1), FEB(2), MAR(3), APR(4), 
   *MAY(5), JUN(6), JUL(7), AUG(8), SEP(9), OCT(10), NOV(11), DEC(12)
   * </pre>
   *
   * <code>int32 dayFilter = 2;</code>
   */
  public int getDayFilter() {
    return dayFilter_;
  }

  public static final int WEEKDAYFILTER_FIELD_NUMBER = 3;
  private int weekdayFilter_;
  /**
   * <pre>
   *转化为二进制后，二进制左起第x位数字为1对应的含义：
   *RESERVED(0), 1(1), …, 30(30), 31(31)
   * </pre>
   *
   * <code>int32 weekdayFilter = 3;</code>
   */
  public int getWeekdayFilter() {
    return weekdayFilter_;
  }

  public static final int FROMTIMEPOINT_FIELD_NUMBER = 4;
  private road.data.proto.LocalTimePoint fromTimePoint_;
  /**
   * <pre>
   *转化为二进制后，二进制左起第x位数字为1对应的含义：
   *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
   */
  public boolean hasFromTimePoint() {
    return fromTimePoint_ != null;
  }
  /**
   * <pre>
   *转化为二进制后，二进制左起第x位数字为1对应的含义：
   *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
   */
  public road.data.proto.LocalTimePoint getFromTimePoint() {
    return fromTimePoint_ == null ? road.data.proto.LocalTimePoint.getDefaultInstance() : fromTimePoint_;
  }
  /**
   * <pre>
   *转化为二进制后，二进制左起第x位数字为1对应的含义：
   *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
   */
  public road.data.proto.LocalTimePointOrBuilder getFromTimePointOrBuilder() {
    return getFromTimePoint();
  }

  public static final int TOTIMEPOINT_FIELD_NUMBER = 5;
  private road.data.proto.LocalTimePoint toTimePoint_;
  /**
   * <pre>
   *信控方案执行当日的结束时刻
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
   */
  public boolean hasToTimePoint() {
    return toTimePoint_ != null;
  }
  /**
   * <pre>
   *信控方案执行当日的结束时刻
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
   */
  public road.data.proto.LocalTimePoint getToTimePoint() {
    return toTimePoint_ == null ? road.data.proto.LocalTimePoint.getDefaultInstance() : toTimePoint_;
  }
  /**
   * <pre>
   *信控方案执行当日的结束时刻
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
   */
  public road.data.proto.LocalTimePointOrBuilder getToTimePointOrBuilder() {
    return getToTimePoint();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (monthFilter_ != 0) {
      output.writeInt32(1, monthFilter_);
    }
    if (dayFilter_ != 0) {
      output.writeInt32(2, dayFilter_);
    }
    if (weekdayFilter_ != 0) {
      output.writeInt32(3, weekdayFilter_);
    }
    if (fromTimePoint_ != null) {
      output.writeMessage(4, getFromTimePoint());
    }
    if (toTimePoint_ != null) {
      output.writeMessage(5, getToTimePoint());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (monthFilter_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, monthFilter_);
    }
    if (dayFilter_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, dayFilter_);
    }
    if (weekdayFilter_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, weekdayFilter_);
    }
    if (fromTimePoint_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getFromTimePoint());
    }
    if (toTimePoint_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getToTimePoint());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.PeriodictimeSpan)) {
      return super.equals(obj);
    }
    road.data.proto.PeriodictimeSpan other = (road.data.proto.PeriodictimeSpan) obj;

    if (getMonthFilter()
        != other.getMonthFilter()) return false;
    if (getDayFilter()
        != other.getDayFilter()) return false;
    if (getWeekdayFilter()
        != other.getWeekdayFilter()) return false;
    if (hasFromTimePoint() != other.hasFromTimePoint()) return false;
    if (hasFromTimePoint()) {
      if (!getFromTimePoint()
          .equals(other.getFromTimePoint())) return false;
    }
    if (hasToTimePoint() != other.hasToTimePoint()) return false;
    if (hasToTimePoint()) {
      if (!getToTimePoint()
          .equals(other.getToTimePoint())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + MONTHFILTER_FIELD_NUMBER;
    hash = (53 * hash) + getMonthFilter();
    hash = (37 * hash) + DAYFILTER_FIELD_NUMBER;
    hash = (53 * hash) + getDayFilter();
    hash = (37 * hash) + WEEKDAYFILTER_FIELD_NUMBER;
    hash = (53 * hash) + getWeekdayFilter();
    if (hasFromTimePoint()) {
      hash = (37 * hash) + FROMTIMEPOINT_FIELD_NUMBER;
      hash = (53 * hash) + getFromTimePoint().hashCode();
    }
    if (hasToTimePoint()) {
      hash = (37 * hash) + TOTIMEPOINT_FIELD_NUMBER;
      hash = (53 * hash) + getToTimePoint().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.PeriodictimeSpan parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PeriodictimeSpan parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PeriodictimeSpan parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PeriodictimeSpan parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PeriodictimeSpan parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PeriodictimeSpan parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PeriodictimeSpan parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PeriodictimeSpan parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PeriodictimeSpan parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.PeriodictimeSpan parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PeriodictimeSpan parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PeriodictimeSpan parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.PeriodictimeSpan prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *时段划分  DateTimeFilter    
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.PeriodictimeSpan}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.PeriodictimeSpan)
      road.data.proto.PeriodictimeSpanOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PeriodictimeSpan_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PeriodictimeSpan_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.PeriodictimeSpan.class, road.data.proto.PeriodictimeSpan.Builder.class);
    }

    // Construct using road.data.proto.PeriodictimeSpan.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      monthFilter_ = 0;

      dayFilter_ = 0;

      weekdayFilter_ = 0;

      if (fromTimePointBuilder_ == null) {
        fromTimePoint_ = null;
      } else {
        fromTimePoint_ = null;
        fromTimePointBuilder_ = null;
      }
      if (toTimePointBuilder_ == null) {
        toTimePoint_ = null;
      } else {
        toTimePoint_ = null;
        toTimePointBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PeriodictimeSpan_descriptor;
    }

    @java.lang.Override
    public road.data.proto.PeriodictimeSpan getDefaultInstanceForType() {
      return road.data.proto.PeriodictimeSpan.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.PeriodictimeSpan build() {
      road.data.proto.PeriodictimeSpan result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.PeriodictimeSpan buildPartial() {
      road.data.proto.PeriodictimeSpan result = new road.data.proto.PeriodictimeSpan(this);
      result.monthFilter_ = monthFilter_;
      result.dayFilter_ = dayFilter_;
      result.weekdayFilter_ = weekdayFilter_;
      if (fromTimePointBuilder_ == null) {
        result.fromTimePoint_ = fromTimePoint_;
      } else {
        result.fromTimePoint_ = fromTimePointBuilder_.build();
      }
      if (toTimePointBuilder_ == null) {
        result.toTimePoint_ = toTimePoint_;
      } else {
        result.toTimePoint_ = toTimePointBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.PeriodictimeSpan) {
        return mergeFrom((road.data.proto.PeriodictimeSpan)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.PeriodictimeSpan other) {
      if (other == road.data.proto.PeriodictimeSpan.getDefaultInstance()) return this;
      if (other.getMonthFilter() != 0) {
        setMonthFilter(other.getMonthFilter());
      }
      if (other.getDayFilter() != 0) {
        setDayFilter(other.getDayFilter());
      }
      if (other.getWeekdayFilter() != 0) {
        setWeekdayFilter(other.getWeekdayFilter());
      }
      if (other.hasFromTimePoint()) {
        mergeFromTimePoint(other.getFromTimePoint());
      }
      if (other.hasToTimePoint()) {
        mergeToTimePoint(other.getToTimePoint());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.PeriodictimeSpan parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.PeriodictimeSpan) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int monthFilter_ ;
    /**
     * <pre>
     *包含所有符合条件的月份的集合
     * </pre>
     *
     * <code>int32 monthFilter = 1;</code>
     */
    public int getMonthFilter() {
      return monthFilter_;
    }
    /**
     * <pre>
     *包含所有符合条件的月份的集合
     * </pre>
     *
     * <code>int32 monthFilter = 1;</code>
     */
    public Builder setMonthFilter(int value) {
      
      monthFilter_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *包含所有符合条件的月份的集合
     * </pre>
     *
     * <code>int32 monthFilter = 1;</code>
     */
    public Builder clearMonthFilter() {
      
      monthFilter_ = 0;
      onChanged();
      return this;
    }

    private int dayFilter_ ;
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *RESERVED(0), JAN(1), FEB(2), MAR(3), APR(4), 
     *MAY(5), JUN(6), JUL(7), AUG(8), SEP(9), OCT(10), NOV(11), DEC(12)
     * </pre>
     *
     * <code>int32 dayFilter = 2;</code>
     */
    public int getDayFilter() {
      return dayFilter_;
    }
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *RESERVED(0), JAN(1), FEB(2), MAR(3), APR(4), 
     *MAY(5), JUN(6), JUL(7), AUG(8), SEP(9), OCT(10), NOV(11), DEC(12)
     * </pre>
     *
     * <code>int32 dayFilter = 2;</code>
     */
    public Builder setDayFilter(int value) {
      
      dayFilter_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *RESERVED(0), JAN(1), FEB(2), MAR(3), APR(4), 
     *MAY(5), JUN(6), JUL(7), AUG(8), SEP(9), OCT(10), NOV(11), DEC(12)
     * </pre>
     *
     * <code>int32 dayFilter = 2;</code>
     */
    public Builder clearDayFilter() {
      
      dayFilter_ = 0;
      onChanged();
      return this;
    }

    private int weekdayFilter_ ;
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *RESERVED(0), 1(1), …, 30(30), 31(31)
     * </pre>
     *
     * <code>int32 weekdayFilter = 3;</code>
     */
    public int getWeekdayFilter() {
      return weekdayFilter_;
    }
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *RESERVED(0), 1(1), …, 30(30), 31(31)
     * </pre>
     *
     * <code>int32 weekdayFilter = 3;</code>
     */
    public Builder setWeekdayFilter(int value) {
      
      weekdayFilter_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *RESERVED(0), 1(1), …, 30(30), 31(31)
     * </pre>
     *
     * <code>int32 weekdayFilter = 3;</code>
     */
    public Builder clearWeekdayFilter() {
      
      weekdayFilter_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.LocalTimePoint fromTimePoint_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LocalTimePoint, road.data.proto.LocalTimePoint.Builder, road.data.proto.LocalTimePointOrBuilder> fromTimePointBuilder_;
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
     */
    public boolean hasFromTimePoint() {
      return fromTimePointBuilder_ != null || fromTimePoint_ != null;
    }
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
     */
    public road.data.proto.LocalTimePoint getFromTimePoint() {
      if (fromTimePointBuilder_ == null) {
        return fromTimePoint_ == null ? road.data.proto.LocalTimePoint.getDefaultInstance() : fromTimePoint_;
      } else {
        return fromTimePointBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
     */
    public Builder setFromTimePoint(road.data.proto.LocalTimePoint value) {
      if (fromTimePointBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        fromTimePoint_ = value;
        onChanged();
      } else {
        fromTimePointBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
     */
    public Builder setFromTimePoint(
        road.data.proto.LocalTimePoint.Builder builderForValue) {
      if (fromTimePointBuilder_ == null) {
        fromTimePoint_ = builderForValue.build();
        onChanged();
      } else {
        fromTimePointBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
     */
    public Builder mergeFromTimePoint(road.data.proto.LocalTimePoint value) {
      if (fromTimePointBuilder_ == null) {
        if (fromTimePoint_ != null) {
          fromTimePoint_ =
            road.data.proto.LocalTimePoint.newBuilder(fromTimePoint_).mergeFrom(value).buildPartial();
        } else {
          fromTimePoint_ = value;
        }
        onChanged();
      } else {
        fromTimePointBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
     */
    public Builder clearFromTimePoint() {
      if (fromTimePointBuilder_ == null) {
        fromTimePoint_ = null;
        onChanged();
      } else {
        fromTimePoint_ = null;
        fromTimePointBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
     */
    public road.data.proto.LocalTimePoint.Builder getFromTimePointBuilder() {
      
      onChanged();
      return getFromTimePointFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
     */
    public road.data.proto.LocalTimePointOrBuilder getFromTimePointOrBuilder() {
      if (fromTimePointBuilder_ != null) {
        return fromTimePointBuilder_.getMessageOrBuilder();
      } else {
        return fromTimePoint_ == null ?
            road.data.proto.LocalTimePoint.getDefaultInstance() : fromTimePoint_;
      }
    }
    /**
     * <pre>
     *转化为二进制后，二进制左起第x位数字为1对应的含义：
     *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LocalTimePoint, road.data.proto.LocalTimePoint.Builder, road.data.proto.LocalTimePointOrBuilder> 
        getFromTimePointFieldBuilder() {
      if (fromTimePointBuilder_ == null) {
        fromTimePointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LocalTimePoint, road.data.proto.LocalTimePoint.Builder, road.data.proto.LocalTimePointOrBuilder>(
                getFromTimePoint(),
                getParentForChildren(),
                isClean());
        fromTimePoint_ = null;
      }
      return fromTimePointBuilder_;
    }

    private road.data.proto.LocalTimePoint toTimePoint_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LocalTimePoint, road.data.proto.LocalTimePoint.Builder, road.data.proto.LocalTimePointOrBuilder> toTimePointBuilder_;
    /**
     * <pre>
     *信控方案执行当日的结束时刻
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
     */
    public boolean hasToTimePoint() {
      return toTimePointBuilder_ != null || toTimePoint_ != null;
    }
    /**
     * <pre>
     *信控方案执行当日的结束时刻
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
     */
    public road.data.proto.LocalTimePoint getToTimePoint() {
      if (toTimePointBuilder_ == null) {
        return toTimePoint_ == null ? road.data.proto.LocalTimePoint.getDefaultInstance() : toTimePoint_;
      } else {
        return toTimePointBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *信控方案执行当日的结束时刻
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
     */
    public Builder setToTimePoint(road.data.proto.LocalTimePoint value) {
      if (toTimePointBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        toTimePoint_ = value;
        onChanged();
      } else {
        toTimePointBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *信控方案执行当日的结束时刻
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
     */
    public Builder setToTimePoint(
        road.data.proto.LocalTimePoint.Builder builderForValue) {
      if (toTimePointBuilder_ == null) {
        toTimePoint_ = builderForValue.build();
        onChanged();
      } else {
        toTimePointBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *信控方案执行当日的结束时刻
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
     */
    public Builder mergeToTimePoint(road.data.proto.LocalTimePoint value) {
      if (toTimePointBuilder_ == null) {
        if (toTimePoint_ != null) {
          toTimePoint_ =
            road.data.proto.LocalTimePoint.newBuilder(toTimePoint_).mergeFrom(value).buildPartial();
        } else {
          toTimePoint_ = value;
        }
        onChanged();
      } else {
        toTimePointBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *信控方案执行当日的结束时刻
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
     */
    public Builder clearToTimePoint() {
      if (toTimePointBuilder_ == null) {
        toTimePoint_ = null;
        onChanged();
      } else {
        toTimePoint_ = null;
        toTimePointBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *信控方案执行当日的结束时刻
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
     */
    public road.data.proto.LocalTimePoint.Builder getToTimePointBuilder() {
      
      onChanged();
      return getToTimePointFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *信控方案执行当日的结束时刻
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
     */
    public road.data.proto.LocalTimePointOrBuilder getToTimePointOrBuilder() {
      if (toTimePointBuilder_ != null) {
        return toTimePointBuilder_.getMessageOrBuilder();
      } else {
        return toTimePoint_ == null ?
            road.data.proto.LocalTimePoint.getDefaultInstance() : toTimePoint_;
      }
    }
    /**
     * <pre>
     *信控方案执行当日的结束时刻
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LocalTimePoint, road.data.proto.LocalTimePoint.Builder, road.data.proto.LocalTimePointOrBuilder> 
        getToTimePointFieldBuilder() {
      if (toTimePointBuilder_ == null) {
        toTimePointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LocalTimePoint, road.data.proto.LocalTimePoint.Builder, road.data.proto.LocalTimePointOrBuilder>(
                getToTimePoint(),
                getParentForChildren(),
                isClean());
        toTimePoint_ = null;
      }
      return toTimePointBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.PeriodictimeSpan)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.PeriodictimeSpan)
  private static final road.data.proto.PeriodictimeSpan DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.PeriodictimeSpan();
  }

  public static road.data.proto.PeriodictimeSpan getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PeriodictimeSpan>
      PARSER = new com.google.protobuf.AbstractParser<PeriodictimeSpan>() {
    @java.lang.Override
    public PeriodictimeSpan parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new PeriodictimeSpan(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<PeriodictimeSpan> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PeriodictimeSpan> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.PeriodictimeSpan getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

