// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface PeriodictimeSpanOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.PeriodictimeSpan)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *包含所有符合条件的月份的集合
   * </pre>
   *
   * <code>int32 monthFilter = 1;</code>
   */
  int getMonthFilter();

  /**
   * <pre>
   *转化为二进制后，二进制左起第x位数字为1对应的含义：
   *RESERVED(0), JAN(1), FEB(2), MAR(3), APR(4), 
   *MAY(5), JUN(6), JUL(7), AUG(8), SEP(9), OCT(10), NOV(11), DEC(12)
   * </pre>
   *
   * <code>int32 dayFilter = 2;</code>
   */
  int getDayFilter();

  /**
   * <pre>
   *转化为二进制后，二进制左起第x位数字为1对应的含义：
   *RESERVED(0), 1(1), …, 30(30), 31(31)
   * </pre>
   *
   * <code>int32 weekdayFilter = 3;</code>
   */
  int getWeekdayFilter();

  /**
   * <pre>
   *转化为二进制后，二进制左起第x位数字为1对应的含义：
   *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
   */
  boolean hasFromTimePoint();
  /**
   * <pre>
   *转化为二进制后，二进制左起第x位数字为1对应的含义：
   *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
   */
  road.data.proto.LocalTimePoint getFromTimePoint();
  /**
   * <pre>
   *转化为二进制后，二进制左起第x位数字为1对应的含义：
   *SUN (0), MON (1), TUE (2), WED (3), THUR (4), FRI (5), SAT (6)
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LocalTimePoint fromTimePoint = 4;</code>
   */
  road.data.proto.LocalTimePointOrBuilder getFromTimePointOrBuilder();

  /**
   * <pre>
   *信控方案执行当日的结束时刻
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
   */
  boolean hasToTimePoint();
  /**
   * <pre>
   *信控方案执行当日的结束时刻
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
   */
  road.data.proto.LocalTimePoint getToTimePoint();
  /**
   * <pre>
   *信控方案执行当日的结束时刻
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LocalTimePoint toTimePoint = 5;</code>
   */
  road.data.proto.LocalTimePointOrBuilder getToTimePointOrBuilder();
}
