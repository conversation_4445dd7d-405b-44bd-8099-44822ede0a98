// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *相位状态Phase    
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.Phase}
 */
public  final class Phase extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.Phase)
    PhaseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Phase.newBuilder() to construct.
  private Phase(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Phase() {
    phaseStates_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Phase();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Phase(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            id_ = input.readUInt32();
            break;
          }
          case 18: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              phaseStates_ = new java.util.ArrayList<road.data.proto.PhaseState>();
              mutable_bitField0_ |= 0x00000001;
            }
            phaseStates_.add(
                input.readMessage(road.data.proto.PhaseState.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        phaseStates_ = java.util.Collections.unmodifiableList(phaseStates_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Phase_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Phase_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.Phase.class, road.data.proto.Phase.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  private int id_;
  /**
   * <pre>
   *相位编号
   * </pre>
   *
   * <code>uint32 id = 1;</code>
   */
  public int getId() {
    return id_;
  }

  public static final int PHASESTATES_FIELD_NUMBER = 2;
  private java.util.List<road.data.proto.PhaseState> phaseStates_;
  /**
   * <pre>
   * 相位灯态状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
   */
  public java.util.List<road.data.proto.PhaseState> getPhaseStatesList() {
    return phaseStates_;
  }
  /**
   * <pre>
   * 相位灯态状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
   */
  public java.util.List<? extends road.data.proto.PhaseStateOrBuilder> 
      getPhaseStatesOrBuilderList() {
    return phaseStates_;
  }
  /**
   * <pre>
   * 相位灯态状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
   */
  public int getPhaseStatesCount() {
    return phaseStates_.size();
  }
  /**
   * <pre>
   * 相位灯态状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
   */
  public road.data.proto.PhaseState getPhaseStates(int index) {
    return phaseStates_.get(index);
  }
  /**
   * <pre>
   * 相位灯态状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
   */
  public road.data.proto.PhaseStateOrBuilder getPhaseStatesOrBuilder(
      int index) {
    return phaseStates_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (id_ != 0) {
      output.writeUInt32(1, id_);
    }
    for (int i = 0; i < phaseStates_.size(); i++) {
      output.writeMessage(2, phaseStates_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (id_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, id_);
    }
    for (int i = 0; i < phaseStates_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, phaseStates_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.Phase)) {
      return super.equals(obj);
    }
    road.data.proto.Phase other = (road.data.proto.Phase) obj;

    if (getId()
        != other.getId()) return false;
    if (!getPhaseStatesList()
        .equals(other.getPhaseStatesList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId();
    if (getPhaseStatesCount() > 0) {
      hash = (37 * hash) + PHASESTATES_FIELD_NUMBER;
      hash = (53 * hash) + getPhaseStatesList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.Phase parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Phase parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Phase parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Phase parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Phase parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Phase parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Phase parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.Phase parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.Phase parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.Phase parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.Phase parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.Phase parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.Phase prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *相位状态Phase    
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.Phase}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.Phase)
      road.data.proto.PhaseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Phase_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Phase_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.Phase.class, road.data.proto.Phase.Builder.class);
    }

    // Construct using road.data.proto.Phase.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getPhaseStatesFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      id_ = 0;

      if (phaseStatesBuilder_ == null) {
        phaseStates_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        phaseStatesBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Phase_descriptor;
    }

    @java.lang.Override
    public road.data.proto.Phase getDefaultInstanceForType() {
      return road.data.proto.Phase.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.Phase build() {
      road.data.proto.Phase result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.Phase buildPartial() {
      road.data.proto.Phase result = new road.data.proto.Phase(this);
      int from_bitField0_ = bitField0_;
      result.id_ = id_;
      if (phaseStatesBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          phaseStates_ = java.util.Collections.unmodifiableList(phaseStates_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.phaseStates_ = phaseStates_;
      } else {
        result.phaseStates_ = phaseStatesBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.Phase) {
        return mergeFrom((road.data.proto.Phase)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.Phase other) {
      if (other == road.data.proto.Phase.getDefaultInstance()) return this;
      if (other.getId() != 0) {
        setId(other.getId());
      }
      if (phaseStatesBuilder_ == null) {
        if (!other.phaseStates_.isEmpty()) {
          if (phaseStates_.isEmpty()) {
            phaseStates_ = other.phaseStates_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePhaseStatesIsMutable();
            phaseStates_.addAll(other.phaseStates_);
          }
          onChanged();
        }
      } else {
        if (!other.phaseStates_.isEmpty()) {
          if (phaseStatesBuilder_.isEmpty()) {
            phaseStatesBuilder_.dispose();
            phaseStatesBuilder_ = null;
            phaseStates_ = other.phaseStates_;
            bitField0_ = (bitField0_ & ~0x00000001);
            phaseStatesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getPhaseStatesFieldBuilder() : null;
          } else {
            phaseStatesBuilder_.addAllMessages(other.phaseStates_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.Phase parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.Phase) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int id_ ;
    /**
     * <pre>
     *相位编号
     * </pre>
     *
     * <code>uint32 id = 1;</code>
     */
    public int getId() {
      return id_;
    }
    /**
     * <pre>
     *相位编号
     * </pre>
     *
     * <code>uint32 id = 1;</code>
     */
    public Builder setId(int value) {
      
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *相位编号
     * </pre>
     *
     * <code>uint32 id = 1;</code>
     */
    public Builder clearId() {
      
      id_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.PhaseState> phaseStates_ =
      java.util.Collections.emptyList();
    private void ensurePhaseStatesIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        phaseStates_ = new java.util.ArrayList<road.data.proto.PhaseState>(phaseStates_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.PhaseState, road.data.proto.PhaseState.Builder, road.data.proto.PhaseStateOrBuilder> phaseStatesBuilder_;

    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public java.util.List<road.data.proto.PhaseState> getPhaseStatesList() {
      if (phaseStatesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(phaseStates_);
      } else {
        return phaseStatesBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public int getPhaseStatesCount() {
      if (phaseStatesBuilder_ == null) {
        return phaseStates_.size();
      } else {
        return phaseStatesBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public road.data.proto.PhaseState getPhaseStates(int index) {
      if (phaseStatesBuilder_ == null) {
        return phaseStates_.get(index);
      } else {
        return phaseStatesBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public Builder setPhaseStates(
        int index, road.data.proto.PhaseState value) {
      if (phaseStatesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePhaseStatesIsMutable();
        phaseStates_.set(index, value);
        onChanged();
      } else {
        phaseStatesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public Builder setPhaseStates(
        int index, road.data.proto.PhaseState.Builder builderForValue) {
      if (phaseStatesBuilder_ == null) {
        ensurePhaseStatesIsMutable();
        phaseStates_.set(index, builderForValue.build());
        onChanged();
      } else {
        phaseStatesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public Builder addPhaseStates(road.data.proto.PhaseState value) {
      if (phaseStatesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePhaseStatesIsMutable();
        phaseStates_.add(value);
        onChanged();
      } else {
        phaseStatesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public Builder addPhaseStates(
        int index, road.data.proto.PhaseState value) {
      if (phaseStatesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePhaseStatesIsMutable();
        phaseStates_.add(index, value);
        onChanged();
      } else {
        phaseStatesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public Builder addPhaseStates(
        road.data.proto.PhaseState.Builder builderForValue) {
      if (phaseStatesBuilder_ == null) {
        ensurePhaseStatesIsMutable();
        phaseStates_.add(builderForValue.build());
        onChanged();
      } else {
        phaseStatesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public Builder addPhaseStates(
        int index, road.data.proto.PhaseState.Builder builderForValue) {
      if (phaseStatesBuilder_ == null) {
        ensurePhaseStatesIsMutable();
        phaseStates_.add(index, builderForValue.build());
        onChanged();
      } else {
        phaseStatesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public Builder addAllPhaseStates(
        java.lang.Iterable<? extends road.data.proto.PhaseState> values) {
      if (phaseStatesBuilder_ == null) {
        ensurePhaseStatesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, phaseStates_);
        onChanged();
      } else {
        phaseStatesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public Builder clearPhaseStates() {
      if (phaseStatesBuilder_ == null) {
        phaseStates_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        phaseStatesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public Builder removePhaseStates(int index) {
      if (phaseStatesBuilder_ == null) {
        ensurePhaseStatesIsMutable();
        phaseStates_.remove(index);
        onChanged();
      } else {
        phaseStatesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public road.data.proto.PhaseState.Builder getPhaseStatesBuilder(
        int index) {
      return getPhaseStatesFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public road.data.proto.PhaseStateOrBuilder getPhaseStatesOrBuilder(
        int index) {
      if (phaseStatesBuilder_ == null) {
        return phaseStates_.get(index);  } else {
        return phaseStatesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public java.util.List<? extends road.data.proto.PhaseStateOrBuilder> 
         getPhaseStatesOrBuilderList() {
      if (phaseStatesBuilder_ != null) {
        return phaseStatesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(phaseStates_);
      }
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public road.data.proto.PhaseState.Builder addPhaseStatesBuilder() {
      return getPhaseStatesFieldBuilder().addBuilder(
          road.data.proto.PhaseState.getDefaultInstance());
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public road.data.proto.PhaseState.Builder addPhaseStatesBuilder(
        int index) {
      return getPhaseStatesFieldBuilder().addBuilder(
          index, road.data.proto.PhaseState.getDefaultInstance());
    }
    /**
     * <pre>
     * 相位灯态状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
     */
    public java.util.List<road.data.proto.PhaseState.Builder> 
         getPhaseStatesBuilderList() {
      return getPhaseStatesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.PhaseState, road.data.proto.PhaseState.Builder, road.data.proto.PhaseStateOrBuilder> 
        getPhaseStatesFieldBuilder() {
      if (phaseStatesBuilder_ == null) {
        phaseStatesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.PhaseState, road.data.proto.PhaseState.Builder, road.data.proto.PhaseStateOrBuilder>(
                phaseStates_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        phaseStates_ = null;
      }
      return phaseStatesBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.Phase)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.Phase)
  private static final road.data.proto.Phase DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.Phase();
  }

  public static road.data.proto.Phase getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Phase>
      PARSER = new com.google.protobuf.AbstractParser<Phase>() {
    @java.lang.Override
    public Phase parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Phase(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Phase> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Phase> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.Phase getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

