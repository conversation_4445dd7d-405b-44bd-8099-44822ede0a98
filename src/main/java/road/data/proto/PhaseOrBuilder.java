// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface PhaseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.Phase)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *相位编号
   * </pre>
   *
   * <code>uint32 id = 1;</code>
   */
  int getId();

  /**
   * <pre>
   * 相位灯态状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
   */
  java.util.List<road.data.proto.PhaseState> 
      getPhaseStatesList();
  /**
   * <pre>
   * 相位灯态状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
   */
  road.data.proto.PhaseState getPhaseStates(int index);
  /**
   * <pre>
   * 相位灯态状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
   */
  int getPhaseStatesCount();
  /**
   * <pre>
   * 相位灯态状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
   */
  java.util.List<? extends road.data.proto.PhaseStateOrBuilder> 
      getPhaseStatesOrBuilderList();
  /**
   * <pre>
   * 相位灯态状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseState phaseStates = 2;</code>
   */
  road.data.proto.PhaseStateOrBuilder getPhaseStatesOrBuilder(
      int index);
}
