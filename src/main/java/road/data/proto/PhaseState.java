// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *相位灯态状态PhaseState   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.PhaseState}
 */
public  final class PhaseState extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.PhaseState)
    PhaseStateOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PhaseState.newBuilder() to construct.
  private PhaseState(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PhaseState() {
    light_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PhaseState();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private PhaseState(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {
            int rawValue = input.readEnum();

            light_ = rawValue;
            break;
          }
          case 18: {
            road.data.proto.TimeCountingDown.Builder subBuilder = null;
            if (timing_ != null) {
              subBuilder = timing_.toBuilder();
            }
            timing_ = input.readMessage(road.data.proto.TimeCountingDown.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(timing_);
              timing_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PhaseState_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PhaseState_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.PhaseState.class, road.data.proto.PhaseState.Builder.class);
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.PhaseState.LightState}
   */
  public enum LightState
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *未知
     * </pre>
     *
     * <code>LIGHT_STATE_UNKNOWN = 0;</code>
     */
    LIGHT_STATE_UNKNOWN(0),
    /**
     * <pre>
     *熄灭
     * </pre>
     *
     * <code>LIGHT_STATE_DARK = 1;</code>
     */
    LIGHT_STATE_DARK(1),
    /**
     * <pre>
     *红闪
     * </pre>
     *
     * <code>LIGHT_STATE_FLASHING_RED = 2;</code>
     */
    LIGHT_STATE_FLASHING_RED(2),
    /**
     * <pre>
     *红灯
     * </pre>
     *
     * <code>LIGHT_STATE_RED = 3;</code>
     */
    LIGHT_STATE_RED(3),
    /**
     * <pre>
     *绿闪
     * </pre>
     *
     * <code>LIGHT_STATE_FLASHING_GREEN = 4;</code>
     */
    LIGHT_STATE_FLASHING_GREEN(4),
    /**
     * <pre>
     *通行允许相位
     * </pre>
     *
     * <code>LIGHT_STATE_PERMISSIVE_GREEN = 5;</code>
     */
    LIGHT_STATE_PERMISSIVE_GREEN(5),
    /**
     * <pre>
     *通行保护相位
     * </pre>
     *
     * <code>LIGHT_STATE_PROTETED_GREEN = 6;</code>
     */
    LIGHT_STATE_PROTETED_GREEN(6),
    /**
     * <pre>
     *黄灯
     * </pre>
     *
     * <code>LIGHT_STATE_YELLOW = 7;</code>
     */
    LIGHT_STATE_YELLOW(7),
    /**
     * <pre>
     *黄闪
     * </pre>
     *
     * <code>LIGHT_STATE_FLASHING_YELLOW = 8;</code>
     */
    LIGHT_STATE_FLASHING_YELLOW(8),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *未知
     * </pre>
     *
     * <code>LIGHT_STATE_UNKNOWN = 0;</code>
     */
    public static final int LIGHT_STATE_UNKNOWN_VALUE = 0;
    /**
     * <pre>
     *熄灭
     * </pre>
     *
     * <code>LIGHT_STATE_DARK = 1;</code>
     */
    public static final int LIGHT_STATE_DARK_VALUE = 1;
    /**
     * <pre>
     *红闪
     * </pre>
     *
     * <code>LIGHT_STATE_FLASHING_RED = 2;</code>
     */
    public static final int LIGHT_STATE_FLASHING_RED_VALUE = 2;
    /**
     * <pre>
     *红灯
     * </pre>
     *
     * <code>LIGHT_STATE_RED = 3;</code>
     */
    public static final int LIGHT_STATE_RED_VALUE = 3;
    /**
     * <pre>
     *绿闪
     * </pre>
     *
     * <code>LIGHT_STATE_FLASHING_GREEN = 4;</code>
     */
    public static final int LIGHT_STATE_FLASHING_GREEN_VALUE = 4;
    /**
     * <pre>
     *通行允许相位
     * </pre>
     *
     * <code>LIGHT_STATE_PERMISSIVE_GREEN = 5;</code>
     */
    public static final int LIGHT_STATE_PERMISSIVE_GREEN_VALUE = 5;
    /**
     * <pre>
     *通行保护相位
     * </pre>
     *
     * <code>LIGHT_STATE_PROTETED_GREEN = 6;</code>
     */
    public static final int LIGHT_STATE_PROTETED_GREEN_VALUE = 6;
    /**
     * <pre>
     *黄灯
     * </pre>
     *
     * <code>LIGHT_STATE_YELLOW = 7;</code>
     */
    public static final int LIGHT_STATE_YELLOW_VALUE = 7;
    /**
     * <pre>
     *黄闪
     * </pre>
     *
     * <code>LIGHT_STATE_FLASHING_YELLOW = 8;</code>
     */
    public static final int LIGHT_STATE_FLASHING_YELLOW_VALUE = 8;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static LightState valueOf(int value) {
      return forNumber(value);
    }

    public static LightState forNumber(int value) {
      switch (value) {
        case 0: return LIGHT_STATE_UNKNOWN;
        case 1: return LIGHT_STATE_DARK;
        case 2: return LIGHT_STATE_FLASHING_RED;
        case 3: return LIGHT_STATE_RED;
        case 4: return LIGHT_STATE_FLASHING_GREEN;
        case 5: return LIGHT_STATE_PERMISSIVE_GREEN;
        case 6: return LIGHT_STATE_PROTETED_GREEN;
        case 7: return LIGHT_STATE_YELLOW;
        case 8: return LIGHT_STATE_FLASHING_YELLOW;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<LightState>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        LightState> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<LightState>() {
            public LightState findValueByNumber(int number) {
              return LightState.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.PhaseState.getDescriptor().getEnumTypes().get(0);
    }

    private static final LightState[] VALUES = values();

    public static LightState valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private LightState(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.PhaseState.LightState)
  }

  public static final int LIGHT_FIELD_NUMBER = 1;
  private int light_;
  /**
   * <pre>
   *灯色 
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PhaseState.LightState light = 1;</code>
   */
  public int getLightValue() {
    return light_;
  }
  /**
   * <pre>
   *灯色 
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PhaseState.LightState light = 1;</code>
   */
  public road.data.proto.PhaseState.LightState getLight() {
    @SuppressWarnings("deprecation")
    road.data.proto.PhaseState.LightState result = road.data.proto.PhaseState.LightState.valueOf(light_);
    return result == null ? road.data.proto.PhaseState.LightState.UNRECOGNIZED : result;
  }

  public static final int TIMING_FIELD_NUMBER = 2;
  private road.data.proto.TimeCountingDown timing_;
  /**
   * <pre>
   *可选，倒计时配置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
   */
  public boolean hasTiming() {
    return timing_ != null;
  }
  /**
   * <pre>
   *可选，倒计时配置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
   */
  public road.data.proto.TimeCountingDown getTiming() {
    return timing_ == null ? road.data.proto.TimeCountingDown.getDefaultInstance() : timing_;
  }
  /**
   * <pre>
   *可选，倒计时配置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
   */
  public road.data.proto.TimeCountingDownOrBuilder getTimingOrBuilder() {
    return getTiming();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (light_ != road.data.proto.PhaseState.LightState.LIGHT_STATE_UNKNOWN.getNumber()) {
      output.writeEnum(1, light_);
    }
    if (timing_ != null) {
      output.writeMessage(2, getTiming());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (light_ != road.data.proto.PhaseState.LightState.LIGHT_STATE_UNKNOWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(1, light_);
    }
    if (timing_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getTiming());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.PhaseState)) {
      return super.equals(obj);
    }
    road.data.proto.PhaseState other = (road.data.proto.PhaseState) obj;

    if (light_ != other.light_) return false;
    if (hasTiming() != other.hasTiming()) return false;
    if (hasTiming()) {
      if (!getTiming()
          .equals(other.getTiming())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + LIGHT_FIELD_NUMBER;
    hash = (53 * hash) + light_;
    if (hasTiming()) {
      hash = (37 * hash) + TIMING_FIELD_NUMBER;
      hash = (53 * hash) + getTiming().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.PhaseState parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PhaseState parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PhaseState parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PhaseState parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PhaseState parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PhaseState parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PhaseState parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PhaseState parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PhaseState parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.PhaseState parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PhaseState parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PhaseState parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.PhaseState prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *相位灯态状态PhaseState   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.PhaseState}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.PhaseState)
      road.data.proto.PhaseStateOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PhaseState_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PhaseState_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.PhaseState.class, road.data.proto.PhaseState.Builder.class);
    }

    // Construct using road.data.proto.PhaseState.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      light_ = 0;

      if (timingBuilder_ == null) {
        timing_ = null;
      } else {
        timing_ = null;
        timingBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PhaseState_descriptor;
    }

    @java.lang.Override
    public road.data.proto.PhaseState getDefaultInstanceForType() {
      return road.data.proto.PhaseState.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.PhaseState build() {
      road.data.proto.PhaseState result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.PhaseState buildPartial() {
      road.data.proto.PhaseState result = new road.data.proto.PhaseState(this);
      result.light_ = light_;
      if (timingBuilder_ == null) {
        result.timing_ = timing_;
      } else {
        result.timing_ = timingBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.PhaseState) {
        return mergeFrom((road.data.proto.PhaseState)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.PhaseState other) {
      if (other == road.data.proto.PhaseState.getDefaultInstance()) return this;
      if (other.light_ != 0) {
        setLightValue(other.getLightValue());
      }
      if (other.hasTiming()) {
        mergeTiming(other.getTiming());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.PhaseState parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.PhaseState) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int light_ = 0;
    /**
     * <pre>
     *灯色 
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PhaseState.LightState light = 1;</code>
     */
    public int getLightValue() {
      return light_;
    }
    /**
     * <pre>
     *灯色 
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PhaseState.LightState light = 1;</code>
     */
    public Builder setLightValue(int value) {
      light_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *灯色 
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PhaseState.LightState light = 1;</code>
     */
    public road.data.proto.PhaseState.LightState getLight() {
      @SuppressWarnings("deprecation")
      road.data.proto.PhaseState.LightState result = road.data.proto.PhaseState.LightState.valueOf(light_);
      return result == null ? road.data.proto.PhaseState.LightState.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *灯色 
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PhaseState.LightState light = 1;</code>
     */
    public Builder setLight(road.data.proto.PhaseState.LightState value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      light_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *灯色 
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PhaseState.LightState light = 1;</code>
     */
    public Builder clearLight() {
      
      light_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.TimeCountingDown timing_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.TimeCountingDown, road.data.proto.TimeCountingDown.Builder, road.data.proto.TimeCountingDownOrBuilder> timingBuilder_;
    /**
     * <pre>
     *可选，倒计时配置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
     */
    public boolean hasTiming() {
      return timingBuilder_ != null || timing_ != null;
    }
    /**
     * <pre>
     *可选，倒计时配置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
     */
    public road.data.proto.TimeCountingDown getTiming() {
      if (timingBuilder_ == null) {
        return timing_ == null ? road.data.proto.TimeCountingDown.getDefaultInstance() : timing_;
      } else {
        return timingBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，倒计时配置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
     */
    public Builder setTiming(road.data.proto.TimeCountingDown value) {
      if (timingBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        timing_ = value;
        onChanged();
      } else {
        timingBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，倒计时配置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
     */
    public Builder setTiming(
        road.data.proto.TimeCountingDown.Builder builderForValue) {
      if (timingBuilder_ == null) {
        timing_ = builderForValue.build();
        onChanged();
      } else {
        timingBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，倒计时配置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
     */
    public Builder mergeTiming(road.data.proto.TimeCountingDown value) {
      if (timingBuilder_ == null) {
        if (timing_ != null) {
          timing_ =
            road.data.proto.TimeCountingDown.newBuilder(timing_).mergeFrom(value).buildPartial();
        } else {
          timing_ = value;
        }
        onChanged();
      } else {
        timingBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，倒计时配置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
     */
    public Builder clearTiming() {
      if (timingBuilder_ == null) {
        timing_ = null;
        onChanged();
      } else {
        timing_ = null;
        timingBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，倒计时配置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
     */
    public road.data.proto.TimeCountingDown.Builder getTimingBuilder() {
      
      onChanged();
      return getTimingFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，倒计时配置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
     */
    public road.data.proto.TimeCountingDownOrBuilder getTimingOrBuilder() {
      if (timingBuilder_ != null) {
        return timingBuilder_.getMessageOrBuilder();
      } else {
        return timing_ == null ?
            road.data.proto.TimeCountingDown.getDefaultInstance() : timing_;
      }
    }
    /**
     * <pre>
     *可选，倒计时配置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.TimeCountingDown, road.data.proto.TimeCountingDown.Builder, road.data.proto.TimeCountingDownOrBuilder> 
        getTimingFieldBuilder() {
      if (timingBuilder_ == null) {
        timingBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.TimeCountingDown, road.data.proto.TimeCountingDown.Builder, road.data.proto.TimeCountingDownOrBuilder>(
                getTiming(),
                getParentForChildren(),
                isClean());
        timing_ = null;
      }
      return timingBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.PhaseState)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.PhaseState)
  private static final road.data.proto.PhaseState DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.PhaseState();
  }

  public static road.data.proto.PhaseState getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PhaseState>
      PARSER = new com.google.protobuf.AbstractParser<PhaseState>() {
    @java.lang.Override
    public PhaseState parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new PhaseState(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<PhaseState> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PhaseState> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.PhaseState getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

