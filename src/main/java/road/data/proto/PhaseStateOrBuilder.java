// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface PhaseStateOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.PhaseState)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *灯色 
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PhaseState.LightState light = 1;</code>
   */
  int getLightValue();
  /**
   * <pre>
   *灯色 
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PhaseState.LightState light = 1;</code>
   */
  road.data.proto.PhaseState.LightState getLight();

  /**
   * <pre>
   *可选，倒计时配置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
   */
  boolean hasTiming();
  /**
   * <pre>
   *可选，倒计时配置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
   */
  road.data.proto.TimeCountingDown getTiming();
  /**
   * <pre>
   *可选，倒计时配置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeCountingDown timing = 2;</code>
   */
  road.data.proto.TimeCountingDownOrBuilder getTimingOrBuilder();
}
