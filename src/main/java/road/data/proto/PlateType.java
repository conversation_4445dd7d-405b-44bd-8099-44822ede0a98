// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车牌类型
 * </pre>
 *
 * Protobuf enum {@code cn.seisys.v2x.pb.PlateType}
 */
public enum PlateType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *未知UNKNOWN_PLATE_TYPE = 0; 未知类型
   * </pre>
   *
   * <code>UNKNOWN_PLATE = 0;</code>
   */
  UNKNOWN_PLATE(0),
  /**
   * <pre>
   *大型汽车号牌
   * </pre>
   *
   * <code>LARGE_CAR_PLATE = 1;</code>
   */
  LARGE_CAR_PLATE(1),
  /**
   * <pre>
   *小型汽车号牌
   * </pre>
   *
   * <code>SMALL_CAR_PLATE = 2;</code>
   */
  SMALL_CAR_PLATE(2),
  /**
   * <pre>
   *使馆汽车号牌
   * </pre>
   *
   * <code>EMBASSY_CAR_PLATE = 3;</code>
   */
  EMBASSY_CAR_PLATE(3),
  /**
   * <pre>
   *领馆汽车号牌
   * </pre>
   *
   * <code>CONSULATE_CAR_PLATE = 4;</code>
   */
  CONSULATE_CAR_PLATE(4),
  /**
   * <pre>
   *境外汽车号牌
   * </pre>
   *
   * <code>OVERSEAS_CAR_PLATE = 5;</code>
   */
  OVERSEAS_CAR_PLATE(5),
  /**
   * <pre>
   *外籍汽车号牌
   * </pre>
   *
   * <code>FOREIGN_CAR_PLATE = 6;</code>
   */
  FOREIGN_CAR_PLATE(6),
  /**
   * <pre>
   *普通摩托车号牌
   * </pre>
   *
   * <code>ORDINARY_MOTORCYCLE_PLATE = 7;</code>
   */
  ORDINARY_MOTORCYCLE_PLATE(7),
  /**
   * <pre>
   *轻便摩托车号牌
   * </pre>
   *
   * <code>MOPED_PLATE = 8;</code>
   */
  MOPED_PLATE(8),
  /**
   * <pre>
   *使馆摩托车号牌
   * </pre>
   *
   * <code>EMBASSY_MOTORCYCLE_PLATE = 9;</code>
   */
  EMBASSY_MOTORCYCLE_PLATE(9),
  /**
   * <pre>
   *领馆摩托车号牌
   * </pre>
   *
   * <code>CONSULATE_MOTORCYCLE_PLATE = 10;</code>
   */
  CONSULATE_MOTORCYCLE_PLATE(10),
  /**
   * <pre>
   *境外摩托车号牌
   * </pre>
   *
   * <code>OVERSEAS_MOTORCYCLE_PLATE = 11;</code>
   */
  OVERSEAS_MOTORCYCLE_PLATE(11),
  /**
   * <pre>
   *外籍摩托车号牌
   * </pre>
   *
   * <code>FOREIGN_MOTORCYCLE_PLATE = 12;</code>
   */
  FOREIGN_MOTORCYCLE_PLATE(12),
  /**
   * <pre>
   *低速车号牌
   * </pre>
   *
   * <code>LOW_SPEED_PLATE = 13;</code>
   */
  LOW_SPEED_PLATE(13),
  /**
   * <pre>
   *拖拉机号牌
   * </pre>
   *
   * <code>TRACTOR_PLATE = 14;</code>
   */
  TRACTOR_PLATE(14),
  /**
   * <pre>
   *挂车号牌
   * </pre>
   *
   * <code>TRAILER_PLATE = 15;</code>
   */
  TRAILER_PLATE(15),
  /**
   * <pre>
   *教练汽车号牌
   * </pre>
   *
   * <code>COACH_CAR_PLATE = 16;</code>
   */
  COACH_CAR_PLATE(16),
  /**
   * <pre>
   *教练摩托车号牌
   * </pre>
   *
   * <code>COACH_MOTORCYCLE_PLATE = 17;</code>
   */
  COACH_MOTORCYCLE_PLATE(17),
  /**
   * <pre>
   *临时入境汽车号牌
   * </pre>
   *
   * <code>TEMPORARY_ENTRY_PLATE = 20;</code>
   */
  TEMPORARY_ENTRY_PLATE(20),
  /**
   * <pre>
   *临时入境摩托车号牌
   * </pre>
   *
   * <code>TEMPORARY_ENTRY_MOTORCYCLE_PLATE = 21;</code>
   */
  TEMPORARY_ENTRY_MOTORCYCLE_PLATE(21),
  /**
   * <pre>
   *临时行驶车号牌
   * </pre>
   *
   * <code>TEMPORARY_DRIVING_PLATE = 22;</code>
   */
  TEMPORARY_DRIVING_PLATE(22),
  /**
   * <pre>
   *警用汽车号牌
   * </pre>
   *
   * <code>POLICE_CAR_PLATE = 23;</code>
   */
  POLICE_CAR_PLATE(23),
  /**
   * <pre>
   *警用摩托车号牌
   * </pre>
   *
   * <code>POLICE_MOTORCYCLE_PLATE = 24;</code>
   */
  POLICE_MOTORCYCLE_PLATE(24),
  /**
   * <pre>
   *原农机号牌
   * </pre>
   *
   * <code>ORIGINAL_AGRICULTURAL_MACHINERY_PLATE = 25;</code>
   */
  ORIGINAL_AGRICULTURAL_MACHINERY_PLATE(25),
  /**
   * <pre>
   *香港入出境号牌
   * </pre>
   *
   * <code>HONGKONG_PLATE = 26;</code>
   */
  HONGKONG_PLATE(26),
  /**
   * <pre>
   *澳门入出境号牌
   * </pre>
   *
   * <code>MACAU_PLATE = 27;</code>
   */
  MACAU_PLATE(27),
  /**
   * <pre>
   *武警号牌
   * </pre>
   *
   * <code>ARMED_POLICE_PLATE = 31;</code>
   */
  ARMED_POLICE_PLATE(31),
  /**
   * <pre>
   *军队号牌
   * </pre>
   *
   * <code>ARMY_PLATE = 32;</code>
   */
  ARMY_PLATE(32),
  /**
   * <pre>
   *无号牌
   * </pre>
   *
   * <code>NO_NUMBER_PLATE = 41;</code>
   */
  NO_NUMBER_PLATE(41),
  /**
   * <pre>
   *假号牌
   * </pre>
   *
   * <code>FAKE_PLATE = 42;</code>
   */
  FAKE_PLATE(42),
  /**
   * <pre>
   *挪用号牌
   * </pre>
   *
   * <code>MISAPPROPRIATION_PLATE = 43;</code>
   */
  MISAPPROPRIATION_PLATE(43),
  /**
   * <pre>
   *无法识别
   * </pre>
   *
   * <code>UNRECOGNIZED_PLATE = 44;</code>
   */
  UNRECOGNIZED_PLATE(44),
  /**
   * <pre>
   *大型新能源汽车（左侧黄色右侧绿色双拼色底黑字）
   * </pre>
   *
   * <code>LARGE_NEW_ENERGY_YELLOW_GREEN_PLATE = 51;</code>
   */
  LARGE_NEW_ENERGY_YELLOW_GREEN_PLATE(51),
  /**
   * <pre>
   *小型新能源汽车（渐变绿底黑字）
   * </pre>
   *
   * <code>SMALL_NEW_ENERGY_GREEN_PLATE = 52;</code>
   */
  SMALL_NEW_ENERGY_GREEN_PLATE(52),
  /**
   * <pre>
   *其他
   * </pre>
   *
   * <code>OTHER_PLATE = 99;</code>
   */
  OTHER_PLATE(99),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *未知UNKNOWN_PLATE_TYPE = 0; 未知类型
   * </pre>
   *
   * <code>UNKNOWN_PLATE = 0;</code>
   */
  public static final int UNKNOWN_PLATE_VALUE = 0;
  /**
   * <pre>
   *大型汽车号牌
   * </pre>
   *
   * <code>LARGE_CAR_PLATE = 1;</code>
   */
  public static final int LARGE_CAR_PLATE_VALUE = 1;
  /**
   * <pre>
   *小型汽车号牌
   * </pre>
   *
   * <code>SMALL_CAR_PLATE = 2;</code>
   */
  public static final int SMALL_CAR_PLATE_VALUE = 2;
  /**
   * <pre>
   *使馆汽车号牌
   * </pre>
   *
   * <code>EMBASSY_CAR_PLATE = 3;</code>
   */
  public static final int EMBASSY_CAR_PLATE_VALUE = 3;
  /**
   * <pre>
   *领馆汽车号牌
   * </pre>
   *
   * <code>CONSULATE_CAR_PLATE = 4;</code>
   */
  public static final int CONSULATE_CAR_PLATE_VALUE = 4;
  /**
   * <pre>
   *境外汽车号牌
   * </pre>
   *
   * <code>OVERSEAS_CAR_PLATE = 5;</code>
   */
  public static final int OVERSEAS_CAR_PLATE_VALUE = 5;
  /**
   * <pre>
   *外籍汽车号牌
   * </pre>
   *
   * <code>FOREIGN_CAR_PLATE = 6;</code>
   */
  public static final int FOREIGN_CAR_PLATE_VALUE = 6;
  /**
   * <pre>
   *普通摩托车号牌
   * </pre>
   *
   * <code>ORDINARY_MOTORCYCLE_PLATE = 7;</code>
   */
  public static final int ORDINARY_MOTORCYCLE_PLATE_VALUE = 7;
  /**
   * <pre>
   *轻便摩托车号牌
   * </pre>
   *
   * <code>MOPED_PLATE = 8;</code>
   */
  public static final int MOPED_PLATE_VALUE = 8;
  /**
   * <pre>
   *使馆摩托车号牌
   * </pre>
   *
   * <code>EMBASSY_MOTORCYCLE_PLATE = 9;</code>
   */
  public static final int EMBASSY_MOTORCYCLE_PLATE_VALUE = 9;
  /**
   * <pre>
   *领馆摩托车号牌
   * </pre>
   *
   * <code>CONSULATE_MOTORCYCLE_PLATE = 10;</code>
   */
  public static final int CONSULATE_MOTORCYCLE_PLATE_VALUE = 10;
  /**
   * <pre>
   *境外摩托车号牌
   * </pre>
   *
   * <code>OVERSEAS_MOTORCYCLE_PLATE = 11;</code>
   */
  public static final int OVERSEAS_MOTORCYCLE_PLATE_VALUE = 11;
  /**
   * <pre>
   *外籍摩托车号牌
   * </pre>
   *
   * <code>FOREIGN_MOTORCYCLE_PLATE = 12;</code>
   */
  public static final int FOREIGN_MOTORCYCLE_PLATE_VALUE = 12;
  /**
   * <pre>
   *低速车号牌
   * </pre>
   *
   * <code>LOW_SPEED_PLATE = 13;</code>
   */
  public static final int LOW_SPEED_PLATE_VALUE = 13;
  /**
   * <pre>
   *拖拉机号牌
   * </pre>
   *
   * <code>TRACTOR_PLATE = 14;</code>
   */
  public static final int TRACTOR_PLATE_VALUE = 14;
  /**
   * <pre>
   *挂车号牌
   * </pre>
   *
   * <code>TRAILER_PLATE = 15;</code>
   */
  public static final int TRAILER_PLATE_VALUE = 15;
  /**
   * <pre>
   *教练汽车号牌
   * </pre>
   *
   * <code>COACH_CAR_PLATE = 16;</code>
   */
  public static final int COACH_CAR_PLATE_VALUE = 16;
  /**
   * <pre>
   *教练摩托车号牌
   * </pre>
   *
   * <code>COACH_MOTORCYCLE_PLATE = 17;</code>
   */
  public static final int COACH_MOTORCYCLE_PLATE_VALUE = 17;
  /**
   * <pre>
   *临时入境汽车号牌
   * </pre>
   *
   * <code>TEMPORARY_ENTRY_PLATE = 20;</code>
   */
  public static final int TEMPORARY_ENTRY_PLATE_VALUE = 20;
  /**
   * <pre>
   *临时入境摩托车号牌
   * </pre>
   *
   * <code>TEMPORARY_ENTRY_MOTORCYCLE_PLATE = 21;</code>
   */
  public static final int TEMPORARY_ENTRY_MOTORCYCLE_PLATE_VALUE = 21;
  /**
   * <pre>
   *临时行驶车号牌
   * </pre>
   *
   * <code>TEMPORARY_DRIVING_PLATE = 22;</code>
   */
  public static final int TEMPORARY_DRIVING_PLATE_VALUE = 22;
  /**
   * <pre>
   *警用汽车号牌
   * </pre>
   *
   * <code>POLICE_CAR_PLATE = 23;</code>
   */
  public static final int POLICE_CAR_PLATE_VALUE = 23;
  /**
   * <pre>
   *警用摩托车号牌
   * </pre>
   *
   * <code>POLICE_MOTORCYCLE_PLATE = 24;</code>
   */
  public static final int POLICE_MOTORCYCLE_PLATE_VALUE = 24;
  /**
   * <pre>
   *原农机号牌
   * </pre>
   *
   * <code>ORIGINAL_AGRICULTURAL_MACHINERY_PLATE = 25;</code>
   */
  public static final int ORIGINAL_AGRICULTURAL_MACHINERY_PLATE_VALUE = 25;
  /**
   * <pre>
   *香港入出境号牌
   * </pre>
   *
   * <code>HONGKONG_PLATE = 26;</code>
   */
  public static final int HONGKONG_PLATE_VALUE = 26;
  /**
   * <pre>
   *澳门入出境号牌
   * </pre>
   *
   * <code>MACAU_PLATE = 27;</code>
   */
  public static final int MACAU_PLATE_VALUE = 27;
  /**
   * <pre>
   *武警号牌
   * </pre>
   *
   * <code>ARMED_POLICE_PLATE = 31;</code>
   */
  public static final int ARMED_POLICE_PLATE_VALUE = 31;
  /**
   * <pre>
   *军队号牌
   * </pre>
   *
   * <code>ARMY_PLATE = 32;</code>
   */
  public static final int ARMY_PLATE_VALUE = 32;
  /**
   * <pre>
   *无号牌
   * </pre>
   *
   * <code>NO_NUMBER_PLATE = 41;</code>
   */
  public static final int NO_NUMBER_PLATE_VALUE = 41;
  /**
   * <pre>
   *假号牌
   * </pre>
   *
   * <code>FAKE_PLATE = 42;</code>
   */
  public static final int FAKE_PLATE_VALUE = 42;
  /**
   * <pre>
   *挪用号牌
   * </pre>
   *
   * <code>MISAPPROPRIATION_PLATE = 43;</code>
   */
  public static final int MISAPPROPRIATION_PLATE_VALUE = 43;
  /**
   * <pre>
   *无法识别
   * </pre>
   *
   * <code>UNRECOGNIZED_PLATE = 44;</code>
   */
  public static final int UNRECOGNIZED_PLATE_VALUE = 44;
  /**
   * <pre>
   *大型新能源汽车（左侧黄色右侧绿色双拼色底黑字）
   * </pre>
   *
   * <code>LARGE_NEW_ENERGY_YELLOW_GREEN_PLATE = 51;</code>
   */
  public static final int LARGE_NEW_ENERGY_YELLOW_GREEN_PLATE_VALUE = 51;
  /**
   * <pre>
   *小型新能源汽车（渐变绿底黑字）
   * </pre>
   *
   * <code>SMALL_NEW_ENERGY_GREEN_PLATE = 52;</code>
   */
  public static final int SMALL_NEW_ENERGY_GREEN_PLATE_VALUE = 52;
  /**
   * <pre>
   *其他
   * </pre>
   *
   * <code>OTHER_PLATE = 99;</code>
   */
  public static final int OTHER_PLATE_VALUE = 99;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static PlateType valueOf(int value) {
    return forNumber(value);
  }

  public static PlateType forNumber(int value) {
    switch (value) {
      case 0: return UNKNOWN_PLATE;
      case 1: return LARGE_CAR_PLATE;
      case 2: return SMALL_CAR_PLATE;
      case 3: return EMBASSY_CAR_PLATE;
      case 4: return CONSULATE_CAR_PLATE;
      case 5: return OVERSEAS_CAR_PLATE;
      case 6: return FOREIGN_CAR_PLATE;
      case 7: return ORDINARY_MOTORCYCLE_PLATE;
      case 8: return MOPED_PLATE;
      case 9: return EMBASSY_MOTORCYCLE_PLATE;
      case 10: return CONSULATE_MOTORCYCLE_PLATE;
      case 11: return OVERSEAS_MOTORCYCLE_PLATE;
      case 12: return FOREIGN_MOTORCYCLE_PLATE;
      case 13: return LOW_SPEED_PLATE;
      case 14: return TRACTOR_PLATE;
      case 15: return TRAILER_PLATE;
      case 16: return COACH_CAR_PLATE;
      case 17: return COACH_MOTORCYCLE_PLATE;
      case 20: return TEMPORARY_ENTRY_PLATE;
      case 21: return TEMPORARY_ENTRY_MOTORCYCLE_PLATE;
      case 22: return TEMPORARY_DRIVING_PLATE;
      case 23: return POLICE_CAR_PLATE;
      case 24: return POLICE_MOTORCYCLE_PLATE;
      case 25: return ORIGINAL_AGRICULTURAL_MACHINERY_PLATE;
      case 26: return HONGKONG_PLATE;
      case 27: return MACAU_PLATE;
      case 31: return ARMED_POLICE_PLATE;
      case 32: return ARMY_PLATE;
      case 41: return NO_NUMBER_PLATE;
      case 42: return FAKE_PLATE;
      case 43: return MISAPPROPRIATION_PLATE;
      case 44: return UNRECOGNIZED_PLATE;
      case 51: return LARGE_NEW_ENERGY_YELLOW_GREEN_PLATE;
      case 52: return SMALL_NEW_ENERGY_GREEN_PLATE;
      case 99: return OTHER_PLATE;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<PlateType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      PlateType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<PlateType>() {
          public PlateType findValueByNumber(int number) {
            return PlateType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return road.data.proto.V2X.getDescriptor().getEnumTypes().get(9);
  }

  private static final PlateType[] VALUES = values();

  public static PlateType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private PlateType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.PlateType)
}

