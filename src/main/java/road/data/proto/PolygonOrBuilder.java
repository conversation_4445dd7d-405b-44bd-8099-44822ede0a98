// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface PolygonOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.Polygon)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *一组三维相对位置的定点组成的多边形区域，至少有4个点
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  java.util.List<road.data.proto.Position3D> 
      getPosList();
  /**
   * <pre>
   *一组三维相对位置的定点组成的多边形区域，至少有4个点
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  road.data.proto.Position3D getPos(int index);
  /**
   * <pre>
   *一组三维相对位置的定点组成的多边形区域，至少有4个点
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  int getPosCount();
  /**
   * <pre>
   *一组三维相对位置的定点组成的多边形区域，至少有4个点
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  java.util.List<? extends road.data.proto.Position3DOrBuilder> 
      getPosOrBuilderList();
  /**
   * <pre>
   *一组三维相对位置的定点组成的多边形区域，至少有4个点
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D pos = 1;</code>
   */
  road.data.proto.Position3DOrBuilder getPosOrBuilder(
      int index);
}
