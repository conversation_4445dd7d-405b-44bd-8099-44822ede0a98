// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *位置   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.Position3D}
 */
public  final class Position3D extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.Position3D)
    Position3DOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Position3D.newBuilder() to construct.
  private Position3D(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Position3D() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Position3D();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Position3D(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            lat_ = input.readInt32();
            break;
          }
          case 16: {

            lon_ = input.readInt32();
            break;
          }
          case 24: {

            ele_ = input.readInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Position3D_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Position3D_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.Position3D.class, road.data.proto.Position3D.Builder.class);
  }

  public static final int LAT_FIELD_NUMBER = 1;
  private int lat_;
  /**
   * <pre>
   *定义纬度数值，北纬为正，南纬为负。取值范围_900000000到900000001，分辨率1e_7°，数值900000001 表示未知或无效。
   * </pre>
   *
   * <code>int32 lat = 1;</code>
   */
  public int getLat() {
    return lat_;
  }

  public static final int LON_FIELD_NUMBER = 2;
  private int lon_;
  /**
   * <pre>
   *定义经度数值。东经为正，西经为负。分辨率为1e_7°， 取值范围_1799999999到1800000001，数值1800000001表示未知或无效。
   * </pre>
   *
   * <code>int32 lon = 2;</code>
   */
  public int getLon() {
    return lon_;
  }

  public static final int ELE_FIELD_NUMBER = 3;
  private int ele_;
  /**
   * <pre>
   *定义车辆海拔高程。分辨率为0.1米，取值范围_4096到61439，数值_4096表示无效数值。
   * </pre>
   *
   * <code>int32 ele = 3;</code>
   */
  public int getEle() {
    return ele_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (lat_ != 0) {
      output.writeInt32(1, lat_);
    }
    if (lon_ != 0) {
      output.writeInt32(2, lon_);
    }
    if (ele_ != 0) {
      output.writeInt32(3, ele_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (lat_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, lat_);
    }
    if (lon_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, lon_);
    }
    if (ele_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, ele_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.Position3D)) {
      return super.equals(obj);
    }
    road.data.proto.Position3D other = (road.data.proto.Position3D) obj;

    if (getLat()
        != other.getLat()) return false;
    if (getLon()
        != other.getLon()) return false;
    if (getEle()
        != other.getEle()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + LAT_FIELD_NUMBER;
    hash = (53 * hash) + getLat();
    hash = (37 * hash) + LON_FIELD_NUMBER;
    hash = (53 * hash) + getLon();
    hash = (37 * hash) + ELE_FIELD_NUMBER;
    hash = (53 * hash) + getEle();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.Position3D parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Position3D parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Position3D parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Position3D parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Position3D parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Position3D parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Position3D parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.Position3D parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.Position3D parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.Position3D parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.Position3D parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.Position3D parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.Position3D prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *位置   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.Position3D}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.Position3D)
      road.data.proto.Position3DOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Position3D_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Position3D_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.Position3D.class, road.data.proto.Position3D.Builder.class);
    }

    // Construct using road.data.proto.Position3D.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      lat_ = 0;

      lon_ = 0;

      ele_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Position3D_descriptor;
    }

    @java.lang.Override
    public road.data.proto.Position3D getDefaultInstanceForType() {
      return road.data.proto.Position3D.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.Position3D build() {
      road.data.proto.Position3D result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.Position3D buildPartial() {
      road.data.proto.Position3D result = new road.data.proto.Position3D(this);
      result.lat_ = lat_;
      result.lon_ = lon_;
      result.ele_ = ele_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.Position3D) {
        return mergeFrom((road.data.proto.Position3D)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.Position3D other) {
      if (other == road.data.proto.Position3D.getDefaultInstance()) return this;
      if (other.getLat() != 0) {
        setLat(other.getLat());
      }
      if (other.getLon() != 0) {
        setLon(other.getLon());
      }
      if (other.getEle() != 0) {
        setEle(other.getEle());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.Position3D parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.Position3D) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int lat_ ;
    /**
     * <pre>
     *定义纬度数值，北纬为正，南纬为负。取值范围_900000000到900000001，分辨率1e_7°，数值900000001 表示未知或无效。
     * </pre>
     *
     * <code>int32 lat = 1;</code>
     */
    public int getLat() {
      return lat_;
    }
    /**
     * <pre>
     *定义纬度数值，北纬为正，南纬为负。取值范围_900000000到900000001，分辨率1e_7°，数值900000001 表示未知或无效。
     * </pre>
     *
     * <code>int32 lat = 1;</code>
     */
    public Builder setLat(int value) {
      
      lat_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *定义纬度数值，北纬为正，南纬为负。取值范围_900000000到900000001，分辨率1e_7°，数值900000001 表示未知或无效。
     * </pre>
     *
     * <code>int32 lat = 1;</code>
     */
    public Builder clearLat() {
      
      lat_ = 0;
      onChanged();
      return this;
    }

    private int lon_ ;
    /**
     * <pre>
     *定义经度数值。东经为正，西经为负。分辨率为1e_7°， 取值范围_1799999999到1800000001，数值1800000001表示未知或无效。
     * </pre>
     *
     * <code>int32 lon = 2;</code>
     */
    public int getLon() {
      return lon_;
    }
    /**
     * <pre>
     *定义经度数值。东经为正，西经为负。分辨率为1e_7°， 取值范围_1799999999到1800000001，数值1800000001表示未知或无效。
     * </pre>
     *
     * <code>int32 lon = 2;</code>
     */
    public Builder setLon(int value) {
      
      lon_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *定义经度数值。东经为正，西经为负。分辨率为1e_7°， 取值范围_1799999999到1800000001，数值1800000001表示未知或无效。
     * </pre>
     *
     * <code>int32 lon = 2;</code>
     */
    public Builder clearLon() {
      
      lon_ = 0;
      onChanged();
      return this;
    }

    private int ele_ ;
    /**
     * <pre>
     *定义车辆海拔高程。分辨率为0.1米，取值范围_4096到61439，数值_4096表示无效数值。
     * </pre>
     *
     * <code>int32 ele = 3;</code>
     */
    public int getEle() {
      return ele_;
    }
    /**
     * <pre>
     *定义车辆海拔高程。分辨率为0.1米，取值范围_4096到61439，数值_4096表示无效数值。
     * </pre>
     *
     * <code>int32 ele = 3;</code>
     */
    public Builder setEle(int value) {
      
      ele_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *定义车辆海拔高程。分辨率为0.1米，取值范围_4096到61439，数值_4096表示无效数值。
     * </pre>
     *
     * <code>int32 ele = 3;</code>
     */
    public Builder clearEle() {
      
      ele_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.Position3D)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.Position3D)
  private static final road.data.proto.Position3D DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.Position3D();
  }

  public static road.data.proto.Position3D getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Position3D>
      PARSER = new com.google.protobuf.AbstractParser<Position3D>() {
    @java.lang.Override
    public Position3D parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Position3D(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Position3D> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Position3D> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.Position3D getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

