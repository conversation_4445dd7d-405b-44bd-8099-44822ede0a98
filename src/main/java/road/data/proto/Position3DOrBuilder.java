// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface Position3DOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.Position3D)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *定义纬度数值，北纬为正，南纬为负。取值范围_900000000到900000001，分辨率1e_7°，数值900000001 表示未知或无效。
   * </pre>
   *
   * <code>int32 lat = 1;</code>
   */
  int getLat();

  /**
   * <pre>
   *定义经度数值。东经为正，西经为负。分辨率为1e_7°， 取值范围_1799999999到1800000001，数值1800000001表示未知或无效。
   * </pre>
   *
   * <code>int32 lon = 2;</code>
   */
  int getLon();

  /**
   * <pre>
   *定义车辆海拔高程。分辨率为0.1米，取值范围_4096到61439，数值_4096表示无效数值。
   * </pre>
   *
   * <code>int32 ele = 3;</code>
   */
  int getEle();
}
