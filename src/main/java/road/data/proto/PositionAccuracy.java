// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *定位精度  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.PositionAccuracy}
 */
public  final class PositionAccuracy extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.PositionAccuracy)
    PositionAccuracyOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PositionAccuracy.newBuilder() to construct.
  private PositionAccuracy(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PositionAccuracy() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PositionAccuracy();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private PositionAccuracy(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            semiMajor_ = input.readInt32();
            break;
          }
          case 16: {

            semiMinor_ = input.readInt32();
            break;
          }
          case 24: {

            orientation_ = input.readInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PositionAccuracy_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PositionAccuracy_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.PositionAccuracy.class, road.data.proto.PositionAccuracy.Builder.class);
  }

  public static final int SEMIMAJOR_FIELD_NUMBER = 1;
  private int semiMajor_;
  /**
   * <pre>
   * 定义用椭圆模型表示的GNSS系统精度中半长轴的大小，单位为0.05米。
   * </pre>
   *
   * <code>int32 semiMajor = 1;</code>
   */
  public int getSemiMajor() {
    return semiMajor_;
  }

  public static final int SEMIMINOR_FIELD_NUMBER = 2;
  private int semiMinor_;
  /**
   * <pre>
   * 定义用椭圆模型表示的GNSS系统精度中半短轴的大小，单位为0.05米。
   * </pre>
   *
   * <code>int32 semiMinor = 2;</code>
   */
  public int getSemiMinor() {
    return semiMinor_;
  }

  public static final int ORIENTATION_FIELD_NUMBER = 3;
  private int orientation_;
  /**
   * <pre>
   * 定义用椭圆模型表示的GNSS系统精度中正北方向顺时针到最近半长轴的夹角大小，单位为0.0054932479°。
   * </pre>
   *
   * <code>int32 orientation = 3;</code>
   */
  public int getOrientation() {
    return orientation_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (semiMajor_ != 0) {
      output.writeInt32(1, semiMajor_);
    }
    if (semiMinor_ != 0) {
      output.writeInt32(2, semiMinor_);
    }
    if (orientation_ != 0) {
      output.writeInt32(3, orientation_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (semiMajor_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, semiMajor_);
    }
    if (semiMinor_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, semiMinor_);
    }
    if (orientation_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, orientation_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.PositionAccuracy)) {
      return super.equals(obj);
    }
    road.data.proto.PositionAccuracy other = (road.data.proto.PositionAccuracy) obj;

    if (getSemiMajor()
        != other.getSemiMajor()) return false;
    if (getSemiMinor()
        != other.getSemiMinor()) return false;
    if (getOrientation()
        != other.getOrientation()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SEMIMAJOR_FIELD_NUMBER;
    hash = (53 * hash) + getSemiMajor();
    hash = (37 * hash) + SEMIMINOR_FIELD_NUMBER;
    hash = (53 * hash) + getSemiMinor();
    hash = (37 * hash) + ORIENTATION_FIELD_NUMBER;
    hash = (53 * hash) + getOrientation();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.PositionAccuracy parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PositionAccuracy parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PositionAccuracy parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PositionAccuracy parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PositionAccuracy parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PositionAccuracy parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PositionAccuracy parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PositionAccuracy parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PositionAccuracy parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.PositionAccuracy parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PositionAccuracy parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PositionAccuracy parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.PositionAccuracy prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *定位精度  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.PositionAccuracy}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.PositionAccuracy)
      road.data.proto.PositionAccuracyOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PositionAccuracy_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PositionAccuracy_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.PositionAccuracy.class, road.data.proto.PositionAccuracy.Builder.class);
    }

    // Construct using road.data.proto.PositionAccuracy.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      semiMajor_ = 0;

      semiMinor_ = 0;

      orientation_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PositionAccuracy_descriptor;
    }

    @java.lang.Override
    public road.data.proto.PositionAccuracy getDefaultInstanceForType() {
      return road.data.proto.PositionAccuracy.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.PositionAccuracy build() {
      road.data.proto.PositionAccuracy result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.PositionAccuracy buildPartial() {
      road.data.proto.PositionAccuracy result = new road.data.proto.PositionAccuracy(this);
      result.semiMajor_ = semiMajor_;
      result.semiMinor_ = semiMinor_;
      result.orientation_ = orientation_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.PositionAccuracy) {
        return mergeFrom((road.data.proto.PositionAccuracy)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.PositionAccuracy other) {
      if (other == road.data.proto.PositionAccuracy.getDefaultInstance()) return this;
      if (other.getSemiMajor() != 0) {
        setSemiMajor(other.getSemiMajor());
      }
      if (other.getSemiMinor() != 0) {
        setSemiMinor(other.getSemiMinor());
      }
      if (other.getOrientation() != 0) {
        setOrientation(other.getOrientation());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.PositionAccuracy parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.PositionAccuracy) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int semiMajor_ ;
    /**
     * <pre>
     * 定义用椭圆模型表示的GNSS系统精度中半长轴的大小，单位为0.05米。
     * </pre>
     *
     * <code>int32 semiMajor = 1;</code>
     */
    public int getSemiMajor() {
      return semiMajor_;
    }
    /**
     * <pre>
     * 定义用椭圆模型表示的GNSS系统精度中半长轴的大小，单位为0.05米。
     * </pre>
     *
     * <code>int32 semiMajor = 1;</code>
     */
    public Builder setSemiMajor(int value) {
      
      semiMajor_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 定义用椭圆模型表示的GNSS系统精度中半长轴的大小，单位为0.05米。
     * </pre>
     *
     * <code>int32 semiMajor = 1;</code>
     */
    public Builder clearSemiMajor() {
      
      semiMajor_ = 0;
      onChanged();
      return this;
    }

    private int semiMinor_ ;
    /**
     * <pre>
     * 定义用椭圆模型表示的GNSS系统精度中半短轴的大小，单位为0.05米。
     * </pre>
     *
     * <code>int32 semiMinor = 2;</code>
     */
    public int getSemiMinor() {
      return semiMinor_;
    }
    /**
     * <pre>
     * 定义用椭圆模型表示的GNSS系统精度中半短轴的大小，单位为0.05米。
     * </pre>
     *
     * <code>int32 semiMinor = 2;</code>
     */
    public Builder setSemiMinor(int value) {
      
      semiMinor_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 定义用椭圆模型表示的GNSS系统精度中半短轴的大小，单位为0.05米。
     * </pre>
     *
     * <code>int32 semiMinor = 2;</code>
     */
    public Builder clearSemiMinor() {
      
      semiMinor_ = 0;
      onChanged();
      return this;
    }

    private int orientation_ ;
    /**
     * <pre>
     * 定义用椭圆模型表示的GNSS系统精度中正北方向顺时针到最近半长轴的夹角大小，单位为0.0054932479°。
     * </pre>
     *
     * <code>int32 orientation = 3;</code>
     */
    public int getOrientation() {
      return orientation_;
    }
    /**
     * <pre>
     * 定义用椭圆模型表示的GNSS系统精度中正北方向顺时针到最近半长轴的夹角大小，单位为0.0054932479°。
     * </pre>
     *
     * <code>int32 orientation = 3;</code>
     */
    public Builder setOrientation(int value) {
      
      orientation_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 定义用椭圆模型表示的GNSS系统精度中正北方向顺时针到最近半长轴的夹角大小，单位为0.0054932479°。
     * </pre>
     *
     * <code>int32 orientation = 3;</code>
     */
    public Builder clearOrientation() {
      
      orientation_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.PositionAccuracy)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.PositionAccuracy)
  private static final road.data.proto.PositionAccuracy DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.PositionAccuracy();
  }

  public static road.data.proto.PositionAccuracy getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PositionAccuracy>
      PARSER = new com.google.protobuf.AbstractParser<PositionAccuracy>() {
    @java.lang.Override
    public PositionAccuracy parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new PositionAccuracy(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<PositionAccuracy> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PositionAccuracy> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.PositionAccuracy getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

