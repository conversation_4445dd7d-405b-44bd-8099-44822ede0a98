// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface PositionAccuracyOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.PositionAccuracy)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 定义用椭圆模型表示的GNSS系统精度中半长轴的大小，单位为0.05米。
   * </pre>
   *
   * <code>int32 semiMajor = 1;</code>
   */
  int getSemiMajor();

  /**
   * <pre>
   * 定义用椭圆模型表示的GNSS系统精度中半短轴的大小，单位为0.05米。
   * </pre>
   *
   * <code>int32 semiMinor = 2;</code>
   */
  int getSemiMinor();

  /**
   * <pre>
   * 定义用椭圆模型表示的GNSS系统精度中正北方向顺时针到最近半长轴的夹角大小，单位为0.0054932479°。
   * </pre>
   *
   * <code>int32 orientation = 3;</code>
   */
  int getOrientation();
}
