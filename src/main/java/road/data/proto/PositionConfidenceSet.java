// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *位置精度  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.PositionConfidenceSet}
 */
public  final class PositionConfidenceSet extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.PositionConfidenceSet)
    PositionConfidenceSetOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PositionConfidenceSet.newBuilder() to construct.
  private PositionConfidenceSet(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PositionConfidenceSet() {
    posConfid_ = 0;
    eleConfid_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PositionConfidenceSet();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private PositionConfidenceSet(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {
            int rawValue = input.readEnum();

            posConfid_ = rawValue;
            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            eleConfid_ = rawValue;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PositionConfidenceSet_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PositionConfidenceSet_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.PositionConfidenceSet.class, road.data.proto.PositionConfidenceSet.Builder.class);
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.PositionConfidenceSet.PositionConfidence}
   */
  public enum PositionConfidence
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 不可用,  B0000 未配备或不可用
     * </pre>
     *
     * <code>UNAVAILABLE_POS_CONFID = 0;</code>
     */
    UNAVAILABLE_POS_CONFID(0),
    /**
     * <pre>
     * 大约 5*10^_3度
     * </pre>
     *
     * <code>POS_CONFID_500M = 1;</code>
     */
    POS_CONFID_500M(1),
    /**
     * <pre>
     * 大约 2*10^_3度
     * </pre>
     *
     * <code>POS_CONFID_200M = 2;</code>
     */
    POS_CONFID_200M(2),
    /**
     * <pre>
     * 大约 1*10^_3度
     * </pre>
     *
     * <code>POS_CONFID_100M = 3;</code>
     */
    POS_CONFID_100M(3),
    /**
     * <pre>
     * 大约 5*10^_4度
     * </pre>
     *
     * <code>POS_CONFID_50M = 4;</code>
     */
    POS_CONFID_50M(4),
    /**
     * <pre>
     * 大约 2*10^_4度
     * </pre>
     *
     * <code>POS_CONFID_20M = 5;</code>
     */
    POS_CONFID_20M(5),
    /**
     * <pre>
     * 约1*10^_4度
     * </pre>
     *
     * <code>POS_CONFID_10M = 6;</code>
     */
    POS_CONFID_10M(6),
    /**
     * <pre>
     * 大约 5*10^_5度
     * </pre>
     *
     * <code>POS_CONFID_5M = 7;</code>
     */
    POS_CONFID_5M(7),
    /**
     * <pre>
     * 大约 2*105度
     * </pre>
     *
     * <code>POS_CONFID_2M = 8;</code>
     */
    POS_CONFID_2M(8),
    /**
     * <pre>
     * 大约 1*10^_5度
     * </pre>
     *
     * <code>POS_CONFID_1M = 9;</code>
     */
    POS_CONFID_1M(9),
    /**
     * <pre>
     * 大约 5*10^_6度
     * </pre>
     *
     * <code>POS_CONFID_50CM = 10;</code>
     */
    POS_CONFID_50CM(10),
    /**
     * <pre>
     * 大约 2*10^_6度
     * </pre>
     *
     * <code>POS_CONFID_20CM = 11;</code>
     */
    POS_CONFID_20CM(11),
    /**
     * <pre>
     * 大约 1*10^_6度
     * </pre>
     *
     * <code>POS_CONFID_10CM = 12;</code>
     */
    POS_CONFID_10CM(12),
    /**
     * <pre>
     * 大约 5*10^_7度
     * </pre>
     *
     * <code>POS_CONFID_5CM = 13;</code>
     */
    POS_CONFID_5CM(13),
    /**
     * <pre>
     * 大约 2*10^_7 度
     * </pre>
     *
     * <code>POS_CONFID_2CM = 14;</code>
     */
    POS_CONFID_2CM(14),
    /**
     * <pre>
     * 大约 1*10^_7度
     * </pre>
     *
     * <code>POS_CONFID_1CM = 15;</code>
     */
    POS_CONFID_1CM(15),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * 不可用,  B0000 未配备或不可用
     * </pre>
     *
     * <code>UNAVAILABLE_POS_CONFID = 0;</code>
     */
    public static final int UNAVAILABLE_POS_CONFID_VALUE = 0;
    /**
     * <pre>
     * 大约 5*10^_3度
     * </pre>
     *
     * <code>POS_CONFID_500M = 1;</code>
     */
    public static final int POS_CONFID_500M_VALUE = 1;
    /**
     * <pre>
     * 大约 2*10^_3度
     * </pre>
     *
     * <code>POS_CONFID_200M = 2;</code>
     */
    public static final int POS_CONFID_200M_VALUE = 2;
    /**
     * <pre>
     * 大约 1*10^_3度
     * </pre>
     *
     * <code>POS_CONFID_100M = 3;</code>
     */
    public static final int POS_CONFID_100M_VALUE = 3;
    /**
     * <pre>
     * 大约 5*10^_4度
     * </pre>
     *
     * <code>POS_CONFID_50M = 4;</code>
     */
    public static final int POS_CONFID_50M_VALUE = 4;
    /**
     * <pre>
     * 大约 2*10^_4度
     * </pre>
     *
     * <code>POS_CONFID_20M = 5;</code>
     */
    public static final int POS_CONFID_20M_VALUE = 5;
    /**
     * <pre>
     * 约1*10^_4度
     * </pre>
     *
     * <code>POS_CONFID_10M = 6;</code>
     */
    public static final int POS_CONFID_10M_VALUE = 6;
    /**
     * <pre>
     * 大约 5*10^_5度
     * </pre>
     *
     * <code>POS_CONFID_5M = 7;</code>
     */
    public static final int POS_CONFID_5M_VALUE = 7;
    /**
     * <pre>
     * 大约 2*105度
     * </pre>
     *
     * <code>POS_CONFID_2M = 8;</code>
     */
    public static final int POS_CONFID_2M_VALUE = 8;
    /**
     * <pre>
     * 大约 1*10^_5度
     * </pre>
     *
     * <code>POS_CONFID_1M = 9;</code>
     */
    public static final int POS_CONFID_1M_VALUE = 9;
    /**
     * <pre>
     * 大约 5*10^_6度
     * </pre>
     *
     * <code>POS_CONFID_50CM = 10;</code>
     */
    public static final int POS_CONFID_50CM_VALUE = 10;
    /**
     * <pre>
     * 大约 2*10^_6度
     * </pre>
     *
     * <code>POS_CONFID_20CM = 11;</code>
     */
    public static final int POS_CONFID_20CM_VALUE = 11;
    /**
     * <pre>
     * 大约 1*10^_6度
     * </pre>
     *
     * <code>POS_CONFID_10CM = 12;</code>
     */
    public static final int POS_CONFID_10CM_VALUE = 12;
    /**
     * <pre>
     * 大约 5*10^_7度
     * </pre>
     *
     * <code>POS_CONFID_5CM = 13;</code>
     */
    public static final int POS_CONFID_5CM_VALUE = 13;
    /**
     * <pre>
     * 大约 2*10^_7 度
     * </pre>
     *
     * <code>POS_CONFID_2CM = 14;</code>
     */
    public static final int POS_CONFID_2CM_VALUE = 14;
    /**
     * <pre>
     * 大约 1*10^_7度
     * </pre>
     *
     * <code>POS_CONFID_1CM = 15;</code>
     */
    public static final int POS_CONFID_1CM_VALUE = 15;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static PositionConfidence valueOf(int value) {
      return forNumber(value);
    }

    public static PositionConfidence forNumber(int value) {
      switch (value) {
        case 0: return UNAVAILABLE_POS_CONFID;
        case 1: return POS_CONFID_500M;
        case 2: return POS_CONFID_200M;
        case 3: return POS_CONFID_100M;
        case 4: return POS_CONFID_50M;
        case 5: return POS_CONFID_20M;
        case 6: return POS_CONFID_10M;
        case 7: return POS_CONFID_5M;
        case 8: return POS_CONFID_2M;
        case 9: return POS_CONFID_1M;
        case 10: return POS_CONFID_50CM;
        case 11: return POS_CONFID_20CM;
        case 12: return POS_CONFID_10CM;
        case 13: return POS_CONFID_5CM;
        case 14: return POS_CONFID_2CM;
        case 15: return POS_CONFID_1CM;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<PositionConfidence>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        PositionConfidence> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<PositionConfidence>() {
            public PositionConfidence findValueByNumber(int number) {
              return PositionConfidence.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.PositionConfidenceSet.getDescriptor().getEnumTypes().get(0);
    }

    private static final PositionConfidence[] VALUES = values();

    public static PositionConfidence valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private PositionConfidence(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.PositionConfidenceSet.PositionConfidence)
  }

  /**
   * <pre>
   * 纵向坐标精度  数值描述了95%置信水平的车辆高程精度，该精度理论上只考虑了当前高程传感器的误差，但是，当系统能够自动检测错误并修正，相应的精度数值也应该提高。
   * </pre>
   *
   * Protobuf enum {@code cn.seisys.v2x.pb.PositionConfidenceSet.ElevationConfidence}
   */
  public enum ElevationConfidence
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 未配备或不可用
     * </pre>
     *
     * <code>UNAVAILABLE_ELE_CONFID = 0;</code>
     */
    UNAVAILABLE_ELE_CONFID(0),
    /**
     * <pre>
     *500米
     * </pre>
     *
     * <code>ELE_CONFID_500M = 1;</code>
     */
    ELE_CONFID_500M(1),
    /**
     * <pre>
     *200米
     * </pre>
     *
     * <code>ELE_CONFID_200M = 2;</code>
     */
    ELE_CONFID_200M(2),
    /**
     * <pre>
     *100米
     * </pre>
     *
     * <code>ELE_CONFID_100M = 3;</code>
     */
    ELE_CONFID_100M(3),
    /**
     * <pre>
     *50米
     * </pre>
     *
     * <code>ELE_CONFID_50M = 4;</code>
     */
    ELE_CONFID_50M(4),
    /**
     * <pre>
     *20米
     * </pre>
     *
     * <code>ELE_CONFID_20M = 5;</code>
     */
    ELE_CONFID_20M(5),
    /**
     * <pre>
     *10米
     * </pre>
     *
     * <code>ELE_CONFID_10M = 6;</code>
     */
    ELE_CONFID_10M(6),
    /**
     * <pre>
     *5米
     * </pre>
     *
     * <code>ELE_CONFID_5M = 7;</code>
     */
    ELE_CONFID_5M(7),
    /**
     * <pre>
     *2米
     * </pre>
     *
     * <code>ELE_CONFID_2M = 8;</code>
     */
    ELE_CONFID_2M(8),
    /**
     * <pre>
     *1米
     * </pre>
     *
     * <code>ELE_CONFID_1M = 9;</code>
     */
    ELE_CONFID_1M(9),
    /**
     * <pre>
     *50厘米
     * </pre>
     *
     * <code>ELE_CONFID_50CM = 10;</code>
     */
    ELE_CONFID_50CM(10),
    /**
     * <pre>
     *20厘米
     * </pre>
     *
     * <code>ELE_CONFID_20CM = 11;</code>
     */
    ELE_CONFID_20CM(11),
    /**
     * <pre>
     *10厘米
     * </pre>
     *
     * <code>ELE_CONFID_10CM = 12;</code>
     */
    ELE_CONFID_10CM(12),
    /**
     * <pre>
     *5厘米
     * </pre>
     *
     * <code>ELE_CONFID_5CM = 13;</code>
     */
    ELE_CONFID_5CM(13),
    /**
     * <pre>
     *2厘米
     * </pre>
     *
     * <code>ELE_CONFID_2CM = 14;</code>
     */
    ELE_CONFID_2CM(14),
    /**
     * <pre>
     *1厘米
     * </pre>
     *
     * <code>ELE_CONFID_1CM = 15;</code>
     */
    ELE_CONFID_1CM(15),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * 未配备或不可用
     * </pre>
     *
     * <code>UNAVAILABLE_ELE_CONFID = 0;</code>
     */
    public static final int UNAVAILABLE_ELE_CONFID_VALUE = 0;
    /**
     * <pre>
     *500米
     * </pre>
     *
     * <code>ELE_CONFID_500M = 1;</code>
     */
    public static final int ELE_CONFID_500M_VALUE = 1;
    /**
     * <pre>
     *200米
     * </pre>
     *
     * <code>ELE_CONFID_200M = 2;</code>
     */
    public static final int ELE_CONFID_200M_VALUE = 2;
    /**
     * <pre>
     *100米
     * </pre>
     *
     * <code>ELE_CONFID_100M = 3;</code>
     */
    public static final int ELE_CONFID_100M_VALUE = 3;
    /**
     * <pre>
     *50米
     * </pre>
     *
     * <code>ELE_CONFID_50M = 4;</code>
     */
    public static final int ELE_CONFID_50M_VALUE = 4;
    /**
     * <pre>
     *20米
     * </pre>
     *
     * <code>ELE_CONFID_20M = 5;</code>
     */
    public static final int ELE_CONFID_20M_VALUE = 5;
    /**
     * <pre>
     *10米
     * </pre>
     *
     * <code>ELE_CONFID_10M = 6;</code>
     */
    public static final int ELE_CONFID_10M_VALUE = 6;
    /**
     * <pre>
     *5米
     * </pre>
     *
     * <code>ELE_CONFID_5M = 7;</code>
     */
    public static final int ELE_CONFID_5M_VALUE = 7;
    /**
     * <pre>
     *2米
     * </pre>
     *
     * <code>ELE_CONFID_2M = 8;</code>
     */
    public static final int ELE_CONFID_2M_VALUE = 8;
    /**
     * <pre>
     *1米
     * </pre>
     *
     * <code>ELE_CONFID_1M = 9;</code>
     */
    public static final int ELE_CONFID_1M_VALUE = 9;
    /**
     * <pre>
     *50厘米
     * </pre>
     *
     * <code>ELE_CONFID_50CM = 10;</code>
     */
    public static final int ELE_CONFID_50CM_VALUE = 10;
    /**
     * <pre>
     *20厘米
     * </pre>
     *
     * <code>ELE_CONFID_20CM = 11;</code>
     */
    public static final int ELE_CONFID_20CM_VALUE = 11;
    /**
     * <pre>
     *10厘米
     * </pre>
     *
     * <code>ELE_CONFID_10CM = 12;</code>
     */
    public static final int ELE_CONFID_10CM_VALUE = 12;
    /**
     * <pre>
     *5厘米
     * </pre>
     *
     * <code>ELE_CONFID_5CM = 13;</code>
     */
    public static final int ELE_CONFID_5CM_VALUE = 13;
    /**
     * <pre>
     *2厘米
     * </pre>
     *
     * <code>ELE_CONFID_2CM = 14;</code>
     */
    public static final int ELE_CONFID_2CM_VALUE = 14;
    /**
     * <pre>
     *1厘米
     * </pre>
     *
     * <code>ELE_CONFID_1CM = 15;</code>
     */
    public static final int ELE_CONFID_1CM_VALUE = 15;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ElevationConfidence valueOf(int value) {
      return forNumber(value);
    }

    public static ElevationConfidence forNumber(int value) {
      switch (value) {
        case 0: return UNAVAILABLE_ELE_CONFID;
        case 1: return ELE_CONFID_500M;
        case 2: return ELE_CONFID_200M;
        case 3: return ELE_CONFID_100M;
        case 4: return ELE_CONFID_50M;
        case 5: return ELE_CONFID_20M;
        case 6: return ELE_CONFID_10M;
        case 7: return ELE_CONFID_5M;
        case 8: return ELE_CONFID_2M;
        case 9: return ELE_CONFID_1M;
        case 10: return ELE_CONFID_50CM;
        case 11: return ELE_CONFID_20CM;
        case 12: return ELE_CONFID_10CM;
        case 13: return ELE_CONFID_5CM;
        case 14: return ELE_CONFID_2CM;
        case 15: return ELE_CONFID_1CM;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ElevationConfidence>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ElevationConfidence> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ElevationConfidence>() {
            public ElevationConfidence findValueByNumber(int number) {
              return ElevationConfidence.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.PositionConfidenceSet.getDescriptor().getEnumTypes().get(1);
    }

    private static final ElevationConfidence[] VALUES = values();

    public static ElevationConfidence valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ElevationConfidence(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.PositionConfidenceSet.ElevationConfidence)
  }

  public static final int POSCONFID_FIELD_NUMBER = 1;
  private int posConfid_;
  /**
   * <pre>
   * 可选，平面坐标精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.PositionConfidence posConfid = 1;</code>
   */
  public int getPosConfidValue() {
    return posConfid_;
  }
  /**
   * <pre>
   * 可选，平面坐标精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.PositionConfidence posConfid = 1;</code>
   */
  public road.data.proto.PositionConfidenceSet.PositionConfidence getPosConfid() {
    @SuppressWarnings("deprecation")
    road.data.proto.PositionConfidenceSet.PositionConfidence result = road.data.proto.PositionConfidenceSet.PositionConfidence.valueOf(posConfid_);
    return result == null ? road.data.proto.PositionConfidenceSet.PositionConfidence.UNRECOGNIZED : result;
  }

  public static final int ELECONFID_FIELD_NUMBER = 2;
  private int eleConfid_;
  /**
   * <pre>
   * 可选，纵向坐标精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.ElevationConfidence eleConfid = 2;</code>
   */
  public int getEleConfidValue() {
    return eleConfid_;
  }
  /**
   * <pre>
   * 可选，纵向坐标精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.ElevationConfidence eleConfid = 2;</code>
   */
  public road.data.proto.PositionConfidenceSet.ElevationConfidence getEleConfid() {
    @SuppressWarnings("deprecation")
    road.data.proto.PositionConfidenceSet.ElevationConfidence result = road.data.proto.PositionConfidenceSet.ElevationConfidence.valueOf(eleConfid_);
    return result == null ? road.data.proto.PositionConfidenceSet.ElevationConfidence.UNRECOGNIZED : result;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (posConfid_ != road.data.proto.PositionConfidenceSet.PositionConfidence.UNAVAILABLE_POS_CONFID.getNumber()) {
      output.writeEnum(1, posConfid_);
    }
    if (eleConfid_ != road.data.proto.PositionConfidenceSet.ElevationConfidence.UNAVAILABLE_ELE_CONFID.getNumber()) {
      output.writeEnum(2, eleConfid_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (posConfid_ != road.data.proto.PositionConfidenceSet.PositionConfidence.UNAVAILABLE_POS_CONFID.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(1, posConfid_);
    }
    if (eleConfid_ != road.data.proto.PositionConfidenceSet.ElevationConfidence.UNAVAILABLE_ELE_CONFID.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, eleConfid_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.PositionConfidenceSet)) {
      return super.equals(obj);
    }
    road.data.proto.PositionConfidenceSet other = (road.data.proto.PositionConfidenceSet) obj;

    if (posConfid_ != other.posConfid_) return false;
    if (eleConfid_ != other.eleConfid_) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + POSCONFID_FIELD_NUMBER;
    hash = (53 * hash) + posConfid_;
    hash = (37 * hash) + ELECONFID_FIELD_NUMBER;
    hash = (53 * hash) + eleConfid_;
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.PositionConfidenceSet parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PositionConfidenceSet parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PositionConfidenceSet parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PositionConfidenceSet parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PositionConfidenceSet parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.PositionConfidenceSet parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.PositionConfidenceSet parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PositionConfidenceSet parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PositionConfidenceSet parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.PositionConfidenceSet parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.PositionConfidenceSet parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.PositionConfidenceSet parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.PositionConfidenceSet prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *位置精度  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.PositionConfidenceSet}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.PositionConfidenceSet)
      road.data.proto.PositionConfidenceSetOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PositionConfidenceSet_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PositionConfidenceSet_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.PositionConfidenceSet.class, road.data.proto.PositionConfidenceSet.Builder.class);
    }

    // Construct using road.data.proto.PositionConfidenceSet.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      posConfid_ = 0;

      eleConfid_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_PositionConfidenceSet_descriptor;
    }

    @java.lang.Override
    public road.data.proto.PositionConfidenceSet getDefaultInstanceForType() {
      return road.data.proto.PositionConfidenceSet.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.PositionConfidenceSet build() {
      road.data.proto.PositionConfidenceSet result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.PositionConfidenceSet buildPartial() {
      road.data.proto.PositionConfidenceSet result = new road.data.proto.PositionConfidenceSet(this);
      result.posConfid_ = posConfid_;
      result.eleConfid_ = eleConfid_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.PositionConfidenceSet) {
        return mergeFrom((road.data.proto.PositionConfidenceSet)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.PositionConfidenceSet other) {
      if (other == road.data.proto.PositionConfidenceSet.getDefaultInstance()) return this;
      if (other.posConfid_ != 0) {
        setPosConfidValue(other.getPosConfidValue());
      }
      if (other.eleConfid_ != 0) {
        setEleConfidValue(other.getEleConfidValue());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.PositionConfidenceSet parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.PositionConfidenceSet) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int posConfid_ = 0;
    /**
     * <pre>
     * 可选，平面坐标精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.PositionConfidence posConfid = 1;</code>
     */
    public int getPosConfidValue() {
      return posConfid_;
    }
    /**
     * <pre>
     * 可选，平面坐标精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.PositionConfidence posConfid = 1;</code>
     */
    public Builder setPosConfidValue(int value) {
      posConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，平面坐标精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.PositionConfidence posConfid = 1;</code>
     */
    public road.data.proto.PositionConfidenceSet.PositionConfidence getPosConfid() {
      @SuppressWarnings("deprecation")
      road.data.proto.PositionConfidenceSet.PositionConfidence result = road.data.proto.PositionConfidenceSet.PositionConfidence.valueOf(posConfid_);
      return result == null ? road.data.proto.PositionConfidenceSet.PositionConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，平面坐标精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.PositionConfidence posConfid = 1;</code>
     */
    public Builder setPosConfid(road.data.proto.PositionConfidenceSet.PositionConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      posConfid_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，平面坐标精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.PositionConfidence posConfid = 1;</code>
     */
    public Builder clearPosConfid() {
      
      posConfid_ = 0;
      onChanged();
      return this;
    }

    private int eleConfid_ = 0;
    /**
     * <pre>
     * 可选，纵向坐标精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.ElevationConfidence eleConfid = 2;</code>
     */
    public int getEleConfidValue() {
      return eleConfid_;
    }
    /**
     * <pre>
     * 可选，纵向坐标精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.ElevationConfidence eleConfid = 2;</code>
     */
    public Builder setEleConfidValue(int value) {
      eleConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，纵向坐标精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.ElevationConfidence eleConfid = 2;</code>
     */
    public road.data.proto.PositionConfidenceSet.ElevationConfidence getEleConfid() {
      @SuppressWarnings("deprecation")
      road.data.proto.PositionConfidenceSet.ElevationConfidence result = road.data.proto.PositionConfidenceSet.ElevationConfidence.valueOf(eleConfid_);
      return result == null ? road.data.proto.PositionConfidenceSet.ElevationConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，纵向坐标精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.ElevationConfidence eleConfid = 2;</code>
     */
    public Builder setEleConfid(road.data.proto.PositionConfidenceSet.ElevationConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      eleConfid_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，纵向坐标精度
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.ElevationConfidence eleConfid = 2;</code>
     */
    public Builder clearEleConfid() {
      
      eleConfid_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.PositionConfidenceSet)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.PositionConfidenceSet)
  private static final road.data.proto.PositionConfidenceSet DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.PositionConfidenceSet();
  }

  public static road.data.proto.PositionConfidenceSet getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PositionConfidenceSet>
      PARSER = new com.google.protobuf.AbstractParser<PositionConfidenceSet>() {
    @java.lang.Override
    public PositionConfidenceSet parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new PositionConfidenceSet(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<PositionConfidenceSet> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PositionConfidenceSet> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.PositionConfidenceSet getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

