// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface PositionConfidenceSetOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.PositionConfidenceSet)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 可选，平面坐标精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.PositionConfidence posConfid = 1;</code>
   */
  int getPosConfidValue();
  /**
   * <pre>
   * 可选，平面坐标精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.PositionConfidence posConfid = 1;</code>
   */
  road.data.proto.PositionConfidenceSet.PositionConfidence getPosConfid();

  /**
   * <pre>
   * 可选，纵向坐标精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.ElevationConfidence eleConfid = 2;</code>
   */
  int getEleConfidValue();
  /**
   * <pre>
   * 可选，纵向坐标精度
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PositionConfidenceSet.ElevationConfidence eleConfid = 2;</code>
   */
  road.data.proto.PositionConfidenceSet.ElevationConfidence getEleConfid();
}
