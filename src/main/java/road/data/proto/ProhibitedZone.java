// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *路段节点内的禁停区域 ProhibitedZone     
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ProhibitedZone}
 */
public  final class ProhibitedZone extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ProhibitedZone)
    ProhibitedZoneOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ProhibitedZone.newBuilder() to construct.
  private ProhibitedZone(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ProhibitedZone() {
    nonMotorVehicleProhibitedZones_ = java.util.Collections.emptyList();
    gridLineMarkingProhibitedZones_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ProhibitedZone();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ProhibitedZone(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.Polygon.Builder subBuilder = null;
            if (centralCirclePrihibitedZone_ != null) {
              subBuilder = centralCirclePrihibitedZone_.toBuilder();
            }
            centralCirclePrihibitedZone_ = input.readMessage(road.data.proto.Polygon.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(centralCirclePrihibitedZone_);
              centralCirclePrihibitedZone_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              nonMotorVehicleProhibitedZones_ = new java.util.ArrayList<road.data.proto.Polygon>();
              mutable_bitField0_ |= 0x00000001;
            }
            nonMotorVehicleProhibitedZones_.add(
                input.readMessage(road.data.proto.Polygon.parser(), extensionRegistry));
            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              gridLineMarkingProhibitedZones_ = new java.util.ArrayList<road.data.proto.Polygon>();
              mutable_bitField0_ |= 0x00000002;
            }
            gridLineMarkingProhibitedZones_.add(
                input.readMessage(road.data.proto.Polygon.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        nonMotorVehicleProhibitedZones_ = java.util.Collections.unmodifiableList(nonMotorVehicleProhibitedZones_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        gridLineMarkingProhibitedZones_ = java.util.Collections.unmodifiableList(gridLineMarkingProhibitedZones_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ProhibitedZone_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ProhibitedZone_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ProhibitedZone.class, road.data.proto.ProhibitedZone.Builder.class);
  }

  public static final int CENTRALCIRCLEPRIHIBITEDZONE_FIELD_NUMBER = 1;
  private road.data.proto.Polygon centralCirclePrihibitedZone_;
  /**
   * <pre>
   *可选，中心禁停区
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
   */
  public boolean hasCentralCirclePrihibitedZone() {
    return centralCirclePrihibitedZone_ != null;
  }
  /**
   * <pre>
   *可选，中心禁停区
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
   */
  public road.data.proto.Polygon getCentralCirclePrihibitedZone() {
    return centralCirclePrihibitedZone_ == null ? road.data.proto.Polygon.getDefaultInstance() : centralCirclePrihibitedZone_;
  }
  /**
   * <pre>
   *可选，中心禁停区
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
   */
  public road.data.proto.PolygonOrBuilder getCentralCirclePrihibitedZoneOrBuilder() {
    return getCentralCirclePrihibitedZone();
  }

  public static final int NONMOTORVEHICLEPROHIBITEDZONES_FIELD_NUMBER = 2;
  private java.util.List<road.data.proto.Polygon> nonMotorVehicleProhibitedZones_;
  /**
   * <pre>
   *可选，非机动车禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
   */
  public java.util.List<road.data.proto.Polygon> getNonMotorVehicleProhibitedZonesList() {
    return nonMotorVehicleProhibitedZones_;
  }
  /**
   * <pre>
   *可选，非机动车禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
   */
  public java.util.List<? extends road.data.proto.PolygonOrBuilder> 
      getNonMotorVehicleProhibitedZonesOrBuilderList() {
    return nonMotorVehicleProhibitedZones_;
  }
  /**
   * <pre>
   *可选，非机动车禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
   */
  public int getNonMotorVehicleProhibitedZonesCount() {
    return nonMotorVehicleProhibitedZones_.size();
  }
  /**
   * <pre>
   *可选，非机动车禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
   */
  public road.data.proto.Polygon getNonMotorVehicleProhibitedZones(int index) {
    return nonMotorVehicleProhibitedZones_.get(index);
  }
  /**
   * <pre>
   *可选，非机动车禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
   */
  public road.data.proto.PolygonOrBuilder getNonMotorVehicleProhibitedZonesOrBuilder(
      int index) {
    return nonMotorVehicleProhibitedZones_.get(index);
  }

  public static final int GRIDLINEMARKINGPROHIBITEDZONES_FIELD_NUMBER = 3;
  private java.util.List<road.data.proto.Polygon> gridLineMarkingProhibitedZones_;
  /**
   * <pre>
   *可选，标记禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
   */
  public java.util.List<road.data.proto.Polygon> getGridLineMarkingProhibitedZonesList() {
    return gridLineMarkingProhibitedZones_;
  }
  /**
   * <pre>
   *可选，标记禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
   */
  public java.util.List<? extends road.data.proto.PolygonOrBuilder> 
      getGridLineMarkingProhibitedZonesOrBuilderList() {
    return gridLineMarkingProhibitedZones_;
  }
  /**
   * <pre>
   *可选，标记禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
   */
  public int getGridLineMarkingProhibitedZonesCount() {
    return gridLineMarkingProhibitedZones_.size();
  }
  /**
   * <pre>
   *可选，标记禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
   */
  public road.data.proto.Polygon getGridLineMarkingProhibitedZones(int index) {
    return gridLineMarkingProhibitedZones_.get(index);
  }
  /**
   * <pre>
   *可选，标记禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
   */
  public road.data.proto.PolygonOrBuilder getGridLineMarkingProhibitedZonesOrBuilder(
      int index) {
    return gridLineMarkingProhibitedZones_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (centralCirclePrihibitedZone_ != null) {
      output.writeMessage(1, getCentralCirclePrihibitedZone());
    }
    for (int i = 0; i < nonMotorVehicleProhibitedZones_.size(); i++) {
      output.writeMessage(2, nonMotorVehicleProhibitedZones_.get(i));
    }
    for (int i = 0; i < gridLineMarkingProhibitedZones_.size(); i++) {
      output.writeMessage(3, gridLineMarkingProhibitedZones_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (centralCirclePrihibitedZone_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getCentralCirclePrihibitedZone());
    }
    for (int i = 0; i < nonMotorVehicleProhibitedZones_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, nonMotorVehicleProhibitedZones_.get(i));
    }
    for (int i = 0; i < gridLineMarkingProhibitedZones_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, gridLineMarkingProhibitedZones_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ProhibitedZone)) {
      return super.equals(obj);
    }
    road.data.proto.ProhibitedZone other = (road.data.proto.ProhibitedZone) obj;

    if (hasCentralCirclePrihibitedZone() != other.hasCentralCirclePrihibitedZone()) return false;
    if (hasCentralCirclePrihibitedZone()) {
      if (!getCentralCirclePrihibitedZone()
          .equals(other.getCentralCirclePrihibitedZone())) return false;
    }
    if (!getNonMotorVehicleProhibitedZonesList()
        .equals(other.getNonMotorVehicleProhibitedZonesList())) return false;
    if (!getGridLineMarkingProhibitedZonesList()
        .equals(other.getGridLineMarkingProhibitedZonesList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasCentralCirclePrihibitedZone()) {
      hash = (37 * hash) + CENTRALCIRCLEPRIHIBITEDZONE_FIELD_NUMBER;
      hash = (53 * hash) + getCentralCirclePrihibitedZone().hashCode();
    }
    if (getNonMotorVehicleProhibitedZonesCount() > 0) {
      hash = (37 * hash) + NONMOTORVEHICLEPROHIBITEDZONES_FIELD_NUMBER;
      hash = (53 * hash) + getNonMotorVehicleProhibitedZonesList().hashCode();
    }
    if (getGridLineMarkingProhibitedZonesCount() > 0) {
      hash = (37 * hash) + GRIDLINEMARKINGPROHIBITEDZONES_FIELD_NUMBER;
      hash = (53 * hash) + getGridLineMarkingProhibitedZonesList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ProhibitedZone parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ProhibitedZone parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ProhibitedZone parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ProhibitedZone parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ProhibitedZone parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ProhibitedZone parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ProhibitedZone parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ProhibitedZone parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ProhibitedZone parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ProhibitedZone parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ProhibitedZone parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ProhibitedZone parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ProhibitedZone prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *路段节点内的禁停区域 ProhibitedZone     
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ProhibitedZone}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ProhibitedZone)
      road.data.proto.ProhibitedZoneOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ProhibitedZone_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ProhibitedZone_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ProhibitedZone.class, road.data.proto.ProhibitedZone.Builder.class);
    }

    // Construct using road.data.proto.ProhibitedZone.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getNonMotorVehicleProhibitedZonesFieldBuilder();
        getGridLineMarkingProhibitedZonesFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (centralCirclePrihibitedZoneBuilder_ == null) {
        centralCirclePrihibitedZone_ = null;
      } else {
        centralCirclePrihibitedZone_ = null;
        centralCirclePrihibitedZoneBuilder_ = null;
      }
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        nonMotorVehicleProhibitedZones_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        nonMotorVehicleProhibitedZonesBuilder_.clear();
      }
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        gridLineMarkingProhibitedZones_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        gridLineMarkingProhibitedZonesBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ProhibitedZone_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ProhibitedZone getDefaultInstanceForType() {
      return road.data.proto.ProhibitedZone.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ProhibitedZone build() {
      road.data.proto.ProhibitedZone result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ProhibitedZone buildPartial() {
      road.data.proto.ProhibitedZone result = new road.data.proto.ProhibitedZone(this);
      int from_bitField0_ = bitField0_;
      if (centralCirclePrihibitedZoneBuilder_ == null) {
        result.centralCirclePrihibitedZone_ = centralCirclePrihibitedZone_;
      } else {
        result.centralCirclePrihibitedZone_ = centralCirclePrihibitedZoneBuilder_.build();
      }
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          nonMotorVehicleProhibitedZones_ = java.util.Collections.unmodifiableList(nonMotorVehicleProhibitedZones_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.nonMotorVehicleProhibitedZones_ = nonMotorVehicleProhibitedZones_;
      } else {
        result.nonMotorVehicleProhibitedZones_ = nonMotorVehicleProhibitedZonesBuilder_.build();
      }
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          gridLineMarkingProhibitedZones_ = java.util.Collections.unmodifiableList(gridLineMarkingProhibitedZones_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.gridLineMarkingProhibitedZones_ = gridLineMarkingProhibitedZones_;
      } else {
        result.gridLineMarkingProhibitedZones_ = gridLineMarkingProhibitedZonesBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ProhibitedZone) {
        return mergeFrom((road.data.proto.ProhibitedZone)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ProhibitedZone other) {
      if (other == road.data.proto.ProhibitedZone.getDefaultInstance()) return this;
      if (other.hasCentralCirclePrihibitedZone()) {
        mergeCentralCirclePrihibitedZone(other.getCentralCirclePrihibitedZone());
      }
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        if (!other.nonMotorVehicleProhibitedZones_.isEmpty()) {
          if (nonMotorVehicleProhibitedZones_.isEmpty()) {
            nonMotorVehicleProhibitedZones_ = other.nonMotorVehicleProhibitedZones_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureNonMotorVehicleProhibitedZonesIsMutable();
            nonMotorVehicleProhibitedZones_.addAll(other.nonMotorVehicleProhibitedZones_);
          }
          onChanged();
        }
      } else {
        if (!other.nonMotorVehicleProhibitedZones_.isEmpty()) {
          if (nonMotorVehicleProhibitedZonesBuilder_.isEmpty()) {
            nonMotorVehicleProhibitedZonesBuilder_.dispose();
            nonMotorVehicleProhibitedZonesBuilder_ = null;
            nonMotorVehicleProhibitedZones_ = other.nonMotorVehicleProhibitedZones_;
            bitField0_ = (bitField0_ & ~0x00000001);
            nonMotorVehicleProhibitedZonesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getNonMotorVehicleProhibitedZonesFieldBuilder() : null;
          } else {
            nonMotorVehicleProhibitedZonesBuilder_.addAllMessages(other.nonMotorVehicleProhibitedZones_);
          }
        }
      }
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        if (!other.gridLineMarkingProhibitedZones_.isEmpty()) {
          if (gridLineMarkingProhibitedZones_.isEmpty()) {
            gridLineMarkingProhibitedZones_ = other.gridLineMarkingProhibitedZones_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureGridLineMarkingProhibitedZonesIsMutable();
            gridLineMarkingProhibitedZones_.addAll(other.gridLineMarkingProhibitedZones_);
          }
          onChanged();
        }
      } else {
        if (!other.gridLineMarkingProhibitedZones_.isEmpty()) {
          if (gridLineMarkingProhibitedZonesBuilder_.isEmpty()) {
            gridLineMarkingProhibitedZonesBuilder_.dispose();
            gridLineMarkingProhibitedZonesBuilder_ = null;
            gridLineMarkingProhibitedZones_ = other.gridLineMarkingProhibitedZones_;
            bitField0_ = (bitField0_ & ~0x00000002);
            gridLineMarkingProhibitedZonesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getGridLineMarkingProhibitedZonesFieldBuilder() : null;
          } else {
            gridLineMarkingProhibitedZonesBuilder_.addAllMessages(other.gridLineMarkingProhibitedZones_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ProhibitedZone parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ProhibitedZone) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private road.data.proto.Polygon centralCirclePrihibitedZone_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> centralCirclePrihibitedZoneBuilder_;
    /**
     * <pre>
     *可选，中心禁停区
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
     */
    public boolean hasCentralCirclePrihibitedZone() {
      return centralCirclePrihibitedZoneBuilder_ != null || centralCirclePrihibitedZone_ != null;
    }
    /**
     * <pre>
     *可选，中心禁停区
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
     */
    public road.data.proto.Polygon getCentralCirclePrihibitedZone() {
      if (centralCirclePrihibitedZoneBuilder_ == null) {
        return centralCirclePrihibitedZone_ == null ? road.data.proto.Polygon.getDefaultInstance() : centralCirclePrihibitedZone_;
      } else {
        return centralCirclePrihibitedZoneBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，中心禁停区
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
     */
    public Builder setCentralCirclePrihibitedZone(road.data.proto.Polygon value) {
      if (centralCirclePrihibitedZoneBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        centralCirclePrihibitedZone_ = value;
        onChanged();
      } else {
        centralCirclePrihibitedZoneBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，中心禁停区
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
     */
    public Builder setCentralCirclePrihibitedZone(
        road.data.proto.Polygon.Builder builderForValue) {
      if (centralCirclePrihibitedZoneBuilder_ == null) {
        centralCirclePrihibitedZone_ = builderForValue.build();
        onChanged();
      } else {
        centralCirclePrihibitedZoneBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，中心禁停区
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
     */
    public Builder mergeCentralCirclePrihibitedZone(road.data.proto.Polygon value) {
      if (centralCirclePrihibitedZoneBuilder_ == null) {
        if (centralCirclePrihibitedZone_ != null) {
          centralCirclePrihibitedZone_ =
            road.data.proto.Polygon.newBuilder(centralCirclePrihibitedZone_).mergeFrom(value).buildPartial();
        } else {
          centralCirclePrihibitedZone_ = value;
        }
        onChanged();
      } else {
        centralCirclePrihibitedZoneBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，中心禁停区
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
     */
    public Builder clearCentralCirclePrihibitedZone() {
      if (centralCirclePrihibitedZoneBuilder_ == null) {
        centralCirclePrihibitedZone_ = null;
        onChanged();
      } else {
        centralCirclePrihibitedZone_ = null;
        centralCirclePrihibitedZoneBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，中心禁停区
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
     */
    public road.data.proto.Polygon.Builder getCentralCirclePrihibitedZoneBuilder() {
      
      onChanged();
      return getCentralCirclePrihibitedZoneFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，中心禁停区
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
     */
    public road.data.proto.PolygonOrBuilder getCentralCirclePrihibitedZoneOrBuilder() {
      if (centralCirclePrihibitedZoneBuilder_ != null) {
        return centralCirclePrihibitedZoneBuilder_.getMessageOrBuilder();
      } else {
        return centralCirclePrihibitedZone_ == null ?
            road.data.proto.Polygon.getDefaultInstance() : centralCirclePrihibitedZone_;
      }
    }
    /**
     * <pre>
     *可选，中心禁停区
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> 
        getCentralCirclePrihibitedZoneFieldBuilder() {
      if (centralCirclePrihibitedZoneBuilder_ == null) {
        centralCirclePrihibitedZoneBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder>(
                getCentralCirclePrihibitedZone(),
                getParentForChildren(),
                isClean());
        centralCirclePrihibitedZone_ = null;
      }
      return centralCirclePrihibitedZoneBuilder_;
    }

    private java.util.List<road.data.proto.Polygon> nonMotorVehicleProhibitedZones_ =
      java.util.Collections.emptyList();
    private void ensureNonMotorVehicleProhibitedZonesIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        nonMotorVehicleProhibitedZones_ = new java.util.ArrayList<road.data.proto.Polygon>(nonMotorVehicleProhibitedZones_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> nonMotorVehicleProhibitedZonesBuilder_;

    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public java.util.List<road.data.proto.Polygon> getNonMotorVehicleProhibitedZonesList() {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(nonMotorVehicleProhibitedZones_);
      } else {
        return nonMotorVehicleProhibitedZonesBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public int getNonMotorVehicleProhibitedZonesCount() {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        return nonMotorVehicleProhibitedZones_.size();
      } else {
        return nonMotorVehicleProhibitedZonesBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public road.data.proto.Polygon getNonMotorVehicleProhibitedZones(int index) {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        return nonMotorVehicleProhibitedZones_.get(index);
      } else {
        return nonMotorVehicleProhibitedZonesBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public Builder setNonMotorVehicleProhibitedZones(
        int index, road.data.proto.Polygon value) {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNonMotorVehicleProhibitedZonesIsMutable();
        nonMotorVehicleProhibitedZones_.set(index, value);
        onChanged();
      } else {
        nonMotorVehicleProhibitedZonesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public Builder setNonMotorVehicleProhibitedZones(
        int index, road.data.proto.Polygon.Builder builderForValue) {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        ensureNonMotorVehicleProhibitedZonesIsMutable();
        nonMotorVehicleProhibitedZones_.set(index, builderForValue.build());
        onChanged();
      } else {
        nonMotorVehicleProhibitedZonesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public Builder addNonMotorVehicleProhibitedZones(road.data.proto.Polygon value) {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNonMotorVehicleProhibitedZonesIsMutable();
        nonMotorVehicleProhibitedZones_.add(value);
        onChanged();
      } else {
        nonMotorVehicleProhibitedZonesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public Builder addNonMotorVehicleProhibitedZones(
        int index, road.data.proto.Polygon value) {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNonMotorVehicleProhibitedZonesIsMutable();
        nonMotorVehicleProhibitedZones_.add(index, value);
        onChanged();
      } else {
        nonMotorVehicleProhibitedZonesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public Builder addNonMotorVehicleProhibitedZones(
        road.data.proto.Polygon.Builder builderForValue) {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        ensureNonMotorVehicleProhibitedZonesIsMutable();
        nonMotorVehicleProhibitedZones_.add(builderForValue.build());
        onChanged();
      } else {
        nonMotorVehicleProhibitedZonesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public Builder addNonMotorVehicleProhibitedZones(
        int index, road.data.proto.Polygon.Builder builderForValue) {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        ensureNonMotorVehicleProhibitedZonesIsMutable();
        nonMotorVehicleProhibitedZones_.add(index, builderForValue.build());
        onChanged();
      } else {
        nonMotorVehicleProhibitedZonesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public Builder addAllNonMotorVehicleProhibitedZones(
        java.lang.Iterable<? extends road.data.proto.Polygon> values) {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        ensureNonMotorVehicleProhibitedZonesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, nonMotorVehicleProhibitedZones_);
        onChanged();
      } else {
        nonMotorVehicleProhibitedZonesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public Builder clearNonMotorVehicleProhibitedZones() {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        nonMotorVehicleProhibitedZones_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        nonMotorVehicleProhibitedZonesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public Builder removeNonMotorVehicleProhibitedZones(int index) {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        ensureNonMotorVehicleProhibitedZonesIsMutable();
        nonMotorVehicleProhibitedZones_.remove(index);
        onChanged();
      } else {
        nonMotorVehicleProhibitedZonesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public road.data.proto.Polygon.Builder getNonMotorVehicleProhibitedZonesBuilder(
        int index) {
      return getNonMotorVehicleProhibitedZonesFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public road.data.proto.PolygonOrBuilder getNonMotorVehicleProhibitedZonesOrBuilder(
        int index) {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        return nonMotorVehicleProhibitedZones_.get(index);  } else {
        return nonMotorVehicleProhibitedZonesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public java.util.List<? extends road.data.proto.PolygonOrBuilder> 
         getNonMotorVehicleProhibitedZonesOrBuilderList() {
      if (nonMotorVehicleProhibitedZonesBuilder_ != null) {
        return nonMotorVehicleProhibitedZonesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(nonMotorVehicleProhibitedZones_);
      }
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public road.data.proto.Polygon.Builder addNonMotorVehicleProhibitedZonesBuilder() {
      return getNonMotorVehicleProhibitedZonesFieldBuilder().addBuilder(
          road.data.proto.Polygon.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public road.data.proto.Polygon.Builder addNonMotorVehicleProhibitedZonesBuilder(
        int index) {
      return getNonMotorVehicleProhibitedZonesFieldBuilder().addBuilder(
          index, road.data.proto.Polygon.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，非机动车禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
     */
    public java.util.List<road.data.proto.Polygon.Builder> 
         getNonMotorVehicleProhibitedZonesBuilderList() {
      return getNonMotorVehicleProhibitedZonesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> 
        getNonMotorVehicleProhibitedZonesFieldBuilder() {
      if (nonMotorVehicleProhibitedZonesBuilder_ == null) {
        nonMotorVehicleProhibitedZonesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder>(
                nonMotorVehicleProhibitedZones_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        nonMotorVehicleProhibitedZones_ = null;
      }
      return nonMotorVehicleProhibitedZonesBuilder_;
    }

    private java.util.List<road.data.proto.Polygon> gridLineMarkingProhibitedZones_ =
      java.util.Collections.emptyList();
    private void ensureGridLineMarkingProhibitedZonesIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        gridLineMarkingProhibitedZones_ = new java.util.ArrayList<road.data.proto.Polygon>(gridLineMarkingProhibitedZones_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> gridLineMarkingProhibitedZonesBuilder_;

    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public java.util.List<road.data.proto.Polygon> getGridLineMarkingProhibitedZonesList() {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(gridLineMarkingProhibitedZones_);
      } else {
        return gridLineMarkingProhibitedZonesBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public int getGridLineMarkingProhibitedZonesCount() {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        return gridLineMarkingProhibitedZones_.size();
      } else {
        return gridLineMarkingProhibitedZonesBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public road.data.proto.Polygon getGridLineMarkingProhibitedZones(int index) {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        return gridLineMarkingProhibitedZones_.get(index);
      } else {
        return gridLineMarkingProhibitedZonesBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public Builder setGridLineMarkingProhibitedZones(
        int index, road.data.proto.Polygon value) {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGridLineMarkingProhibitedZonesIsMutable();
        gridLineMarkingProhibitedZones_.set(index, value);
        onChanged();
      } else {
        gridLineMarkingProhibitedZonesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public Builder setGridLineMarkingProhibitedZones(
        int index, road.data.proto.Polygon.Builder builderForValue) {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        ensureGridLineMarkingProhibitedZonesIsMutable();
        gridLineMarkingProhibitedZones_.set(index, builderForValue.build());
        onChanged();
      } else {
        gridLineMarkingProhibitedZonesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public Builder addGridLineMarkingProhibitedZones(road.data.proto.Polygon value) {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGridLineMarkingProhibitedZonesIsMutable();
        gridLineMarkingProhibitedZones_.add(value);
        onChanged();
      } else {
        gridLineMarkingProhibitedZonesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public Builder addGridLineMarkingProhibitedZones(
        int index, road.data.proto.Polygon value) {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGridLineMarkingProhibitedZonesIsMutable();
        gridLineMarkingProhibitedZones_.add(index, value);
        onChanged();
      } else {
        gridLineMarkingProhibitedZonesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public Builder addGridLineMarkingProhibitedZones(
        road.data.proto.Polygon.Builder builderForValue) {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        ensureGridLineMarkingProhibitedZonesIsMutable();
        gridLineMarkingProhibitedZones_.add(builderForValue.build());
        onChanged();
      } else {
        gridLineMarkingProhibitedZonesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public Builder addGridLineMarkingProhibitedZones(
        int index, road.data.proto.Polygon.Builder builderForValue) {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        ensureGridLineMarkingProhibitedZonesIsMutable();
        gridLineMarkingProhibitedZones_.add(index, builderForValue.build());
        onChanged();
      } else {
        gridLineMarkingProhibitedZonesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public Builder addAllGridLineMarkingProhibitedZones(
        java.lang.Iterable<? extends road.data.proto.Polygon> values) {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        ensureGridLineMarkingProhibitedZonesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, gridLineMarkingProhibitedZones_);
        onChanged();
      } else {
        gridLineMarkingProhibitedZonesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public Builder clearGridLineMarkingProhibitedZones() {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        gridLineMarkingProhibitedZones_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        gridLineMarkingProhibitedZonesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public Builder removeGridLineMarkingProhibitedZones(int index) {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        ensureGridLineMarkingProhibitedZonesIsMutable();
        gridLineMarkingProhibitedZones_.remove(index);
        onChanged();
      } else {
        gridLineMarkingProhibitedZonesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public road.data.proto.Polygon.Builder getGridLineMarkingProhibitedZonesBuilder(
        int index) {
      return getGridLineMarkingProhibitedZonesFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public road.data.proto.PolygonOrBuilder getGridLineMarkingProhibitedZonesOrBuilder(
        int index) {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        return gridLineMarkingProhibitedZones_.get(index);  } else {
        return gridLineMarkingProhibitedZonesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public java.util.List<? extends road.data.proto.PolygonOrBuilder> 
         getGridLineMarkingProhibitedZonesOrBuilderList() {
      if (gridLineMarkingProhibitedZonesBuilder_ != null) {
        return gridLineMarkingProhibitedZonesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(gridLineMarkingProhibitedZones_);
      }
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public road.data.proto.Polygon.Builder addGridLineMarkingProhibitedZonesBuilder() {
      return getGridLineMarkingProhibitedZonesFieldBuilder().addBuilder(
          road.data.proto.Polygon.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public road.data.proto.Polygon.Builder addGridLineMarkingProhibitedZonesBuilder(
        int index) {
      return getGridLineMarkingProhibitedZonesFieldBuilder().addBuilder(
          index, road.data.proto.Polygon.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，标记禁停区
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
     */
    public java.util.List<road.data.proto.Polygon.Builder> 
         getGridLineMarkingProhibitedZonesBuilderList() {
      return getGridLineMarkingProhibitedZonesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder> 
        getGridLineMarkingProhibitedZonesFieldBuilder() {
      if (gridLineMarkingProhibitedZonesBuilder_ == null) {
        gridLineMarkingProhibitedZonesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.Polygon, road.data.proto.Polygon.Builder, road.data.proto.PolygonOrBuilder>(
                gridLineMarkingProhibitedZones_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        gridLineMarkingProhibitedZones_ = null;
      }
      return gridLineMarkingProhibitedZonesBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ProhibitedZone)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ProhibitedZone)
  private static final road.data.proto.ProhibitedZone DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ProhibitedZone();
  }

  public static road.data.proto.ProhibitedZone getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ProhibitedZone>
      PARSER = new com.google.protobuf.AbstractParser<ProhibitedZone>() {
    @java.lang.Override
    public ProhibitedZone parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ProhibitedZone(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ProhibitedZone> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ProhibitedZone> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ProhibitedZone getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

