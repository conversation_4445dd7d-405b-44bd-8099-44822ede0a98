// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ProhibitedZoneOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ProhibitedZone)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *可选，中心禁停区
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
   */
  boolean hasCentralCirclePrihibitedZone();
  /**
   * <pre>
   *可选，中心禁停区
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
   */
  road.data.proto.Polygon getCentralCirclePrihibitedZone();
  /**
   * <pre>
   *可选，中心禁停区
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Polygon centralCirclePrihibitedZone = 1;</code>
   */
  road.data.proto.PolygonOrBuilder getCentralCirclePrihibitedZoneOrBuilder();

  /**
   * <pre>
   *可选，非机动车禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
   */
  java.util.List<road.data.proto.Polygon> 
      getNonMotorVehicleProhibitedZonesList();
  /**
   * <pre>
   *可选，非机动车禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
   */
  road.data.proto.Polygon getNonMotorVehicleProhibitedZones(int index);
  /**
   * <pre>
   *可选，非机动车禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
   */
  int getNonMotorVehicleProhibitedZonesCount();
  /**
   * <pre>
   *可选，非机动车禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
   */
  java.util.List<? extends road.data.proto.PolygonOrBuilder> 
      getNonMotorVehicleProhibitedZonesOrBuilderList();
  /**
   * <pre>
   *可选，非机动车禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon nonMotorVehicleProhibitedZones = 2;</code>
   */
  road.data.proto.PolygonOrBuilder getNonMotorVehicleProhibitedZonesOrBuilder(
      int index);

  /**
   * <pre>
   *可选，标记禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
   */
  java.util.List<road.data.proto.Polygon> 
      getGridLineMarkingProhibitedZonesList();
  /**
   * <pre>
   *可选，标记禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
   */
  road.data.proto.Polygon getGridLineMarkingProhibitedZones(int index);
  /**
   * <pre>
   *可选，标记禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
   */
  int getGridLineMarkingProhibitedZonesCount();
  /**
   * <pre>
   *可选，标记禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
   */
  java.util.List<? extends road.data.proto.PolygonOrBuilder> 
      getGridLineMarkingProhibitedZonesOrBuilderList();
  /**
   * <pre>
   *可选，标记禁停区
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Polygon gridLineMarkingProhibitedZones = 3;</code>
   */
  road.data.proto.PolygonOrBuilder getGridLineMarkingProhibitedZonesOrBuilder(
      int index);
}
