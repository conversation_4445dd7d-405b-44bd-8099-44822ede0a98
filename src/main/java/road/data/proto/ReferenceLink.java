// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *关联路段    
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ReferenceLink}
 */
public  final class ReferenceLink extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ReferenceLink)
    ReferenceLinkOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ReferenceLink.newBuilder() to construct.
  private ReferenceLink(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ReferenceLink() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ReferenceLink();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ReferenceLink(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (upstreamNodeId_ != null) {
              subBuilder = upstreamNodeId_.toBuilder();
            }
            upstreamNodeId_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(upstreamNodeId_);
              upstreamNodeId_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (downstreamNodeId_ != null) {
              subBuilder = downstreamNodeId_.toBuilder();
            }
            downstreamNodeId_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(downstreamNodeId_);
              downstreamNodeId_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            road.data.proto.ReferenceLanes.Builder subBuilder = null;
            if (referenceLanes_ != null) {
              subBuilder = referenceLanes_.toBuilder();
            }
            referenceLanes_ = input.readMessage(road.data.proto.ReferenceLanes.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(referenceLanes_);
              referenceLanes_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReferenceLink_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReferenceLink_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ReferenceLink.class, road.data.proto.ReferenceLink.Builder.class);
  }

  public static final int UPSTREAMNODEID_FIELD_NUMBER = 1;
  private road.data.proto.NodeReferenceId upstreamNodeId_;
  /**
   * <pre>
   *上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
   */
  public boolean hasUpstreamNodeId() {
    return upstreamNodeId_ != null;
  }
  /**
   * <pre>
   *上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
   */
  public road.data.proto.NodeReferenceId getUpstreamNodeId() {
    return upstreamNodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
  }
  /**
   * <pre>
   *上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder() {
    return getUpstreamNodeId();
  }

  public static final int DOWNSTREAMNODEID_FIELD_NUMBER = 2;
  private road.data.proto.NodeReferenceId downstreamNodeId_;
  /**
   * <pre>
   *下LaneStatInfo游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
   */
  public boolean hasDownstreamNodeId() {
    return downstreamNodeId_ != null;
  }
  /**
   * <pre>
   *下LaneStatInfo游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
   */
  public road.data.proto.NodeReferenceId getDownstreamNodeId() {
    return downstreamNodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : downstreamNodeId_;
  }
  /**
   * <pre>
   *下LaneStatInfo游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getDownstreamNodeIdOrBuilder() {
    return getDownstreamNodeId();
  }

  public static final int REFERENCELANES_FIELD_NUMBER = 3;
  private road.data.proto.ReferenceLanes referenceLanes_;
  /**
   * <pre>
   *可选，定义路段中指定的关联车道
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
   */
  public boolean hasReferenceLanes() {
    return referenceLanes_ != null;
  }
  /**
   * <pre>
   *可选，定义路段中指定的关联车道
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
   */
  public road.data.proto.ReferenceLanes getReferenceLanes() {
    return referenceLanes_ == null ? road.data.proto.ReferenceLanes.getDefaultInstance() : referenceLanes_;
  }
  /**
   * <pre>
   *可选，定义路段中指定的关联车道
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
   */
  public road.data.proto.ReferenceLanesOrBuilder getReferenceLanesOrBuilder() {
    return getReferenceLanes();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (upstreamNodeId_ != null) {
      output.writeMessage(1, getUpstreamNodeId());
    }
    if (downstreamNodeId_ != null) {
      output.writeMessage(2, getDownstreamNodeId());
    }
    if (referenceLanes_ != null) {
      output.writeMessage(3, getReferenceLanes());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (upstreamNodeId_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getUpstreamNodeId());
    }
    if (downstreamNodeId_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getDownstreamNodeId());
    }
    if (referenceLanes_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getReferenceLanes());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ReferenceLink)) {
      return super.equals(obj);
    }
    road.data.proto.ReferenceLink other = (road.data.proto.ReferenceLink) obj;

    if (hasUpstreamNodeId() != other.hasUpstreamNodeId()) return false;
    if (hasUpstreamNodeId()) {
      if (!getUpstreamNodeId()
          .equals(other.getUpstreamNodeId())) return false;
    }
    if (hasDownstreamNodeId() != other.hasDownstreamNodeId()) return false;
    if (hasDownstreamNodeId()) {
      if (!getDownstreamNodeId()
          .equals(other.getDownstreamNodeId())) return false;
    }
    if (hasReferenceLanes() != other.hasReferenceLanes()) return false;
    if (hasReferenceLanes()) {
      if (!getReferenceLanes()
          .equals(other.getReferenceLanes())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasUpstreamNodeId()) {
      hash = (37 * hash) + UPSTREAMNODEID_FIELD_NUMBER;
      hash = (53 * hash) + getUpstreamNodeId().hashCode();
    }
    if (hasDownstreamNodeId()) {
      hash = (37 * hash) + DOWNSTREAMNODEID_FIELD_NUMBER;
      hash = (53 * hash) + getDownstreamNodeId().hashCode();
    }
    if (hasReferenceLanes()) {
      hash = (37 * hash) + REFERENCELANES_FIELD_NUMBER;
      hash = (53 * hash) + getReferenceLanes().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ReferenceLink parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReferenceLink parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReferenceLink parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReferenceLink parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReferenceLink parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReferenceLink parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReferenceLink parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReferenceLink parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReferenceLink parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ReferenceLink parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReferenceLink parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReferenceLink parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ReferenceLink prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *关联路段    
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ReferenceLink}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ReferenceLink)
      road.data.proto.ReferenceLinkOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReferenceLink_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReferenceLink_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ReferenceLink.class, road.data.proto.ReferenceLink.Builder.class);
    }

    // Construct using road.data.proto.ReferenceLink.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = null;
      } else {
        upstreamNodeId_ = null;
        upstreamNodeIdBuilder_ = null;
      }
      if (downstreamNodeIdBuilder_ == null) {
        downstreamNodeId_ = null;
      } else {
        downstreamNodeId_ = null;
        downstreamNodeIdBuilder_ = null;
      }
      if (referenceLanesBuilder_ == null) {
        referenceLanes_ = null;
      } else {
        referenceLanes_ = null;
        referenceLanesBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReferenceLink_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ReferenceLink getDefaultInstanceForType() {
      return road.data.proto.ReferenceLink.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ReferenceLink build() {
      road.data.proto.ReferenceLink result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ReferenceLink buildPartial() {
      road.data.proto.ReferenceLink result = new road.data.proto.ReferenceLink(this);
      if (upstreamNodeIdBuilder_ == null) {
        result.upstreamNodeId_ = upstreamNodeId_;
      } else {
        result.upstreamNodeId_ = upstreamNodeIdBuilder_.build();
      }
      if (downstreamNodeIdBuilder_ == null) {
        result.downstreamNodeId_ = downstreamNodeId_;
      } else {
        result.downstreamNodeId_ = downstreamNodeIdBuilder_.build();
      }
      if (referenceLanesBuilder_ == null) {
        result.referenceLanes_ = referenceLanes_;
      } else {
        result.referenceLanes_ = referenceLanesBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ReferenceLink) {
        return mergeFrom((road.data.proto.ReferenceLink)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ReferenceLink other) {
      if (other == road.data.proto.ReferenceLink.getDefaultInstance()) return this;
      if (other.hasUpstreamNodeId()) {
        mergeUpstreamNodeId(other.getUpstreamNodeId());
      }
      if (other.hasDownstreamNodeId()) {
        mergeDownstreamNodeId(other.getDownstreamNodeId());
      }
      if (other.hasReferenceLanes()) {
        mergeReferenceLanes(other.getReferenceLanes());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ReferenceLink parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ReferenceLink) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.NodeReferenceId upstreamNodeId_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> upstreamNodeIdBuilder_;
    /**
     * <pre>
     *上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public boolean hasUpstreamNodeId() {
      return upstreamNodeIdBuilder_ != null || upstreamNodeId_ != null;
    }
    /**
     * <pre>
     *上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceId getUpstreamNodeId() {
      if (upstreamNodeIdBuilder_ == null) {
        return upstreamNodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
      } else {
        return upstreamNodeIdBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public Builder setUpstreamNodeId(road.data.proto.NodeReferenceId value) {
      if (upstreamNodeIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        upstreamNodeId_ = value;
        onChanged();
      } else {
        upstreamNodeIdBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public Builder setUpstreamNodeId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = builderForValue.build();
        onChanged();
      } else {
        upstreamNodeIdBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public Builder mergeUpstreamNodeId(road.data.proto.NodeReferenceId value) {
      if (upstreamNodeIdBuilder_ == null) {
        if (upstreamNodeId_ != null) {
          upstreamNodeId_ =
            road.data.proto.NodeReferenceId.newBuilder(upstreamNodeId_).mergeFrom(value).buildPartial();
        } else {
          upstreamNodeId_ = value;
        }
        onChanged();
      } else {
        upstreamNodeIdBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public Builder clearUpstreamNodeId() {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeId_ = null;
        onChanged();
      } else {
        upstreamNodeId_ = null;
        upstreamNodeIdBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getUpstreamNodeIdBuilder() {
      
      onChanged();
      return getUpstreamNodeIdFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder() {
      if (upstreamNodeIdBuilder_ != null) {
        return upstreamNodeIdBuilder_.getMessageOrBuilder();
      } else {
        return upstreamNodeId_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : upstreamNodeId_;
      }
    }
    /**
     * <pre>
     *上游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getUpstreamNodeIdFieldBuilder() {
      if (upstreamNodeIdBuilder_ == null) {
        upstreamNodeIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getUpstreamNodeId(),
                getParentForChildren(),
                isClean());
        upstreamNodeId_ = null;
      }
      return upstreamNodeIdBuilder_;
    }

    private road.data.proto.NodeReferenceId downstreamNodeId_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> downstreamNodeIdBuilder_;
    /**
     * <pre>
     *下LaneStatInfo游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
     */
    public boolean hasDownstreamNodeId() {
      return downstreamNodeIdBuilder_ != null || downstreamNodeId_ != null;
    }
    /**
     * <pre>
     *下LaneStatInfo游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
     */
    public road.data.proto.NodeReferenceId getDownstreamNodeId() {
      if (downstreamNodeIdBuilder_ == null) {
        return downstreamNodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : downstreamNodeId_;
      } else {
        return downstreamNodeIdBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *下LaneStatInfo游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
     */
    public Builder setDownstreamNodeId(road.data.proto.NodeReferenceId value) {
      if (downstreamNodeIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        downstreamNodeId_ = value;
        onChanged();
      } else {
        downstreamNodeIdBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *下LaneStatInfo游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
     */
    public Builder setDownstreamNodeId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (downstreamNodeIdBuilder_ == null) {
        downstreamNodeId_ = builderForValue.build();
        onChanged();
      } else {
        downstreamNodeIdBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *下LaneStatInfo游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
     */
    public Builder mergeDownstreamNodeId(road.data.proto.NodeReferenceId value) {
      if (downstreamNodeIdBuilder_ == null) {
        if (downstreamNodeId_ != null) {
          downstreamNodeId_ =
            road.data.proto.NodeReferenceId.newBuilder(downstreamNodeId_).mergeFrom(value).buildPartial();
        } else {
          downstreamNodeId_ = value;
        }
        onChanged();
      } else {
        downstreamNodeIdBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *下LaneStatInfo游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
     */
    public Builder clearDownstreamNodeId() {
      if (downstreamNodeIdBuilder_ == null) {
        downstreamNodeId_ = null;
        onChanged();
      } else {
        downstreamNodeId_ = null;
        downstreamNodeIdBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *下LaneStatInfo游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getDownstreamNodeIdBuilder() {
      
      onChanged();
      return getDownstreamNodeIdFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *下LaneStatInfo游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getDownstreamNodeIdOrBuilder() {
      if (downstreamNodeIdBuilder_ != null) {
        return downstreamNodeIdBuilder_.getMessageOrBuilder();
      } else {
        return downstreamNodeId_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : downstreamNodeId_;
      }
    }
    /**
     * <pre>
     *下LaneStatInfo游节点ID
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getDownstreamNodeIdFieldBuilder() {
      if (downstreamNodeIdBuilder_ == null) {
        downstreamNodeIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getDownstreamNodeId(),
                getParentForChildren(),
                isClean());
        downstreamNodeId_ = null;
      }
      return downstreamNodeIdBuilder_;
    }

    private road.data.proto.ReferenceLanes referenceLanes_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReferenceLanes, road.data.proto.ReferenceLanes.Builder, road.data.proto.ReferenceLanesOrBuilder> referenceLanesBuilder_;
    /**
     * <pre>
     *可选，定义路段中指定的关联车道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
     */
    public boolean hasReferenceLanes() {
      return referenceLanesBuilder_ != null || referenceLanes_ != null;
    }
    /**
     * <pre>
     *可选，定义路段中指定的关联车道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
     */
    public road.data.proto.ReferenceLanes getReferenceLanes() {
      if (referenceLanesBuilder_ == null) {
        return referenceLanes_ == null ? road.data.proto.ReferenceLanes.getDefaultInstance() : referenceLanes_;
      } else {
        return referenceLanesBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，定义路段中指定的关联车道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
     */
    public Builder setReferenceLanes(road.data.proto.ReferenceLanes value) {
      if (referenceLanesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        referenceLanes_ = value;
        onChanged();
      } else {
        referenceLanesBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，定义路段中指定的关联车道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
     */
    public Builder setReferenceLanes(
        road.data.proto.ReferenceLanes.Builder builderForValue) {
      if (referenceLanesBuilder_ == null) {
        referenceLanes_ = builderForValue.build();
        onChanged();
      } else {
        referenceLanesBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，定义路段中指定的关联车道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
     */
    public Builder mergeReferenceLanes(road.data.proto.ReferenceLanes value) {
      if (referenceLanesBuilder_ == null) {
        if (referenceLanes_ != null) {
          referenceLanes_ =
            road.data.proto.ReferenceLanes.newBuilder(referenceLanes_).mergeFrom(value).buildPartial();
        } else {
          referenceLanes_ = value;
        }
        onChanged();
      } else {
        referenceLanesBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，定义路段中指定的关联车道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
     */
    public Builder clearReferenceLanes() {
      if (referenceLanesBuilder_ == null) {
        referenceLanes_ = null;
        onChanged();
      } else {
        referenceLanes_ = null;
        referenceLanesBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，定义路段中指定的关联车道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
     */
    public road.data.proto.ReferenceLanes.Builder getReferenceLanesBuilder() {
      
      onChanged();
      return getReferenceLanesFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，定义路段中指定的关联车道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
     */
    public road.data.proto.ReferenceLanesOrBuilder getReferenceLanesOrBuilder() {
      if (referenceLanesBuilder_ != null) {
        return referenceLanesBuilder_.getMessageOrBuilder();
      } else {
        return referenceLanes_ == null ?
            road.data.proto.ReferenceLanes.getDefaultInstance() : referenceLanes_;
      }
    }
    /**
     * <pre>
     *可选，定义路段中指定的关联车道
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReferenceLanes, road.data.proto.ReferenceLanes.Builder, road.data.proto.ReferenceLanesOrBuilder> 
        getReferenceLanesFieldBuilder() {
      if (referenceLanesBuilder_ == null) {
        referenceLanesBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ReferenceLanes, road.data.proto.ReferenceLanes.Builder, road.data.proto.ReferenceLanesOrBuilder>(
                getReferenceLanes(),
                getParentForChildren(),
                isClean());
        referenceLanes_ = null;
      }
      return referenceLanesBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ReferenceLink)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ReferenceLink)
  private static final road.data.proto.ReferenceLink DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ReferenceLink();
  }

  public static road.data.proto.ReferenceLink getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ReferenceLink>
      PARSER = new com.google.protobuf.AbstractParser<ReferenceLink>() {
    @java.lang.Override
    public ReferenceLink parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ReferenceLink(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ReferenceLink> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ReferenceLink> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ReferenceLink getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

