// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ReferenceLinkOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ReferenceLink)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
   */
  boolean hasUpstreamNodeId();
  /**
   * <pre>
   *上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
   */
  road.data.proto.NodeReferenceId getUpstreamNodeId();
  /**
   * <pre>
   *上游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upstreamNodeId = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getUpstreamNodeIdOrBuilder();

  /**
   * <pre>
   *下LaneStatInfo游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
   */
  boolean hasDownstreamNodeId();
  /**
   * <pre>
   *下LaneStatInfo游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
   */
  road.data.proto.NodeReferenceId getDownstreamNodeId();
  /**
   * <pre>
   *下LaneStatInfo游节点ID
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId downstreamNodeId = 2;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getDownstreamNodeIdOrBuilder();

  /**
   * <pre>
   *可选，定义路段中指定的关联车道
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
   */
  boolean hasReferenceLanes();
  /**
   * <pre>
   *可选，定义路段中指定的关联车道
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
   */
  road.data.proto.ReferenceLanes getReferenceLanes();
  /**
   * <pre>
   *可选，定义路段中指定的关联车道
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReferenceLanes referenceLanes = 3;</code>
   */
  road.data.proto.ReferenceLanesOrBuilder getReferenceLanesOrBuilder();
}
