// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *关联路径    
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ReferencePath}
 */
public  final class ReferencePath extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ReferencePath)
    ReferencePathOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ReferencePath.newBuilder() to construct.
  private ReferencePath(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ReferencePath() {
    activePath_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ReferencePath();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ReferencePath(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              activePath_ = new java.util.ArrayList<road.data.proto.Position3D>();
              mutable_bitField0_ |= 0x00000001;
            }
            activePath_.add(
                input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry));
            break;
          }
          case 16: {

            pathRadius_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        activePath_ = java.util.Collections.unmodifiableList(activePath_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReferencePath_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReferencePath_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ReferencePath.class, road.data.proto.ReferencePath.Builder.class);
  }

  public static final int ACTIVEPATH_FIELD_NUMBER = 1;
  private java.util.List<road.data.proto.Position3D> activePath_;
  /**
   * <pre>
   *影响路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
   */
  public java.util.List<road.data.proto.Position3D> getActivePathList() {
    return activePath_;
  }
  /**
   * <pre>
   *影响路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
   */
  public java.util.List<? extends road.data.proto.Position3DOrBuilder> 
      getActivePathOrBuilderList() {
    return activePath_;
  }
  /**
   * <pre>
   *影响路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
   */
  public int getActivePathCount() {
    return activePath_.size();
  }
  /**
   * <pre>
   *影响路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
   */
  public road.data.proto.Position3D getActivePath(int index) {
    return activePath_.get(index);
  }
  /**
   * <pre>
   *影响路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
   */
  public road.data.proto.Position3DOrBuilder getActivePathOrBuilder(
      int index) {
    return activePath_.get(index);
  }

  public static final int PATHRADIUS_FIELD_NUMBER = 2;
  private int pathRadius_;
  /**
   * <pre>
   *路段半径，单位：0.1m
   * </pre>
   *
   * <code>uint32 pathRadius = 2;</code>
   */
  public int getPathRadius() {
    return pathRadius_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < activePath_.size(); i++) {
      output.writeMessage(1, activePath_.get(i));
    }
    if (pathRadius_ != 0) {
      output.writeUInt32(2, pathRadius_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < activePath_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, activePath_.get(i));
    }
    if (pathRadius_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, pathRadius_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ReferencePath)) {
      return super.equals(obj);
    }
    road.data.proto.ReferencePath other = (road.data.proto.ReferencePath) obj;

    if (!getActivePathList()
        .equals(other.getActivePathList())) return false;
    if (getPathRadius()
        != other.getPathRadius()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getActivePathCount() > 0) {
      hash = (37 * hash) + ACTIVEPATH_FIELD_NUMBER;
      hash = (53 * hash) + getActivePathList().hashCode();
    }
    hash = (37 * hash) + PATHRADIUS_FIELD_NUMBER;
    hash = (53 * hash) + getPathRadius();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ReferencePath parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReferencePath parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReferencePath parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReferencePath parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReferencePath parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReferencePath parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReferencePath parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReferencePath parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReferencePath parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ReferencePath parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReferencePath parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReferencePath parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ReferencePath prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *关联路径    
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ReferencePath}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ReferencePath)
      road.data.proto.ReferencePathOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReferencePath_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReferencePath_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ReferencePath.class, road.data.proto.ReferencePath.Builder.class);
    }

    // Construct using road.data.proto.ReferencePath.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getActivePathFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (activePathBuilder_ == null) {
        activePath_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        activePathBuilder_.clear();
      }
      pathRadius_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReferencePath_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ReferencePath getDefaultInstanceForType() {
      return road.data.proto.ReferencePath.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ReferencePath build() {
      road.data.proto.ReferencePath result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ReferencePath buildPartial() {
      road.data.proto.ReferencePath result = new road.data.proto.ReferencePath(this);
      int from_bitField0_ = bitField0_;
      if (activePathBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          activePath_ = java.util.Collections.unmodifiableList(activePath_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.activePath_ = activePath_;
      } else {
        result.activePath_ = activePathBuilder_.build();
      }
      result.pathRadius_ = pathRadius_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ReferencePath) {
        return mergeFrom((road.data.proto.ReferencePath)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ReferencePath other) {
      if (other == road.data.proto.ReferencePath.getDefaultInstance()) return this;
      if (activePathBuilder_ == null) {
        if (!other.activePath_.isEmpty()) {
          if (activePath_.isEmpty()) {
            activePath_ = other.activePath_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureActivePathIsMutable();
            activePath_.addAll(other.activePath_);
          }
          onChanged();
        }
      } else {
        if (!other.activePath_.isEmpty()) {
          if (activePathBuilder_.isEmpty()) {
            activePathBuilder_.dispose();
            activePathBuilder_ = null;
            activePath_ = other.activePath_;
            bitField0_ = (bitField0_ & ~0x00000001);
            activePathBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getActivePathFieldBuilder() : null;
          } else {
            activePathBuilder_.addAllMessages(other.activePath_);
          }
        }
      }
      if (other.getPathRadius() != 0) {
        setPathRadius(other.getPathRadius());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ReferencePath parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ReferencePath) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.util.List<road.data.proto.Position3D> activePath_ =
      java.util.Collections.emptyList();
    private void ensureActivePathIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        activePath_ = new java.util.ArrayList<road.data.proto.Position3D>(activePath_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> activePathBuilder_;

    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public java.util.List<road.data.proto.Position3D> getActivePathList() {
      if (activePathBuilder_ == null) {
        return java.util.Collections.unmodifiableList(activePath_);
      } else {
        return activePathBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public int getActivePathCount() {
      if (activePathBuilder_ == null) {
        return activePath_.size();
      } else {
        return activePathBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public road.data.proto.Position3D getActivePath(int index) {
      if (activePathBuilder_ == null) {
        return activePath_.get(index);
      } else {
        return activePathBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public Builder setActivePath(
        int index, road.data.proto.Position3D value) {
      if (activePathBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureActivePathIsMutable();
        activePath_.set(index, value);
        onChanged();
      } else {
        activePathBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public Builder setActivePath(
        int index, road.data.proto.Position3D.Builder builderForValue) {
      if (activePathBuilder_ == null) {
        ensureActivePathIsMutable();
        activePath_.set(index, builderForValue.build());
        onChanged();
      } else {
        activePathBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public Builder addActivePath(road.data.proto.Position3D value) {
      if (activePathBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureActivePathIsMutable();
        activePath_.add(value);
        onChanged();
      } else {
        activePathBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public Builder addActivePath(
        int index, road.data.proto.Position3D value) {
      if (activePathBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureActivePathIsMutable();
        activePath_.add(index, value);
        onChanged();
      } else {
        activePathBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public Builder addActivePath(
        road.data.proto.Position3D.Builder builderForValue) {
      if (activePathBuilder_ == null) {
        ensureActivePathIsMutable();
        activePath_.add(builderForValue.build());
        onChanged();
      } else {
        activePathBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public Builder addActivePath(
        int index, road.data.proto.Position3D.Builder builderForValue) {
      if (activePathBuilder_ == null) {
        ensureActivePathIsMutable();
        activePath_.add(index, builderForValue.build());
        onChanged();
      } else {
        activePathBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public Builder addAllActivePath(
        java.lang.Iterable<? extends road.data.proto.Position3D> values) {
      if (activePathBuilder_ == null) {
        ensureActivePathIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, activePath_);
        onChanged();
      } else {
        activePathBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public Builder clearActivePath() {
      if (activePathBuilder_ == null) {
        activePath_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        activePathBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public Builder removeActivePath(int index) {
      if (activePathBuilder_ == null) {
        ensureActivePathIsMutable();
        activePath_.remove(index);
        onChanged();
      } else {
        activePathBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public road.data.proto.Position3D.Builder getActivePathBuilder(
        int index) {
      return getActivePathFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public road.data.proto.Position3DOrBuilder getActivePathOrBuilder(
        int index) {
      if (activePathBuilder_ == null) {
        return activePath_.get(index);  } else {
        return activePathBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public java.util.List<? extends road.data.proto.Position3DOrBuilder> 
         getActivePathOrBuilderList() {
      if (activePathBuilder_ != null) {
        return activePathBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(activePath_);
      }
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public road.data.proto.Position3D.Builder addActivePathBuilder() {
      return getActivePathFieldBuilder().addBuilder(
          road.data.proto.Position3D.getDefaultInstance());
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public road.data.proto.Position3D.Builder addActivePathBuilder(
        int index) {
      return getActivePathFieldBuilder().addBuilder(
          index, road.data.proto.Position3D.getDefaultInstance());
    }
    /**
     * <pre>
     *影响路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
     */
    public java.util.List<road.data.proto.Position3D.Builder> 
         getActivePathBuilderList() {
      return getActivePathFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getActivePathFieldBuilder() {
      if (activePathBuilder_ == null) {
        activePathBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                activePath_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        activePath_ = null;
      }
      return activePathBuilder_;
    }

    private int pathRadius_ ;
    /**
     * <pre>
     *路段半径，单位：0.1m
     * </pre>
     *
     * <code>uint32 pathRadius = 2;</code>
     */
    public int getPathRadius() {
      return pathRadius_;
    }
    /**
     * <pre>
     *路段半径，单位：0.1m
     * </pre>
     *
     * <code>uint32 pathRadius = 2;</code>
     */
    public Builder setPathRadius(int value) {
      
      pathRadius_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *路段半径，单位：0.1m
     * </pre>
     *
     * <code>uint32 pathRadius = 2;</code>
     */
    public Builder clearPathRadius() {
      
      pathRadius_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ReferencePath)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ReferencePath)
  private static final road.data.proto.ReferencePath DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ReferencePath();
  }

  public static road.data.proto.ReferencePath getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ReferencePath>
      PARSER = new com.google.protobuf.AbstractParser<ReferencePath>() {
    @java.lang.Override
    public ReferencePath parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ReferencePath(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ReferencePath> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ReferencePath> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ReferencePath getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

