// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ReferencePathOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ReferencePath)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *影响路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
   */
  java.util.List<road.data.proto.Position3D> 
      getActivePathList();
  /**
   * <pre>
   *影响路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
   */
  road.data.proto.Position3D getActivePath(int index);
  /**
   * <pre>
   *影响路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
   */
  int getActivePathCount();
  /**
   * <pre>
   *影响路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
   */
  java.util.List<? extends road.data.proto.Position3DOrBuilder> 
      getActivePathOrBuilderList();
  /**
   * <pre>
   *影响路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.Position3D activePath = 1;</code>
   */
  road.data.proto.Position3DOrBuilder getActivePathOrBuilder(
      int index);

  /**
   * <pre>
   *路段半径，单位：0.1m
   * </pre>
   *
   * <code>uint32 pathRadius = 2;</code>
   */
  int getPathRadius();
}
