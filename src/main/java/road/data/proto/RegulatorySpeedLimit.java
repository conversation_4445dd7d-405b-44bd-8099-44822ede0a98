// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *限速   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.RegulatorySpeedLimit}
 */
public  final class RegulatorySpeedLimit extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.RegulatorySpeedLimit)
    RegulatorySpeedLimitOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RegulatorySpeedLimit.newBuilder() to construct.
  private RegulatorySpeedLimit(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RegulatorySpeedLimit() {
    speedLimitType_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RegulatorySpeedLimit();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RegulatorySpeedLimit(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {
            int rawValue = input.readEnum();

            speedLimitType_ = rawValue;
            break;
          }
          case 16: {

            speed_ = input.readInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RegulatorySpeedLimit_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RegulatorySpeedLimit_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.RegulatorySpeedLimit.class, road.data.proto.RegulatorySpeedLimit.Builder.class);
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.RegulatorySpeedLimit.SpeedLimitType}
   */
  public enum SpeedLimitType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *不可用
     * </pre>
     *
     * <code>SPEED_LIMIT_UNKNOWN = 0;</code>
     */
    SPEED_LIMIT_UNKNOWN(0),
    /**
     * <pre>
     *仅在限制有效时发送
     * </pre>
     *
     * <code>MAX_SPEED_IN_SCHOOL_ZONE = 1;</code>
     */
    MAX_SPEED_IN_SCHOOL_ZONE(1),
    /**
     * <pre>
     *校车随时发车
     * </pre>
     *
     * <code>MAX_SPEED_INSCHOOL_ZONE_WHEN_CHILDREN_ARE_PRESENT = 2;</code>
     */
    MAX_SPEED_INSCHOOL_ZONE_WHEN_CHILDREN_ARE_PRESENT(2),
    /**
     * <pre>
     *用于工作区、事故区等
     * </pre>
     *
     * <code>MAX_SPEED_INCONSTRUCTION_ZONE = 3;</code>
     */
    MAX_SPEED_INCONSTRUCTION_ZONE(3),
    /**
     * <pre>
     *车辆最小速度
     * </pre>
     *
     * <code>VEHICLE_MIN_SPEED = 4;</code>
     */
    VEHICLE_MIN_SPEED(4),
    /**
     * <pre>
     *一般交通的监管限速
     * </pre>
     *
     * <code>VEHICLE_SPEED = 5;</code>
     */
    VEHICLE_SPEED(5),
    /**
     * <pre>
     *车辆夜间最大速度
     * </pre>
     *
     * <code>VEHICLE_NIGHT_MAX_SPEED = 6;</code>
     */
    VEHICLE_NIGHT_MAX_SPEED(6),
    /**
     * <pre>
     *卡车最小速度
     * </pre>
     *
     * <code>TRUCK_MIN_SPEED = 7;</code>
     */
    TRUCK_MIN_SPEED(7),
    /**
     * <pre>
     *卡车最大速度
     * </pre>
     *
     * <code>TRUCK_MAX_SPEED = 8;</code>
     */
    TRUCK_MAX_SPEED(8),
    /**
     * <pre>
     *卡车夜间最大速度
     * </pre>
     *
     * <code>TRUCK_NIGHT_MAX_SPEED = 9;</code>
     */
    TRUCK_NIGHT_MAX_SPEED(9),
    /**
     * <pre>
     *拖车最小速度
     * </pre>
     *
     * <code>VEHICLES_WITH_TRAILERS_MIN_SPEED = 10;</code>
     */
    VEHICLES_WITH_TRAILERS_MIN_SPEED(10),
    /**
     * <pre>
     *拖车最大速度
     * </pre>
     *
     * <code>VEHICLES_WITH_TRAILERS_MAX_SPEED = 11;</code>
     */
    VEHICLES_WITH_TRAILERS_MAX_SPEED(11),
    /**
     * <pre>
     *拖车夜间最大速度
     * </pre>
     *
     * <code>VEHICLES_WITHTRAILERS_NIGHT_MAX_SPEED = 12;</code>
     */
    VEHICLES_WITHTRAILERS_NIGHT_MAX_SPEED(12),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *不可用
     * </pre>
     *
     * <code>SPEED_LIMIT_UNKNOWN = 0;</code>
     */
    public static final int SPEED_LIMIT_UNKNOWN_VALUE = 0;
    /**
     * <pre>
     *仅在限制有效时发送
     * </pre>
     *
     * <code>MAX_SPEED_IN_SCHOOL_ZONE = 1;</code>
     */
    public static final int MAX_SPEED_IN_SCHOOL_ZONE_VALUE = 1;
    /**
     * <pre>
     *校车随时发车
     * </pre>
     *
     * <code>MAX_SPEED_INSCHOOL_ZONE_WHEN_CHILDREN_ARE_PRESENT = 2;</code>
     */
    public static final int MAX_SPEED_INSCHOOL_ZONE_WHEN_CHILDREN_ARE_PRESENT_VALUE = 2;
    /**
     * <pre>
     *用于工作区、事故区等
     * </pre>
     *
     * <code>MAX_SPEED_INCONSTRUCTION_ZONE = 3;</code>
     */
    public static final int MAX_SPEED_INCONSTRUCTION_ZONE_VALUE = 3;
    /**
     * <pre>
     *车辆最小速度
     * </pre>
     *
     * <code>VEHICLE_MIN_SPEED = 4;</code>
     */
    public static final int VEHICLE_MIN_SPEED_VALUE = 4;
    /**
     * <pre>
     *一般交通的监管限速
     * </pre>
     *
     * <code>VEHICLE_SPEED = 5;</code>
     */
    public static final int VEHICLE_SPEED_VALUE = 5;
    /**
     * <pre>
     *车辆夜间最大速度
     * </pre>
     *
     * <code>VEHICLE_NIGHT_MAX_SPEED = 6;</code>
     */
    public static final int VEHICLE_NIGHT_MAX_SPEED_VALUE = 6;
    /**
     * <pre>
     *卡车最小速度
     * </pre>
     *
     * <code>TRUCK_MIN_SPEED = 7;</code>
     */
    public static final int TRUCK_MIN_SPEED_VALUE = 7;
    /**
     * <pre>
     *卡车最大速度
     * </pre>
     *
     * <code>TRUCK_MAX_SPEED = 8;</code>
     */
    public static final int TRUCK_MAX_SPEED_VALUE = 8;
    /**
     * <pre>
     *卡车夜间最大速度
     * </pre>
     *
     * <code>TRUCK_NIGHT_MAX_SPEED = 9;</code>
     */
    public static final int TRUCK_NIGHT_MAX_SPEED_VALUE = 9;
    /**
     * <pre>
     *拖车最小速度
     * </pre>
     *
     * <code>VEHICLES_WITH_TRAILERS_MIN_SPEED = 10;</code>
     */
    public static final int VEHICLES_WITH_TRAILERS_MIN_SPEED_VALUE = 10;
    /**
     * <pre>
     *拖车最大速度
     * </pre>
     *
     * <code>VEHICLES_WITH_TRAILERS_MAX_SPEED = 11;</code>
     */
    public static final int VEHICLES_WITH_TRAILERS_MAX_SPEED_VALUE = 11;
    /**
     * <pre>
     *拖车夜间最大速度
     * </pre>
     *
     * <code>VEHICLES_WITHTRAILERS_NIGHT_MAX_SPEED = 12;</code>
     */
    public static final int VEHICLES_WITHTRAILERS_NIGHT_MAX_SPEED_VALUE = 12;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static SpeedLimitType valueOf(int value) {
      return forNumber(value);
    }

    public static SpeedLimitType forNumber(int value) {
      switch (value) {
        case 0: return SPEED_LIMIT_UNKNOWN;
        case 1: return MAX_SPEED_IN_SCHOOL_ZONE;
        case 2: return MAX_SPEED_INSCHOOL_ZONE_WHEN_CHILDREN_ARE_PRESENT;
        case 3: return MAX_SPEED_INCONSTRUCTION_ZONE;
        case 4: return VEHICLE_MIN_SPEED;
        case 5: return VEHICLE_SPEED;
        case 6: return VEHICLE_NIGHT_MAX_SPEED;
        case 7: return TRUCK_MIN_SPEED;
        case 8: return TRUCK_MAX_SPEED;
        case 9: return TRUCK_NIGHT_MAX_SPEED;
        case 10: return VEHICLES_WITH_TRAILERS_MIN_SPEED;
        case 11: return VEHICLES_WITH_TRAILERS_MAX_SPEED;
        case 12: return VEHICLES_WITHTRAILERS_NIGHT_MAX_SPEED;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SpeedLimitType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        SpeedLimitType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SpeedLimitType>() {
            public SpeedLimitType findValueByNumber(int number) {
              return SpeedLimitType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.RegulatorySpeedLimit.getDescriptor().getEnumTypes().get(0);
    }

    private static final SpeedLimitType[] VALUES = values();

    public static SpeedLimitType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private SpeedLimitType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.RegulatorySpeedLimit.SpeedLimitType)
  }

  public static final int SPEEDLIMITTYPE_FIELD_NUMBER = 1;
  private int speedLimitType_;
  /**
   * <pre>
   *遵循的监管速度类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RegulatorySpeedLimit.SpeedLimitType speedLimitType = 1;</code>
   */
  public int getSpeedLimitTypeValue() {
    return speedLimitType_;
  }
  /**
   * <pre>
   *遵循的监管速度类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RegulatorySpeedLimit.SpeedLimitType speedLimitType = 1;</code>
   */
  public road.data.proto.RegulatorySpeedLimit.SpeedLimitType getSpeedLimitType() {
    @SuppressWarnings("deprecation")
    road.data.proto.RegulatorySpeedLimit.SpeedLimitType result = road.data.proto.RegulatorySpeedLimit.SpeedLimitType.valueOf(speedLimitType_);
    return result == null ? road.data.proto.RegulatorySpeedLimit.SpeedLimitType.UNRECOGNIZED : result;
  }

  public static final int SPEED_FIELD_NUMBER = 2;
  private int speed_;
  /**
   * <pre>
   *分辨率为0.02 m/s。数值8191表示无效数值。
   * </pre>
   *
   * <code>int32 speed = 2;</code>
   */
  public int getSpeed() {
    return speed_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (speedLimitType_ != road.data.proto.RegulatorySpeedLimit.SpeedLimitType.SPEED_LIMIT_UNKNOWN.getNumber()) {
      output.writeEnum(1, speedLimitType_);
    }
    if (speed_ != 0) {
      output.writeInt32(2, speed_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (speedLimitType_ != road.data.proto.RegulatorySpeedLimit.SpeedLimitType.SPEED_LIMIT_UNKNOWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(1, speedLimitType_);
    }
    if (speed_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, speed_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.RegulatorySpeedLimit)) {
      return super.equals(obj);
    }
    road.data.proto.RegulatorySpeedLimit other = (road.data.proto.RegulatorySpeedLimit) obj;

    if (speedLimitType_ != other.speedLimitType_) return false;
    if (getSpeed()
        != other.getSpeed()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SPEEDLIMITTYPE_FIELD_NUMBER;
    hash = (53 * hash) + speedLimitType_;
    hash = (37 * hash) + SPEED_FIELD_NUMBER;
    hash = (53 * hash) + getSpeed();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.RegulatorySpeedLimit parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RegulatorySpeedLimit parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RegulatorySpeedLimit parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RegulatorySpeedLimit parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RegulatorySpeedLimit parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RegulatorySpeedLimit parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RegulatorySpeedLimit parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RegulatorySpeedLimit parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RegulatorySpeedLimit parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.RegulatorySpeedLimit parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RegulatorySpeedLimit parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RegulatorySpeedLimit parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.RegulatorySpeedLimit prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *限速   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.RegulatorySpeedLimit}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.RegulatorySpeedLimit)
      road.data.proto.RegulatorySpeedLimitOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RegulatorySpeedLimit_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RegulatorySpeedLimit_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.RegulatorySpeedLimit.class, road.data.proto.RegulatorySpeedLimit.Builder.class);
    }

    // Construct using road.data.proto.RegulatorySpeedLimit.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      speedLimitType_ = 0;

      speed_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RegulatorySpeedLimit_descriptor;
    }

    @java.lang.Override
    public road.data.proto.RegulatorySpeedLimit getDefaultInstanceForType() {
      return road.data.proto.RegulatorySpeedLimit.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.RegulatorySpeedLimit build() {
      road.data.proto.RegulatorySpeedLimit result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.RegulatorySpeedLimit buildPartial() {
      road.data.proto.RegulatorySpeedLimit result = new road.data.proto.RegulatorySpeedLimit(this);
      result.speedLimitType_ = speedLimitType_;
      result.speed_ = speed_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.RegulatorySpeedLimit) {
        return mergeFrom((road.data.proto.RegulatorySpeedLimit)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.RegulatorySpeedLimit other) {
      if (other == road.data.proto.RegulatorySpeedLimit.getDefaultInstance()) return this;
      if (other.speedLimitType_ != 0) {
        setSpeedLimitTypeValue(other.getSpeedLimitTypeValue());
      }
      if (other.getSpeed() != 0) {
        setSpeed(other.getSpeed());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.RegulatorySpeedLimit parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.RegulatorySpeedLimit) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int speedLimitType_ = 0;
    /**
     * <pre>
     *遵循的监管速度类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RegulatorySpeedLimit.SpeedLimitType speedLimitType = 1;</code>
     */
    public int getSpeedLimitTypeValue() {
      return speedLimitType_;
    }
    /**
     * <pre>
     *遵循的监管速度类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RegulatorySpeedLimit.SpeedLimitType speedLimitType = 1;</code>
     */
    public Builder setSpeedLimitTypeValue(int value) {
      speedLimitType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *遵循的监管速度类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RegulatorySpeedLimit.SpeedLimitType speedLimitType = 1;</code>
     */
    public road.data.proto.RegulatorySpeedLimit.SpeedLimitType getSpeedLimitType() {
      @SuppressWarnings("deprecation")
      road.data.proto.RegulatorySpeedLimit.SpeedLimitType result = road.data.proto.RegulatorySpeedLimit.SpeedLimitType.valueOf(speedLimitType_);
      return result == null ? road.data.proto.RegulatorySpeedLimit.SpeedLimitType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *遵循的监管速度类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RegulatorySpeedLimit.SpeedLimitType speedLimitType = 1;</code>
     */
    public Builder setSpeedLimitType(road.data.proto.RegulatorySpeedLimit.SpeedLimitType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      speedLimitType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *遵循的监管速度类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RegulatorySpeedLimit.SpeedLimitType speedLimitType = 1;</code>
     */
    public Builder clearSpeedLimitType() {
      
      speedLimitType_ = 0;
      onChanged();
      return this;
    }

    private int speed_ ;
    /**
     * <pre>
     *分辨率为0.02 m/s。数值8191表示无效数值。
     * </pre>
     *
     * <code>int32 speed = 2;</code>
     */
    public int getSpeed() {
      return speed_;
    }
    /**
     * <pre>
     *分辨率为0.02 m/s。数值8191表示无效数值。
     * </pre>
     *
     * <code>int32 speed = 2;</code>
     */
    public Builder setSpeed(int value) {
      
      speed_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *分辨率为0.02 m/s。数值8191表示无效数值。
     * </pre>
     *
     * <code>int32 speed = 2;</code>
     */
    public Builder clearSpeed() {
      
      speed_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.RegulatorySpeedLimit)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.RegulatorySpeedLimit)
  private static final road.data.proto.RegulatorySpeedLimit DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.RegulatorySpeedLimit();
  }

  public static road.data.proto.RegulatorySpeedLimit getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RegulatorySpeedLimit>
      PARSER = new com.google.protobuf.AbstractParser<RegulatorySpeedLimit>() {
    @java.lang.Override
    public RegulatorySpeedLimit parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RegulatorySpeedLimit(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RegulatorySpeedLimit> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RegulatorySpeedLimit> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.RegulatorySpeedLimit getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

