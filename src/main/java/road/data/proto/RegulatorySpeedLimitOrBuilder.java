// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface RegulatorySpeedLimitOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.RegulatorySpeedLimit)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *遵循的监管速度类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RegulatorySpeedLimit.SpeedLimitType speedLimitType = 1;</code>
   */
  int getSpeedLimitTypeValue();
  /**
   * <pre>
   *遵循的监管速度类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RegulatorySpeedLimit.SpeedLimitType speedLimitType = 1;</code>
   */
  road.data.proto.RegulatorySpeedLimit.SpeedLimitType getSpeedLimitType();

  /**
   * <pre>
   *分辨率为0.02 m/s。数值8191表示无效数值。
   * </pre>
   *
   * <code>int32 speed = 2;</code>
   */
  int getSpeed();
}
