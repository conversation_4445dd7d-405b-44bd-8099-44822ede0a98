// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ReqClearTheWayOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ReqClearTheWay)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *目标路段上游节点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
   */
  boolean hasUpStreamNode();
  /**
   * <pre>
   *目标路段上游节点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
   */
  road.data.proto.NodeReferenceId getUpStreamNode();
  /**
   * <pre>
   *目标路段上游节点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getUpStreamNodeOrBuilder();

  /**
   * <pre>
   *目标路段下游节点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
   */
  boolean hasDownStreamNode();
  /**
   * <pre>
   *目标路段下游节点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
   */
  road.data.proto.NodeReferenceId getDownStreamNode();
  /**
   * <pre>
   *目标路段下游节点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getDownStreamNodeOrBuilder();

  /**
   * <pre>
   *目标路段id
   * </pre>
   *
   * <code>uint32 targetLane = 3;</code>
   */
  int getTargetLane();
}
