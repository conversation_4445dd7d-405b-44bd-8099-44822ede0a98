// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车辆请求信息  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ReqInfo}
 */
public  final class ReqInfo extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ReqInfo)
    ReqInfoOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ReqInfo.newBuilder() to construct.
  private ReqInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ReqInfo() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ReqInfo();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ReqInfo(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.ReqLaneChange.Builder subBuilder = null;
            if (reqInfoOneOfCase_ == 1) {
              subBuilder = ((road.data.proto.ReqLaneChange) reqInfoOneOf_).toBuilder();
            }
            reqInfoOneOf_ =
                input.readMessage(road.data.proto.ReqLaneChange.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.ReqLaneChange) reqInfoOneOf_);
              reqInfoOneOf_ = subBuilder.buildPartial();
            }
            reqInfoOneOfCase_ = 1;
            break;
          }
          case 18: {
            road.data.proto.ReqClearTheWay.Builder subBuilder = null;
            if (reqInfoOneOfCase_ == 2) {
              subBuilder = ((road.data.proto.ReqClearTheWay) reqInfoOneOf_).toBuilder();
            }
            reqInfoOneOf_ =
                input.readMessage(road.data.proto.ReqClearTheWay.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.ReqClearTheWay) reqInfoOneOf_);
              reqInfoOneOf_ = subBuilder.buildPartial();
            }
            reqInfoOneOfCase_ = 2;
            break;
          }
          case 26: {
            road.data.proto.ReqSignalPriority.Builder subBuilder = null;
            if (reqInfoOneOfCase_ == 3) {
              subBuilder = ((road.data.proto.ReqSignalPriority) reqInfoOneOf_).toBuilder();
            }
            reqInfoOneOf_ =
                input.readMessage(road.data.proto.ReqSignalPriority.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.ReqSignalPriority) reqInfoOneOf_);
              reqInfoOneOf_ = subBuilder.buildPartial();
            }
            reqInfoOneOfCase_ = 3;
            break;
          }
          case 34: {
            road.data.proto.ReqSensorSharing.Builder subBuilder = null;
            if (reqInfoOneOfCase_ == 4) {
              subBuilder = ((road.data.proto.ReqSensorSharing) reqInfoOneOf_).toBuilder();
            }
            reqInfoOneOf_ =
                input.readMessage(road.data.proto.ReqSensorSharing.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.ReqSensorSharing) reqInfoOneOf_);
              reqInfoOneOf_ = subBuilder.buildPartial();
            }
            reqInfoOneOfCase_ = 4;
            break;
          }
          case 42: {
            road.data.proto.ReqParkingArea.Builder subBuilder = null;
            if (reqInfoOneOfCase_ == 5) {
              subBuilder = ((road.data.proto.ReqParkingArea) reqInfoOneOf_).toBuilder();
            }
            reqInfoOneOf_ =
                input.readMessage(road.data.proto.ReqParkingArea.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.ReqParkingArea) reqInfoOneOf_);
              reqInfoOneOf_ = subBuilder.buildPartial();
            }
            reqInfoOneOfCase_ = 5;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ReqInfo.class, road.data.proto.ReqInfo.Builder.class);
  }

  private int reqInfoOneOfCase_ = 0;
  private java.lang.Object reqInfoOneOf_;
  public enum ReqInfoOneOfCase
      implements com.google.protobuf.Internal.EnumLite {
    LANECHANGE(1),
    CLEARTHEWAY(2),
    SIGNALPRIORITY(3),
    SENSORSHARING(4),
    PARKING(5),
    REQINFOONEOF_NOT_SET(0);
    private final int value;
    private ReqInfoOneOfCase(int value) {
      this.value = value;
    }
    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ReqInfoOneOfCase valueOf(int value) {
      return forNumber(value);
    }

    public static ReqInfoOneOfCase forNumber(int value) {
      switch (value) {
        case 1: return LANECHANGE;
        case 2: return CLEARTHEWAY;
        case 3: return SIGNALPRIORITY;
        case 4: return SENSORSHARING;
        case 5: return PARKING;
        case 0: return REQINFOONEOF_NOT_SET;
        default: return null;
      }
    }
    public int getNumber() {
      return this.value;
    }
  };

  public ReqInfoOneOfCase
  getReqInfoOneOfCase() {
    return ReqInfoOneOfCase.forNumber(
        reqInfoOneOfCase_);
  }

  public static final int LANECHANGE_FIELD_NUMBER = 1;
  /**
   * <pre>
   *可选，车道变更请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
   */
  public boolean hasLaneChange() {
    return reqInfoOneOfCase_ == 1;
  }
  /**
   * <pre>
   *可选，车道变更请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
   */
  public road.data.proto.ReqLaneChange getLaneChange() {
    if (reqInfoOneOfCase_ == 1) {
       return (road.data.proto.ReqLaneChange) reqInfoOneOf_;
    }
    return road.data.proto.ReqLaneChange.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，车道变更请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
   */
  public road.data.proto.ReqLaneChangeOrBuilder getLaneChangeOrBuilder() {
    if (reqInfoOneOfCase_ == 1) {
       return (road.data.proto.ReqLaneChange) reqInfoOneOf_;
    }
    return road.data.proto.ReqLaneChange.getDefaultInstance();
  }

  public static final int CLEARTHEWAY_FIELD_NUMBER = 2;
  /**
   * <pre>
   *可选，道路清空请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
   */
  public boolean hasClearTheWay() {
    return reqInfoOneOfCase_ == 2;
  }
  /**
   * <pre>
   *可选，道路清空请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
   */
  public road.data.proto.ReqClearTheWay getClearTheWay() {
    if (reqInfoOneOfCase_ == 2) {
       return (road.data.proto.ReqClearTheWay) reqInfoOneOf_;
    }
    return road.data.proto.ReqClearTheWay.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，道路清空请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
   */
  public road.data.proto.ReqClearTheWayOrBuilder getClearTheWayOrBuilder() {
    if (reqInfoOneOfCase_ == 2) {
       return (road.data.proto.ReqClearTheWay) reqInfoOneOf_;
    }
    return road.data.proto.ReqClearTheWay.getDefaultInstance();
  }

  public static final int SIGNALPRIORITY_FIELD_NUMBER = 3;
  /**
   * <pre>
   *可选，信号优先请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
   */
  public boolean hasSignalPriority() {
    return reqInfoOneOfCase_ == 3;
  }
  /**
   * <pre>
   *可选，信号优先请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
   */
  public road.data.proto.ReqSignalPriority getSignalPriority() {
    if (reqInfoOneOfCase_ == 3) {
       return (road.data.proto.ReqSignalPriority) reqInfoOneOf_;
    }
    return road.data.proto.ReqSignalPriority.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，信号优先请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
   */
  public road.data.proto.ReqSignalPriorityOrBuilder getSignalPriorityOrBuilder() {
    if (reqInfoOneOfCase_ == 3) {
       return (road.data.proto.ReqSignalPriority) reqInfoOneOf_;
    }
    return road.data.proto.ReqSignalPriority.getDefaultInstance();
  }

  public static final int SENSORSHARING_FIELD_NUMBER = 4;
  /**
   * <pre>
   *可选，感知信息共享请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
   */
  public boolean hasSensorSharing() {
    return reqInfoOneOfCase_ == 4;
  }
  /**
   * <pre>
   *可选，感知信息共享请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
   */
  public road.data.proto.ReqSensorSharing getSensorSharing() {
    if (reqInfoOneOfCase_ == 4) {
       return (road.data.proto.ReqSensorSharing) reqInfoOneOf_;
    }
    return road.data.proto.ReqSensorSharing.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，感知信息共享请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
   */
  public road.data.proto.ReqSensorSharingOrBuilder getSensorSharingOrBuilder() {
    if (reqInfoOneOfCase_ == 4) {
       return (road.data.proto.ReqSensorSharing) reqInfoOneOf_;
    }
    return road.data.proto.ReqSensorSharing.getDefaultInstance();
  }

  public static final int PARKING_FIELD_NUMBER = 5;
  /**
   * <pre>
   *可选，场站入场请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
   */
  public boolean hasParking() {
    return reqInfoOneOfCase_ == 5;
  }
  /**
   * <pre>
   *可选，场站入场请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
   */
  public road.data.proto.ReqParkingArea getParking() {
    if (reqInfoOneOfCase_ == 5) {
       return (road.data.proto.ReqParkingArea) reqInfoOneOf_;
    }
    return road.data.proto.ReqParkingArea.getDefaultInstance();
  }
  /**
   * <pre>
   *可选，场站入场请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
   */
  public road.data.proto.ReqParkingAreaOrBuilder getParkingOrBuilder() {
    if (reqInfoOneOfCase_ == 5) {
       return (road.data.proto.ReqParkingArea) reqInfoOneOf_;
    }
    return road.data.proto.ReqParkingArea.getDefaultInstance();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (reqInfoOneOfCase_ == 1) {
      output.writeMessage(1, (road.data.proto.ReqLaneChange) reqInfoOneOf_);
    }
    if (reqInfoOneOfCase_ == 2) {
      output.writeMessage(2, (road.data.proto.ReqClearTheWay) reqInfoOneOf_);
    }
    if (reqInfoOneOfCase_ == 3) {
      output.writeMessage(3, (road.data.proto.ReqSignalPriority) reqInfoOneOf_);
    }
    if (reqInfoOneOfCase_ == 4) {
      output.writeMessage(4, (road.data.proto.ReqSensorSharing) reqInfoOneOf_);
    }
    if (reqInfoOneOfCase_ == 5) {
      output.writeMessage(5, (road.data.proto.ReqParkingArea) reqInfoOneOf_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (reqInfoOneOfCase_ == 1) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, (road.data.proto.ReqLaneChange) reqInfoOneOf_);
    }
    if (reqInfoOneOfCase_ == 2) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, (road.data.proto.ReqClearTheWay) reqInfoOneOf_);
    }
    if (reqInfoOneOfCase_ == 3) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, (road.data.proto.ReqSignalPriority) reqInfoOneOf_);
    }
    if (reqInfoOneOfCase_ == 4) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, (road.data.proto.ReqSensorSharing) reqInfoOneOf_);
    }
    if (reqInfoOneOfCase_ == 5) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, (road.data.proto.ReqParkingArea) reqInfoOneOf_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ReqInfo)) {
      return super.equals(obj);
    }
    road.data.proto.ReqInfo other = (road.data.proto.ReqInfo) obj;

    if (!getReqInfoOneOfCase().equals(other.getReqInfoOneOfCase())) return false;
    switch (reqInfoOneOfCase_) {
      case 1:
        if (!getLaneChange()
            .equals(other.getLaneChange())) return false;
        break;
      case 2:
        if (!getClearTheWay()
            .equals(other.getClearTheWay())) return false;
        break;
      case 3:
        if (!getSignalPriority()
            .equals(other.getSignalPriority())) return false;
        break;
      case 4:
        if (!getSensorSharing()
            .equals(other.getSensorSharing())) return false;
        break;
      case 5:
        if (!getParking()
            .equals(other.getParking())) return false;
        break;
      case 0:
      default:
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    switch (reqInfoOneOfCase_) {
      case 1:
        hash = (37 * hash) + LANECHANGE_FIELD_NUMBER;
        hash = (53 * hash) + getLaneChange().hashCode();
        break;
      case 2:
        hash = (37 * hash) + CLEARTHEWAY_FIELD_NUMBER;
        hash = (53 * hash) + getClearTheWay().hashCode();
        break;
      case 3:
        hash = (37 * hash) + SIGNALPRIORITY_FIELD_NUMBER;
        hash = (53 * hash) + getSignalPriority().hashCode();
        break;
      case 4:
        hash = (37 * hash) + SENSORSHARING_FIELD_NUMBER;
        hash = (53 * hash) + getSensorSharing().hashCode();
        break;
      case 5:
        hash = (37 * hash) + PARKING_FIELD_NUMBER;
        hash = (53 * hash) + getParking().hashCode();
        break;
      case 0:
      default:
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ReqInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReqInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReqInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ReqInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *车辆请求信息  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ReqInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ReqInfo)
      road.data.proto.ReqInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ReqInfo.class, road.data.proto.ReqInfo.Builder.class);
    }

    // Construct using road.data.proto.ReqInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      reqInfoOneOfCase_ = 0;
      reqInfoOneOf_ = null;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqInfo_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ReqInfo getDefaultInstanceForType() {
      return road.data.proto.ReqInfo.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ReqInfo build() {
      road.data.proto.ReqInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ReqInfo buildPartial() {
      road.data.proto.ReqInfo result = new road.data.proto.ReqInfo(this);
      if (reqInfoOneOfCase_ == 1) {
        if (laneChangeBuilder_ == null) {
          result.reqInfoOneOf_ = reqInfoOneOf_;
        } else {
          result.reqInfoOneOf_ = laneChangeBuilder_.build();
        }
      }
      if (reqInfoOneOfCase_ == 2) {
        if (clearTheWayBuilder_ == null) {
          result.reqInfoOneOf_ = reqInfoOneOf_;
        } else {
          result.reqInfoOneOf_ = clearTheWayBuilder_.build();
        }
      }
      if (reqInfoOneOfCase_ == 3) {
        if (signalPriorityBuilder_ == null) {
          result.reqInfoOneOf_ = reqInfoOneOf_;
        } else {
          result.reqInfoOneOf_ = signalPriorityBuilder_.build();
        }
      }
      if (reqInfoOneOfCase_ == 4) {
        if (sensorSharingBuilder_ == null) {
          result.reqInfoOneOf_ = reqInfoOneOf_;
        } else {
          result.reqInfoOneOf_ = sensorSharingBuilder_.build();
        }
      }
      if (reqInfoOneOfCase_ == 5) {
        if (parkingBuilder_ == null) {
          result.reqInfoOneOf_ = reqInfoOneOf_;
        } else {
          result.reqInfoOneOf_ = parkingBuilder_.build();
        }
      }
      result.reqInfoOneOfCase_ = reqInfoOneOfCase_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ReqInfo) {
        return mergeFrom((road.data.proto.ReqInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ReqInfo other) {
      if (other == road.data.proto.ReqInfo.getDefaultInstance()) return this;
      switch (other.getReqInfoOneOfCase()) {
        case LANECHANGE: {
          mergeLaneChange(other.getLaneChange());
          break;
        }
        case CLEARTHEWAY: {
          mergeClearTheWay(other.getClearTheWay());
          break;
        }
        case SIGNALPRIORITY: {
          mergeSignalPriority(other.getSignalPriority());
          break;
        }
        case SENSORSHARING: {
          mergeSensorSharing(other.getSensorSharing());
          break;
        }
        case PARKING: {
          mergeParking(other.getParking());
          break;
        }
        case REQINFOONEOF_NOT_SET: {
          break;
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ReqInfo parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ReqInfo) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int reqInfoOneOfCase_ = 0;
    private java.lang.Object reqInfoOneOf_;
    public ReqInfoOneOfCase
        getReqInfoOneOfCase() {
      return ReqInfoOneOfCase.forNumber(
          reqInfoOneOfCase_);
    }

    public Builder clearReqInfoOneOf() {
      reqInfoOneOfCase_ = 0;
      reqInfoOneOf_ = null;
      onChanged();
      return this;
    }


    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReqLaneChange, road.data.proto.ReqLaneChange.Builder, road.data.proto.ReqLaneChangeOrBuilder> laneChangeBuilder_;
    /**
     * <pre>
     *可选，车道变更请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
     */
    public boolean hasLaneChange() {
      return reqInfoOneOfCase_ == 1;
    }
    /**
     * <pre>
     *可选，车道变更请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
     */
    public road.data.proto.ReqLaneChange getLaneChange() {
      if (laneChangeBuilder_ == null) {
        if (reqInfoOneOfCase_ == 1) {
          return (road.data.proto.ReqLaneChange) reqInfoOneOf_;
        }
        return road.data.proto.ReqLaneChange.getDefaultInstance();
      } else {
        if (reqInfoOneOfCase_ == 1) {
          return laneChangeBuilder_.getMessage();
        }
        return road.data.proto.ReqLaneChange.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，车道变更请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
     */
    public Builder setLaneChange(road.data.proto.ReqLaneChange value) {
      if (laneChangeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        reqInfoOneOf_ = value;
        onChanged();
      } else {
        laneChangeBuilder_.setMessage(value);
      }
      reqInfoOneOfCase_ = 1;
      return this;
    }
    /**
     * <pre>
     *可选，车道变更请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
     */
    public Builder setLaneChange(
        road.data.proto.ReqLaneChange.Builder builderForValue) {
      if (laneChangeBuilder_ == null) {
        reqInfoOneOf_ = builderForValue.build();
        onChanged();
      } else {
        laneChangeBuilder_.setMessage(builderForValue.build());
      }
      reqInfoOneOfCase_ = 1;
      return this;
    }
    /**
     * <pre>
     *可选，车道变更请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
     */
    public Builder mergeLaneChange(road.data.proto.ReqLaneChange value) {
      if (laneChangeBuilder_ == null) {
        if (reqInfoOneOfCase_ == 1 &&
            reqInfoOneOf_ != road.data.proto.ReqLaneChange.getDefaultInstance()) {
          reqInfoOneOf_ = road.data.proto.ReqLaneChange.newBuilder((road.data.proto.ReqLaneChange) reqInfoOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          reqInfoOneOf_ = value;
        }
        onChanged();
      } else {
        if (reqInfoOneOfCase_ == 1) {
          laneChangeBuilder_.mergeFrom(value);
        }
        laneChangeBuilder_.setMessage(value);
      }
      reqInfoOneOfCase_ = 1;
      return this;
    }
    /**
     * <pre>
     *可选，车道变更请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
     */
    public Builder clearLaneChange() {
      if (laneChangeBuilder_ == null) {
        if (reqInfoOneOfCase_ == 1) {
          reqInfoOneOfCase_ = 0;
          reqInfoOneOf_ = null;
          onChanged();
        }
      } else {
        if (reqInfoOneOfCase_ == 1) {
          reqInfoOneOfCase_ = 0;
          reqInfoOneOf_ = null;
        }
        laneChangeBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，车道变更请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
     */
    public road.data.proto.ReqLaneChange.Builder getLaneChangeBuilder() {
      return getLaneChangeFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，车道变更请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
     */
    public road.data.proto.ReqLaneChangeOrBuilder getLaneChangeOrBuilder() {
      if ((reqInfoOneOfCase_ == 1) && (laneChangeBuilder_ != null)) {
        return laneChangeBuilder_.getMessageOrBuilder();
      } else {
        if (reqInfoOneOfCase_ == 1) {
          return (road.data.proto.ReqLaneChange) reqInfoOneOf_;
        }
        return road.data.proto.ReqLaneChange.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，车道变更请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReqLaneChange, road.data.proto.ReqLaneChange.Builder, road.data.proto.ReqLaneChangeOrBuilder> 
        getLaneChangeFieldBuilder() {
      if (laneChangeBuilder_ == null) {
        if (!(reqInfoOneOfCase_ == 1)) {
          reqInfoOneOf_ = road.data.proto.ReqLaneChange.getDefaultInstance();
        }
        laneChangeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ReqLaneChange, road.data.proto.ReqLaneChange.Builder, road.data.proto.ReqLaneChangeOrBuilder>(
                (road.data.proto.ReqLaneChange) reqInfoOneOf_,
                getParentForChildren(),
                isClean());
        reqInfoOneOf_ = null;
      }
      reqInfoOneOfCase_ = 1;
      onChanged();;
      return laneChangeBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReqClearTheWay, road.data.proto.ReqClearTheWay.Builder, road.data.proto.ReqClearTheWayOrBuilder> clearTheWayBuilder_;
    /**
     * <pre>
     *可选，道路清空请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
     */
    public boolean hasClearTheWay() {
      return reqInfoOneOfCase_ == 2;
    }
    /**
     * <pre>
     *可选，道路清空请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
     */
    public road.data.proto.ReqClearTheWay getClearTheWay() {
      if (clearTheWayBuilder_ == null) {
        if (reqInfoOneOfCase_ == 2) {
          return (road.data.proto.ReqClearTheWay) reqInfoOneOf_;
        }
        return road.data.proto.ReqClearTheWay.getDefaultInstance();
      } else {
        if (reqInfoOneOfCase_ == 2) {
          return clearTheWayBuilder_.getMessage();
        }
        return road.data.proto.ReqClearTheWay.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，道路清空请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
     */
    public Builder setClearTheWay(road.data.proto.ReqClearTheWay value) {
      if (clearTheWayBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        reqInfoOneOf_ = value;
        onChanged();
      } else {
        clearTheWayBuilder_.setMessage(value);
      }
      reqInfoOneOfCase_ = 2;
      return this;
    }
    /**
     * <pre>
     *可选，道路清空请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
     */
    public Builder setClearTheWay(
        road.data.proto.ReqClearTheWay.Builder builderForValue) {
      if (clearTheWayBuilder_ == null) {
        reqInfoOneOf_ = builderForValue.build();
        onChanged();
      } else {
        clearTheWayBuilder_.setMessage(builderForValue.build());
      }
      reqInfoOneOfCase_ = 2;
      return this;
    }
    /**
     * <pre>
     *可选，道路清空请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
     */
    public Builder mergeClearTheWay(road.data.proto.ReqClearTheWay value) {
      if (clearTheWayBuilder_ == null) {
        if (reqInfoOneOfCase_ == 2 &&
            reqInfoOneOf_ != road.data.proto.ReqClearTheWay.getDefaultInstance()) {
          reqInfoOneOf_ = road.data.proto.ReqClearTheWay.newBuilder((road.data.proto.ReqClearTheWay) reqInfoOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          reqInfoOneOf_ = value;
        }
        onChanged();
      } else {
        if (reqInfoOneOfCase_ == 2) {
          clearTheWayBuilder_.mergeFrom(value);
        }
        clearTheWayBuilder_.setMessage(value);
      }
      reqInfoOneOfCase_ = 2;
      return this;
    }
    /**
     * <pre>
     *可选，道路清空请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
     */
    public Builder clearClearTheWay() {
      if (clearTheWayBuilder_ == null) {
        if (reqInfoOneOfCase_ == 2) {
          reqInfoOneOfCase_ = 0;
          reqInfoOneOf_ = null;
          onChanged();
        }
      } else {
        if (reqInfoOneOfCase_ == 2) {
          reqInfoOneOfCase_ = 0;
          reqInfoOneOf_ = null;
        }
        clearTheWayBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，道路清空请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
     */
    public road.data.proto.ReqClearTheWay.Builder getClearTheWayBuilder() {
      return getClearTheWayFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，道路清空请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
     */
    public road.data.proto.ReqClearTheWayOrBuilder getClearTheWayOrBuilder() {
      if ((reqInfoOneOfCase_ == 2) && (clearTheWayBuilder_ != null)) {
        return clearTheWayBuilder_.getMessageOrBuilder();
      } else {
        if (reqInfoOneOfCase_ == 2) {
          return (road.data.proto.ReqClearTheWay) reqInfoOneOf_;
        }
        return road.data.proto.ReqClearTheWay.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，道路清空请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReqClearTheWay, road.data.proto.ReqClearTheWay.Builder, road.data.proto.ReqClearTheWayOrBuilder> 
        getClearTheWayFieldBuilder() {
      if (clearTheWayBuilder_ == null) {
        if (!(reqInfoOneOfCase_ == 2)) {
          reqInfoOneOf_ = road.data.proto.ReqClearTheWay.getDefaultInstance();
        }
        clearTheWayBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ReqClearTheWay, road.data.proto.ReqClearTheWay.Builder, road.data.proto.ReqClearTheWayOrBuilder>(
                (road.data.proto.ReqClearTheWay) reqInfoOneOf_,
                getParentForChildren(),
                isClean());
        reqInfoOneOf_ = null;
      }
      reqInfoOneOfCase_ = 2;
      onChanged();;
      return clearTheWayBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReqSignalPriority, road.data.proto.ReqSignalPriority.Builder, road.data.proto.ReqSignalPriorityOrBuilder> signalPriorityBuilder_;
    /**
     * <pre>
     *可选，信号优先请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
     */
    public boolean hasSignalPriority() {
      return reqInfoOneOfCase_ == 3;
    }
    /**
     * <pre>
     *可选，信号优先请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
     */
    public road.data.proto.ReqSignalPriority getSignalPriority() {
      if (signalPriorityBuilder_ == null) {
        if (reqInfoOneOfCase_ == 3) {
          return (road.data.proto.ReqSignalPriority) reqInfoOneOf_;
        }
        return road.data.proto.ReqSignalPriority.getDefaultInstance();
      } else {
        if (reqInfoOneOfCase_ == 3) {
          return signalPriorityBuilder_.getMessage();
        }
        return road.data.proto.ReqSignalPriority.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，信号优先请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
     */
    public Builder setSignalPriority(road.data.proto.ReqSignalPriority value) {
      if (signalPriorityBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        reqInfoOneOf_ = value;
        onChanged();
      } else {
        signalPriorityBuilder_.setMessage(value);
      }
      reqInfoOneOfCase_ = 3;
      return this;
    }
    /**
     * <pre>
     *可选，信号优先请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
     */
    public Builder setSignalPriority(
        road.data.proto.ReqSignalPriority.Builder builderForValue) {
      if (signalPriorityBuilder_ == null) {
        reqInfoOneOf_ = builderForValue.build();
        onChanged();
      } else {
        signalPriorityBuilder_.setMessage(builderForValue.build());
      }
      reqInfoOneOfCase_ = 3;
      return this;
    }
    /**
     * <pre>
     *可选，信号优先请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
     */
    public Builder mergeSignalPriority(road.data.proto.ReqSignalPriority value) {
      if (signalPriorityBuilder_ == null) {
        if (reqInfoOneOfCase_ == 3 &&
            reqInfoOneOf_ != road.data.proto.ReqSignalPriority.getDefaultInstance()) {
          reqInfoOneOf_ = road.data.proto.ReqSignalPriority.newBuilder((road.data.proto.ReqSignalPriority) reqInfoOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          reqInfoOneOf_ = value;
        }
        onChanged();
      } else {
        if (reqInfoOneOfCase_ == 3) {
          signalPriorityBuilder_.mergeFrom(value);
        }
        signalPriorityBuilder_.setMessage(value);
      }
      reqInfoOneOfCase_ = 3;
      return this;
    }
    /**
     * <pre>
     *可选，信号优先请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
     */
    public Builder clearSignalPriority() {
      if (signalPriorityBuilder_ == null) {
        if (reqInfoOneOfCase_ == 3) {
          reqInfoOneOfCase_ = 0;
          reqInfoOneOf_ = null;
          onChanged();
        }
      } else {
        if (reqInfoOneOfCase_ == 3) {
          reqInfoOneOfCase_ = 0;
          reqInfoOneOf_ = null;
        }
        signalPriorityBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，信号优先请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
     */
    public road.data.proto.ReqSignalPriority.Builder getSignalPriorityBuilder() {
      return getSignalPriorityFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，信号优先请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
     */
    public road.data.proto.ReqSignalPriorityOrBuilder getSignalPriorityOrBuilder() {
      if ((reqInfoOneOfCase_ == 3) && (signalPriorityBuilder_ != null)) {
        return signalPriorityBuilder_.getMessageOrBuilder();
      } else {
        if (reqInfoOneOfCase_ == 3) {
          return (road.data.proto.ReqSignalPriority) reqInfoOneOf_;
        }
        return road.data.proto.ReqSignalPriority.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，信号优先请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReqSignalPriority, road.data.proto.ReqSignalPriority.Builder, road.data.proto.ReqSignalPriorityOrBuilder> 
        getSignalPriorityFieldBuilder() {
      if (signalPriorityBuilder_ == null) {
        if (!(reqInfoOneOfCase_ == 3)) {
          reqInfoOneOf_ = road.data.proto.ReqSignalPriority.getDefaultInstance();
        }
        signalPriorityBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ReqSignalPriority, road.data.proto.ReqSignalPriority.Builder, road.data.proto.ReqSignalPriorityOrBuilder>(
                (road.data.proto.ReqSignalPriority) reqInfoOneOf_,
                getParentForChildren(),
                isClean());
        reqInfoOneOf_ = null;
      }
      reqInfoOneOfCase_ = 3;
      onChanged();;
      return signalPriorityBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReqSensorSharing, road.data.proto.ReqSensorSharing.Builder, road.data.proto.ReqSensorSharingOrBuilder> sensorSharingBuilder_;
    /**
     * <pre>
     *可选，感知信息共享请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
     */
    public boolean hasSensorSharing() {
      return reqInfoOneOfCase_ == 4;
    }
    /**
     * <pre>
     *可选，感知信息共享请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
     */
    public road.data.proto.ReqSensorSharing getSensorSharing() {
      if (sensorSharingBuilder_ == null) {
        if (reqInfoOneOfCase_ == 4) {
          return (road.data.proto.ReqSensorSharing) reqInfoOneOf_;
        }
        return road.data.proto.ReqSensorSharing.getDefaultInstance();
      } else {
        if (reqInfoOneOfCase_ == 4) {
          return sensorSharingBuilder_.getMessage();
        }
        return road.data.proto.ReqSensorSharing.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，感知信息共享请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
     */
    public Builder setSensorSharing(road.data.proto.ReqSensorSharing value) {
      if (sensorSharingBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        reqInfoOneOf_ = value;
        onChanged();
      } else {
        sensorSharingBuilder_.setMessage(value);
      }
      reqInfoOneOfCase_ = 4;
      return this;
    }
    /**
     * <pre>
     *可选，感知信息共享请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
     */
    public Builder setSensorSharing(
        road.data.proto.ReqSensorSharing.Builder builderForValue) {
      if (sensorSharingBuilder_ == null) {
        reqInfoOneOf_ = builderForValue.build();
        onChanged();
      } else {
        sensorSharingBuilder_.setMessage(builderForValue.build());
      }
      reqInfoOneOfCase_ = 4;
      return this;
    }
    /**
     * <pre>
     *可选，感知信息共享请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
     */
    public Builder mergeSensorSharing(road.data.proto.ReqSensorSharing value) {
      if (sensorSharingBuilder_ == null) {
        if (reqInfoOneOfCase_ == 4 &&
            reqInfoOneOf_ != road.data.proto.ReqSensorSharing.getDefaultInstance()) {
          reqInfoOneOf_ = road.data.proto.ReqSensorSharing.newBuilder((road.data.proto.ReqSensorSharing) reqInfoOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          reqInfoOneOf_ = value;
        }
        onChanged();
      } else {
        if (reqInfoOneOfCase_ == 4) {
          sensorSharingBuilder_.mergeFrom(value);
        }
        sensorSharingBuilder_.setMessage(value);
      }
      reqInfoOneOfCase_ = 4;
      return this;
    }
    /**
     * <pre>
     *可选，感知信息共享请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
     */
    public Builder clearSensorSharing() {
      if (sensorSharingBuilder_ == null) {
        if (reqInfoOneOfCase_ == 4) {
          reqInfoOneOfCase_ = 0;
          reqInfoOneOf_ = null;
          onChanged();
        }
      } else {
        if (reqInfoOneOfCase_ == 4) {
          reqInfoOneOfCase_ = 0;
          reqInfoOneOf_ = null;
        }
        sensorSharingBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，感知信息共享请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
     */
    public road.data.proto.ReqSensorSharing.Builder getSensorSharingBuilder() {
      return getSensorSharingFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，感知信息共享请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
     */
    public road.data.proto.ReqSensorSharingOrBuilder getSensorSharingOrBuilder() {
      if ((reqInfoOneOfCase_ == 4) && (sensorSharingBuilder_ != null)) {
        return sensorSharingBuilder_.getMessageOrBuilder();
      } else {
        if (reqInfoOneOfCase_ == 4) {
          return (road.data.proto.ReqSensorSharing) reqInfoOneOf_;
        }
        return road.data.proto.ReqSensorSharing.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，感知信息共享请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReqSensorSharing, road.data.proto.ReqSensorSharing.Builder, road.data.proto.ReqSensorSharingOrBuilder> 
        getSensorSharingFieldBuilder() {
      if (sensorSharingBuilder_ == null) {
        if (!(reqInfoOneOfCase_ == 4)) {
          reqInfoOneOf_ = road.data.proto.ReqSensorSharing.getDefaultInstance();
        }
        sensorSharingBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ReqSensorSharing, road.data.proto.ReqSensorSharing.Builder, road.data.proto.ReqSensorSharingOrBuilder>(
                (road.data.proto.ReqSensorSharing) reqInfoOneOf_,
                getParentForChildren(),
                isClean());
        reqInfoOneOf_ = null;
      }
      reqInfoOneOfCase_ = 4;
      onChanged();;
      return sensorSharingBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReqParkingArea, road.data.proto.ReqParkingArea.Builder, road.data.proto.ReqParkingAreaOrBuilder> parkingBuilder_;
    /**
     * <pre>
     *可选，场站入场请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
     */
    public boolean hasParking() {
      return reqInfoOneOfCase_ == 5;
    }
    /**
     * <pre>
     *可选，场站入场请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
     */
    public road.data.proto.ReqParkingArea getParking() {
      if (parkingBuilder_ == null) {
        if (reqInfoOneOfCase_ == 5) {
          return (road.data.proto.ReqParkingArea) reqInfoOneOf_;
        }
        return road.data.proto.ReqParkingArea.getDefaultInstance();
      } else {
        if (reqInfoOneOfCase_ == 5) {
          return parkingBuilder_.getMessage();
        }
        return road.data.proto.ReqParkingArea.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，场站入场请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
     */
    public Builder setParking(road.data.proto.ReqParkingArea value) {
      if (parkingBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        reqInfoOneOf_ = value;
        onChanged();
      } else {
        parkingBuilder_.setMessage(value);
      }
      reqInfoOneOfCase_ = 5;
      return this;
    }
    /**
     * <pre>
     *可选，场站入场请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
     */
    public Builder setParking(
        road.data.proto.ReqParkingArea.Builder builderForValue) {
      if (parkingBuilder_ == null) {
        reqInfoOneOf_ = builderForValue.build();
        onChanged();
      } else {
        parkingBuilder_.setMessage(builderForValue.build());
      }
      reqInfoOneOfCase_ = 5;
      return this;
    }
    /**
     * <pre>
     *可选，场站入场请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
     */
    public Builder mergeParking(road.data.proto.ReqParkingArea value) {
      if (parkingBuilder_ == null) {
        if (reqInfoOneOfCase_ == 5 &&
            reqInfoOneOf_ != road.data.proto.ReqParkingArea.getDefaultInstance()) {
          reqInfoOneOf_ = road.data.proto.ReqParkingArea.newBuilder((road.data.proto.ReqParkingArea) reqInfoOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          reqInfoOneOf_ = value;
        }
        onChanged();
      } else {
        if (reqInfoOneOfCase_ == 5) {
          parkingBuilder_.mergeFrom(value);
        }
        parkingBuilder_.setMessage(value);
      }
      reqInfoOneOfCase_ = 5;
      return this;
    }
    /**
     * <pre>
     *可选，场站入场请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
     */
    public Builder clearParking() {
      if (parkingBuilder_ == null) {
        if (reqInfoOneOfCase_ == 5) {
          reqInfoOneOfCase_ = 0;
          reqInfoOneOf_ = null;
          onChanged();
        }
      } else {
        if (reqInfoOneOfCase_ == 5) {
          reqInfoOneOfCase_ = 0;
          reqInfoOneOf_ = null;
        }
        parkingBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，场站入场请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
     */
    public road.data.proto.ReqParkingArea.Builder getParkingBuilder() {
      return getParkingFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，场站入场请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
     */
    public road.data.proto.ReqParkingAreaOrBuilder getParkingOrBuilder() {
      if ((reqInfoOneOfCase_ == 5) && (parkingBuilder_ != null)) {
        return parkingBuilder_.getMessageOrBuilder();
      } else {
        if (reqInfoOneOfCase_ == 5) {
          return (road.data.proto.ReqParkingArea) reqInfoOneOf_;
        }
        return road.data.proto.ReqParkingArea.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *可选，场站入场请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ReqParkingArea, road.data.proto.ReqParkingArea.Builder, road.data.proto.ReqParkingAreaOrBuilder> 
        getParkingFieldBuilder() {
      if (parkingBuilder_ == null) {
        if (!(reqInfoOneOfCase_ == 5)) {
          reqInfoOneOf_ = road.data.proto.ReqParkingArea.getDefaultInstance();
        }
        parkingBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ReqParkingArea, road.data.proto.ReqParkingArea.Builder, road.data.proto.ReqParkingAreaOrBuilder>(
                (road.data.proto.ReqParkingArea) reqInfoOneOf_,
                getParentForChildren(),
                isClean());
        reqInfoOneOf_ = null;
      }
      reqInfoOneOfCase_ = 5;
      onChanged();;
      return parkingBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ReqInfo)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ReqInfo)
  private static final road.data.proto.ReqInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ReqInfo();
  }

  public static road.data.proto.ReqInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ReqInfo>
      PARSER = new com.google.protobuf.AbstractParser<ReqInfo>() {
    @java.lang.Override
    public ReqInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ReqInfo(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ReqInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ReqInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ReqInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

