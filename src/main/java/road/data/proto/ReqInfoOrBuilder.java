// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ReqInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ReqInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *可选，车道变更请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
   */
  boolean hasLaneChange();
  /**
   * <pre>
   *可选，车道变更请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
   */
  road.data.proto.ReqLaneChange getLaneChange();
  /**
   * <pre>
   *可选，车道变更请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqLaneChange laneChange = 1;</code>
   */
  road.data.proto.ReqLaneChangeOrBuilder getLaneChangeOrBuilder();

  /**
   * <pre>
   *可选，道路清空请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
   */
  boolean hasClearTheWay();
  /**
   * <pre>
   *可选，道路清空请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
   */
  road.data.proto.ReqClearTheWay getClearTheWay();
  /**
   * <pre>
   *可选，道路清空请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqClearTheWay clearTheWay = 2;</code>
   */
  road.data.proto.ReqClearTheWayOrBuilder getClearTheWayOrBuilder();

  /**
   * <pre>
   *可选，信号优先请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
   */
  boolean hasSignalPriority();
  /**
   * <pre>
   *可选，信号优先请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
   */
  road.data.proto.ReqSignalPriority getSignalPriority();
  /**
   * <pre>
   *可选，信号优先请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqSignalPriority signalPriority = 3;</code>
   */
  road.data.proto.ReqSignalPriorityOrBuilder getSignalPriorityOrBuilder();

  /**
   * <pre>
   *可选，感知信息共享请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
   */
  boolean hasSensorSharing();
  /**
   * <pre>
   *可选，感知信息共享请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
   */
  road.data.proto.ReqSensorSharing getSensorSharing();
  /**
   * <pre>
   *可选，感知信息共享请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqSensorSharing sensorSharing = 4;</code>
   */
  road.data.proto.ReqSensorSharingOrBuilder getSensorSharingOrBuilder();

  /**
   * <pre>
   *可选，场站入场请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
   */
  boolean hasParking();
  /**
   * <pre>
   *可选，场站入场请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
   */
  road.data.proto.ReqParkingArea getParking();
  /**
   * <pre>
   *可选，场站入场请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ReqParkingArea parking = 5;</code>
   */
  road.data.proto.ReqParkingAreaOrBuilder getParkingOrBuilder();

  public road.data.proto.ReqInfo.ReqInfoOneOfCase getReqInfoOneOfCase();
}
