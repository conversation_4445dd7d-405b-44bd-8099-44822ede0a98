// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *________________________________________________________________________________
 *车辆意图及请求消息VirData
 *车道变更请求  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ReqLaneChange}
 */
public  final class ReqLaneChange extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ReqLaneChange)
    ReqLaneChangeOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ReqLaneChange.newBuilder() to construct.
  private ReqLaneChange(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ReqLaneChange() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ReqLaneChange();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ReqLaneChange(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (upStreamNode_ != null) {
              subBuilder = upStreamNode_.toBuilder();
            }
            upStreamNode_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(upStreamNode_);
              upStreamNode_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (downStreamNode_ != null) {
              subBuilder = downStreamNode_.toBuilder();
            }
            downStreamNode_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(downStreamNode_);
              downStreamNode_ = subBuilder.buildPartial();
            }

            break;
          }
          case 24: {

            targetLane_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqLaneChange_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqLaneChange_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ReqLaneChange.class, road.data.proto.ReqLaneChange.Builder.class);
  }

  public static final int UPSTREAMNODE_FIELD_NUMBER = 1;
  private road.data.proto.NodeReferenceId upStreamNode_;
  /**
   * <pre>
   *目标路段上游节点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
   */
  public boolean hasUpStreamNode() {
    return upStreamNode_ != null;
  }
  /**
   * <pre>
   *目标路段上游节点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
   */
  public road.data.proto.NodeReferenceId getUpStreamNode() {
    return upStreamNode_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : upStreamNode_;
  }
  /**
   * <pre>
   *目标路段上游节点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getUpStreamNodeOrBuilder() {
    return getUpStreamNode();
  }

  public static final int DOWNSTREAMNODE_FIELD_NUMBER = 2;
  private road.data.proto.NodeReferenceId downStreamNode_;
  /**
   * <pre>
   *目标路段下游节点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
   */
  public boolean hasDownStreamNode() {
    return downStreamNode_ != null;
  }
  /**
   * <pre>
   *目标路段下游节点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
   */
  public road.data.proto.NodeReferenceId getDownStreamNode() {
    return downStreamNode_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : downStreamNode_;
  }
  /**
   * <pre>
   *目标路段下游节点
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getDownStreamNodeOrBuilder() {
    return getDownStreamNode();
  }

  public static final int TARGETLANE_FIELD_NUMBER = 3;
  private int targetLane_;
  /**
   * <pre>
   *目标路段id
   * </pre>
   *
   * <code>uint32 targetLane = 3;</code>
   */
  public int getTargetLane() {
    return targetLane_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (upStreamNode_ != null) {
      output.writeMessage(1, getUpStreamNode());
    }
    if (downStreamNode_ != null) {
      output.writeMessage(2, getDownStreamNode());
    }
    if (targetLane_ != 0) {
      output.writeUInt32(3, targetLane_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (upStreamNode_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getUpStreamNode());
    }
    if (downStreamNode_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getDownStreamNode());
    }
    if (targetLane_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, targetLane_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ReqLaneChange)) {
      return super.equals(obj);
    }
    road.data.proto.ReqLaneChange other = (road.data.proto.ReqLaneChange) obj;

    if (hasUpStreamNode() != other.hasUpStreamNode()) return false;
    if (hasUpStreamNode()) {
      if (!getUpStreamNode()
          .equals(other.getUpStreamNode())) return false;
    }
    if (hasDownStreamNode() != other.hasDownStreamNode()) return false;
    if (hasDownStreamNode()) {
      if (!getDownStreamNode()
          .equals(other.getDownStreamNode())) return false;
    }
    if (getTargetLane()
        != other.getTargetLane()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasUpStreamNode()) {
      hash = (37 * hash) + UPSTREAMNODE_FIELD_NUMBER;
      hash = (53 * hash) + getUpStreamNode().hashCode();
    }
    if (hasDownStreamNode()) {
      hash = (37 * hash) + DOWNSTREAMNODE_FIELD_NUMBER;
      hash = (53 * hash) + getDownStreamNode().hashCode();
    }
    hash = (37 * hash) + TARGETLANE_FIELD_NUMBER;
    hash = (53 * hash) + getTargetLane();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ReqLaneChange parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqLaneChange parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqLaneChange parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqLaneChange parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqLaneChange parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqLaneChange parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqLaneChange parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqLaneChange parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReqLaneChange parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqLaneChange parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReqLaneChange parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqLaneChange parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ReqLaneChange prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *________________________________________________________________________________
   *车辆意图及请求消息VirData
   *车道变更请求  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ReqLaneChange}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ReqLaneChange)
      road.data.proto.ReqLaneChangeOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqLaneChange_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqLaneChange_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ReqLaneChange.class, road.data.proto.ReqLaneChange.Builder.class);
    }

    // Construct using road.data.proto.ReqLaneChange.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (upStreamNodeBuilder_ == null) {
        upStreamNode_ = null;
      } else {
        upStreamNode_ = null;
        upStreamNodeBuilder_ = null;
      }
      if (downStreamNodeBuilder_ == null) {
        downStreamNode_ = null;
      } else {
        downStreamNode_ = null;
        downStreamNodeBuilder_ = null;
      }
      targetLane_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqLaneChange_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ReqLaneChange getDefaultInstanceForType() {
      return road.data.proto.ReqLaneChange.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ReqLaneChange build() {
      road.data.proto.ReqLaneChange result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ReqLaneChange buildPartial() {
      road.data.proto.ReqLaneChange result = new road.data.proto.ReqLaneChange(this);
      if (upStreamNodeBuilder_ == null) {
        result.upStreamNode_ = upStreamNode_;
      } else {
        result.upStreamNode_ = upStreamNodeBuilder_.build();
      }
      if (downStreamNodeBuilder_ == null) {
        result.downStreamNode_ = downStreamNode_;
      } else {
        result.downStreamNode_ = downStreamNodeBuilder_.build();
      }
      result.targetLane_ = targetLane_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ReqLaneChange) {
        return mergeFrom((road.data.proto.ReqLaneChange)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ReqLaneChange other) {
      if (other == road.data.proto.ReqLaneChange.getDefaultInstance()) return this;
      if (other.hasUpStreamNode()) {
        mergeUpStreamNode(other.getUpStreamNode());
      }
      if (other.hasDownStreamNode()) {
        mergeDownStreamNode(other.getDownStreamNode());
      }
      if (other.getTargetLane() != 0) {
        setTargetLane(other.getTargetLane());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ReqLaneChange parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ReqLaneChange) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.NodeReferenceId upStreamNode_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> upStreamNodeBuilder_;
    /**
     * <pre>
     *目标路段上游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
     */
    public boolean hasUpStreamNode() {
      return upStreamNodeBuilder_ != null || upStreamNode_ != null;
    }
    /**
     * <pre>
     *目标路段上游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
     */
    public road.data.proto.NodeReferenceId getUpStreamNode() {
      if (upStreamNodeBuilder_ == null) {
        return upStreamNode_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : upStreamNode_;
      } else {
        return upStreamNodeBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *目标路段上游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
     */
    public Builder setUpStreamNode(road.data.proto.NodeReferenceId value) {
      if (upStreamNodeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        upStreamNode_ = value;
        onChanged();
      } else {
        upStreamNodeBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *目标路段上游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
     */
    public Builder setUpStreamNode(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (upStreamNodeBuilder_ == null) {
        upStreamNode_ = builderForValue.build();
        onChanged();
      } else {
        upStreamNodeBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *目标路段上游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
     */
    public Builder mergeUpStreamNode(road.data.proto.NodeReferenceId value) {
      if (upStreamNodeBuilder_ == null) {
        if (upStreamNode_ != null) {
          upStreamNode_ =
            road.data.proto.NodeReferenceId.newBuilder(upStreamNode_).mergeFrom(value).buildPartial();
        } else {
          upStreamNode_ = value;
        }
        onChanged();
      } else {
        upStreamNodeBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *目标路段上游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
     */
    public Builder clearUpStreamNode() {
      if (upStreamNodeBuilder_ == null) {
        upStreamNode_ = null;
        onChanged();
      } else {
        upStreamNode_ = null;
        upStreamNodeBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *目标路段上游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getUpStreamNodeBuilder() {
      
      onChanged();
      return getUpStreamNodeFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *目标路段上游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getUpStreamNodeOrBuilder() {
      if (upStreamNodeBuilder_ != null) {
        return upStreamNodeBuilder_.getMessageOrBuilder();
      } else {
        return upStreamNode_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : upStreamNode_;
      }
    }
    /**
     * <pre>
     *目标路段上游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId upStreamNode = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getUpStreamNodeFieldBuilder() {
      if (upStreamNodeBuilder_ == null) {
        upStreamNodeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getUpStreamNode(),
                getParentForChildren(),
                isClean());
        upStreamNode_ = null;
      }
      return upStreamNodeBuilder_;
    }

    private road.data.proto.NodeReferenceId downStreamNode_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> downStreamNodeBuilder_;
    /**
     * <pre>
     *目标路段下游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
     */
    public boolean hasDownStreamNode() {
      return downStreamNodeBuilder_ != null || downStreamNode_ != null;
    }
    /**
     * <pre>
     *目标路段下游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
     */
    public road.data.proto.NodeReferenceId getDownStreamNode() {
      if (downStreamNodeBuilder_ == null) {
        return downStreamNode_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : downStreamNode_;
      } else {
        return downStreamNodeBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *目标路段下游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
     */
    public Builder setDownStreamNode(road.data.proto.NodeReferenceId value) {
      if (downStreamNodeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        downStreamNode_ = value;
        onChanged();
      } else {
        downStreamNodeBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *目标路段下游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
     */
    public Builder setDownStreamNode(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (downStreamNodeBuilder_ == null) {
        downStreamNode_ = builderForValue.build();
        onChanged();
      } else {
        downStreamNodeBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *目标路段下游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
     */
    public Builder mergeDownStreamNode(road.data.proto.NodeReferenceId value) {
      if (downStreamNodeBuilder_ == null) {
        if (downStreamNode_ != null) {
          downStreamNode_ =
            road.data.proto.NodeReferenceId.newBuilder(downStreamNode_).mergeFrom(value).buildPartial();
        } else {
          downStreamNode_ = value;
        }
        onChanged();
      } else {
        downStreamNodeBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *目标路段下游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
     */
    public Builder clearDownStreamNode() {
      if (downStreamNodeBuilder_ == null) {
        downStreamNode_ = null;
        onChanged();
      } else {
        downStreamNode_ = null;
        downStreamNodeBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *目标路段下游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getDownStreamNodeBuilder() {
      
      onChanged();
      return getDownStreamNodeFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *目标路段下游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getDownStreamNodeOrBuilder() {
      if (downStreamNodeBuilder_ != null) {
        return downStreamNodeBuilder_.getMessageOrBuilder();
      } else {
        return downStreamNode_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : downStreamNode_;
      }
    }
    /**
     * <pre>
     *目标路段下游节点
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId downStreamNode = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getDownStreamNodeFieldBuilder() {
      if (downStreamNodeBuilder_ == null) {
        downStreamNodeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getDownStreamNode(),
                getParentForChildren(),
                isClean());
        downStreamNode_ = null;
      }
      return downStreamNodeBuilder_;
    }

    private int targetLane_ ;
    /**
     * <pre>
     *目标路段id
     * </pre>
     *
     * <code>uint32 targetLane = 3;</code>
     */
    public int getTargetLane() {
      return targetLane_;
    }
    /**
     * <pre>
     *目标路段id
     * </pre>
     *
     * <code>uint32 targetLane = 3;</code>
     */
    public Builder setTargetLane(int value) {
      
      targetLane_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *目标路段id
     * </pre>
     *
     * <code>uint32 targetLane = 3;</code>
     */
    public Builder clearTargetLane() {
      
      targetLane_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ReqLaneChange)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ReqLaneChange)
  private static final road.data.proto.ReqLaneChange DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ReqLaneChange();
  }

  public static road.data.proto.ReqLaneChange getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ReqLaneChange>
      PARSER = new com.google.protobuf.AbstractParser<ReqLaneChange>() {
    @java.lang.Override
    public ReqLaneChange parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ReqLaneChange(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ReqLaneChange> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ReqLaneChange> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ReqLaneChange getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

