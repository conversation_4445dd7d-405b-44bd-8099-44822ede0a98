// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *场站入场请求  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ReqParkingArea}
 */
public  final class ReqParkingArea extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ReqParkingArea)
    ReqParkingAreaOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ReqParkingArea.newBuilder() to construct.
  private ReqParkingArea(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ReqParkingArea() {
    vehicleType_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ReqParkingArea();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ReqParkingArea(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {
            int rawValue = input.readEnum();

            vehicleType_ = rawValue;
            break;
          }
          case 18: {
            road.data.proto.ParkingRequest.Builder subBuilder = null;
            if (req_ != null) {
              subBuilder = req_.toBuilder();
            }
            req_ = input.readMessage(road.data.proto.ParkingRequest.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(req_);
              req_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            road.data.proto.ParkingType.Builder subBuilder = null;
            if (parkingType_ != null) {
              subBuilder = parkingType_.toBuilder();
            }
            parkingType_ = input.readMessage(road.data.proto.ParkingType.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(parkingType_);
              parkingType_ = subBuilder.buildPartial();
            }

            break;
          }
          case 32: {

            expectedParkingSlotId_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqParkingArea_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqParkingArea_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ReqParkingArea.class, road.data.proto.ReqParkingArea.Builder.class);
  }

  public static final int VEHICLETYPE_FIELD_NUMBER = 1;
  private int vehicleType_;
  /**
   * <pre>
   *车辆类型分类
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 1;</code>
   */
  public int getVehicleTypeValue() {
    return vehicleType_;
  }
  /**
   * <pre>
   *车辆类型分类
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 1;</code>
   */
  public road.data.proto.VehicleType getVehicleType() {
    @SuppressWarnings("deprecation")
    road.data.proto.VehicleType result = road.data.proto.VehicleType.valueOf(vehicleType_);
    return result == null ? road.data.proto.VehicleType.UNRECOGNIZED : result;
  }

  public static final int REQ_FIELD_NUMBER = 2;
  private road.data.proto.ParkingRequest req_;
  /**
   * <pre>
   *来自车或交通站的停车区请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
   */
  public boolean hasReq() {
    return req_ != null;
  }
  /**
   * <pre>
   *来自车或交通站的停车区请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
   */
  public road.data.proto.ParkingRequest getReq() {
    return req_ == null ? road.data.proto.ParkingRequest.getDefaultInstance() : req_;
  }
  /**
   * <pre>
   *来自车或交通站的停车区请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
   */
  public road.data.proto.ParkingRequestOrBuilder getReqOrBuilder() {
    return getReq();
  }

  public static final int PARKINGTYPE_FIELD_NUMBER = 3;
  private road.data.proto.ParkingType parkingType_;
  /**
   * <pre>
   *可选，停车位类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
   */
  public boolean hasParkingType() {
    return parkingType_ != null;
  }
  /**
   * <pre>
   *可选，停车位类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
   */
  public road.data.proto.ParkingType getParkingType() {
    return parkingType_ == null ? road.data.proto.ParkingType.getDefaultInstance() : parkingType_;
  }
  /**
   * <pre>
   *可选，停车位类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
   */
  public road.data.proto.ParkingTypeOrBuilder getParkingTypeOrBuilder() {
    return getParkingType();
  }

  public static final int EXPECTEDPARKINGSLOTID_FIELD_NUMBER = 4;
  private int expectedParkingSlotId_;
  /**
   * <pre>
   *可选，预期停车位id
   * </pre>
   *
   * <code>uint32 expectedParkingSlotId = 4;</code>
   */
  public int getExpectedParkingSlotId() {
    return expectedParkingSlotId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (vehicleType_ != road.data.proto.VehicleType.UNKNOWN_VEHICLE_CLASS.getNumber()) {
      output.writeEnum(1, vehicleType_);
    }
    if (req_ != null) {
      output.writeMessage(2, getReq());
    }
    if (parkingType_ != null) {
      output.writeMessage(3, getParkingType());
    }
    if (expectedParkingSlotId_ != 0) {
      output.writeUInt32(4, expectedParkingSlotId_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (vehicleType_ != road.data.proto.VehicleType.UNKNOWN_VEHICLE_CLASS.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(1, vehicleType_);
    }
    if (req_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getReq());
    }
    if (parkingType_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getParkingType());
    }
    if (expectedParkingSlotId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, expectedParkingSlotId_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ReqParkingArea)) {
      return super.equals(obj);
    }
    road.data.proto.ReqParkingArea other = (road.data.proto.ReqParkingArea) obj;

    if (vehicleType_ != other.vehicleType_) return false;
    if (hasReq() != other.hasReq()) return false;
    if (hasReq()) {
      if (!getReq()
          .equals(other.getReq())) return false;
    }
    if (hasParkingType() != other.hasParkingType()) return false;
    if (hasParkingType()) {
      if (!getParkingType()
          .equals(other.getParkingType())) return false;
    }
    if (getExpectedParkingSlotId()
        != other.getExpectedParkingSlotId()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + VEHICLETYPE_FIELD_NUMBER;
    hash = (53 * hash) + vehicleType_;
    if (hasReq()) {
      hash = (37 * hash) + REQ_FIELD_NUMBER;
      hash = (53 * hash) + getReq().hashCode();
    }
    if (hasParkingType()) {
      hash = (37 * hash) + PARKINGTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getParkingType().hashCode();
    }
    hash = (37 * hash) + EXPECTEDPARKINGSLOTID_FIELD_NUMBER;
    hash = (53 * hash) + getExpectedParkingSlotId();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ReqParkingArea parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqParkingArea parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqParkingArea parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqParkingArea parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqParkingArea parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqParkingArea parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqParkingArea parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqParkingArea parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReqParkingArea parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqParkingArea parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReqParkingArea parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqParkingArea parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ReqParkingArea prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *场站入场请求  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ReqParkingArea}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ReqParkingArea)
      road.data.proto.ReqParkingAreaOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqParkingArea_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqParkingArea_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ReqParkingArea.class, road.data.proto.ReqParkingArea.Builder.class);
    }

    // Construct using road.data.proto.ReqParkingArea.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      vehicleType_ = 0;

      if (reqBuilder_ == null) {
        req_ = null;
      } else {
        req_ = null;
        reqBuilder_ = null;
      }
      if (parkingTypeBuilder_ == null) {
        parkingType_ = null;
      } else {
        parkingType_ = null;
        parkingTypeBuilder_ = null;
      }
      expectedParkingSlotId_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqParkingArea_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ReqParkingArea getDefaultInstanceForType() {
      return road.data.proto.ReqParkingArea.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ReqParkingArea build() {
      road.data.proto.ReqParkingArea result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ReqParkingArea buildPartial() {
      road.data.proto.ReqParkingArea result = new road.data.proto.ReqParkingArea(this);
      result.vehicleType_ = vehicleType_;
      if (reqBuilder_ == null) {
        result.req_ = req_;
      } else {
        result.req_ = reqBuilder_.build();
      }
      if (parkingTypeBuilder_ == null) {
        result.parkingType_ = parkingType_;
      } else {
        result.parkingType_ = parkingTypeBuilder_.build();
      }
      result.expectedParkingSlotId_ = expectedParkingSlotId_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ReqParkingArea) {
        return mergeFrom((road.data.proto.ReqParkingArea)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ReqParkingArea other) {
      if (other == road.data.proto.ReqParkingArea.getDefaultInstance()) return this;
      if (other.vehicleType_ != 0) {
        setVehicleTypeValue(other.getVehicleTypeValue());
      }
      if (other.hasReq()) {
        mergeReq(other.getReq());
      }
      if (other.hasParkingType()) {
        mergeParkingType(other.getParkingType());
      }
      if (other.getExpectedParkingSlotId() != 0) {
        setExpectedParkingSlotId(other.getExpectedParkingSlotId());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ReqParkingArea parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ReqParkingArea) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int vehicleType_ = 0;
    /**
     * <pre>
     *车辆类型分类
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 1;</code>
     */
    public int getVehicleTypeValue() {
      return vehicleType_;
    }
    /**
     * <pre>
     *车辆类型分类
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 1;</code>
     */
    public Builder setVehicleTypeValue(int value) {
      vehicleType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *车辆类型分类
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 1;</code>
     */
    public road.data.proto.VehicleType getVehicleType() {
      @SuppressWarnings("deprecation")
      road.data.proto.VehicleType result = road.data.proto.VehicleType.valueOf(vehicleType_);
      return result == null ? road.data.proto.VehicleType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *车辆类型分类
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 1;</code>
     */
    public Builder setVehicleType(road.data.proto.VehicleType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      vehicleType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *车辆类型分类
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 1;</code>
     */
    public Builder clearVehicleType() {
      
      vehicleType_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.ParkingRequest req_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ParkingRequest, road.data.proto.ParkingRequest.Builder, road.data.proto.ParkingRequestOrBuilder> reqBuilder_;
    /**
     * <pre>
     *来自车或交通站的停车区请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
     */
    public boolean hasReq() {
      return reqBuilder_ != null || req_ != null;
    }
    /**
     * <pre>
     *来自车或交通站的停车区请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
     */
    public road.data.proto.ParkingRequest getReq() {
      if (reqBuilder_ == null) {
        return req_ == null ? road.data.proto.ParkingRequest.getDefaultInstance() : req_;
      } else {
        return reqBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *来自车或交通站的停车区请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
     */
    public Builder setReq(road.data.proto.ParkingRequest value) {
      if (reqBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        req_ = value;
        onChanged();
      } else {
        reqBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *来自车或交通站的停车区请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
     */
    public Builder setReq(
        road.data.proto.ParkingRequest.Builder builderForValue) {
      if (reqBuilder_ == null) {
        req_ = builderForValue.build();
        onChanged();
      } else {
        reqBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *来自车或交通站的停车区请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
     */
    public Builder mergeReq(road.data.proto.ParkingRequest value) {
      if (reqBuilder_ == null) {
        if (req_ != null) {
          req_ =
            road.data.proto.ParkingRequest.newBuilder(req_).mergeFrom(value).buildPartial();
        } else {
          req_ = value;
        }
        onChanged();
      } else {
        reqBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *来自车或交通站的停车区请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
     */
    public Builder clearReq() {
      if (reqBuilder_ == null) {
        req_ = null;
        onChanged();
      } else {
        req_ = null;
        reqBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *来自车或交通站的停车区请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
     */
    public road.data.proto.ParkingRequest.Builder getReqBuilder() {
      
      onChanged();
      return getReqFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *来自车或交通站的停车区请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
     */
    public road.data.proto.ParkingRequestOrBuilder getReqOrBuilder() {
      if (reqBuilder_ != null) {
        return reqBuilder_.getMessageOrBuilder();
      } else {
        return req_ == null ?
            road.data.proto.ParkingRequest.getDefaultInstance() : req_;
      }
    }
    /**
     * <pre>
     *来自车或交通站的停车区请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ParkingRequest, road.data.proto.ParkingRequest.Builder, road.data.proto.ParkingRequestOrBuilder> 
        getReqFieldBuilder() {
      if (reqBuilder_ == null) {
        reqBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ParkingRequest, road.data.proto.ParkingRequest.Builder, road.data.proto.ParkingRequestOrBuilder>(
                getReq(),
                getParentForChildren(),
                isClean());
        req_ = null;
      }
      return reqBuilder_;
    }

    private road.data.proto.ParkingType parkingType_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ParkingType, road.data.proto.ParkingType.Builder, road.data.proto.ParkingTypeOrBuilder> parkingTypeBuilder_;
    /**
     * <pre>
     *可选，停车位类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
     */
    public boolean hasParkingType() {
      return parkingTypeBuilder_ != null || parkingType_ != null;
    }
    /**
     * <pre>
     *可选，停车位类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
     */
    public road.data.proto.ParkingType getParkingType() {
      if (parkingTypeBuilder_ == null) {
        return parkingType_ == null ? road.data.proto.ParkingType.getDefaultInstance() : parkingType_;
      } else {
        return parkingTypeBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，停车位类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
     */
    public Builder setParkingType(road.data.proto.ParkingType value) {
      if (parkingTypeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        parkingType_ = value;
        onChanged();
      } else {
        parkingTypeBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，停车位类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
     */
    public Builder setParkingType(
        road.data.proto.ParkingType.Builder builderForValue) {
      if (parkingTypeBuilder_ == null) {
        parkingType_ = builderForValue.build();
        onChanged();
      } else {
        parkingTypeBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，停车位类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
     */
    public Builder mergeParkingType(road.data.proto.ParkingType value) {
      if (parkingTypeBuilder_ == null) {
        if (parkingType_ != null) {
          parkingType_ =
            road.data.proto.ParkingType.newBuilder(parkingType_).mergeFrom(value).buildPartial();
        } else {
          parkingType_ = value;
        }
        onChanged();
      } else {
        parkingTypeBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，停车位类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
     */
    public Builder clearParkingType() {
      if (parkingTypeBuilder_ == null) {
        parkingType_ = null;
        onChanged();
      } else {
        parkingType_ = null;
        parkingTypeBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，停车位类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
     */
    public road.data.proto.ParkingType.Builder getParkingTypeBuilder() {
      
      onChanged();
      return getParkingTypeFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，停车位类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
     */
    public road.data.proto.ParkingTypeOrBuilder getParkingTypeOrBuilder() {
      if (parkingTypeBuilder_ != null) {
        return parkingTypeBuilder_.getMessageOrBuilder();
      } else {
        return parkingType_ == null ?
            road.data.proto.ParkingType.getDefaultInstance() : parkingType_;
      }
    }
    /**
     * <pre>
     *可选，停车位类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.ParkingType, road.data.proto.ParkingType.Builder, road.data.proto.ParkingTypeOrBuilder> 
        getParkingTypeFieldBuilder() {
      if (parkingTypeBuilder_ == null) {
        parkingTypeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.ParkingType, road.data.proto.ParkingType.Builder, road.data.proto.ParkingTypeOrBuilder>(
                getParkingType(),
                getParentForChildren(),
                isClean());
        parkingType_ = null;
      }
      return parkingTypeBuilder_;
    }

    private int expectedParkingSlotId_ ;
    /**
     * <pre>
     *可选，预期停车位id
     * </pre>
     *
     * <code>uint32 expectedParkingSlotId = 4;</code>
     */
    public int getExpectedParkingSlotId() {
      return expectedParkingSlotId_;
    }
    /**
     * <pre>
     *可选，预期停车位id
     * </pre>
     *
     * <code>uint32 expectedParkingSlotId = 4;</code>
     */
    public Builder setExpectedParkingSlotId(int value) {
      
      expectedParkingSlotId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，预期停车位id
     * </pre>
     *
     * <code>uint32 expectedParkingSlotId = 4;</code>
     */
    public Builder clearExpectedParkingSlotId() {
      
      expectedParkingSlotId_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ReqParkingArea)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ReqParkingArea)
  private static final road.data.proto.ReqParkingArea DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ReqParkingArea();
  }

  public static road.data.proto.ReqParkingArea getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ReqParkingArea>
      PARSER = new com.google.protobuf.AbstractParser<ReqParkingArea>() {
    @java.lang.Override
    public ReqParkingArea parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ReqParkingArea(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ReqParkingArea> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ReqParkingArea> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ReqParkingArea getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

