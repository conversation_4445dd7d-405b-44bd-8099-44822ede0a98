// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ReqParkingAreaOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ReqParkingArea)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *车辆类型分类
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 1;</code>
   */
  int getVehicleTypeValue();
  /**
   * <pre>
   *车辆类型分类
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 1;</code>
   */
  road.data.proto.VehicleType getVehicleType();

  /**
   * <pre>
   *来自车或交通站的停车区请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
   */
  boolean hasReq();
  /**
   * <pre>
   *来自车或交通站的停车区请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
   */
  road.data.proto.ParkingRequest getReq();
  /**
   * <pre>
   *来自车或交通站的停车区请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParkingRequest req = 2;</code>
   */
  road.data.proto.ParkingRequestOrBuilder getReqOrBuilder();

  /**
   * <pre>
   *可选，停车位类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
   */
  boolean hasParkingType();
  /**
   * <pre>
   *可选，停车位类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
   */
  road.data.proto.ParkingType getParkingType();
  /**
   * <pre>
   *可选，停车位类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParkingType parkingType = 3;</code>
   */
  road.data.proto.ParkingTypeOrBuilder getParkingTypeOrBuilder();

  /**
   * <pre>
   *可选，预期停车位id
   * </pre>
   *
   * <code>uint32 expectedParkingSlotId = 4;</code>
   */
  int getExpectedParkingSlotId();
}
