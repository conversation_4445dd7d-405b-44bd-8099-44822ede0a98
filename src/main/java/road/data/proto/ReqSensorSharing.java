// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *感知信息共享请求  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ReqSensorSharing}
 */
public  final class ReqSensorSharing extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ReqSensorSharing)
    ReqSensorSharingOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ReqSensorSharing.newBuilder() to construct.
  private ReqSensorSharing(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ReqSensorSharing() {
    detectorArea_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ReqSensorSharing();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ReqSensorSharing(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              detectorArea_ = new java.util.ArrayList<road.data.proto.ReferencePath>();
              mutable_bitField0_ |= 0x00000001;
            }
            detectorArea_.add(
                input.readMessage(road.data.proto.ReferencePath.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        detectorArea_ = java.util.Collections.unmodifiableList(detectorArea_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqSensorSharing_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqSensorSharing_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ReqSensorSharing.class, road.data.proto.ReqSensorSharing.Builder.class);
  }

  public static final int DETECTORAREA_FIELD_NUMBER = 1;
  private java.util.List<road.data.proto.ReferencePath> detectorArea_;
  /**
   * <pre>
   *可选，请求的感知区域的相关路径列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
   */
  public java.util.List<road.data.proto.ReferencePath> getDetectorAreaList() {
    return detectorArea_;
  }
  /**
   * <pre>
   *可选，请求的感知区域的相关路径列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
   */
  public java.util.List<? extends road.data.proto.ReferencePathOrBuilder> 
      getDetectorAreaOrBuilderList() {
    return detectorArea_;
  }
  /**
   * <pre>
   *可选，请求的感知区域的相关路径列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
   */
  public int getDetectorAreaCount() {
    return detectorArea_.size();
  }
  /**
   * <pre>
   *可选，请求的感知区域的相关路径列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
   */
  public road.data.proto.ReferencePath getDetectorArea(int index) {
    return detectorArea_.get(index);
  }
  /**
   * <pre>
   *可选，请求的感知区域的相关路径列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
   */
  public road.data.proto.ReferencePathOrBuilder getDetectorAreaOrBuilder(
      int index) {
    return detectorArea_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < detectorArea_.size(); i++) {
      output.writeMessage(1, detectorArea_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < detectorArea_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, detectorArea_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ReqSensorSharing)) {
      return super.equals(obj);
    }
    road.data.proto.ReqSensorSharing other = (road.data.proto.ReqSensorSharing) obj;

    if (!getDetectorAreaList()
        .equals(other.getDetectorAreaList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getDetectorAreaCount() > 0) {
      hash = (37 * hash) + DETECTORAREA_FIELD_NUMBER;
      hash = (53 * hash) + getDetectorAreaList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ReqSensorSharing parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqSensorSharing parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqSensorSharing parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqSensorSharing parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqSensorSharing parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqSensorSharing parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqSensorSharing parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqSensorSharing parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReqSensorSharing parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqSensorSharing parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReqSensorSharing parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqSensorSharing parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ReqSensorSharing prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *感知信息共享请求  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ReqSensorSharing}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ReqSensorSharing)
      road.data.proto.ReqSensorSharingOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqSensorSharing_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqSensorSharing_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ReqSensorSharing.class, road.data.proto.ReqSensorSharing.Builder.class);
    }

    // Construct using road.data.proto.ReqSensorSharing.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getDetectorAreaFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (detectorAreaBuilder_ == null) {
        detectorArea_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        detectorAreaBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqSensorSharing_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ReqSensorSharing getDefaultInstanceForType() {
      return road.data.proto.ReqSensorSharing.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ReqSensorSharing build() {
      road.data.proto.ReqSensorSharing result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ReqSensorSharing buildPartial() {
      road.data.proto.ReqSensorSharing result = new road.data.proto.ReqSensorSharing(this);
      int from_bitField0_ = bitField0_;
      if (detectorAreaBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          detectorArea_ = java.util.Collections.unmodifiableList(detectorArea_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.detectorArea_ = detectorArea_;
      } else {
        result.detectorArea_ = detectorAreaBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ReqSensorSharing) {
        return mergeFrom((road.data.proto.ReqSensorSharing)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ReqSensorSharing other) {
      if (other == road.data.proto.ReqSensorSharing.getDefaultInstance()) return this;
      if (detectorAreaBuilder_ == null) {
        if (!other.detectorArea_.isEmpty()) {
          if (detectorArea_.isEmpty()) {
            detectorArea_ = other.detectorArea_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDetectorAreaIsMutable();
            detectorArea_.addAll(other.detectorArea_);
          }
          onChanged();
        }
      } else {
        if (!other.detectorArea_.isEmpty()) {
          if (detectorAreaBuilder_.isEmpty()) {
            detectorAreaBuilder_.dispose();
            detectorAreaBuilder_ = null;
            detectorArea_ = other.detectorArea_;
            bitField0_ = (bitField0_ & ~0x00000001);
            detectorAreaBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getDetectorAreaFieldBuilder() : null;
          } else {
            detectorAreaBuilder_.addAllMessages(other.detectorArea_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ReqSensorSharing parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ReqSensorSharing) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.util.List<road.data.proto.ReferencePath> detectorArea_ =
      java.util.Collections.emptyList();
    private void ensureDetectorAreaIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        detectorArea_ = new java.util.ArrayList<road.data.proto.ReferencePath>(detectorArea_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder> detectorAreaBuilder_;

    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public java.util.List<road.data.proto.ReferencePath> getDetectorAreaList() {
      if (detectorAreaBuilder_ == null) {
        return java.util.Collections.unmodifiableList(detectorArea_);
      } else {
        return detectorAreaBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public int getDetectorAreaCount() {
      if (detectorAreaBuilder_ == null) {
        return detectorArea_.size();
      } else {
        return detectorAreaBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public road.data.proto.ReferencePath getDetectorArea(int index) {
      if (detectorAreaBuilder_ == null) {
        return detectorArea_.get(index);
      } else {
        return detectorAreaBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public Builder setDetectorArea(
        int index, road.data.proto.ReferencePath value) {
      if (detectorAreaBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetectorAreaIsMutable();
        detectorArea_.set(index, value);
        onChanged();
      } else {
        detectorAreaBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public Builder setDetectorArea(
        int index, road.data.proto.ReferencePath.Builder builderForValue) {
      if (detectorAreaBuilder_ == null) {
        ensureDetectorAreaIsMutable();
        detectorArea_.set(index, builderForValue.build());
        onChanged();
      } else {
        detectorAreaBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public Builder addDetectorArea(road.data.proto.ReferencePath value) {
      if (detectorAreaBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetectorAreaIsMutable();
        detectorArea_.add(value);
        onChanged();
      } else {
        detectorAreaBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public Builder addDetectorArea(
        int index, road.data.proto.ReferencePath value) {
      if (detectorAreaBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetectorAreaIsMutable();
        detectorArea_.add(index, value);
        onChanged();
      } else {
        detectorAreaBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public Builder addDetectorArea(
        road.data.proto.ReferencePath.Builder builderForValue) {
      if (detectorAreaBuilder_ == null) {
        ensureDetectorAreaIsMutable();
        detectorArea_.add(builderForValue.build());
        onChanged();
      } else {
        detectorAreaBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public Builder addDetectorArea(
        int index, road.data.proto.ReferencePath.Builder builderForValue) {
      if (detectorAreaBuilder_ == null) {
        ensureDetectorAreaIsMutable();
        detectorArea_.add(index, builderForValue.build());
        onChanged();
      } else {
        detectorAreaBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public Builder addAllDetectorArea(
        java.lang.Iterable<? extends road.data.proto.ReferencePath> values) {
      if (detectorAreaBuilder_ == null) {
        ensureDetectorAreaIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, detectorArea_);
        onChanged();
      } else {
        detectorAreaBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public Builder clearDetectorArea() {
      if (detectorAreaBuilder_ == null) {
        detectorArea_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        detectorAreaBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public Builder removeDetectorArea(int index) {
      if (detectorAreaBuilder_ == null) {
        ensureDetectorAreaIsMutable();
        detectorArea_.remove(index);
        onChanged();
      } else {
        detectorAreaBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public road.data.proto.ReferencePath.Builder getDetectorAreaBuilder(
        int index) {
      return getDetectorAreaFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public road.data.proto.ReferencePathOrBuilder getDetectorAreaOrBuilder(
        int index) {
      if (detectorAreaBuilder_ == null) {
        return detectorArea_.get(index);  } else {
        return detectorAreaBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public java.util.List<? extends road.data.proto.ReferencePathOrBuilder> 
         getDetectorAreaOrBuilderList() {
      if (detectorAreaBuilder_ != null) {
        return detectorAreaBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(detectorArea_);
      }
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public road.data.proto.ReferencePath.Builder addDetectorAreaBuilder() {
      return getDetectorAreaFieldBuilder().addBuilder(
          road.data.proto.ReferencePath.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public road.data.proto.ReferencePath.Builder addDetectorAreaBuilder(
        int index) {
      return getDetectorAreaFieldBuilder().addBuilder(
          index, road.data.proto.ReferencePath.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，请求的感知区域的相关路径列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
     */
    public java.util.List<road.data.proto.ReferencePath.Builder> 
         getDetectorAreaBuilderList() {
      return getDetectorAreaFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder> 
        getDetectorAreaFieldBuilder() {
      if (detectorAreaBuilder_ == null) {
        detectorAreaBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder>(
                detectorArea_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        detectorArea_ = null;
      }
      return detectorAreaBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ReqSensorSharing)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ReqSensorSharing)
  private static final road.data.proto.ReqSensorSharing DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ReqSensorSharing();
  }

  public static road.data.proto.ReqSensorSharing getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ReqSensorSharing>
      PARSER = new com.google.protobuf.AbstractParser<ReqSensorSharing>() {
    @java.lang.Override
    public ReqSensorSharing parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ReqSensorSharing(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ReqSensorSharing> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ReqSensorSharing> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ReqSensorSharing getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

