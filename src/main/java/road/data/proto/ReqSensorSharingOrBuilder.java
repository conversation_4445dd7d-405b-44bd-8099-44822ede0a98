// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ReqSensorSharingOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ReqSensorSharing)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *可选，请求的感知区域的相关路径列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
   */
  java.util.List<road.data.proto.ReferencePath> 
      getDetectorAreaList();
  /**
   * <pre>
   *可选，请求的感知区域的相关路径列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
   */
  road.data.proto.ReferencePath getDetectorArea(int index);
  /**
   * <pre>
   *可选，请求的感知区域的相关路径列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
   */
  int getDetectorAreaCount();
  /**
   * <pre>
   *可选，请求的感知区域的相关路径列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
   */
  java.util.List<? extends road.data.proto.ReferencePathOrBuilder> 
      getDetectorAreaOrBuilderList();
  /**
   * <pre>
   *可选，请求的感知区域的相关路径列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath detectorArea = 1;</code>
   */
  road.data.proto.ReferencePathOrBuilder getDetectorAreaOrBuilder(
      int index);
}
