// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *信号优先请求  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ReqSignalPriority}
 */
public  final class ReqSignalPriority extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ReqSignalPriority)
    ReqSignalPriorityOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ReqSignalPriority.newBuilder() to construct.
  private ReqSignalPriority(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ReqSignalPriority() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ReqSignalPriority();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ReqSignalPriority(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (intersectionId_ != null) {
              subBuilder = intersectionId_.toBuilder();
            }
            intersectionId_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(intersectionId_);
              intersectionId_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            road.data.proto.MovementStatInfo.Builder subBuilder = null;
            if (requiredMove_ != null) {
              subBuilder = requiredMove_.toBuilder();
            }
            requiredMove_ = input.readMessage(road.data.proto.MovementStatInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(requiredMove_);
              requiredMove_ = subBuilder.buildPartial();
            }

            break;
          }
          case 24: {

            estimatedArrivalTime_ = input.readUInt32();
            break;
          }
          case 32: {

            distance2Intersection_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqSignalPriority_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqSignalPriority_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ReqSignalPriority.class, road.data.proto.ReqSignalPriority.Builder.class);
  }

  public static final int INTERSECTIONID_FIELD_NUMBER = 1;
  private road.data.proto.NodeReferenceId intersectionId_;
  /**
   * <pre>
   *  指示目标交通信号的交叉口 id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
   */
  public boolean hasIntersectionId() {
    return intersectionId_ != null;
  }
  /**
   * <pre>
   *  指示目标交通信号的交叉口 id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
   */
  public road.data.proto.NodeReferenceId getIntersectionId() {
    return intersectionId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : intersectionId_;
  }
  /**
   * <pre>
   *  指示目标交通信号的交叉口 id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getIntersectionIdOrBuilder() {
    return getIntersectionId();
  }

  public static final int REQUIREDMOVE_FIELD_NUMBER = 2;
  private road.data.proto.MovementStatInfo requiredMove_;
  /**
   * <pre>
   *运动信息。 需要包括远程交叉口id和转弯方向
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
   */
  public boolean hasRequiredMove() {
    return requiredMove_ != null;
  }
  /**
   * <pre>
   *运动信息。 需要包括远程交叉口id和转弯方向
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
   */
  public road.data.proto.MovementStatInfo getRequiredMove() {
    return requiredMove_ == null ? road.data.proto.MovementStatInfo.getDefaultInstance() : requiredMove_;
  }
  /**
   * <pre>
   *运动信息。 需要包括远程交叉口id和转弯方向
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
   */
  public road.data.proto.MovementStatInfoOrBuilder getRequiredMoveOrBuilder() {
    return getRequiredMove();
  }

  public static final int ESTIMATEDARRIVALTIME_FIELD_NUMBER = 3;
  private int estimatedArrivalTime_;
  /**
   * <pre>
   *可选，时间偏移
   * </pre>
   *
   * <code>uint32 estimatedArrivalTime = 3;</code>
   */
  public int getEstimatedArrivalTime() {
    return estimatedArrivalTime_;
  }

  public static final int DISTANCE2INTERSECTION_FIELD_NUMBER = 4;
  private int distance2Intersection_;
  /**
   * <pre>
   *可选，到达路口的距离，单位0.1m
   * </pre>
   *
   * <code>uint32 distance2Intersection = 4;</code>
   */
  public int getDistance2Intersection() {
    return distance2Intersection_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (intersectionId_ != null) {
      output.writeMessage(1, getIntersectionId());
    }
    if (requiredMove_ != null) {
      output.writeMessage(2, getRequiredMove());
    }
    if (estimatedArrivalTime_ != 0) {
      output.writeUInt32(3, estimatedArrivalTime_);
    }
    if (distance2Intersection_ != 0) {
      output.writeUInt32(4, distance2Intersection_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (intersectionId_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getIntersectionId());
    }
    if (requiredMove_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getRequiredMove());
    }
    if (estimatedArrivalTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, estimatedArrivalTime_);
    }
    if (distance2Intersection_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, distance2Intersection_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ReqSignalPriority)) {
      return super.equals(obj);
    }
    road.data.proto.ReqSignalPriority other = (road.data.proto.ReqSignalPriority) obj;

    if (hasIntersectionId() != other.hasIntersectionId()) return false;
    if (hasIntersectionId()) {
      if (!getIntersectionId()
          .equals(other.getIntersectionId())) return false;
    }
    if (hasRequiredMove() != other.hasRequiredMove()) return false;
    if (hasRequiredMove()) {
      if (!getRequiredMove()
          .equals(other.getRequiredMove())) return false;
    }
    if (getEstimatedArrivalTime()
        != other.getEstimatedArrivalTime()) return false;
    if (getDistance2Intersection()
        != other.getDistance2Intersection()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasIntersectionId()) {
      hash = (37 * hash) + INTERSECTIONID_FIELD_NUMBER;
      hash = (53 * hash) + getIntersectionId().hashCode();
    }
    if (hasRequiredMove()) {
      hash = (37 * hash) + REQUIREDMOVE_FIELD_NUMBER;
      hash = (53 * hash) + getRequiredMove().hashCode();
    }
    hash = (37 * hash) + ESTIMATEDARRIVALTIME_FIELD_NUMBER;
    hash = (53 * hash) + getEstimatedArrivalTime();
    hash = (37 * hash) + DISTANCE2INTERSECTION_FIELD_NUMBER;
    hash = (53 * hash) + getDistance2Intersection();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ReqSignalPriority parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqSignalPriority parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqSignalPriority parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqSignalPriority parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqSignalPriority parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ReqSignalPriority parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ReqSignalPriority parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqSignalPriority parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReqSignalPriority parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqSignalPriority parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ReqSignalPriority parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ReqSignalPriority parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ReqSignalPriority prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *信号优先请求  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ReqSignalPriority}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ReqSignalPriority)
      road.data.proto.ReqSignalPriorityOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqSignalPriority_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqSignalPriority_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ReqSignalPriority.class, road.data.proto.ReqSignalPriority.Builder.class);
    }

    // Construct using road.data.proto.ReqSignalPriority.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (intersectionIdBuilder_ == null) {
        intersectionId_ = null;
      } else {
        intersectionId_ = null;
        intersectionIdBuilder_ = null;
      }
      if (requiredMoveBuilder_ == null) {
        requiredMove_ = null;
      } else {
        requiredMove_ = null;
        requiredMoveBuilder_ = null;
      }
      estimatedArrivalTime_ = 0;

      distance2Intersection_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ReqSignalPriority_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ReqSignalPriority getDefaultInstanceForType() {
      return road.data.proto.ReqSignalPriority.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ReqSignalPriority build() {
      road.data.proto.ReqSignalPriority result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ReqSignalPriority buildPartial() {
      road.data.proto.ReqSignalPriority result = new road.data.proto.ReqSignalPriority(this);
      if (intersectionIdBuilder_ == null) {
        result.intersectionId_ = intersectionId_;
      } else {
        result.intersectionId_ = intersectionIdBuilder_.build();
      }
      if (requiredMoveBuilder_ == null) {
        result.requiredMove_ = requiredMove_;
      } else {
        result.requiredMove_ = requiredMoveBuilder_.build();
      }
      result.estimatedArrivalTime_ = estimatedArrivalTime_;
      result.distance2Intersection_ = distance2Intersection_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ReqSignalPriority) {
        return mergeFrom((road.data.proto.ReqSignalPriority)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ReqSignalPriority other) {
      if (other == road.data.proto.ReqSignalPriority.getDefaultInstance()) return this;
      if (other.hasIntersectionId()) {
        mergeIntersectionId(other.getIntersectionId());
      }
      if (other.hasRequiredMove()) {
        mergeRequiredMove(other.getRequiredMove());
      }
      if (other.getEstimatedArrivalTime() != 0) {
        setEstimatedArrivalTime(other.getEstimatedArrivalTime());
      }
      if (other.getDistance2Intersection() != 0) {
        setDistance2Intersection(other.getDistance2Intersection());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ReqSignalPriority parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ReqSignalPriority) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.NodeReferenceId intersectionId_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> intersectionIdBuilder_;
    /**
     * <pre>
     *  指示目标交通信号的交叉口 id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public boolean hasIntersectionId() {
      return intersectionIdBuilder_ != null || intersectionId_ != null;
    }
    /**
     * <pre>
     *  指示目标交通信号的交叉口 id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public road.data.proto.NodeReferenceId getIntersectionId() {
      if (intersectionIdBuilder_ == null) {
        return intersectionId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : intersectionId_;
      } else {
        return intersectionIdBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *  指示目标交通信号的交叉口 id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public Builder setIntersectionId(road.data.proto.NodeReferenceId value) {
      if (intersectionIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        intersectionId_ = value;
        onChanged();
      } else {
        intersectionIdBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *  指示目标交通信号的交叉口 id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public Builder setIntersectionId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (intersectionIdBuilder_ == null) {
        intersectionId_ = builderForValue.build();
        onChanged();
      } else {
        intersectionIdBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *  指示目标交通信号的交叉口 id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public Builder mergeIntersectionId(road.data.proto.NodeReferenceId value) {
      if (intersectionIdBuilder_ == null) {
        if (intersectionId_ != null) {
          intersectionId_ =
            road.data.proto.NodeReferenceId.newBuilder(intersectionId_).mergeFrom(value).buildPartial();
        } else {
          intersectionId_ = value;
        }
        onChanged();
      } else {
        intersectionIdBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *  指示目标交通信号的交叉口 id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public Builder clearIntersectionId() {
      if (intersectionIdBuilder_ == null) {
        intersectionId_ = null;
        onChanged();
      } else {
        intersectionId_ = null;
        intersectionIdBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *  指示目标交通信号的交叉口 id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getIntersectionIdBuilder() {
      
      onChanged();
      return getIntersectionIdFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *  指示目标交通信号的交叉口 id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getIntersectionIdOrBuilder() {
      if (intersectionIdBuilder_ != null) {
        return intersectionIdBuilder_.getMessageOrBuilder();
      } else {
        return intersectionId_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : intersectionId_;
      }
    }
    /**
     * <pre>
     *  指示目标交通信号的交叉口 id
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getIntersectionIdFieldBuilder() {
      if (intersectionIdBuilder_ == null) {
        intersectionIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getIntersectionId(),
                getParentForChildren(),
                isClean());
        intersectionId_ = null;
      }
      return intersectionIdBuilder_;
    }

    private road.data.proto.MovementStatInfo requiredMove_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MovementStatInfo, road.data.proto.MovementStatInfo.Builder, road.data.proto.MovementStatInfoOrBuilder> requiredMoveBuilder_;
    /**
     * <pre>
     *运动信息。 需要包括远程交叉口id和转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
     */
    public boolean hasRequiredMove() {
      return requiredMoveBuilder_ != null || requiredMove_ != null;
    }
    /**
     * <pre>
     *运动信息。 需要包括远程交叉口id和转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
     */
    public road.data.proto.MovementStatInfo getRequiredMove() {
      if (requiredMoveBuilder_ == null) {
        return requiredMove_ == null ? road.data.proto.MovementStatInfo.getDefaultInstance() : requiredMove_;
      } else {
        return requiredMoveBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *运动信息。 需要包括远程交叉口id和转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
     */
    public Builder setRequiredMove(road.data.proto.MovementStatInfo value) {
      if (requiredMoveBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        requiredMove_ = value;
        onChanged();
      } else {
        requiredMoveBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *运动信息。 需要包括远程交叉口id和转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
     */
    public Builder setRequiredMove(
        road.data.proto.MovementStatInfo.Builder builderForValue) {
      if (requiredMoveBuilder_ == null) {
        requiredMove_ = builderForValue.build();
        onChanged();
      } else {
        requiredMoveBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *运动信息。 需要包括远程交叉口id和转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
     */
    public Builder mergeRequiredMove(road.data.proto.MovementStatInfo value) {
      if (requiredMoveBuilder_ == null) {
        if (requiredMove_ != null) {
          requiredMove_ =
            road.data.proto.MovementStatInfo.newBuilder(requiredMove_).mergeFrom(value).buildPartial();
        } else {
          requiredMove_ = value;
        }
        onChanged();
      } else {
        requiredMoveBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *运动信息。 需要包括远程交叉口id和转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
     */
    public Builder clearRequiredMove() {
      if (requiredMoveBuilder_ == null) {
        requiredMove_ = null;
        onChanged();
      } else {
        requiredMove_ = null;
        requiredMoveBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *运动信息。 需要包括远程交叉口id和转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
     */
    public road.data.proto.MovementStatInfo.Builder getRequiredMoveBuilder() {
      
      onChanged();
      return getRequiredMoveFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *运动信息。 需要包括远程交叉口id和转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
     */
    public road.data.proto.MovementStatInfoOrBuilder getRequiredMoveOrBuilder() {
      if (requiredMoveBuilder_ != null) {
        return requiredMoveBuilder_.getMessageOrBuilder();
      } else {
        return requiredMove_ == null ?
            road.data.proto.MovementStatInfo.getDefaultInstance() : requiredMove_;
      }
    }
    /**
     * <pre>
     *运动信息。 需要包括远程交叉口id和转弯方向
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MovementStatInfo, road.data.proto.MovementStatInfo.Builder, road.data.proto.MovementStatInfoOrBuilder> 
        getRequiredMoveFieldBuilder() {
      if (requiredMoveBuilder_ == null) {
        requiredMoveBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.MovementStatInfo, road.data.proto.MovementStatInfo.Builder, road.data.proto.MovementStatInfoOrBuilder>(
                getRequiredMove(),
                getParentForChildren(),
                isClean());
        requiredMove_ = null;
      }
      return requiredMoveBuilder_;
    }

    private int estimatedArrivalTime_ ;
    /**
     * <pre>
     *可选，时间偏移
     * </pre>
     *
     * <code>uint32 estimatedArrivalTime = 3;</code>
     */
    public int getEstimatedArrivalTime() {
      return estimatedArrivalTime_;
    }
    /**
     * <pre>
     *可选，时间偏移
     * </pre>
     *
     * <code>uint32 estimatedArrivalTime = 3;</code>
     */
    public Builder setEstimatedArrivalTime(int value) {
      
      estimatedArrivalTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，时间偏移
     * </pre>
     *
     * <code>uint32 estimatedArrivalTime = 3;</code>
     */
    public Builder clearEstimatedArrivalTime() {
      
      estimatedArrivalTime_ = 0;
      onChanged();
      return this;
    }

    private int distance2Intersection_ ;
    /**
     * <pre>
     *可选，到达路口的距离，单位0.1m
     * </pre>
     *
     * <code>uint32 distance2Intersection = 4;</code>
     */
    public int getDistance2Intersection() {
      return distance2Intersection_;
    }
    /**
     * <pre>
     *可选，到达路口的距离，单位0.1m
     * </pre>
     *
     * <code>uint32 distance2Intersection = 4;</code>
     */
    public Builder setDistance2Intersection(int value) {
      
      distance2Intersection_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，到达路口的距离，单位0.1m
     * </pre>
     *
     * <code>uint32 distance2Intersection = 4;</code>
     */
    public Builder clearDistance2Intersection() {
      
      distance2Intersection_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ReqSignalPriority)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ReqSignalPriority)
  private static final road.data.proto.ReqSignalPriority DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ReqSignalPriority();
  }

  public static road.data.proto.ReqSignalPriority getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ReqSignalPriority>
      PARSER = new com.google.protobuf.AbstractParser<ReqSignalPriority>() {
    @java.lang.Override
    public ReqSignalPriority parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ReqSignalPriority(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ReqSignalPriority> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ReqSignalPriority> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ReqSignalPriority getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

