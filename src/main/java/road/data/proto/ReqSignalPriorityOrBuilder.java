// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ReqSignalPriorityOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ReqSignalPriority)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *  指示目标交通信号的交叉口 id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
   */
  boolean hasIntersectionId();
  /**
   * <pre>
   *  指示目标交通信号的交叉口 id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
   */
  road.data.proto.NodeReferenceId getIntersectionId();
  /**
   * <pre>
   *  指示目标交通信号的交叉口 id
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId intersectionId = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getIntersectionIdOrBuilder();

  /**
   * <pre>
   *运动信息。 需要包括远程交叉口id和转弯方向
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
   */
  boolean hasRequiredMove();
  /**
   * <pre>
   *运动信息。 需要包括远程交叉口id和转弯方向
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
   */
  road.data.proto.MovementStatInfo getRequiredMove();
  /**
   * <pre>
   *运动信息。 需要包括远程交叉口id和转弯方向
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MovementStatInfo requiredMove = 2;</code>
   */
  road.data.proto.MovementStatInfoOrBuilder getRequiredMoveOrBuilder();

  /**
   * <pre>
   *可选，时间偏移
   * </pre>
   *
   * <code>uint32 estimatedArrivalTime = 3;</code>
   */
  int getEstimatedArrivalTime();

  /**
   * <pre>
   *可选，到达路口的距离，单位0.1m
   * </pre>
   *
   * <code>uint32 distance2Intersection = 4;</code>
   */
  int getDistance2Intersection();
}
