// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车辆协作或引导RscData   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.RscData}
 */
public  final class RscData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.RscData)
    RscDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RscData.newBuilder() to construct.
  private RscData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RscData() {
    rsuId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RscData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RscData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            msgCnt_ = input.readUInt32();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            rsuId_ = s;
            break;
          }
          case 24: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 34: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (pos_ != null) {
              subBuilder = pos_.toBuilder();
            }
            pos_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(pos_);
              pos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 42: {
            road.data.proto.VehicleCoordination.Builder subBuilder = null;
            if (coordinates_ != null) {
              subBuilder = coordinates_.toBuilder();
            }
            coordinates_ = input.readMessage(road.data.proto.VehicleCoordination.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(coordinates_);
              coordinates_ = subBuilder.buildPartial();
            }

            break;
          }
          case 50: {
            road.data.proto.LaneCoordination.Builder subBuilder = null;
            if (laneCoordinates_ != null) {
              subBuilder = laneCoordinates_.toBuilder();
            }
            laneCoordinates_ = input.readMessage(road.data.proto.LaneCoordination.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(laneCoordinates_);
              laneCoordinates_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RscData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RscData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.RscData.class, road.data.proto.RscData.Builder.class);
  }

  public static final int MSGCNT_FIELD_NUMBER = 1;
  private int msgCnt_;
  /**
   * <pre>
   *定义消息编号。
   * </pre>
   *
   * <code>uint32 msgCnt = 1;</code>
   */
  public int getMsgCnt() {
    return msgCnt_;
  }

  public static final int RSUID_FIELD_NUMBER = 2;
  private volatile java.lang.Object rsuId_;
  /**
   * <pre>
   *RSU id
   * </pre>
   *
   * <code>string rsuId = 2;</code>
   */
  public java.lang.String getRsuId() {
    java.lang.Object ref = rsuId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      rsuId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *RSU id
   * </pre>
   *
   * <code>string rsuId = 2;</code>
   */
  public com.google.protobuf.ByteString
      getRsuIdBytes() {
    java.lang.Object ref = rsuId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      rsuId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 3;
  private long timestamp_;
  /**
   * <pre>
   *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 3;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int POS_FIELD_NUMBER = 4;
  private road.data.proto.Position3D pos_;
  /**
   * <pre>
   *参考点位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  public boolean hasPos() {
    return pos_ != null;
  }
  /**
   * <pre>
   *参考点位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  public road.data.proto.Position3D getPos() {
    return pos_ == null ? road.data.proto.Position3D.getDefaultInstance() : pos_;
  }
  /**
   * <pre>
   *参考点位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  public road.data.proto.Position3DOrBuilder getPosOrBuilder() {
    return getPos();
  }

  public static final int COORDINATES_FIELD_NUMBER = 5;
  private road.data.proto.VehicleCoordination coordinates_;
  /**
   * <pre>
   *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
   */
  public boolean hasCoordinates() {
    return coordinates_ != null;
  }
  /**
   * <pre>
   *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
   */
  public road.data.proto.VehicleCoordination getCoordinates() {
    return coordinates_ == null ? road.data.proto.VehicleCoordination.getDefaultInstance() : coordinates_;
  }
  /**
   * <pre>
   *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
   */
  public road.data.proto.VehicleCoordinationOrBuilder getCoordinatesOrBuilder() {
    return getCoordinates();
  }

  public static final int LANECOORDINATES_FIELD_NUMBER = 6;
  private road.data.proto.LaneCoordination laneCoordinates_;
  /**
   * <pre>
   *可选，对道路或车道的引导信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
   */
  public boolean hasLaneCoordinates() {
    return laneCoordinates_ != null;
  }
  /**
   * <pre>
   *可选，对道路或车道的引导信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
   */
  public road.data.proto.LaneCoordination getLaneCoordinates() {
    return laneCoordinates_ == null ? road.data.proto.LaneCoordination.getDefaultInstance() : laneCoordinates_;
  }
  /**
   * <pre>
   *可选，对道路或车道的引导信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
   */
  public road.data.proto.LaneCoordinationOrBuilder getLaneCoordinatesOrBuilder() {
    return getLaneCoordinates();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (msgCnt_ != 0) {
      output.writeUInt32(1, msgCnt_);
    }
    if (!getRsuIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, rsuId_);
    }
    if (timestamp_ != 0L) {
      output.writeUInt64(3, timestamp_);
    }
    if (pos_ != null) {
      output.writeMessage(4, getPos());
    }
    if (coordinates_ != null) {
      output.writeMessage(5, getCoordinates());
    }
    if (laneCoordinates_ != null) {
      output.writeMessage(6, getLaneCoordinates());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (msgCnt_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, msgCnt_);
    }
    if (!getRsuIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, rsuId_);
    }
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, timestamp_);
    }
    if (pos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getPos());
    }
    if (coordinates_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getCoordinates());
    }
    if (laneCoordinates_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getLaneCoordinates());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.RscData)) {
      return super.equals(obj);
    }
    road.data.proto.RscData other = (road.data.proto.RscData) obj;

    if (getMsgCnt()
        != other.getMsgCnt()) return false;
    if (!getRsuId()
        .equals(other.getRsuId())) return false;
    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (hasPos() != other.hasPos()) return false;
    if (hasPos()) {
      if (!getPos()
          .equals(other.getPos())) return false;
    }
    if (hasCoordinates() != other.hasCoordinates()) return false;
    if (hasCoordinates()) {
      if (!getCoordinates()
          .equals(other.getCoordinates())) return false;
    }
    if (hasLaneCoordinates() != other.hasLaneCoordinates()) return false;
    if (hasLaneCoordinates()) {
      if (!getLaneCoordinates()
          .equals(other.getLaneCoordinates())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + MSGCNT_FIELD_NUMBER;
    hash = (53 * hash) + getMsgCnt();
    hash = (37 * hash) + RSUID_FIELD_NUMBER;
    hash = (53 * hash) + getRsuId().hashCode();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    if (hasPos()) {
      hash = (37 * hash) + POS_FIELD_NUMBER;
      hash = (53 * hash) + getPos().hashCode();
    }
    if (hasCoordinates()) {
      hash = (37 * hash) + COORDINATES_FIELD_NUMBER;
      hash = (53 * hash) + getCoordinates().hashCode();
    }
    if (hasLaneCoordinates()) {
      hash = (37 * hash) + LANECOORDINATES_FIELD_NUMBER;
      hash = (53 * hash) + getLaneCoordinates().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.RscData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RscData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RscData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RscData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RscData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RscData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RscData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RscData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RscData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.RscData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RscData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RscData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.RscData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *车辆协作或引导RscData   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.RscData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.RscData)
      road.data.proto.RscDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RscData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RscData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.RscData.class, road.data.proto.RscData.Builder.class);
    }

    // Construct using road.data.proto.RscData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      msgCnt_ = 0;

      rsuId_ = "";

      timestamp_ = 0L;

      if (posBuilder_ == null) {
        pos_ = null;
      } else {
        pos_ = null;
        posBuilder_ = null;
      }
      if (coordinatesBuilder_ == null) {
        coordinates_ = null;
      } else {
        coordinates_ = null;
        coordinatesBuilder_ = null;
      }
      if (laneCoordinatesBuilder_ == null) {
        laneCoordinates_ = null;
      } else {
        laneCoordinates_ = null;
        laneCoordinatesBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RscData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.RscData getDefaultInstanceForType() {
      return road.data.proto.RscData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.RscData build() {
      road.data.proto.RscData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.RscData buildPartial() {
      road.data.proto.RscData result = new road.data.proto.RscData(this);
      result.msgCnt_ = msgCnt_;
      result.rsuId_ = rsuId_;
      result.timestamp_ = timestamp_;
      if (posBuilder_ == null) {
        result.pos_ = pos_;
      } else {
        result.pos_ = posBuilder_.build();
      }
      if (coordinatesBuilder_ == null) {
        result.coordinates_ = coordinates_;
      } else {
        result.coordinates_ = coordinatesBuilder_.build();
      }
      if (laneCoordinatesBuilder_ == null) {
        result.laneCoordinates_ = laneCoordinates_;
      } else {
        result.laneCoordinates_ = laneCoordinatesBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.RscData) {
        return mergeFrom((road.data.proto.RscData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.RscData other) {
      if (other == road.data.proto.RscData.getDefaultInstance()) return this;
      if (other.getMsgCnt() != 0) {
        setMsgCnt(other.getMsgCnt());
      }
      if (!other.getRsuId().isEmpty()) {
        rsuId_ = other.rsuId_;
        onChanged();
      }
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (other.hasPos()) {
        mergePos(other.getPos());
      }
      if (other.hasCoordinates()) {
        mergeCoordinates(other.getCoordinates());
      }
      if (other.hasLaneCoordinates()) {
        mergeLaneCoordinates(other.getLaneCoordinates());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.RscData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.RscData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int msgCnt_ ;
    /**
     * <pre>
     *定义消息编号。
     * </pre>
     *
     * <code>uint32 msgCnt = 1;</code>
     */
    public int getMsgCnt() {
      return msgCnt_;
    }
    /**
     * <pre>
     *定义消息编号。
     * </pre>
     *
     * <code>uint32 msgCnt = 1;</code>
     */
    public Builder setMsgCnt(int value) {
      
      msgCnt_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *定义消息编号。
     * </pre>
     *
     * <code>uint32 msgCnt = 1;</code>
     */
    public Builder clearMsgCnt() {
      
      msgCnt_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object rsuId_ = "";
    /**
     * <pre>
     *RSU id
     * </pre>
     *
     * <code>string rsuId = 2;</code>
     */
    public java.lang.String getRsuId() {
      java.lang.Object ref = rsuId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        rsuId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *RSU id
     * </pre>
     *
     * <code>string rsuId = 2;</code>
     */
    public com.google.protobuf.ByteString
        getRsuIdBytes() {
      java.lang.Object ref = rsuId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rsuId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *RSU id
     * </pre>
     *
     * <code>string rsuId = 2;</code>
     */
    public Builder setRsuId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      rsuId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *RSU id
     * </pre>
     *
     * <code>string rsuId = 2;</code>
     */
    public Builder clearRsuId() {
      
      rsuId_ = getDefaultInstance().getRsuId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *RSU id
     * </pre>
     *
     * <code>string rsuId = 2;</code>
     */
    public Builder setRsuIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      rsuId_ = value;
      onChanged();
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private road.data.proto.Position3D pos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> posBuilder_;
    /**
     * <pre>
     *参考点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public boolean hasPos() {
      return posBuilder_ != null || pos_ != null;
    }
    /**
     * <pre>
     *参考点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public road.data.proto.Position3D getPos() {
      if (posBuilder_ == null) {
        return pos_ == null ? road.data.proto.Position3D.getDefaultInstance() : pos_;
      } else {
        return posBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *参考点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public Builder setPos(road.data.proto.Position3D value) {
      if (posBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        pos_ = value;
        onChanged();
      } else {
        posBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *参考点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public Builder setPos(
        road.data.proto.Position3D.Builder builderForValue) {
      if (posBuilder_ == null) {
        pos_ = builderForValue.build();
        onChanged();
      } else {
        posBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *参考点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public Builder mergePos(road.data.proto.Position3D value) {
      if (posBuilder_ == null) {
        if (pos_ != null) {
          pos_ =
            road.data.proto.Position3D.newBuilder(pos_).mergeFrom(value).buildPartial();
        } else {
          pos_ = value;
        }
        onChanged();
      } else {
        posBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *参考点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public Builder clearPos() {
      if (posBuilder_ == null) {
        pos_ = null;
        onChanged();
      } else {
        pos_ = null;
        posBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *参考点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public road.data.proto.Position3D.Builder getPosBuilder() {
      
      onChanged();
      return getPosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *参考点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public road.data.proto.Position3DOrBuilder getPosOrBuilder() {
      if (posBuilder_ != null) {
        return posBuilder_.getMessageOrBuilder();
      } else {
        return pos_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : pos_;
      }
    }
    /**
     * <pre>
     *参考点位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getPosFieldBuilder() {
      if (posBuilder_ == null) {
        posBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getPos(),
                getParentForChildren(),
                isClean());
        pos_ = null;
      }
      return posBuilder_;
    }

    private road.data.proto.VehicleCoordination coordinates_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.VehicleCoordination, road.data.proto.VehicleCoordination.Builder, road.data.proto.VehicleCoordinationOrBuilder> coordinatesBuilder_;
    /**
     * <pre>
     *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
     */
    public boolean hasCoordinates() {
      return coordinatesBuilder_ != null || coordinates_ != null;
    }
    /**
     * <pre>
     *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
     */
    public road.data.proto.VehicleCoordination getCoordinates() {
      if (coordinatesBuilder_ == null) {
        return coordinates_ == null ? road.data.proto.VehicleCoordination.getDefaultInstance() : coordinates_;
      } else {
        return coordinatesBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
     */
    public Builder setCoordinates(road.data.proto.VehicleCoordination value) {
      if (coordinatesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        coordinates_ = value;
        onChanged();
      } else {
        coordinatesBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
     */
    public Builder setCoordinates(
        road.data.proto.VehicleCoordination.Builder builderForValue) {
      if (coordinatesBuilder_ == null) {
        coordinates_ = builderForValue.build();
        onChanged();
      } else {
        coordinatesBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
     */
    public Builder mergeCoordinates(road.data.proto.VehicleCoordination value) {
      if (coordinatesBuilder_ == null) {
        if (coordinates_ != null) {
          coordinates_ =
            road.data.proto.VehicleCoordination.newBuilder(coordinates_).mergeFrom(value).buildPartial();
        } else {
          coordinates_ = value;
        }
        onChanged();
      } else {
        coordinatesBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
     */
    public Builder clearCoordinates() {
      if (coordinatesBuilder_ == null) {
        coordinates_ = null;
        onChanged();
      } else {
        coordinates_ = null;
        coordinatesBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
     */
    public road.data.proto.VehicleCoordination.Builder getCoordinatesBuilder() {
      
      onChanged();
      return getCoordinatesFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
     */
    public road.data.proto.VehicleCoordinationOrBuilder getCoordinatesOrBuilder() {
      if (coordinatesBuilder_ != null) {
        return coordinatesBuilder_.getMessageOrBuilder();
      } else {
        return coordinates_ == null ?
            road.data.proto.VehicleCoordination.getDefaultInstance() : coordinates_;
      }
    }
    /**
     * <pre>
     *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.VehicleCoordination, road.data.proto.VehicleCoordination.Builder, road.data.proto.VehicleCoordinationOrBuilder> 
        getCoordinatesFieldBuilder() {
      if (coordinatesBuilder_ == null) {
        coordinatesBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.VehicleCoordination, road.data.proto.VehicleCoordination.Builder, road.data.proto.VehicleCoordinationOrBuilder>(
                getCoordinates(),
                getParentForChildren(),
                isClean());
        coordinates_ = null;
      }
      return coordinatesBuilder_;
    }

    private road.data.proto.LaneCoordination laneCoordinates_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneCoordination, road.data.proto.LaneCoordination.Builder, road.data.proto.LaneCoordinationOrBuilder> laneCoordinatesBuilder_;
    /**
     * <pre>
     *可选，对道路或车道的引导信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
     */
    public boolean hasLaneCoordinates() {
      return laneCoordinatesBuilder_ != null || laneCoordinates_ != null;
    }
    /**
     * <pre>
     *可选，对道路或车道的引导信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
     */
    public road.data.proto.LaneCoordination getLaneCoordinates() {
      if (laneCoordinatesBuilder_ == null) {
        return laneCoordinates_ == null ? road.data.proto.LaneCoordination.getDefaultInstance() : laneCoordinates_;
      } else {
        return laneCoordinatesBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，对道路或车道的引导信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
     */
    public Builder setLaneCoordinates(road.data.proto.LaneCoordination value) {
      if (laneCoordinatesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        laneCoordinates_ = value;
        onChanged();
      } else {
        laneCoordinatesBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，对道路或车道的引导信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
     */
    public Builder setLaneCoordinates(
        road.data.proto.LaneCoordination.Builder builderForValue) {
      if (laneCoordinatesBuilder_ == null) {
        laneCoordinates_ = builderForValue.build();
        onChanged();
      } else {
        laneCoordinatesBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，对道路或车道的引导信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
     */
    public Builder mergeLaneCoordinates(road.data.proto.LaneCoordination value) {
      if (laneCoordinatesBuilder_ == null) {
        if (laneCoordinates_ != null) {
          laneCoordinates_ =
            road.data.proto.LaneCoordination.newBuilder(laneCoordinates_).mergeFrom(value).buildPartial();
        } else {
          laneCoordinates_ = value;
        }
        onChanged();
      } else {
        laneCoordinatesBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，对道路或车道的引导信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
     */
    public Builder clearLaneCoordinates() {
      if (laneCoordinatesBuilder_ == null) {
        laneCoordinates_ = null;
        onChanged();
      } else {
        laneCoordinates_ = null;
        laneCoordinatesBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，对道路或车道的引导信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
     */
    public road.data.proto.LaneCoordination.Builder getLaneCoordinatesBuilder() {
      
      onChanged();
      return getLaneCoordinatesFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，对道路或车道的引导信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
     */
    public road.data.proto.LaneCoordinationOrBuilder getLaneCoordinatesOrBuilder() {
      if (laneCoordinatesBuilder_ != null) {
        return laneCoordinatesBuilder_.getMessageOrBuilder();
      } else {
        return laneCoordinates_ == null ?
            road.data.proto.LaneCoordination.getDefaultInstance() : laneCoordinates_;
      }
    }
    /**
     * <pre>
     *可选，对道路或车道的引导信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneCoordination, road.data.proto.LaneCoordination.Builder, road.data.proto.LaneCoordinationOrBuilder> 
        getLaneCoordinatesFieldBuilder() {
      if (laneCoordinatesBuilder_ == null) {
        laneCoordinatesBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneCoordination, road.data.proto.LaneCoordination.Builder, road.data.proto.LaneCoordinationOrBuilder>(
                getLaneCoordinates(),
                getParentForChildren(),
                isClean());
        laneCoordinates_ = null;
      }
      return laneCoordinatesBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.RscData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.RscData)
  private static final road.data.proto.RscData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.RscData();
  }

  public static road.data.proto.RscData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RscData>
      PARSER = new com.google.protobuf.AbstractParser<RscData>() {
    @java.lang.Override
    public RscData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RscData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RscData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RscData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.RscData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

