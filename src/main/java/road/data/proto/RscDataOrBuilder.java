// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface RscDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.RscData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *定义消息编号。
   * </pre>
   *
   * <code>uint32 msgCnt = 1;</code>
   */
  int getMsgCnt();

  /**
   * <pre>
   *RSU id
   * </pre>
   *
   * <code>string rsuId = 2;</code>
   */
  java.lang.String getRsuId();
  /**
   * <pre>
   *RSU id
   * </pre>
   *
   * <code>string rsuId = 2;</code>
   */
  com.google.protobuf.ByteString
      getRsuIdBytes();

  /**
   * <pre>
   *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 3;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   *参考点位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  boolean hasPos();
  /**
   * <pre>
   *参考点位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  road.data.proto.Position3D getPos();
  /**
   * <pre>
   *参考点位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  road.data.proto.Position3DOrBuilder getPosOrBuilder();

  /**
   * <pre>
   *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
   */
  boolean hasCoordinates();
  /**
   * <pre>
   *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
   */
  road.data.proto.VehicleCoordination getCoordinates();
  /**
   * <pre>
   *可选，定义RSU对某单一车辆的协调规划信息。 包括车辆的临时标识ID，以及RSU提供的驾驶建议和路径规划等信息。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleCoordination coordinates = 5;</code>
   */
  road.data.proto.VehicleCoordinationOrBuilder getCoordinatesOrBuilder();

  /**
   * <pre>
   *可选，对道路或车道的引导信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
   */
  boolean hasLaneCoordinates();
  /**
   * <pre>
   *可选，对道路或车道的引导信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
   */
  road.data.proto.LaneCoordination getLaneCoordinates();
  /**
   * <pre>
   *可选，对道路或车道的引导信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneCoordination laneCoordinates = 6;</code>
   */
  road.data.proto.LaneCoordinationOrBuilder getLaneCoordinatesOrBuilder();
}
