// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *（MEC侧、RSU侧发送）云端回执消息 RsiReply，具体字段意思见协议*******、*******
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.RsiReply}
 */
public  final class RsiReply extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.RsiReply)
    RsiReplyOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RsiReply.newBuilder() to construct.
  private RsiReply(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RsiReply() {
    sourceDeviceId_ = "";
    targetDeviceId_ = "";
    creatTime_ = "";
    distributionTime_ = "";
    completionTime_ = "";
    updateTime_ = "";
    description_ = "";
    sourceTopic_ = "";
    targetTopic_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RsiReply();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RsiReply(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            id_ = input.readUInt64();
            break;
          }
          case 16: {

            eventType_ = input.readUInt32();
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            sourceDeviceId_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            targetDeviceId_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            creatTime_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            distributionTime_ = s;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            completionTime_ = s;
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            updateTime_ = s;
            break;
          }
          case 72: {

            operationType_ = input.readUInt32();
            break;
          }
          case 80: {

            camDataId_ = input.readUInt64();
            break;
          }
          case 88: {

            dataId_ = input.readUInt64();
            break;
          }
          case 96: {

            eventSourceId_ = input.readUInt64();
            break;
          }
          case 104: {

            distributionStatusId_ = input.readUInt32();
            break;
          }
          case 114: {
            java.lang.String s = input.readStringRequireUtf8();

            description_ = s;
            break;
          }
          case 122: {
            java.lang.String s = input.readStringRequireUtf8();

            sourceTopic_ = s;
            break;
          }
          case 130: {
            java.lang.String s = input.readStringRequireUtf8();

            targetTopic_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsiReply_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsiReply_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.RsiReply.class, road.data.proto.RsiReply.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  private long id_;
  /**
   * <code>uint64 id = 1;</code>
   */
  public long getId() {
    return id_;
  }

  public static final int EVENTTYPE_FIELD_NUMBER = 2;
  private int eventType_;
  /**
   * <pre>
   *事件类型 0-rte,1-rts
   * </pre>
   *
   * <code>uint32 eventType = 2;</code>
   */
  public int getEventType() {
    return eventType_;
  }

  public static final int SOURCEDEVICEID_FIELD_NUMBER = 3;
  private volatile java.lang.Object sourceDeviceId_;
  /**
   * <pre>
   *来源设备编码
   * </pre>
   *
   * <code>string sourceDeviceId = 3;</code>
   */
  public java.lang.String getSourceDeviceId() {
    java.lang.Object ref = sourceDeviceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      sourceDeviceId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *来源设备编码
   * </pre>
   *
   * <code>string sourceDeviceId = 3;</code>
   */
  public com.google.protobuf.ByteString
      getSourceDeviceIdBytes() {
    java.lang.Object ref = sourceDeviceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      sourceDeviceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TARGETDEVICEID_FIELD_NUMBER = 4;
  private volatile java.lang.Object targetDeviceId_;
  /**
   * <pre>
   *目标设备编码
   * </pre>
   *
   * <code>string targetDeviceId = 4;</code>
   */
  public java.lang.String getTargetDeviceId() {
    java.lang.Object ref = targetDeviceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      targetDeviceId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *目标设备编码
   * </pre>
   *
   * <code>string targetDeviceId = 4;</code>
   */
  public com.google.protobuf.ByteString
      getTargetDeviceIdBytes() {
    java.lang.Object ref = targetDeviceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      targetDeviceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CREATTIME_FIELD_NUMBER = 5;
  private volatile java.lang.Object creatTime_;
  /**
   * <pre>
   *创建事件
   * </pre>
   *
   * <code>string creatTime = 5;</code>
   */
  public java.lang.String getCreatTime() {
    java.lang.Object ref = creatTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      creatTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *创建事件
   * </pre>
   *
   * <code>string creatTime = 5;</code>
   */
  public com.google.protobuf.ByteString
      getCreatTimeBytes() {
    java.lang.Object ref = creatTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      creatTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DISTRIBUTIONTIME_FIELD_NUMBER = 6;
  private volatile java.lang.Object distributionTime_;
  /**
   * <pre>
   *下发时间
   * </pre>
   *
   * <code>string distributionTime = 6;</code>
   */
  public java.lang.String getDistributionTime() {
    java.lang.Object ref = distributionTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      distributionTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *下发时间
   * </pre>
   *
   * <code>string distributionTime = 6;</code>
   */
  public com.google.protobuf.ByteString
      getDistributionTimeBytes() {
    java.lang.Object ref = distributionTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      distributionTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int COMPLETIONTIME_FIELD_NUMBER = 7;
  private volatile java.lang.Object completionTime_;
  /**
   * <pre>
   *完成时间
   * </pre>
   *
   * <code>string completionTime = 7;</code>
   */
  public java.lang.String getCompletionTime() {
    java.lang.Object ref = completionTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      completionTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *完成时间
   * </pre>
   *
   * <code>string completionTime = 7;</code>
   */
  public com.google.protobuf.ByteString
      getCompletionTimeBytes() {
    java.lang.Object ref = completionTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      completionTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int UPDATETIME_FIELD_NUMBER = 8;
  private volatile java.lang.Object updateTime_;
  /**
   * <pre>
   *更新时间
   * </pre>
   *
   * <code>string updateTime = 8;</code>
   */
  public java.lang.String getUpdateTime() {
    java.lang.Object ref = updateTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      updateTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *更新时间
   * </pre>
   *
   * <code>string updateTime = 8;</code>
   */
  public com.google.protobuf.ByteString
      getUpdateTimeBytes() {
    java.lang.Object ref = updateTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      updateTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int OPERATIONTYPE_FIELD_NUMBER = 9;
  private int operationType_;
  /**
   * <pre>
   *操作类型 0-自动 1-手工,2-未知
   * </pre>
   *
   * <code>uint32 operationType = 9;</code>
   */
  public int getOperationType() {
    return operationType_;
  }

  public static final int CAMDATAID_FIELD_NUMBER = 10;
  private long camDataId_;
  /**
   * <pre>
   *cam外键
   * </pre>
   *
   * <code>uint64 camDataId = 10;</code>
   */
  public long getCamDataId() {
    return camDataId_;
  }

  public static final int DATAID_FIELD_NUMBER = 11;
  private long dataId_;
  /**
   * <pre>
   *rte/rts外键
   * </pre>
   *
   * <code>uint64 dataId = 11;</code>
   */
  public long getDataId() {
    return dataId_;
  }

  public static final int EVENTSOURCEID_FIELD_NUMBER = 12;
  private long eventSourceId_;
  /**
   * <pre>
   *事件来源
   * </pre>
   *
   * <code>uint64 eventSourceId = 12;</code>
   */
  public long getEventSourceId() {
    return eventSourceId_;
  }

  public static final int DISTRIBUTIONSTATUSID_FIELD_NUMBER = 13;
  private int distributionStatusId_;
  /**
   * <pre>
   *下发状态，0-未知，1-下发中，2-已下发(针对rsu回执，ans编码成功)，3-下发失败,4-asn编码失败
   * </pre>
   *
   * <code>uint32 distributionStatusId = 13;</code>
   */
  public int getDistributionStatusId() {
    return distributionStatusId_;
  }

  public static final int DESCRIPTION_FIELD_NUMBER = 14;
  private volatile java.lang.Object description_;
  /**
   * <pre>
   *可选，mec侧的回执不需要，rsu广播rsi失败时，需要增加失败原因描述
   * </pre>
   *
   * <code>string description = 14;</code>
   */
  public java.lang.String getDescription() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      description_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，mec侧的回执不需要，rsu广播rsi失败时，需要增加失败原因描述
   * </pre>
   *
   * <code>string description = 14;</code>
   */
  public com.google.protobuf.ByteString
      getDescriptionBytes() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      description_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SOURCETOPIC_FIELD_NUMBER = 15;
  private volatile java.lang.Object sourceTopic_;
  /**
   * <pre>
   *转发状态的源topic编码
   * </pre>
   *
   * <code>string sourceTopic = 15;</code>
   */
  public java.lang.String getSourceTopic() {
    java.lang.Object ref = sourceTopic_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      sourceTopic_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *转发状态的源topic编码
   * </pre>
   *
   * <code>string sourceTopic = 15;</code>
   */
  public com.google.protobuf.ByteString
      getSourceTopicBytes() {
    java.lang.Object ref = sourceTopic_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      sourceTopic_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TARGETTOPIC_FIELD_NUMBER = 16;
  private volatile java.lang.Object targetTopic_;
  /**
   * <pre>
   * 转发状态的目标topic编码
   * </pre>
   *
   * <code>string targetTopic = 16;</code>
   */
  public java.lang.String getTargetTopic() {
    java.lang.Object ref = targetTopic_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      targetTopic_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 转发状态的目标topic编码
   * </pre>
   *
   * <code>string targetTopic = 16;</code>
   */
  public com.google.protobuf.ByteString
      getTargetTopicBytes() {
    java.lang.Object ref = targetTopic_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      targetTopic_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (id_ != 0L) {
      output.writeUInt64(1, id_);
    }
    if (eventType_ != 0) {
      output.writeUInt32(2, eventType_);
    }
    if (!getSourceDeviceIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, sourceDeviceId_);
    }
    if (!getTargetDeviceIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, targetDeviceId_);
    }
    if (!getCreatTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, creatTime_);
    }
    if (!getDistributionTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, distributionTime_);
    }
    if (!getCompletionTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, completionTime_);
    }
    if (!getUpdateTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, updateTime_);
    }
    if (operationType_ != 0) {
      output.writeUInt32(9, operationType_);
    }
    if (camDataId_ != 0L) {
      output.writeUInt64(10, camDataId_);
    }
    if (dataId_ != 0L) {
      output.writeUInt64(11, dataId_);
    }
    if (eventSourceId_ != 0L) {
      output.writeUInt64(12, eventSourceId_);
    }
    if (distributionStatusId_ != 0) {
      output.writeUInt32(13, distributionStatusId_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 14, description_);
    }
    if (!getSourceTopicBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 15, sourceTopic_);
    }
    if (!getTargetTopicBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 16, targetTopic_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (id_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, id_);
    }
    if (eventType_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, eventType_);
    }
    if (!getSourceDeviceIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, sourceDeviceId_);
    }
    if (!getTargetDeviceIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, targetDeviceId_);
    }
    if (!getCreatTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, creatTime_);
    }
    if (!getDistributionTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, distributionTime_);
    }
    if (!getCompletionTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, completionTime_);
    }
    if (!getUpdateTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, updateTime_);
    }
    if (operationType_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(9, operationType_);
    }
    if (camDataId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(10, camDataId_);
    }
    if (dataId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(11, dataId_);
    }
    if (eventSourceId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(12, eventSourceId_);
    }
    if (distributionStatusId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(13, distributionStatusId_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, description_);
    }
    if (!getSourceTopicBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, sourceTopic_);
    }
    if (!getTargetTopicBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, targetTopic_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.RsiReply)) {
      return super.equals(obj);
    }
    road.data.proto.RsiReply other = (road.data.proto.RsiReply) obj;

    if (getId()
        != other.getId()) return false;
    if (getEventType()
        != other.getEventType()) return false;
    if (!getSourceDeviceId()
        .equals(other.getSourceDeviceId())) return false;
    if (!getTargetDeviceId()
        .equals(other.getTargetDeviceId())) return false;
    if (!getCreatTime()
        .equals(other.getCreatTime())) return false;
    if (!getDistributionTime()
        .equals(other.getDistributionTime())) return false;
    if (!getCompletionTime()
        .equals(other.getCompletionTime())) return false;
    if (!getUpdateTime()
        .equals(other.getUpdateTime())) return false;
    if (getOperationType()
        != other.getOperationType()) return false;
    if (getCamDataId()
        != other.getCamDataId()) return false;
    if (getDataId()
        != other.getDataId()) return false;
    if (getEventSourceId()
        != other.getEventSourceId()) return false;
    if (getDistributionStatusId()
        != other.getDistributionStatusId()) return false;
    if (!getDescription()
        .equals(other.getDescription())) return false;
    if (!getSourceTopic()
        .equals(other.getSourceTopic())) return false;
    if (!getTargetTopic()
        .equals(other.getTargetTopic())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getId());
    hash = (37 * hash) + EVENTTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getEventType();
    hash = (37 * hash) + SOURCEDEVICEID_FIELD_NUMBER;
    hash = (53 * hash) + getSourceDeviceId().hashCode();
    hash = (37 * hash) + TARGETDEVICEID_FIELD_NUMBER;
    hash = (53 * hash) + getTargetDeviceId().hashCode();
    hash = (37 * hash) + CREATTIME_FIELD_NUMBER;
    hash = (53 * hash) + getCreatTime().hashCode();
    hash = (37 * hash) + DISTRIBUTIONTIME_FIELD_NUMBER;
    hash = (53 * hash) + getDistributionTime().hashCode();
    hash = (37 * hash) + COMPLETIONTIME_FIELD_NUMBER;
    hash = (53 * hash) + getCompletionTime().hashCode();
    hash = (37 * hash) + UPDATETIME_FIELD_NUMBER;
    hash = (53 * hash) + getUpdateTime().hashCode();
    hash = (37 * hash) + OPERATIONTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getOperationType();
    hash = (37 * hash) + CAMDATAID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getCamDataId());
    hash = (37 * hash) + DATAID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getDataId());
    hash = (37 * hash) + EVENTSOURCEID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getEventSourceId());
    hash = (37 * hash) + DISTRIBUTIONSTATUSID_FIELD_NUMBER;
    hash = (53 * hash) + getDistributionStatusId();
    hash = (37 * hash) + DESCRIPTION_FIELD_NUMBER;
    hash = (53 * hash) + getDescription().hashCode();
    hash = (37 * hash) + SOURCETOPIC_FIELD_NUMBER;
    hash = (53 * hash) + getSourceTopic().hashCode();
    hash = (37 * hash) + TARGETTOPIC_FIELD_NUMBER;
    hash = (53 * hash) + getTargetTopic().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.RsiReply parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RsiReply parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RsiReply parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RsiReply parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RsiReply parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RsiReply parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RsiReply parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RsiReply parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RsiReply parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.RsiReply parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RsiReply parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RsiReply parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.RsiReply prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *（MEC侧、RSU侧发送）云端回执消息 RsiReply，具体字段意思见协议*******、*******
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.RsiReply}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.RsiReply)
      road.data.proto.RsiReplyOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsiReply_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsiReply_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.RsiReply.class, road.data.proto.RsiReply.Builder.class);
    }

    // Construct using road.data.proto.RsiReply.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      id_ = 0L;

      eventType_ = 0;

      sourceDeviceId_ = "";

      targetDeviceId_ = "";

      creatTime_ = "";

      distributionTime_ = "";

      completionTime_ = "";

      updateTime_ = "";

      operationType_ = 0;

      camDataId_ = 0L;

      dataId_ = 0L;

      eventSourceId_ = 0L;

      distributionStatusId_ = 0;

      description_ = "";

      sourceTopic_ = "";

      targetTopic_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsiReply_descriptor;
    }

    @java.lang.Override
    public road.data.proto.RsiReply getDefaultInstanceForType() {
      return road.data.proto.RsiReply.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.RsiReply build() {
      road.data.proto.RsiReply result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.RsiReply buildPartial() {
      road.data.proto.RsiReply result = new road.data.proto.RsiReply(this);
      result.id_ = id_;
      result.eventType_ = eventType_;
      result.sourceDeviceId_ = sourceDeviceId_;
      result.targetDeviceId_ = targetDeviceId_;
      result.creatTime_ = creatTime_;
      result.distributionTime_ = distributionTime_;
      result.completionTime_ = completionTime_;
      result.updateTime_ = updateTime_;
      result.operationType_ = operationType_;
      result.camDataId_ = camDataId_;
      result.dataId_ = dataId_;
      result.eventSourceId_ = eventSourceId_;
      result.distributionStatusId_ = distributionStatusId_;
      result.description_ = description_;
      result.sourceTopic_ = sourceTopic_;
      result.targetTopic_ = targetTopic_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.RsiReply) {
        return mergeFrom((road.data.proto.RsiReply)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.RsiReply other) {
      if (other == road.data.proto.RsiReply.getDefaultInstance()) return this;
      if (other.getId() != 0L) {
        setId(other.getId());
      }
      if (other.getEventType() != 0) {
        setEventType(other.getEventType());
      }
      if (!other.getSourceDeviceId().isEmpty()) {
        sourceDeviceId_ = other.sourceDeviceId_;
        onChanged();
      }
      if (!other.getTargetDeviceId().isEmpty()) {
        targetDeviceId_ = other.targetDeviceId_;
        onChanged();
      }
      if (!other.getCreatTime().isEmpty()) {
        creatTime_ = other.creatTime_;
        onChanged();
      }
      if (!other.getDistributionTime().isEmpty()) {
        distributionTime_ = other.distributionTime_;
        onChanged();
      }
      if (!other.getCompletionTime().isEmpty()) {
        completionTime_ = other.completionTime_;
        onChanged();
      }
      if (!other.getUpdateTime().isEmpty()) {
        updateTime_ = other.updateTime_;
        onChanged();
      }
      if (other.getOperationType() != 0) {
        setOperationType(other.getOperationType());
      }
      if (other.getCamDataId() != 0L) {
        setCamDataId(other.getCamDataId());
      }
      if (other.getDataId() != 0L) {
        setDataId(other.getDataId());
      }
      if (other.getEventSourceId() != 0L) {
        setEventSourceId(other.getEventSourceId());
      }
      if (other.getDistributionStatusId() != 0) {
        setDistributionStatusId(other.getDistributionStatusId());
      }
      if (!other.getDescription().isEmpty()) {
        description_ = other.description_;
        onChanged();
      }
      if (!other.getSourceTopic().isEmpty()) {
        sourceTopic_ = other.sourceTopic_;
        onChanged();
      }
      if (!other.getTargetTopic().isEmpty()) {
        targetTopic_ = other.targetTopic_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.RsiReply parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.RsiReply) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long id_ ;
    /**
     * <code>uint64 id = 1;</code>
     */
    public long getId() {
      return id_;
    }
    /**
     * <code>uint64 id = 1;</code>
     */
    public Builder setId(long value) {
      
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>uint64 id = 1;</code>
     */
    public Builder clearId() {
      
      id_ = 0L;
      onChanged();
      return this;
    }

    private int eventType_ ;
    /**
     * <pre>
     *事件类型 0-rte,1-rts
     * </pre>
     *
     * <code>uint32 eventType = 2;</code>
     */
    public int getEventType() {
      return eventType_;
    }
    /**
     * <pre>
     *事件类型 0-rte,1-rts
     * </pre>
     *
     * <code>uint32 eventType = 2;</code>
     */
    public Builder setEventType(int value) {
      
      eventType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *事件类型 0-rte,1-rts
     * </pre>
     *
     * <code>uint32 eventType = 2;</code>
     */
    public Builder clearEventType() {
      
      eventType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object sourceDeviceId_ = "";
    /**
     * <pre>
     *来源设备编码
     * </pre>
     *
     * <code>string sourceDeviceId = 3;</code>
     */
    public java.lang.String getSourceDeviceId() {
      java.lang.Object ref = sourceDeviceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sourceDeviceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *来源设备编码
     * </pre>
     *
     * <code>string sourceDeviceId = 3;</code>
     */
    public com.google.protobuf.ByteString
        getSourceDeviceIdBytes() {
      java.lang.Object ref = sourceDeviceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sourceDeviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *来源设备编码
     * </pre>
     *
     * <code>string sourceDeviceId = 3;</code>
     */
    public Builder setSourceDeviceId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      sourceDeviceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *来源设备编码
     * </pre>
     *
     * <code>string sourceDeviceId = 3;</code>
     */
    public Builder clearSourceDeviceId() {
      
      sourceDeviceId_ = getDefaultInstance().getSourceDeviceId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *来源设备编码
     * </pre>
     *
     * <code>string sourceDeviceId = 3;</code>
     */
    public Builder setSourceDeviceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      sourceDeviceId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object targetDeviceId_ = "";
    /**
     * <pre>
     *目标设备编码
     * </pre>
     *
     * <code>string targetDeviceId = 4;</code>
     */
    public java.lang.String getTargetDeviceId() {
      java.lang.Object ref = targetDeviceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        targetDeviceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *目标设备编码
     * </pre>
     *
     * <code>string targetDeviceId = 4;</code>
     */
    public com.google.protobuf.ByteString
        getTargetDeviceIdBytes() {
      java.lang.Object ref = targetDeviceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        targetDeviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *目标设备编码
     * </pre>
     *
     * <code>string targetDeviceId = 4;</code>
     */
    public Builder setTargetDeviceId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      targetDeviceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *目标设备编码
     * </pre>
     *
     * <code>string targetDeviceId = 4;</code>
     */
    public Builder clearTargetDeviceId() {
      
      targetDeviceId_ = getDefaultInstance().getTargetDeviceId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *目标设备编码
     * </pre>
     *
     * <code>string targetDeviceId = 4;</code>
     */
    public Builder setTargetDeviceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      targetDeviceId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object creatTime_ = "";
    /**
     * <pre>
     *创建事件
     * </pre>
     *
     * <code>string creatTime = 5;</code>
     */
    public java.lang.String getCreatTime() {
      java.lang.Object ref = creatTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        creatTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *创建事件
     * </pre>
     *
     * <code>string creatTime = 5;</code>
     */
    public com.google.protobuf.ByteString
        getCreatTimeBytes() {
      java.lang.Object ref = creatTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        creatTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *创建事件
     * </pre>
     *
     * <code>string creatTime = 5;</code>
     */
    public Builder setCreatTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      creatTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *创建事件
     * </pre>
     *
     * <code>string creatTime = 5;</code>
     */
    public Builder clearCreatTime() {
      
      creatTime_ = getDefaultInstance().getCreatTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *创建事件
     * </pre>
     *
     * <code>string creatTime = 5;</code>
     */
    public Builder setCreatTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      creatTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object distributionTime_ = "";
    /**
     * <pre>
     *下发时间
     * </pre>
     *
     * <code>string distributionTime = 6;</code>
     */
    public java.lang.String getDistributionTime() {
      java.lang.Object ref = distributionTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        distributionTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *下发时间
     * </pre>
     *
     * <code>string distributionTime = 6;</code>
     */
    public com.google.protobuf.ByteString
        getDistributionTimeBytes() {
      java.lang.Object ref = distributionTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        distributionTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *下发时间
     * </pre>
     *
     * <code>string distributionTime = 6;</code>
     */
    public Builder setDistributionTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      distributionTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *下发时间
     * </pre>
     *
     * <code>string distributionTime = 6;</code>
     */
    public Builder clearDistributionTime() {
      
      distributionTime_ = getDefaultInstance().getDistributionTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *下发时间
     * </pre>
     *
     * <code>string distributionTime = 6;</code>
     */
    public Builder setDistributionTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      distributionTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object completionTime_ = "";
    /**
     * <pre>
     *完成时间
     * </pre>
     *
     * <code>string completionTime = 7;</code>
     */
    public java.lang.String getCompletionTime() {
      java.lang.Object ref = completionTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        completionTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *完成时间
     * </pre>
     *
     * <code>string completionTime = 7;</code>
     */
    public com.google.protobuf.ByteString
        getCompletionTimeBytes() {
      java.lang.Object ref = completionTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        completionTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *完成时间
     * </pre>
     *
     * <code>string completionTime = 7;</code>
     */
    public Builder setCompletionTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      completionTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *完成时间
     * </pre>
     *
     * <code>string completionTime = 7;</code>
     */
    public Builder clearCompletionTime() {
      
      completionTime_ = getDefaultInstance().getCompletionTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *完成时间
     * </pre>
     *
     * <code>string completionTime = 7;</code>
     */
    public Builder setCompletionTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      completionTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object updateTime_ = "";
    /**
     * <pre>
     *更新时间
     * </pre>
     *
     * <code>string updateTime = 8;</code>
     */
    public java.lang.String getUpdateTime() {
      java.lang.Object ref = updateTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        updateTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *更新时间
     * </pre>
     *
     * <code>string updateTime = 8;</code>
     */
    public com.google.protobuf.ByteString
        getUpdateTimeBytes() {
      java.lang.Object ref = updateTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        updateTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *更新时间
     * </pre>
     *
     * <code>string updateTime = 8;</code>
     */
    public Builder setUpdateTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      updateTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *更新时间
     * </pre>
     *
     * <code>string updateTime = 8;</code>
     */
    public Builder clearUpdateTime() {
      
      updateTime_ = getDefaultInstance().getUpdateTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *更新时间
     * </pre>
     *
     * <code>string updateTime = 8;</code>
     */
    public Builder setUpdateTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      updateTime_ = value;
      onChanged();
      return this;
    }

    private int operationType_ ;
    /**
     * <pre>
     *操作类型 0-自动 1-手工,2-未知
     * </pre>
     *
     * <code>uint32 operationType = 9;</code>
     */
    public int getOperationType() {
      return operationType_;
    }
    /**
     * <pre>
     *操作类型 0-自动 1-手工,2-未知
     * </pre>
     *
     * <code>uint32 operationType = 9;</code>
     */
    public Builder setOperationType(int value) {
      
      operationType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *操作类型 0-自动 1-手工,2-未知
     * </pre>
     *
     * <code>uint32 operationType = 9;</code>
     */
    public Builder clearOperationType() {
      
      operationType_ = 0;
      onChanged();
      return this;
    }

    private long camDataId_ ;
    /**
     * <pre>
     *cam外键
     * </pre>
     *
     * <code>uint64 camDataId = 10;</code>
     */
    public long getCamDataId() {
      return camDataId_;
    }
    /**
     * <pre>
     *cam外键
     * </pre>
     *
     * <code>uint64 camDataId = 10;</code>
     */
    public Builder setCamDataId(long value) {
      
      camDataId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *cam外键
     * </pre>
     *
     * <code>uint64 camDataId = 10;</code>
     */
    public Builder clearCamDataId() {
      
      camDataId_ = 0L;
      onChanged();
      return this;
    }

    private long dataId_ ;
    /**
     * <pre>
     *rte/rts外键
     * </pre>
     *
     * <code>uint64 dataId = 11;</code>
     */
    public long getDataId() {
      return dataId_;
    }
    /**
     * <pre>
     *rte/rts外键
     * </pre>
     *
     * <code>uint64 dataId = 11;</code>
     */
    public Builder setDataId(long value) {
      
      dataId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *rte/rts外键
     * </pre>
     *
     * <code>uint64 dataId = 11;</code>
     */
    public Builder clearDataId() {
      
      dataId_ = 0L;
      onChanged();
      return this;
    }

    private long eventSourceId_ ;
    /**
     * <pre>
     *事件来源
     * </pre>
     *
     * <code>uint64 eventSourceId = 12;</code>
     */
    public long getEventSourceId() {
      return eventSourceId_;
    }
    /**
     * <pre>
     *事件来源
     * </pre>
     *
     * <code>uint64 eventSourceId = 12;</code>
     */
    public Builder setEventSourceId(long value) {
      
      eventSourceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *事件来源
     * </pre>
     *
     * <code>uint64 eventSourceId = 12;</code>
     */
    public Builder clearEventSourceId() {
      
      eventSourceId_ = 0L;
      onChanged();
      return this;
    }

    private int distributionStatusId_ ;
    /**
     * <pre>
     *下发状态，0-未知，1-下发中，2-已下发(针对rsu回执，ans编码成功)，3-下发失败,4-asn编码失败
     * </pre>
     *
     * <code>uint32 distributionStatusId = 13;</code>
     */
    public int getDistributionStatusId() {
      return distributionStatusId_;
    }
    /**
     * <pre>
     *下发状态，0-未知，1-下发中，2-已下发(针对rsu回执，ans编码成功)，3-下发失败,4-asn编码失败
     * </pre>
     *
     * <code>uint32 distributionStatusId = 13;</code>
     */
    public Builder setDistributionStatusId(int value) {
      
      distributionStatusId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *下发状态，0-未知，1-下发中，2-已下发(针对rsu回执，ans编码成功)，3-下发失败,4-asn编码失败
     * </pre>
     *
     * <code>uint32 distributionStatusId = 13;</code>
     */
    public Builder clearDistributionStatusId() {
      
      distributionStatusId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object description_ = "";
    /**
     * <pre>
     *可选，mec侧的回执不需要，rsu广播rsi失败时，需要增加失败原因描述
     * </pre>
     *
     * <code>string description = 14;</code>
     */
    public java.lang.String getDescription() {
      java.lang.Object ref = description_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        description_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，mec侧的回执不需要，rsu广播rsi失败时，需要增加失败原因描述
     * </pre>
     *
     * <code>string description = 14;</code>
     */
    public com.google.protobuf.ByteString
        getDescriptionBytes() {
      java.lang.Object ref = description_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        description_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，mec侧的回执不需要，rsu广播rsi失败时，需要增加失败原因描述
     * </pre>
     *
     * <code>string description = 14;</code>
     */
    public Builder setDescription(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      description_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，mec侧的回执不需要，rsu广播rsi失败时，需要增加失败原因描述
     * </pre>
     *
     * <code>string description = 14;</code>
     */
    public Builder clearDescription() {
      
      description_ = getDefaultInstance().getDescription();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，mec侧的回执不需要，rsu广播rsi失败时，需要增加失败原因描述
     * </pre>
     *
     * <code>string description = 14;</code>
     */
    public Builder setDescriptionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      description_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object sourceTopic_ = "";
    /**
     * <pre>
     *转发状态的源topic编码
     * </pre>
     *
     * <code>string sourceTopic = 15;</code>
     */
    public java.lang.String getSourceTopic() {
      java.lang.Object ref = sourceTopic_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sourceTopic_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *转发状态的源topic编码
     * </pre>
     *
     * <code>string sourceTopic = 15;</code>
     */
    public com.google.protobuf.ByteString
        getSourceTopicBytes() {
      java.lang.Object ref = sourceTopic_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sourceTopic_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *转发状态的源topic编码
     * </pre>
     *
     * <code>string sourceTopic = 15;</code>
     */
    public Builder setSourceTopic(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      sourceTopic_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *转发状态的源topic编码
     * </pre>
     *
     * <code>string sourceTopic = 15;</code>
     */
    public Builder clearSourceTopic() {
      
      sourceTopic_ = getDefaultInstance().getSourceTopic();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *转发状态的源topic编码
     * </pre>
     *
     * <code>string sourceTopic = 15;</code>
     */
    public Builder setSourceTopicBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      sourceTopic_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object targetTopic_ = "";
    /**
     * <pre>
     * 转发状态的目标topic编码
     * </pre>
     *
     * <code>string targetTopic = 16;</code>
     */
    public java.lang.String getTargetTopic() {
      java.lang.Object ref = targetTopic_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        targetTopic_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 转发状态的目标topic编码
     * </pre>
     *
     * <code>string targetTopic = 16;</code>
     */
    public com.google.protobuf.ByteString
        getTargetTopicBytes() {
      java.lang.Object ref = targetTopic_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        targetTopic_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 转发状态的目标topic编码
     * </pre>
     *
     * <code>string targetTopic = 16;</code>
     */
    public Builder setTargetTopic(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      targetTopic_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 转发状态的目标topic编码
     * </pre>
     *
     * <code>string targetTopic = 16;</code>
     */
    public Builder clearTargetTopic() {
      
      targetTopic_ = getDefaultInstance().getTargetTopic();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 转发状态的目标topic编码
     * </pre>
     *
     * <code>string targetTopic = 16;</code>
     */
    public Builder setTargetTopicBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      targetTopic_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.RsiReply)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.RsiReply)
  private static final road.data.proto.RsiReply DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.RsiReply();
  }

  public static road.data.proto.RsiReply getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RsiReply>
      PARSER = new com.google.protobuf.AbstractParser<RsiReply>() {
    @java.lang.Override
    public RsiReply parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RsiReply(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RsiReply> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RsiReply> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.RsiReply getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

