// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface RsiReplyOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.RsiReply)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>uint64 id = 1;</code>
   */
  long getId();

  /**
   * <pre>
   *事件类型 0-rte,1-rts
   * </pre>
   *
   * <code>uint32 eventType = 2;</code>
   */
  int getEventType();

  /**
   * <pre>
   *来源设备编码
   * </pre>
   *
   * <code>string sourceDeviceId = 3;</code>
   */
  java.lang.String getSourceDeviceId();
  /**
   * <pre>
   *来源设备编码
   * </pre>
   *
   * <code>string sourceDeviceId = 3;</code>
   */
  com.google.protobuf.ByteString
      getSourceDeviceIdBytes();

  /**
   * <pre>
   *目标设备编码
   * </pre>
   *
   * <code>string targetDeviceId = 4;</code>
   */
  java.lang.String getTargetDeviceId();
  /**
   * <pre>
   *目标设备编码
   * </pre>
   *
   * <code>string targetDeviceId = 4;</code>
   */
  com.google.protobuf.ByteString
      getTargetDeviceIdBytes();

  /**
   * <pre>
   *创建事件
   * </pre>
   *
   * <code>string creatTime = 5;</code>
   */
  java.lang.String getCreatTime();
  /**
   * <pre>
   *创建事件
   * </pre>
   *
   * <code>string creatTime = 5;</code>
   */
  com.google.protobuf.ByteString
      getCreatTimeBytes();

  /**
   * <pre>
   *下发时间
   * </pre>
   *
   * <code>string distributionTime = 6;</code>
   */
  java.lang.String getDistributionTime();
  /**
   * <pre>
   *下发时间
   * </pre>
   *
   * <code>string distributionTime = 6;</code>
   */
  com.google.protobuf.ByteString
      getDistributionTimeBytes();

  /**
   * <pre>
   *完成时间
   * </pre>
   *
   * <code>string completionTime = 7;</code>
   */
  java.lang.String getCompletionTime();
  /**
   * <pre>
   *完成时间
   * </pre>
   *
   * <code>string completionTime = 7;</code>
   */
  com.google.protobuf.ByteString
      getCompletionTimeBytes();

  /**
   * <pre>
   *更新时间
   * </pre>
   *
   * <code>string updateTime = 8;</code>
   */
  java.lang.String getUpdateTime();
  /**
   * <pre>
   *更新时间
   * </pre>
   *
   * <code>string updateTime = 8;</code>
   */
  com.google.protobuf.ByteString
      getUpdateTimeBytes();

  /**
   * <pre>
   *操作类型 0-自动 1-手工,2-未知
   * </pre>
   *
   * <code>uint32 operationType = 9;</code>
   */
  int getOperationType();

  /**
   * <pre>
   *cam外键
   * </pre>
   *
   * <code>uint64 camDataId = 10;</code>
   */
  long getCamDataId();

  /**
   * <pre>
   *rte/rts外键
   * </pre>
   *
   * <code>uint64 dataId = 11;</code>
   */
  long getDataId();

  /**
   * <pre>
   *事件来源
   * </pre>
   *
   * <code>uint64 eventSourceId = 12;</code>
   */
  long getEventSourceId();

  /**
   * <pre>
   *下发状态，0-未知，1-下发中，2-已下发(针对rsu回执，ans编码成功)，3-下发失败,4-asn编码失败
   * </pre>
   *
   * <code>uint32 distributionStatusId = 13;</code>
   */
  int getDistributionStatusId();

  /**
   * <pre>
   *可选，mec侧的回执不需要，rsu广播rsi失败时，需要增加失败原因描述
   * </pre>
   *
   * <code>string description = 14;</code>
   */
  java.lang.String getDescription();
  /**
   * <pre>
   *可选，mec侧的回执不需要，rsu广播rsi失败时，需要增加失败原因描述
   * </pre>
   *
   * <code>string description = 14;</code>
   */
  com.google.protobuf.ByteString
      getDescriptionBytes();

  /**
   * <pre>
   *转发状态的源topic编码
   * </pre>
   *
   * <code>string sourceTopic = 15;</code>
   */
  java.lang.String getSourceTopic();
  /**
   * <pre>
   *转发状态的源topic编码
   * </pre>
   *
   * <code>string sourceTopic = 15;</code>
   */
  com.google.protobuf.ByteString
      getSourceTopicBytes();

  /**
   * <pre>
   * 转发状态的目标topic编码
   * </pre>
   *
   * <code>string targetTopic = 16;</code>
   */
  java.lang.String getTargetTopic();
  /**
   * <pre>
   * 转发状态的目标topic编码
   * </pre>
   *
   * <code>string targetTopic = 16;</code>
   */
  com.google.protobuf.ByteString
      getTargetTopicBytes();
}
