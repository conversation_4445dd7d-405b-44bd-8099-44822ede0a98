// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *起终时间   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.RsiTimeDetails}
 */
public  final class RsiTimeDetails extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.RsiTimeDetails)
    RsiTimeDetailsOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RsiTimeDetails.newBuilder() to construct.
  private RsiTimeDetails(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RsiTimeDetails() {
    endTimeConfidence_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RsiTimeDetails();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RsiTimeDetails(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            startTime_ = input.readUInt64();
            break;
          }
          case 16: {

            endTime_ = input.readUInt64();
            break;
          }
          case 24: {
            int rawValue = input.readEnum();

            endTimeConfidence_ = rawValue;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsiTimeDetails_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsiTimeDetails_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.RsiTimeDetails.class, road.data.proto.RsiTimeDetails.Builder.class);
  }

  public static final int STARTTIME_FIELD_NUMBER = 1;
  private long startTime_;
  /**
   * <pre>
   * 开始时间
   * </pre>
   *
   * <code>uint64 startTime = 1;</code>
   */
  public long getStartTime() {
    return startTime_;
  }

  public static final int ENDTIME_FIELD_NUMBER = 2;
  private long endTime_;
  /**
   * <pre>
   * 结束时间
   * </pre>
   *
   * <code>uint64 endTime = 2;</code>
   */
  public long getEndTime() {
    return endTime_;
  }

  public static final int ENDTIMECONFIDENCE_FIELD_NUMBER = 3;
  private int endTimeConfidence_;
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence endTimeConfidence = 3;</code>
   */
  public int getEndTimeConfidenceValue() {
    return endTimeConfidence_;
  }
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence endTimeConfidence = 3;</code>
   */
  public road.data.proto.TimeConfidence getEndTimeConfidence() {
    @SuppressWarnings("deprecation")
    road.data.proto.TimeConfidence result = road.data.proto.TimeConfidence.valueOf(endTimeConfidence_);
    return result == null ? road.data.proto.TimeConfidence.UNRECOGNIZED : result;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (startTime_ != 0L) {
      output.writeUInt64(1, startTime_);
    }
    if (endTime_ != 0L) {
      output.writeUInt64(2, endTime_);
    }
    if (endTimeConfidence_ != road.data.proto.TimeConfidence.UNAVAILABLE.getNumber()) {
      output.writeEnum(3, endTimeConfidence_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (startTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, startTime_);
    }
    if (endTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(2, endTime_);
    }
    if (endTimeConfidence_ != road.data.proto.TimeConfidence.UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, endTimeConfidence_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.RsiTimeDetails)) {
      return super.equals(obj);
    }
    road.data.proto.RsiTimeDetails other = (road.data.proto.RsiTimeDetails) obj;

    if (getStartTime()
        != other.getStartTime()) return false;
    if (getEndTime()
        != other.getEndTime()) return false;
    if (endTimeConfidence_ != other.endTimeConfidence_) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + STARTTIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getStartTime());
    hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getEndTime());
    hash = (37 * hash) + ENDTIMECONFIDENCE_FIELD_NUMBER;
    hash = (53 * hash) + endTimeConfidence_;
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.RsiTimeDetails parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RsiTimeDetails parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RsiTimeDetails parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RsiTimeDetails parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RsiTimeDetails parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RsiTimeDetails parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RsiTimeDetails parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RsiTimeDetails parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RsiTimeDetails parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.RsiTimeDetails parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RsiTimeDetails parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RsiTimeDetails parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.RsiTimeDetails prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *起终时间   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.RsiTimeDetails}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.RsiTimeDetails)
      road.data.proto.RsiTimeDetailsOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsiTimeDetails_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsiTimeDetails_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.RsiTimeDetails.class, road.data.proto.RsiTimeDetails.Builder.class);
    }

    // Construct using road.data.proto.RsiTimeDetails.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      startTime_ = 0L;

      endTime_ = 0L;

      endTimeConfidence_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsiTimeDetails_descriptor;
    }

    @java.lang.Override
    public road.data.proto.RsiTimeDetails getDefaultInstanceForType() {
      return road.data.proto.RsiTimeDetails.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.RsiTimeDetails build() {
      road.data.proto.RsiTimeDetails result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.RsiTimeDetails buildPartial() {
      road.data.proto.RsiTimeDetails result = new road.data.proto.RsiTimeDetails(this);
      result.startTime_ = startTime_;
      result.endTime_ = endTime_;
      result.endTimeConfidence_ = endTimeConfidence_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.RsiTimeDetails) {
        return mergeFrom((road.data.proto.RsiTimeDetails)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.RsiTimeDetails other) {
      if (other == road.data.proto.RsiTimeDetails.getDefaultInstance()) return this;
      if (other.getStartTime() != 0L) {
        setStartTime(other.getStartTime());
      }
      if (other.getEndTime() != 0L) {
        setEndTime(other.getEndTime());
      }
      if (other.endTimeConfidence_ != 0) {
        setEndTimeConfidenceValue(other.getEndTimeConfidenceValue());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.RsiTimeDetails parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.RsiTimeDetails) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long startTime_ ;
    /**
     * <pre>
     * 开始时间
     * </pre>
     *
     * <code>uint64 startTime = 1;</code>
     */
    public long getStartTime() {
      return startTime_;
    }
    /**
     * <pre>
     * 开始时间
     * </pre>
     *
     * <code>uint64 startTime = 1;</code>
     */
    public Builder setStartTime(long value) {
      
      startTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 开始时间
     * </pre>
     *
     * <code>uint64 startTime = 1;</code>
     */
    public Builder clearStartTime() {
      
      startTime_ = 0L;
      onChanged();
      return this;
    }

    private long endTime_ ;
    /**
     * <pre>
     * 结束时间
     * </pre>
     *
     * <code>uint64 endTime = 2;</code>
     */
    public long getEndTime() {
      return endTime_;
    }
    /**
     * <pre>
     * 结束时间
     * </pre>
     *
     * <code>uint64 endTime = 2;</code>
     */
    public Builder setEndTime(long value) {
      
      endTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 结束时间
     * </pre>
     *
     * <code>uint64 endTime = 2;</code>
     */
    public Builder clearEndTime() {
      
      endTime_ = 0L;
      onChanged();
      return this;
    }

    private int endTimeConfidence_ = 0;
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence endTimeConfidence = 3;</code>
     */
    public int getEndTimeConfidenceValue() {
      return endTimeConfidence_;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence endTimeConfidence = 3;</code>
     */
    public Builder setEndTimeConfidenceValue(int value) {
      endTimeConfidence_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence endTimeConfidence = 3;</code>
     */
    public road.data.proto.TimeConfidence getEndTimeConfidence() {
      @SuppressWarnings("deprecation")
      road.data.proto.TimeConfidence result = road.data.proto.TimeConfidence.valueOf(endTimeConfidence_);
      return result == null ? road.data.proto.TimeConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence endTimeConfidence = 3;</code>
     */
    public Builder setEndTimeConfidence(road.data.proto.TimeConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      endTimeConfidence_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence endTimeConfidence = 3;</code>
     */
    public Builder clearEndTimeConfidence() {
      
      endTimeConfidence_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.RsiTimeDetails)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.RsiTimeDetails)
  private static final road.data.proto.RsiTimeDetails DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.RsiTimeDetails();
  }

  public static road.data.proto.RsiTimeDetails getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RsiTimeDetails>
      PARSER = new com.google.protobuf.AbstractParser<RsiTimeDetails>() {
    @java.lang.Override
    public RsiTimeDetails parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RsiTimeDetails(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RsiTimeDetails> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RsiTimeDetails> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.RsiTimeDetails getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

