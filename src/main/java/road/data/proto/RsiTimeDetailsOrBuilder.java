// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface RsiTimeDetailsOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.RsiTimeDetails)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 开始时间
   * </pre>
   *
   * <code>uint64 startTime = 1;</code>
   */
  long getStartTime();

  /**
   * <pre>
   * 结束时间
   * </pre>
   *
   * <code>uint64 endTime = 2;</code>
   */
  long getEndTime();

  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence endTimeConfidence = 3;</code>
   */
  int getEndTimeConfidenceValue();
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence endTimeConfidence = 3;</code>
   */
  road.data.proto.TimeConfidence getEndTimeConfidence();
}
