// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 * RSM状态回执消息RsmReply
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.RsmReply}
 */
public  final class RsmReply extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.RsmReply)
    RsmReplyOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RsmReply.newBuilder() to construct.
  private RsmReply(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RsmReply() {
    description_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RsmReply();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RsmReply(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            dataId_ = input.readUInt64();
            break;
          }
          case 16: {

            distributionStatusId_ = input.readUInt32();
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            description_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsmReply_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsmReply_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.RsmReply.class, road.data.proto.RsmReply.Builder.class);
  }

  public static final int DATAID_FIELD_NUMBER = 1;
  private long dataId_;
  /**
   * <pre>
   *Rsm外键，取自ParticipantData中的id字段
   * </pre>
   *
   * <code>uint64 dataId = 1;</code>
   */
  public long getDataId() {
    return dataId_;
  }

  public static final int DISTRIBUTIONSTATUSID_FIELD_NUMBER = 2;
  private int distributionStatusId_;
  /**
   * <pre>
   *下发状态，0-未知，1-下发中，2-已下发(针对rsu回执，ans编码成功)，3-下发失败,4-asn编码失败
   * </pre>
   *
   * <code>uint32 distributionStatusId = 2;</code>
   */
  public int getDistributionStatusId() {
    return distributionStatusId_;
  }

  public static final int DESCRIPTION_FIELD_NUMBER = 3;
  private volatile java.lang.Object description_;
  /**
   * <pre>
   *可选，rsu广播rsm失败时，需要增加失败原因描述
   * </pre>
   *
   * <code>string description = 3;</code>
   */
  public java.lang.String getDescription() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      description_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，rsu广播rsm失败时，需要增加失败原因描述
   * </pre>
   *
   * <code>string description = 3;</code>
   */
  public com.google.protobuf.ByteString
      getDescriptionBytes() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      description_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (dataId_ != 0L) {
      output.writeUInt64(1, dataId_);
    }
    if (distributionStatusId_ != 0) {
      output.writeUInt32(2, distributionStatusId_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, description_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (dataId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, dataId_);
    }
    if (distributionStatusId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, distributionStatusId_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, description_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.RsmReply)) {
      return super.equals(obj);
    }
    road.data.proto.RsmReply other = (road.data.proto.RsmReply) obj;

    if (getDataId()
        != other.getDataId()) return false;
    if (getDistributionStatusId()
        != other.getDistributionStatusId()) return false;
    if (!getDescription()
        .equals(other.getDescription())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + DATAID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getDataId());
    hash = (37 * hash) + DISTRIBUTIONSTATUSID_FIELD_NUMBER;
    hash = (53 * hash) + getDistributionStatusId();
    hash = (37 * hash) + DESCRIPTION_FIELD_NUMBER;
    hash = (53 * hash) + getDescription().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.RsmReply parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RsmReply parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RsmReply parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RsmReply parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RsmReply parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RsmReply parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RsmReply parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RsmReply parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RsmReply parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.RsmReply parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RsmReply parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RsmReply parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.RsmReply prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * RSM状态回执消息RsmReply
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.RsmReply}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.RsmReply)
      road.data.proto.RsmReplyOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsmReply_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsmReply_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.RsmReply.class, road.data.proto.RsmReply.Builder.class);
    }

    // Construct using road.data.proto.RsmReply.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      dataId_ = 0L;

      distributionStatusId_ = 0;

      description_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsmReply_descriptor;
    }

    @java.lang.Override
    public road.data.proto.RsmReply getDefaultInstanceForType() {
      return road.data.proto.RsmReply.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.RsmReply build() {
      road.data.proto.RsmReply result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.RsmReply buildPartial() {
      road.data.proto.RsmReply result = new road.data.proto.RsmReply(this);
      result.dataId_ = dataId_;
      result.distributionStatusId_ = distributionStatusId_;
      result.description_ = description_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.RsmReply) {
        return mergeFrom((road.data.proto.RsmReply)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.RsmReply other) {
      if (other == road.data.proto.RsmReply.getDefaultInstance()) return this;
      if (other.getDataId() != 0L) {
        setDataId(other.getDataId());
      }
      if (other.getDistributionStatusId() != 0) {
        setDistributionStatusId(other.getDistributionStatusId());
      }
      if (!other.getDescription().isEmpty()) {
        description_ = other.description_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.RsmReply parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.RsmReply) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long dataId_ ;
    /**
     * <pre>
     *Rsm外键，取自ParticipantData中的id字段
     * </pre>
     *
     * <code>uint64 dataId = 1;</code>
     */
    public long getDataId() {
      return dataId_;
    }
    /**
     * <pre>
     *Rsm外键，取自ParticipantData中的id字段
     * </pre>
     *
     * <code>uint64 dataId = 1;</code>
     */
    public Builder setDataId(long value) {
      
      dataId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *Rsm外键，取自ParticipantData中的id字段
     * </pre>
     *
     * <code>uint64 dataId = 1;</code>
     */
    public Builder clearDataId() {
      
      dataId_ = 0L;
      onChanged();
      return this;
    }

    private int distributionStatusId_ ;
    /**
     * <pre>
     *下发状态，0-未知，1-下发中，2-已下发(针对rsu回执，ans编码成功)，3-下发失败,4-asn编码失败
     * </pre>
     *
     * <code>uint32 distributionStatusId = 2;</code>
     */
    public int getDistributionStatusId() {
      return distributionStatusId_;
    }
    /**
     * <pre>
     *下发状态，0-未知，1-下发中，2-已下发(针对rsu回执，ans编码成功)，3-下发失败,4-asn编码失败
     * </pre>
     *
     * <code>uint32 distributionStatusId = 2;</code>
     */
    public Builder setDistributionStatusId(int value) {
      
      distributionStatusId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *下发状态，0-未知，1-下发中，2-已下发(针对rsu回执，ans编码成功)，3-下发失败,4-asn编码失败
     * </pre>
     *
     * <code>uint32 distributionStatusId = 2;</code>
     */
    public Builder clearDistributionStatusId() {
      
      distributionStatusId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object description_ = "";
    /**
     * <pre>
     *可选，rsu广播rsm失败时，需要增加失败原因描述
     * </pre>
     *
     * <code>string description = 3;</code>
     */
    public java.lang.String getDescription() {
      java.lang.Object ref = description_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        description_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，rsu广播rsm失败时，需要增加失败原因描述
     * </pre>
     *
     * <code>string description = 3;</code>
     */
    public com.google.protobuf.ByteString
        getDescriptionBytes() {
      java.lang.Object ref = description_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        description_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，rsu广播rsm失败时，需要增加失败原因描述
     * </pre>
     *
     * <code>string description = 3;</code>
     */
    public Builder setDescription(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      description_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，rsu广播rsm失败时，需要增加失败原因描述
     * </pre>
     *
     * <code>string description = 3;</code>
     */
    public Builder clearDescription() {
      
      description_ = getDefaultInstance().getDescription();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，rsu广播rsm失败时，需要增加失败原因描述
     * </pre>
     *
     * <code>string description = 3;</code>
     */
    public Builder setDescriptionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      description_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.RsmReply)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.RsmReply)
  private static final road.data.proto.RsmReply DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.RsmReply();
  }

  public static road.data.proto.RsmReply getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RsmReply>
      PARSER = new com.google.protobuf.AbstractParser<RsmReply>() {
    @java.lang.Override
    public RsmReply parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RsmReply(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RsmReply> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RsmReply> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.RsmReply getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

