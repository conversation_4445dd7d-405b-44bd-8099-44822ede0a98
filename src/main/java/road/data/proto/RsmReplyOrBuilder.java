// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface RsmReplyOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.RsmReply)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *Rsm外键，取自ParticipantData中的id字段
   * </pre>
   *
   * <code>uint64 dataId = 1;</code>
   */
  long getDataId();

  /**
   * <pre>
   *下发状态，0-未知，1-下发中，2-已下发(针对rsu回执，ans编码成功)，3-下发失败,4-asn编码失败
   * </pre>
   *
   * <code>uint32 distributionStatusId = 2;</code>
   */
  int getDistributionStatusId();

  /**
   * <pre>
   *可选，rsu广播rsm失败时，需要增加失败原因描述
   * </pre>
   *
   * <code>string description = 3;</code>
   */
  java.lang.String getDescription();
  /**
   * <pre>
   *可选，rsu广播rsm失败时，需要增加失败原因描述
   * </pre>
   *
   * <code>string description = 3;</code>
   */
  com.google.protobuf.ByteString
      getDescriptionBytes();
}
