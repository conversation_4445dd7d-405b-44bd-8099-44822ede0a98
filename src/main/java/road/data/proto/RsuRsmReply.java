// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *（RSU侧发送RSM)云端回执RsuRsmReply，具体字段意思见*******
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.RsuRsmReply}
 */
public  final class RsuRsmReply extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.RsuRsmReply)
    RsuRsmReplyOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RsuRsmReply.newBuilder() to construct.
  private RsuRsmReply(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RsuRsmReply() {
    sourceDeviceId_ = "";
    targetDeviceId_ = "";
    rsmReplyList_ = java.util.Collections.emptyList();
    targetTopic_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RsuRsmReply();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RsuRsmReply(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            sourceDeviceId_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            targetDeviceId_ = s;
            break;
          }
          case 24: {

            camDataId_ = input.readUInt64();
            break;
          }
          case 34: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              rsmReplyList_ = new java.util.ArrayList<road.data.proto.RsmReply>();
              mutable_bitField0_ |= 0x00000001;
            }
            rsmReplyList_.add(
                input.readMessage(road.data.proto.RsmReply.parser(), extensionRegistry));
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            targetTopic_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        rsmReplyList_ = java.util.Collections.unmodifiableList(rsmReplyList_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsuRsmReply_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsuRsmReply_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.RsuRsmReply.class, road.data.proto.RsuRsmReply.Builder.class);
  }

  public static final int SOURCEDEVICEID_FIELD_NUMBER = 1;
  private volatile java.lang.Object sourceDeviceId_;
  /**
   * <pre>
   *来源设备编码,Mec的Deviced
   * </pre>
   *
   * <code>string sourceDeviceId = 1;</code>
   */
  public java.lang.String getSourceDeviceId() {
    java.lang.Object ref = sourceDeviceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      sourceDeviceId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *来源设备编码,Mec的Deviced
   * </pre>
   *
   * <code>string sourceDeviceId = 1;</code>
   */
  public com.google.protobuf.ByteString
      getSourceDeviceIdBytes() {
    java.lang.Object ref = sourceDeviceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      sourceDeviceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TARGETDEVICEID_FIELD_NUMBER = 2;
  private volatile java.lang.Object targetDeviceId_;
  /**
   * <pre>
   *目标设备编码，Rsu设备编码
   * </pre>
   *
   * <code>string targetDeviceId = 2;</code>
   */
  public java.lang.String getTargetDeviceId() {
    java.lang.Object ref = targetDeviceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      targetDeviceId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *目标设备编码，Rsu设备编码
   * </pre>
   *
   * <code>string targetDeviceId = 2;</code>
   */
  public com.google.protobuf.ByteString
      getTargetDeviceIdBytes() {
    java.lang.Object ref = targetDeviceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      targetDeviceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CAMDATAID_FIELD_NUMBER = 3;
  private long camDataId_;
  /**
   * <pre>
   *cam外键，取自CamData中的id字段
   * </pre>
   *
   * <code>uint64 camDataId = 3;</code>
   */
  public long getCamDataId() {
    return camDataId_;
  }

  public static final int RSMREPLYLIST_FIELD_NUMBER = 4;
  private java.util.List<road.data.proto.RsmReply> rsmReplyList_;
  /**
   * <pre>
   *交通参与者回执id、状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
   */
  public java.util.List<road.data.proto.RsmReply> getRsmReplyListList() {
    return rsmReplyList_;
  }
  /**
   * <pre>
   *交通参与者回执id、状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
   */
  public java.util.List<? extends road.data.proto.RsmReplyOrBuilder> 
      getRsmReplyListOrBuilderList() {
    return rsmReplyList_;
  }
  /**
   * <pre>
   *交通参与者回执id、状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
   */
  public int getRsmReplyListCount() {
    return rsmReplyList_.size();
  }
  /**
   * <pre>
   *交通参与者回执id、状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
   */
  public road.data.proto.RsmReply getRsmReplyList(int index) {
    return rsmReplyList_.get(index);
  }
  /**
   * <pre>
   *交通参与者回执id、状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
   */
  public road.data.proto.RsmReplyOrBuilder getRsmReplyListOrBuilder(
      int index) {
    return rsmReplyList_.get(index);
  }

  public static final int TARGETTOPIC_FIELD_NUMBER = 5;
  private volatile java.lang.Object targetTopic_;
  /**
   * <pre>
   * 转发状态的目标topic编码，rsu朝云端上报rsm回执的topic
   * </pre>
   *
   * <code>string targetTopic = 5;</code>
   */
  public java.lang.String getTargetTopic() {
    java.lang.Object ref = targetTopic_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      targetTopic_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 转发状态的目标topic编码，rsu朝云端上报rsm回执的topic
   * </pre>
   *
   * <code>string targetTopic = 5;</code>
   */
  public com.google.protobuf.ByteString
      getTargetTopicBytes() {
    java.lang.Object ref = targetTopic_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      targetTopic_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getSourceDeviceIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, sourceDeviceId_);
    }
    if (!getTargetDeviceIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, targetDeviceId_);
    }
    if (camDataId_ != 0L) {
      output.writeUInt64(3, camDataId_);
    }
    for (int i = 0; i < rsmReplyList_.size(); i++) {
      output.writeMessage(4, rsmReplyList_.get(i));
    }
    if (!getTargetTopicBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, targetTopic_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getSourceDeviceIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, sourceDeviceId_);
    }
    if (!getTargetDeviceIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, targetDeviceId_);
    }
    if (camDataId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, camDataId_);
    }
    for (int i = 0; i < rsmReplyList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, rsmReplyList_.get(i));
    }
    if (!getTargetTopicBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, targetTopic_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.RsuRsmReply)) {
      return super.equals(obj);
    }
    road.data.proto.RsuRsmReply other = (road.data.proto.RsuRsmReply) obj;

    if (!getSourceDeviceId()
        .equals(other.getSourceDeviceId())) return false;
    if (!getTargetDeviceId()
        .equals(other.getTargetDeviceId())) return false;
    if (getCamDataId()
        != other.getCamDataId()) return false;
    if (!getRsmReplyListList()
        .equals(other.getRsmReplyListList())) return false;
    if (!getTargetTopic()
        .equals(other.getTargetTopic())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SOURCEDEVICEID_FIELD_NUMBER;
    hash = (53 * hash) + getSourceDeviceId().hashCode();
    hash = (37 * hash) + TARGETDEVICEID_FIELD_NUMBER;
    hash = (53 * hash) + getTargetDeviceId().hashCode();
    hash = (37 * hash) + CAMDATAID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getCamDataId());
    if (getRsmReplyListCount() > 0) {
      hash = (37 * hash) + RSMREPLYLIST_FIELD_NUMBER;
      hash = (53 * hash) + getRsmReplyListList().hashCode();
    }
    hash = (37 * hash) + TARGETTOPIC_FIELD_NUMBER;
    hash = (53 * hash) + getTargetTopic().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.RsuRsmReply parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RsuRsmReply parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RsuRsmReply parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RsuRsmReply parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RsuRsmReply parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RsuRsmReply parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RsuRsmReply parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RsuRsmReply parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RsuRsmReply parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.RsuRsmReply parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RsuRsmReply parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RsuRsmReply parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.RsuRsmReply prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *（RSU侧发送RSM)云端回执RsuRsmReply，具体字段意思见*******
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.RsuRsmReply}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.RsuRsmReply)
      road.data.proto.RsuRsmReplyOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsuRsmReply_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsuRsmReply_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.RsuRsmReply.class, road.data.proto.RsuRsmReply.Builder.class);
    }

    // Construct using road.data.proto.RsuRsmReply.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getRsmReplyListFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      sourceDeviceId_ = "";

      targetDeviceId_ = "";

      camDataId_ = 0L;

      if (rsmReplyListBuilder_ == null) {
        rsmReplyList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        rsmReplyListBuilder_.clear();
      }
      targetTopic_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RsuRsmReply_descriptor;
    }

    @java.lang.Override
    public road.data.proto.RsuRsmReply getDefaultInstanceForType() {
      return road.data.proto.RsuRsmReply.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.RsuRsmReply build() {
      road.data.proto.RsuRsmReply result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.RsuRsmReply buildPartial() {
      road.data.proto.RsuRsmReply result = new road.data.proto.RsuRsmReply(this);
      int from_bitField0_ = bitField0_;
      result.sourceDeviceId_ = sourceDeviceId_;
      result.targetDeviceId_ = targetDeviceId_;
      result.camDataId_ = camDataId_;
      if (rsmReplyListBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          rsmReplyList_ = java.util.Collections.unmodifiableList(rsmReplyList_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.rsmReplyList_ = rsmReplyList_;
      } else {
        result.rsmReplyList_ = rsmReplyListBuilder_.build();
      }
      result.targetTopic_ = targetTopic_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.RsuRsmReply) {
        return mergeFrom((road.data.proto.RsuRsmReply)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.RsuRsmReply other) {
      if (other == road.data.proto.RsuRsmReply.getDefaultInstance()) return this;
      if (!other.getSourceDeviceId().isEmpty()) {
        sourceDeviceId_ = other.sourceDeviceId_;
        onChanged();
      }
      if (!other.getTargetDeviceId().isEmpty()) {
        targetDeviceId_ = other.targetDeviceId_;
        onChanged();
      }
      if (other.getCamDataId() != 0L) {
        setCamDataId(other.getCamDataId());
      }
      if (rsmReplyListBuilder_ == null) {
        if (!other.rsmReplyList_.isEmpty()) {
          if (rsmReplyList_.isEmpty()) {
            rsmReplyList_ = other.rsmReplyList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureRsmReplyListIsMutable();
            rsmReplyList_.addAll(other.rsmReplyList_);
          }
          onChanged();
        }
      } else {
        if (!other.rsmReplyList_.isEmpty()) {
          if (rsmReplyListBuilder_.isEmpty()) {
            rsmReplyListBuilder_.dispose();
            rsmReplyListBuilder_ = null;
            rsmReplyList_ = other.rsmReplyList_;
            bitField0_ = (bitField0_ & ~0x00000001);
            rsmReplyListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getRsmReplyListFieldBuilder() : null;
          } else {
            rsmReplyListBuilder_.addAllMessages(other.rsmReplyList_);
          }
        }
      }
      if (!other.getTargetTopic().isEmpty()) {
        targetTopic_ = other.targetTopic_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.RsuRsmReply parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.RsuRsmReply) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object sourceDeviceId_ = "";
    /**
     * <pre>
     *来源设备编码,Mec的Deviced
     * </pre>
     *
     * <code>string sourceDeviceId = 1;</code>
     */
    public java.lang.String getSourceDeviceId() {
      java.lang.Object ref = sourceDeviceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sourceDeviceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *来源设备编码,Mec的Deviced
     * </pre>
     *
     * <code>string sourceDeviceId = 1;</code>
     */
    public com.google.protobuf.ByteString
        getSourceDeviceIdBytes() {
      java.lang.Object ref = sourceDeviceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sourceDeviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *来源设备编码,Mec的Deviced
     * </pre>
     *
     * <code>string sourceDeviceId = 1;</code>
     */
    public Builder setSourceDeviceId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      sourceDeviceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *来源设备编码,Mec的Deviced
     * </pre>
     *
     * <code>string sourceDeviceId = 1;</code>
     */
    public Builder clearSourceDeviceId() {
      
      sourceDeviceId_ = getDefaultInstance().getSourceDeviceId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *来源设备编码,Mec的Deviced
     * </pre>
     *
     * <code>string sourceDeviceId = 1;</code>
     */
    public Builder setSourceDeviceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      sourceDeviceId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object targetDeviceId_ = "";
    /**
     * <pre>
     *目标设备编码，Rsu设备编码
     * </pre>
     *
     * <code>string targetDeviceId = 2;</code>
     */
    public java.lang.String getTargetDeviceId() {
      java.lang.Object ref = targetDeviceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        targetDeviceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *目标设备编码，Rsu设备编码
     * </pre>
     *
     * <code>string targetDeviceId = 2;</code>
     */
    public com.google.protobuf.ByteString
        getTargetDeviceIdBytes() {
      java.lang.Object ref = targetDeviceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        targetDeviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *目标设备编码，Rsu设备编码
     * </pre>
     *
     * <code>string targetDeviceId = 2;</code>
     */
    public Builder setTargetDeviceId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      targetDeviceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *目标设备编码，Rsu设备编码
     * </pre>
     *
     * <code>string targetDeviceId = 2;</code>
     */
    public Builder clearTargetDeviceId() {
      
      targetDeviceId_ = getDefaultInstance().getTargetDeviceId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *目标设备编码，Rsu设备编码
     * </pre>
     *
     * <code>string targetDeviceId = 2;</code>
     */
    public Builder setTargetDeviceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      targetDeviceId_ = value;
      onChanged();
      return this;
    }

    private long camDataId_ ;
    /**
     * <pre>
     *cam外键，取自CamData中的id字段
     * </pre>
     *
     * <code>uint64 camDataId = 3;</code>
     */
    public long getCamDataId() {
      return camDataId_;
    }
    /**
     * <pre>
     *cam外键，取自CamData中的id字段
     * </pre>
     *
     * <code>uint64 camDataId = 3;</code>
     */
    public Builder setCamDataId(long value) {
      
      camDataId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *cam外键，取自CamData中的id字段
     * </pre>
     *
     * <code>uint64 camDataId = 3;</code>
     */
    public Builder clearCamDataId() {
      
      camDataId_ = 0L;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.RsmReply> rsmReplyList_ =
      java.util.Collections.emptyList();
    private void ensureRsmReplyListIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        rsmReplyList_ = new java.util.ArrayList<road.data.proto.RsmReply>(rsmReplyList_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RsmReply, road.data.proto.RsmReply.Builder, road.data.proto.RsmReplyOrBuilder> rsmReplyListBuilder_;

    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public java.util.List<road.data.proto.RsmReply> getRsmReplyListList() {
      if (rsmReplyListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rsmReplyList_);
      } else {
        return rsmReplyListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public int getRsmReplyListCount() {
      if (rsmReplyListBuilder_ == null) {
        return rsmReplyList_.size();
      } else {
        return rsmReplyListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public road.data.proto.RsmReply getRsmReplyList(int index) {
      if (rsmReplyListBuilder_ == null) {
        return rsmReplyList_.get(index);
      } else {
        return rsmReplyListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public Builder setRsmReplyList(
        int index, road.data.proto.RsmReply value) {
      if (rsmReplyListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRsmReplyListIsMutable();
        rsmReplyList_.set(index, value);
        onChanged();
      } else {
        rsmReplyListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public Builder setRsmReplyList(
        int index, road.data.proto.RsmReply.Builder builderForValue) {
      if (rsmReplyListBuilder_ == null) {
        ensureRsmReplyListIsMutable();
        rsmReplyList_.set(index, builderForValue.build());
        onChanged();
      } else {
        rsmReplyListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public Builder addRsmReplyList(road.data.proto.RsmReply value) {
      if (rsmReplyListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRsmReplyListIsMutable();
        rsmReplyList_.add(value);
        onChanged();
      } else {
        rsmReplyListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public Builder addRsmReplyList(
        int index, road.data.proto.RsmReply value) {
      if (rsmReplyListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRsmReplyListIsMutable();
        rsmReplyList_.add(index, value);
        onChanged();
      } else {
        rsmReplyListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public Builder addRsmReplyList(
        road.data.proto.RsmReply.Builder builderForValue) {
      if (rsmReplyListBuilder_ == null) {
        ensureRsmReplyListIsMutable();
        rsmReplyList_.add(builderForValue.build());
        onChanged();
      } else {
        rsmReplyListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public Builder addRsmReplyList(
        int index, road.data.proto.RsmReply.Builder builderForValue) {
      if (rsmReplyListBuilder_ == null) {
        ensureRsmReplyListIsMutable();
        rsmReplyList_.add(index, builderForValue.build());
        onChanged();
      } else {
        rsmReplyListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public Builder addAllRsmReplyList(
        java.lang.Iterable<? extends road.data.proto.RsmReply> values) {
      if (rsmReplyListBuilder_ == null) {
        ensureRsmReplyListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rsmReplyList_);
        onChanged();
      } else {
        rsmReplyListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public Builder clearRsmReplyList() {
      if (rsmReplyListBuilder_ == null) {
        rsmReplyList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        rsmReplyListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public Builder removeRsmReplyList(int index) {
      if (rsmReplyListBuilder_ == null) {
        ensureRsmReplyListIsMutable();
        rsmReplyList_.remove(index);
        onChanged();
      } else {
        rsmReplyListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public road.data.proto.RsmReply.Builder getRsmReplyListBuilder(
        int index) {
      return getRsmReplyListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public road.data.proto.RsmReplyOrBuilder getRsmReplyListOrBuilder(
        int index) {
      if (rsmReplyListBuilder_ == null) {
        return rsmReplyList_.get(index);  } else {
        return rsmReplyListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public java.util.List<? extends road.data.proto.RsmReplyOrBuilder> 
         getRsmReplyListOrBuilderList() {
      if (rsmReplyListBuilder_ != null) {
        return rsmReplyListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rsmReplyList_);
      }
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public road.data.proto.RsmReply.Builder addRsmReplyListBuilder() {
      return getRsmReplyListFieldBuilder().addBuilder(
          road.data.proto.RsmReply.getDefaultInstance());
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public road.data.proto.RsmReply.Builder addRsmReplyListBuilder(
        int index) {
      return getRsmReplyListFieldBuilder().addBuilder(
          index, road.data.proto.RsmReply.getDefaultInstance());
    }
    /**
     * <pre>
     *交通参与者回执id、状态列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
     */
    public java.util.List<road.data.proto.RsmReply.Builder> 
         getRsmReplyListBuilderList() {
      return getRsmReplyListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.RsmReply, road.data.proto.RsmReply.Builder, road.data.proto.RsmReplyOrBuilder> 
        getRsmReplyListFieldBuilder() {
      if (rsmReplyListBuilder_ == null) {
        rsmReplyListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.RsmReply, road.data.proto.RsmReply.Builder, road.data.proto.RsmReplyOrBuilder>(
                rsmReplyList_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        rsmReplyList_ = null;
      }
      return rsmReplyListBuilder_;
    }

    private java.lang.Object targetTopic_ = "";
    /**
     * <pre>
     * 转发状态的目标topic编码，rsu朝云端上报rsm回执的topic
     * </pre>
     *
     * <code>string targetTopic = 5;</code>
     */
    public java.lang.String getTargetTopic() {
      java.lang.Object ref = targetTopic_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        targetTopic_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 转发状态的目标topic编码，rsu朝云端上报rsm回执的topic
     * </pre>
     *
     * <code>string targetTopic = 5;</code>
     */
    public com.google.protobuf.ByteString
        getTargetTopicBytes() {
      java.lang.Object ref = targetTopic_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        targetTopic_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 转发状态的目标topic编码，rsu朝云端上报rsm回执的topic
     * </pre>
     *
     * <code>string targetTopic = 5;</code>
     */
    public Builder setTargetTopic(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      targetTopic_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 转发状态的目标topic编码，rsu朝云端上报rsm回执的topic
     * </pre>
     *
     * <code>string targetTopic = 5;</code>
     */
    public Builder clearTargetTopic() {
      
      targetTopic_ = getDefaultInstance().getTargetTopic();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 转发状态的目标topic编码，rsu朝云端上报rsm回执的topic
     * </pre>
     *
     * <code>string targetTopic = 5;</code>
     */
    public Builder setTargetTopicBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      targetTopic_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.RsuRsmReply)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.RsuRsmReply)
  private static final road.data.proto.RsuRsmReply DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.RsuRsmReply();
  }

  public static road.data.proto.RsuRsmReply getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RsuRsmReply>
      PARSER = new com.google.protobuf.AbstractParser<RsuRsmReply>() {
    @java.lang.Override
    public RsuRsmReply parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RsuRsmReply(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RsuRsmReply> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RsuRsmReply> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.RsuRsmReply getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

