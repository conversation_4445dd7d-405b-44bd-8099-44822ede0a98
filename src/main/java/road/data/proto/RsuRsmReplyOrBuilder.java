// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface RsuRsmReplyOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.RsuRsmReply)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *来源设备编码,Mec的Deviced
   * </pre>
   *
   * <code>string sourceDeviceId = 1;</code>
   */
  java.lang.String getSourceDeviceId();
  /**
   * <pre>
   *来源设备编码,Mec的Deviced
   * </pre>
   *
   * <code>string sourceDeviceId = 1;</code>
   */
  com.google.protobuf.ByteString
      getSourceDeviceIdBytes();

  /**
   * <pre>
   *目标设备编码，Rsu设备编码
   * </pre>
   *
   * <code>string targetDeviceId = 2;</code>
   */
  java.lang.String getTargetDeviceId();
  /**
   * <pre>
   *目标设备编码，Rsu设备编码
   * </pre>
   *
   * <code>string targetDeviceId = 2;</code>
   */
  com.google.protobuf.ByteString
      getTargetDeviceIdBytes();

  /**
   * <pre>
   *cam外键，取自CamData中的id字段
   * </pre>
   *
   * <code>uint64 camDataId = 3;</code>
   */
  long getCamDataId();

  /**
   * <pre>
   *交通参与者回执id、状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
   */
  java.util.List<road.data.proto.RsmReply> 
      getRsmReplyListList();
  /**
   * <pre>
   *交通参与者回执id、状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
   */
  road.data.proto.RsmReply getRsmReplyList(int index);
  /**
   * <pre>
   *交通参与者回执id、状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
   */
  int getRsmReplyListCount();
  /**
   * <pre>
   *交通参与者回执id、状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
   */
  java.util.List<? extends road.data.proto.RsmReplyOrBuilder> 
      getRsmReplyListOrBuilderList();
  /**
   * <pre>
   *交通参与者回执id、状态列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.RsmReply rsmReplyList = 4;</code>
   */
  road.data.proto.RsmReplyOrBuilder getRsmReplyListOrBuilder(
      int index);

  /**
   * <pre>
   * 转发状态的目标topic编码，rsu朝云端上报rsm回执的topic
   * </pre>
   *
   * <code>string targetTopic = 5;</code>
   */
  java.lang.String getTargetTopic();
  /**
   * <pre>
   * 转发状态的目标topic编码，rsu朝云端上报rsm回执的topic
   * </pre>
   *
   * <code>string targetTopic = 5;</code>
   */
  com.google.protobuf.ByteString
      getTargetTopicBytes();
}
