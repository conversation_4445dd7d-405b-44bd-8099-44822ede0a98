// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *道路交通事件信息RteData （除了RteType）
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.RteData}
 */
public  final class RteData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.RteData)
    RteDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RteData.newBuilder() to construct.
  private RteData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RteData() {
    description_ = "";
    eventSource_ = 0;
    dataSource_ = 0;
    deviceIdList_ = "";
    priority_ = "";
    referencePath_ = java.util.Collections.emptyList();
    referenceLinks_ = java.util.Collections.emptyList();
    eventObjId_ = java.util.Collections.emptyList();
    eventImages_ = "";
    eventVideos_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RteData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RteData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            rteId_ = input.readUInt32();
            break;
          }
          case 16: {

            rteType_ = input.readUInt32();
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            description_ = s;
            break;
          }
          case 32: {
            int rawValue = input.readEnum();

            eventSource_ = rawValue;
            break;
          }
          case 40: {
            int rawValue = input.readEnum();

            dataSource_ = rawValue;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            deviceIdList_ = s;
            break;
          }
          case 58: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (rtePos_ != null) {
              subBuilder = rtePos_.toBuilder();
            }
            rtePos_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(rtePos_);
              rtePos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 66: {
            road.data.proto.MapLocation.Builder subBuilder = null;
            if (mapLocation_ != null) {
              subBuilder = mapLocation_.toBuilder();
            }
            mapLocation_ = input.readMessage(road.data.proto.MapLocation.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(mapLocation_);
              mapLocation_ = subBuilder.buildPartial();
            }

            break;
          }
          case 72: {

            eventRadius_ = input.readUInt32();
            break;
          }
          case 82: {
            road.data.proto.RsiTimeDetails.Builder subBuilder = null;
            if (timeDetails_ != null) {
              subBuilder = timeDetails_.toBuilder();
            }
            timeDetails_ = input.readMessage(road.data.proto.RsiTimeDetails.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(timeDetails_);
              timeDetails_ = subBuilder.buildPartial();
            }

            break;
          }
          case 90: {
            java.lang.String s = input.readStringRequireUtf8();

            priority_ = s;
            break;
          }
          case 98: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              referencePath_ = new java.util.ArrayList<road.data.proto.ReferencePath>();
              mutable_bitField0_ |= 0x00000001;
            }
            referencePath_.add(
                input.readMessage(road.data.proto.ReferencePath.parser(), extensionRegistry));
            break;
          }
          case 106: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              referenceLinks_ = new java.util.ArrayList<road.data.proto.ReferenceLink>();
              mutable_bitField0_ |= 0x00000002;
            }
            referenceLinks_.add(
                input.readMessage(road.data.proto.ReferenceLink.parser(), extensionRegistry));
            break;
          }
          case 114: {
            if (!((mutable_bitField0_ & 0x00000004) != 0)) {
              eventObjId_ = new java.util.ArrayList<road.data.proto.ObjIdValue>();
              mutable_bitField0_ |= 0x00000004;
            }
            eventObjId_.add(
                input.readMessage(road.data.proto.ObjIdValue.parser(), extensionRegistry));
            break;
          }
          case 120: {

            eventConfid_ = input.readInt32();
            break;
          }
          case 130: {
            java.lang.String s = input.readStringRequireUtf8();

            eventImages_ = s;
            break;
          }
          case 138: {
            java.lang.String s = input.readStringRequireUtf8();

            eventVideos_ = s;
            break;
          }
          case 144: {

            sessionId_ = input.readUInt64();
            break;
          }
          case 152: {

            id_ = input.readUInt64();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        referencePath_ = java.util.Collections.unmodifiableList(referencePath_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        referenceLinks_ = java.util.Collections.unmodifiableList(referenceLinks_);
      }
      if (((mutable_bitField0_ & 0x00000004) != 0)) {
        eventObjId_ = java.util.Collections.unmodifiableList(eventObjId_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RteData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RteData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.RteData.class, road.data.proto.RteData.Builder.class);
  }

  /**
   * <pre>
   *{
   *“id”: 810907429486297107,  //全局唯一事件ID
   *“state”: 0,   //-1: 发送到MEC失败
   *-2: 发送到RSU失败
   *-3: 发送到车端失败
   *-4: 发送到云端失败
   *0: 已生成
   *1: 发送到MEC
   *2: 发送到RSU
   *3: 发送到车端
   *4: 发送到云端
   *“desc”: { … }  //自定义描述信息，失败原因等
   *}
   * </pre>
   *
   * Protobuf enum {@code cn.seisys.v2x.pb.RteData.EventSource}
   */
  public enum EventSource
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *未知
     * </pre>
     *
     * <code>UNKNOWN_EVENT_SOURCE = 0;</code>
     */
    UNKNOWN_EVENT_SOURCE(0),
    /**
     * <pre>
     *交警
     * </pre>
     *
     * <code>TRAFFIC_POLICE = 1;</code>
     */
    TRAFFIC_POLICE(1),
    /**
     * <pre>
     *政府
     * </pre>
     *
     * <code>GOVENMENT = 2;</code>
     */
    GOVENMENT(2),
    /**
     * <pre>
     *气象部门
     * </pre>
     *
     * <code>METEOROLOGICAL_DEPARTMENT = 3;</code>
     */
    METEOROLOGICAL_DEPARTMENT(3),
    /**
     * <pre>
     *互联网服务
     * </pre>
     *
     * <code>INTERNET_SERVICES = 4;</code>
     */
    INTERNET_SERVICES(4),
    /**
     * <pre>
     * 本地检测
     * </pre>
     *
     * <code>LOCAL_DETECTION = 5;</code>
     */
    LOCAL_DETECTION(5),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *未知
     * </pre>
     *
     * <code>UNKNOWN_EVENT_SOURCE = 0;</code>
     */
    public static final int UNKNOWN_EVENT_SOURCE_VALUE = 0;
    /**
     * <pre>
     *交警
     * </pre>
     *
     * <code>TRAFFIC_POLICE = 1;</code>
     */
    public static final int TRAFFIC_POLICE_VALUE = 1;
    /**
     * <pre>
     *政府
     * </pre>
     *
     * <code>GOVENMENT = 2;</code>
     */
    public static final int GOVENMENT_VALUE = 2;
    /**
     * <pre>
     *气象部门
     * </pre>
     *
     * <code>METEOROLOGICAL_DEPARTMENT = 3;</code>
     */
    public static final int METEOROLOGICAL_DEPARTMENT_VALUE = 3;
    /**
     * <pre>
     *互联网服务
     * </pre>
     *
     * <code>INTERNET_SERVICES = 4;</code>
     */
    public static final int INTERNET_SERVICES_VALUE = 4;
    /**
     * <pre>
     * 本地检测
     * </pre>
     *
     * <code>LOCAL_DETECTION = 5;</code>
     */
    public static final int LOCAL_DETECTION_VALUE = 5;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EventSource valueOf(int value) {
      return forNumber(value);
    }

    public static EventSource forNumber(int value) {
      switch (value) {
        case 0: return UNKNOWN_EVENT_SOURCE;
        case 1: return TRAFFIC_POLICE;
        case 2: return GOVENMENT;
        case 3: return METEOROLOGICAL_DEPARTMENT;
        case 4: return INTERNET_SERVICES;
        case 5: return LOCAL_DETECTION;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EventSource>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EventSource> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EventSource>() {
            public EventSource findValueByNumber(int number) {
              return EventSource.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.RteData.getDescriptor().getEnumTypes().get(0);
    }

    private static final EventSource[] VALUES = values();

    public static EventSource valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EventSource(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.RteData.EventSource)
  }

  public static final int RTEID_FIELD_NUMBER = 1;
  private int rteId_;
  /**
   * <pre>
   * 每个事件有个ID编号，不同事件编号不同;ID循环编号
   * </pre>
   *
   * <code>uint32 rteId = 1;</code>
   */
  public int getRteId() {
    return rteId_;
  }

  public static final int RTETYPE_FIELD_NUMBER = 2;
  private int rteType_;
  /**
   * <pre>
   * 道路的事件信息的类型，参考事件类型章节
   * </pre>
   *
   * <code>uint32 rteType = 2;</code>
   */
  public int getRteType() {
    return rteType_;
  }

  public static final int DESCRIPTION_FIELD_NUMBER = 3;
  private volatile java.lang.Object description_;
  /**
   * <pre>
   *可选，描述，json字串
   * </pre>
   *
   * <code>string description = 3;</code>
   */
  public java.lang.String getDescription() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      description_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，描述，json字串
   * </pre>
   *
   * <code>string description = 3;</code>
   */
  public com.google.protobuf.ByteString
      getDescriptionBytes() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      description_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int EVENTSOURCE_FIELD_NUMBER = 4;
  private int eventSource_;
  /**
   * <pre>
   *事件信息来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RteData.EventSource eventSource = 4;</code>
   */
  public int getEventSourceValue() {
    return eventSource_;
  }
  /**
   * <pre>
   *事件信息来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RteData.EventSource eventSource = 4;</code>
   */
  public road.data.proto.RteData.EventSource getEventSource() {
    @SuppressWarnings("deprecation")
    road.data.proto.RteData.EventSource result = road.data.proto.RteData.EventSource.valueOf(eventSource_);
    return result == null ? road.data.proto.RteData.EventSource.UNRECOGNIZED : result;
  }

  public static final int DATASOURCE_FIELD_NUMBER = 5;
  private int dataSource_;
  /**
   * <pre>
   *路侧数据来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource dataSource = 5;</code>
   */
  public int getDataSourceValue() {
    return dataSource_;
  }
  /**
   * <pre>
   *路侧数据来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource dataSource = 5;</code>
   */
  public road.data.proto.DataSource getDataSource() {
    @SuppressWarnings("deprecation")
    road.data.proto.DataSource result = road.data.proto.DataSource.valueOf(dataSource_);
    return result == null ? road.data.proto.DataSource.UNRECOGNIZED : result;
  }

  public static final int DEVICEIDLIST_FIELD_NUMBER = 6;
  private volatile java.lang.Object deviceIdList_;
  /**
   * <pre>
   *检测来源设备id，json数组
   * </pre>
   *
   * <code>string deviceIdList = 6;</code>
   */
  public java.lang.String getDeviceIdList() {
    java.lang.Object ref = deviceIdList_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deviceIdList_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *检测来源设备id，json数组
   * </pre>
   *
   * <code>string deviceIdList = 6;</code>
   */
  public com.google.protobuf.ByteString
      getDeviceIdListBytes() {
    java.lang.Object ref = deviceIdList_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deviceIdList_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RTEPOS_FIELD_NUMBER = 7;
  private road.data.proto.Position3D rtePos_;
  /**
   * <pre>
   * 定义事件的经纬度和高，绝对位置。    
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
   */
  public boolean hasRtePos() {
    return rtePos_ != null;
  }
  /**
   * <pre>
   * 定义事件的经纬度和高，绝对位置。    
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
   */
  public road.data.proto.Position3D getRtePos() {
    return rtePos_ == null ? road.data.proto.Position3D.getDefaultInstance() : rtePos_;
  }
  /**
   * <pre>
   * 定义事件的经纬度和高，绝对位置。    
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
   */
  public road.data.proto.Position3DOrBuilder getRtePosOrBuilder() {
    return getRtePos();
  }

  public static final int MAPLOCATION_FIELD_NUMBER = 8;
  private road.data.proto.MapLocation mapLocation_;
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
   */
  public boolean hasMapLocation() {
    return mapLocation_ != null;
  }
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
   */
  public road.data.proto.MapLocation getMapLocation() {
    return mapLocation_ == null ? road.data.proto.MapLocation.getDefaultInstance() : mapLocation_;
  }
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
   */
  public road.data.proto.MapLocationOrBuilder getMapLocationOrBuilder() {
    return getMapLocation();
  }

  public static final int EVENTRADIUS_FIELD_NUMBER = 9;
  private int eventRadius_;
  /**
   * <pre>
   * 可选，特定圆形范围的半径大小，单位0.1m，默认值2000。表示交通事件影响区域边界离中心线的垂直距离，与RefPath字段组合，共同反映该区域的宽度以覆盖实际路段。
   * </pre>
   *
   * <code>uint32 eventRadius = 9;</code>
   */
  public int getEventRadius() {
    return eventRadius_;
  }

  public static final int TIMEDETAILS_FIELD_NUMBER = 10;
  private road.data.proto.RsiTimeDetails timeDetails_;
  /**
   * <pre>
   *可选，事件始终时间    
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
   */
  public boolean hasTimeDetails() {
    return timeDetails_ != null;
  }
  /**
   * <pre>
   *可选，事件始终时间    
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
   */
  public road.data.proto.RsiTimeDetails getTimeDetails() {
    return timeDetails_ == null ? road.data.proto.RsiTimeDetails.getDefaultInstance() : timeDetails_;
  }
  /**
   * <pre>
   *可选，事件始终时间    
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
   */
  public road.data.proto.RsiTimeDetailsOrBuilder getTimeDetailsOrBuilder() {
    return getTimeDetails();
  }

  public static final int PRIORITY_FIELD_NUMBER = 11;
  private volatile java.lang.Object priority_;
  /**
   * <pre>
   *可选， 事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
   * </pre>
   *
   * <code>string priority = 11;</code>
   */
  public java.lang.String getPriority() {
    java.lang.Object ref = priority_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      priority_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选， 事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
   * </pre>
   *
   * <code>string priority = 11;</code>
   */
  public com.google.protobuf.ByteString
      getPriorityBytes() {
    java.lang.Object ref = priority_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      priority_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REFERENCEPATH_FIELD_NUMBER = 12;
  private java.util.List<road.data.proto.ReferencePath> referencePath_;
  /**
   * <pre>
   *可选，道路交通事件和标志的关联路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
   */
  public java.util.List<road.data.proto.ReferencePath> getReferencePathList() {
    return referencePath_;
  }
  /**
   * <pre>
   *可选，道路交通事件和标志的关联路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
   */
  public java.util.List<? extends road.data.proto.ReferencePathOrBuilder> 
      getReferencePathOrBuilderList() {
    return referencePath_;
  }
  /**
   * <pre>
   *可选，道路交通事件和标志的关联路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
   */
  public int getReferencePathCount() {
    return referencePath_.size();
  }
  /**
   * <pre>
   *可选，道路交通事件和标志的关联路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
   */
  public road.data.proto.ReferencePath getReferencePath(int index) {
    return referencePath_.get(index);
  }
  /**
   * <pre>
   *可选，道路交通事件和标志的关联路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
   */
  public road.data.proto.ReferencePathOrBuilder getReferencePathOrBuilder(
      int index) {
    return referencePath_.get(index);
  }

  public static final int REFERENCELINKS_FIELD_NUMBER = 13;
  private java.util.List<road.data.proto.ReferenceLink> referenceLinks_;
  /**
   * <pre>
   *可选，相关车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
   */
  public java.util.List<road.data.proto.ReferenceLink> getReferenceLinksList() {
    return referenceLinks_;
  }
  /**
   * <pre>
   *可选，相关车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
   */
  public java.util.List<? extends road.data.proto.ReferenceLinkOrBuilder> 
      getReferenceLinksOrBuilderList() {
    return referenceLinks_;
  }
  /**
   * <pre>
   *可选，相关车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
   */
  public int getReferenceLinksCount() {
    return referenceLinks_.size();
  }
  /**
   * <pre>
   *可选，相关车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
   */
  public road.data.proto.ReferenceLink getReferenceLinks(int index) {
    return referenceLinks_.get(index);
  }
  /**
   * <pre>
   *可选，相关车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
   */
  public road.data.proto.ReferenceLinkOrBuilder getReferenceLinksOrBuilder(
      int index) {
    return referenceLinks_.get(index);
  }

  public static final int EVENTOBJID_FIELD_NUMBER = 14;
  private java.util.List<road.data.proto.ObjIdValue> eventObjId_;
  /**
   * <pre>
   *可选，参与到交通事件中的物体Id
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
   */
  public java.util.List<road.data.proto.ObjIdValue> getEventObjIdList() {
    return eventObjId_;
  }
  /**
   * <pre>
   *可选，参与到交通事件中的物体Id
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
   */
  public java.util.List<? extends road.data.proto.ObjIdValueOrBuilder> 
      getEventObjIdOrBuilderList() {
    return eventObjId_;
  }
  /**
   * <pre>
   *可选，参与到交通事件中的物体Id
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
   */
  public int getEventObjIdCount() {
    return eventObjId_.size();
  }
  /**
   * <pre>
   *可选，参与到交通事件中的物体Id
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
   */
  public road.data.proto.ObjIdValue getEventObjId(int index) {
    return eventObjId_.get(index);
  }
  /**
   * <pre>
   *可选，参与到交通事件中的物体Id
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
   */
  public road.data.proto.ObjIdValueOrBuilder getEventObjIdOrBuilder(
      int index) {
    return eventObjId_.get(index);
  }

  public static final int EVENTCONFID_FIELD_NUMBER = 15;
  private int eventConfid_;
  /**
   * <pre>
   *可选，定义事件的置信度；分辨率为0.005。指示事件源设置的事件置信度 检测到的事件在某个地方真实程度的概率/置信度，以帮助车辆确定是否信任接收到的信息。
   * </pre>
   *
   * <code>int32 eventConfid = 15;</code>
   */
  public int getEventConfid() {
    return eventConfid_;
  }

  public static final int EVENTIMAGES_FIELD_NUMBER = 16;
  private volatile java.lang.Object eventImages_;
  /**
   * <pre>
   *可选，事件抓拍图片在云端的相对路径列表，id串列表，以“,”分割
   * </pre>
   *
   * <code>string eventImages = 16;</code>
   */
  public java.lang.String getEventImages() {
    java.lang.Object ref = eventImages_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      eventImages_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，事件抓拍图片在云端的相对路径列表，id串列表，以“,”分割
   * </pre>
   *
   * <code>string eventImages = 16;</code>
   */
  public com.google.protobuf.ByteString
      getEventImagesBytes() {
    java.lang.Object ref = eventImages_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      eventImages_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int EVENTVIDEOS_FIELD_NUMBER = 17;
  private volatile java.lang.Object eventVideos_;
  /**
   * <pre>
   *可选，事件抓拍视频在云端的相对路径列表，id串列表，以“,”分割
   * </pre>
   *
   * <code>string eventVideos = 17;</code>
   */
  public java.lang.String getEventVideos() {
    java.lang.Object ref = eventVideos_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      eventVideos_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，事件抓拍视频在云端的相对路径列表，id串列表，以“,”分割
   * </pre>
   *
   * <code>string eventVideos = 17;</code>
   */
  public com.google.protobuf.ByteString
      getEventVideosBytes() {
    java.lang.Object ref = eventVideos_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      eventVideos_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SESSIONID_FIELD_NUMBER = 18;
  private long sessionId_;
  /**
   * <pre>
   *会话id
   * </pre>
   *
   * <code>uint64 sessionId = 18;</code>
   */
  public long getSessionId() {
    return sessionId_;
  }

  public static final int ID_FIELD_NUMBER = 19;
  private long id_;
  /**
   * <pre>
   *全局唯一ID，利用雪花算法生成
   * </pre>
   *
   * <code>uint64 id = 19;</code>
   */
  public long getId() {
    return id_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (rteId_ != 0) {
      output.writeUInt32(1, rteId_);
    }
    if (rteType_ != 0) {
      output.writeUInt32(2, rteType_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, description_);
    }
    if (eventSource_ != road.data.proto.RteData.EventSource.UNKNOWN_EVENT_SOURCE.getNumber()) {
      output.writeEnum(4, eventSource_);
    }
    if (dataSource_ != road.data.proto.DataSource.DATA_SOURCE_UNKNOWN.getNumber()) {
      output.writeEnum(5, dataSource_);
    }
    if (!getDeviceIdListBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, deviceIdList_);
    }
    if (rtePos_ != null) {
      output.writeMessage(7, getRtePos());
    }
    if (mapLocation_ != null) {
      output.writeMessage(8, getMapLocation());
    }
    if (eventRadius_ != 0) {
      output.writeUInt32(9, eventRadius_);
    }
    if (timeDetails_ != null) {
      output.writeMessage(10, getTimeDetails());
    }
    if (!getPriorityBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, priority_);
    }
    for (int i = 0; i < referencePath_.size(); i++) {
      output.writeMessage(12, referencePath_.get(i));
    }
    for (int i = 0; i < referenceLinks_.size(); i++) {
      output.writeMessage(13, referenceLinks_.get(i));
    }
    for (int i = 0; i < eventObjId_.size(); i++) {
      output.writeMessage(14, eventObjId_.get(i));
    }
    if (eventConfid_ != 0) {
      output.writeInt32(15, eventConfid_);
    }
    if (!getEventImagesBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 16, eventImages_);
    }
    if (!getEventVideosBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 17, eventVideos_);
    }
    if (sessionId_ != 0L) {
      output.writeUInt64(18, sessionId_);
    }
    if (id_ != 0L) {
      output.writeUInt64(19, id_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (rteId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, rteId_);
    }
    if (rteType_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, rteType_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, description_);
    }
    if (eventSource_ != road.data.proto.RteData.EventSource.UNKNOWN_EVENT_SOURCE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(4, eventSource_);
    }
    if (dataSource_ != road.data.proto.DataSource.DATA_SOURCE_UNKNOWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(5, dataSource_);
    }
    if (!getDeviceIdListBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, deviceIdList_);
    }
    if (rtePos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getRtePos());
    }
    if (mapLocation_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, getMapLocation());
    }
    if (eventRadius_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(9, eventRadius_);
    }
    if (timeDetails_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(10, getTimeDetails());
    }
    if (!getPriorityBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, priority_);
    }
    for (int i = 0; i < referencePath_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(12, referencePath_.get(i));
    }
    for (int i = 0; i < referenceLinks_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(13, referenceLinks_.get(i));
    }
    for (int i = 0; i < eventObjId_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(14, eventObjId_.get(i));
    }
    if (eventConfid_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(15, eventConfid_);
    }
    if (!getEventImagesBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, eventImages_);
    }
    if (!getEventVideosBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, eventVideos_);
    }
    if (sessionId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(18, sessionId_);
    }
    if (id_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(19, id_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.RteData)) {
      return super.equals(obj);
    }
    road.data.proto.RteData other = (road.data.proto.RteData) obj;

    if (getRteId()
        != other.getRteId()) return false;
    if (getRteType()
        != other.getRteType()) return false;
    if (!getDescription()
        .equals(other.getDescription())) return false;
    if (eventSource_ != other.eventSource_) return false;
    if (dataSource_ != other.dataSource_) return false;
    if (!getDeviceIdList()
        .equals(other.getDeviceIdList())) return false;
    if (hasRtePos() != other.hasRtePos()) return false;
    if (hasRtePos()) {
      if (!getRtePos()
          .equals(other.getRtePos())) return false;
    }
    if (hasMapLocation() != other.hasMapLocation()) return false;
    if (hasMapLocation()) {
      if (!getMapLocation()
          .equals(other.getMapLocation())) return false;
    }
    if (getEventRadius()
        != other.getEventRadius()) return false;
    if (hasTimeDetails() != other.hasTimeDetails()) return false;
    if (hasTimeDetails()) {
      if (!getTimeDetails()
          .equals(other.getTimeDetails())) return false;
    }
    if (!getPriority()
        .equals(other.getPriority())) return false;
    if (!getReferencePathList()
        .equals(other.getReferencePathList())) return false;
    if (!getReferenceLinksList()
        .equals(other.getReferenceLinksList())) return false;
    if (!getEventObjIdList()
        .equals(other.getEventObjIdList())) return false;
    if (getEventConfid()
        != other.getEventConfid()) return false;
    if (!getEventImages()
        .equals(other.getEventImages())) return false;
    if (!getEventVideos()
        .equals(other.getEventVideos())) return false;
    if (getSessionId()
        != other.getSessionId()) return false;
    if (getId()
        != other.getId()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + RTEID_FIELD_NUMBER;
    hash = (53 * hash) + getRteId();
    hash = (37 * hash) + RTETYPE_FIELD_NUMBER;
    hash = (53 * hash) + getRteType();
    hash = (37 * hash) + DESCRIPTION_FIELD_NUMBER;
    hash = (53 * hash) + getDescription().hashCode();
    hash = (37 * hash) + EVENTSOURCE_FIELD_NUMBER;
    hash = (53 * hash) + eventSource_;
    hash = (37 * hash) + DATASOURCE_FIELD_NUMBER;
    hash = (53 * hash) + dataSource_;
    hash = (37 * hash) + DEVICEIDLIST_FIELD_NUMBER;
    hash = (53 * hash) + getDeviceIdList().hashCode();
    if (hasRtePos()) {
      hash = (37 * hash) + RTEPOS_FIELD_NUMBER;
      hash = (53 * hash) + getRtePos().hashCode();
    }
    if (hasMapLocation()) {
      hash = (37 * hash) + MAPLOCATION_FIELD_NUMBER;
      hash = (53 * hash) + getMapLocation().hashCode();
    }
    hash = (37 * hash) + EVENTRADIUS_FIELD_NUMBER;
    hash = (53 * hash) + getEventRadius();
    if (hasTimeDetails()) {
      hash = (37 * hash) + TIMEDETAILS_FIELD_NUMBER;
      hash = (53 * hash) + getTimeDetails().hashCode();
    }
    hash = (37 * hash) + PRIORITY_FIELD_NUMBER;
    hash = (53 * hash) + getPriority().hashCode();
    if (getReferencePathCount() > 0) {
      hash = (37 * hash) + REFERENCEPATH_FIELD_NUMBER;
      hash = (53 * hash) + getReferencePathList().hashCode();
    }
    if (getReferenceLinksCount() > 0) {
      hash = (37 * hash) + REFERENCELINKS_FIELD_NUMBER;
      hash = (53 * hash) + getReferenceLinksList().hashCode();
    }
    if (getEventObjIdCount() > 0) {
      hash = (37 * hash) + EVENTOBJID_FIELD_NUMBER;
      hash = (53 * hash) + getEventObjIdList().hashCode();
    }
    hash = (37 * hash) + EVENTCONFID_FIELD_NUMBER;
    hash = (53 * hash) + getEventConfid();
    hash = (37 * hash) + EVENTIMAGES_FIELD_NUMBER;
    hash = (53 * hash) + getEventImages().hashCode();
    hash = (37 * hash) + EVENTVIDEOS_FIELD_NUMBER;
    hash = (53 * hash) + getEventVideos().hashCode();
    hash = (37 * hash) + SESSIONID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getSessionId());
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getId());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.RteData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RteData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RteData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RteData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RteData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RteData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RteData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RteData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RteData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.RteData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RteData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RteData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.RteData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *道路交通事件信息RteData （除了RteType）
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.RteData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.RteData)
      road.data.proto.RteDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RteData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RteData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.RteData.class, road.data.proto.RteData.Builder.class);
    }

    // Construct using road.data.proto.RteData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getReferencePathFieldBuilder();
        getReferenceLinksFieldBuilder();
        getEventObjIdFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      rteId_ = 0;

      rteType_ = 0;

      description_ = "";

      eventSource_ = 0;

      dataSource_ = 0;

      deviceIdList_ = "";

      if (rtePosBuilder_ == null) {
        rtePos_ = null;
      } else {
        rtePos_ = null;
        rtePosBuilder_ = null;
      }
      if (mapLocationBuilder_ == null) {
        mapLocation_ = null;
      } else {
        mapLocation_ = null;
        mapLocationBuilder_ = null;
      }
      eventRadius_ = 0;

      if (timeDetailsBuilder_ == null) {
        timeDetails_ = null;
      } else {
        timeDetails_ = null;
        timeDetailsBuilder_ = null;
      }
      priority_ = "";

      if (referencePathBuilder_ == null) {
        referencePath_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        referencePathBuilder_.clear();
      }
      if (referenceLinksBuilder_ == null) {
        referenceLinks_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        referenceLinksBuilder_.clear();
      }
      if (eventObjIdBuilder_ == null) {
        eventObjId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
      } else {
        eventObjIdBuilder_.clear();
      }
      eventConfid_ = 0;

      eventImages_ = "";

      eventVideos_ = "";

      sessionId_ = 0L;

      id_ = 0L;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RteData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.RteData getDefaultInstanceForType() {
      return road.data.proto.RteData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.RteData build() {
      road.data.proto.RteData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.RteData buildPartial() {
      road.data.proto.RteData result = new road.data.proto.RteData(this);
      int from_bitField0_ = bitField0_;
      result.rteId_ = rteId_;
      result.rteType_ = rteType_;
      result.description_ = description_;
      result.eventSource_ = eventSource_;
      result.dataSource_ = dataSource_;
      result.deviceIdList_ = deviceIdList_;
      if (rtePosBuilder_ == null) {
        result.rtePos_ = rtePos_;
      } else {
        result.rtePos_ = rtePosBuilder_.build();
      }
      if (mapLocationBuilder_ == null) {
        result.mapLocation_ = mapLocation_;
      } else {
        result.mapLocation_ = mapLocationBuilder_.build();
      }
      result.eventRadius_ = eventRadius_;
      if (timeDetailsBuilder_ == null) {
        result.timeDetails_ = timeDetails_;
      } else {
        result.timeDetails_ = timeDetailsBuilder_.build();
      }
      result.priority_ = priority_;
      if (referencePathBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          referencePath_ = java.util.Collections.unmodifiableList(referencePath_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.referencePath_ = referencePath_;
      } else {
        result.referencePath_ = referencePathBuilder_.build();
      }
      if (referenceLinksBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          referenceLinks_ = java.util.Collections.unmodifiableList(referenceLinks_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.referenceLinks_ = referenceLinks_;
      } else {
        result.referenceLinks_ = referenceLinksBuilder_.build();
      }
      if (eventObjIdBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          eventObjId_ = java.util.Collections.unmodifiableList(eventObjId_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.eventObjId_ = eventObjId_;
      } else {
        result.eventObjId_ = eventObjIdBuilder_.build();
      }
      result.eventConfid_ = eventConfid_;
      result.eventImages_ = eventImages_;
      result.eventVideos_ = eventVideos_;
      result.sessionId_ = sessionId_;
      result.id_ = id_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.RteData) {
        return mergeFrom((road.data.proto.RteData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.RteData other) {
      if (other == road.data.proto.RteData.getDefaultInstance()) return this;
      if (other.getRteId() != 0) {
        setRteId(other.getRteId());
      }
      if (other.getRteType() != 0) {
        setRteType(other.getRteType());
      }
      if (!other.getDescription().isEmpty()) {
        description_ = other.description_;
        onChanged();
      }
      if (other.eventSource_ != 0) {
        setEventSourceValue(other.getEventSourceValue());
      }
      if (other.dataSource_ != 0) {
        setDataSourceValue(other.getDataSourceValue());
      }
      if (!other.getDeviceIdList().isEmpty()) {
        deviceIdList_ = other.deviceIdList_;
        onChanged();
      }
      if (other.hasRtePos()) {
        mergeRtePos(other.getRtePos());
      }
      if (other.hasMapLocation()) {
        mergeMapLocation(other.getMapLocation());
      }
      if (other.getEventRadius() != 0) {
        setEventRadius(other.getEventRadius());
      }
      if (other.hasTimeDetails()) {
        mergeTimeDetails(other.getTimeDetails());
      }
      if (!other.getPriority().isEmpty()) {
        priority_ = other.priority_;
        onChanged();
      }
      if (referencePathBuilder_ == null) {
        if (!other.referencePath_.isEmpty()) {
          if (referencePath_.isEmpty()) {
            referencePath_ = other.referencePath_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureReferencePathIsMutable();
            referencePath_.addAll(other.referencePath_);
          }
          onChanged();
        }
      } else {
        if (!other.referencePath_.isEmpty()) {
          if (referencePathBuilder_.isEmpty()) {
            referencePathBuilder_.dispose();
            referencePathBuilder_ = null;
            referencePath_ = other.referencePath_;
            bitField0_ = (bitField0_ & ~0x00000001);
            referencePathBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getReferencePathFieldBuilder() : null;
          } else {
            referencePathBuilder_.addAllMessages(other.referencePath_);
          }
        }
      }
      if (referenceLinksBuilder_ == null) {
        if (!other.referenceLinks_.isEmpty()) {
          if (referenceLinks_.isEmpty()) {
            referenceLinks_ = other.referenceLinks_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureReferenceLinksIsMutable();
            referenceLinks_.addAll(other.referenceLinks_);
          }
          onChanged();
        }
      } else {
        if (!other.referenceLinks_.isEmpty()) {
          if (referenceLinksBuilder_.isEmpty()) {
            referenceLinksBuilder_.dispose();
            referenceLinksBuilder_ = null;
            referenceLinks_ = other.referenceLinks_;
            bitField0_ = (bitField0_ & ~0x00000002);
            referenceLinksBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getReferenceLinksFieldBuilder() : null;
          } else {
            referenceLinksBuilder_.addAllMessages(other.referenceLinks_);
          }
        }
      }
      if (eventObjIdBuilder_ == null) {
        if (!other.eventObjId_.isEmpty()) {
          if (eventObjId_.isEmpty()) {
            eventObjId_ = other.eventObjId_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureEventObjIdIsMutable();
            eventObjId_.addAll(other.eventObjId_);
          }
          onChanged();
        }
      } else {
        if (!other.eventObjId_.isEmpty()) {
          if (eventObjIdBuilder_.isEmpty()) {
            eventObjIdBuilder_.dispose();
            eventObjIdBuilder_ = null;
            eventObjId_ = other.eventObjId_;
            bitField0_ = (bitField0_ & ~0x00000004);
            eventObjIdBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getEventObjIdFieldBuilder() : null;
          } else {
            eventObjIdBuilder_.addAllMessages(other.eventObjId_);
          }
        }
      }
      if (other.getEventConfid() != 0) {
        setEventConfid(other.getEventConfid());
      }
      if (!other.getEventImages().isEmpty()) {
        eventImages_ = other.eventImages_;
        onChanged();
      }
      if (!other.getEventVideos().isEmpty()) {
        eventVideos_ = other.eventVideos_;
        onChanged();
      }
      if (other.getSessionId() != 0L) {
        setSessionId(other.getSessionId());
      }
      if (other.getId() != 0L) {
        setId(other.getId());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.RteData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.RteData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int rteId_ ;
    /**
     * <pre>
     * 每个事件有个ID编号，不同事件编号不同;ID循环编号
     * </pre>
     *
     * <code>uint32 rteId = 1;</code>
     */
    public int getRteId() {
      return rteId_;
    }
    /**
     * <pre>
     * 每个事件有个ID编号，不同事件编号不同;ID循环编号
     * </pre>
     *
     * <code>uint32 rteId = 1;</code>
     */
    public Builder setRteId(int value) {
      
      rteId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 每个事件有个ID编号，不同事件编号不同;ID循环编号
     * </pre>
     *
     * <code>uint32 rteId = 1;</code>
     */
    public Builder clearRteId() {
      
      rteId_ = 0;
      onChanged();
      return this;
    }

    private int rteType_ ;
    /**
     * <pre>
     * 道路的事件信息的类型，参考事件类型章节
     * </pre>
     *
     * <code>uint32 rteType = 2;</code>
     */
    public int getRteType() {
      return rteType_;
    }
    /**
     * <pre>
     * 道路的事件信息的类型，参考事件类型章节
     * </pre>
     *
     * <code>uint32 rteType = 2;</code>
     */
    public Builder setRteType(int value) {
      
      rteType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 道路的事件信息的类型，参考事件类型章节
     * </pre>
     *
     * <code>uint32 rteType = 2;</code>
     */
    public Builder clearRteType() {
      
      rteType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object description_ = "";
    /**
     * <pre>
     *可选，描述，json字串
     * </pre>
     *
     * <code>string description = 3;</code>
     */
    public java.lang.String getDescription() {
      java.lang.Object ref = description_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        description_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，描述，json字串
     * </pre>
     *
     * <code>string description = 3;</code>
     */
    public com.google.protobuf.ByteString
        getDescriptionBytes() {
      java.lang.Object ref = description_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        description_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，描述，json字串
     * </pre>
     *
     * <code>string description = 3;</code>
     */
    public Builder setDescription(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      description_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，描述，json字串
     * </pre>
     *
     * <code>string description = 3;</code>
     */
    public Builder clearDescription() {
      
      description_ = getDefaultInstance().getDescription();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，描述，json字串
     * </pre>
     *
     * <code>string description = 3;</code>
     */
    public Builder setDescriptionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      description_ = value;
      onChanged();
      return this;
    }

    private int eventSource_ = 0;
    /**
     * <pre>
     *事件信息来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RteData.EventSource eventSource = 4;</code>
     */
    public int getEventSourceValue() {
      return eventSource_;
    }
    /**
     * <pre>
     *事件信息来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RteData.EventSource eventSource = 4;</code>
     */
    public Builder setEventSourceValue(int value) {
      eventSource_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *事件信息来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RteData.EventSource eventSource = 4;</code>
     */
    public road.data.proto.RteData.EventSource getEventSource() {
      @SuppressWarnings("deprecation")
      road.data.proto.RteData.EventSource result = road.data.proto.RteData.EventSource.valueOf(eventSource_);
      return result == null ? road.data.proto.RteData.EventSource.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *事件信息来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RteData.EventSource eventSource = 4;</code>
     */
    public Builder setEventSource(road.data.proto.RteData.EventSource value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      eventSource_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *事件信息来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RteData.EventSource eventSource = 4;</code>
     */
    public Builder clearEventSource() {
      
      eventSource_ = 0;
      onChanged();
      return this;
    }

    private int dataSource_ = 0;
    /**
     * <pre>
     *路侧数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 5;</code>
     */
    public int getDataSourceValue() {
      return dataSource_;
    }
    /**
     * <pre>
     *路侧数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 5;</code>
     */
    public Builder setDataSourceValue(int value) {
      dataSource_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *路侧数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 5;</code>
     */
    public road.data.proto.DataSource getDataSource() {
      @SuppressWarnings("deprecation")
      road.data.proto.DataSource result = road.data.proto.DataSource.valueOf(dataSource_);
      return result == null ? road.data.proto.DataSource.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *路侧数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 5;</code>
     */
    public Builder setDataSource(road.data.proto.DataSource value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      dataSource_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *路侧数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 5;</code>
     */
    public Builder clearDataSource() {
      
      dataSource_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object deviceIdList_ = "";
    /**
     * <pre>
     *检测来源设备id，json数组
     * </pre>
     *
     * <code>string deviceIdList = 6;</code>
     */
    public java.lang.String getDeviceIdList() {
      java.lang.Object ref = deviceIdList_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceIdList_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *检测来源设备id，json数组
     * </pre>
     *
     * <code>string deviceIdList = 6;</code>
     */
    public com.google.protobuf.ByteString
        getDeviceIdListBytes() {
      java.lang.Object ref = deviceIdList_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceIdList_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *检测来源设备id，json数组
     * </pre>
     *
     * <code>string deviceIdList = 6;</code>
     */
    public Builder setDeviceIdList(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deviceIdList_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *检测来源设备id，json数组
     * </pre>
     *
     * <code>string deviceIdList = 6;</code>
     */
    public Builder clearDeviceIdList() {
      
      deviceIdList_ = getDefaultInstance().getDeviceIdList();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *检测来源设备id，json数组
     * </pre>
     *
     * <code>string deviceIdList = 6;</code>
     */
    public Builder setDeviceIdListBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deviceIdList_ = value;
      onChanged();
      return this;
    }

    private road.data.proto.Position3D rtePos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> rtePosBuilder_;
    /**
     * <pre>
     * 定义事件的经纬度和高，绝对位置。    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
     */
    public boolean hasRtePos() {
      return rtePosBuilder_ != null || rtePos_ != null;
    }
    /**
     * <pre>
     * 定义事件的经纬度和高，绝对位置。    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
     */
    public road.data.proto.Position3D getRtePos() {
      if (rtePosBuilder_ == null) {
        return rtePos_ == null ? road.data.proto.Position3D.getDefaultInstance() : rtePos_;
      } else {
        return rtePosBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 定义事件的经纬度和高，绝对位置。    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
     */
    public Builder setRtePos(road.data.proto.Position3D value) {
      if (rtePosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        rtePos_ = value;
        onChanged();
      } else {
        rtePosBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 定义事件的经纬度和高，绝对位置。    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
     */
    public Builder setRtePos(
        road.data.proto.Position3D.Builder builderForValue) {
      if (rtePosBuilder_ == null) {
        rtePos_ = builderForValue.build();
        onChanged();
      } else {
        rtePosBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 定义事件的经纬度和高，绝对位置。    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
     */
    public Builder mergeRtePos(road.data.proto.Position3D value) {
      if (rtePosBuilder_ == null) {
        if (rtePos_ != null) {
          rtePos_ =
            road.data.proto.Position3D.newBuilder(rtePos_).mergeFrom(value).buildPartial();
        } else {
          rtePos_ = value;
        }
        onChanged();
      } else {
        rtePosBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 定义事件的经纬度和高，绝对位置。    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
     */
    public Builder clearRtePos() {
      if (rtePosBuilder_ == null) {
        rtePos_ = null;
        onChanged();
      } else {
        rtePos_ = null;
        rtePosBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 定义事件的经纬度和高，绝对位置。    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
     */
    public road.data.proto.Position3D.Builder getRtePosBuilder() {
      
      onChanged();
      return getRtePosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 定义事件的经纬度和高，绝对位置。    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
     */
    public road.data.proto.Position3DOrBuilder getRtePosOrBuilder() {
      if (rtePosBuilder_ != null) {
        return rtePosBuilder_.getMessageOrBuilder();
      } else {
        return rtePos_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : rtePos_;
      }
    }
    /**
     * <pre>
     * 定义事件的经纬度和高，绝对位置。    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getRtePosFieldBuilder() {
      if (rtePosBuilder_ == null) {
        rtePosBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getRtePos(),
                getParentForChildren(),
                isClean());
        rtePos_ = null;
      }
      return rtePosBuilder_;
    }

    private road.data.proto.MapLocation mapLocation_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MapLocation, road.data.proto.MapLocation.Builder, road.data.proto.MapLocationOrBuilder> mapLocationBuilder_;
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public boolean hasMapLocation() {
      return mapLocationBuilder_ != null || mapLocation_ != null;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public road.data.proto.MapLocation getMapLocation() {
      if (mapLocationBuilder_ == null) {
        return mapLocation_ == null ? road.data.proto.MapLocation.getDefaultInstance() : mapLocation_;
      } else {
        return mapLocationBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public Builder setMapLocation(road.data.proto.MapLocation value) {
      if (mapLocationBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        mapLocation_ = value;
        onChanged();
      } else {
        mapLocationBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public Builder setMapLocation(
        road.data.proto.MapLocation.Builder builderForValue) {
      if (mapLocationBuilder_ == null) {
        mapLocation_ = builderForValue.build();
        onChanged();
      } else {
        mapLocationBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public Builder mergeMapLocation(road.data.proto.MapLocation value) {
      if (mapLocationBuilder_ == null) {
        if (mapLocation_ != null) {
          mapLocation_ =
            road.data.proto.MapLocation.newBuilder(mapLocation_).mergeFrom(value).buildPartial();
        } else {
          mapLocation_ = value;
        }
        onChanged();
      } else {
        mapLocationBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public Builder clearMapLocation() {
      if (mapLocationBuilder_ == null) {
        mapLocation_ = null;
        onChanged();
      } else {
        mapLocation_ = null;
        mapLocationBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public road.data.proto.MapLocation.Builder getMapLocationBuilder() {
      
      onChanged();
      return getMapLocationFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    public road.data.proto.MapLocationOrBuilder getMapLocationOrBuilder() {
      if (mapLocationBuilder_ != null) {
        return mapLocationBuilder_.getMessageOrBuilder();
      } else {
        return mapLocation_ == null ?
            road.data.proto.MapLocation.getDefaultInstance() : mapLocation_;
      }
    }
    /**
     * <pre>
     *可选，所在地图位置，有地图信息时填写
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MapLocation, road.data.proto.MapLocation.Builder, road.data.proto.MapLocationOrBuilder> 
        getMapLocationFieldBuilder() {
      if (mapLocationBuilder_ == null) {
        mapLocationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.MapLocation, road.data.proto.MapLocation.Builder, road.data.proto.MapLocationOrBuilder>(
                getMapLocation(),
                getParentForChildren(),
                isClean());
        mapLocation_ = null;
      }
      return mapLocationBuilder_;
    }

    private int eventRadius_ ;
    /**
     * <pre>
     * 可选，特定圆形范围的半径大小，单位0.1m，默认值2000。表示交通事件影响区域边界离中心线的垂直距离，与RefPath字段组合，共同反映该区域的宽度以覆盖实际路段。
     * </pre>
     *
     * <code>uint32 eventRadius = 9;</code>
     */
    public int getEventRadius() {
      return eventRadius_;
    }
    /**
     * <pre>
     * 可选，特定圆形范围的半径大小，单位0.1m，默认值2000。表示交通事件影响区域边界离中心线的垂直距离，与RefPath字段组合，共同反映该区域的宽度以覆盖实际路段。
     * </pre>
     *
     * <code>uint32 eventRadius = 9;</code>
     */
    public Builder setEventRadius(int value) {
      
      eventRadius_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，特定圆形范围的半径大小，单位0.1m，默认值2000。表示交通事件影响区域边界离中心线的垂直距离，与RefPath字段组合，共同反映该区域的宽度以覆盖实际路段。
     * </pre>
     *
     * <code>uint32 eventRadius = 9;</code>
     */
    public Builder clearEventRadius() {
      
      eventRadius_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.RsiTimeDetails timeDetails_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.RsiTimeDetails, road.data.proto.RsiTimeDetails.Builder, road.data.proto.RsiTimeDetailsOrBuilder> timeDetailsBuilder_;
    /**
     * <pre>
     *可选，事件始终时间    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
     */
    public boolean hasTimeDetails() {
      return timeDetailsBuilder_ != null || timeDetails_ != null;
    }
    /**
     * <pre>
     *可选，事件始终时间    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
     */
    public road.data.proto.RsiTimeDetails getTimeDetails() {
      if (timeDetailsBuilder_ == null) {
        return timeDetails_ == null ? road.data.proto.RsiTimeDetails.getDefaultInstance() : timeDetails_;
      } else {
        return timeDetailsBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，事件始终时间    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
     */
    public Builder setTimeDetails(road.data.proto.RsiTimeDetails value) {
      if (timeDetailsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        timeDetails_ = value;
        onChanged();
      } else {
        timeDetailsBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，事件始终时间    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
     */
    public Builder setTimeDetails(
        road.data.proto.RsiTimeDetails.Builder builderForValue) {
      if (timeDetailsBuilder_ == null) {
        timeDetails_ = builderForValue.build();
        onChanged();
      } else {
        timeDetailsBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，事件始终时间    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
     */
    public Builder mergeTimeDetails(road.data.proto.RsiTimeDetails value) {
      if (timeDetailsBuilder_ == null) {
        if (timeDetails_ != null) {
          timeDetails_ =
            road.data.proto.RsiTimeDetails.newBuilder(timeDetails_).mergeFrom(value).buildPartial();
        } else {
          timeDetails_ = value;
        }
        onChanged();
      } else {
        timeDetailsBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，事件始终时间    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
     */
    public Builder clearTimeDetails() {
      if (timeDetailsBuilder_ == null) {
        timeDetails_ = null;
        onChanged();
      } else {
        timeDetails_ = null;
        timeDetailsBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，事件始终时间    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
     */
    public road.data.proto.RsiTimeDetails.Builder getTimeDetailsBuilder() {
      
      onChanged();
      return getTimeDetailsFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，事件始终时间    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
     */
    public road.data.proto.RsiTimeDetailsOrBuilder getTimeDetailsOrBuilder() {
      if (timeDetailsBuilder_ != null) {
        return timeDetailsBuilder_.getMessageOrBuilder();
      } else {
        return timeDetails_ == null ?
            road.data.proto.RsiTimeDetails.getDefaultInstance() : timeDetails_;
      }
    }
    /**
     * <pre>
     *可选，事件始终时间    
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.RsiTimeDetails, road.data.proto.RsiTimeDetails.Builder, road.data.proto.RsiTimeDetailsOrBuilder> 
        getTimeDetailsFieldBuilder() {
      if (timeDetailsBuilder_ == null) {
        timeDetailsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.RsiTimeDetails, road.data.proto.RsiTimeDetails.Builder, road.data.proto.RsiTimeDetailsOrBuilder>(
                getTimeDetails(),
                getParentForChildren(),
                isClean());
        timeDetails_ = null;
      }
      return timeDetailsBuilder_;
    }

    private java.lang.Object priority_ = "";
    /**
     * <pre>
     *可选， 事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
     * </pre>
     *
     * <code>string priority = 11;</code>
     */
    public java.lang.String getPriority() {
      java.lang.Object ref = priority_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        priority_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选， 事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
     * </pre>
     *
     * <code>string priority = 11;</code>
     */
    public com.google.protobuf.ByteString
        getPriorityBytes() {
      java.lang.Object ref = priority_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        priority_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选， 事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
     * </pre>
     *
     * <code>string priority = 11;</code>
     */
    public Builder setPriority(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      priority_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选， 事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
     * </pre>
     *
     * <code>string priority = 11;</code>
     */
    public Builder clearPriority() {
      
      priority_ = getDefaultInstance().getPriority();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选， 事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
     * </pre>
     *
     * <code>string priority = 11;</code>
     */
    public Builder setPriorityBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      priority_ = value;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.ReferencePath> referencePath_ =
      java.util.Collections.emptyList();
    private void ensureReferencePathIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        referencePath_ = new java.util.ArrayList<road.data.proto.ReferencePath>(referencePath_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder> referencePathBuilder_;

    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public java.util.List<road.data.proto.ReferencePath> getReferencePathList() {
      if (referencePathBuilder_ == null) {
        return java.util.Collections.unmodifiableList(referencePath_);
      } else {
        return referencePathBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public int getReferencePathCount() {
      if (referencePathBuilder_ == null) {
        return referencePath_.size();
      } else {
        return referencePathBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public road.data.proto.ReferencePath getReferencePath(int index) {
      if (referencePathBuilder_ == null) {
        return referencePath_.get(index);
      } else {
        return referencePathBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public Builder setReferencePath(
        int index, road.data.proto.ReferencePath value) {
      if (referencePathBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureReferencePathIsMutable();
        referencePath_.set(index, value);
        onChanged();
      } else {
        referencePathBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public Builder setReferencePath(
        int index, road.data.proto.ReferencePath.Builder builderForValue) {
      if (referencePathBuilder_ == null) {
        ensureReferencePathIsMutable();
        referencePath_.set(index, builderForValue.build());
        onChanged();
      } else {
        referencePathBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public Builder addReferencePath(road.data.proto.ReferencePath value) {
      if (referencePathBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureReferencePathIsMutable();
        referencePath_.add(value);
        onChanged();
      } else {
        referencePathBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public Builder addReferencePath(
        int index, road.data.proto.ReferencePath value) {
      if (referencePathBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureReferencePathIsMutable();
        referencePath_.add(index, value);
        onChanged();
      } else {
        referencePathBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public Builder addReferencePath(
        road.data.proto.ReferencePath.Builder builderForValue) {
      if (referencePathBuilder_ == null) {
        ensureReferencePathIsMutable();
        referencePath_.add(builderForValue.build());
        onChanged();
      } else {
        referencePathBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public Builder addReferencePath(
        int index, road.data.proto.ReferencePath.Builder builderForValue) {
      if (referencePathBuilder_ == null) {
        ensureReferencePathIsMutable();
        referencePath_.add(index, builderForValue.build());
        onChanged();
      } else {
        referencePathBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public Builder addAllReferencePath(
        java.lang.Iterable<? extends road.data.proto.ReferencePath> values) {
      if (referencePathBuilder_ == null) {
        ensureReferencePathIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, referencePath_);
        onChanged();
      } else {
        referencePathBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public Builder clearReferencePath() {
      if (referencePathBuilder_ == null) {
        referencePath_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        referencePathBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public Builder removeReferencePath(int index) {
      if (referencePathBuilder_ == null) {
        ensureReferencePathIsMutable();
        referencePath_.remove(index);
        onChanged();
      } else {
        referencePathBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public road.data.proto.ReferencePath.Builder getReferencePathBuilder(
        int index) {
      return getReferencePathFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public road.data.proto.ReferencePathOrBuilder getReferencePathOrBuilder(
        int index) {
      if (referencePathBuilder_ == null) {
        return referencePath_.get(index);  } else {
        return referencePathBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public java.util.List<? extends road.data.proto.ReferencePathOrBuilder> 
         getReferencePathOrBuilderList() {
      if (referencePathBuilder_ != null) {
        return referencePathBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(referencePath_);
      }
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public road.data.proto.ReferencePath.Builder addReferencePathBuilder() {
      return getReferencePathFieldBuilder().addBuilder(
          road.data.proto.ReferencePath.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public road.data.proto.ReferencePath.Builder addReferencePathBuilder(
        int index) {
      return getReferencePathFieldBuilder().addBuilder(
          index, road.data.proto.ReferencePath.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，道路交通事件和标志的关联路径
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
     */
    public java.util.List<road.data.proto.ReferencePath.Builder> 
         getReferencePathBuilderList() {
      return getReferencePathFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder> 
        getReferencePathFieldBuilder() {
      if (referencePathBuilder_ == null) {
        referencePathBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder>(
                referencePath_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        referencePath_ = null;
      }
      return referencePathBuilder_;
    }

    private java.util.List<road.data.proto.ReferenceLink> referenceLinks_ =
      java.util.Collections.emptyList();
    private void ensureReferenceLinksIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        referenceLinks_ = new java.util.ArrayList<road.data.proto.ReferenceLink>(referenceLinks_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder> referenceLinksBuilder_;

    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public java.util.List<road.data.proto.ReferenceLink> getReferenceLinksList() {
      if (referenceLinksBuilder_ == null) {
        return java.util.Collections.unmodifiableList(referenceLinks_);
      } else {
        return referenceLinksBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public int getReferenceLinksCount() {
      if (referenceLinksBuilder_ == null) {
        return referenceLinks_.size();
      } else {
        return referenceLinksBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public road.data.proto.ReferenceLink getReferenceLinks(int index) {
      if (referenceLinksBuilder_ == null) {
        return referenceLinks_.get(index);
      } else {
        return referenceLinksBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public Builder setReferenceLinks(
        int index, road.data.proto.ReferenceLink value) {
      if (referenceLinksBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureReferenceLinksIsMutable();
        referenceLinks_.set(index, value);
        onChanged();
      } else {
        referenceLinksBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public Builder setReferenceLinks(
        int index, road.data.proto.ReferenceLink.Builder builderForValue) {
      if (referenceLinksBuilder_ == null) {
        ensureReferenceLinksIsMutable();
        referenceLinks_.set(index, builderForValue.build());
        onChanged();
      } else {
        referenceLinksBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public Builder addReferenceLinks(road.data.proto.ReferenceLink value) {
      if (referenceLinksBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureReferenceLinksIsMutable();
        referenceLinks_.add(value);
        onChanged();
      } else {
        referenceLinksBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public Builder addReferenceLinks(
        int index, road.data.proto.ReferenceLink value) {
      if (referenceLinksBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureReferenceLinksIsMutable();
        referenceLinks_.add(index, value);
        onChanged();
      } else {
        referenceLinksBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public Builder addReferenceLinks(
        road.data.proto.ReferenceLink.Builder builderForValue) {
      if (referenceLinksBuilder_ == null) {
        ensureReferenceLinksIsMutable();
        referenceLinks_.add(builderForValue.build());
        onChanged();
      } else {
        referenceLinksBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public Builder addReferenceLinks(
        int index, road.data.proto.ReferenceLink.Builder builderForValue) {
      if (referenceLinksBuilder_ == null) {
        ensureReferenceLinksIsMutable();
        referenceLinks_.add(index, builderForValue.build());
        onChanged();
      } else {
        referenceLinksBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public Builder addAllReferenceLinks(
        java.lang.Iterable<? extends road.data.proto.ReferenceLink> values) {
      if (referenceLinksBuilder_ == null) {
        ensureReferenceLinksIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, referenceLinks_);
        onChanged();
      } else {
        referenceLinksBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public Builder clearReferenceLinks() {
      if (referenceLinksBuilder_ == null) {
        referenceLinks_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        referenceLinksBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public Builder removeReferenceLinks(int index) {
      if (referenceLinksBuilder_ == null) {
        ensureReferenceLinksIsMutable();
        referenceLinks_.remove(index);
        onChanged();
      } else {
        referenceLinksBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public road.data.proto.ReferenceLink.Builder getReferenceLinksBuilder(
        int index) {
      return getReferenceLinksFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public road.data.proto.ReferenceLinkOrBuilder getReferenceLinksOrBuilder(
        int index) {
      if (referenceLinksBuilder_ == null) {
        return referenceLinks_.get(index);  } else {
        return referenceLinksBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public java.util.List<? extends road.data.proto.ReferenceLinkOrBuilder> 
         getReferenceLinksOrBuilderList() {
      if (referenceLinksBuilder_ != null) {
        return referenceLinksBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(referenceLinks_);
      }
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public road.data.proto.ReferenceLink.Builder addReferenceLinksBuilder() {
      return getReferenceLinksFieldBuilder().addBuilder(
          road.data.proto.ReferenceLink.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public road.data.proto.ReferenceLink.Builder addReferenceLinksBuilder(
        int index) {
      return getReferenceLinksFieldBuilder().addBuilder(
          index, road.data.proto.ReferenceLink.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，相关车道
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
     */
    public java.util.List<road.data.proto.ReferenceLink.Builder> 
         getReferenceLinksBuilderList() {
      return getReferenceLinksFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder> 
        getReferenceLinksFieldBuilder() {
      if (referenceLinksBuilder_ == null) {
        referenceLinksBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder>(
                referenceLinks_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        referenceLinks_ = null;
      }
      return referenceLinksBuilder_;
    }

    private java.util.List<road.data.proto.ObjIdValue> eventObjId_ =
      java.util.Collections.emptyList();
    private void ensureEventObjIdIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        eventObjId_ = new java.util.ArrayList<road.data.proto.ObjIdValue>(eventObjId_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ObjIdValue, road.data.proto.ObjIdValue.Builder, road.data.proto.ObjIdValueOrBuilder> eventObjIdBuilder_;

    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public java.util.List<road.data.proto.ObjIdValue> getEventObjIdList() {
      if (eventObjIdBuilder_ == null) {
        return java.util.Collections.unmodifiableList(eventObjId_);
      } else {
        return eventObjIdBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public int getEventObjIdCount() {
      if (eventObjIdBuilder_ == null) {
        return eventObjId_.size();
      } else {
        return eventObjIdBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public road.data.proto.ObjIdValue getEventObjId(int index) {
      if (eventObjIdBuilder_ == null) {
        return eventObjId_.get(index);
      } else {
        return eventObjIdBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public Builder setEventObjId(
        int index, road.data.proto.ObjIdValue value) {
      if (eventObjIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventObjIdIsMutable();
        eventObjId_.set(index, value);
        onChanged();
      } else {
        eventObjIdBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public Builder setEventObjId(
        int index, road.data.proto.ObjIdValue.Builder builderForValue) {
      if (eventObjIdBuilder_ == null) {
        ensureEventObjIdIsMutable();
        eventObjId_.set(index, builderForValue.build());
        onChanged();
      } else {
        eventObjIdBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public Builder addEventObjId(road.data.proto.ObjIdValue value) {
      if (eventObjIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventObjIdIsMutable();
        eventObjId_.add(value);
        onChanged();
      } else {
        eventObjIdBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public Builder addEventObjId(
        int index, road.data.proto.ObjIdValue value) {
      if (eventObjIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventObjIdIsMutable();
        eventObjId_.add(index, value);
        onChanged();
      } else {
        eventObjIdBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public Builder addEventObjId(
        road.data.proto.ObjIdValue.Builder builderForValue) {
      if (eventObjIdBuilder_ == null) {
        ensureEventObjIdIsMutable();
        eventObjId_.add(builderForValue.build());
        onChanged();
      } else {
        eventObjIdBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public Builder addEventObjId(
        int index, road.data.proto.ObjIdValue.Builder builderForValue) {
      if (eventObjIdBuilder_ == null) {
        ensureEventObjIdIsMutable();
        eventObjId_.add(index, builderForValue.build());
        onChanged();
      } else {
        eventObjIdBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public Builder addAllEventObjId(
        java.lang.Iterable<? extends road.data.proto.ObjIdValue> values) {
      if (eventObjIdBuilder_ == null) {
        ensureEventObjIdIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, eventObjId_);
        onChanged();
      } else {
        eventObjIdBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public Builder clearEventObjId() {
      if (eventObjIdBuilder_ == null) {
        eventObjId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        eventObjIdBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public Builder removeEventObjId(int index) {
      if (eventObjIdBuilder_ == null) {
        ensureEventObjIdIsMutable();
        eventObjId_.remove(index);
        onChanged();
      } else {
        eventObjIdBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public road.data.proto.ObjIdValue.Builder getEventObjIdBuilder(
        int index) {
      return getEventObjIdFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public road.data.proto.ObjIdValueOrBuilder getEventObjIdOrBuilder(
        int index) {
      if (eventObjIdBuilder_ == null) {
        return eventObjId_.get(index);  } else {
        return eventObjIdBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public java.util.List<? extends road.data.proto.ObjIdValueOrBuilder> 
         getEventObjIdOrBuilderList() {
      if (eventObjIdBuilder_ != null) {
        return eventObjIdBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(eventObjId_);
      }
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public road.data.proto.ObjIdValue.Builder addEventObjIdBuilder() {
      return getEventObjIdFieldBuilder().addBuilder(
          road.data.proto.ObjIdValue.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public road.data.proto.ObjIdValue.Builder addEventObjIdBuilder(
        int index) {
      return getEventObjIdFieldBuilder().addBuilder(
          index, road.data.proto.ObjIdValue.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，参与到交通事件中的物体Id
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
     */
    public java.util.List<road.data.proto.ObjIdValue.Builder> 
         getEventObjIdBuilderList() {
      return getEventObjIdFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ObjIdValue, road.data.proto.ObjIdValue.Builder, road.data.proto.ObjIdValueOrBuilder> 
        getEventObjIdFieldBuilder() {
      if (eventObjIdBuilder_ == null) {
        eventObjIdBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.ObjIdValue, road.data.proto.ObjIdValue.Builder, road.data.proto.ObjIdValueOrBuilder>(
                eventObjId_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        eventObjId_ = null;
      }
      return eventObjIdBuilder_;
    }

    private int eventConfid_ ;
    /**
     * <pre>
     *可选，定义事件的置信度；分辨率为0.005。指示事件源设置的事件置信度 检测到的事件在某个地方真实程度的概率/置信度，以帮助车辆确定是否信任接收到的信息。
     * </pre>
     *
     * <code>int32 eventConfid = 15;</code>
     */
    public int getEventConfid() {
      return eventConfid_;
    }
    /**
     * <pre>
     *可选，定义事件的置信度；分辨率为0.005。指示事件源设置的事件置信度 检测到的事件在某个地方真实程度的概率/置信度，以帮助车辆确定是否信任接收到的信息。
     * </pre>
     *
     * <code>int32 eventConfid = 15;</code>
     */
    public Builder setEventConfid(int value) {
      
      eventConfid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，定义事件的置信度；分辨率为0.005。指示事件源设置的事件置信度 检测到的事件在某个地方真实程度的概率/置信度，以帮助车辆确定是否信任接收到的信息。
     * </pre>
     *
     * <code>int32 eventConfid = 15;</code>
     */
    public Builder clearEventConfid() {
      
      eventConfid_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object eventImages_ = "";
    /**
     * <pre>
     *可选，事件抓拍图片在云端的相对路径列表，id串列表，以“,”分割
     * </pre>
     *
     * <code>string eventImages = 16;</code>
     */
    public java.lang.String getEventImages() {
      java.lang.Object ref = eventImages_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        eventImages_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，事件抓拍图片在云端的相对路径列表，id串列表，以“,”分割
     * </pre>
     *
     * <code>string eventImages = 16;</code>
     */
    public com.google.protobuf.ByteString
        getEventImagesBytes() {
      java.lang.Object ref = eventImages_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        eventImages_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，事件抓拍图片在云端的相对路径列表，id串列表，以“,”分割
     * </pre>
     *
     * <code>string eventImages = 16;</code>
     */
    public Builder setEventImages(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      eventImages_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，事件抓拍图片在云端的相对路径列表，id串列表，以“,”分割
     * </pre>
     *
     * <code>string eventImages = 16;</code>
     */
    public Builder clearEventImages() {
      
      eventImages_ = getDefaultInstance().getEventImages();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，事件抓拍图片在云端的相对路径列表，id串列表，以“,”分割
     * </pre>
     *
     * <code>string eventImages = 16;</code>
     */
    public Builder setEventImagesBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      eventImages_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object eventVideos_ = "";
    /**
     * <pre>
     *可选，事件抓拍视频在云端的相对路径列表，id串列表，以“,”分割
     * </pre>
     *
     * <code>string eventVideos = 17;</code>
     */
    public java.lang.String getEventVideos() {
      java.lang.Object ref = eventVideos_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        eventVideos_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，事件抓拍视频在云端的相对路径列表，id串列表，以“,”分割
     * </pre>
     *
     * <code>string eventVideos = 17;</code>
     */
    public com.google.protobuf.ByteString
        getEventVideosBytes() {
      java.lang.Object ref = eventVideos_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        eventVideos_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，事件抓拍视频在云端的相对路径列表，id串列表，以“,”分割
     * </pre>
     *
     * <code>string eventVideos = 17;</code>
     */
    public Builder setEventVideos(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      eventVideos_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，事件抓拍视频在云端的相对路径列表，id串列表，以“,”分割
     * </pre>
     *
     * <code>string eventVideos = 17;</code>
     */
    public Builder clearEventVideos() {
      
      eventVideos_ = getDefaultInstance().getEventVideos();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，事件抓拍视频在云端的相对路径列表，id串列表，以“,”分割
     * </pre>
     *
     * <code>string eventVideos = 17;</code>
     */
    public Builder setEventVideosBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      eventVideos_ = value;
      onChanged();
      return this;
    }

    private long sessionId_ ;
    /**
     * <pre>
     *会话id
     * </pre>
     *
     * <code>uint64 sessionId = 18;</code>
     */
    public long getSessionId() {
      return sessionId_;
    }
    /**
     * <pre>
     *会话id
     * </pre>
     *
     * <code>uint64 sessionId = 18;</code>
     */
    public Builder setSessionId(long value) {
      
      sessionId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *会话id
     * </pre>
     *
     * <code>uint64 sessionId = 18;</code>
     */
    public Builder clearSessionId() {
      
      sessionId_ = 0L;
      onChanged();
      return this;
    }

    private long id_ ;
    /**
     * <pre>
     *全局唯一ID，利用雪花算法生成
     * </pre>
     *
     * <code>uint64 id = 19;</code>
     */
    public long getId() {
      return id_;
    }
    /**
     * <pre>
     *全局唯一ID，利用雪花算法生成
     * </pre>
     *
     * <code>uint64 id = 19;</code>
     */
    public Builder setId(long value) {
      
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *全局唯一ID，利用雪花算法生成
     * </pre>
     *
     * <code>uint64 id = 19;</code>
     */
    public Builder clearId() {
      
      id_ = 0L;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.RteData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.RteData)
  private static final road.data.proto.RteData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.RteData();
  }

  public static road.data.proto.RteData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RteData>
      PARSER = new com.google.protobuf.AbstractParser<RteData>() {
    @java.lang.Override
    public RteData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RteData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RteData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RteData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.RteData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

