// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface RteDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.RteData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 每个事件有个ID编号，不同事件编号不同;ID循环编号
   * </pre>
   *
   * <code>uint32 rteId = 1;</code>
   */
  int getRteId();

  /**
   * <pre>
   * 道路的事件信息的类型，参考事件类型章节
   * </pre>
   *
   * <code>uint32 rteType = 2;</code>
   */
  int getRteType();

  /**
   * <pre>
   *可选，描述，json字串
   * </pre>
   *
   * <code>string description = 3;</code>
   */
  java.lang.String getDescription();
  /**
   * <pre>
   *可选，描述，json字串
   * </pre>
   *
   * <code>string description = 3;</code>
   */
  com.google.protobuf.ByteString
      getDescriptionBytes();

  /**
   * <pre>
   *事件信息来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RteData.EventSource eventSource = 4;</code>
   */
  int getEventSourceValue();
  /**
   * <pre>
   *事件信息来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RteData.EventSource eventSource = 4;</code>
   */
  road.data.proto.RteData.EventSource getEventSource();

  /**
   * <pre>
   *路侧数据来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource dataSource = 5;</code>
   */
  int getDataSourceValue();
  /**
   * <pre>
   *路侧数据来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource dataSource = 5;</code>
   */
  road.data.proto.DataSource getDataSource();

  /**
   * <pre>
   *检测来源设备id，json数组
   * </pre>
   *
   * <code>string deviceIdList = 6;</code>
   */
  java.lang.String getDeviceIdList();
  /**
   * <pre>
   *检测来源设备id，json数组
   * </pre>
   *
   * <code>string deviceIdList = 6;</code>
   */
  com.google.protobuf.ByteString
      getDeviceIdListBytes();

  /**
   * <pre>
   * 定义事件的经纬度和高，绝对位置。    
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
   */
  boolean hasRtePos();
  /**
   * <pre>
   * 定义事件的经纬度和高，绝对位置。    
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
   */
  road.data.proto.Position3D getRtePos();
  /**
   * <pre>
   * 定义事件的经纬度和高，绝对位置。    
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D rtePos = 7;</code>
   */
  road.data.proto.Position3DOrBuilder getRtePosOrBuilder();

  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
   */
  boolean hasMapLocation();
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
   */
  road.data.proto.MapLocation getMapLocation();
  /**
   * <pre>
   *可选，所在地图位置，有地图信息时填写
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapLocation mapLocation = 8;</code>
   */
  road.data.proto.MapLocationOrBuilder getMapLocationOrBuilder();

  /**
   * <pre>
   * 可选，特定圆形范围的半径大小，单位0.1m，默认值2000。表示交通事件影响区域边界离中心线的垂直距离，与RefPath字段组合，共同反映该区域的宽度以覆盖实际路段。
   * </pre>
   *
   * <code>uint32 eventRadius = 9;</code>
   */
  int getEventRadius();

  /**
   * <pre>
   *可选，事件始终时间    
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
   */
  boolean hasTimeDetails();
  /**
   * <pre>
   *可选，事件始终时间    
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
   */
  road.data.proto.RsiTimeDetails getTimeDetails();
  /**
   * <pre>
   *可选，事件始终时间    
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 10;</code>
   */
  road.data.proto.RsiTimeDetailsOrBuilder getTimeDetailsOrBuilder();

  /**
   * <pre>
   *可选， 事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
   * </pre>
   *
   * <code>string priority = 11;</code>
   */
  java.lang.String getPriority();
  /**
   * <pre>
   *可选， 事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
   * </pre>
   *
   * <code>string priority = 11;</code>
   */
  com.google.protobuf.ByteString
      getPriorityBytes();

  /**
   * <pre>
   *可选，道路交通事件和标志的关联路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
   */
  java.util.List<road.data.proto.ReferencePath> 
      getReferencePathList();
  /**
   * <pre>
   *可选，道路交通事件和标志的关联路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
   */
  road.data.proto.ReferencePath getReferencePath(int index);
  /**
   * <pre>
   *可选，道路交通事件和标志的关联路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
   */
  int getReferencePathCount();
  /**
   * <pre>
   *可选，道路交通事件和标志的关联路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
   */
  java.util.List<? extends road.data.proto.ReferencePathOrBuilder> 
      getReferencePathOrBuilderList();
  /**
   * <pre>
   *可选，道路交通事件和标志的关联路径
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath referencePath = 12;</code>
   */
  road.data.proto.ReferencePathOrBuilder getReferencePathOrBuilder(
      int index);

  /**
   * <pre>
   *可选，相关车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
   */
  java.util.List<road.data.proto.ReferenceLink> 
      getReferenceLinksList();
  /**
   * <pre>
   *可选，相关车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
   */
  road.data.proto.ReferenceLink getReferenceLinks(int index);
  /**
   * <pre>
   *可选，相关车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
   */
  int getReferenceLinksCount();
  /**
   * <pre>
   *可选，相关车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
   */
  java.util.List<? extends road.data.proto.ReferenceLinkOrBuilder> 
      getReferenceLinksOrBuilderList();
  /**
   * <pre>
   *可选，相关车道
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink referenceLinks = 13;</code>
   */
  road.data.proto.ReferenceLinkOrBuilder getReferenceLinksOrBuilder(
      int index);

  /**
   * <pre>
   *可选，参与到交通事件中的物体Id
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
   */
  java.util.List<road.data.proto.ObjIdValue> 
      getEventObjIdList();
  /**
   * <pre>
   *可选，参与到交通事件中的物体Id
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
   */
  road.data.proto.ObjIdValue getEventObjId(int index);
  /**
   * <pre>
   *可选，参与到交通事件中的物体Id
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
   */
  int getEventObjIdCount();
  /**
   * <pre>
   *可选，参与到交通事件中的物体Id
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
   */
  java.util.List<? extends road.data.proto.ObjIdValueOrBuilder> 
      getEventObjIdOrBuilderList();
  /**
   * <pre>
   *可选，参与到交通事件中的物体Id
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ObjIdValue eventObjId = 14;</code>
   */
  road.data.proto.ObjIdValueOrBuilder getEventObjIdOrBuilder(
      int index);

  /**
   * <pre>
   *可选，定义事件的置信度；分辨率为0.005。指示事件源设置的事件置信度 检测到的事件在某个地方真实程度的概率/置信度，以帮助车辆确定是否信任接收到的信息。
   * </pre>
   *
   * <code>int32 eventConfid = 15;</code>
   */
  int getEventConfid();

  /**
   * <pre>
   *可选，事件抓拍图片在云端的相对路径列表，id串列表，以“,”分割
   * </pre>
   *
   * <code>string eventImages = 16;</code>
   */
  java.lang.String getEventImages();
  /**
   * <pre>
   *可选，事件抓拍图片在云端的相对路径列表，id串列表，以“,”分割
   * </pre>
   *
   * <code>string eventImages = 16;</code>
   */
  com.google.protobuf.ByteString
      getEventImagesBytes();

  /**
   * <pre>
   *可选，事件抓拍视频在云端的相对路径列表，id串列表，以“,”分割
   * </pre>
   *
   * <code>string eventVideos = 17;</code>
   */
  java.lang.String getEventVideos();
  /**
   * <pre>
   *可选，事件抓拍视频在云端的相对路径列表，id串列表，以“,”分割
   * </pre>
   *
   * <code>string eventVideos = 17;</code>
   */
  com.google.protobuf.ByteString
      getEventVideosBytes();

  /**
   * <pre>
   *会话id
   * </pre>
   *
   * <code>uint64 sessionId = 18;</code>
   */
  long getSessionId();

  /**
   * <pre>
   *全局唯一ID，利用雪花算法生成
   * </pre>
   *
   * <code>uint64 id = 19;</code>
   */
  long getId();
}
