// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *________________________________________________________________________________
 *道路交通标志信息RtsData 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.RtsData}
 */
public  final class RtsData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.RtsData)
    RtsDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RtsData.newBuilder() to construct.
  private RtsData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RtsData() {
    dataSource_ = 0;
    priority_ = "";
    description_ = "";
    refPathList_ = java.util.Collections.emptyList();
    refLinkList_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RtsData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RtsData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            rtsId_ = input.readInt32();
            break;
          }
          case 16: {

            rtsType_ = input.readInt32();
            break;
          }
          case 24: {
            int rawValue = input.readEnum();

            dataSource_ = rawValue;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            priority_ = s;
            break;
          }
          case 42: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (rtsPos_ != null) {
              subBuilder = rtsPos_.toBuilder();
            }
            rtsPos_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(rtsPos_);
              rtsPos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 50: {
            road.data.proto.RsiTimeDetails.Builder subBuilder = null;
            if (timeDetails_ != null) {
              subBuilder = timeDetails_.toBuilder();
            }
            timeDetails_ = input.readMessage(road.data.proto.RsiTimeDetails.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(timeDetails_);
              timeDetails_ = subBuilder.buildPartial();
            }

            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            description_ = s;
            break;
          }
          case 66: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              refPathList_ = new java.util.ArrayList<road.data.proto.ReferencePath>();
              mutable_bitField0_ |= 0x00000001;
            }
            refPathList_.add(
                input.readMessage(road.data.proto.ReferencePath.parser(), extensionRegistry));
            break;
          }
          case 74: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              refLinkList_ = new java.util.ArrayList<road.data.proto.ReferenceLink>();
              mutable_bitField0_ |= 0x00000002;
            }
            refLinkList_.add(
                input.readMessage(road.data.proto.ReferenceLink.parser(), extensionRegistry));
            break;
          }
          case 80: {

            pathRadius_ = input.readUInt32();
            break;
          }
          case 88: {

            sessionId_ = input.readUInt64();
            break;
          }
          case 96: {

            id_ = input.readUInt64();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        refPathList_ = java.util.Collections.unmodifiableList(refPathList_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        refLinkList_ = java.util.Collections.unmodifiableList(refLinkList_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RtsData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RtsData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.RtsData.class, road.data.proto.RtsData.Builder.class);
  }

  public static final int RTSID_FIELD_NUMBER = 1;
  private int rtsId_;
  /**
   * <pre>
   * 交通标志编号
   * </pre>
   *
   * <code>int32 rtsId = 1;</code>
   */
  public int getRtsId() {
    return rtsId_;
  }

  public static final int RTSTYPE_FIELD_NUMBER = 2;
  private int rtsType_;
  /**
   * <pre>
   * 可选，交通标志类型 交通标志类型符合中国GB 5768.2 值为0表示未知类型或使用文字描述
   * </pre>
   *
   * <code>int32 rtsType = 2;</code>
   */
  public int getRtsType() {
    return rtsType_;
  }

  public static final int DATASOURCE_FIELD_NUMBER = 3;
  private int dataSource_;
  /**
   * <pre>
   *路侧数据来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
   */
  public int getDataSourceValue() {
    return dataSource_;
  }
  /**
   * <pre>
   *路侧数据来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
   */
  public road.data.proto.DataSource getDataSource() {
    @SuppressWarnings("deprecation")
    road.data.proto.DataSource result = road.data.proto.DataSource.valueOf(dataSource_);
    return result == null ? road.data.proto.DataSource.UNRECOGNIZED : result;
  }

  public static final int PRIORITY_FIELD_NUMBER = 4;
  private volatile java.lang.Object priority_;
  /**
   * <pre>
   *	可选，事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
   * </pre>
   *
   * <code>string priority = 4;</code>
   */
  public java.lang.String getPriority() {
    java.lang.Object ref = priority_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      priority_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *	可选，事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
   * </pre>
   *
   * <code>string priority = 4;</code>
   */
  public com.google.protobuf.ByteString
      getPriorityBytes() {
    java.lang.Object ref = priority_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      priority_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RTSPOS_FIELD_NUMBER = 5;
  private road.data.proto.Position3D rtsPos_;
  /**
   * <pre>
   *可选，标志标线位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
   */
  public boolean hasRtsPos() {
    return rtsPos_ != null;
  }
  /**
   * <pre>
   *可选，标志标线位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
   */
  public road.data.proto.Position3D getRtsPos() {
    return rtsPos_ == null ? road.data.proto.Position3D.getDefaultInstance() : rtsPos_;
  }
  /**
   * <pre>
   *可选，标志标线位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
   */
  public road.data.proto.Position3DOrBuilder getRtsPosOrBuilder() {
    return getRtsPos();
  }

  public static final int TIMEDETAILS_FIELD_NUMBER = 6;
  private road.data.proto.RsiTimeDetails timeDetails_;
  /**
   * <pre>
   *可选， 事件生效时间
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
   */
  public boolean hasTimeDetails() {
    return timeDetails_ != null;
  }
  /**
   * <pre>
   *可选， 事件生效时间
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
   */
  public road.data.proto.RsiTimeDetails getTimeDetails() {
    return timeDetails_ == null ? road.data.proto.RsiTimeDetails.getDefaultInstance() : timeDetails_;
  }
  /**
   * <pre>
   *可选， 事件生效时间
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
   */
  public road.data.proto.RsiTimeDetailsOrBuilder getTimeDetailsOrBuilder() {
    return getTimeDetails();
  }

  public static final int DESCRIPTION_FIELD_NUMBER = 7;
  private volatile java.lang.Object description_;
  /**
   * <pre>
   * 可选，描述，json字串
   * </pre>
   *
   * <code>string description = 7;</code>
   */
  public java.lang.String getDescription() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      description_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 可选，描述，json字串
   * </pre>
   *
   * <code>string description = 7;</code>
   */
  public com.google.protobuf.ByteString
      getDescriptionBytes() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      description_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REFPATHLIST_FIELD_NUMBER = 8;
  private java.util.List<road.data.proto.ReferencePath> refPathList_;
  /**
   * <pre>
   *{
   *“id”: 810907429486297107,  //全局唯一事件ID
   *“state”: 0,   //-1: 发送到MEC失败
   *-2: 发送到RSU失败
   *-3: 发送到车端失败
   *-4: 发送到云端失败
   *0: 已生成
   *1: 发送到MEC
   *2: 发送到RSU
   *3: 发送到车端
   *4: 发送到云端
   *“desc”: { … }  //自定义描述信息，失败原因等
   *}
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
   */
  public java.util.List<road.data.proto.ReferencePath> getRefPathListList() {
    return refPathList_;
  }
  /**
   * <pre>
   *{
   *“id”: 810907429486297107,  //全局唯一事件ID
   *“state”: 0,   //-1: 发送到MEC失败
   *-2: 发送到RSU失败
   *-3: 发送到车端失败
   *-4: 发送到云端失败
   *0: 已生成
   *1: 发送到MEC
   *2: 发送到RSU
   *3: 发送到车端
   *4: 发送到云端
   *“desc”: { … }  //自定义描述信息，失败原因等
   *}
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
   */
  public java.util.List<? extends road.data.proto.ReferencePathOrBuilder> 
      getRefPathListOrBuilderList() {
    return refPathList_;
  }
  /**
   * <pre>
   *{
   *“id”: 810907429486297107,  //全局唯一事件ID
   *“state”: 0,   //-1: 发送到MEC失败
   *-2: 发送到RSU失败
   *-3: 发送到车端失败
   *-4: 发送到云端失败
   *0: 已生成
   *1: 发送到MEC
   *2: 发送到RSU
   *3: 发送到车端
   *4: 发送到云端
   *“desc”: { … }  //自定义描述信息，失败原因等
   *}
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
   */
  public int getRefPathListCount() {
    return refPathList_.size();
  }
  /**
   * <pre>
   *{
   *“id”: 810907429486297107,  //全局唯一事件ID
   *“state”: 0,   //-1: 发送到MEC失败
   *-2: 发送到RSU失败
   *-3: 发送到车端失败
   *-4: 发送到云端失败
   *0: 已生成
   *1: 发送到MEC
   *2: 发送到RSU
   *3: 发送到车端
   *4: 发送到云端
   *“desc”: { … }  //自定义描述信息，失败原因等
   *}
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
   */
  public road.data.proto.ReferencePath getRefPathList(int index) {
    return refPathList_.get(index);
  }
  /**
   * <pre>
   *{
   *“id”: 810907429486297107,  //全局唯一事件ID
   *“state”: 0,   //-1: 发送到MEC失败
   *-2: 发送到RSU失败
   *-3: 发送到车端失败
   *-4: 发送到云端失败
   *0: 已生成
   *1: 发送到MEC
   *2: 发送到RSU
   *3: 发送到车端
   *4: 发送到云端
   *“desc”: { … }  //自定义描述信息，失败原因等
   *}
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
   */
  public road.data.proto.ReferencePathOrBuilder getRefPathListOrBuilder(
      int index) {
    return refPathList_.get(index);
  }

  public static final int REFLINKLIST_FIELD_NUMBER = 9;
  private java.util.List<road.data.proto.ReferenceLink> refLinkList_;
  /**
   * <pre>
   * 可选，定义道路交通事件的关联路段集合。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
   */
  public java.util.List<road.data.proto.ReferenceLink> getRefLinkListList() {
    return refLinkList_;
  }
  /**
   * <pre>
   * 可选，定义道路交通事件的关联路段集合。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
   */
  public java.util.List<? extends road.data.proto.ReferenceLinkOrBuilder> 
      getRefLinkListOrBuilderList() {
    return refLinkList_;
  }
  /**
   * <pre>
   * 可选，定义道路交通事件的关联路段集合。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
   */
  public int getRefLinkListCount() {
    return refLinkList_.size();
  }
  /**
   * <pre>
   * 可选，定义道路交通事件的关联路段集合。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
   */
  public road.data.proto.ReferenceLink getRefLinkList(int index) {
    return refLinkList_.get(index);
  }
  /**
   * <pre>
   * 可选，定义道路交通事件的关联路段集合。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
   */
  public road.data.proto.ReferenceLinkOrBuilder getRefLinkListOrBuilder(
      int index) {
    return refLinkList_.get(index);
  }

  public static final int PATHRADIUS_FIELD_NUMBER = 10;
  private int pathRadius_;
  /**
   * <pre>
   *可选，影响半径，单位：0.1m
   * </pre>
   *
   * <code>uint32 pathRadius = 10;</code>
   */
  public int getPathRadius() {
    return pathRadius_;
  }

  public static final int SESSIONID_FIELD_NUMBER = 11;
  private long sessionId_;
  /**
   * <pre>
   *会话id
   * </pre>
   *
   * <code>uint64 sessionId = 11;</code>
   */
  public long getSessionId() {
    return sessionId_;
  }

  public static final int ID_FIELD_NUMBER = 12;
  private long id_;
  /**
   * <pre>
   *全局唯一ID，利用雪花算法生成
   * </pre>
   *
   * <code>uint64 id = 12;</code>
   */
  public long getId() {
    return id_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (rtsId_ != 0) {
      output.writeInt32(1, rtsId_);
    }
    if (rtsType_ != 0) {
      output.writeInt32(2, rtsType_);
    }
    if (dataSource_ != road.data.proto.DataSource.DATA_SOURCE_UNKNOWN.getNumber()) {
      output.writeEnum(3, dataSource_);
    }
    if (!getPriorityBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, priority_);
    }
    if (rtsPos_ != null) {
      output.writeMessage(5, getRtsPos());
    }
    if (timeDetails_ != null) {
      output.writeMessage(6, getTimeDetails());
    }
    if (!getDescriptionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, description_);
    }
    for (int i = 0; i < refPathList_.size(); i++) {
      output.writeMessage(8, refPathList_.get(i));
    }
    for (int i = 0; i < refLinkList_.size(); i++) {
      output.writeMessage(9, refLinkList_.get(i));
    }
    if (pathRadius_ != 0) {
      output.writeUInt32(10, pathRadius_);
    }
    if (sessionId_ != 0L) {
      output.writeUInt64(11, sessionId_);
    }
    if (id_ != 0L) {
      output.writeUInt64(12, id_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (rtsId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, rtsId_);
    }
    if (rtsType_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, rtsType_);
    }
    if (dataSource_ != road.data.proto.DataSource.DATA_SOURCE_UNKNOWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, dataSource_);
    }
    if (!getPriorityBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, priority_);
    }
    if (rtsPos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getRtsPos());
    }
    if (timeDetails_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getTimeDetails());
    }
    if (!getDescriptionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, description_);
    }
    for (int i = 0; i < refPathList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, refPathList_.get(i));
    }
    for (int i = 0; i < refLinkList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(9, refLinkList_.get(i));
    }
    if (pathRadius_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(10, pathRadius_);
    }
    if (sessionId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(11, sessionId_);
    }
    if (id_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(12, id_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.RtsData)) {
      return super.equals(obj);
    }
    road.data.proto.RtsData other = (road.data.proto.RtsData) obj;

    if (getRtsId()
        != other.getRtsId()) return false;
    if (getRtsType()
        != other.getRtsType()) return false;
    if (dataSource_ != other.dataSource_) return false;
    if (!getPriority()
        .equals(other.getPriority())) return false;
    if (hasRtsPos() != other.hasRtsPos()) return false;
    if (hasRtsPos()) {
      if (!getRtsPos()
          .equals(other.getRtsPos())) return false;
    }
    if (hasTimeDetails() != other.hasTimeDetails()) return false;
    if (hasTimeDetails()) {
      if (!getTimeDetails()
          .equals(other.getTimeDetails())) return false;
    }
    if (!getDescription()
        .equals(other.getDescription())) return false;
    if (!getRefPathListList()
        .equals(other.getRefPathListList())) return false;
    if (!getRefLinkListList()
        .equals(other.getRefLinkListList())) return false;
    if (getPathRadius()
        != other.getPathRadius()) return false;
    if (getSessionId()
        != other.getSessionId()) return false;
    if (getId()
        != other.getId()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + RTSID_FIELD_NUMBER;
    hash = (53 * hash) + getRtsId();
    hash = (37 * hash) + RTSTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getRtsType();
    hash = (37 * hash) + DATASOURCE_FIELD_NUMBER;
    hash = (53 * hash) + dataSource_;
    hash = (37 * hash) + PRIORITY_FIELD_NUMBER;
    hash = (53 * hash) + getPriority().hashCode();
    if (hasRtsPos()) {
      hash = (37 * hash) + RTSPOS_FIELD_NUMBER;
      hash = (53 * hash) + getRtsPos().hashCode();
    }
    if (hasTimeDetails()) {
      hash = (37 * hash) + TIMEDETAILS_FIELD_NUMBER;
      hash = (53 * hash) + getTimeDetails().hashCode();
    }
    hash = (37 * hash) + DESCRIPTION_FIELD_NUMBER;
    hash = (53 * hash) + getDescription().hashCode();
    if (getRefPathListCount() > 0) {
      hash = (37 * hash) + REFPATHLIST_FIELD_NUMBER;
      hash = (53 * hash) + getRefPathListList().hashCode();
    }
    if (getRefLinkListCount() > 0) {
      hash = (37 * hash) + REFLINKLIST_FIELD_NUMBER;
      hash = (53 * hash) + getRefLinkListList().hashCode();
    }
    hash = (37 * hash) + PATHRADIUS_FIELD_NUMBER;
    hash = (53 * hash) + getPathRadius();
    hash = (37 * hash) + SESSIONID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getSessionId());
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getId());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.RtsData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RtsData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RtsData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RtsData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RtsData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.RtsData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.RtsData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RtsData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RtsData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.RtsData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.RtsData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.RtsData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.RtsData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *________________________________________________________________________________
   *道路交通标志信息RtsData 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.RtsData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.RtsData)
      road.data.proto.RtsDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RtsData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RtsData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.RtsData.class, road.data.proto.RtsData.Builder.class);
    }

    // Construct using road.data.proto.RtsData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getRefPathListFieldBuilder();
        getRefLinkListFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      rtsId_ = 0;

      rtsType_ = 0;

      dataSource_ = 0;

      priority_ = "";

      if (rtsPosBuilder_ == null) {
        rtsPos_ = null;
      } else {
        rtsPos_ = null;
        rtsPosBuilder_ = null;
      }
      if (timeDetailsBuilder_ == null) {
        timeDetails_ = null;
      } else {
        timeDetails_ = null;
        timeDetailsBuilder_ = null;
      }
      description_ = "";

      if (refPathListBuilder_ == null) {
        refPathList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        refPathListBuilder_.clear();
      }
      if (refLinkListBuilder_ == null) {
        refLinkList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        refLinkListBuilder_.clear();
      }
      pathRadius_ = 0;

      sessionId_ = 0L;

      id_ = 0L;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_RtsData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.RtsData getDefaultInstanceForType() {
      return road.data.proto.RtsData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.RtsData build() {
      road.data.proto.RtsData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.RtsData buildPartial() {
      road.data.proto.RtsData result = new road.data.proto.RtsData(this);
      int from_bitField0_ = bitField0_;
      result.rtsId_ = rtsId_;
      result.rtsType_ = rtsType_;
      result.dataSource_ = dataSource_;
      result.priority_ = priority_;
      if (rtsPosBuilder_ == null) {
        result.rtsPos_ = rtsPos_;
      } else {
        result.rtsPos_ = rtsPosBuilder_.build();
      }
      if (timeDetailsBuilder_ == null) {
        result.timeDetails_ = timeDetails_;
      } else {
        result.timeDetails_ = timeDetailsBuilder_.build();
      }
      result.description_ = description_;
      if (refPathListBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          refPathList_ = java.util.Collections.unmodifiableList(refPathList_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.refPathList_ = refPathList_;
      } else {
        result.refPathList_ = refPathListBuilder_.build();
      }
      if (refLinkListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          refLinkList_ = java.util.Collections.unmodifiableList(refLinkList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.refLinkList_ = refLinkList_;
      } else {
        result.refLinkList_ = refLinkListBuilder_.build();
      }
      result.pathRadius_ = pathRadius_;
      result.sessionId_ = sessionId_;
      result.id_ = id_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.RtsData) {
        return mergeFrom((road.data.proto.RtsData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.RtsData other) {
      if (other == road.data.proto.RtsData.getDefaultInstance()) return this;
      if (other.getRtsId() != 0) {
        setRtsId(other.getRtsId());
      }
      if (other.getRtsType() != 0) {
        setRtsType(other.getRtsType());
      }
      if (other.dataSource_ != 0) {
        setDataSourceValue(other.getDataSourceValue());
      }
      if (!other.getPriority().isEmpty()) {
        priority_ = other.priority_;
        onChanged();
      }
      if (other.hasRtsPos()) {
        mergeRtsPos(other.getRtsPos());
      }
      if (other.hasTimeDetails()) {
        mergeTimeDetails(other.getTimeDetails());
      }
      if (!other.getDescription().isEmpty()) {
        description_ = other.description_;
        onChanged();
      }
      if (refPathListBuilder_ == null) {
        if (!other.refPathList_.isEmpty()) {
          if (refPathList_.isEmpty()) {
            refPathList_ = other.refPathList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureRefPathListIsMutable();
            refPathList_.addAll(other.refPathList_);
          }
          onChanged();
        }
      } else {
        if (!other.refPathList_.isEmpty()) {
          if (refPathListBuilder_.isEmpty()) {
            refPathListBuilder_.dispose();
            refPathListBuilder_ = null;
            refPathList_ = other.refPathList_;
            bitField0_ = (bitField0_ & ~0x00000001);
            refPathListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getRefPathListFieldBuilder() : null;
          } else {
            refPathListBuilder_.addAllMessages(other.refPathList_);
          }
        }
      }
      if (refLinkListBuilder_ == null) {
        if (!other.refLinkList_.isEmpty()) {
          if (refLinkList_.isEmpty()) {
            refLinkList_ = other.refLinkList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureRefLinkListIsMutable();
            refLinkList_.addAll(other.refLinkList_);
          }
          onChanged();
        }
      } else {
        if (!other.refLinkList_.isEmpty()) {
          if (refLinkListBuilder_.isEmpty()) {
            refLinkListBuilder_.dispose();
            refLinkListBuilder_ = null;
            refLinkList_ = other.refLinkList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            refLinkListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getRefLinkListFieldBuilder() : null;
          } else {
            refLinkListBuilder_.addAllMessages(other.refLinkList_);
          }
        }
      }
      if (other.getPathRadius() != 0) {
        setPathRadius(other.getPathRadius());
      }
      if (other.getSessionId() != 0L) {
        setSessionId(other.getSessionId());
      }
      if (other.getId() != 0L) {
        setId(other.getId());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.RtsData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.RtsData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int rtsId_ ;
    /**
     * <pre>
     * 交通标志编号
     * </pre>
     *
     * <code>int32 rtsId = 1;</code>
     */
    public int getRtsId() {
      return rtsId_;
    }
    /**
     * <pre>
     * 交通标志编号
     * </pre>
     *
     * <code>int32 rtsId = 1;</code>
     */
    public Builder setRtsId(int value) {
      
      rtsId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 交通标志编号
     * </pre>
     *
     * <code>int32 rtsId = 1;</code>
     */
    public Builder clearRtsId() {
      
      rtsId_ = 0;
      onChanged();
      return this;
    }

    private int rtsType_ ;
    /**
     * <pre>
     * 可选，交通标志类型 交通标志类型符合中国GB 5768.2 值为0表示未知类型或使用文字描述
     * </pre>
     *
     * <code>int32 rtsType = 2;</code>
     */
    public int getRtsType() {
      return rtsType_;
    }
    /**
     * <pre>
     * 可选，交通标志类型 交通标志类型符合中国GB 5768.2 值为0表示未知类型或使用文字描述
     * </pre>
     *
     * <code>int32 rtsType = 2;</code>
     */
    public Builder setRtsType(int value) {
      
      rtsType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，交通标志类型 交通标志类型符合中国GB 5768.2 值为0表示未知类型或使用文字描述
     * </pre>
     *
     * <code>int32 rtsType = 2;</code>
     */
    public Builder clearRtsType() {
      
      rtsType_ = 0;
      onChanged();
      return this;
    }

    private int dataSource_ = 0;
    /**
     * <pre>
     *路侧数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
     */
    public int getDataSourceValue() {
      return dataSource_;
    }
    /**
     * <pre>
     *路侧数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
     */
    public Builder setDataSourceValue(int value) {
      dataSource_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *路侧数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
     */
    public road.data.proto.DataSource getDataSource() {
      @SuppressWarnings("deprecation")
      road.data.proto.DataSource result = road.data.proto.DataSource.valueOf(dataSource_);
      return result == null ? road.data.proto.DataSource.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *路侧数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
     */
    public Builder setDataSource(road.data.proto.DataSource value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      dataSource_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *路侧数据来源
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
     */
    public Builder clearDataSource() {
      
      dataSource_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object priority_ = "";
    /**
     * <pre>
     *	可选，事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
     * </pre>
     *
     * <code>string priority = 4;</code>
     */
    public java.lang.String getPriority() {
      java.lang.Object ref = priority_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        priority_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *	可选，事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
     * </pre>
     *
     * <code>string priority = 4;</code>
     */
    public com.google.protobuf.ByteString
        getPriorityBytes() {
      java.lang.Object ref = priority_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        priority_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *	可选，事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
     * </pre>
     *
     * <code>string priority = 4;</code>
     */
    public Builder setPriority(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      priority_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *	可选，事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
     * </pre>
     *
     * <code>string priority = 4;</code>
     */
    public Builder clearPriority() {
      
      priority_ = getDefaultInstance().getPriority();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *	可选，事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
     * </pre>
     *
     * <code>string priority = 4;</code>
     */
    public Builder setPriorityBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      priority_ = value;
      onChanged();
      return this;
    }

    private road.data.proto.Position3D rtsPos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> rtsPosBuilder_;
    /**
     * <pre>
     *可选，标志标线位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
     */
    public boolean hasRtsPos() {
      return rtsPosBuilder_ != null || rtsPos_ != null;
    }
    /**
     * <pre>
     *可选，标志标线位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
     */
    public road.data.proto.Position3D getRtsPos() {
      if (rtsPosBuilder_ == null) {
        return rtsPos_ == null ? road.data.proto.Position3D.getDefaultInstance() : rtsPos_;
      } else {
        return rtsPosBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，标志标线位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
     */
    public Builder setRtsPos(road.data.proto.Position3D value) {
      if (rtsPosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        rtsPos_ = value;
        onChanged();
      } else {
        rtsPosBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，标志标线位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
     */
    public Builder setRtsPos(
        road.data.proto.Position3D.Builder builderForValue) {
      if (rtsPosBuilder_ == null) {
        rtsPos_ = builderForValue.build();
        onChanged();
      } else {
        rtsPosBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，标志标线位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
     */
    public Builder mergeRtsPos(road.data.proto.Position3D value) {
      if (rtsPosBuilder_ == null) {
        if (rtsPos_ != null) {
          rtsPos_ =
            road.data.proto.Position3D.newBuilder(rtsPos_).mergeFrom(value).buildPartial();
        } else {
          rtsPos_ = value;
        }
        onChanged();
      } else {
        rtsPosBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，标志标线位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
     */
    public Builder clearRtsPos() {
      if (rtsPosBuilder_ == null) {
        rtsPos_ = null;
        onChanged();
      } else {
        rtsPos_ = null;
        rtsPosBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，标志标线位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
     */
    public road.data.proto.Position3D.Builder getRtsPosBuilder() {
      
      onChanged();
      return getRtsPosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，标志标线位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
     */
    public road.data.proto.Position3DOrBuilder getRtsPosOrBuilder() {
      if (rtsPosBuilder_ != null) {
        return rtsPosBuilder_.getMessageOrBuilder();
      } else {
        return rtsPos_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : rtsPos_;
      }
    }
    /**
     * <pre>
     *可选，标志标线位置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getRtsPosFieldBuilder() {
      if (rtsPosBuilder_ == null) {
        rtsPosBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getRtsPos(),
                getParentForChildren(),
                isClean());
        rtsPos_ = null;
      }
      return rtsPosBuilder_;
    }

    private road.data.proto.RsiTimeDetails timeDetails_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.RsiTimeDetails, road.data.proto.RsiTimeDetails.Builder, road.data.proto.RsiTimeDetailsOrBuilder> timeDetailsBuilder_;
    /**
     * <pre>
     *可选， 事件生效时间
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
     */
    public boolean hasTimeDetails() {
      return timeDetailsBuilder_ != null || timeDetails_ != null;
    }
    /**
     * <pre>
     *可选， 事件生效时间
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
     */
    public road.data.proto.RsiTimeDetails getTimeDetails() {
      if (timeDetailsBuilder_ == null) {
        return timeDetails_ == null ? road.data.proto.RsiTimeDetails.getDefaultInstance() : timeDetails_;
      } else {
        return timeDetailsBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选， 事件生效时间
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
     */
    public Builder setTimeDetails(road.data.proto.RsiTimeDetails value) {
      if (timeDetailsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        timeDetails_ = value;
        onChanged();
      } else {
        timeDetailsBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选， 事件生效时间
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
     */
    public Builder setTimeDetails(
        road.data.proto.RsiTimeDetails.Builder builderForValue) {
      if (timeDetailsBuilder_ == null) {
        timeDetails_ = builderForValue.build();
        onChanged();
      } else {
        timeDetailsBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选， 事件生效时间
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
     */
    public Builder mergeTimeDetails(road.data.proto.RsiTimeDetails value) {
      if (timeDetailsBuilder_ == null) {
        if (timeDetails_ != null) {
          timeDetails_ =
            road.data.proto.RsiTimeDetails.newBuilder(timeDetails_).mergeFrom(value).buildPartial();
        } else {
          timeDetails_ = value;
        }
        onChanged();
      } else {
        timeDetailsBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选， 事件生效时间
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
     */
    public Builder clearTimeDetails() {
      if (timeDetailsBuilder_ == null) {
        timeDetails_ = null;
        onChanged();
      } else {
        timeDetails_ = null;
        timeDetailsBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选， 事件生效时间
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
     */
    public road.data.proto.RsiTimeDetails.Builder getTimeDetailsBuilder() {
      
      onChanged();
      return getTimeDetailsFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选， 事件生效时间
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
     */
    public road.data.proto.RsiTimeDetailsOrBuilder getTimeDetailsOrBuilder() {
      if (timeDetailsBuilder_ != null) {
        return timeDetailsBuilder_.getMessageOrBuilder();
      } else {
        return timeDetails_ == null ?
            road.data.proto.RsiTimeDetails.getDefaultInstance() : timeDetails_;
      }
    }
    /**
     * <pre>
     *可选， 事件生效时间
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.RsiTimeDetails, road.data.proto.RsiTimeDetails.Builder, road.data.proto.RsiTimeDetailsOrBuilder> 
        getTimeDetailsFieldBuilder() {
      if (timeDetailsBuilder_ == null) {
        timeDetailsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.RsiTimeDetails, road.data.proto.RsiTimeDetails.Builder, road.data.proto.RsiTimeDetailsOrBuilder>(
                getTimeDetails(),
                getParentForChildren(),
                isClean());
        timeDetails_ = null;
      }
      return timeDetailsBuilder_;
    }

    private java.lang.Object description_ = "";
    /**
     * <pre>
     * 可选，描述，json字串
     * </pre>
     *
     * <code>string description = 7;</code>
     */
    public java.lang.String getDescription() {
      java.lang.Object ref = description_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        description_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 可选，描述，json字串
     * </pre>
     *
     * <code>string description = 7;</code>
     */
    public com.google.protobuf.ByteString
        getDescriptionBytes() {
      java.lang.Object ref = description_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        description_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 可选，描述，json字串
     * </pre>
     *
     * <code>string description = 7;</code>
     */
    public Builder setDescription(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      description_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，描述，json字串
     * </pre>
     *
     * <code>string description = 7;</code>
     */
    public Builder clearDescription() {
      
      description_ = getDefaultInstance().getDescription();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，描述，json字串
     * </pre>
     *
     * <code>string description = 7;</code>
     */
    public Builder setDescriptionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      description_ = value;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.ReferencePath> refPathList_ =
      java.util.Collections.emptyList();
    private void ensureRefPathListIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        refPathList_ = new java.util.ArrayList<road.data.proto.ReferencePath>(refPathList_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder> refPathListBuilder_;

    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public java.util.List<road.data.proto.ReferencePath> getRefPathListList() {
      if (refPathListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(refPathList_);
      } else {
        return refPathListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public int getRefPathListCount() {
      if (refPathListBuilder_ == null) {
        return refPathList_.size();
      } else {
        return refPathListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public road.data.proto.ReferencePath getRefPathList(int index) {
      if (refPathListBuilder_ == null) {
        return refPathList_.get(index);
      } else {
        return refPathListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public Builder setRefPathList(
        int index, road.data.proto.ReferencePath value) {
      if (refPathListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRefPathListIsMutable();
        refPathList_.set(index, value);
        onChanged();
      } else {
        refPathListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public Builder setRefPathList(
        int index, road.data.proto.ReferencePath.Builder builderForValue) {
      if (refPathListBuilder_ == null) {
        ensureRefPathListIsMutable();
        refPathList_.set(index, builderForValue.build());
        onChanged();
      } else {
        refPathListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public Builder addRefPathList(road.data.proto.ReferencePath value) {
      if (refPathListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRefPathListIsMutable();
        refPathList_.add(value);
        onChanged();
      } else {
        refPathListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public Builder addRefPathList(
        int index, road.data.proto.ReferencePath value) {
      if (refPathListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRefPathListIsMutable();
        refPathList_.add(index, value);
        onChanged();
      } else {
        refPathListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public Builder addRefPathList(
        road.data.proto.ReferencePath.Builder builderForValue) {
      if (refPathListBuilder_ == null) {
        ensureRefPathListIsMutable();
        refPathList_.add(builderForValue.build());
        onChanged();
      } else {
        refPathListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public Builder addRefPathList(
        int index, road.data.proto.ReferencePath.Builder builderForValue) {
      if (refPathListBuilder_ == null) {
        ensureRefPathListIsMutable();
        refPathList_.add(index, builderForValue.build());
        onChanged();
      } else {
        refPathListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public Builder addAllRefPathList(
        java.lang.Iterable<? extends road.data.proto.ReferencePath> values) {
      if (refPathListBuilder_ == null) {
        ensureRefPathListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, refPathList_);
        onChanged();
      } else {
        refPathListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public Builder clearRefPathList() {
      if (refPathListBuilder_ == null) {
        refPathList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        refPathListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public Builder removeRefPathList(int index) {
      if (refPathListBuilder_ == null) {
        ensureRefPathListIsMutable();
        refPathList_.remove(index);
        onChanged();
      } else {
        refPathListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public road.data.proto.ReferencePath.Builder getRefPathListBuilder(
        int index) {
      return getRefPathListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public road.data.proto.ReferencePathOrBuilder getRefPathListOrBuilder(
        int index) {
      if (refPathListBuilder_ == null) {
        return refPathList_.get(index);  } else {
        return refPathListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public java.util.List<? extends road.data.proto.ReferencePathOrBuilder> 
         getRefPathListOrBuilderList() {
      if (refPathListBuilder_ != null) {
        return refPathListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(refPathList_);
      }
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public road.data.proto.ReferencePath.Builder addRefPathListBuilder() {
      return getRefPathListFieldBuilder().addBuilder(
          road.data.proto.ReferencePath.getDefaultInstance());
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public road.data.proto.ReferencePath.Builder addRefPathListBuilder(
        int index) {
      return getRefPathListFieldBuilder().addBuilder(
          index, road.data.proto.ReferencePath.getDefaultInstance());
    }
    /**
     * <pre>
     *{
     *“id”: 810907429486297107,  //全局唯一事件ID
     *“state”: 0,   //-1: 发送到MEC失败
     *-2: 发送到RSU失败
     *-3: 发送到车端失败
     *-4: 发送到云端失败
     *0: 已生成
     *1: 发送到MEC
     *2: 发送到RSU
     *3: 发送到车端
     *4: 发送到云端
     *“desc”: { … }  //自定义描述信息，失败原因等
     *}
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
     */
    public java.util.List<road.data.proto.ReferencePath.Builder> 
         getRefPathListBuilderList() {
      return getRefPathListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder> 
        getRefPathListFieldBuilder() {
      if (refPathListBuilder_ == null) {
        refPathListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.ReferencePath, road.data.proto.ReferencePath.Builder, road.data.proto.ReferencePathOrBuilder>(
                refPathList_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        refPathList_ = null;
      }
      return refPathListBuilder_;
    }

    private java.util.List<road.data.proto.ReferenceLink> refLinkList_ =
      java.util.Collections.emptyList();
    private void ensureRefLinkListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        refLinkList_ = new java.util.ArrayList<road.data.proto.ReferenceLink>(refLinkList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder> refLinkListBuilder_;

    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public java.util.List<road.data.proto.ReferenceLink> getRefLinkListList() {
      if (refLinkListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(refLinkList_);
      } else {
        return refLinkListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public int getRefLinkListCount() {
      if (refLinkListBuilder_ == null) {
        return refLinkList_.size();
      } else {
        return refLinkListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public road.data.proto.ReferenceLink getRefLinkList(int index) {
      if (refLinkListBuilder_ == null) {
        return refLinkList_.get(index);
      } else {
        return refLinkListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public Builder setRefLinkList(
        int index, road.data.proto.ReferenceLink value) {
      if (refLinkListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRefLinkListIsMutable();
        refLinkList_.set(index, value);
        onChanged();
      } else {
        refLinkListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public Builder setRefLinkList(
        int index, road.data.proto.ReferenceLink.Builder builderForValue) {
      if (refLinkListBuilder_ == null) {
        ensureRefLinkListIsMutable();
        refLinkList_.set(index, builderForValue.build());
        onChanged();
      } else {
        refLinkListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public Builder addRefLinkList(road.data.proto.ReferenceLink value) {
      if (refLinkListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRefLinkListIsMutable();
        refLinkList_.add(value);
        onChanged();
      } else {
        refLinkListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public Builder addRefLinkList(
        int index, road.data.proto.ReferenceLink value) {
      if (refLinkListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRefLinkListIsMutable();
        refLinkList_.add(index, value);
        onChanged();
      } else {
        refLinkListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public Builder addRefLinkList(
        road.data.proto.ReferenceLink.Builder builderForValue) {
      if (refLinkListBuilder_ == null) {
        ensureRefLinkListIsMutable();
        refLinkList_.add(builderForValue.build());
        onChanged();
      } else {
        refLinkListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public Builder addRefLinkList(
        int index, road.data.proto.ReferenceLink.Builder builderForValue) {
      if (refLinkListBuilder_ == null) {
        ensureRefLinkListIsMutable();
        refLinkList_.add(index, builderForValue.build());
        onChanged();
      } else {
        refLinkListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public Builder addAllRefLinkList(
        java.lang.Iterable<? extends road.data.proto.ReferenceLink> values) {
      if (refLinkListBuilder_ == null) {
        ensureRefLinkListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, refLinkList_);
        onChanged();
      } else {
        refLinkListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public Builder clearRefLinkList() {
      if (refLinkListBuilder_ == null) {
        refLinkList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        refLinkListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public Builder removeRefLinkList(int index) {
      if (refLinkListBuilder_ == null) {
        ensureRefLinkListIsMutable();
        refLinkList_.remove(index);
        onChanged();
      } else {
        refLinkListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public road.data.proto.ReferenceLink.Builder getRefLinkListBuilder(
        int index) {
      return getRefLinkListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public road.data.proto.ReferenceLinkOrBuilder getRefLinkListOrBuilder(
        int index) {
      if (refLinkListBuilder_ == null) {
        return refLinkList_.get(index);  } else {
        return refLinkListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public java.util.List<? extends road.data.proto.ReferenceLinkOrBuilder> 
         getRefLinkListOrBuilderList() {
      if (refLinkListBuilder_ != null) {
        return refLinkListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(refLinkList_);
      }
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public road.data.proto.ReferenceLink.Builder addRefLinkListBuilder() {
      return getRefLinkListFieldBuilder().addBuilder(
          road.data.proto.ReferenceLink.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public road.data.proto.ReferenceLink.Builder addRefLinkListBuilder(
        int index) {
      return getRefLinkListFieldBuilder().addBuilder(
          index, road.data.proto.ReferenceLink.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，定义道路交通事件的关联路段集合。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
     */
    public java.util.List<road.data.proto.ReferenceLink.Builder> 
         getRefLinkListBuilderList() {
      return getRefLinkListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder> 
        getRefLinkListFieldBuilder() {
      if (refLinkListBuilder_ == null) {
        refLinkListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.ReferenceLink, road.data.proto.ReferenceLink.Builder, road.data.proto.ReferenceLinkOrBuilder>(
                refLinkList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        refLinkList_ = null;
      }
      return refLinkListBuilder_;
    }

    private int pathRadius_ ;
    /**
     * <pre>
     *可选，影响半径，单位：0.1m
     * </pre>
     *
     * <code>uint32 pathRadius = 10;</code>
     */
    public int getPathRadius() {
      return pathRadius_;
    }
    /**
     * <pre>
     *可选，影响半径，单位：0.1m
     * </pre>
     *
     * <code>uint32 pathRadius = 10;</code>
     */
    public Builder setPathRadius(int value) {
      
      pathRadius_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，影响半径，单位：0.1m
     * </pre>
     *
     * <code>uint32 pathRadius = 10;</code>
     */
    public Builder clearPathRadius() {
      
      pathRadius_ = 0;
      onChanged();
      return this;
    }

    private long sessionId_ ;
    /**
     * <pre>
     *会话id
     * </pre>
     *
     * <code>uint64 sessionId = 11;</code>
     */
    public long getSessionId() {
      return sessionId_;
    }
    /**
     * <pre>
     *会话id
     * </pre>
     *
     * <code>uint64 sessionId = 11;</code>
     */
    public Builder setSessionId(long value) {
      
      sessionId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *会话id
     * </pre>
     *
     * <code>uint64 sessionId = 11;</code>
     */
    public Builder clearSessionId() {
      
      sessionId_ = 0L;
      onChanged();
      return this;
    }

    private long id_ ;
    /**
     * <pre>
     *全局唯一ID，利用雪花算法生成
     * </pre>
     *
     * <code>uint64 id = 12;</code>
     */
    public long getId() {
      return id_;
    }
    /**
     * <pre>
     *全局唯一ID，利用雪花算法生成
     * </pre>
     *
     * <code>uint64 id = 12;</code>
     */
    public Builder setId(long value) {
      
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *全局唯一ID，利用雪花算法生成
     * </pre>
     *
     * <code>uint64 id = 12;</code>
     */
    public Builder clearId() {
      
      id_ = 0L;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.RtsData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.RtsData)
  private static final road.data.proto.RtsData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.RtsData();
  }

  public static road.data.proto.RtsData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RtsData>
      PARSER = new com.google.protobuf.AbstractParser<RtsData>() {
    @java.lang.Override
    public RtsData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RtsData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RtsData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RtsData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.RtsData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

