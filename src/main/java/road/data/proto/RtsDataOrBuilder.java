// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface RtsDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.RtsData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 交通标志编号
   * </pre>
   *
   * <code>int32 rtsId = 1;</code>
   */
  int getRtsId();

  /**
   * <pre>
   * 可选，交通标志类型 交通标志类型符合中国GB 5768.2 值为0表示未知类型或使用文字描述
   * </pre>
   *
   * <code>int32 rtsType = 2;</code>
   */
  int getRtsType();

  /**
   * <pre>
   *路侧数据来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
   */
  int getDataSourceValue();
  /**
   * <pre>
   *路侧数据来源
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DataSource dataSource = 3;</code>
   */
  road.data.proto.DataSource getDataSource();

  /**
   * <pre>
   *	可选，事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
   * </pre>
   *
   * <code>string priority = 4;</code>
   */
  java.lang.String getPriority();
  /**
   * <pre>
   *	可选，事件的优先级，数值长度占8位，其中低五位为0，为无效位，高三位为有效数据位，数值有效范围是B00000000 到B11100000，分别表示8档由低到高的优先级。
   * </pre>
   *
   * <code>string priority = 4;</code>
   */
  com.google.protobuf.ByteString
      getPriorityBytes();

  /**
   * <pre>
   *可选，标志标线位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
   */
  boolean hasRtsPos();
  /**
   * <pre>
   *可选，标志标线位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
   */
  road.data.proto.Position3D getRtsPos();
  /**
   * <pre>
   *可选，标志标线位置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D rtsPos = 5;</code>
   */
  road.data.proto.Position3DOrBuilder getRtsPosOrBuilder();

  /**
   * <pre>
   *可选， 事件生效时间
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
   */
  boolean hasTimeDetails();
  /**
   * <pre>
   *可选， 事件生效时间
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
   */
  road.data.proto.RsiTimeDetails getTimeDetails();
  /**
   * <pre>
   *可选， 事件生效时间
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.RsiTimeDetails timeDetails = 6;</code>
   */
  road.data.proto.RsiTimeDetailsOrBuilder getTimeDetailsOrBuilder();

  /**
   * <pre>
   * 可选，描述，json字串
   * </pre>
   *
   * <code>string description = 7;</code>
   */
  java.lang.String getDescription();
  /**
   * <pre>
   * 可选，描述，json字串
   * </pre>
   *
   * <code>string description = 7;</code>
   */
  com.google.protobuf.ByteString
      getDescriptionBytes();

  /**
   * <pre>
   *{
   *“id”: 810907429486297107,  //全局唯一事件ID
   *“state”: 0,   //-1: 发送到MEC失败
   *-2: 发送到RSU失败
   *-3: 发送到车端失败
   *-4: 发送到云端失败
   *0: 已生成
   *1: 发送到MEC
   *2: 发送到RSU
   *3: 发送到车端
   *4: 发送到云端
   *“desc”: { … }  //自定义描述信息，失败原因等
   *}
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
   */
  java.util.List<road.data.proto.ReferencePath> 
      getRefPathListList();
  /**
   * <pre>
   *{
   *“id”: 810907429486297107,  //全局唯一事件ID
   *“state”: 0,   //-1: 发送到MEC失败
   *-2: 发送到RSU失败
   *-3: 发送到车端失败
   *-4: 发送到云端失败
   *0: 已生成
   *1: 发送到MEC
   *2: 发送到RSU
   *3: 发送到车端
   *4: 发送到云端
   *“desc”: { … }  //自定义描述信息，失败原因等
   *}
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
   */
  road.data.proto.ReferencePath getRefPathList(int index);
  /**
   * <pre>
   *{
   *“id”: 810907429486297107,  //全局唯一事件ID
   *“state”: 0,   //-1: 发送到MEC失败
   *-2: 发送到RSU失败
   *-3: 发送到车端失败
   *-4: 发送到云端失败
   *0: 已生成
   *1: 发送到MEC
   *2: 发送到RSU
   *3: 发送到车端
   *4: 发送到云端
   *“desc”: { … }  //自定义描述信息，失败原因等
   *}
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
   */
  int getRefPathListCount();
  /**
   * <pre>
   *{
   *“id”: 810907429486297107,  //全局唯一事件ID
   *“state”: 0,   //-1: 发送到MEC失败
   *-2: 发送到RSU失败
   *-3: 发送到车端失败
   *-4: 发送到云端失败
   *0: 已生成
   *1: 发送到MEC
   *2: 发送到RSU
   *3: 发送到车端
   *4: 发送到云端
   *“desc”: { … }  //自定义描述信息，失败原因等
   *}
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
   */
  java.util.List<? extends road.data.proto.ReferencePathOrBuilder> 
      getRefPathListOrBuilderList();
  /**
   * <pre>
   *{
   *“id”: 810907429486297107,  //全局唯一事件ID
   *“state”: 0,   //-1: 发送到MEC失败
   *-2: 发送到RSU失败
   *-3: 发送到车端失败
   *-4: 发送到云端失败
   *0: 已生成
   *1: 发送到MEC
   *2: 发送到RSU
   *3: 发送到车端
   *4: 发送到云端
   *“desc”: { … }  //自定义描述信息，失败原因等
   *}
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferencePath refPathList = 8;</code>
   */
  road.data.proto.ReferencePathOrBuilder getRefPathListOrBuilder(
      int index);

  /**
   * <pre>
   * 可选，定义道路交通事件的关联路段集合。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
   */
  java.util.List<road.data.proto.ReferenceLink> 
      getRefLinkListList();
  /**
   * <pre>
   * 可选，定义道路交通事件的关联路段集合。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
   */
  road.data.proto.ReferenceLink getRefLinkList(int index);
  /**
   * <pre>
   * 可选，定义道路交通事件的关联路段集合。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
   */
  int getRefLinkListCount();
  /**
   * <pre>
   * 可选，定义道路交通事件的关联路段集合。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
   */
  java.util.List<? extends road.data.proto.ReferenceLinkOrBuilder> 
      getRefLinkListOrBuilderList();
  /**
   * <pre>
   * 可选，定义道路交通事件的关联路段集合。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.ReferenceLink refLinkList = 9;</code>
   */
  road.data.proto.ReferenceLinkOrBuilder getRefLinkListOrBuilder(
      int index);

  /**
   * <pre>
   *可选，影响半径，单位：0.1m
   * </pre>
   *
   * <code>uint32 pathRadius = 10;</code>
   */
  int getPathRadius();

  /**
   * <pre>
   *会话id
   * </pre>
   *
   * <code>uint64 sessionId = 11;</code>
   */
  long getSessionId();

  /**
   * <pre>
   *全局唯一ID，利用雪花算法生成
   * </pre>
   *
   * <code>uint64 id = 12;</code>
   */
  long getId();
}
