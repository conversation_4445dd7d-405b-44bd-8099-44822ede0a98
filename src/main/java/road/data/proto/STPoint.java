// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *ST坐标 STPoint         
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.STPoint}
 */
public  final class STPoint extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.STPoint)
    STPointOrBuilder {
private static final long serialVersionUID = 0L;
  // Use STPoint.newBuilder() to construct.
  private STPoint(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private STPoint() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new STPoint();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private STPoint(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            sAxis_ = input.readInt32();
            break;
          }
          case 16: {

            tAxis_ = input.readInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_STPoint_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_STPoint_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.STPoint.class, road.data.proto.STPoint.Builder.class);
  }

  public static final int SAXIS_FIELD_NUMBER = 1;
  private int sAxis_;
  /**
   * <pre>
   *车道参考线，单位：0.1m
   * </pre>
   *
   * <code>int32 sAxis = 1;</code>
   */
  public int getSAxis() {
    return sAxis_;
  }

  public static final int TAXIS_FIELD_NUMBER = 2;
  private int tAxis_;
  /**
   * <pre>
   *车辆在垂直车道参考线上的横向距离
   * </pre>
   *
   * <code>int32 tAxis = 2;</code>
   */
  public int getTAxis() {
    return tAxis_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (sAxis_ != 0) {
      output.writeInt32(1, sAxis_);
    }
    if (tAxis_ != 0) {
      output.writeInt32(2, tAxis_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (sAxis_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, sAxis_);
    }
    if (tAxis_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, tAxis_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.STPoint)) {
      return super.equals(obj);
    }
    road.data.proto.STPoint other = (road.data.proto.STPoint) obj;

    if (getSAxis()
        != other.getSAxis()) return false;
    if (getTAxis()
        != other.getTAxis()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SAXIS_FIELD_NUMBER;
    hash = (53 * hash) + getSAxis();
    hash = (37 * hash) + TAXIS_FIELD_NUMBER;
    hash = (53 * hash) + getTAxis();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.STPoint parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.STPoint parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.STPoint parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.STPoint parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.STPoint parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.STPoint parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.STPoint parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.STPoint parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.STPoint parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.STPoint parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.STPoint parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.STPoint parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.STPoint prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *ST坐标 STPoint         
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.STPoint}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.STPoint)
      road.data.proto.STPointOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_STPoint_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_STPoint_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.STPoint.class, road.data.proto.STPoint.Builder.class);
    }

    // Construct using road.data.proto.STPoint.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      sAxis_ = 0;

      tAxis_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_STPoint_descriptor;
    }

    @java.lang.Override
    public road.data.proto.STPoint getDefaultInstanceForType() {
      return road.data.proto.STPoint.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.STPoint build() {
      road.data.proto.STPoint result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.STPoint buildPartial() {
      road.data.proto.STPoint result = new road.data.proto.STPoint(this);
      result.sAxis_ = sAxis_;
      result.tAxis_ = tAxis_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.STPoint) {
        return mergeFrom((road.data.proto.STPoint)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.STPoint other) {
      if (other == road.data.proto.STPoint.getDefaultInstance()) return this;
      if (other.getSAxis() != 0) {
        setSAxis(other.getSAxis());
      }
      if (other.getTAxis() != 0) {
        setTAxis(other.getTAxis());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.STPoint parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.STPoint) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int sAxis_ ;
    /**
     * <pre>
     *车道参考线，单位：0.1m
     * </pre>
     *
     * <code>int32 sAxis = 1;</code>
     */
    public int getSAxis() {
      return sAxis_;
    }
    /**
     * <pre>
     *车道参考线，单位：0.1m
     * </pre>
     *
     * <code>int32 sAxis = 1;</code>
     */
    public Builder setSAxis(int value) {
      
      sAxis_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *车道参考线，单位：0.1m
     * </pre>
     *
     * <code>int32 sAxis = 1;</code>
     */
    public Builder clearSAxis() {
      
      sAxis_ = 0;
      onChanged();
      return this;
    }

    private int tAxis_ ;
    /**
     * <pre>
     *车辆在垂直车道参考线上的横向距离
     * </pre>
     *
     * <code>int32 tAxis = 2;</code>
     */
    public int getTAxis() {
      return tAxis_;
    }
    /**
     * <pre>
     *车辆在垂直车道参考线上的横向距离
     * </pre>
     *
     * <code>int32 tAxis = 2;</code>
     */
    public Builder setTAxis(int value) {
      
      tAxis_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *车辆在垂直车道参考线上的横向距离
     * </pre>
     *
     * <code>int32 tAxis = 2;</code>
     */
    public Builder clearTAxis() {
      
      tAxis_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.STPoint)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.STPoint)
  private static final road.data.proto.STPoint DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.STPoint();
  }

  public static road.data.proto.STPoint getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<STPoint>
      PARSER = new com.google.protobuf.AbstractParser<STPoint>() {
    @java.lang.Override
    public STPoint parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new STPoint(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<STPoint> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<STPoint> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.STPoint getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

