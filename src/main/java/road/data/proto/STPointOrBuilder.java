// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface STPointOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.STPoint)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *车道参考线，单位：0.1m
   * </pre>
   *
   * <code>int32 sAxis = 1;</code>
   */
  int getSAxis();

  /**
   * <pre>
   *车辆在垂直车道参考线上的横向距离
   * </pre>
   *
   * <code>int32 tAxis = 2;</code>
   */
  int getTAxis();
}
