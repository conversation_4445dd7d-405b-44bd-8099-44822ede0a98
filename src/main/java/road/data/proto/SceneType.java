// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *________________________________________________________________________________
 *合作感知信息CAM
 *示场地类型   
 * </pre>
 *
 * Protobuf enum {@code cn.seisys.v2x.pb.SceneType}
 */
public enum SceneType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   * 表示开放区域（城市道路）;
   * </pre>
   *
   * <code>SCENE_TYPE_URBAN = 0;</code>
   */
  SCENE_TYPE_URBAN(0),
  /**
   * <pre>
   * 表示半封闭区域（高速高架、桥梁隧道）;
   * </pre>
   *
   * <code>SCENE_TYPE_HIGHSPEED = 1;</code>
   */
  SCENE_TYPE_HIGHSPEED(1),
  /**
   * <pre>
   * 表示封闭限定区域（园区、机场、停车场），
   * </pre>
   *
   * <code>SCENE_TYPE_CLOSEDPARK = 2;</code>
   */
  SCENE_TYPE_CLOSEDPARK(2),
  /**
   * <pre>
   *为预留
   * </pre>
   *
   * <code>SCENE_TYPE_RESERVED = 3;</code>
   */
  SCENE_TYPE_RESERVED(3),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   * 表示开放区域（城市道路）;
   * </pre>
   *
   * <code>SCENE_TYPE_URBAN = 0;</code>
   */
  public static final int SCENE_TYPE_URBAN_VALUE = 0;
  /**
   * <pre>
   * 表示半封闭区域（高速高架、桥梁隧道）;
   * </pre>
   *
   * <code>SCENE_TYPE_HIGHSPEED = 1;</code>
   */
  public static final int SCENE_TYPE_HIGHSPEED_VALUE = 1;
  /**
   * <pre>
   * 表示封闭限定区域（园区、机场、停车场），
   * </pre>
   *
   * <code>SCENE_TYPE_CLOSEDPARK = 2;</code>
   */
  public static final int SCENE_TYPE_CLOSEDPARK_VALUE = 2;
  /**
   * <pre>
   *为预留
   * </pre>
   *
   * <code>SCENE_TYPE_RESERVED = 3;</code>
   */
  public static final int SCENE_TYPE_RESERVED_VALUE = 3;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static SceneType valueOf(int value) {
    return forNumber(value);
  }

  public static SceneType forNumber(int value) {
    switch (value) {
      case 0: return SCENE_TYPE_URBAN;
      case 1: return SCENE_TYPE_HIGHSPEED;
      case 2: return SCENE_TYPE_CLOSEDPARK;
      case 3: return SCENE_TYPE_RESERVED;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<SceneType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      SceneType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<SceneType>() {
          public SceneType findValueByNumber(int number) {
            return SceneType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return road.data.proto.V2X.getDescriptor().getEnumTypes().get(11);
  }

  private static final SceneType[] VALUES = values();

  public static SceneType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private SceneType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.SceneType)
}

