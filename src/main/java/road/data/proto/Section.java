// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *路段区域分段 Section      
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.Section}
 */
public  final class Section extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.Section)
    SectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Section.newBuilder() to construct.
  private Section(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Section() {
    lanes_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Section();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Section(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            secId_ = input.readUInt32();
            break;
          }
          case 18: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              lanes_ = new java.util.ArrayList<road.data.proto.LaneEx>();
              mutable_bitField0_ |= 0x00000001;
            }
            lanes_.add(
                input.readMessage(road.data.proto.LaneEx.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        lanes_ = java.util.Collections.unmodifiableList(lanes_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Section_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Section_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.Section.class, road.data.proto.Section.Builder.class);
  }

  public static final int SECID_FIELD_NUMBER = 1;
  private int secId_;
  /**
   * <pre>
   *区间分段的标识ID
   * </pre>
   *
   * <code>uint32 SecId = 1;</code>
   */
  public int getSecId() {
    return secId_;
  }

  public static final int LANES_FIELD_NUMBER = 2;
  private java.util.List<road.data.proto.LaneEx> lanes_;
  /**
   * <pre>
   *车道扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
   */
  public java.util.List<road.data.proto.LaneEx> getLanesList() {
    return lanes_;
  }
  /**
   * <pre>
   *车道扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
   */
  public java.util.List<? extends road.data.proto.LaneExOrBuilder> 
      getLanesOrBuilderList() {
    return lanes_;
  }
  /**
   * <pre>
   *车道扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
   */
  public int getLanesCount() {
    return lanes_.size();
  }
  /**
   * <pre>
   *车道扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
   */
  public road.data.proto.LaneEx getLanes(int index) {
    return lanes_.get(index);
  }
  /**
   * <pre>
   *车道扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
   */
  public road.data.proto.LaneExOrBuilder getLanesOrBuilder(
      int index) {
    return lanes_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (secId_ != 0) {
      output.writeUInt32(1, secId_);
    }
    for (int i = 0; i < lanes_.size(); i++) {
      output.writeMessage(2, lanes_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (secId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, secId_);
    }
    for (int i = 0; i < lanes_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, lanes_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.Section)) {
      return super.equals(obj);
    }
    road.data.proto.Section other = (road.data.proto.Section) obj;

    if (getSecId()
        != other.getSecId()) return false;
    if (!getLanesList()
        .equals(other.getLanesList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SECID_FIELD_NUMBER;
    hash = (53 * hash) + getSecId();
    if (getLanesCount() > 0) {
      hash = (37 * hash) + LANES_FIELD_NUMBER;
      hash = (53 * hash) + getLanesList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.Section parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Section parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Section parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Section parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Section parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.Section parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.Section parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.Section parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.Section parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.Section parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.Section parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.Section parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.Section prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *路段区域分段 Section      
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.Section}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.Section)
      road.data.proto.SectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Section_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Section_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.Section.class, road.data.proto.Section.Builder.class);
    }

    // Construct using road.data.proto.Section.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getLanesFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      secId_ = 0;

      if (lanesBuilder_ == null) {
        lanes_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        lanesBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_Section_descriptor;
    }

    @java.lang.Override
    public road.data.proto.Section getDefaultInstanceForType() {
      return road.data.proto.Section.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.Section build() {
      road.data.proto.Section result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.Section buildPartial() {
      road.data.proto.Section result = new road.data.proto.Section(this);
      int from_bitField0_ = bitField0_;
      result.secId_ = secId_;
      if (lanesBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          lanes_ = java.util.Collections.unmodifiableList(lanes_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.lanes_ = lanes_;
      } else {
        result.lanes_ = lanesBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.Section) {
        return mergeFrom((road.data.proto.Section)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.Section other) {
      if (other == road.data.proto.Section.getDefaultInstance()) return this;
      if (other.getSecId() != 0) {
        setSecId(other.getSecId());
      }
      if (lanesBuilder_ == null) {
        if (!other.lanes_.isEmpty()) {
          if (lanes_.isEmpty()) {
            lanes_ = other.lanes_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureLanesIsMutable();
            lanes_.addAll(other.lanes_);
          }
          onChanged();
        }
      } else {
        if (!other.lanes_.isEmpty()) {
          if (lanesBuilder_.isEmpty()) {
            lanesBuilder_.dispose();
            lanesBuilder_ = null;
            lanes_ = other.lanes_;
            bitField0_ = (bitField0_ & ~0x00000001);
            lanesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getLanesFieldBuilder() : null;
          } else {
            lanesBuilder_.addAllMessages(other.lanes_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.Section parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.Section) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int secId_ ;
    /**
     * <pre>
     *区间分段的标识ID
     * </pre>
     *
     * <code>uint32 SecId = 1;</code>
     */
    public int getSecId() {
      return secId_;
    }
    /**
     * <pre>
     *区间分段的标识ID
     * </pre>
     *
     * <code>uint32 SecId = 1;</code>
     */
    public Builder setSecId(int value) {
      
      secId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *区间分段的标识ID
     * </pre>
     *
     * <code>uint32 SecId = 1;</code>
     */
    public Builder clearSecId() {
      
      secId_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.LaneEx> lanes_ =
      java.util.Collections.emptyList();
    private void ensureLanesIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        lanes_ = new java.util.ArrayList<road.data.proto.LaneEx>(lanes_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.LaneEx, road.data.proto.LaneEx.Builder, road.data.proto.LaneExOrBuilder> lanesBuilder_;

    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public java.util.List<road.data.proto.LaneEx> getLanesList() {
      if (lanesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(lanes_);
      } else {
        return lanesBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public int getLanesCount() {
      if (lanesBuilder_ == null) {
        return lanes_.size();
      } else {
        return lanesBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public road.data.proto.LaneEx getLanes(int index) {
      if (lanesBuilder_ == null) {
        return lanes_.get(index);
      } else {
        return lanesBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public Builder setLanes(
        int index, road.data.proto.LaneEx value) {
      if (lanesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLanesIsMutable();
        lanes_.set(index, value);
        onChanged();
      } else {
        lanesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public Builder setLanes(
        int index, road.data.proto.LaneEx.Builder builderForValue) {
      if (lanesBuilder_ == null) {
        ensureLanesIsMutable();
        lanes_.set(index, builderForValue.build());
        onChanged();
      } else {
        lanesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public Builder addLanes(road.data.proto.LaneEx value) {
      if (lanesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLanesIsMutable();
        lanes_.add(value);
        onChanged();
      } else {
        lanesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public Builder addLanes(
        int index, road.data.proto.LaneEx value) {
      if (lanesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLanesIsMutable();
        lanes_.add(index, value);
        onChanged();
      } else {
        lanesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public Builder addLanes(
        road.data.proto.LaneEx.Builder builderForValue) {
      if (lanesBuilder_ == null) {
        ensureLanesIsMutable();
        lanes_.add(builderForValue.build());
        onChanged();
      } else {
        lanesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public Builder addLanes(
        int index, road.data.proto.LaneEx.Builder builderForValue) {
      if (lanesBuilder_ == null) {
        ensureLanesIsMutable();
        lanes_.add(index, builderForValue.build());
        onChanged();
      } else {
        lanesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public Builder addAllLanes(
        java.lang.Iterable<? extends road.data.proto.LaneEx> values) {
      if (lanesBuilder_ == null) {
        ensureLanesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, lanes_);
        onChanged();
      } else {
        lanesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public Builder clearLanes() {
      if (lanesBuilder_ == null) {
        lanes_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        lanesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public Builder removeLanes(int index) {
      if (lanesBuilder_ == null) {
        ensureLanesIsMutable();
        lanes_.remove(index);
        onChanged();
      } else {
        lanesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public road.data.proto.LaneEx.Builder getLanesBuilder(
        int index) {
      return getLanesFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public road.data.proto.LaneExOrBuilder getLanesOrBuilder(
        int index) {
      if (lanesBuilder_ == null) {
        return lanes_.get(index);  } else {
        return lanesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public java.util.List<? extends road.data.proto.LaneExOrBuilder> 
         getLanesOrBuilderList() {
      if (lanesBuilder_ != null) {
        return lanesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(lanes_);
      }
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public road.data.proto.LaneEx.Builder addLanesBuilder() {
      return getLanesFieldBuilder().addBuilder(
          road.data.proto.LaneEx.getDefaultInstance());
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public road.data.proto.LaneEx.Builder addLanesBuilder(
        int index) {
      return getLanesFieldBuilder().addBuilder(
          index, road.data.proto.LaneEx.getDefaultInstance());
    }
    /**
     * <pre>
     *车道扩展信息列表
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
     */
    public java.util.List<road.data.proto.LaneEx.Builder> 
         getLanesBuilderList() {
      return getLanesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.LaneEx, road.data.proto.LaneEx.Builder, road.data.proto.LaneExOrBuilder> 
        getLanesFieldBuilder() {
      if (lanesBuilder_ == null) {
        lanesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.LaneEx, road.data.proto.LaneEx.Builder, road.data.proto.LaneExOrBuilder>(
                lanes_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        lanes_ = null;
      }
      return lanesBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.Section)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.Section)
  private static final road.data.proto.Section DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.Section();
  }

  public static road.data.proto.Section getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Section>
      PARSER = new com.google.protobuf.AbstractParser<Section>() {
    @java.lang.Override
    public Section parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Section(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Section> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Section> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.Section getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

