// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface SectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.Section)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *区间分段的标识ID
   * </pre>
   *
   * <code>uint32 SecId = 1;</code>
   */
  int getSecId();

  /**
   * <pre>
   *车道扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
   */
  java.util.List<road.data.proto.LaneEx> 
      getLanesList();
  /**
   * <pre>
   *车道扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
   */
  road.data.proto.LaneEx getLanes(int index);
  /**
   * <pre>
   *车道扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
   */
  int getLanesCount();
  /**
   * <pre>
   *车道扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
   */
  java.util.List<? extends road.data.proto.LaneExOrBuilder> 
      getLanesOrBuilderList();
  /**
   * <pre>
   *车道扩展信息列表
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneEx lanes = 2;</code>
   */
  road.data.proto.LaneExOrBuilder getLanesOrBuilder(
      int index);
}
