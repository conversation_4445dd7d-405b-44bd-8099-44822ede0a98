// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *路段区段分段对象  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.SectionStatInfo}
 */
public  final class SectionStatInfo extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.SectionStatInfo)
    SectionStatInfoOrBuilder {
private static final long serialVersionUID = 0L;
  // Use SectionStatInfo.newBuilder() to construct.
  private SectionStatInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private SectionStatInfo() {
    extId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new SectionStatInfo();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private SectionStatInfo(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            sectionId_ = input.readUInt32();
            break;
          }
          case 18: {
            road.data.proto.LinkStatInfo.Builder subBuilder = null;
            if (linkStatInfo_ != null) {
              subBuilder = linkStatInfo_.toBuilder();
            }
            linkStatInfo_ = input.readMessage(road.data.proto.LinkStatInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(linkStatInfo_);
              linkStatInfo_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            extId_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SectionStatInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SectionStatInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.SectionStatInfo.class, road.data.proto.SectionStatInfo.Builder.class);
  }

  public static final int SECTIONID_FIELD_NUMBER = 1;
  private int sectionId_;
  /**
   * <pre>
   * 路段的区间分段编号 定义来自Section对象
   * </pre>
   *
   * <code>uint32 sectionId = 1;</code>
   */
  public int getSectionId() {
    return sectionId_;
  }

  public static final int LINKSTATINFO_FIELD_NUMBER = 2;
  private road.data.proto.LinkStatInfo linkStatInfo_;
  /**
   * <pre>
   *所属路段link的编号和信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
   */
  public boolean hasLinkStatInfo() {
    return linkStatInfo_ != null;
  }
  /**
   * <pre>
   *所属路段link的编号和信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
   */
  public road.data.proto.LinkStatInfo getLinkStatInfo() {
    return linkStatInfo_ == null ? road.data.proto.LinkStatInfo.getDefaultInstance() : linkStatInfo_;
  }
  /**
   * <pre>
   *所属路段link的编号和信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
   */
  public road.data.proto.LinkStatInfoOrBuilder getLinkStatInfoOrBuilder() {
    return getLinkStatInfo();
  }

  public static final int EXTID_FIELD_NUMBER = 3;
  private volatile java.lang.Object extId_;
  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 3;</code>
   */
  public java.lang.String getExtId() {
    java.lang.Object ref = extId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      extId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 3;</code>
   */
  public com.google.protobuf.ByteString
      getExtIdBytes() {
    java.lang.Object ref = extId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      extId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (sectionId_ != 0) {
      output.writeUInt32(1, sectionId_);
    }
    if (linkStatInfo_ != null) {
      output.writeMessage(2, getLinkStatInfo());
    }
    if (!getExtIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, extId_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (sectionId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, sectionId_);
    }
    if (linkStatInfo_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getLinkStatInfo());
    }
    if (!getExtIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, extId_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.SectionStatInfo)) {
      return super.equals(obj);
    }
    road.data.proto.SectionStatInfo other = (road.data.proto.SectionStatInfo) obj;

    if (getSectionId()
        != other.getSectionId()) return false;
    if (hasLinkStatInfo() != other.hasLinkStatInfo()) return false;
    if (hasLinkStatInfo()) {
      if (!getLinkStatInfo()
          .equals(other.getLinkStatInfo())) return false;
    }
    if (!getExtId()
        .equals(other.getExtId())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SECTIONID_FIELD_NUMBER;
    hash = (53 * hash) + getSectionId();
    if (hasLinkStatInfo()) {
      hash = (37 * hash) + LINKSTATINFO_FIELD_NUMBER;
      hash = (53 * hash) + getLinkStatInfo().hashCode();
    }
    hash = (37 * hash) + EXTID_FIELD_NUMBER;
    hash = (53 * hash) + getExtId().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.SectionStatInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SectionStatInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SectionStatInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SectionStatInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SectionStatInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SectionStatInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SectionStatInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.SectionStatInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.SectionStatInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.SectionStatInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.SectionStatInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.SectionStatInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.SectionStatInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *路段区段分段对象  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.SectionStatInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.SectionStatInfo)
      road.data.proto.SectionStatInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SectionStatInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SectionStatInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.SectionStatInfo.class, road.data.proto.SectionStatInfo.Builder.class);
    }

    // Construct using road.data.proto.SectionStatInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      sectionId_ = 0;

      if (linkStatInfoBuilder_ == null) {
        linkStatInfo_ = null;
      } else {
        linkStatInfo_ = null;
        linkStatInfoBuilder_ = null;
      }
      extId_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SectionStatInfo_descriptor;
    }

    @java.lang.Override
    public road.data.proto.SectionStatInfo getDefaultInstanceForType() {
      return road.data.proto.SectionStatInfo.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.SectionStatInfo build() {
      road.data.proto.SectionStatInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.SectionStatInfo buildPartial() {
      road.data.proto.SectionStatInfo result = new road.data.proto.SectionStatInfo(this);
      result.sectionId_ = sectionId_;
      if (linkStatInfoBuilder_ == null) {
        result.linkStatInfo_ = linkStatInfo_;
      } else {
        result.linkStatInfo_ = linkStatInfoBuilder_.build();
      }
      result.extId_ = extId_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.SectionStatInfo) {
        return mergeFrom((road.data.proto.SectionStatInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.SectionStatInfo other) {
      if (other == road.data.proto.SectionStatInfo.getDefaultInstance()) return this;
      if (other.getSectionId() != 0) {
        setSectionId(other.getSectionId());
      }
      if (other.hasLinkStatInfo()) {
        mergeLinkStatInfo(other.getLinkStatInfo());
      }
      if (!other.getExtId().isEmpty()) {
        extId_ = other.extId_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.SectionStatInfo parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.SectionStatInfo) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int sectionId_ ;
    /**
     * <pre>
     * 路段的区间分段编号 定义来自Section对象
     * </pre>
     *
     * <code>uint32 sectionId = 1;</code>
     */
    public int getSectionId() {
      return sectionId_;
    }
    /**
     * <pre>
     * 路段的区间分段编号 定义来自Section对象
     * </pre>
     *
     * <code>uint32 sectionId = 1;</code>
     */
    public Builder setSectionId(int value) {
      
      sectionId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 路段的区间分段编号 定义来自Section对象
     * </pre>
     *
     * <code>uint32 sectionId = 1;</code>
     */
    public Builder clearSectionId() {
      
      sectionId_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.LinkStatInfo linkStatInfo_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LinkStatInfo, road.data.proto.LinkStatInfo.Builder, road.data.proto.LinkStatInfoOrBuilder> linkStatInfoBuilder_;
    /**
     * <pre>
     *所属路段link的编号和信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
     */
    public boolean hasLinkStatInfo() {
      return linkStatInfoBuilder_ != null || linkStatInfo_ != null;
    }
    /**
     * <pre>
     *所属路段link的编号和信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
     */
    public road.data.proto.LinkStatInfo getLinkStatInfo() {
      if (linkStatInfoBuilder_ == null) {
        return linkStatInfo_ == null ? road.data.proto.LinkStatInfo.getDefaultInstance() : linkStatInfo_;
      } else {
        return linkStatInfoBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *所属路段link的编号和信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
     */
    public Builder setLinkStatInfo(road.data.proto.LinkStatInfo value) {
      if (linkStatInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        linkStatInfo_ = value;
        onChanged();
      } else {
        linkStatInfoBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *所属路段link的编号和信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
     */
    public Builder setLinkStatInfo(
        road.data.proto.LinkStatInfo.Builder builderForValue) {
      if (linkStatInfoBuilder_ == null) {
        linkStatInfo_ = builderForValue.build();
        onChanged();
      } else {
        linkStatInfoBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *所属路段link的编号和信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
     */
    public Builder mergeLinkStatInfo(road.data.proto.LinkStatInfo value) {
      if (linkStatInfoBuilder_ == null) {
        if (linkStatInfo_ != null) {
          linkStatInfo_ =
            road.data.proto.LinkStatInfo.newBuilder(linkStatInfo_).mergeFrom(value).buildPartial();
        } else {
          linkStatInfo_ = value;
        }
        onChanged();
      } else {
        linkStatInfoBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *所属路段link的编号和信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
     */
    public Builder clearLinkStatInfo() {
      if (linkStatInfoBuilder_ == null) {
        linkStatInfo_ = null;
        onChanged();
      } else {
        linkStatInfo_ = null;
        linkStatInfoBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *所属路段link的编号和信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
     */
    public road.data.proto.LinkStatInfo.Builder getLinkStatInfoBuilder() {
      
      onChanged();
      return getLinkStatInfoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *所属路段link的编号和信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
     */
    public road.data.proto.LinkStatInfoOrBuilder getLinkStatInfoOrBuilder() {
      if (linkStatInfoBuilder_ != null) {
        return linkStatInfoBuilder_.getMessageOrBuilder();
      } else {
        return linkStatInfo_ == null ?
            road.data.proto.LinkStatInfo.getDefaultInstance() : linkStatInfo_;
      }
    }
    /**
     * <pre>
     *所属路段link的编号和信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LinkStatInfo, road.data.proto.LinkStatInfo.Builder, road.data.proto.LinkStatInfoOrBuilder> 
        getLinkStatInfoFieldBuilder() {
      if (linkStatInfoBuilder_ == null) {
        linkStatInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LinkStatInfo, road.data.proto.LinkStatInfo.Builder, road.data.proto.LinkStatInfoOrBuilder>(
                getLinkStatInfo(),
                getParentForChildren(),
                isClean());
        linkStatInfo_ = null;
      }
      return linkStatInfoBuilder_;
    }

    private java.lang.Object extId_ = "";
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 3;</code>
     */
    public java.lang.String getExtId() {
      java.lang.Object ref = extId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        extId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 3;</code>
     */
    public com.google.protobuf.ByteString
        getExtIdBytes() {
      java.lang.Object ref = extId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 3;</code>
     */
    public Builder setExtId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      extId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 3;</code>
     */
    public Builder clearExtId() {
      
      extId_ = getDefaultInstance().getExtId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，拓展ID、保证全局唯一，根据拼接规则定义
     * </pre>
     *
     * <code>string extId = 3;</code>
     */
    public Builder setExtIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      extId_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.SectionStatInfo)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.SectionStatInfo)
  private static final road.data.proto.SectionStatInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.SectionStatInfo();
  }

  public static road.data.proto.SectionStatInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SectionStatInfo>
      PARSER = new com.google.protobuf.AbstractParser<SectionStatInfo>() {
    @java.lang.Override
    public SectionStatInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new SectionStatInfo(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<SectionStatInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SectionStatInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.SectionStatInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

