// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface SectionStatInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.SectionStatInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 路段的区间分段编号 定义来自Section对象
   * </pre>
   *
   * <code>uint32 sectionId = 1;</code>
   */
  int getSectionId();

  /**
   * <pre>
   *所属路段link的编号和信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
   */
  boolean hasLinkStatInfo();
  /**
   * <pre>
   *所属路段link的编号和信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
   */
  road.data.proto.LinkStatInfo getLinkStatInfo();
  /**
   * <pre>
   *所属路段link的编号和信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 2;</code>
   */
  road.data.proto.LinkStatInfoOrBuilder getLinkStatInfoOrBuilder();

  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 3;</code>
   */
  java.lang.String getExtId();
  /**
   * <pre>
   *可选，拓展ID、保证全局唯一，根据拼接规则定义
   * </pre>
   *
   * <code>string extId = 3;</code>
   */
  com.google.protobuf.ByteString
      getExtIdBytes();
}
