// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *信号补充元素统计值SignalControlIndexAdded  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.SignalControlIndexAdded}
 */
public  final class SignalControlIndexAdded extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.SignalControlIndexAdded)
    SignalControlIndexAddedOrBuilder {
private static final long serialVersionUID = 0L;
  // Use SignalControlIndexAdded.newBuilder() to construct.
  private SignalControlIndexAdded(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private SignalControlIndexAdded() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new SignalControlIndexAdded();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private SignalControlIndexAdded(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            phaseId_ = input.readUInt32();
            break;
          }
          case 16: {

            greenStartQueue_ = input.readUInt32();
            break;
          }
          case 24: {

            redStartQueue_ = input.readUInt32();
            break;
          }
          case 32: {

            greenUtilization_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalControlIndexAdded_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalControlIndexAdded_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.SignalControlIndexAdded.class, road.data.proto.SignalControlIndexAdded.Builder.class);
  }

  public static final int PHASEID_FIELD_NUMBER = 1;
  private int phaseId_;
  /**
   * <pre>
   *[0,255]，相位编号
   * </pre>
   *
   * <code>uint32 phaseId = 1;</code>
   */
  public int getPhaseId() {
    return phaseId_;
  }

  public static final int GREENSTARTQUEUE_FIELD_NUMBER = 2;
  private int greenStartQueue_;
  /**
   * <pre>
   *可选，绿灯启亮时的排队长度，单位0.01m
   * </pre>
   *
   * <code>uint32 greenStartQueue = 2;</code>
   */
  public int getGreenStartQueue() {
    return greenStartQueue_;
  }

  public static final int REDSTARTQUEUE_FIELD_NUMBER = 3;
  private int redStartQueue_;
  /**
   * <pre>
   *可选，红灯启亮时的二次排队长度，单位0.01m
   * </pre>
   *
   * <code>uint32 redStartQueue = 3;</code>
   */
  public int getRedStartQueue() {
    return redStartQueue_;
  }

  public static final int GREENUTILIZATION_FIELD_NUMBER = 4;
  private int greenUtilization_;
  /**
   * <pre>
   *可选，周期绿灯利用率，0.01%
   * </pre>
   *
   * <code>uint32 greenUtilization = 4;</code>
   */
  public int getGreenUtilization() {
    return greenUtilization_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (phaseId_ != 0) {
      output.writeUInt32(1, phaseId_);
    }
    if (greenStartQueue_ != 0) {
      output.writeUInt32(2, greenStartQueue_);
    }
    if (redStartQueue_ != 0) {
      output.writeUInt32(3, redStartQueue_);
    }
    if (greenUtilization_ != 0) {
      output.writeUInt32(4, greenUtilization_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (phaseId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, phaseId_);
    }
    if (greenStartQueue_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, greenStartQueue_);
    }
    if (redStartQueue_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, redStartQueue_);
    }
    if (greenUtilization_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, greenUtilization_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.SignalControlIndexAdded)) {
      return super.equals(obj);
    }
    road.data.proto.SignalControlIndexAdded other = (road.data.proto.SignalControlIndexAdded) obj;

    if (getPhaseId()
        != other.getPhaseId()) return false;
    if (getGreenStartQueue()
        != other.getGreenStartQueue()) return false;
    if (getRedStartQueue()
        != other.getRedStartQueue()) return false;
    if (getGreenUtilization()
        != other.getGreenUtilization()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + PHASEID_FIELD_NUMBER;
    hash = (53 * hash) + getPhaseId();
    hash = (37 * hash) + GREENSTARTQUEUE_FIELD_NUMBER;
    hash = (53 * hash) + getGreenStartQueue();
    hash = (37 * hash) + REDSTARTQUEUE_FIELD_NUMBER;
    hash = (53 * hash) + getRedStartQueue();
    hash = (37 * hash) + GREENUTILIZATION_FIELD_NUMBER;
    hash = (53 * hash) + getGreenUtilization();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.SignalControlIndexAdded parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SignalControlIndexAdded parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SignalControlIndexAdded parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SignalControlIndexAdded parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SignalControlIndexAdded parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SignalControlIndexAdded parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SignalControlIndexAdded parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.SignalControlIndexAdded parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.SignalControlIndexAdded parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.SignalControlIndexAdded parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.SignalControlIndexAdded parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.SignalControlIndexAdded parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.SignalControlIndexAdded prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *信号补充元素统计值SignalControlIndexAdded  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.SignalControlIndexAdded}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.SignalControlIndexAdded)
      road.data.proto.SignalControlIndexAddedOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalControlIndexAdded_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalControlIndexAdded_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.SignalControlIndexAdded.class, road.data.proto.SignalControlIndexAdded.Builder.class);
    }

    // Construct using road.data.proto.SignalControlIndexAdded.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      phaseId_ = 0;

      greenStartQueue_ = 0;

      redStartQueue_ = 0;

      greenUtilization_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalControlIndexAdded_descriptor;
    }

    @java.lang.Override
    public road.data.proto.SignalControlIndexAdded getDefaultInstanceForType() {
      return road.data.proto.SignalControlIndexAdded.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.SignalControlIndexAdded build() {
      road.data.proto.SignalControlIndexAdded result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.SignalControlIndexAdded buildPartial() {
      road.data.proto.SignalControlIndexAdded result = new road.data.proto.SignalControlIndexAdded(this);
      result.phaseId_ = phaseId_;
      result.greenStartQueue_ = greenStartQueue_;
      result.redStartQueue_ = redStartQueue_;
      result.greenUtilization_ = greenUtilization_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.SignalControlIndexAdded) {
        return mergeFrom((road.data.proto.SignalControlIndexAdded)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.SignalControlIndexAdded other) {
      if (other == road.data.proto.SignalControlIndexAdded.getDefaultInstance()) return this;
      if (other.getPhaseId() != 0) {
        setPhaseId(other.getPhaseId());
      }
      if (other.getGreenStartQueue() != 0) {
        setGreenStartQueue(other.getGreenStartQueue());
      }
      if (other.getRedStartQueue() != 0) {
        setRedStartQueue(other.getRedStartQueue());
      }
      if (other.getGreenUtilization() != 0) {
        setGreenUtilization(other.getGreenUtilization());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.SignalControlIndexAdded parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.SignalControlIndexAdded) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int phaseId_ ;
    /**
     * <pre>
     *[0,255]，相位编号
     * </pre>
     *
     * <code>uint32 phaseId = 1;</code>
     */
    public int getPhaseId() {
      return phaseId_;
    }
    /**
     * <pre>
     *[0,255]，相位编号
     * </pre>
     *
     * <code>uint32 phaseId = 1;</code>
     */
    public Builder setPhaseId(int value) {
      
      phaseId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *[0,255]，相位编号
     * </pre>
     *
     * <code>uint32 phaseId = 1;</code>
     */
    public Builder clearPhaseId() {
      
      phaseId_ = 0;
      onChanged();
      return this;
    }

    private int greenStartQueue_ ;
    /**
     * <pre>
     *可选，绿灯启亮时的排队长度，单位0.01m
     * </pre>
     *
     * <code>uint32 greenStartQueue = 2;</code>
     */
    public int getGreenStartQueue() {
      return greenStartQueue_;
    }
    /**
     * <pre>
     *可选，绿灯启亮时的排队长度，单位0.01m
     * </pre>
     *
     * <code>uint32 greenStartQueue = 2;</code>
     */
    public Builder setGreenStartQueue(int value) {
      
      greenStartQueue_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，绿灯启亮时的排队长度，单位0.01m
     * </pre>
     *
     * <code>uint32 greenStartQueue = 2;</code>
     */
    public Builder clearGreenStartQueue() {
      
      greenStartQueue_ = 0;
      onChanged();
      return this;
    }

    private int redStartQueue_ ;
    /**
     * <pre>
     *可选，红灯启亮时的二次排队长度，单位0.01m
     * </pre>
     *
     * <code>uint32 redStartQueue = 3;</code>
     */
    public int getRedStartQueue() {
      return redStartQueue_;
    }
    /**
     * <pre>
     *可选，红灯启亮时的二次排队长度，单位0.01m
     * </pre>
     *
     * <code>uint32 redStartQueue = 3;</code>
     */
    public Builder setRedStartQueue(int value) {
      
      redStartQueue_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，红灯启亮时的二次排队长度，单位0.01m
     * </pre>
     *
     * <code>uint32 redStartQueue = 3;</code>
     */
    public Builder clearRedStartQueue() {
      
      redStartQueue_ = 0;
      onChanged();
      return this;
    }

    private int greenUtilization_ ;
    /**
     * <pre>
     *可选，周期绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 greenUtilization = 4;</code>
     */
    public int getGreenUtilization() {
      return greenUtilization_;
    }
    /**
     * <pre>
     *可选，周期绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 greenUtilization = 4;</code>
     */
    public Builder setGreenUtilization(int value) {
      
      greenUtilization_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，周期绿灯利用率，0.01%
     * </pre>
     *
     * <code>uint32 greenUtilization = 4;</code>
     */
    public Builder clearGreenUtilization() {
      
      greenUtilization_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.SignalControlIndexAdded)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.SignalControlIndexAdded)
  private static final road.data.proto.SignalControlIndexAdded DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.SignalControlIndexAdded();
  }

  public static road.data.proto.SignalControlIndexAdded getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SignalControlIndexAdded>
      PARSER = new com.google.protobuf.AbstractParser<SignalControlIndexAdded>() {
    @java.lang.Override
    public SignalControlIndexAdded parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new SignalControlIndexAdded(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<SignalControlIndexAdded> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SignalControlIndexAdded> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.SignalControlIndexAdded getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

