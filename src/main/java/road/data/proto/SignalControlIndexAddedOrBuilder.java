// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface SignalControlIndexAddedOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.SignalControlIndexAdded)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *[0,255]，相位编号
   * </pre>
   *
   * <code>uint32 phaseId = 1;</code>
   */
  int getPhaseId();

  /**
   * <pre>
   *可选，绿灯启亮时的排队长度，单位0.01m
   * </pre>
   *
   * <code>uint32 greenStartQueue = 2;</code>
   */
  int getGreenStartQueue();

  /**
   * <pre>
   *可选，红灯启亮时的二次排队长度，单位0.01m
   * </pre>
   *
   * <code>uint32 redStartQueue = 3;</code>
   */
  int getRedStartQueue();

  /**
   * <pre>
   *可选，周期绿灯利用率，0.01%
   * </pre>
   *
   * <code>uint32 greenUtilization = 4;</code>
   */
  int getGreenUtilization();
}
