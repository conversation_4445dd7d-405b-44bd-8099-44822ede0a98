// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *信号优化SignalScheme    
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.SignalScheme}
 */
public  final class SignalScheme extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.SignalScheme)
    SignalSchemeOrBuilder {
private static final long serialVersionUID = 0L;
  // Use SignalScheme.newBuilder() to construct.
  private SignalScheme(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private SignalScheme() {
    optimDataList_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new SignalScheme();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private SignalScheme(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (nodeId_ != null) {
              subBuilder = nodeId_.toBuilder();
            }
            nodeId_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(nodeId_);
              nodeId_ = subBuilder.buildPartial();
            }

            break;
          }
          case 16: {

            optimType_ = input.readUInt32();
            break;
          }
          case 24: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 34: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              optimDataList_ = new java.util.ArrayList<road.data.proto.OptimData>();
              mutable_bitField0_ |= 0x00000001;
            }
            optimDataList_.add(
                input.readMessage(road.data.proto.OptimData.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        optimDataList_ = java.util.Collections.unmodifiableList(optimDataList_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalScheme_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalScheme_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.SignalScheme.class, road.data.proto.SignalScheme.Builder.class);
  }

  public static final int NODEID_FIELD_NUMBER = 1;
  private road.data.proto.NodeReferenceId nodeId_;
  /**
   * <pre>
   *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  public boolean hasNodeId() {
    return nodeId_ != null;
  }
  /**
   * <pre>
   *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  public road.data.proto.NodeReferenceId getNodeId() {
    return nodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
  }
  /**
   * <pre>
   *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder() {
    return getNodeId();
  }

  public static final int OPTIMTYPE_FIELD_NUMBER = 2;
  private int optimType_;
  /**
   * <pre>
   *1:实时优化（优化近期时段的方案建议）；2:基于历史数据的优化（全时段的方案建议）；
   * </pre>
   *
   * <code>uint32 optimType = 2;</code>
   */
  public int getOptimType() {
    return optimType_;
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 3;
  private long timestamp_;
  /**
   * <pre>
   *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 3;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int OPTIMDATALIST_FIELD_NUMBER = 4;
  private java.util.List<road.data.proto.OptimData> optimDataList_;
  /**
   * <pre>
   *可选，分时段的优化方案建议
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
   */
  public java.util.List<road.data.proto.OptimData> getOptimDataListList() {
    return optimDataList_;
  }
  /**
   * <pre>
   *可选，分时段的优化方案建议
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
   */
  public java.util.List<? extends road.data.proto.OptimDataOrBuilder> 
      getOptimDataListOrBuilderList() {
    return optimDataList_;
  }
  /**
   * <pre>
   *可选，分时段的优化方案建议
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
   */
  public int getOptimDataListCount() {
    return optimDataList_.size();
  }
  /**
   * <pre>
   *可选，分时段的优化方案建议
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
   */
  public road.data.proto.OptimData getOptimDataList(int index) {
    return optimDataList_.get(index);
  }
  /**
   * <pre>
   *可选，分时段的优化方案建议
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
   */
  public road.data.proto.OptimDataOrBuilder getOptimDataListOrBuilder(
      int index) {
    return optimDataList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (nodeId_ != null) {
      output.writeMessage(1, getNodeId());
    }
    if (optimType_ != 0) {
      output.writeUInt32(2, optimType_);
    }
    if (timestamp_ != 0L) {
      output.writeUInt64(3, timestamp_);
    }
    for (int i = 0; i < optimDataList_.size(); i++) {
      output.writeMessage(4, optimDataList_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (nodeId_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getNodeId());
    }
    if (optimType_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, optimType_);
    }
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, timestamp_);
    }
    for (int i = 0; i < optimDataList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, optimDataList_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.SignalScheme)) {
      return super.equals(obj);
    }
    road.data.proto.SignalScheme other = (road.data.proto.SignalScheme) obj;

    if (hasNodeId() != other.hasNodeId()) return false;
    if (hasNodeId()) {
      if (!getNodeId()
          .equals(other.getNodeId())) return false;
    }
    if (getOptimType()
        != other.getOptimType()) return false;
    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (!getOptimDataListList()
        .equals(other.getOptimDataListList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasNodeId()) {
      hash = (37 * hash) + NODEID_FIELD_NUMBER;
      hash = (53 * hash) + getNodeId().hashCode();
    }
    hash = (37 * hash) + OPTIMTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getOptimType();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    if (getOptimDataListCount() > 0) {
      hash = (37 * hash) + OPTIMDATALIST_FIELD_NUMBER;
      hash = (53 * hash) + getOptimDataListList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.SignalScheme parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SignalScheme parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SignalScheme parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SignalScheme parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SignalScheme parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SignalScheme parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SignalScheme parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.SignalScheme parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.SignalScheme parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.SignalScheme parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.SignalScheme parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.SignalScheme parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.SignalScheme prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *信号优化SignalScheme    
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.SignalScheme}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.SignalScheme)
      road.data.proto.SignalSchemeOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalScheme_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalScheme_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.SignalScheme.class, road.data.proto.SignalScheme.Builder.class);
    }

    // Construct using road.data.proto.SignalScheme.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getOptimDataListFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (nodeIdBuilder_ == null) {
        nodeId_ = null;
      } else {
        nodeId_ = null;
        nodeIdBuilder_ = null;
      }
      optimType_ = 0;

      timestamp_ = 0L;

      if (optimDataListBuilder_ == null) {
        optimDataList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        optimDataListBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalScheme_descriptor;
    }

    @java.lang.Override
    public road.data.proto.SignalScheme getDefaultInstanceForType() {
      return road.data.proto.SignalScheme.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.SignalScheme build() {
      road.data.proto.SignalScheme result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.SignalScheme buildPartial() {
      road.data.proto.SignalScheme result = new road.data.proto.SignalScheme(this);
      int from_bitField0_ = bitField0_;
      if (nodeIdBuilder_ == null) {
        result.nodeId_ = nodeId_;
      } else {
        result.nodeId_ = nodeIdBuilder_.build();
      }
      result.optimType_ = optimType_;
      result.timestamp_ = timestamp_;
      if (optimDataListBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          optimDataList_ = java.util.Collections.unmodifiableList(optimDataList_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.optimDataList_ = optimDataList_;
      } else {
        result.optimDataList_ = optimDataListBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.SignalScheme) {
        return mergeFrom((road.data.proto.SignalScheme)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.SignalScheme other) {
      if (other == road.data.proto.SignalScheme.getDefaultInstance()) return this;
      if (other.hasNodeId()) {
        mergeNodeId(other.getNodeId());
      }
      if (other.getOptimType() != 0) {
        setOptimType(other.getOptimType());
      }
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (optimDataListBuilder_ == null) {
        if (!other.optimDataList_.isEmpty()) {
          if (optimDataList_.isEmpty()) {
            optimDataList_ = other.optimDataList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureOptimDataListIsMutable();
            optimDataList_.addAll(other.optimDataList_);
          }
          onChanged();
        }
      } else {
        if (!other.optimDataList_.isEmpty()) {
          if (optimDataListBuilder_.isEmpty()) {
            optimDataListBuilder_.dispose();
            optimDataListBuilder_ = null;
            optimDataList_ = other.optimDataList_;
            bitField0_ = (bitField0_ & ~0x00000001);
            optimDataListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getOptimDataListFieldBuilder() : null;
          } else {
            optimDataListBuilder_.addAllMessages(other.optimDataList_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.SignalScheme parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.SignalScheme) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private road.data.proto.NodeReferenceId nodeId_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> nodeIdBuilder_;
    /**
     * <pre>
     *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public boolean hasNodeId() {
      return nodeIdBuilder_ != null || nodeId_ != null;
    }
    /**
     * <pre>
     *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceId getNodeId() {
      if (nodeIdBuilder_ == null) {
        return nodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
      } else {
        return nodeIdBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder setNodeId(road.data.proto.NodeReferenceId value) {
      if (nodeIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        nodeId_ = value;
        onChanged();
      } else {
        nodeIdBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder setNodeId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (nodeIdBuilder_ == null) {
        nodeId_ = builderForValue.build();
        onChanged();
      } else {
        nodeIdBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder mergeNodeId(road.data.proto.NodeReferenceId value) {
      if (nodeIdBuilder_ == null) {
        if (nodeId_ != null) {
          nodeId_ =
            road.data.proto.NodeReferenceId.newBuilder(nodeId_).mergeFrom(value).buildPartial();
        } else {
          nodeId_ = value;
        }
        onChanged();
      } else {
        nodeIdBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder clearNodeId() {
      if (nodeIdBuilder_ == null) {
        nodeId_ = null;
        onChanged();
      } else {
        nodeId_ = null;
        nodeIdBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getNodeIdBuilder() {
      
      onChanged();
      return getNodeIdFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder() {
      if (nodeIdBuilder_ != null) {
        return nodeIdBuilder_.getMessageOrBuilder();
      } else {
        return nodeId_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
      }
    }
    /**
     * <pre>
     *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getNodeIdFieldBuilder() {
      if (nodeIdBuilder_ == null) {
        nodeIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getNodeId(),
                getParentForChildren(),
                isClean());
        nodeId_ = null;
      }
      return nodeIdBuilder_;
    }

    private int optimType_ ;
    /**
     * <pre>
     *1:实时优化（优化近期时段的方案建议）；2:基于历史数据的优化（全时段的方案建议）；
     * </pre>
     *
     * <code>uint32 optimType = 2;</code>
     */
    public int getOptimType() {
      return optimType_;
    }
    /**
     * <pre>
     *1:实时优化（优化近期时段的方案建议）；2:基于历史数据的优化（全时段的方案建议）；
     * </pre>
     *
     * <code>uint32 optimType = 2;</code>
     */
    public Builder setOptimType(int value) {
      
      optimType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *1:实时优化（优化近期时段的方案建议）；2:基于历史数据的优化（全时段的方案建议）；
     * </pre>
     *
     * <code>uint32 optimType = 2;</code>
     */
    public Builder clearOptimType() {
      
      optimType_ = 0;
      onChanged();
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.OptimData> optimDataList_ =
      java.util.Collections.emptyList();
    private void ensureOptimDataListIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        optimDataList_ = new java.util.ArrayList<road.data.proto.OptimData>(optimDataList_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.OptimData, road.data.proto.OptimData.Builder, road.data.proto.OptimDataOrBuilder> optimDataListBuilder_;

    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public java.util.List<road.data.proto.OptimData> getOptimDataListList() {
      if (optimDataListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(optimDataList_);
      } else {
        return optimDataListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public int getOptimDataListCount() {
      if (optimDataListBuilder_ == null) {
        return optimDataList_.size();
      } else {
        return optimDataListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public road.data.proto.OptimData getOptimDataList(int index) {
      if (optimDataListBuilder_ == null) {
        return optimDataList_.get(index);
      } else {
        return optimDataListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public Builder setOptimDataList(
        int index, road.data.proto.OptimData value) {
      if (optimDataListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOptimDataListIsMutable();
        optimDataList_.set(index, value);
        onChanged();
      } else {
        optimDataListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public Builder setOptimDataList(
        int index, road.data.proto.OptimData.Builder builderForValue) {
      if (optimDataListBuilder_ == null) {
        ensureOptimDataListIsMutable();
        optimDataList_.set(index, builderForValue.build());
        onChanged();
      } else {
        optimDataListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public Builder addOptimDataList(road.data.proto.OptimData value) {
      if (optimDataListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOptimDataListIsMutable();
        optimDataList_.add(value);
        onChanged();
      } else {
        optimDataListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public Builder addOptimDataList(
        int index, road.data.proto.OptimData value) {
      if (optimDataListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOptimDataListIsMutable();
        optimDataList_.add(index, value);
        onChanged();
      } else {
        optimDataListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public Builder addOptimDataList(
        road.data.proto.OptimData.Builder builderForValue) {
      if (optimDataListBuilder_ == null) {
        ensureOptimDataListIsMutable();
        optimDataList_.add(builderForValue.build());
        onChanged();
      } else {
        optimDataListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public Builder addOptimDataList(
        int index, road.data.proto.OptimData.Builder builderForValue) {
      if (optimDataListBuilder_ == null) {
        ensureOptimDataListIsMutable();
        optimDataList_.add(index, builderForValue.build());
        onChanged();
      } else {
        optimDataListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public Builder addAllOptimDataList(
        java.lang.Iterable<? extends road.data.proto.OptimData> values) {
      if (optimDataListBuilder_ == null) {
        ensureOptimDataListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, optimDataList_);
        onChanged();
      } else {
        optimDataListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public Builder clearOptimDataList() {
      if (optimDataListBuilder_ == null) {
        optimDataList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        optimDataListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public Builder removeOptimDataList(int index) {
      if (optimDataListBuilder_ == null) {
        ensureOptimDataListIsMutable();
        optimDataList_.remove(index);
        onChanged();
      } else {
        optimDataListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public road.data.proto.OptimData.Builder getOptimDataListBuilder(
        int index) {
      return getOptimDataListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public road.data.proto.OptimDataOrBuilder getOptimDataListOrBuilder(
        int index) {
      if (optimDataListBuilder_ == null) {
        return optimDataList_.get(index);  } else {
        return optimDataListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public java.util.List<? extends road.data.proto.OptimDataOrBuilder> 
         getOptimDataListOrBuilderList() {
      if (optimDataListBuilder_ != null) {
        return optimDataListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(optimDataList_);
      }
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public road.data.proto.OptimData.Builder addOptimDataListBuilder() {
      return getOptimDataListFieldBuilder().addBuilder(
          road.data.proto.OptimData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public road.data.proto.OptimData.Builder addOptimDataListBuilder(
        int index) {
      return getOptimDataListFieldBuilder().addBuilder(
          index, road.data.proto.OptimData.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，分时段的优化方案建议
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
     */
    public java.util.List<road.data.proto.OptimData.Builder> 
         getOptimDataListBuilderList() {
      return getOptimDataListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.OptimData, road.data.proto.OptimData.Builder, road.data.proto.OptimDataOrBuilder> 
        getOptimDataListFieldBuilder() {
      if (optimDataListBuilder_ == null) {
        optimDataListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.OptimData, road.data.proto.OptimData.Builder, road.data.proto.OptimDataOrBuilder>(
                optimDataList_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        optimDataList_ = null;
      }
      return optimDataListBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.SignalScheme)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.SignalScheme)
  private static final road.data.proto.SignalScheme DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.SignalScheme();
  }

  public static road.data.proto.SignalScheme getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SignalScheme>
      PARSER = new com.google.protobuf.AbstractParser<SignalScheme>() {
    @java.lang.Override
    public SignalScheme parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new SignalScheme(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<SignalScheme> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SignalScheme> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.SignalScheme getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

