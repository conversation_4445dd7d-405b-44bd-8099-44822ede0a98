// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface SignalSchemeOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.SignalScheme)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  boolean hasNodeId();
  /**
   * <pre>
   *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  road.data.proto.NodeReferenceId getNodeId();
  /**
   * <pre>
   *交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder();

  /**
   * <pre>
   *1:实时优化（优化近期时段的方案建议）；2:基于历史数据的优化（全时段的方案建议）；
   * </pre>
   *
   * <code>uint32 optimType = 2;</code>
   */
  int getOptimType();

  /**
   * <pre>
   *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 3;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   *可选，分时段的优化方案建议
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
   */
  java.util.List<road.data.proto.OptimData> 
      getOptimDataListList();
  /**
   * <pre>
   *可选，分时段的优化方案建议
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
   */
  road.data.proto.OptimData getOptimDataList(int index);
  /**
   * <pre>
   *可选，分时段的优化方案建议
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
   */
  int getOptimDataListCount();
  /**
   * <pre>
   *可选，分时段的优化方案建议
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
   */
  java.util.List<? extends road.data.proto.OptimDataOrBuilder> 
      getOptimDataListOrBuilderList();
  /**
   * <pre>
   *可选，分时段的优化方案建议
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.OptimData optimDataList = 4;</code>
   */
  road.data.proto.OptimDataOrBuilder getOptimDataListOrBuilder(
      int index);
}
