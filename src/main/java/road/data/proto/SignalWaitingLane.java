// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *特定信号灯相位的等待区域 SignalWaitingLane               
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.SignalWaitingLane}
 */
public  final class SignalWaitingLane extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.SignalWaitingLane)
    SignalWaitingLaneOrBuilder {
private static final long serialVersionUID = 0L;
  // Use SignalWaitingLane.newBuilder() to construct.
  private SignalWaitingLane(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private SignalWaitingLane() {
    allowedPhaseIds_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new SignalWaitingLane();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private SignalWaitingLane(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            laneWidth_ = input.readInt32();
            break;
          }
          case 18: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (points_ != null) {
              subBuilder = points_.toBuilder();
            }
            points_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(points_);
              points_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              allowedPhaseIds_ = new java.util.ArrayList<road.data.proto.PhaseId>();
              mutable_bitField0_ |= 0x00000001;
            }
            allowedPhaseIds_.add(
                input.readMessage(road.data.proto.PhaseId.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        allowedPhaseIds_ = java.util.Collections.unmodifiableList(allowedPhaseIds_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalWaitingLane_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalWaitingLane_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.SignalWaitingLane.class, road.data.proto.SignalWaitingLane.Builder.class);
  }

  public static final int LANEWIDTH_FIELD_NUMBER = 1;
  private int laneWidth_;
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>int32 laneWidth = 1;</code>
   */
  public int getLaneWidth() {
    return laneWidth_;
  }

  public static final int POINTS_FIELD_NUMBER = 2;
  private road.data.proto.Position3D points_;
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
   */
  public boolean hasPoints() {
    return points_ != null;
  }
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
   */
  public road.data.proto.Position3D getPoints() {
    return points_ == null ? road.data.proto.Position3D.getDefaultInstance() : points_;
  }
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
   */
  public road.data.proto.Position3DOrBuilder getPointsOrBuilder() {
    return getPoints();
  }

  public static final int ALLOWEDPHASEIDS_FIELD_NUMBER = 3;
  private java.util.List<road.data.proto.PhaseId> allowedPhaseIds_;
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
   */
  public java.util.List<road.data.proto.PhaseId> getAllowedPhaseIdsList() {
    return allowedPhaseIds_;
  }
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
   */
  public java.util.List<? extends road.data.proto.PhaseIdOrBuilder> 
      getAllowedPhaseIdsOrBuilderList() {
    return allowedPhaseIds_;
  }
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
   */
  public int getAllowedPhaseIdsCount() {
    return allowedPhaseIds_.size();
  }
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
   */
  public road.data.proto.PhaseId getAllowedPhaseIds(int index) {
    return allowedPhaseIds_.get(index);
  }
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
   */
  public road.data.proto.PhaseIdOrBuilder getAllowedPhaseIdsOrBuilder(
      int index) {
    return allowedPhaseIds_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (laneWidth_ != 0) {
      output.writeInt32(1, laneWidth_);
    }
    if (points_ != null) {
      output.writeMessage(2, getPoints());
    }
    for (int i = 0; i < allowedPhaseIds_.size(); i++) {
      output.writeMessage(3, allowedPhaseIds_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (laneWidth_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, laneWidth_);
    }
    if (points_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getPoints());
    }
    for (int i = 0; i < allowedPhaseIds_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, allowedPhaseIds_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.SignalWaitingLane)) {
      return super.equals(obj);
    }
    road.data.proto.SignalWaitingLane other = (road.data.proto.SignalWaitingLane) obj;

    if (getLaneWidth()
        != other.getLaneWidth()) return false;
    if (hasPoints() != other.hasPoints()) return false;
    if (hasPoints()) {
      if (!getPoints()
          .equals(other.getPoints())) return false;
    }
    if (!getAllowedPhaseIdsList()
        .equals(other.getAllowedPhaseIdsList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + LANEWIDTH_FIELD_NUMBER;
    hash = (53 * hash) + getLaneWidth();
    if (hasPoints()) {
      hash = (37 * hash) + POINTS_FIELD_NUMBER;
      hash = (53 * hash) + getPoints().hashCode();
    }
    if (getAllowedPhaseIdsCount() > 0) {
      hash = (37 * hash) + ALLOWEDPHASEIDS_FIELD_NUMBER;
      hash = (53 * hash) + getAllowedPhaseIdsList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.SignalWaitingLane parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SignalWaitingLane parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SignalWaitingLane parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SignalWaitingLane parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SignalWaitingLane parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SignalWaitingLane parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SignalWaitingLane parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.SignalWaitingLane parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.SignalWaitingLane parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.SignalWaitingLane parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.SignalWaitingLane parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.SignalWaitingLane parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.SignalWaitingLane prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *特定信号灯相位的等待区域 SignalWaitingLane               
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.SignalWaitingLane}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.SignalWaitingLane)
      road.data.proto.SignalWaitingLaneOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalWaitingLane_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalWaitingLane_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.SignalWaitingLane.class, road.data.proto.SignalWaitingLane.Builder.class);
    }

    // Construct using road.data.proto.SignalWaitingLane.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getAllowedPhaseIdsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      laneWidth_ = 0;

      if (pointsBuilder_ == null) {
        points_ = null;
      } else {
        points_ = null;
        pointsBuilder_ = null;
      }
      if (allowedPhaseIdsBuilder_ == null) {
        allowedPhaseIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        allowedPhaseIdsBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SignalWaitingLane_descriptor;
    }

    @java.lang.Override
    public road.data.proto.SignalWaitingLane getDefaultInstanceForType() {
      return road.data.proto.SignalWaitingLane.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.SignalWaitingLane build() {
      road.data.proto.SignalWaitingLane result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.SignalWaitingLane buildPartial() {
      road.data.proto.SignalWaitingLane result = new road.data.proto.SignalWaitingLane(this);
      int from_bitField0_ = bitField0_;
      result.laneWidth_ = laneWidth_;
      if (pointsBuilder_ == null) {
        result.points_ = points_;
      } else {
        result.points_ = pointsBuilder_.build();
      }
      if (allowedPhaseIdsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          allowedPhaseIds_ = java.util.Collections.unmodifiableList(allowedPhaseIds_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.allowedPhaseIds_ = allowedPhaseIds_;
      } else {
        result.allowedPhaseIds_ = allowedPhaseIdsBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.SignalWaitingLane) {
        return mergeFrom((road.data.proto.SignalWaitingLane)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.SignalWaitingLane other) {
      if (other == road.data.proto.SignalWaitingLane.getDefaultInstance()) return this;
      if (other.getLaneWidth() != 0) {
        setLaneWidth(other.getLaneWidth());
      }
      if (other.hasPoints()) {
        mergePoints(other.getPoints());
      }
      if (allowedPhaseIdsBuilder_ == null) {
        if (!other.allowedPhaseIds_.isEmpty()) {
          if (allowedPhaseIds_.isEmpty()) {
            allowedPhaseIds_ = other.allowedPhaseIds_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureAllowedPhaseIdsIsMutable();
            allowedPhaseIds_.addAll(other.allowedPhaseIds_);
          }
          onChanged();
        }
      } else {
        if (!other.allowedPhaseIds_.isEmpty()) {
          if (allowedPhaseIdsBuilder_.isEmpty()) {
            allowedPhaseIdsBuilder_.dispose();
            allowedPhaseIdsBuilder_ = null;
            allowedPhaseIds_ = other.allowedPhaseIds_;
            bitField0_ = (bitField0_ & ~0x00000001);
            allowedPhaseIdsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getAllowedPhaseIdsFieldBuilder() : null;
          } else {
            allowedPhaseIdsBuilder_.addAllMessages(other.allowedPhaseIds_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.SignalWaitingLane parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.SignalWaitingLane) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int laneWidth_ ;
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>int32 laneWidth = 1;</code>
     */
    public int getLaneWidth() {
      return laneWidth_;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>int32 laneWidth = 1;</code>
     */
    public Builder setLaneWidth(int value) {
      
      laneWidth_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>int32 laneWidth = 1;</code>
     */
    public Builder clearLaneWidth() {
      
      laneWidth_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.Position3D points_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> pointsBuilder_;
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
     */
    public boolean hasPoints() {
      return pointsBuilder_ != null || points_ != null;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
     */
    public road.data.proto.Position3D getPoints() {
      if (pointsBuilder_ == null) {
        return points_ == null ? road.data.proto.Position3D.getDefaultInstance() : points_;
      } else {
        return pointsBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
     */
    public Builder setPoints(road.data.proto.Position3D value) {
      if (pointsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        points_ = value;
        onChanged();
      } else {
        pointsBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
     */
    public Builder setPoints(
        road.data.proto.Position3D.Builder builderForValue) {
      if (pointsBuilder_ == null) {
        points_ = builderForValue.build();
        onChanged();
      } else {
        pointsBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
     */
    public Builder mergePoints(road.data.proto.Position3D value) {
      if (pointsBuilder_ == null) {
        if (points_ != null) {
          points_ =
            road.data.proto.Position3D.newBuilder(points_).mergeFrom(value).buildPartial();
        } else {
          points_ = value;
        }
        onChanged();
      } else {
        pointsBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
     */
    public Builder clearPoints() {
      if (pointsBuilder_ == null) {
        points_ = null;
        onChanged();
      } else {
        points_ = null;
        pointsBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
     */
    public road.data.proto.Position3D.Builder getPointsBuilder() {
      
      onChanged();
      return getPointsFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
     */
    public road.data.proto.Position3DOrBuilder getPointsOrBuilder() {
      if (pointsBuilder_ != null) {
        return pointsBuilder_.getMessageOrBuilder();
      } else {
        return points_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : points_;
      }
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getPointsFieldBuilder() {
      if (pointsBuilder_ == null) {
        pointsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getPoints(),
                getParentForChildren(),
                isClean());
        points_ = null;
      }
      return pointsBuilder_;
    }

    private java.util.List<road.data.proto.PhaseId> allowedPhaseIds_ =
      java.util.Collections.emptyList();
    private void ensureAllowedPhaseIdsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        allowedPhaseIds_ = new java.util.ArrayList<road.data.proto.PhaseId>(allowedPhaseIds_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.PhaseId, road.data.proto.PhaseId.Builder, road.data.proto.PhaseIdOrBuilder> allowedPhaseIdsBuilder_;

    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public java.util.List<road.data.proto.PhaseId> getAllowedPhaseIdsList() {
      if (allowedPhaseIdsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(allowedPhaseIds_);
      } else {
        return allowedPhaseIdsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public int getAllowedPhaseIdsCount() {
      if (allowedPhaseIdsBuilder_ == null) {
        return allowedPhaseIds_.size();
      } else {
        return allowedPhaseIdsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public road.data.proto.PhaseId getAllowedPhaseIds(int index) {
      if (allowedPhaseIdsBuilder_ == null) {
        return allowedPhaseIds_.get(index);
      } else {
        return allowedPhaseIdsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public Builder setAllowedPhaseIds(
        int index, road.data.proto.PhaseId value) {
      if (allowedPhaseIdsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAllowedPhaseIdsIsMutable();
        allowedPhaseIds_.set(index, value);
        onChanged();
      } else {
        allowedPhaseIdsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public Builder setAllowedPhaseIds(
        int index, road.data.proto.PhaseId.Builder builderForValue) {
      if (allowedPhaseIdsBuilder_ == null) {
        ensureAllowedPhaseIdsIsMutable();
        allowedPhaseIds_.set(index, builderForValue.build());
        onChanged();
      } else {
        allowedPhaseIdsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public Builder addAllowedPhaseIds(road.data.proto.PhaseId value) {
      if (allowedPhaseIdsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAllowedPhaseIdsIsMutable();
        allowedPhaseIds_.add(value);
        onChanged();
      } else {
        allowedPhaseIdsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public Builder addAllowedPhaseIds(
        int index, road.data.proto.PhaseId value) {
      if (allowedPhaseIdsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAllowedPhaseIdsIsMutable();
        allowedPhaseIds_.add(index, value);
        onChanged();
      } else {
        allowedPhaseIdsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public Builder addAllowedPhaseIds(
        road.data.proto.PhaseId.Builder builderForValue) {
      if (allowedPhaseIdsBuilder_ == null) {
        ensureAllowedPhaseIdsIsMutable();
        allowedPhaseIds_.add(builderForValue.build());
        onChanged();
      } else {
        allowedPhaseIdsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public Builder addAllowedPhaseIds(
        int index, road.data.proto.PhaseId.Builder builderForValue) {
      if (allowedPhaseIdsBuilder_ == null) {
        ensureAllowedPhaseIdsIsMutable();
        allowedPhaseIds_.add(index, builderForValue.build());
        onChanged();
      } else {
        allowedPhaseIdsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public Builder addAllAllowedPhaseIds(
        java.lang.Iterable<? extends road.data.proto.PhaseId> values) {
      if (allowedPhaseIdsBuilder_ == null) {
        ensureAllowedPhaseIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, allowedPhaseIds_);
        onChanged();
      } else {
        allowedPhaseIdsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public Builder clearAllowedPhaseIds() {
      if (allowedPhaseIdsBuilder_ == null) {
        allowedPhaseIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        allowedPhaseIdsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public Builder removeAllowedPhaseIds(int index) {
      if (allowedPhaseIdsBuilder_ == null) {
        ensureAllowedPhaseIdsIsMutable();
        allowedPhaseIds_.remove(index);
        onChanged();
      } else {
        allowedPhaseIdsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public road.data.proto.PhaseId.Builder getAllowedPhaseIdsBuilder(
        int index) {
      return getAllowedPhaseIdsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public road.data.proto.PhaseIdOrBuilder getAllowedPhaseIdsOrBuilder(
        int index) {
      if (allowedPhaseIdsBuilder_ == null) {
        return allowedPhaseIds_.get(index);  } else {
        return allowedPhaseIdsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public java.util.List<? extends road.data.proto.PhaseIdOrBuilder> 
         getAllowedPhaseIdsOrBuilderList() {
      if (allowedPhaseIdsBuilder_ != null) {
        return allowedPhaseIdsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(allowedPhaseIds_);
      }
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public road.data.proto.PhaseId.Builder addAllowedPhaseIdsBuilder() {
      return getAllowedPhaseIdsFieldBuilder().addBuilder(
          road.data.proto.PhaseId.getDefaultInstance());
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public road.data.proto.PhaseId.Builder addAllowedPhaseIdsBuilder(
        int index) {
      return getAllowedPhaseIdsFieldBuilder().addBuilder(
          index, road.data.proto.PhaseId.getDefaultInstance());
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
     */
    public java.util.List<road.data.proto.PhaseId.Builder> 
         getAllowedPhaseIdsBuilderList() {
      return getAllowedPhaseIdsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.PhaseId, road.data.proto.PhaseId.Builder, road.data.proto.PhaseIdOrBuilder> 
        getAllowedPhaseIdsFieldBuilder() {
      if (allowedPhaseIdsBuilder_ == null) {
        allowedPhaseIdsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.PhaseId, road.data.proto.PhaseId.Builder, road.data.proto.PhaseIdOrBuilder>(
                allowedPhaseIds_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        allowedPhaseIds_ = null;
      }
      return allowedPhaseIdsBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.SignalWaitingLane)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.SignalWaitingLane)
  private static final road.data.proto.SignalWaitingLane DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.SignalWaitingLane();
  }

  public static road.data.proto.SignalWaitingLane getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SignalWaitingLane>
      PARSER = new com.google.protobuf.AbstractParser<SignalWaitingLane>() {
    @java.lang.Override
    public SignalWaitingLane parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new SignalWaitingLane(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<SignalWaitingLane> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SignalWaitingLane> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.SignalWaitingLane getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

