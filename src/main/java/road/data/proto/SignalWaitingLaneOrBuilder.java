// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface SignalWaitingLaneOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.SignalWaitingLane)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *
   * </pre>
   *
   * <code>int32 laneWidth = 1;</code>
   */
  int getLaneWidth();

  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
   */
  boolean hasPoints();
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
   */
  road.data.proto.Position3D getPoints();
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D points = 2;</code>
   */
  road.data.proto.Position3DOrBuilder getPointsOrBuilder();

  /**
   * <pre>
   *
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
   */
  java.util.List<road.data.proto.PhaseId> 
      getAllowedPhaseIdsList();
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
   */
  road.data.proto.PhaseId getAllowedPhaseIds(int index);
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
   */
  int getAllowedPhaseIdsCount();
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
   */
  java.util.List<? extends road.data.proto.PhaseIdOrBuilder> 
      getAllowedPhaseIdsOrBuilderList();
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.PhaseId allowedPhaseIds = 3;</code>
   */
  road.data.proto.PhaseIdOrBuilder getAllowedPhaseIdsOrBuilder(
      int index);
}
