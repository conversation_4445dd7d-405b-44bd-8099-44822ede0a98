// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface SingleTimeSpanOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.SingleTimeSpan)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *优化时段开始时间
   * </pre>
   *
   * <code>uint64 startTime = 1;</code>
   */
  long getStartTime();

  /**
   * <pre>
   *UNIX时间戳（秒级）//
   * </pre>
   *
   * <code>uint64 endTime = 2;</code>
   */
  long getEndTime();
}
