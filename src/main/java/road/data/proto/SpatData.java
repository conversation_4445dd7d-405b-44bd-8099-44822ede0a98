// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *信号状态采集SpatData    
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.SpatData}
 */
public  final class SpatData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.SpatData)
    SpatDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use SpatData.newBuilder() to construct.
  private SpatData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private SpatData() {
    intersections_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new SpatData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private SpatData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            msgCnt_ = input.readUInt32();
            break;
          }
          case 16: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              intersections_ = new java.util.ArrayList<road.data.proto.IntersectionState>();
              mutable_bitField0_ |= 0x00000001;
            }
            intersections_.add(
                input.readMessage(road.data.proto.IntersectionState.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        intersections_ = java.util.Collections.unmodifiableList(intersections_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SpatData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SpatData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.SpatData.class, road.data.proto.SpatData.Builder.class);
  }

  public static final int MSGCNT_FIELD_NUMBER = 1;
  private int msgCnt_;
  /**
   * <pre>
   *将 msgCount 初始化为一个随机值，其范围为 0 到 127。
   * </pre>
   *
   * <code>uint32 msgCnt = 1;</code>
   */
  public int getMsgCnt() {
    return msgCnt_;
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 2;
  private long timestamp_;
  /**
   * <pre>
   * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 2;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int INTERSECTIONS_FIELD_NUMBER = 3;
  private java.util.List<road.data.proto.IntersectionState> intersections_;
  /**
   * <pre>
   * 多个路口信号灯的属性和当前状态。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
   */
  public java.util.List<road.data.proto.IntersectionState> getIntersectionsList() {
    return intersections_;
  }
  /**
   * <pre>
   * 多个路口信号灯的属性和当前状态。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
   */
  public java.util.List<? extends road.data.proto.IntersectionStateOrBuilder> 
      getIntersectionsOrBuilderList() {
    return intersections_;
  }
  /**
   * <pre>
   * 多个路口信号灯的属性和当前状态。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
   */
  public int getIntersectionsCount() {
    return intersections_.size();
  }
  /**
   * <pre>
   * 多个路口信号灯的属性和当前状态。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
   */
  public road.data.proto.IntersectionState getIntersections(int index) {
    return intersections_.get(index);
  }
  /**
   * <pre>
   * 多个路口信号灯的属性和当前状态。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
   */
  public road.data.proto.IntersectionStateOrBuilder getIntersectionsOrBuilder(
      int index) {
    return intersections_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (msgCnt_ != 0) {
      output.writeUInt32(1, msgCnt_);
    }
    if (timestamp_ != 0L) {
      output.writeUInt64(2, timestamp_);
    }
    for (int i = 0; i < intersections_.size(); i++) {
      output.writeMessage(3, intersections_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (msgCnt_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, msgCnt_);
    }
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(2, timestamp_);
    }
    for (int i = 0; i < intersections_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, intersections_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.SpatData)) {
      return super.equals(obj);
    }
    road.data.proto.SpatData other = (road.data.proto.SpatData) obj;

    if (getMsgCnt()
        != other.getMsgCnt()) return false;
    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (!getIntersectionsList()
        .equals(other.getIntersectionsList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + MSGCNT_FIELD_NUMBER;
    hash = (53 * hash) + getMsgCnt();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    if (getIntersectionsCount() > 0) {
      hash = (37 * hash) + INTERSECTIONS_FIELD_NUMBER;
      hash = (53 * hash) + getIntersectionsList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.SpatData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SpatData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SpatData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SpatData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SpatData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.SpatData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.SpatData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.SpatData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.SpatData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.SpatData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.SpatData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.SpatData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.SpatData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *信号状态采集SpatData    
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.SpatData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.SpatData)
      road.data.proto.SpatDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SpatData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SpatData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.SpatData.class, road.data.proto.SpatData.Builder.class);
    }

    // Construct using road.data.proto.SpatData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getIntersectionsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      msgCnt_ = 0;

      timestamp_ = 0L;

      if (intersectionsBuilder_ == null) {
        intersections_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        intersectionsBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_SpatData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.SpatData getDefaultInstanceForType() {
      return road.data.proto.SpatData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.SpatData build() {
      road.data.proto.SpatData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.SpatData buildPartial() {
      road.data.proto.SpatData result = new road.data.proto.SpatData(this);
      int from_bitField0_ = bitField0_;
      result.msgCnt_ = msgCnt_;
      result.timestamp_ = timestamp_;
      if (intersectionsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          intersections_ = java.util.Collections.unmodifiableList(intersections_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.intersections_ = intersections_;
      } else {
        result.intersections_ = intersectionsBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.SpatData) {
        return mergeFrom((road.data.proto.SpatData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.SpatData other) {
      if (other == road.data.proto.SpatData.getDefaultInstance()) return this;
      if (other.getMsgCnt() != 0) {
        setMsgCnt(other.getMsgCnt());
      }
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (intersectionsBuilder_ == null) {
        if (!other.intersections_.isEmpty()) {
          if (intersections_.isEmpty()) {
            intersections_ = other.intersections_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureIntersectionsIsMutable();
            intersections_.addAll(other.intersections_);
          }
          onChanged();
        }
      } else {
        if (!other.intersections_.isEmpty()) {
          if (intersectionsBuilder_.isEmpty()) {
            intersectionsBuilder_.dispose();
            intersectionsBuilder_ = null;
            intersections_ = other.intersections_;
            bitField0_ = (bitField0_ & ~0x00000001);
            intersectionsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getIntersectionsFieldBuilder() : null;
          } else {
            intersectionsBuilder_.addAllMessages(other.intersections_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.SpatData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.SpatData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int msgCnt_ ;
    /**
     * <pre>
     *将 msgCount 初始化为一个随机值，其范围为 0 到 127。
     * </pre>
     *
     * <code>uint32 msgCnt = 1;</code>
     */
    public int getMsgCnt() {
      return msgCnt_;
    }
    /**
     * <pre>
     *将 msgCount 初始化为一个随机值，其范围为 0 到 127。
     * </pre>
     *
     * <code>uint32 msgCnt = 1;</code>
     */
    public Builder setMsgCnt(int value) {
      
      msgCnt_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *将 msgCount 初始化为一个随机值，其范围为 0 到 127。
     * </pre>
     *
     * <code>uint32 msgCnt = 1;</code>
     */
    public Builder clearMsgCnt() {
      
      msgCnt_ = 0;
      onChanged();
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 2;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 2;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 2;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private java.util.List<road.data.proto.IntersectionState> intersections_ =
      java.util.Collections.emptyList();
    private void ensureIntersectionsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        intersections_ = new java.util.ArrayList<road.data.proto.IntersectionState>(intersections_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.IntersectionState, road.data.proto.IntersectionState.Builder, road.data.proto.IntersectionStateOrBuilder> intersectionsBuilder_;

    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public java.util.List<road.data.proto.IntersectionState> getIntersectionsList() {
      if (intersectionsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(intersections_);
      } else {
        return intersectionsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public int getIntersectionsCount() {
      if (intersectionsBuilder_ == null) {
        return intersections_.size();
      } else {
        return intersectionsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public road.data.proto.IntersectionState getIntersections(int index) {
      if (intersectionsBuilder_ == null) {
        return intersections_.get(index);
      } else {
        return intersectionsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public Builder setIntersections(
        int index, road.data.proto.IntersectionState value) {
      if (intersectionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureIntersectionsIsMutable();
        intersections_.set(index, value);
        onChanged();
      } else {
        intersectionsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public Builder setIntersections(
        int index, road.data.proto.IntersectionState.Builder builderForValue) {
      if (intersectionsBuilder_ == null) {
        ensureIntersectionsIsMutable();
        intersections_.set(index, builderForValue.build());
        onChanged();
      } else {
        intersectionsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public Builder addIntersections(road.data.proto.IntersectionState value) {
      if (intersectionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureIntersectionsIsMutable();
        intersections_.add(value);
        onChanged();
      } else {
        intersectionsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public Builder addIntersections(
        int index, road.data.proto.IntersectionState value) {
      if (intersectionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureIntersectionsIsMutable();
        intersections_.add(index, value);
        onChanged();
      } else {
        intersectionsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public Builder addIntersections(
        road.data.proto.IntersectionState.Builder builderForValue) {
      if (intersectionsBuilder_ == null) {
        ensureIntersectionsIsMutable();
        intersections_.add(builderForValue.build());
        onChanged();
      } else {
        intersectionsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public Builder addIntersections(
        int index, road.data.proto.IntersectionState.Builder builderForValue) {
      if (intersectionsBuilder_ == null) {
        ensureIntersectionsIsMutable();
        intersections_.add(index, builderForValue.build());
        onChanged();
      } else {
        intersectionsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public Builder addAllIntersections(
        java.lang.Iterable<? extends road.data.proto.IntersectionState> values) {
      if (intersectionsBuilder_ == null) {
        ensureIntersectionsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, intersections_);
        onChanged();
      } else {
        intersectionsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public Builder clearIntersections() {
      if (intersectionsBuilder_ == null) {
        intersections_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        intersectionsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public Builder removeIntersections(int index) {
      if (intersectionsBuilder_ == null) {
        ensureIntersectionsIsMutable();
        intersections_.remove(index);
        onChanged();
      } else {
        intersectionsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public road.data.proto.IntersectionState.Builder getIntersectionsBuilder(
        int index) {
      return getIntersectionsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public road.data.proto.IntersectionStateOrBuilder getIntersectionsOrBuilder(
        int index) {
      if (intersectionsBuilder_ == null) {
        return intersections_.get(index);  } else {
        return intersectionsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public java.util.List<? extends road.data.proto.IntersectionStateOrBuilder> 
         getIntersectionsOrBuilderList() {
      if (intersectionsBuilder_ != null) {
        return intersectionsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(intersections_);
      }
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public road.data.proto.IntersectionState.Builder addIntersectionsBuilder() {
      return getIntersectionsFieldBuilder().addBuilder(
          road.data.proto.IntersectionState.getDefaultInstance());
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public road.data.proto.IntersectionState.Builder addIntersectionsBuilder(
        int index) {
      return getIntersectionsFieldBuilder().addBuilder(
          index, road.data.proto.IntersectionState.getDefaultInstance());
    }
    /**
     * <pre>
     * 多个路口信号灯的属性和当前状态。
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
     */
    public java.util.List<road.data.proto.IntersectionState.Builder> 
         getIntersectionsBuilderList() {
      return getIntersectionsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.IntersectionState, road.data.proto.IntersectionState.Builder, road.data.proto.IntersectionStateOrBuilder> 
        getIntersectionsFieldBuilder() {
      if (intersectionsBuilder_ == null) {
        intersectionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.IntersectionState, road.data.proto.IntersectionState.Builder, road.data.proto.IntersectionStateOrBuilder>(
                intersections_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        intersections_ = null;
      }
      return intersectionsBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.SpatData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.SpatData)
  private static final road.data.proto.SpatData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.SpatData();
  }

  public static road.data.proto.SpatData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SpatData>
      PARSER = new com.google.protobuf.AbstractParser<SpatData>() {
    @java.lang.Override
    public SpatData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new SpatData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<SpatData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SpatData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.SpatData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

