// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface SpatDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.SpatData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *将 msgCount 初始化为一个随机值，其范围为 0 到 127。
   * </pre>
   *
   * <code>uint32 msgCnt = 1;</code>
   */
  int getMsgCnt();

  /**
   * <pre>
   * 产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 2;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   * 多个路口信号灯的属性和当前状态。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
   */
  java.util.List<road.data.proto.IntersectionState> 
      getIntersectionsList();
  /**
   * <pre>
   * 多个路口信号灯的属性和当前状态。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
   */
  road.data.proto.IntersectionState getIntersections(int index);
  /**
   * <pre>
   * 多个路口信号灯的属性和当前状态。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
   */
  int getIntersectionsCount();
  /**
   * <pre>
   * 多个路口信号灯的属性和当前状态。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
   */
  java.util.List<? extends road.data.proto.IntersectionStateOrBuilder> 
      getIntersectionsOrBuilderList();
  /**
   * <pre>
   * 多个路口信号灯的属性和当前状态。
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.IntersectionState intersections = 3;</code>
   */
  road.data.proto.IntersectionStateOrBuilder getIntersectionsOrBuilder(
      int index);
}
