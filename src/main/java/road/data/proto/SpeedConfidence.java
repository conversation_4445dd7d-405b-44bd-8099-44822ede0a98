// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *速度  数值描述了95%置信水平的速度精度。该精度理论上只考虑了当前速度传感器的误差。但是，当系统能够自动检测错误并修正，相应的精度数值也成该提高。
 * </pre>
 *
 * Protobuf enum {@code cn.seisys.v2x.pb.SpeedConfidence}
 */
public enum SpeedConfidence
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   * __未配备或不可用
   * </pre>
   *
   * <code>SPEED_CONFID_UNAVAILABLE = 0;</code>
   */
  SPEED_CONFID_UNAVAILABLE(0),
  /**
   * <pre>
   * __100 METERS/SEC
   * </pre>
   *
   * <code>SPEED_CONFID_100MS = 1;</code>
   */
  SPEED_CONFID_100MS(1),
  /**
   * <pre>
   * __10 METERS/SEE
   * </pre>
   *
   * <code>SPEED_CONFID_10MS = 2;</code>
   */
  SPEED_CONFID_10MS(2),
  /**
   * <pre>
   * __5 METERS/SEC
   * </pre>
   *
   * <code>SPEED_CONFID_5MS = 3;</code>
   */
  SPEED_CONFID_5MS(3),
  /**
   * <pre>
   * __1 METERS/SEC
   * </pre>
   *
   * <code>SPEED_CONFID_1MS = 4;</code>
   */
  SPEED_CONFID_1MS(4),
  /**
   * <pre>
   * __ 0.1 METERS/SEC
   * </pre>
   *
   * <code>SPEED_CONFID_0_1MS = 5;</code>
   */
  SPEED_CONFID_0_1MS(5),
  /**
   * <pre>
   * __0.05 METERS/SEC
   * </pre>
   *
   * <code>SPEED_CONFID_0_05MS = 6;</code>
   */
  SPEED_CONFID_0_05MS(6),
  /**
   * <pre>
   * __0.01 METERS/SEC
   * </pre>
   *
   * <code>SPEED_CONFID_0_01MS = 7;</code>
   */
  SPEED_CONFID_0_01MS(7),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   * __未配备或不可用
   * </pre>
   *
   * <code>SPEED_CONFID_UNAVAILABLE = 0;</code>
   */
  public static final int SPEED_CONFID_UNAVAILABLE_VALUE = 0;
  /**
   * <pre>
   * __100 METERS/SEC
   * </pre>
   *
   * <code>SPEED_CONFID_100MS = 1;</code>
   */
  public static final int SPEED_CONFID_100MS_VALUE = 1;
  /**
   * <pre>
   * __10 METERS/SEE
   * </pre>
   *
   * <code>SPEED_CONFID_10MS = 2;</code>
   */
  public static final int SPEED_CONFID_10MS_VALUE = 2;
  /**
   * <pre>
   * __5 METERS/SEC
   * </pre>
   *
   * <code>SPEED_CONFID_5MS = 3;</code>
   */
  public static final int SPEED_CONFID_5MS_VALUE = 3;
  /**
   * <pre>
   * __1 METERS/SEC
   * </pre>
   *
   * <code>SPEED_CONFID_1MS = 4;</code>
   */
  public static final int SPEED_CONFID_1MS_VALUE = 4;
  /**
   * <pre>
   * __ 0.1 METERS/SEC
   * </pre>
   *
   * <code>SPEED_CONFID_0_1MS = 5;</code>
   */
  public static final int SPEED_CONFID_0_1MS_VALUE = 5;
  /**
   * <pre>
   * __0.05 METERS/SEC
   * </pre>
   *
   * <code>SPEED_CONFID_0_05MS = 6;</code>
   */
  public static final int SPEED_CONFID_0_05MS_VALUE = 6;
  /**
   * <pre>
   * __0.01 METERS/SEC
   * </pre>
   *
   * <code>SPEED_CONFID_0_01MS = 7;</code>
   */
  public static final int SPEED_CONFID_0_01MS_VALUE = 7;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static SpeedConfidence valueOf(int value) {
    return forNumber(value);
  }

  public static SpeedConfidence forNumber(int value) {
    switch (value) {
      case 0: return SPEED_CONFID_UNAVAILABLE;
      case 1: return SPEED_CONFID_100MS;
      case 2: return SPEED_CONFID_10MS;
      case 3: return SPEED_CONFID_5MS;
      case 4: return SPEED_CONFID_1MS;
      case 5: return SPEED_CONFID_0_1MS;
      case 6: return SPEED_CONFID_0_05MS;
      case 7: return SPEED_CONFID_0_01MS;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<SpeedConfidence>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      SpeedConfidence> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<SpeedConfidence>() {
          public SpeedConfidence findValueByNumber(int number) {
            return SpeedConfidence.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return road.data.proto.V2X.getDescriptor().getEnumTypes().get(4);
  }

  private static final SpeedConfidence[] VALUES = values();

  public static SpeedConfidence valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private SpeedConfidence(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.SpeedConfidence)
}

