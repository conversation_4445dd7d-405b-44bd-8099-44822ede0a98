// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *状态列表 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.StatusData}
 */
public  final class StatusData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.StatusData)
    StatusDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use StatusData.newBuilder() to construct.
  private StatusData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private StatusData() {
    deviceId_ = "";
    mapDeviceId_ = "";
    deviceType_ = 0;
    statusType_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new StatusData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private StatusData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            deviceId_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            mapDeviceId_ = s;
            break;
          }
          case 24: {
            int rawValue = input.readEnum();

            deviceType_ = rawValue;
            break;
          }
          case 32: {
            int rawValue = input.readEnum();

            statusType_ = rawValue;
            break;
          }
          case 42: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (posDevice_ != null) {
              subBuilder = posDevice_.toBuilder();
            }
            posDevice_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(posDevice_);
              posDevice_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_StatusData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_StatusData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.StatusData.class, road.data.proto.StatusData.Builder.class);
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.StatusData.StatusType}
   */
  public enum StatusType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>DEV_STATUS_UNKNOWN = 0;</code>
     */
    DEV_STATUS_UNKNOWN(0),
    /**
     * <pre>
     *  无异常
     * </pre>
     *
     * <code>DEV_STATUS_OK = 1;</code>
     */
    DEV_STATUS_OK(1),
    /**
     * <pre>
     *   设备运行状态异常
     * </pre>
     *
     * <code>DEV_STATUS_ABNORMAL = 2;</code>
     */
    DEV_STATUS_ABNORMAL(2),
    /**
     * <pre>
     *  离线
     * </pre>
     *
     * <code>DEV_STATUS_OFF = 3;</code>
     */
    DEV_STATUS_OFF(3),
    /**
     * <pre>
     *   重启中
     * </pre>
     *
     * <code>DEV_STATUS_REBOOT = 4;</code>
     */
    DEV_STATUS_REBOOT(4),
    /**
     * <pre>
     *  设备维修中
     * </pre>
     *
     * <code>DEV_STATUS_MAINTAIN = 5;</code>
     */
    DEV_STATUS_MAINTAIN(5),
    /**
     * <pre>
     *  设备已报废
     * </pre>
     *
     * <code>DEV_STATUS_SCRAP = 6;</code>
     */
    DEV_STATUS_SCRAP(6),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>DEV_STATUS_UNKNOWN = 0;</code>
     */
    public static final int DEV_STATUS_UNKNOWN_VALUE = 0;
    /**
     * <pre>
     *  无异常
     * </pre>
     *
     * <code>DEV_STATUS_OK = 1;</code>
     */
    public static final int DEV_STATUS_OK_VALUE = 1;
    /**
     * <pre>
     *   设备运行状态异常
     * </pre>
     *
     * <code>DEV_STATUS_ABNORMAL = 2;</code>
     */
    public static final int DEV_STATUS_ABNORMAL_VALUE = 2;
    /**
     * <pre>
     *  离线
     * </pre>
     *
     * <code>DEV_STATUS_OFF = 3;</code>
     */
    public static final int DEV_STATUS_OFF_VALUE = 3;
    /**
     * <pre>
     *   重启中
     * </pre>
     *
     * <code>DEV_STATUS_REBOOT = 4;</code>
     */
    public static final int DEV_STATUS_REBOOT_VALUE = 4;
    /**
     * <pre>
     *  设备维修中
     * </pre>
     *
     * <code>DEV_STATUS_MAINTAIN = 5;</code>
     */
    public static final int DEV_STATUS_MAINTAIN_VALUE = 5;
    /**
     * <pre>
     *  设备已报废
     * </pre>
     *
     * <code>DEV_STATUS_SCRAP = 6;</code>
     */
    public static final int DEV_STATUS_SCRAP_VALUE = 6;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static StatusType valueOf(int value) {
      return forNumber(value);
    }

    public static StatusType forNumber(int value) {
      switch (value) {
        case 0: return DEV_STATUS_UNKNOWN;
        case 1: return DEV_STATUS_OK;
        case 2: return DEV_STATUS_ABNORMAL;
        case 3: return DEV_STATUS_OFF;
        case 4: return DEV_STATUS_REBOOT;
        case 5: return DEV_STATUS_MAINTAIN;
        case 6: return DEV_STATUS_SCRAP;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<StatusType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        StatusType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<StatusType>() {
            public StatusType findValueByNumber(int number) {
              return StatusType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.StatusData.getDescriptor().getEnumTypes().get(0);
    }

    private static final StatusType[] VALUES = values();

    public static StatusType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private StatusType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.StatusData.StatusType)
  }

  public static final int DEVICEID_FIELD_NUMBER = 1;
  private volatile java.lang.Object deviceId_;
  /**
   * <pre>
   *设备id
   * </pre>
   *
   * <code>string deviceId = 1;</code>
   */
  public java.lang.String getDeviceId() {
    java.lang.Object ref = deviceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deviceId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *设备id
   * </pre>
   *
   * <code>string deviceId = 1;</code>
   */
  public com.google.protobuf.ByteString
      getDeviceIdBytes() {
    java.lang.Object ref = deviceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deviceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MAPDEVICEID_FIELD_NUMBER = 2;
  private volatile java.lang.Object mapDeviceId_;
  /**
   * <pre>
   *位置相关的设备编号
   * </pre>
   *
   * <code>string mapDeviceId = 2;</code>
   */
  public java.lang.String getMapDeviceId() {
    java.lang.Object ref = mapDeviceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      mapDeviceId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *位置相关的设备编号
   * </pre>
   *
   * <code>string mapDeviceId = 2;</code>
   */
  public com.google.protobuf.ByteString
      getMapDeviceIdBytes() {
    java.lang.Object ref = mapDeviceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      mapDeviceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DEVICETYPE_FIELD_NUMBER = 3;
  private int deviceType_;
  /**
   * <pre>
   *设备状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DeviceType deviceType = 3;</code>
   */
  public int getDeviceTypeValue() {
    return deviceType_;
  }
  /**
   * <pre>
   *设备状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DeviceType deviceType = 3;</code>
   */
  public road.data.proto.DeviceType getDeviceType() {
    @SuppressWarnings("deprecation")
    road.data.proto.DeviceType result = road.data.proto.DeviceType.valueOf(deviceType_);
    return result == null ? road.data.proto.DeviceType.UNRECOGNIZED : result;
  }

  public static final int STATUSTYPE_FIELD_NUMBER = 4;
  private int statusType_;
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.StatusData.StatusType statusType = 4;</code>
   */
  public int getStatusTypeValue() {
    return statusType_;
  }
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.StatusData.StatusType statusType = 4;</code>
   */
  public road.data.proto.StatusData.StatusType getStatusType() {
    @SuppressWarnings("deprecation")
    road.data.proto.StatusData.StatusType result = road.data.proto.StatusData.StatusType.valueOf(statusType_);
    return result == null ? road.data.proto.StatusData.StatusType.UNRECOGNIZED : result;
  }

  public static final int POSDEVICE_FIELD_NUMBER = 5;
  private road.data.proto.Position3D posDevice_;
  /**
   * <pre>
   *设备绝对经纬度坐标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
   */
  public boolean hasPosDevice() {
    return posDevice_ != null;
  }
  /**
   * <pre>
   *设备绝对经纬度坐标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
   */
  public road.data.proto.Position3D getPosDevice() {
    return posDevice_ == null ? road.data.proto.Position3D.getDefaultInstance() : posDevice_;
  }
  /**
   * <pre>
   *设备绝对经纬度坐标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
   */
  public road.data.proto.Position3DOrBuilder getPosDeviceOrBuilder() {
    return getPosDevice();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getDeviceIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, deviceId_);
    }
    if (!getMapDeviceIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, mapDeviceId_);
    }
    if (deviceType_ != road.data.proto.DeviceType.DEVICE_TYPE_UNKONWN.getNumber()) {
      output.writeEnum(3, deviceType_);
    }
    if (statusType_ != road.data.proto.StatusData.StatusType.DEV_STATUS_UNKNOWN.getNumber()) {
      output.writeEnum(4, statusType_);
    }
    if (posDevice_ != null) {
      output.writeMessage(5, getPosDevice());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getDeviceIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, deviceId_);
    }
    if (!getMapDeviceIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, mapDeviceId_);
    }
    if (deviceType_ != road.data.proto.DeviceType.DEVICE_TYPE_UNKONWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, deviceType_);
    }
    if (statusType_ != road.data.proto.StatusData.StatusType.DEV_STATUS_UNKNOWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(4, statusType_);
    }
    if (posDevice_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getPosDevice());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.StatusData)) {
      return super.equals(obj);
    }
    road.data.proto.StatusData other = (road.data.proto.StatusData) obj;

    if (!getDeviceId()
        .equals(other.getDeviceId())) return false;
    if (!getMapDeviceId()
        .equals(other.getMapDeviceId())) return false;
    if (deviceType_ != other.deviceType_) return false;
    if (statusType_ != other.statusType_) return false;
    if (hasPosDevice() != other.hasPosDevice()) return false;
    if (hasPosDevice()) {
      if (!getPosDevice()
          .equals(other.getPosDevice())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + DEVICEID_FIELD_NUMBER;
    hash = (53 * hash) + getDeviceId().hashCode();
    hash = (37 * hash) + MAPDEVICEID_FIELD_NUMBER;
    hash = (53 * hash) + getMapDeviceId().hashCode();
    hash = (37 * hash) + DEVICETYPE_FIELD_NUMBER;
    hash = (53 * hash) + deviceType_;
    hash = (37 * hash) + STATUSTYPE_FIELD_NUMBER;
    hash = (53 * hash) + statusType_;
    if (hasPosDevice()) {
      hash = (37 * hash) + POSDEVICE_FIELD_NUMBER;
      hash = (53 * hash) + getPosDevice().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.StatusData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.StatusData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.StatusData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.StatusData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.StatusData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.StatusData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.StatusData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.StatusData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.StatusData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.StatusData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.StatusData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.StatusData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.StatusData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *状态列表 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.StatusData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.StatusData)
      road.data.proto.StatusDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_StatusData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_StatusData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.StatusData.class, road.data.proto.StatusData.Builder.class);
    }

    // Construct using road.data.proto.StatusData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      deviceId_ = "";

      mapDeviceId_ = "";

      deviceType_ = 0;

      statusType_ = 0;

      if (posDeviceBuilder_ == null) {
        posDevice_ = null;
      } else {
        posDevice_ = null;
        posDeviceBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_StatusData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.StatusData getDefaultInstanceForType() {
      return road.data.proto.StatusData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.StatusData build() {
      road.data.proto.StatusData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.StatusData buildPartial() {
      road.data.proto.StatusData result = new road.data.proto.StatusData(this);
      result.deviceId_ = deviceId_;
      result.mapDeviceId_ = mapDeviceId_;
      result.deviceType_ = deviceType_;
      result.statusType_ = statusType_;
      if (posDeviceBuilder_ == null) {
        result.posDevice_ = posDevice_;
      } else {
        result.posDevice_ = posDeviceBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.StatusData) {
        return mergeFrom((road.data.proto.StatusData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.StatusData other) {
      if (other == road.data.proto.StatusData.getDefaultInstance()) return this;
      if (!other.getDeviceId().isEmpty()) {
        deviceId_ = other.deviceId_;
        onChanged();
      }
      if (!other.getMapDeviceId().isEmpty()) {
        mapDeviceId_ = other.mapDeviceId_;
        onChanged();
      }
      if (other.deviceType_ != 0) {
        setDeviceTypeValue(other.getDeviceTypeValue());
      }
      if (other.statusType_ != 0) {
        setStatusTypeValue(other.getStatusTypeValue());
      }
      if (other.hasPosDevice()) {
        mergePosDevice(other.getPosDevice());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.StatusData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.StatusData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object deviceId_ = "";
    /**
     * <pre>
     *设备id
     * </pre>
     *
     * <code>string deviceId = 1;</code>
     */
    public java.lang.String getDeviceId() {
      java.lang.Object ref = deviceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *设备id
     * </pre>
     *
     * <code>string deviceId = 1;</code>
     */
    public com.google.protobuf.ByteString
        getDeviceIdBytes() {
      java.lang.Object ref = deviceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *设备id
     * </pre>
     *
     * <code>string deviceId = 1;</code>
     */
    public Builder setDeviceId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deviceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *设备id
     * </pre>
     *
     * <code>string deviceId = 1;</code>
     */
    public Builder clearDeviceId() {
      
      deviceId_ = getDefaultInstance().getDeviceId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *设备id
     * </pre>
     *
     * <code>string deviceId = 1;</code>
     */
    public Builder setDeviceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deviceId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object mapDeviceId_ = "";
    /**
     * <pre>
     *位置相关的设备编号
     * </pre>
     *
     * <code>string mapDeviceId = 2;</code>
     */
    public java.lang.String getMapDeviceId() {
      java.lang.Object ref = mapDeviceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        mapDeviceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *位置相关的设备编号
     * </pre>
     *
     * <code>string mapDeviceId = 2;</code>
     */
    public com.google.protobuf.ByteString
        getMapDeviceIdBytes() {
      java.lang.Object ref = mapDeviceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        mapDeviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *位置相关的设备编号
     * </pre>
     *
     * <code>string mapDeviceId = 2;</code>
     */
    public Builder setMapDeviceId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      mapDeviceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *位置相关的设备编号
     * </pre>
     *
     * <code>string mapDeviceId = 2;</code>
     */
    public Builder clearMapDeviceId() {
      
      mapDeviceId_ = getDefaultInstance().getMapDeviceId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *位置相关的设备编号
     * </pre>
     *
     * <code>string mapDeviceId = 2;</code>
     */
    public Builder setMapDeviceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      mapDeviceId_ = value;
      onChanged();
      return this;
    }

    private int deviceType_ = 0;
    /**
     * <pre>
     *设备状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DeviceType deviceType = 3;</code>
     */
    public int getDeviceTypeValue() {
      return deviceType_;
    }
    /**
     * <pre>
     *设备状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DeviceType deviceType = 3;</code>
     */
    public Builder setDeviceTypeValue(int value) {
      deviceType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *设备状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DeviceType deviceType = 3;</code>
     */
    public road.data.proto.DeviceType getDeviceType() {
      @SuppressWarnings("deprecation")
      road.data.proto.DeviceType result = road.data.proto.DeviceType.valueOf(deviceType_);
      return result == null ? road.data.proto.DeviceType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *设备状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DeviceType deviceType = 3;</code>
     */
    public Builder setDeviceType(road.data.proto.DeviceType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      deviceType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *设备状态
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DeviceType deviceType = 3;</code>
     */
    public Builder clearDeviceType() {
      
      deviceType_ = 0;
      onChanged();
      return this;
    }

    private int statusType_ = 0;
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.StatusData.StatusType statusType = 4;</code>
     */
    public int getStatusTypeValue() {
      return statusType_;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.StatusData.StatusType statusType = 4;</code>
     */
    public Builder setStatusTypeValue(int value) {
      statusType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.StatusData.StatusType statusType = 4;</code>
     */
    public road.data.proto.StatusData.StatusType getStatusType() {
      @SuppressWarnings("deprecation")
      road.data.proto.StatusData.StatusType result = road.data.proto.StatusData.StatusType.valueOf(statusType_);
      return result == null ? road.data.proto.StatusData.StatusType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.StatusData.StatusType statusType = 4;</code>
     */
    public Builder setStatusType(road.data.proto.StatusData.StatusType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      statusType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.StatusData.StatusType statusType = 4;</code>
     */
    public Builder clearStatusType() {
      
      statusType_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.Position3D posDevice_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> posDeviceBuilder_;
    /**
     * <pre>
     *设备绝对经纬度坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
     */
    public boolean hasPosDevice() {
      return posDeviceBuilder_ != null || posDevice_ != null;
    }
    /**
     * <pre>
     *设备绝对经纬度坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
     */
    public road.data.proto.Position3D getPosDevice() {
      if (posDeviceBuilder_ == null) {
        return posDevice_ == null ? road.data.proto.Position3D.getDefaultInstance() : posDevice_;
      } else {
        return posDeviceBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *设备绝对经纬度坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
     */
    public Builder setPosDevice(road.data.proto.Position3D value) {
      if (posDeviceBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        posDevice_ = value;
        onChanged();
      } else {
        posDeviceBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *设备绝对经纬度坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
     */
    public Builder setPosDevice(
        road.data.proto.Position3D.Builder builderForValue) {
      if (posDeviceBuilder_ == null) {
        posDevice_ = builderForValue.build();
        onChanged();
      } else {
        posDeviceBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *设备绝对经纬度坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
     */
    public Builder mergePosDevice(road.data.proto.Position3D value) {
      if (posDeviceBuilder_ == null) {
        if (posDevice_ != null) {
          posDevice_ =
            road.data.proto.Position3D.newBuilder(posDevice_).mergeFrom(value).buildPartial();
        } else {
          posDevice_ = value;
        }
        onChanged();
      } else {
        posDeviceBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *设备绝对经纬度坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
     */
    public Builder clearPosDevice() {
      if (posDeviceBuilder_ == null) {
        posDevice_ = null;
        onChanged();
      } else {
        posDevice_ = null;
        posDeviceBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *设备绝对经纬度坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
     */
    public road.data.proto.Position3D.Builder getPosDeviceBuilder() {
      
      onChanged();
      return getPosDeviceFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *设备绝对经纬度坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
     */
    public road.data.proto.Position3DOrBuilder getPosDeviceOrBuilder() {
      if (posDeviceBuilder_ != null) {
        return posDeviceBuilder_.getMessageOrBuilder();
      } else {
        return posDevice_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : posDevice_;
      }
    }
    /**
     * <pre>
     *设备绝对经纬度坐标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getPosDeviceFieldBuilder() {
      if (posDeviceBuilder_ == null) {
        posDeviceBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getPosDevice(),
                getParentForChildren(),
                isClean());
        posDevice_ = null;
      }
      return posDeviceBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.StatusData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.StatusData)
  private static final road.data.proto.StatusData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.StatusData();
  }

  public static road.data.proto.StatusData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<StatusData>
      PARSER = new com.google.protobuf.AbstractParser<StatusData>() {
    @java.lang.Override
    public StatusData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new StatusData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<StatusData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<StatusData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.StatusData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

