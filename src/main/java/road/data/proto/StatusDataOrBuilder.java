// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface StatusDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.StatusData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *设备id
   * </pre>
   *
   * <code>string deviceId = 1;</code>
   */
  java.lang.String getDeviceId();
  /**
   * <pre>
   *设备id
   * </pre>
   *
   * <code>string deviceId = 1;</code>
   */
  com.google.protobuf.ByteString
      getDeviceIdBytes();

  /**
   * <pre>
   *位置相关的设备编号
   * </pre>
   *
   * <code>string mapDeviceId = 2;</code>
   */
  java.lang.String getMapDeviceId();
  /**
   * <pre>
   *位置相关的设备编号
   * </pre>
   *
   * <code>string mapDeviceId = 2;</code>
   */
  com.google.protobuf.ByteString
      getMapDeviceIdBytes();

  /**
   * <pre>
   *设备状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DeviceType deviceType = 3;</code>
   */
  int getDeviceTypeValue();
  /**
   * <pre>
   *设备状态
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DeviceType deviceType = 3;</code>
   */
  road.data.proto.DeviceType getDeviceType();

  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.StatusData.StatusType statusType = 4;</code>
   */
  int getStatusTypeValue();
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.StatusData.StatusType statusType = 4;</code>
   */
  road.data.proto.StatusData.StatusType getStatusType();

  /**
   * <pre>
   *设备绝对经纬度坐标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
   */
  boolean hasPosDevice();
  /**
   * <pre>
   *设备绝对经纬度坐标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
   */
  road.data.proto.Position3D getPosDevice();
  /**
   * <pre>
   *设备绝对经纬度坐标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D posDevice = 5;</code>
   */
  road.data.proto.Position3DOrBuilder getPosDeviceOrBuilder();
}
