// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *定义车辆的给油系统状态 ThrottleSystemStatus   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.ThrottleSystemStatus}
 */
public  final class ThrottleSystemStatus extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.ThrottleSystemStatus)
    ThrottleSystemStatusOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ThrottleSystemStatus.newBuilder() to construct.
  private ThrottleSystemStatus(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ThrottleSystemStatus() {
    throttlePadel_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ThrottleSystemStatus();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ThrottleSystemStatus(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            thorttleControl_ = input.readUInt32();
            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            throttlePadel_ = rawValue;
            break;
          }
          case 24: {

            wheelThrottles_ = input.readInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ThrottleSystemStatus_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ThrottleSystemStatus_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.ThrottleSystemStatus.class, road.data.proto.ThrottleSystemStatus.Builder.class);
  }

  /**
   * Protobuf enum {@code cn.seisys.v2x.pb.ThrottleSystemStatus.ThrottlePedalStauts}
   */
  public enum ThrottlePedalStauts
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     *，车辆油门踏板检测器不可用； 
     * </pre>
     *
     * <code>UNAVAILABLE_PEDAL = 0;</code>
     */
    UNAVAILABLE_PEDAL(0),
    /**
     * <pre>
     *，车辆油门踏板未踩下；
     * </pre>
     *
     * <code>OFF = 1;</code>
     */
    OFF(1),
    /**
     * <pre>
     *，踩下车辆的油门踏板；
     * </pre>
     *
     * <code>ON = 2;</code>
     */
    ON(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     *，车辆油门踏板检测器不可用； 
     * </pre>
     *
     * <code>UNAVAILABLE_PEDAL = 0;</code>
     */
    public static final int UNAVAILABLE_PEDAL_VALUE = 0;
    /**
     * <pre>
     *，车辆油门踏板未踩下；
     * </pre>
     *
     * <code>OFF = 1;</code>
     */
    public static final int OFF_VALUE = 1;
    /**
     * <pre>
     *，踩下车辆的油门踏板；
     * </pre>
     *
     * <code>ON = 2;</code>
     */
    public static final int ON_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ThrottlePedalStauts valueOf(int value) {
      return forNumber(value);
    }

    public static ThrottlePedalStauts forNumber(int value) {
      switch (value) {
        case 0: return UNAVAILABLE_PEDAL;
        case 1: return OFF;
        case 2: return ON;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ThrottlePedalStauts>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ThrottlePedalStauts> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ThrottlePedalStauts>() {
            public ThrottlePedalStauts findValueByNumber(int number) {
              return ThrottlePedalStauts.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return road.data.proto.ThrottleSystemStatus.getDescriptor().getEnumTypes().get(0);
    }

    private static final ThrottlePedalStauts[] VALUES = values();

    public static ThrottlePedalStauts valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ThrottlePedalStauts(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.ThrottleSystemStatus.ThrottlePedalStauts)
  }

  public static final int THORTTLECONTROL_FIELD_NUMBER = 1;
  private int thorttleControl_;
  /**
   * <pre>
   *油门踩踏强度 百分比：0~100%，精度0.1%
   * </pre>
   *
   * <code>uint32 thorttleControl = 1;</code>
   */
  public int getThorttleControl() {
    return thorttleControl_;
  }

  public static final int THROTTLEPADEL_FIELD_NUMBER = 2;
  private int throttlePadel_;
  /**
   * <pre>
   *可选，油门踏板踩下情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus.ThrottlePedalStauts throttlePadel = 2;</code>
   */
  public int getThrottlePadelValue() {
    return throttlePadel_;
  }
  /**
   * <pre>
   *可选，油门踏板踩下情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus.ThrottlePedalStauts throttlePadel = 2;</code>
   */
  public road.data.proto.ThrottleSystemStatus.ThrottlePedalStauts getThrottlePadel() {
    @SuppressWarnings("deprecation")
    road.data.proto.ThrottleSystemStatus.ThrottlePedalStauts result = road.data.proto.ThrottleSystemStatus.ThrottlePedalStauts.valueOf(throttlePadel_);
    return result == null ? road.data.proto.ThrottleSystemStatus.ThrottlePedalStauts.UNRECOGNIZED : result;
  }

  public static final int WHEELTHROTTLES_FIELD_NUMBER = 3;
  private int wheelThrottles_;
  /**
   * <pre>
   *ThrottleAppliedStatus四轮分别的动力情况,位串,转化为二进制后，二进制第x位数字为1对应的含义：
   * </pre>
   *
   * <code>int32 wheelThrottles = 3;</code>
   */
  public int getWheelThrottles() {
    return wheelThrottles_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (thorttleControl_ != 0) {
      output.writeUInt32(1, thorttleControl_);
    }
    if (throttlePadel_ != road.data.proto.ThrottleSystemStatus.ThrottlePedalStauts.UNAVAILABLE_PEDAL.getNumber()) {
      output.writeEnum(2, throttlePadel_);
    }
    if (wheelThrottles_ != 0) {
      output.writeInt32(3, wheelThrottles_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (thorttleControl_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, thorttleControl_);
    }
    if (throttlePadel_ != road.data.proto.ThrottleSystemStatus.ThrottlePedalStauts.UNAVAILABLE_PEDAL.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, throttlePadel_);
    }
    if (wheelThrottles_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, wheelThrottles_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.ThrottleSystemStatus)) {
      return super.equals(obj);
    }
    road.data.proto.ThrottleSystemStatus other = (road.data.proto.ThrottleSystemStatus) obj;

    if (getThorttleControl()
        != other.getThorttleControl()) return false;
    if (throttlePadel_ != other.throttlePadel_) return false;
    if (getWheelThrottles()
        != other.getWheelThrottles()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + THORTTLECONTROL_FIELD_NUMBER;
    hash = (53 * hash) + getThorttleControl();
    hash = (37 * hash) + THROTTLEPADEL_FIELD_NUMBER;
    hash = (53 * hash) + throttlePadel_;
    hash = (37 * hash) + WHEELTHROTTLES_FIELD_NUMBER;
    hash = (53 * hash) + getWheelThrottles();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.ThrottleSystemStatus parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ThrottleSystemStatus parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ThrottleSystemStatus parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ThrottleSystemStatus parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ThrottleSystemStatus parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.ThrottleSystemStatus parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.ThrottleSystemStatus parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ThrottleSystemStatus parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ThrottleSystemStatus parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.ThrottleSystemStatus parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.ThrottleSystemStatus parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.ThrottleSystemStatus parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.ThrottleSystemStatus prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *定义车辆的给油系统状态 ThrottleSystemStatus   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.ThrottleSystemStatus}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.ThrottleSystemStatus)
      road.data.proto.ThrottleSystemStatusOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ThrottleSystemStatus_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ThrottleSystemStatus_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.ThrottleSystemStatus.class, road.data.proto.ThrottleSystemStatus.Builder.class);
    }

    // Construct using road.data.proto.ThrottleSystemStatus.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      thorttleControl_ = 0;

      throttlePadel_ = 0;

      wheelThrottles_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_ThrottleSystemStatus_descriptor;
    }

    @java.lang.Override
    public road.data.proto.ThrottleSystemStatus getDefaultInstanceForType() {
      return road.data.proto.ThrottleSystemStatus.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.ThrottleSystemStatus build() {
      road.data.proto.ThrottleSystemStatus result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.ThrottleSystemStatus buildPartial() {
      road.data.proto.ThrottleSystemStatus result = new road.data.proto.ThrottleSystemStatus(this);
      result.thorttleControl_ = thorttleControl_;
      result.throttlePadel_ = throttlePadel_;
      result.wheelThrottles_ = wheelThrottles_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.ThrottleSystemStatus) {
        return mergeFrom((road.data.proto.ThrottleSystemStatus)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.ThrottleSystemStatus other) {
      if (other == road.data.proto.ThrottleSystemStatus.getDefaultInstance()) return this;
      if (other.getThorttleControl() != 0) {
        setThorttleControl(other.getThorttleControl());
      }
      if (other.throttlePadel_ != 0) {
        setThrottlePadelValue(other.getThrottlePadelValue());
      }
      if (other.getWheelThrottles() != 0) {
        setWheelThrottles(other.getWheelThrottles());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.ThrottleSystemStatus parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.ThrottleSystemStatus) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int thorttleControl_ ;
    /**
     * <pre>
     *油门踩踏强度 百分比：0~100%，精度0.1%
     * </pre>
     *
     * <code>uint32 thorttleControl = 1;</code>
     */
    public int getThorttleControl() {
      return thorttleControl_;
    }
    /**
     * <pre>
     *油门踩踏强度 百分比：0~100%，精度0.1%
     * </pre>
     *
     * <code>uint32 thorttleControl = 1;</code>
     */
    public Builder setThorttleControl(int value) {
      
      thorttleControl_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *油门踩踏强度 百分比：0~100%，精度0.1%
     * </pre>
     *
     * <code>uint32 thorttleControl = 1;</code>
     */
    public Builder clearThorttleControl() {
      
      thorttleControl_ = 0;
      onChanged();
      return this;
    }

    private int throttlePadel_ = 0;
    /**
     * <pre>
     *可选，油门踏板踩下情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus.ThrottlePedalStauts throttlePadel = 2;</code>
     */
    public int getThrottlePadelValue() {
      return throttlePadel_;
    }
    /**
     * <pre>
     *可选，油门踏板踩下情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus.ThrottlePedalStauts throttlePadel = 2;</code>
     */
    public Builder setThrottlePadelValue(int value) {
      throttlePadel_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，油门踏板踩下情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus.ThrottlePedalStauts throttlePadel = 2;</code>
     */
    public road.data.proto.ThrottleSystemStatus.ThrottlePedalStauts getThrottlePadel() {
      @SuppressWarnings("deprecation")
      road.data.proto.ThrottleSystemStatus.ThrottlePedalStauts result = road.data.proto.ThrottleSystemStatus.ThrottlePedalStauts.valueOf(throttlePadel_);
      return result == null ? road.data.proto.ThrottleSystemStatus.ThrottlePedalStauts.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *可选，油门踏板踩下情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus.ThrottlePedalStauts throttlePadel = 2;</code>
     */
    public Builder setThrottlePadel(road.data.proto.ThrottleSystemStatus.ThrottlePedalStauts value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      throttlePadel_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，油门踏板踩下情况
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus.ThrottlePedalStauts throttlePadel = 2;</code>
     */
    public Builder clearThrottlePadel() {
      
      throttlePadel_ = 0;
      onChanged();
      return this;
    }

    private int wheelThrottles_ ;
    /**
     * <pre>
     *ThrottleAppliedStatus四轮分别的动力情况,位串,转化为二进制后，二进制第x位数字为1对应的含义：
     * </pre>
     *
     * <code>int32 wheelThrottles = 3;</code>
     */
    public int getWheelThrottles() {
      return wheelThrottles_;
    }
    /**
     * <pre>
     *ThrottleAppliedStatus四轮分别的动力情况,位串,转化为二进制后，二进制第x位数字为1对应的含义：
     * </pre>
     *
     * <code>int32 wheelThrottles = 3;</code>
     */
    public Builder setWheelThrottles(int value) {
      
      wheelThrottles_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *ThrottleAppliedStatus四轮分别的动力情况,位串,转化为二进制后，二进制第x位数字为1对应的含义：
     * </pre>
     *
     * <code>int32 wheelThrottles = 3;</code>
     */
    public Builder clearWheelThrottles() {
      
      wheelThrottles_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.ThrottleSystemStatus)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.ThrottleSystemStatus)
  private static final road.data.proto.ThrottleSystemStatus DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.ThrottleSystemStatus();
  }

  public static road.data.proto.ThrottleSystemStatus getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ThrottleSystemStatus>
      PARSER = new com.google.protobuf.AbstractParser<ThrottleSystemStatus>() {
    @java.lang.Override
    public ThrottleSystemStatus parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ThrottleSystemStatus(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ThrottleSystemStatus> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ThrottleSystemStatus> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.ThrottleSystemStatus getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

