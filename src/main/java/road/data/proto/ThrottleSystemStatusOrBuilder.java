// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface ThrottleSystemStatusOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.ThrottleSystemStatus)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *油门踩踏强度 百分比：0~100%，精度0.1%
   * </pre>
   *
   * <code>uint32 thorttleControl = 1;</code>
   */
  int getThorttleControl();

  /**
   * <pre>
   *可选，油门踏板踩下情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus.ThrottlePedalStauts throttlePadel = 2;</code>
   */
  int getThrottlePadelValue();
  /**
   * <pre>
   *可选，油门踏板踩下情况
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ThrottleSystemStatus.ThrottlePedalStauts throttlePadel = 2;</code>
   */
  road.data.proto.ThrottleSystemStatus.ThrottlePedalStauts getThrottlePadel();

  /**
   * <pre>
   *ThrottleAppliedStatus四轮分别的动力情况,位串,转化为二进制后，二进制第x位数字为1对应的含义：
   * </pre>
   *
   * <code>int32 wheelThrottles = 3;</code>
   */
  int getWheelThrottles();
}
