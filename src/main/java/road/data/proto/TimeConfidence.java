// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *时间精度      
 * </pre>
 *
 * Protobuf enum {@code cn.seisys.v2x.pb.TimeConfidence}
 */
public enum TimeConfidence
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   * 未配备或不可用
   * </pre>
   *
   * <code>UNAVAILABLE = 0;</code>
   */
  UNAVAILABLE(0),
  /**
   * <pre>
   * 优于100 SECONDS
   * </pre>
   *
   * <code>TIME_100_000 = 1;</code>
   */
  TIME_100_000(1),
  /**
   * <pre>
   * 优于50 SECONDS
   * </pre>
   *
   * <code>TIME_050_000 = 2;</code>
   */
  TIME_050_000(2),
  /**
   * <pre>
   * 优于20 SECONDS
   * </pre>
   *
   * <code>TIME_020_000 = 3;</code>
   */
  TIME_020_000(3),
  /**
   * <pre>
   * 优于10 SECONDS
   * </pre>
   *
   * <code>TIME_010_000 = 4;</code>
   */
  TIME_010_000(4),
  /**
   * <pre>
   * 优于2 SECONDS
   * </pre>
   *
   * <code>TIME_002_000 = 5;</code>
   */
  TIME_002_000(5),
  /**
   * <pre>
   * 优于1 SECOND
   * </pre>
   *
   * <code>TIME_001_000 = 6;</code>
   */
  TIME_001_000(6),
  /**
   * <pre>
   * 优于0.5 SECONDS
   * </pre>
   *
   * <code>TIME_000_500 = 7;</code>
   */
  TIME_000_500(7),
  /**
   * <pre>
   * 优于0.2 SECONDS
   * </pre>
   *
   * <code>TIME_000_200 = 8;</code>
   */
  TIME_000_200(8),
  /**
   * <pre>
   * 优于0.1 SECONDS
   * </pre>
   *
   * <code>TIME_000_100 = 9;</code>
   */
  TIME_000_100(9),
  /**
   * <pre>
   * 优于0.05 SECONDS
   * </pre>
   *
   * <code>TIME_000_050 = 10;</code>
   */
  TIME_000_050(10),
  /**
   * <pre>
   * 优于0.02 SECONDS
   * </pre>
   *
   * <code>TIME_000_020 = 11;</code>
   */
  TIME_000_020(11),
  /**
   * <pre>
   * 优于0.01 SECONDS
   * </pre>
   *
   * <code>TIME_000_010 = 12;</code>
   */
  TIME_000_010(12),
  /**
   * <pre>
   * 优于0.005 SECONDS
   * </pre>
   *
   * <code>TIME_000_005 = 13;</code>
   */
  TIME_000_005(13),
  /**
   * <pre>
   * 优于0.002 SECONDS
   * </pre>
   *
   * <code>TIME_000_002 = 14;</code>
   */
  TIME_000_002(14),
  /**
   * <pre>
   * 优于0.001 SECONDS
   * </pre>
   *
   * <code>TIME_000_001 = 15;</code>
   */
  TIME_000_001(15),
  /**
   * <pre>
   * 优于0.000,5 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_5 = 16;</code>
   */
  TIME_000_000_5(16),
  /**
   * <pre>
   * 优于0.000,2 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_2 = 17;</code>
   */
  TIME_000_000_2(17),
  /**
   * <pre>
   *优于0.000,1 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_1 = 18;</code>
   */
  TIME_000_000_1(18),
  /**
   * <pre>
   *优于0.000,05 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_05 = 19;</code>
   */
  TIME_000_000_05(19),
  /**
   * <pre>
   * 优于0.000,02 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_02 = 20;</code>
   */
  TIME_000_000_02(20),
  /**
   * <pre>
   * 优于0.000,01 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_01 = 21;</code>
   */
  TIME_000_000_01(21),
  /**
   * <pre>
   * 优于0.000,005 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_005 = 22;</code>
   */
  TIME_000_000_005(22),
  /**
   * <pre>
   * 优于0.000,002 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_002 = 23;</code>
   */
  TIME_000_000_002(23),
  /**
   * <pre>
   * 优于0.000,001 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_001 = 24;</code>
   */
  TIME_000_000_001(24),
  /**
   * <pre>
   * 优于0.000,000,5 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_5 = 25;</code>
   */
  TIME_000_000_000_5(25),
  /**
   * <pre>
   * 优于0.000,000,2 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_2 = 26;</code>
   */
  TIME_000_000_000_2(26),
  /**
   * <pre>
   * 优于0.000,000,1 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_1 = 27;</code>
   */
  TIME_000_000_000_1(27),
  /**
   * <pre>
   * 优于0.000,000,05 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_05 = 28;</code>
   */
  TIME_000_000_000_05(28),
  /**
   * <pre>
   * 优于0.000,000,02 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_02 = 29;</code>
   */
  TIME_000_000_000_02(29),
  /**
   * <pre>
   *优于0.000,000,01 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_01 = 30;</code>
   */
  TIME_000_000_000_01(30),
  /**
   * <pre>
   * 优于0.000,000,005 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_005 = 31;</code>
   */
  TIME_000_000_000_005(31),
  /**
   * <pre>
   * 优于0.000,000,002 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_002 = 32;</code>
   */
  TIME_000_000_000_002(32),
  /**
   * <pre>
   * 优于0.000,000,001 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_001 = 33;</code>
   */
  TIME_000_000_000_001(33),
  /**
   * <pre>
   * 优于0.000,000,000,5 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_000_5 = 34;</code>
   */
  TIME_000_000_000_000_5(34),
  /**
   * <pre>
   *优于0.000,000,000,2 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_000_2 = 35;</code>
   */
  TIME_000_000_000_000_2(35),
  /**
   * <pre>
   * 优于0.000,000,000,1 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_000_1 = 36;</code>
   */
  TIME_000_000_000_000_1(36),
  /**
   * <pre>
   * 优于0.000,000,000,05 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_000_05 = 37;</code>
   */
  TIME_000_000_000_000_05(37),
  /**
   * <pre>
   * 优于0.000,000,000,02 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_000_02 = 38;</code>
   */
  TIME_000_000_000_000_02(38),
  /**
   * <pre>
   * 优于0.000,000,000,01 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_000_01 = 39;</code>
   */
  TIME_000_000_000_000_01(39),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   * 未配备或不可用
   * </pre>
   *
   * <code>UNAVAILABLE = 0;</code>
   */
  public static final int UNAVAILABLE_VALUE = 0;
  /**
   * <pre>
   * 优于100 SECONDS
   * </pre>
   *
   * <code>TIME_100_000 = 1;</code>
   */
  public static final int TIME_100_000_VALUE = 1;
  /**
   * <pre>
   * 优于50 SECONDS
   * </pre>
   *
   * <code>TIME_050_000 = 2;</code>
   */
  public static final int TIME_050_000_VALUE = 2;
  /**
   * <pre>
   * 优于20 SECONDS
   * </pre>
   *
   * <code>TIME_020_000 = 3;</code>
   */
  public static final int TIME_020_000_VALUE = 3;
  /**
   * <pre>
   * 优于10 SECONDS
   * </pre>
   *
   * <code>TIME_010_000 = 4;</code>
   */
  public static final int TIME_010_000_VALUE = 4;
  /**
   * <pre>
   * 优于2 SECONDS
   * </pre>
   *
   * <code>TIME_002_000 = 5;</code>
   */
  public static final int TIME_002_000_VALUE = 5;
  /**
   * <pre>
   * 优于1 SECOND
   * </pre>
   *
   * <code>TIME_001_000 = 6;</code>
   */
  public static final int TIME_001_000_VALUE = 6;
  /**
   * <pre>
   * 优于0.5 SECONDS
   * </pre>
   *
   * <code>TIME_000_500 = 7;</code>
   */
  public static final int TIME_000_500_VALUE = 7;
  /**
   * <pre>
   * 优于0.2 SECONDS
   * </pre>
   *
   * <code>TIME_000_200 = 8;</code>
   */
  public static final int TIME_000_200_VALUE = 8;
  /**
   * <pre>
   * 优于0.1 SECONDS
   * </pre>
   *
   * <code>TIME_000_100 = 9;</code>
   */
  public static final int TIME_000_100_VALUE = 9;
  /**
   * <pre>
   * 优于0.05 SECONDS
   * </pre>
   *
   * <code>TIME_000_050 = 10;</code>
   */
  public static final int TIME_000_050_VALUE = 10;
  /**
   * <pre>
   * 优于0.02 SECONDS
   * </pre>
   *
   * <code>TIME_000_020 = 11;</code>
   */
  public static final int TIME_000_020_VALUE = 11;
  /**
   * <pre>
   * 优于0.01 SECONDS
   * </pre>
   *
   * <code>TIME_000_010 = 12;</code>
   */
  public static final int TIME_000_010_VALUE = 12;
  /**
   * <pre>
   * 优于0.005 SECONDS
   * </pre>
   *
   * <code>TIME_000_005 = 13;</code>
   */
  public static final int TIME_000_005_VALUE = 13;
  /**
   * <pre>
   * 优于0.002 SECONDS
   * </pre>
   *
   * <code>TIME_000_002 = 14;</code>
   */
  public static final int TIME_000_002_VALUE = 14;
  /**
   * <pre>
   * 优于0.001 SECONDS
   * </pre>
   *
   * <code>TIME_000_001 = 15;</code>
   */
  public static final int TIME_000_001_VALUE = 15;
  /**
   * <pre>
   * 优于0.000,5 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_5 = 16;</code>
   */
  public static final int TIME_000_000_5_VALUE = 16;
  /**
   * <pre>
   * 优于0.000,2 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_2 = 17;</code>
   */
  public static final int TIME_000_000_2_VALUE = 17;
  /**
   * <pre>
   *优于0.000,1 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_1 = 18;</code>
   */
  public static final int TIME_000_000_1_VALUE = 18;
  /**
   * <pre>
   *优于0.000,05 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_05 = 19;</code>
   */
  public static final int TIME_000_000_05_VALUE = 19;
  /**
   * <pre>
   * 优于0.000,02 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_02 = 20;</code>
   */
  public static final int TIME_000_000_02_VALUE = 20;
  /**
   * <pre>
   * 优于0.000,01 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_01 = 21;</code>
   */
  public static final int TIME_000_000_01_VALUE = 21;
  /**
   * <pre>
   * 优于0.000,005 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_005 = 22;</code>
   */
  public static final int TIME_000_000_005_VALUE = 22;
  /**
   * <pre>
   * 优于0.000,002 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_002 = 23;</code>
   */
  public static final int TIME_000_000_002_VALUE = 23;
  /**
   * <pre>
   * 优于0.000,001 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_001 = 24;</code>
   */
  public static final int TIME_000_000_001_VALUE = 24;
  /**
   * <pre>
   * 优于0.000,000,5 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_5 = 25;</code>
   */
  public static final int TIME_000_000_000_5_VALUE = 25;
  /**
   * <pre>
   * 优于0.000,000,2 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_2 = 26;</code>
   */
  public static final int TIME_000_000_000_2_VALUE = 26;
  /**
   * <pre>
   * 优于0.000,000,1 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_1 = 27;</code>
   */
  public static final int TIME_000_000_000_1_VALUE = 27;
  /**
   * <pre>
   * 优于0.000,000,05 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_05 = 28;</code>
   */
  public static final int TIME_000_000_000_05_VALUE = 28;
  /**
   * <pre>
   * 优于0.000,000,02 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_02 = 29;</code>
   */
  public static final int TIME_000_000_000_02_VALUE = 29;
  /**
   * <pre>
   *优于0.000,000,01 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_01 = 30;</code>
   */
  public static final int TIME_000_000_000_01_VALUE = 30;
  /**
   * <pre>
   * 优于0.000,000,005 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_005 = 31;</code>
   */
  public static final int TIME_000_000_000_005_VALUE = 31;
  /**
   * <pre>
   * 优于0.000,000,002 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_002 = 32;</code>
   */
  public static final int TIME_000_000_000_002_VALUE = 32;
  /**
   * <pre>
   * 优于0.000,000,001 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_001 = 33;</code>
   */
  public static final int TIME_000_000_000_001_VALUE = 33;
  /**
   * <pre>
   * 优于0.000,000,000,5 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_000_5 = 34;</code>
   */
  public static final int TIME_000_000_000_000_5_VALUE = 34;
  /**
   * <pre>
   *优于0.000,000,000,2 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_000_2 = 35;</code>
   */
  public static final int TIME_000_000_000_000_2_VALUE = 35;
  /**
   * <pre>
   * 优于0.000,000,000,1 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_000_1 = 36;</code>
   */
  public static final int TIME_000_000_000_000_1_VALUE = 36;
  /**
   * <pre>
   * 优于0.000,000,000,05 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_000_05 = 37;</code>
   */
  public static final int TIME_000_000_000_000_05_VALUE = 37;
  /**
   * <pre>
   * 优于0.000,000,000,02 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_000_02 = 38;</code>
   */
  public static final int TIME_000_000_000_000_02_VALUE = 38;
  /**
   * <pre>
   * 优于0.000,000,000,01 SECONDS
   * </pre>
   *
   * <code>TIME_000_000_000_000_01 = 39;</code>
   */
  public static final int TIME_000_000_000_000_01_VALUE = 39;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static TimeConfidence valueOf(int value) {
    return forNumber(value);
  }

  public static TimeConfidence forNumber(int value) {
    switch (value) {
      case 0: return UNAVAILABLE;
      case 1: return TIME_100_000;
      case 2: return TIME_050_000;
      case 3: return TIME_020_000;
      case 4: return TIME_010_000;
      case 5: return TIME_002_000;
      case 6: return TIME_001_000;
      case 7: return TIME_000_500;
      case 8: return TIME_000_200;
      case 9: return TIME_000_100;
      case 10: return TIME_000_050;
      case 11: return TIME_000_020;
      case 12: return TIME_000_010;
      case 13: return TIME_000_005;
      case 14: return TIME_000_002;
      case 15: return TIME_000_001;
      case 16: return TIME_000_000_5;
      case 17: return TIME_000_000_2;
      case 18: return TIME_000_000_1;
      case 19: return TIME_000_000_05;
      case 20: return TIME_000_000_02;
      case 21: return TIME_000_000_01;
      case 22: return TIME_000_000_005;
      case 23: return TIME_000_000_002;
      case 24: return TIME_000_000_001;
      case 25: return TIME_000_000_000_5;
      case 26: return TIME_000_000_000_2;
      case 27: return TIME_000_000_000_1;
      case 28: return TIME_000_000_000_05;
      case 29: return TIME_000_000_000_02;
      case 30: return TIME_000_000_000_01;
      case 31: return TIME_000_000_000_005;
      case 32: return TIME_000_000_000_002;
      case 33: return TIME_000_000_000_001;
      case 34: return TIME_000_000_000_000_5;
      case 35: return TIME_000_000_000_000_2;
      case 36: return TIME_000_000_000_000_1;
      case 37: return TIME_000_000_000_000_05;
      case 38: return TIME_000_000_000_000_02;
      case 39: return TIME_000_000_000_000_01;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<TimeConfidence>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      TimeConfidence> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<TimeConfidence>() {
          public TimeConfidence findValueByNumber(int number) {
            return TimeConfidence.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return road.data.proto.V2X.getDescriptor().getEnumTypes().get(2);
  }

  private static final TimeConfidence[] VALUES = values();

  public static TimeConfidence valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private TimeConfidence(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.TimeConfidence)
}

