// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *相位倒计时状态TimeCountingDown    
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.TimeCountingDown}
 */
public  final class TimeCountingDown extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.TimeCountingDown)
    TimeCountingDownOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TimeCountingDown.newBuilder() to construct.
  private TimeCountingDown(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private TimeCountingDown() {
    timeConfidence_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new TimeCountingDown();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private TimeCountingDown(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            startTime_ = input.readUInt32();
            break;
          }
          case 16: {

            minEndTime_ = input.readUInt32();
            break;
          }
          case 24: {

            maxEndTime_ = input.readUInt32();
            break;
          }
          case 32: {

            likelyEndTime_ = input.readUInt32();
            break;
          }
          case 40: {
            int rawValue = input.readEnum();

            timeConfidence_ = rawValue;
            break;
          }
          case 48: {

            nextStartTime_ = input.readUInt32();
            break;
          }
          case 56: {

            nextDuration_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TimeCountingDown_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TimeCountingDown_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.TimeCountingDown.class, road.data.proto.TimeCountingDown.Builder.class);
  }

  public static final int STARTTIME_FIELD_NUMBER = 1;
  private int startTime_;
  /**
   * <pre>
   * [0,36001] 信号灯当前处于该灯色状态，则值为 0，否则为该灯色 状态下一次开始（据离当前）的时间
   * </pre>
   *
   * <code>uint32 startTime = 1;</code>
   */
  public int getStartTime() {
    return startTime_;
  }

  public static final int MINENDTIME_FIELD_NUMBER = 2;
  private int minEndTime_;
  /**
   * <pre>
   * 可选，[0,36001]表示当前时刻距离该相位状态下一次结束的最短时间（不管当前时刻该相位状态是否开始）。对于固定周期配时信号灯，minEndTime 应该等于 maxEndTime。
   * </pre>
   *
   * <code>uint32 minEndTime = 2;</code>
   */
  public int getMinEndTime() {
    return minEndTime_;
  }

  public static final int MAXENDTIME_FIELD_NUMBER = 3;
  private int maxEndTime_;
  /**
   * <pre>
   * 可选，[0,36001] 表示当前时刻距离该相位状态下一次结束的最长时间（不管当前时刻该相位状态是否开始）。
   * </pre>
   *
   * <code>uint32 maxEndTime = 3;</code>
   */
  public int getMaxEndTime() {
    return maxEndTime_;
  }

  public static final int LIKELYENDTIME_FIELD_NUMBER = 4;
  private int likelyEndTime_;
  /**
   * <pre>
   * [0,36001] 表示当前时刻距离该相位状态下一次结束的估计时间（不管当前时刻该相位状态是否开始）。
   * </pre>
   *
   * <code>uint32 likelyEndTime = 4;</code>
   */
  public int getLikelyEndTime() {
    return likelyEndTime_;
  }

  public static final int TIMECONFIDENCE_FIELD_NUMBER = 5;
  private int timeConfidence_;
  /**
   * <pre>
   * 可选，(0,200) 定义置信度。分辨率为0.005。上述 likelyEndTime 预测时间的置信度水平。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 5;</code>
   */
  public int getTimeConfidenceValue() {
    return timeConfidence_;
  }
  /**
   * <pre>
   * 可选，(0,200) 定义置信度。分辨率为0.005。上述 likelyEndTime 预测时间的置信度水平。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 5;</code>
   */
  public road.data.proto.TimeConfidence getTimeConfidence() {
    @SuppressWarnings("deprecation")
    road.data.proto.TimeConfidence result = road.data.proto.TimeConfidence.valueOf(timeConfidence_);
    return result == null ? road.data.proto.TimeConfidence.UNRECOGNIZED : result;
  }

  public static final int NEXTSTARTTIME_FIELD_NUMBER = 6;
  private int nextStartTime_;
  /**
   * <pre>
   * 可选，[0,36001] 如果当前该相位状态已开始（未结束），则该数值表示当前时刻距离该相位状态下一次开始
   * </pre>
   *
   * <code>uint32 nextStartTime = 6;</code>
   */
  public int getNextStartTime() {
    return nextStartTime_;
  }

  public static final int NEXTDURATION_FIELD_NUMBER = 7;
  private int nextDuration_;
  /**
   * <pre>
   * 可选，[0,36001] 如果当前该相位状态已开始（未结束），则该数值表示该相位状态下一次开始后的持续时长;如果当前该相位状态未开始，则表示该相位状态第二次开始后的持续时长。与 nextStartTime 配合
   * </pre>
   *
   * <code>uint32 nextDuration = 7;</code>
   */
  public int getNextDuration() {
    return nextDuration_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (startTime_ != 0) {
      output.writeUInt32(1, startTime_);
    }
    if (minEndTime_ != 0) {
      output.writeUInt32(2, minEndTime_);
    }
    if (maxEndTime_ != 0) {
      output.writeUInt32(3, maxEndTime_);
    }
    if (likelyEndTime_ != 0) {
      output.writeUInt32(4, likelyEndTime_);
    }
    if (timeConfidence_ != road.data.proto.TimeConfidence.UNAVAILABLE.getNumber()) {
      output.writeEnum(5, timeConfidence_);
    }
    if (nextStartTime_ != 0) {
      output.writeUInt32(6, nextStartTime_);
    }
    if (nextDuration_ != 0) {
      output.writeUInt32(7, nextDuration_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (startTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, startTime_);
    }
    if (minEndTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, minEndTime_);
    }
    if (maxEndTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, maxEndTime_);
    }
    if (likelyEndTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(4, likelyEndTime_);
    }
    if (timeConfidence_ != road.data.proto.TimeConfidence.UNAVAILABLE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(5, timeConfidence_);
    }
    if (nextStartTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(6, nextStartTime_);
    }
    if (nextDuration_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(7, nextDuration_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.TimeCountingDown)) {
      return super.equals(obj);
    }
    road.data.proto.TimeCountingDown other = (road.data.proto.TimeCountingDown) obj;

    if (getStartTime()
        != other.getStartTime()) return false;
    if (getMinEndTime()
        != other.getMinEndTime()) return false;
    if (getMaxEndTime()
        != other.getMaxEndTime()) return false;
    if (getLikelyEndTime()
        != other.getLikelyEndTime()) return false;
    if (timeConfidence_ != other.timeConfidence_) return false;
    if (getNextStartTime()
        != other.getNextStartTime()) return false;
    if (getNextDuration()
        != other.getNextDuration()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + STARTTIME_FIELD_NUMBER;
    hash = (53 * hash) + getStartTime();
    hash = (37 * hash) + MINENDTIME_FIELD_NUMBER;
    hash = (53 * hash) + getMinEndTime();
    hash = (37 * hash) + MAXENDTIME_FIELD_NUMBER;
    hash = (53 * hash) + getMaxEndTime();
    hash = (37 * hash) + LIKELYENDTIME_FIELD_NUMBER;
    hash = (53 * hash) + getLikelyEndTime();
    hash = (37 * hash) + TIMECONFIDENCE_FIELD_NUMBER;
    hash = (53 * hash) + timeConfidence_;
    hash = (37 * hash) + NEXTSTARTTIME_FIELD_NUMBER;
    hash = (53 * hash) + getNextStartTime();
    hash = (37 * hash) + NEXTDURATION_FIELD_NUMBER;
    hash = (53 * hash) + getNextDuration();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.TimeCountingDown parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TimeCountingDown parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TimeCountingDown parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TimeCountingDown parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TimeCountingDown parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TimeCountingDown parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TimeCountingDown parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TimeCountingDown parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TimeCountingDown parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.TimeCountingDown parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TimeCountingDown parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TimeCountingDown parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.TimeCountingDown prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *相位倒计时状态TimeCountingDown    
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.TimeCountingDown}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.TimeCountingDown)
      road.data.proto.TimeCountingDownOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TimeCountingDown_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TimeCountingDown_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.TimeCountingDown.class, road.data.proto.TimeCountingDown.Builder.class);
    }

    // Construct using road.data.proto.TimeCountingDown.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      startTime_ = 0;

      minEndTime_ = 0;

      maxEndTime_ = 0;

      likelyEndTime_ = 0;

      timeConfidence_ = 0;

      nextStartTime_ = 0;

      nextDuration_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TimeCountingDown_descriptor;
    }

    @java.lang.Override
    public road.data.proto.TimeCountingDown getDefaultInstanceForType() {
      return road.data.proto.TimeCountingDown.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.TimeCountingDown build() {
      road.data.proto.TimeCountingDown result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.TimeCountingDown buildPartial() {
      road.data.proto.TimeCountingDown result = new road.data.proto.TimeCountingDown(this);
      result.startTime_ = startTime_;
      result.minEndTime_ = minEndTime_;
      result.maxEndTime_ = maxEndTime_;
      result.likelyEndTime_ = likelyEndTime_;
      result.timeConfidence_ = timeConfidence_;
      result.nextStartTime_ = nextStartTime_;
      result.nextDuration_ = nextDuration_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.TimeCountingDown) {
        return mergeFrom((road.data.proto.TimeCountingDown)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.TimeCountingDown other) {
      if (other == road.data.proto.TimeCountingDown.getDefaultInstance()) return this;
      if (other.getStartTime() != 0) {
        setStartTime(other.getStartTime());
      }
      if (other.getMinEndTime() != 0) {
        setMinEndTime(other.getMinEndTime());
      }
      if (other.getMaxEndTime() != 0) {
        setMaxEndTime(other.getMaxEndTime());
      }
      if (other.getLikelyEndTime() != 0) {
        setLikelyEndTime(other.getLikelyEndTime());
      }
      if (other.timeConfidence_ != 0) {
        setTimeConfidenceValue(other.getTimeConfidenceValue());
      }
      if (other.getNextStartTime() != 0) {
        setNextStartTime(other.getNextStartTime());
      }
      if (other.getNextDuration() != 0) {
        setNextDuration(other.getNextDuration());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.TimeCountingDown parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.TimeCountingDown) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int startTime_ ;
    /**
     * <pre>
     * [0,36001] 信号灯当前处于该灯色状态，则值为 0，否则为该灯色 状态下一次开始（据离当前）的时间
     * </pre>
     *
     * <code>uint32 startTime = 1;</code>
     */
    public int getStartTime() {
      return startTime_;
    }
    /**
     * <pre>
     * [0,36001] 信号灯当前处于该灯色状态，则值为 0，否则为该灯色 状态下一次开始（据离当前）的时间
     * </pre>
     *
     * <code>uint32 startTime = 1;</code>
     */
    public Builder setStartTime(int value) {
      
      startTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * [0,36001] 信号灯当前处于该灯色状态，则值为 0，否则为该灯色 状态下一次开始（据离当前）的时间
     * </pre>
     *
     * <code>uint32 startTime = 1;</code>
     */
    public Builder clearStartTime() {
      
      startTime_ = 0;
      onChanged();
      return this;
    }

    private int minEndTime_ ;
    /**
     * <pre>
     * 可选，[0,36001]表示当前时刻距离该相位状态下一次结束的最短时间（不管当前时刻该相位状态是否开始）。对于固定周期配时信号灯，minEndTime 应该等于 maxEndTime。
     * </pre>
     *
     * <code>uint32 minEndTime = 2;</code>
     */
    public int getMinEndTime() {
      return minEndTime_;
    }
    /**
     * <pre>
     * 可选，[0,36001]表示当前时刻距离该相位状态下一次结束的最短时间（不管当前时刻该相位状态是否开始）。对于固定周期配时信号灯，minEndTime 应该等于 maxEndTime。
     * </pre>
     *
     * <code>uint32 minEndTime = 2;</code>
     */
    public Builder setMinEndTime(int value) {
      
      minEndTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，[0,36001]表示当前时刻距离该相位状态下一次结束的最短时间（不管当前时刻该相位状态是否开始）。对于固定周期配时信号灯，minEndTime 应该等于 maxEndTime。
     * </pre>
     *
     * <code>uint32 minEndTime = 2;</code>
     */
    public Builder clearMinEndTime() {
      
      minEndTime_ = 0;
      onChanged();
      return this;
    }

    private int maxEndTime_ ;
    /**
     * <pre>
     * 可选，[0,36001] 表示当前时刻距离该相位状态下一次结束的最长时间（不管当前时刻该相位状态是否开始）。
     * </pre>
     *
     * <code>uint32 maxEndTime = 3;</code>
     */
    public int getMaxEndTime() {
      return maxEndTime_;
    }
    /**
     * <pre>
     * 可选，[0,36001] 表示当前时刻距离该相位状态下一次结束的最长时间（不管当前时刻该相位状态是否开始）。
     * </pre>
     *
     * <code>uint32 maxEndTime = 3;</code>
     */
    public Builder setMaxEndTime(int value) {
      
      maxEndTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，[0,36001] 表示当前时刻距离该相位状态下一次结束的最长时间（不管当前时刻该相位状态是否开始）。
     * </pre>
     *
     * <code>uint32 maxEndTime = 3;</code>
     */
    public Builder clearMaxEndTime() {
      
      maxEndTime_ = 0;
      onChanged();
      return this;
    }

    private int likelyEndTime_ ;
    /**
     * <pre>
     * [0,36001] 表示当前时刻距离该相位状态下一次结束的估计时间（不管当前时刻该相位状态是否开始）。
     * </pre>
     *
     * <code>uint32 likelyEndTime = 4;</code>
     */
    public int getLikelyEndTime() {
      return likelyEndTime_;
    }
    /**
     * <pre>
     * [0,36001] 表示当前时刻距离该相位状态下一次结束的估计时间（不管当前时刻该相位状态是否开始）。
     * </pre>
     *
     * <code>uint32 likelyEndTime = 4;</code>
     */
    public Builder setLikelyEndTime(int value) {
      
      likelyEndTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * [0,36001] 表示当前时刻距离该相位状态下一次结束的估计时间（不管当前时刻该相位状态是否开始）。
     * </pre>
     *
     * <code>uint32 likelyEndTime = 4;</code>
     */
    public Builder clearLikelyEndTime() {
      
      likelyEndTime_ = 0;
      onChanged();
      return this;
    }

    private int timeConfidence_ = 0;
    /**
     * <pre>
     * 可选，(0,200) 定义置信度。分辨率为0.005。上述 likelyEndTime 预测时间的置信度水平。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 5;</code>
     */
    public int getTimeConfidenceValue() {
      return timeConfidence_;
    }
    /**
     * <pre>
     * 可选，(0,200) 定义置信度。分辨率为0.005。上述 likelyEndTime 预测时间的置信度水平。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 5;</code>
     */
    public Builder setTimeConfidenceValue(int value) {
      timeConfidence_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，(0,200) 定义置信度。分辨率为0.005。上述 likelyEndTime 预测时间的置信度水平。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 5;</code>
     */
    public road.data.proto.TimeConfidence getTimeConfidence() {
      @SuppressWarnings("deprecation")
      road.data.proto.TimeConfidence result = road.data.proto.TimeConfidence.valueOf(timeConfidence_);
      return result == null ? road.data.proto.TimeConfidence.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，(0,200) 定义置信度。分辨率为0.005。上述 likelyEndTime 预测时间的置信度水平。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 5;</code>
     */
    public Builder setTimeConfidence(road.data.proto.TimeConfidence value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      timeConfidence_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，(0,200) 定义置信度。分辨率为0.005。上述 likelyEndTime 预测时间的置信度水平。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 5;</code>
     */
    public Builder clearTimeConfidence() {
      
      timeConfidence_ = 0;
      onChanged();
      return this;
    }

    private int nextStartTime_ ;
    /**
     * <pre>
     * 可选，[0,36001] 如果当前该相位状态已开始（未结束），则该数值表示当前时刻距离该相位状态下一次开始
     * </pre>
     *
     * <code>uint32 nextStartTime = 6;</code>
     */
    public int getNextStartTime() {
      return nextStartTime_;
    }
    /**
     * <pre>
     * 可选，[0,36001] 如果当前该相位状态已开始（未结束），则该数值表示当前时刻距离该相位状态下一次开始
     * </pre>
     *
     * <code>uint32 nextStartTime = 6;</code>
     */
    public Builder setNextStartTime(int value) {
      
      nextStartTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，[0,36001] 如果当前该相位状态已开始（未结束），则该数值表示当前时刻距离该相位状态下一次开始
     * </pre>
     *
     * <code>uint32 nextStartTime = 6;</code>
     */
    public Builder clearNextStartTime() {
      
      nextStartTime_ = 0;
      onChanged();
      return this;
    }

    private int nextDuration_ ;
    /**
     * <pre>
     * 可选，[0,36001] 如果当前该相位状态已开始（未结束），则该数值表示该相位状态下一次开始后的持续时长;如果当前该相位状态未开始，则表示该相位状态第二次开始后的持续时长。与 nextStartTime 配合
     * </pre>
     *
     * <code>uint32 nextDuration = 7;</code>
     */
    public int getNextDuration() {
      return nextDuration_;
    }
    /**
     * <pre>
     * 可选，[0,36001] 如果当前该相位状态已开始（未结束），则该数值表示该相位状态下一次开始后的持续时长;如果当前该相位状态未开始，则表示该相位状态第二次开始后的持续时长。与 nextStartTime 配合
     * </pre>
     *
     * <code>uint32 nextDuration = 7;</code>
     */
    public Builder setNextDuration(int value) {
      
      nextDuration_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，[0,36001] 如果当前该相位状态已开始（未结束），则该数值表示该相位状态下一次开始后的持续时长;如果当前该相位状态未开始，则表示该相位状态第二次开始后的持续时长。与 nextStartTime 配合
     * </pre>
     *
     * <code>uint32 nextDuration = 7;</code>
     */
    public Builder clearNextDuration() {
      
      nextDuration_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.TimeCountingDown)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.TimeCountingDown)
  private static final road.data.proto.TimeCountingDown DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.TimeCountingDown();
  }

  public static road.data.proto.TimeCountingDown getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TimeCountingDown>
      PARSER = new com.google.protobuf.AbstractParser<TimeCountingDown>() {
    @java.lang.Override
    public TimeCountingDown parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new TimeCountingDown(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<TimeCountingDown> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TimeCountingDown> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.TimeCountingDown getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

