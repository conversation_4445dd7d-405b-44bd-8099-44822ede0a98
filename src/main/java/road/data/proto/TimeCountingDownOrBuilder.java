// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface TimeCountingDownOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.TimeCountingDown)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * [0,36001] 信号灯当前处于该灯色状态，则值为 0，否则为该灯色 状态下一次开始（据离当前）的时间
   * </pre>
   *
   * <code>uint32 startTime = 1;</code>
   */
  int getStartTime();

  /**
   * <pre>
   * 可选，[0,36001]表示当前时刻距离该相位状态下一次结束的最短时间（不管当前时刻该相位状态是否开始）。对于固定周期配时信号灯，minEndTime 应该等于 maxEndTime。
   * </pre>
   *
   * <code>uint32 minEndTime = 2;</code>
   */
  int getMinEndTime();

  /**
   * <pre>
   * 可选，[0,36001] 表示当前时刻距离该相位状态下一次结束的最长时间（不管当前时刻该相位状态是否开始）。
   * </pre>
   *
   * <code>uint32 maxEndTime = 3;</code>
   */
  int getMaxEndTime();

  /**
   * <pre>
   * [0,36001] 表示当前时刻距离该相位状态下一次结束的估计时间（不管当前时刻该相位状态是否开始）。
   * </pre>
   *
   * <code>uint32 likelyEndTime = 4;</code>
   */
  int getLikelyEndTime();

  /**
   * <pre>
   * 可选，(0,200) 定义置信度。分辨率为0.005。上述 likelyEndTime 预测时间的置信度水平。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 5;</code>
   */
  int getTimeConfidenceValue();
  /**
   * <pre>
   * 可选，(0,200) 定义置信度。分辨率为0.005。上述 likelyEndTime 预测时间的置信度水平。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TimeConfidence timeConfidence = 5;</code>
   */
  road.data.proto.TimeConfidence getTimeConfidence();

  /**
   * <pre>
   * 可选，[0,36001] 如果当前该相位状态已开始（未结束），则该数值表示当前时刻距离该相位状态下一次开始
   * </pre>
   *
   * <code>uint32 nextStartTime = 6;</code>
   */
  int getNextStartTime();

  /**
   * <pre>
   * 可选，[0,36001] 如果当前该相位状态已开始（未结束），则该数值表示该相位状态下一次开始后的持续时长;如果当前该相位状态未开始，则表示该相位状态第二次开始后的持续时长。与 nextStartTime 配合
   * </pre>
   *
   * <code>uint32 nextDuration = 7;</code>
   */
  int getNextDuration();
}
