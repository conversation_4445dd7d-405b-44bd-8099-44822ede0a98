// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *交通流综合统计信息     
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.TrafficFlow}
 */
public  final class TrafficFlow extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.TrafficFlow)
    TrafficFlowOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TrafficFlow.newBuilder() to construct.
  private TrafficFlow(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private TrafficFlow() {
    stats_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new TrafficFlow();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private TrafficFlow(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.NodeReferenceId.Builder subBuilder = null;
            if (nodeId_ != null) {
              subBuilder = nodeId_.toBuilder();
            }
            nodeId_ = input.readMessage(road.data.proto.NodeReferenceId.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(nodeId_);
              nodeId_ = subBuilder.buildPartial();
            }

            break;
          }
          case 16: {

            genTime_ = input.readUInt64();
            break;
          }
          case 26: {
            road.data.proto.TrafficFlowStatType.Builder subBuilder = null;
            if (statType_ != null) {
              subBuilder = statType_.toBuilder();
            }
            statType_ = input.readMessage(road.data.proto.TrafficFlowStatType.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(statType_);
              statType_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              stats_ = new java.util.ArrayList<road.data.proto.TrafficFlowStat>();
              mutable_bitField0_ |= 0x00000001;
            }
            stats_.add(
                input.readMessage(road.data.proto.TrafficFlowStat.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        stats_ = java.util.Collections.unmodifiableList(stats_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlow_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlow_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.TrafficFlow.class, road.data.proto.TrafficFlow.Builder.class);
  }

  public static final int NODEID_FIELD_NUMBER = 1;
  private road.data.proto.NodeReferenceId nodeId_;
  /**
   * <pre>
   *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  public boolean hasNodeId() {
    return nodeId_ != null;
  }
  /**
   * <pre>
   *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  public road.data.proto.NodeReferenceId getNodeId() {
    return nodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
  }
  /**
   * <pre>
   *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  public road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder() {
    return getNodeId();
  }

  public static final int GENTIME_FIELD_NUMBER = 2;
  private long genTime_;
  /**
   * <pre>
   *可选，消息生成时刻UNIX时间戳（秒级）
   * </pre>
   *
   * <code>uint64 genTime = 2;</code>
   */
  public long getGenTime() {
    return genTime_;
  }

  public static final int STATTYPE_FIELD_NUMBER = 3;
  private road.data.proto.TrafficFlowStatType statType_;
  /**
   * <pre>
   * 交通流统计方式信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
   */
  public boolean hasStatType() {
    return statType_ != null;
  }
  /**
   * <pre>
   * 交通流统计方式信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
   */
  public road.data.proto.TrafficFlowStatType getStatType() {
    return statType_ == null ? road.data.proto.TrafficFlowStatType.getDefaultInstance() : statType_;
  }
  /**
   * <pre>
   * 交通流统计方式信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
   */
  public road.data.proto.TrafficFlowStatTypeOrBuilder getStatTypeOrBuilder() {
    return getStatType();
  }

  public static final int STATS_FIELD_NUMBER = 4;
  private java.util.List<road.data.proto.TrafficFlowStat> stats_;
  /**
   * <pre>
   *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
   */
  public java.util.List<road.data.proto.TrafficFlowStat> getStatsList() {
    return stats_;
  }
  /**
   * <pre>
   *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
   */
  public java.util.List<? extends road.data.proto.TrafficFlowStatOrBuilder> 
      getStatsOrBuilderList() {
    return stats_;
  }
  /**
   * <pre>
   *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
   */
  public int getStatsCount() {
    return stats_.size();
  }
  /**
   * <pre>
   *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
   */
  public road.data.proto.TrafficFlowStat getStats(int index) {
    return stats_.get(index);
  }
  /**
   * <pre>
   *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
   */
  public road.data.proto.TrafficFlowStatOrBuilder getStatsOrBuilder(
      int index) {
    return stats_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (nodeId_ != null) {
      output.writeMessage(1, getNodeId());
    }
    if (genTime_ != 0L) {
      output.writeUInt64(2, genTime_);
    }
    if (statType_ != null) {
      output.writeMessage(3, getStatType());
    }
    for (int i = 0; i < stats_.size(); i++) {
      output.writeMessage(4, stats_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (nodeId_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getNodeId());
    }
    if (genTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(2, genTime_);
    }
    if (statType_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getStatType());
    }
    for (int i = 0; i < stats_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, stats_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.TrafficFlow)) {
      return super.equals(obj);
    }
    road.data.proto.TrafficFlow other = (road.data.proto.TrafficFlow) obj;

    if (hasNodeId() != other.hasNodeId()) return false;
    if (hasNodeId()) {
      if (!getNodeId()
          .equals(other.getNodeId())) return false;
    }
    if (getGenTime()
        != other.getGenTime()) return false;
    if (hasStatType() != other.hasStatType()) return false;
    if (hasStatType()) {
      if (!getStatType()
          .equals(other.getStatType())) return false;
    }
    if (!getStatsList()
        .equals(other.getStatsList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasNodeId()) {
      hash = (37 * hash) + NODEID_FIELD_NUMBER;
      hash = (53 * hash) + getNodeId().hashCode();
    }
    hash = (37 * hash) + GENTIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getGenTime());
    if (hasStatType()) {
      hash = (37 * hash) + STATTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getStatType().hashCode();
    }
    if (getStatsCount() > 0) {
      hash = (37 * hash) + STATS_FIELD_NUMBER;
      hash = (53 * hash) + getStatsList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.TrafficFlow parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlow parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlow parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlow parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlow parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlow parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlow parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlow parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TrafficFlow parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlow parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TrafficFlow parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlow parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.TrafficFlow prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *交通流综合统计信息     
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.TrafficFlow}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.TrafficFlow)
      road.data.proto.TrafficFlowOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlow_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlow_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.TrafficFlow.class, road.data.proto.TrafficFlow.Builder.class);
    }

    // Construct using road.data.proto.TrafficFlow.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getStatsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (nodeIdBuilder_ == null) {
        nodeId_ = null;
      } else {
        nodeId_ = null;
        nodeIdBuilder_ = null;
      }
      genTime_ = 0L;

      if (statTypeBuilder_ == null) {
        statType_ = null;
      } else {
        statType_ = null;
        statTypeBuilder_ = null;
      }
      if (statsBuilder_ == null) {
        stats_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        statsBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlow_descriptor;
    }

    @java.lang.Override
    public road.data.proto.TrafficFlow getDefaultInstanceForType() {
      return road.data.proto.TrafficFlow.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.TrafficFlow build() {
      road.data.proto.TrafficFlow result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.TrafficFlow buildPartial() {
      road.data.proto.TrafficFlow result = new road.data.proto.TrafficFlow(this);
      int from_bitField0_ = bitField0_;
      if (nodeIdBuilder_ == null) {
        result.nodeId_ = nodeId_;
      } else {
        result.nodeId_ = nodeIdBuilder_.build();
      }
      result.genTime_ = genTime_;
      if (statTypeBuilder_ == null) {
        result.statType_ = statType_;
      } else {
        result.statType_ = statTypeBuilder_.build();
      }
      if (statsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          stats_ = java.util.Collections.unmodifiableList(stats_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.stats_ = stats_;
      } else {
        result.stats_ = statsBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.TrafficFlow) {
        return mergeFrom((road.data.proto.TrafficFlow)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.TrafficFlow other) {
      if (other == road.data.proto.TrafficFlow.getDefaultInstance()) return this;
      if (other.hasNodeId()) {
        mergeNodeId(other.getNodeId());
      }
      if (other.getGenTime() != 0L) {
        setGenTime(other.getGenTime());
      }
      if (other.hasStatType()) {
        mergeStatType(other.getStatType());
      }
      if (statsBuilder_ == null) {
        if (!other.stats_.isEmpty()) {
          if (stats_.isEmpty()) {
            stats_ = other.stats_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureStatsIsMutable();
            stats_.addAll(other.stats_);
          }
          onChanged();
        }
      } else {
        if (!other.stats_.isEmpty()) {
          if (statsBuilder_.isEmpty()) {
            statsBuilder_.dispose();
            statsBuilder_ = null;
            stats_ = other.stats_;
            bitField0_ = (bitField0_ & ~0x00000001);
            statsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getStatsFieldBuilder() : null;
          } else {
            statsBuilder_.addAllMessages(other.stats_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.TrafficFlow parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.TrafficFlow) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private road.data.proto.NodeReferenceId nodeId_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> nodeIdBuilder_;
    /**
     * <pre>
     *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public boolean hasNodeId() {
      return nodeIdBuilder_ != null || nodeId_ != null;
    }
    /**
     * <pre>
     *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceId getNodeId() {
      if (nodeIdBuilder_ == null) {
        return nodeId_ == null ? road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
      } else {
        return nodeIdBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder setNodeId(road.data.proto.NodeReferenceId value) {
      if (nodeIdBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        nodeId_ = value;
        onChanged();
      } else {
        nodeIdBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder setNodeId(
        road.data.proto.NodeReferenceId.Builder builderForValue) {
      if (nodeIdBuilder_ == null) {
        nodeId_ = builderForValue.build();
        onChanged();
      } else {
        nodeIdBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder mergeNodeId(road.data.proto.NodeReferenceId value) {
      if (nodeIdBuilder_ == null) {
        if (nodeId_ != null) {
          nodeId_ =
            road.data.proto.NodeReferenceId.newBuilder(nodeId_).mergeFrom(value).buildPartial();
        } else {
          nodeId_ = value;
        }
        onChanged();
      } else {
        nodeIdBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public Builder clearNodeId() {
      if (nodeIdBuilder_ == null) {
        nodeId_ = null;
        onChanged();
      } else {
        nodeId_ = null;
        nodeIdBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceId.Builder getNodeIdBuilder() {
      
      onChanged();
      return getNodeIdFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    public road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder() {
      if (nodeIdBuilder_ != null) {
        return nodeIdBuilder_.getMessageOrBuilder();
      } else {
        return nodeId_ == null ?
            road.data.proto.NodeReferenceId.getDefaultInstance() : nodeId_;
      }
    }
    /**
     * <pre>
     *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder> 
        getNodeIdFieldBuilder() {
      if (nodeIdBuilder_ == null) {
        nodeIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeReferenceId, road.data.proto.NodeReferenceId.Builder, road.data.proto.NodeReferenceIdOrBuilder>(
                getNodeId(),
                getParentForChildren(),
                isClean());
        nodeId_ = null;
      }
      return nodeIdBuilder_;
    }

    private long genTime_ ;
    /**
     * <pre>
     *可选，消息生成时刻UNIX时间戳（秒级）
     * </pre>
     *
     * <code>uint64 genTime = 2;</code>
     */
    public long getGenTime() {
      return genTime_;
    }
    /**
     * <pre>
     *可选，消息生成时刻UNIX时间戳（秒级）
     * </pre>
     *
     * <code>uint64 genTime = 2;</code>
     */
    public Builder setGenTime(long value) {
      
      genTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，消息生成时刻UNIX时间戳（秒级）
     * </pre>
     *
     * <code>uint64 genTime = 2;</code>
     */
    public Builder clearGenTime() {
      
      genTime_ = 0L;
      onChanged();
      return this;
    }

    private road.data.proto.TrafficFlowStatType statType_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.TrafficFlowStatType, road.data.proto.TrafficFlowStatType.Builder, road.data.proto.TrafficFlowStatTypeOrBuilder> statTypeBuilder_;
    /**
     * <pre>
     * 交通流统计方式信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
     */
    public boolean hasStatType() {
      return statTypeBuilder_ != null || statType_ != null;
    }
    /**
     * <pre>
     * 交通流统计方式信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
     */
    public road.data.proto.TrafficFlowStatType getStatType() {
      if (statTypeBuilder_ == null) {
        return statType_ == null ? road.data.proto.TrafficFlowStatType.getDefaultInstance() : statType_;
      } else {
        return statTypeBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 交通流统计方式信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
     */
    public Builder setStatType(road.data.proto.TrafficFlowStatType value) {
      if (statTypeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        statType_ = value;
        onChanged();
      } else {
        statTypeBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 交通流统计方式信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
     */
    public Builder setStatType(
        road.data.proto.TrafficFlowStatType.Builder builderForValue) {
      if (statTypeBuilder_ == null) {
        statType_ = builderForValue.build();
        onChanged();
      } else {
        statTypeBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 交通流统计方式信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
     */
    public Builder mergeStatType(road.data.proto.TrafficFlowStatType value) {
      if (statTypeBuilder_ == null) {
        if (statType_ != null) {
          statType_ =
            road.data.proto.TrafficFlowStatType.newBuilder(statType_).mergeFrom(value).buildPartial();
        } else {
          statType_ = value;
        }
        onChanged();
      } else {
        statTypeBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 交通流统计方式信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
     */
    public Builder clearStatType() {
      if (statTypeBuilder_ == null) {
        statType_ = null;
        onChanged();
      } else {
        statType_ = null;
        statTypeBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 交通流统计方式信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
     */
    public road.data.proto.TrafficFlowStatType.Builder getStatTypeBuilder() {
      
      onChanged();
      return getStatTypeFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 交通流统计方式信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
     */
    public road.data.proto.TrafficFlowStatTypeOrBuilder getStatTypeOrBuilder() {
      if (statTypeBuilder_ != null) {
        return statTypeBuilder_.getMessageOrBuilder();
      } else {
        return statType_ == null ?
            road.data.proto.TrafficFlowStatType.getDefaultInstance() : statType_;
      }
    }
    /**
     * <pre>
     * 交通流统计方式信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.TrafficFlowStatType, road.data.proto.TrafficFlowStatType.Builder, road.data.proto.TrafficFlowStatTypeOrBuilder> 
        getStatTypeFieldBuilder() {
      if (statTypeBuilder_ == null) {
        statTypeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.TrafficFlowStatType, road.data.proto.TrafficFlowStatType.Builder, road.data.proto.TrafficFlowStatTypeOrBuilder>(
                getStatType(),
                getParentForChildren(),
                isClean());
        statType_ = null;
      }
      return statTypeBuilder_;
    }

    private java.util.List<road.data.proto.TrafficFlowStat> stats_ =
      java.util.Collections.emptyList();
    private void ensureStatsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        stats_ = new java.util.ArrayList<road.data.proto.TrafficFlowStat>(stats_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.TrafficFlowStat, road.data.proto.TrafficFlowStat.Builder, road.data.proto.TrafficFlowStatOrBuilder> statsBuilder_;

    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public java.util.List<road.data.proto.TrafficFlowStat> getStatsList() {
      if (statsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(stats_);
      } else {
        return statsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public int getStatsCount() {
      if (statsBuilder_ == null) {
        return stats_.size();
      } else {
        return statsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public road.data.proto.TrafficFlowStat getStats(int index) {
      if (statsBuilder_ == null) {
        return stats_.get(index);
      } else {
        return statsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public Builder setStats(
        int index, road.data.proto.TrafficFlowStat value) {
      if (statsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureStatsIsMutable();
        stats_.set(index, value);
        onChanged();
      } else {
        statsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public Builder setStats(
        int index, road.data.proto.TrafficFlowStat.Builder builderForValue) {
      if (statsBuilder_ == null) {
        ensureStatsIsMutable();
        stats_.set(index, builderForValue.build());
        onChanged();
      } else {
        statsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public Builder addStats(road.data.proto.TrafficFlowStat value) {
      if (statsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureStatsIsMutable();
        stats_.add(value);
        onChanged();
      } else {
        statsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public Builder addStats(
        int index, road.data.proto.TrafficFlowStat value) {
      if (statsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureStatsIsMutable();
        stats_.add(index, value);
        onChanged();
      } else {
        statsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public Builder addStats(
        road.data.proto.TrafficFlowStat.Builder builderForValue) {
      if (statsBuilder_ == null) {
        ensureStatsIsMutable();
        stats_.add(builderForValue.build());
        onChanged();
      } else {
        statsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public Builder addStats(
        int index, road.data.proto.TrafficFlowStat.Builder builderForValue) {
      if (statsBuilder_ == null) {
        ensureStatsIsMutable();
        stats_.add(index, builderForValue.build());
        onChanged();
      } else {
        statsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public Builder addAllStats(
        java.lang.Iterable<? extends road.data.proto.TrafficFlowStat> values) {
      if (statsBuilder_ == null) {
        ensureStatsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, stats_);
        onChanged();
      } else {
        statsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public Builder clearStats() {
      if (statsBuilder_ == null) {
        stats_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        statsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public Builder removeStats(int index) {
      if (statsBuilder_ == null) {
        ensureStatsIsMutable();
        stats_.remove(index);
        onChanged();
      } else {
        statsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public road.data.proto.TrafficFlowStat.Builder getStatsBuilder(
        int index) {
      return getStatsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public road.data.proto.TrafficFlowStatOrBuilder getStatsOrBuilder(
        int index) {
      if (statsBuilder_ == null) {
        return stats_.get(index);  } else {
        return statsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public java.util.List<? extends road.data.proto.TrafficFlowStatOrBuilder> 
         getStatsOrBuilderList() {
      if (statsBuilder_ != null) {
        return statsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(stats_);
      }
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public road.data.proto.TrafficFlowStat.Builder addStatsBuilder() {
      return getStatsFieldBuilder().addBuilder(
          road.data.proto.TrafficFlowStat.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public road.data.proto.TrafficFlowStat.Builder addStatsBuilder(
        int index) {
      return getStatsFieldBuilder().addBuilder(
          index, road.data.proto.TrafficFlowStat.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
     */
    public java.util.List<road.data.proto.TrafficFlowStat.Builder> 
         getStatsBuilderList() {
      return getStatsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.TrafficFlowStat, road.data.proto.TrafficFlowStat.Builder, road.data.proto.TrafficFlowStatOrBuilder> 
        getStatsFieldBuilder() {
      if (statsBuilder_ == null) {
        statsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.TrafficFlowStat, road.data.proto.TrafficFlowStat.Builder, road.data.proto.TrafficFlowStatOrBuilder>(
                stats_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        stats_ = null;
      }
      return statsBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.TrafficFlow)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.TrafficFlow)
  private static final road.data.proto.TrafficFlow DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.TrafficFlow();
  }

  public static road.data.proto.TrafficFlow getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TrafficFlow>
      PARSER = new com.google.protobuf.AbstractParser<TrafficFlow>() {
    @java.lang.Override
    public TrafficFlow parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new TrafficFlow(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<TrafficFlow> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TrafficFlow> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.TrafficFlow getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

