// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *扩展交通流指标  
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.TrafficFlowExtension}
 */
public  final class TrafficFlowExtension extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.TrafficFlowExtension)
    TrafficFlowExtensionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TrafficFlowExtension.newBuilder() to construct.
  private TrafficFlowExtension(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private TrafficFlowExtension() {
    laneIndex_ = java.util.Collections.emptyList();
    linkIndex_ = java.util.Collections.emptyList();
    movementIndex_ = java.util.Collections.emptyList();
    nodeIndex_ = java.util.Collections.emptyList();
    signalIndex_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new TrafficFlowExtension();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private TrafficFlowExtension(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              laneIndex_ = new java.util.ArrayList<road.data.proto.LaneIndexAdded>();
              mutable_bitField0_ |= 0x00000001;
            }
            laneIndex_.add(
                input.readMessage(road.data.proto.LaneIndexAdded.parser(), extensionRegistry));
            break;
          }
          case 18: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              linkIndex_ = new java.util.ArrayList<road.data.proto.LinkIndexAdded>();
              mutable_bitField0_ |= 0x00000002;
            }
            linkIndex_.add(
                input.readMessage(road.data.proto.LinkIndexAdded.parser(), extensionRegistry));
            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000004) != 0)) {
              movementIndex_ = new java.util.ArrayList<road.data.proto.MovementIndexAdded>();
              mutable_bitField0_ |= 0x00000004;
            }
            movementIndex_.add(
                input.readMessage(road.data.proto.MovementIndexAdded.parser(), extensionRegistry));
            break;
          }
          case 34: {
            if (!((mutable_bitField0_ & 0x00000008) != 0)) {
              nodeIndex_ = new java.util.ArrayList<road.data.proto.NodeIndexAdded>();
              mutable_bitField0_ |= 0x00000008;
            }
            nodeIndex_.add(
                input.readMessage(road.data.proto.NodeIndexAdded.parser(), extensionRegistry));
            break;
          }
          case 42: {
            if (!((mutable_bitField0_ & 0x00000010) != 0)) {
              signalIndex_ = new java.util.ArrayList<road.data.proto.SignalControlIndexAdded>();
              mutable_bitField0_ |= 0x00000010;
            }
            signalIndex_.add(
                input.readMessage(road.data.proto.SignalControlIndexAdded.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        laneIndex_ = java.util.Collections.unmodifiableList(laneIndex_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        linkIndex_ = java.util.Collections.unmodifiableList(linkIndex_);
      }
      if (((mutable_bitField0_ & 0x00000004) != 0)) {
        movementIndex_ = java.util.Collections.unmodifiableList(movementIndex_);
      }
      if (((mutable_bitField0_ & 0x00000008) != 0)) {
        nodeIndex_ = java.util.Collections.unmodifiableList(nodeIndex_);
      }
      if (((mutable_bitField0_ & 0x00000010) != 0)) {
        signalIndex_ = java.util.Collections.unmodifiableList(signalIndex_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowExtension_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowExtension_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.TrafficFlowExtension.class, road.data.proto.TrafficFlowExtension.Builder.class);
  }

  public static final int LANEINDEX_FIELD_NUMBER = 1;
  private java.util.List<road.data.proto.LaneIndexAdded> laneIndex_;
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
   */
  public java.util.List<road.data.proto.LaneIndexAdded> getLaneIndexList() {
    return laneIndex_;
  }
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
   */
  public java.util.List<? extends road.data.proto.LaneIndexAddedOrBuilder> 
      getLaneIndexOrBuilderList() {
    return laneIndex_;
  }
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
   */
  public int getLaneIndexCount() {
    return laneIndex_.size();
  }
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
   */
  public road.data.proto.LaneIndexAdded getLaneIndex(int index) {
    return laneIndex_.get(index);
  }
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
   */
  public road.data.proto.LaneIndexAddedOrBuilder getLaneIndexOrBuilder(
      int index) {
    return laneIndex_.get(index);
  }

  public static final int LINKINDEX_FIELD_NUMBER = 2;
  private java.util.List<road.data.proto.LinkIndexAdded> linkIndex_;
  /**
   * <pre>
   * 可选，除通用交通指标之外的进口道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
   */
  public java.util.List<road.data.proto.LinkIndexAdded> getLinkIndexList() {
    return linkIndex_;
  }
  /**
   * <pre>
   * 可选，除通用交通指标之外的进口道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
   */
  public java.util.List<? extends road.data.proto.LinkIndexAddedOrBuilder> 
      getLinkIndexOrBuilderList() {
    return linkIndex_;
  }
  /**
   * <pre>
   * 可选，除通用交通指标之外的进口道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
   */
  public int getLinkIndexCount() {
    return linkIndex_.size();
  }
  /**
   * <pre>
   * 可选，除通用交通指标之外的进口道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
   */
  public road.data.proto.LinkIndexAdded getLinkIndex(int index) {
    return linkIndex_.get(index);
  }
  /**
   * <pre>
   * 可选，除通用交通指标之外的进口道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
   */
  public road.data.proto.LinkIndexAddedOrBuilder getLinkIndexOrBuilder(
      int index) {
    return linkIndex_.get(index);
  }

  public static final int MOVEMENTINDEX_FIELD_NUMBER = 3;
  private java.util.List<road.data.proto.MovementIndexAdded> movementIndex_;
  /**
   * <pre>
   *可选，除通用交通指标之外的转向级交通指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
   */
  public java.util.List<road.data.proto.MovementIndexAdded> getMovementIndexList() {
    return movementIndex_;
  }
  /**
   * <pre>
   *可选，除通用交通指标之外的转向级交通指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
   */
  public java.util.List<? extends road.data.proto.MovementIndexAddedOrBuilder> 
      getMovementIndexOrBuilderList() {
    return movementIndex_;
  }
  /**
   * <pre>
   *可选，除通用交通指标之外的转向级交通指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
   */
  public int getMovementIndexCount() {
    return movementIndex_.size();
  }
  /**
   * <pre>
   *可选，除通用交通指标之外的转向级交通指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
   */
  public road.data.proto.MovementIndexAdded getMovementIndex(int index) {
    return movementIndex_.get(index);
  }
  /**
   * <pre>
   *可选，除通用交通指标之外的转向级交通指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
   */
  public road.data.proto.MovementIndexAddedOrBuilder getMovementIndexOrBuilder(
      int index) {
    return movementIndex_.get(index);
  }

  public static final int NODEINDEX_FIELD_NUMBER = 4;
  private java.util.List<road.data.proto.NodeIndexAdded> nodeIndex_;
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级路口级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
   */
  public java.util.List<road.data.proto.NodeIndexAdded> getNodeIndexList() {
    return nodeIndex_;
  }
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级路口级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
   */
  public java.util.List<? extends road.data.proto.NodeIndexAddedOrBuilder> 
      getNodeIndexOrBuilderList() {
    return nodeIndex_;
  }
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级路口级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
   */
  public int getNodeIndexCount() {
    return nodeIndex_.size();
  }
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级路口级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
   */
  public road.data.proto.NodeIndexAdded getNodeIndex(int index) {
    return nodeIndex_.get(index);
  }
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级路口级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
   */
  public road.data.proto.NodeIndexAddedOrBuilder getNodeIndexOrBuilder(
      int index) {
    return nodeIndex_.get(index);
  }

  public static final int SIGNALINDEX_FIELD_NUMBER = 5;
  private java.util.List<road.data.proto.SignalControlIndexAdded> signalIndex_;
  /**
   * <pre>
   *可选，交叉口信控评价的可选拓展统计指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
   */
  public java.util.List<road.data.proto.SignalControlIndexAdded> getSignalIndexList() {
    return signalIndex_;
  }
  /**
   * <pre>
   *可选，交叉口信控评价的可选拓展统计指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
   */
  public java.util.List<? extends road.data.proto.SignalControlIndexAddedOrBuilder> 
      getSignalIndexOrBuilderList() {
    return signalIndex_;
  }
  /**
   * <pre>
   *可选，交叉口信控评价的可选拓展统计指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
   */
  public int getSignalIndexCount() {
    return signalIndex_.size();
  }
  /**
   * <pre>
   *可选，交叉口信控评价的可选拓展统计指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
   */
  public road.data.proto.SignalControlIndexAdded getSignalIndex(int index) {
    return signalIndex_.get(index);
  }
  /**
   * <pre>
   *可选，交叉口信控评价的可选拓展统计指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
   */
  public road.data.proto.SignalControlIndexAddedOrBuilder getSignalIndexOrBuilder(
      int index) {
    return signalIndex_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < laneIndex_.size(); i++) {
      output.writeMessage(1, laneIndex_.get(i));
    }
    for (int i = 0; i < linkIndex_.size(); i++) {
      output.writeMessage(2, linkIndex_.get(i));
    }
    for (int i = 0; i < movementIndex_.size(); i++) {
      output.writeMessage(3, movementIndex_.get(i));
    }
    for (int i = 0; i < nodeIndex_.size(); i++) {
      output.writeMessage(4, nodeIndex_.get(i));
    }
    for (int i = 0; i < signalIndex_.size(); i++) {
      output.writeMessage(5, signalIndex_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < laneIndex_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, laneIndex_.get(i));
    }
    for (int i = 0; i < linkIndex_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, linkIndex_.get(i));
    }
    for (int i = 0; i < movementIndex_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, movementIndex_.get(i));
    }
    for (int i = 0; i < nodeIndex_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, nodeIndex_.get(i));
    }
    for (int i = 0; i < signalIndex_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, signalIndex_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.TrafficFlowExtension)) {
      return super.equals(obj);
    }
    road.data.proto.TrafficFlowExtension other = (road.data.proto.TrafficFlowExtension) obj;

    if (!getLaneIndexList()
        .equals(other.getLaneIndexList())) return false;
    if (!getLinkIndexList()
        .equals(other.getLinkIndexList())) return false;
    if (!getMovementIndexList()
        .equals(other.getMovementIndexList())) return false;
    if (!getNodeIndexList()
        .equals(other.getNodeIndexList())) return false;
    if (!getSignalIndexList()
        .equals(other.getSignalIndexList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getLaneIndexCount() > 0) {
      hash = (37 * hash) + LANEINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getLaneIndexList().hashCode();
    }
    if (getLinkIndexCount() > 0) {
      hash = (37 * hash) + LINKINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getLinkIndexList().hashCode();
    }
    if (getMovementIndexCount() > 0) {
      hash = (37 * hash) + MOVEMENTINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getMovementIndexList().hashCode();
    }
    if (getNodeIndexCount() > 0) {
      hash = (37 * hash) + NODEINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getNodeIndexList().hashCode();
    }
    if (getSignalIndexCount() > 0) {
      hash = (37 * hash) + SIGNALINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getSignalIndexList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.TrafficFlowExtension parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowExtension parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowExtension parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowExtension parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowExtension parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowExtension parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowExtension parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowExtension parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowExtension parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowExtension parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowExtension parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowExtension parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.TrafficFlowExtension prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *扩展交通流指标  
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.TrafficFlowExtension}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.TrafficFlowExtension)
      road.data.proto.TrafficFlowExtensionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowExtension_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowExtension_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.TrafficFlowExtension.class, road.data.proto.TrafficFlowExtension.Builder.class);
    }

    // Construct using road.data.proto.TrafficFlowExtension.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getLaneIndexFieldBuilder();
        getLinkIndexFieldBuilder();
        getMovementIndexFieldBuilder();
        getNodeIndexFieldBuilder();
        getSignalIndexFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (laneIndexBuilder_ == null) {
        laneIndex_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        laneIndexBuilder_.clear();
      }
      if (linkIndexBuilder_ == null) {
        linkIndex_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        linkIndexBuilder_.clear();
      }
      if (movementIndexBuilder_ == null) {
        movementIndex_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
      } else {
        movementIndexBuilder_.clear();
      }
      if (nodeIndexBuilder_ == null) {
        nodeIndex_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
      } else {
        nodeIndexBuilder_.clear();
      }
      if (signalIndexBuilder_ == null) {
        signalIndex_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
      } else {
        signalIndexBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowExtension_descriptor;
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowExtension getDefaultInstanceForType() {
      return road.data.proto.TrafficFlowExtension.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowExtension build() {
      road.data.proto.TrafficFlowExtension result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowExtension buildPartial() {
      road.data.proto.TrafficFlowExtension result = new road.data.proto.TrafficFlowExtension(this);
      int from_bitField0_ = bitField0_;
      if (laneIndexBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          laneIndex_ = java.util.Collections.unmodifiableList(laneIndex_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.laneIndex_ = laneIndex_;
      } else {
        result.laneIndex_ = laneIndexBuilder_.build();
      }
      if (linkIndexBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          linkIndex_ = java.util.Collections.unmodifiableList(linkIndex_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.linkIndex_ = linkIndex_;
      } else {
        result.linkIndex_ = linkIndexBuilder_.build();
      }
      if (movementIndexBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          movementIndex_ = java.util.Collections.unmodifiableList(movementIndex_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.movementIndex_ = movementIndex_;
      } else {
        result.movementIndex_ = movementIndexBuilder_.build();
      }
      if (nodeIndexBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          nodeIndex_ = java.util.Collections.unmodifiableList(nodeIndex_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.nodeIndex_ = nodeIndex_;
      } else {
        result.nodeIndex_ = nodeIndexBuilder_.build();
      }
      if (signalIndexBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          signalIndex_ = java.util.Collections.unmodifiableList(signalIndex_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.signalIndex_ = signalIndex_;
      } else {
        result.signalIndex_ = signalIndexBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.TrafficFlowExtension) {
        return mergeFrom((road.data.proto.TrafficFlowExtension)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.TrafficFlowExtension other) {
      if (other == road.data.proto.TrafficFlowExtension.getDefaultInstance()) return this;
      if (laneIndexBuilder_ == null) {
        if (!other.laneIndex_.isEmpty()) {
          if (laneIndex_.isEmpty()) {
            laneIndex_ = other.laneIndex_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureLaneIndexIsMutable();
            laneIndex_.addAll(other.laneIndex_);
          }
          onChanged();
        }
      } else {
        if (!other.laneIndex_.isEmpty()) {
          if (laneIndexBuilder_.isEmpty()) {
            laneIndexBuilder_.dispose();
            laneIndexBuilder_ = null;
            laneIndex_ = other.laneIndex_;
            bitField0_ = (bitField0_ & ~0x00000001);
            laneIndexBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getLaneIndexFieldBuilder() : null;
          } else {
            laneIndexBuilder_.addAllMessages(other.laneIndex_);
          }
        }
      }
      if (linkIndexBuilder_ == null) {
        if (!other.linkIndex_.isEmpty()) {
          if (linkIndex_.isEmpty()) {
            linkIndex_ = other.linkIndex_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureLinkIndexIsMutable();
            linkIndex_.addAll(other.linkIndex_);
          }
          onChanged();
        }
      } else {
        if (!other.linkIndex_.isEmpty()) {
          if (linkIndexBuilder_.isEmpty()) {
            linkIndexBuilder_.dispose();
            linkIndexBuilder_ = null;
            linkIndex_ = other.linkIndex_;
            bitField0_ = (bitField0_ & ~0x00000002);
            linkIndexBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getLinkIndexFieldBuilder() : null;
          } else {
            linkIndexBuilder_.addAllMessages(other.linkIndex_);
          }
        }
      }
      if (movementIndexBuilder_ == null) {
        if (!other.movementIndex_.isEmpty()) {
          if (movementIndex_.isEmpty()) {
            movementIndex_ = other.movementIndex_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureMovementIndexIsMutable();
            movementIndex_.addAll(other.movementIndex_);
          }
          onChanged();
        }
      } else {
        if (!other.movementIndex_.isEmpty()) {
          if (movementIndexBuilder_.isEmpty()) {
            movementIndexBuilder_.dispose();
            movementIndexBuilder_ = null;
            movementIndex_ = other.movementIndex_;
            bitField0_ = (bitField0_ & ~0x00000004);
            movementIndexBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getMovementIndexFieldBuilder() : null;
          } else {
            movementIndexBuilder_.addAllMessages(other.movementIndex_);
          }
        }
      }
      if (nodeIndexBuilder_ == null) {
        if (!other.nodeIndex_.isEmpty()) {
          if (nodeIndex_.isEmpty()) {
            nodeIndex_ = other.nodeIndex_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureNodeIndexIsMutable();
            nodeIndex_.addAll(other.nodeIndex_);
          }
          onChanged();
        }
      } else {
        if (!other.nodeIndex_.isEmpty()) {
          if (nodeIndexBuilder_.isEmpty()) {
            nodeIndexBuilder_.dispose();
            nodeIndexBuilder_ = null;
            nodeIndex_ = other.nodeIndex_;
            bitField0_ = (bitField0_ & ~0x00000008);
            nodeIndexBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getNodeIndexFieldBuilder() : null;
          } else {
            nodeIndexBuilder_.addAllMessages(other.nodeIndex_);
          }
        }
      }
      if (signalIndexBuilder_ == null) {
        if (!other.signalIndex_.isEmpty()) {
          if (signalIndex_.isEmpty()) {
            signalIndex_ = other.signalIndex_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureSignalIndexIsMutable();
            signalIndex_.addAll(other.signalIndex_);
          }
          onChanged();
        }
      } else {
        if (!other.signalIndex_.isEmpty()) {
          if (signalIndexBuilder_.isEmpty()) {
            signalIndexBuilder_.dispose();
            signalIndexBuilder_ = null;
            signalIndex_ = other.signalIndex_;
            bitField0_ = (bitField0_ & ~0x00000010);
            signalIndexBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getSignalIndexFieldBuilder() : null;
          } else {
            signalIndexBuilder_.addAllMessages(other.signalIndex_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.TrafficFlowExtension parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.TrafficFlowExtension) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.util.List<road.data.proto.LaneIndexAdded> laneIndex_ =
      java.util.Collections.emptyList();
    private void ensureLaneIndexIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        laneIndex_ = new java.util.ArrayList<road.data.proto.LaneIndexAdded>(laneIndex_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.LaneIndexAdded, road.data.proto.LaneIndexAdded.Builder, road.data.proto.LaneIndexAddedOrBuilder> laneIndexBuilder_;

    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public java.util.List<road.data.proto.LaneIndexAdded> getLaneIndexList() {
      if (laneIndexBuilder_ == null) {
        return java.util.Collections.unmodifiableList(laneIndex_);
      } else {
        return laneIndexBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public int getLaneIndexCount() {
      if (laneIndexBuilder_ == null) {
        return laneIndex_.size();
      } else {
        return laneIndexBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public road.data.proto.LaneIndexAdded getLaneIndex(int index) {
      if (laneIndexBuilder_ == null) {
        return laneIndex_.get(index);
      } else {
        return laneIndexBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public Builder setLaneIndex(
        int index, road.data.proto.LaneIndexAdded value) {
      if (laneIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLaneIndexIsMutable();
        laneIndex_.set(index, value);
        onChanged();
      } else {
        laneIndexBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public Builder setLaneIndex(
        int index, road.data.proto.LaneIndexAdded.Builder builderForValue) {
      if (laneIndexBuilder_ == null) {
        ensureLaneIndexIsMutable();
        laneIndex_.set(index, builderForValue.build());
        onChanged();
      } else {
        laneIndexBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public Builder addLaneIndex(road.data.proto.LaneIndexAdded value) {
      if (laneIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLaneIndexIsMutable();
        laneIndex_.add(value);
        onChanged();
      } else {
        laneIndexBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public Builder addLaneIndex(
        int index, road.data.proto.LaneIndexAdded value) {
      if (laneIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLaneIndexIsMutable();
        laneIndex_.add(index, value);
        onChanged();
      } else {
        laneIndexBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public Builder addLaneIndex(
        road.data.proto.LaneIndexAdded.Builder builderForValue) {
      if (laneIndexBuilder_ == null) {
        ensureLaneIndexIsMutable();
        laneIndex_.add(builderForValue.build());
        onChanged();
      } else {
        laneIndexBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public Builder addLaneIndex(
        int index, road.data.proto.LaneIndexAdded.Builder builderForValue) {
      if (laneIndexBuilder_ == null) {
        ensureLaneIndexIsMutable();
        laneIndex_.add(index, builderForValue.build());
        onChanged();
      } else {
        laneIndexBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public Builder addAllLaneIndex(
        java.lang.Iterable<? extends road.data.proto.LaneIndexAdded> values) {
      if (laneIndexBuilder_ == null) {
        ensureLaneIndexIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, laneIndex_);
        onChanged();
      } else {
        laneIndexBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public Builder clearLaneIndex() {
      if (laneIndexBuilder_ == null) {
        laneIndex_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        laneIndexBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public Builder removeLaneIndex(int index) {
      if (laneIndexBuilder_ == null) {
        ensureLaneIndexIsMutable();
        laneIndex_.remove(index);
        onChanged();
      } else {
        laneIndexBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public road.data.proto.LaneIndexAdded.Builder getLaneIndexBuilder(
        int index) {
      return getLaneIndexFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public road.data.proto.LaneIndexAddedOrBuilder getLaneIndexOrBuilder(
        int index) {
      if (laneIndexBuilder_ == null) {
        return laneIndex_.get(index);  } else {
        return laneIndexBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public java.util.List<? extends road.data.proto.LaneIndexAddedOrBuilder> 
         getLaneIndexOrBuilderList() {
      if (laneIndexBuilder_ != null) {
        return laneIndexBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(laneIndex_);
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public road.data.proto.LaneIndexAdded.Builder addLaneIndexBuilder() {
      return getLaneIndexFieldBuilder().addBuilder(
          road.data.proto.LaneIndexAdded.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public road.data.proto.LaneIndexAdded.Builder addLaneIndexBuilder(
        int index) {
      return getLaneIndexFieldBuilder().addBuilder(
          index, road.data.proto.LaneIndexAdded.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
     */
    public java.util.List<road.data.proto.LaneIndexAdded.Builder> 
         getLaneIndexBuilderList() {
      return getLaneIndexFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.LaneIndexAdded, road.data.proto.LaneIndexAdded.Builder, road.data.proto.LaneIndexAddedOrBuilder> 
        getLaneIndexFieldBuilder() {
      if (laneIndexBuilder_ == null) {
        laneIndexBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.LaneIndexAdded, road.data.proto.LaneIndexAdded.Builder, road.data.proto.LaneIndexAddedOrBuilder>(
                laneIndex_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        laneIndex_ = null;
      }
      return laneIndexBuilder_;
    }

    private java.util.List<road.data.proto.LinkIndexAdded> linkIndex_ =
      java.util.Collections.emptyList();
    private void ensureLinkIndexIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        linkIndex_ = new java.util.ArrayList<road.data.proto.LinkIndexAdded>(linkIndex_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.LinkIndexAdded, road.data.proto.LinkIndexAdded.Builder, road.data.proto.LinkIndexAddedOrBuilder> linkIndexBuilder_;

    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public java.util.List<road.data.proto.LinkIndexAdded> getLinkIndexList() {
      if (linkIndexBuilder_ == null) {
        return java.util.Collections.unmodifiableList(linkIndex_);
      } else {
        return linkIndexBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public int getLinkIndexCount() {
      if (linkIndexBuilder_ == null) {
        return linkIndex_.size();
      } else {
        return linkIndexBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public road.data.proto.LinkIndexAdded getLinkIndex(int index) {
      if (linkIndexBuilder_ == null) {
        return linkIndex_.get(index);
      } else {
        return linkIndexBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public Builder setLinkIndex(
        int index, road.data.proto.LinkIndexAdded value) {
      if (linkIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLinkIndexIsMutable();
        linkIndex_.set(index, value);
        onChanged();
      } else {
        linkIndexBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public Builder setLinkIndex(
        int index, road.data.proto.LinkIndexAdded.Builder builderForValue) {
      if (linkIndexBuilder_ == null) {
        ensureLinkIndexIsMutable();
        linkIndex_.set(index, builderForValue.build());
        onChanged();
      } else {
        linkIndexBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public Builder addLinkIndex(road.data.proto.LinkIndexAdded value) {
      if (linkIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLinkIndexIsMutable();
        linkIndex_.add(value);
        onChanged();
      } else {
        linkIndexBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public Builder addLinkIndex(
        int index, road.data.proto.LinkIndexAdded value) {
      if (linkIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLinkIndexIsMutable();
        linkIndex_.add(index, value);
        onChanged();
      } else {
        linkIndexBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public Builder addLinkIndex(
        road.data.proto.LinkIndexAdded.Builder builderForValue) {
      if (linkIndexBuilder_ == null) {
        ensureLinkIndexIsMutable();
        linkIndex_.add(builderForValue.build());
        onChanged();
      } else {
        linkIndexBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public Builder addLinkIndex(
        int index, road.data.proto.LinkIndexAdded.Builder builderForValue) {
      if (linkIndexBuilder_ == null) {
        ensureLinkIndexIsMutable();
        linkIndex_.add(index, builderForValue.build());
        onChanged();
      } else {
        linkIndexBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public Builder addAllLinkIndex(
        java.lang.Iterable<? extends road.data.proto.LinkIndexAdded> values) {
      if (linkIndexBuilder_ == null) {
        ensureLinkIndexIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, linkIndex_);
        onChanged();
      } else {
        linkIndexBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public Builder clearLinkIndex() {
      if (linkIndexBuilder_ == null) {
        linkIndex_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        linkIndexBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public Builder removeLinkIndex(int index) {
      if (linkIndexBuilder_ == null) {
        ensureLinkIndexIsMutable();
        linkIndex_.remove(index);
        onChanged();
      } else {
        linkIndexBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public road.data.proto.LinkIndexAdded.Builder getLinkIndexBuilder(
        int index) {
      return getLinkIndexFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public road.data.proto.LinkIndexAddedOrBuilder getLinkIndexOrBuilder(
        int index) {
      if (linkIndexBuilder_ == null) {
        return linkIndex_.get(index);  } else {
        return linkIndexBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public java.util.List<? extends road.data.proto.LinkIndexAddedOrBuilder> 
         getLinkIndexOrBuilderList() {
      if (linkIndexBuilder_ != null) {
        return linkIndexBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(linkIndex_);
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public road.data.proto.LinkIndexAdded.Builder addLinkIndexBuilder() {
      return getLinkIndexFieldBuilder().addBuilder(
          road.data.proto.LinkIndexAdded.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public road.data.proto.LinkIndexAdded.Builder addLinkIndexBuilder(
        int index) {
      return getLinkIndexFieldBuilder().addBuilder(
          index, road.data.proto.LinkIndexAdded.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的进口道级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
     */
    public java.util.List<road.data.proto.LinkIndexAdded.Builder> 
         getLinkIndexBuilderList() {
      return getLinkIndexFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.LinkIndexAdded, road.data.proto.LinkIndexAdded.Builder, road.data.proto.LinkIndexAddedOrBuilder> 
        getLinkIndexFieldBuilder() {
      if (linkIndexBuilder_ == null) {
        linkIndexBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.LinkIndexAdded, road.data.proto.LinkIndexAdded.Builder, road.data.proto.LinkIndexAddedOrBuilder>(
                linkIndex_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        linkIndex_ = null;
      }
      return linkIndexBuilder_;
    }

    private java.util.List<road.data.proto.MovementIndexAdded> movementIndex_ =
      java.util.Collections.emptyList();
    private void ensureMovementIndexIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        movementIndex_ = new java.util.ArrayList<road.data.proto.MovementIndexAdded>(movementIndex_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.MovementIndexAdded, road.data.proto.MovementIndexAdded.Builder, road.data.proto.MovementIndexAddedOrBuilder> movementIndexBuilder_;

    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public java.util.List<road.data.proto.MovementIndexAdded> getMovementIndexList() {
      if (movementIndexBuilder_ == null) {
        return java.util.Collections.unmodifiableList(movementIndex_);
      } else {
        return movementIndexBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public int getMovementIndexCount() {
      if (movementIndexBuilder_ == null) {
        return movementIndex_.size();
      } else {
        return movementIndexBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public road.data.proto.MovementIndexAdded getMovementIndex(int index) {
      if (movementIndexBuilder_ == null) {
        return movementIndex_.get(index);
      } else {
        return movementIndexBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public Builder setMovementIndex(
        int index, road.data.proto.MovementIndexAdded value) {
      if (movementIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMovementIndexIsMutable();
        movementIndex_.set(index, value);
        onChanged();
      } else {
        movementIndexBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public Builder setMovementIndex(
        int index, road.data.proto.MovementIndexAdded.Builder builderForValue) {
      if (movementIndexBuilder_ == null) {
        ensureMovementIndexIsMutable();
        movementIndex_.set(index, builderForValue.build());
        onChanged();
      } else {
        movementIndexBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public Builder addMovementIndex(road.data.proto.MovementIndexAdded value) {
      if (movementIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMovementIndexIsMutable();
        movementIndex_.add(value);
        onChanged();
      } else {
        movementIndexBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public Builder addMovementIndex(
        int index, road.data.proto.MovementIndexAdded value) {
      if (movementIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMovementIndexIsMutable();
        movementIndex_.add(index, value);
        onChanged();
      } else {
        movementIndexBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public Builder addMovementIndex(
        road.data.proto.MovementIndexAdded.Builder builderForValue) {
      if (movementIndexBuilder_ == null) {
        ensureMovementIndexIsMutable();
        movementIndex_.add(builderForValue.build());
        onChanged();
      } else {
        movementIndexBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public Builder addMovementIndex(
        int index, road.data.proto.MovementIndexAdded.Builder builderForValue) {
      if (movementIndexBuilder_ == null) {
        ensureMovementIndexIsMutable();
        movementIndex_.add(index, builderForValue.build());
        onChanged();
      } else {
        movementIndexBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public Builder addAllMovementIndex(
        java.lang.Iterable<? extends road.data.proto.MovementIndexAdded> values) {
      if (movementIndexBuilder_ == null) {
        ensureMovementIndexIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, movementIndex_);
        onChanged();
      } else {
        movementIndexBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public Builder clearMovementIndex() {
      if (movementIndexBuilder_ == null) {
        movementIndex_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        movementIndexBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public Builder removeMovementIndex(int index) {
      if (movementIndexBuilder_ == null) {
        ensureMovementIndexIsMutable();
        movementIndex_.remove(index);
        onChanged();
      } else {
        movementIndexBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public road.data.proto.MovementIndexAdded.Builder getMovementIndexBuilder(
        int index) {
      return getMovementIndexFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public road.data.proto.MovementIndexAddedOrBuilder getMovementIndexOrBuilder(
        int index) {
      if (movementIndexBuilder_ == null) {
        return movementIndex_.get(index);  } else {
        return movementIndexBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public java.util.List<? extends road.data.proto.MovementIndexAddedOrBuilder> 
         getMovementIndexOrBuilderList() {
      if (movementIndexBuilder_ != null) {
        return movementIndexBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(movementIndex_);
      }
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public road.data.proto.MovementIndexAdded.Builder addMovementIndexBuilder() {
      return getMovementIndexFieldBuilder().addBuilder(
          road.data.proto.MovementIndexAdded.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public road.data.proto.MovementIndexAdded.Builder addMovementIndexBuilder(
        int index) {
      return getMovementIndexFieldBuilder().addBuilder(
          index, road.data.proto.MovementIndexAdded.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，除通用交通指标之外的转向级交通指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
     */
    public java.util.List<road.data.proto.MovementIndexAdded.Builder> 
         getMovementIndexBuilderList() {
      return getMovementIndexFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.MovementIndexAdded, road.data.proto.MovementIndexAdded.Builder, road.data.proto.MovementIndexAddedOrBuilder> 
        getMovementIndexFieldBuilder() {
      if (movementIndexBuilder_ == null) {
        movementIndexBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.MovementIndexAdded, road.data.proto.MovementIndexAdded.Builder, road.data.proto.MovementIndexAddedOrBuilder>(
                movementIndex_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        movementIndex_ = null;
      }
      return movementIndexBuilder_;
    }

    private java.util.List<road.data.proto.NodeIndexAdded> nodeIndex_ =
      java.util.Collections.emptyList();
    private void ensureNodeIndexIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        nodeIndex_ = new java.util.ArrayList<road.data.proto.NodeIndexAdded>(nodeIndex_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.NodeIndexAdded, road.data.proto.NodeIndexAdded.Builder, road.data.proto.NodeIndexAddedOrBuilder> nodeIndexBuilder_;

    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public java.util.List<road.data.proto.NodeIndexAdded> getNodeIndexList() {
      if (nodeIndexBuilder_ == null) {
        return java.util.Collections.unmodifiableList(nodeIndex_);
      } else {
        return nodeIndexBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public int getNodeIndexCount() {
      if (nodeIndexBuilder_ == null) {
        return nodeIndex_.size();
      } else {
        return nodeIndexBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public road.data.proto.NodeIndexAdded getNodeIndex(int index) {
      if (nodeIndexBuilder_ == null) {
        return nodeIndex_.get(index);
      } else {
        return nodeIndexBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public Builder setNodeIndex(
        int index, road.data.proto.NodeIndexAdded value) {
      if (nodeIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNodeIndexIsMutable();
        nodeIndex_.set(index, value);
        onChanged();
      } else {
        nodeIndexBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public Builder setNodeIndex(
        int index, road.data.proto.NodeIndexAdded.Builder builderForValue) {
      if (nodeIndexBuilder_ == null) {
        ensureNodeIndexIsMutable();
        nodeIndex_.set(index, builderForValue.build());
        onChanged();
      } else {
        nodeIndexBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public Builder addNodeIndex(road.data.proto.NodeIndexAdded value) {
      if (nodeIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNodeIndexIsMutable();
        nodeIndex_.add(value);
        onChanged();
      } else {
        nodeIndexBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public Builder addNodeIndex(
        int index, road.data.proto.NodeIndexAdded value) {
      if (nodeIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNodeIndexIsMutable();
        nodeIndex_.add(index, value);
        onChanged();
      } else {
        nodeIndexBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public Builder addNodeIndex(
        road.data.proto.NodeIndexAdded.Builder builderForValue) {
      if (nodeIndexBuilder_ == null) {
        ensureNodeIndexIsMutable();
        nodeIndex_.add(builderForValue.build());
        onChanged();
      } else {
        nodeIndexBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public Builder addNodeIndex(
        int index, road.data.proto.NodeIndexAdded.Builder builderForValue) {
      if (nodeIndexBuilder_ == null) {
        ensureNodeIndexIsMutable();
        nodeIndex_.add(index, builderForValue.build());
        onChanged();
      } else {
        nodeIndexBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public Builder addAllNodeIndex(
        java.lang.Iterable<? extends road.data.proto.NodeIndexAdded> values) {
      if (nodeIndexBuilder_ == null) {
        ensureNodeIndexIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, nodeIndex_);
        onChanged();
      } else {
        nodeIndexBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public Builder clearNodeIndex() {
      if (nodeIndexBuilder_ == null) {
        nodeIndex_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        nodeIndexBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public Builder removeNodeIndex(int index) {
      if (nodeIndexBuilder_ == null) {
        ensureNodeIndexIsMutable();
        nodeIndex_.remove(index);
        onChanged();
      } else {
        nodeIndexBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public road.data.proto.NodeIndexAdded.Builder getNodeIndexBuilder(
        int index) {
      return getNodeIndexFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public road.data.proto.NodeIndexAddedOrBuilder getNodeIndexOrBuilder(
        int index) {
      if (nodeIndexBuilder_ == null) {
        return nodeIndex_.get(index);  } else {
        return nodeIndexBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public java.util.List<? extends road.data.proto.NodeIndexAddedOrBuilder> 
         getNodeIndexOrBuilderList() {
      if (nodeIndexBuilder_ != null) {
        return nodeIndexBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(nodeIndex_);
      }
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public road.data.proto.NodeIndexAdded.Builder addNodeIndexBuilder() {
      return getNodeIndexFieldBuilder().addBuilder(
          road.data.proto.NodeIndexAdded.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public road.data.proto.NodeIndexAdded.Builder addNodeIndexBuilder(
        int index) {
      return getNodeIndexFieldBuilder().addBuilder(
          index, road.data.proto.NodeIndexAdded.getDefaultInstance());
    }
    /**
     * <pre>
     * 可选，除通用交通指标之外的车道级路口级交通指标;
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
     */
    public java.util.List<road.data.proto.NodeIndexAdded.Builder> 
         getNodeIndexBuilderList() {
      return getNodeIndexFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.NodeIndexAdded, road.data.proto.NodeIndexAdded.Builder, road.data.proto.NodeIndexAddedOrBuilder> 
        getNodeIndexFieldBuilder() {
      if (nodeIndexBuilder_ == null) {
        nodeIndexBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.NodeIndexAdded, road.data.proto.NodeIndexAdded.Builder, road.data.proto.NodeIndexAddedOrBuilder>(
                nodeIndex_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        nodeIndex_ = null;
      }
      return nodeIndexBuilder_;
    }

    private java.util.List<road.data.proto.SignalControlIndexAdded> signalIndex_ =
      java.util.Collections.emptyList();
    private void ensureSignalIndexIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        signalIndex_ = new java.util.ArrayList<road.data.proto.SignalControlIndexAdded>(signalIndex_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.SignalControlIndexAdded, road.data.proto.SignalControlIndexAdded.Builder, road.data.proto.SignalControlIndexAddedOrBuilder> signalIndexBuilder_;

    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public java.util.List<road.data.proto.SignalControlIndexAdded> getSignalIndexList() {
      if (signalIndexBuilder_ == null) {
        return java.util.Collections.unmodifiableList(signalIndex_);
      } else {
        return signalIndexBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public int getSignalIndexCount() {
      if (signalIndexBuilder_ == null) {
        return signalIndex_.size();
      } else {
        return signalIndexBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public road.data.proto.SignalControlIndexAdded getSignalIndex(int index) {
      if (signalIndexBuilder_ == null) {
        return signalIndex_.get(index);
      } else {
        return signalIndexBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public Builder setSignalIndex(
        int index, road.data.proto.SignalControlIndexAdded value) {
      if (signalIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSignalIndexIsMutable();
        signalIndex_.set(index, value);
        onChanged();
      } else {
        signalIndexBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public Builder setSignalIndex(
        int index, road.data.proto.SignalControlIndexAdded.Builder builderForValue) {
      if (signalIndexBuilder_ == null) {
        ensureSignalIndexIsMutable();
        signalIndex_.set(index, builderForValue.build());
        onChanged();
      } else {
        signalIndexBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public Builder addSignalIndex(road.data.proto.SignalControlIndexAdded value) {
      if (signalIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSignalIndexIsMutable();
        signalIndex_.add(value);
        onChanged();
      } else {
        signalIndexBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public Builder addSignalIndex(
        int index, road.data.proto.SignalControlIndexAdded value) {
      if (signalIndexBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSignalIndexIsMutable();
        signalIndex_.add(index, value);
        onChanged();
      } else {
        signalIndexBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public Builder addSignalIndex(
        road.data.proto.SignalControlIndexAdded.Builder builderForValue) {
      if (signalIndexBuilder_ == null) {
        ensureSignalIndexIsMutable();
        signalIndex_.add(builderForValue.build());
        onChanged();
      } else {
        signalIndexBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public Builder addSignalIndex(
        int index, road.data.proto.SignalControlIndexAdded.Builder builderForValue) {
      if (signalIndexBuilder_ == null) {
        ensureSignalIndexIsMutable();
        signalIndex_.add(index, builderForValue.build());
        onChanged();
      } else {
        signalIndexBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public Builder addAllSignalIndex(
        java.lang.Iterable<? extends road.data.proto.SignalControlIndexAdded> values) {
      if (signalIndexBuilder_ == null) {
        ensureSignalIndexIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, signalIndex_);
        onChanged();
      } else {
        signalIndexBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public Builder clearSignalIndex() {
      if (signalIndexBuilder_ == null) {
        signalIndex_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        signalIndexBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public Builder removeSignalIndex(int index) {
      if (signalIndexBuilder_ == null) {
        ensureSignalIndexIsMutable();
        signalIndex_.remove(index);
        onChanged();
      } else {
        signalIndexBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public road.data.proto.SignalControlIndexAdded.Builder getSignalIndexBuilder(
        int index) {
      return getSignalIndexFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public road.data.proto.SignalControlIndexAddedOrBuilder getSignalIndexOrBuilder(
        int index) {
      if (signalIndexBuilder_ == null) {
        return signalIndex_.get(index);  } else {
        return signalIndexBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public java.util.List<? extends road.data.proto.SignalControlIndexAddedOrBuilder> 
         getSignalIndexOrBuilderList() {
      if (signalIndexBuilder_ != null) {
        return signalIndexBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(signalIndex_);
      }
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public road.data.proto.SignalControlIndexAdded.Builder addSignalIndexBuilder() {
      return getSignalIndexFieldBuilder().addBuilder(
          road.data.proto.SignalControlIndexAdded.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public road.data.proto.SignalControlIndexAdded.Builder addSignalIndexBuilder(
        int index) {
      return getSignalIndexFieldBuilder().addBuilder(
          index, road.data.proto.SignalControlIndexAdded.getDefaultInstance());
    }
    /**
     * <pre>
     *可选，交叉口信控评价的可选拓展统计指标
     * </pre>
     *
     * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
     */
    public java.util.List<road.data.proto.SignalControlIndexAdded.Builder> 
         getSignalIndexBuilderList() {
      return getSignalIndexFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        road.data.proto.SignalControlIndexAdded, road.data.proto.SignalControlIndexAdded.Builder, road.data.proto.SignalControlIndexAddedOrBuilder> 
        getSignalIndexFieldBuilder() {
      if (signalIndexBuilder_ == null) {
        signalIndexBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            road.data.proto.SignalControlIndexAdded, road.data.proto.SignalControlIndexAdded.Builder, road.data.proto.SignalControlIndexAddedOrBuilder>(
                signalIndex_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        signalIndex_ = null;
      }
      return signalIndexBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.TrafficFlowExtension)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.TrafficFlowExtension)
  private static final road.data.proto.TrafficFlowExtension DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.TrafficFlowExtension();
  }

  public static road.data.proto.TrafficFlowExtension getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TrafficFlowExtension>
      PARSER = new com.google.protobuf.AbstractParser<TrafficFlowExtension>() {
    @java.lang.Override
    public TrafficFlowExtension parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new TrafficFlowExtension(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<TrafficFlowExtension> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TrafficFlowExtension> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.TrafficFlowExtension getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

