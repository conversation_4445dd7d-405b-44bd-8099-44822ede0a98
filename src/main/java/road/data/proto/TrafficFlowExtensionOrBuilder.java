// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface TrafficFlowExtensionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.TrafficFlowExtension)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
   */
  java.util.List<road.data.proto.LaneIndexAdded> 
      getLaneIndexList();
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
   */
  road.data.proto.LaneIndexAdded getLaneIndex(int index);
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
   */
  int getLaneIndexCount();
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
   */
  java.util.List<? extends road.data.proto.LaneIndexAddedOrBuilder> 
      getLaneIndexOrBuilderList();
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LaneIndexAdded laneIndex = 1;</code>
   */
  road.data.proto.LaneIndexAddedOrBuilder getLaneIndexOrBuilder(
      int index);

  /**
   * <pre>
   * 可选，除通用交通指标之外的进口道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
   */
  java.util.List<road.data.proto.LinkIndexAdded> 
      getLinkIndexList();
  /**
   * <pre>
   * 可选，除通用交通指标之外的进口道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
   */
  road.data.proto.LinkIndexAdded getLinkIndex(int index);
  /**
   * <pre>
   * 可选，除通用交通指标之外的进口道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
   */
  int getLinkIndexCount();
  /**
   * <pre>
   * 可选，除通用交通指标之外的进口道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
   */
  java.util.List<? extends road.data.proto.LinkIndexAddedOrBuilder> 
      getLinkIndexOrBuilderList();
  /**
   * <pre>
   * 可选，除通用交通指标之外的进口道级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.LinkIndexAdded linkIndex = 2;</code>
   */
  road.data.proto.LinkIndexAddedOrBuilder getLinkIndexOrBuilder(
      int index);

  /**
   * <pre>
   *可选，除通用交通指标之外的转向级交通指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
   */
  java.util.List<road.data.proto.MovementIndexAdded> 
      getMovementIndexList();
  /**
   * <pre>
   *可选，除通用交通指标之外的转向级交通指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
   */
  road.data.proto.MovementIndexAdded getMovementIndex(int index);
  /**
   * <pre>
   *可选，除通用交通指标之外的转向级交通指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
   */
  int getMovementIndexCount();
  /**
   * <pre>
   *可选，除通用交通指标之外的转向级交通指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
   */
  java.util.List<? extends road.data.proto.MovementIndexAddedOrBuilder> 
      getMovementIndexOrBuilderList();
  /**
   * <pre>
   *可选，除通用交通指标之外的转向级交通指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MovementIndexAdded movementIndex = 3;</code>
   */
  road.data.proto.MovementIndexAddedOrBuilder getMovementIndexOrBuilder(
      int index);

  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级路口级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
   */
  java.util.List<road.data.proto.NodeIndexAdded> 
      getNodeIndexList();
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级路口级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
   */
  road.data.proto.NodeIndexAdded getNodeIndex(int index);
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级路口级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
   */
  int getNodeIndexCount();
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级路口级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
   */
  java.util.List<? extends road.data.proto.NodeIndexAddedOrBuilder> 
      getNodeIndexOrBuilderList();
  /**
   * <pre>
   * 可选，除通用交通指标之外的车道级路口级交通指标;
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.NodeIndexAdded nodeIndex = 4;</code>
   */
  road.data.proto.NodeIndexAddedOrBuilder getNodeIndexOrBuilder(
      int index);

  /**
   * <pre>
   *可选，交叉口信控评价的可选拓展统计指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
   */
  java.util.List<road.data.proto.SignalControlIndexAdded> 
      getSignalIndexList();
  /**
   * <pre>
   *可选，交叉口信控评价的可选拓展统计指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
   */
  road.data.proto.SignalControlIndexAdded getSignalIndex(int index);
  /**
   * <pre>
   *可选，交叉口信控评价的可选拓展统计指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
   */
  int getSignalIndexCount();
  /**
   * <pre>
   *可选，交叉口信控评价的可选拓展统计指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
   */
  java.util.List<? extends road.data.proto.SignalControlIndexAddedOrBuilder> 
      getSignalIndexOrBuilderList();
  /**
   * <pre>
   *可选，交叉口信控评价的可选拓展统计指标
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.SignalControlIndexAdded signalIndex = 5;</code>
   */
  road.data.proto.SignalControlIndexAddedOrBuilder getSignalIndexOrBuilder(
      int index);
}
