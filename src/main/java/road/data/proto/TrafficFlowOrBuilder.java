// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface TrafficFlowOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.TrafficFlow)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  boolean hasNodeId();
  /**
   * <pre>
   *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  road.data.proto.NodeReferenceId getNodeId();
  /**
   * <pre>
   *可选，交叉口ID，非城市道路或不与任何交叉口绑定时可不填，在附加到特定节点时设置
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeReferenceId nodeId = 1;</code>
   */
  road.data.proto.NodeReferenceIdOrBuilder getNodeIdOrBuilder();

  /**
   * <pre>
   *可选，消息生成时刻UNIX时间戳（秒级）
   * </pre>
   *
   * <code>uint64 genTime = 2;</code>
   */
  long getGenTime();

  /**
   * <pre>
   * 交通流统计方式信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
   */
  boolean hasStatType();
  /**
   * <pre>
   * 交通流统计方式信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
   */
  road.data.proto.TrafficFlowStatType getStatType();
  /**
   * <pre>
   * 交通流统计方式信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatType statType = 3;</code>
   */
  road.data.proto.TrafficFlowStatTypeOrBuilder getStatTypeOrBuilder();

  /**
   * <pre>
   *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
   */
  java.util.List<road.data.proto.TrafficFlowStat> 
      getStatsList();
  /**
   * <pre>
   *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
   */
  road.data.proto.TrafficFlowStat getStats(int index);
  /**
   * <pre>
   *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
   */
  int getStatsCount();
  /**
   * <pre>
   *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
   */
  java.util.List<? extends road.data.proto.TrafficFlowStatOrBuilder> 
      getStatsOrBuilderList();
  /**
   * <pre>
   *可选，交通流元素统计值合集，用于支持同时上报多组绑定到车道、有向路段、路口对象的统计值
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlowStat stats = 4;</code>
   */
  road.data.proto.TrafficFlowStatOrBuilder getStatsOrBuilder(
      int index);
}
