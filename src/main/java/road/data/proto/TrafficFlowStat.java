// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *单路网元素统计值      
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.TrafficFlowStat}
 */
public  final class TrafficFlowStat extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.TrafficFlowStat)
    TrafficFlowStatOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TrafficFlowStat.newBuilder() to construct.
  private TrafficFlowStat(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private TrafficFlowStat() {
    mapElementType_ = 0;
    ptcType_ = 0;
    vehicleType_ = 0;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new TrafficFlowStat();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private TrafficFlowStat(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.TrafficFlowStatMapElement.Builder subBuilder = null;
            if (mapElement_ != null) {
              subBuilder = mapElement_.toBuilder();
            }
            mapElement_ = input.readMessage(road.data.proto.TrafficFlowStatMapElement.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(mapElement_);
              mapElement_ = subBuilder.buildPartial();
            }

            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            mapElementType_ = rawValue;
            break;
          }
          case 24: {
            int rawValue = input.readEnum();

            ptcType_ = rawValue;
            break;
          }
          case 32: {
            int rawValue = input.readEnum();

            vehicleType_ = rawValue;
            break;
          }
          case 40: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 48: {

            volume_ = input.readUInt32();
            break;
          }
          case 56: {

            speedPoint_ = input.readUInt32();
            break;
          }
          case 64: {

            speedArea_ = input.readUInt32();
            break;
          }
          case 72: {

            density_ = input.readUInt32();
            break;
          }
          case 80: {

            travelTime_ = input.readUInt32();
            break;
          }
          case 88: {

            delay_ = input.readUInt32();
            break;
          }
          case 96: {

            queueLength_ = input.readUInt32();
            break;
          }
          case 104: {

            queueInt_ = input.readUInt32();
            break;
          }
          case 112: {

            congestion_ = input.readUInt32();
            break;
          }
          case 122: {
            road.data.proto.TrafficFlowExtension.Builder subBuilder = null;
            if (trafficFlowExtension_ != null) {
              subBuilder = trafficFlowExtension_.toBuilder();
            }
            trafficFlowExtension_ = input.readMessage(road.data.proto.TrafficFlowExtension.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(trafficFlowExtension_);
              trafficFlowExtension_ = subBuilder.buildPartial();
            }

            break;
          }
          case 128: {

            timeHeadway_ = input.readUInt32();
            break;
          }
          case 136: {

            spaceHeadway_ = input.readUInt32();
            break;
          }
          case 144: {

            stopNums_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStat_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStat_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.TrafficFlowStat.class, road.data.proto.TrafficFlowStat.Builder.class);
  }

  public static final int MAPELEMENT_FIELD_NUMBER = 1;
  private road.data.proto.TrafficFlowStatMapElement mapElement_;
  /**
   * <pre>
   * 本组交通流统计值绑定的路网元素
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
   */
  public boolean hasMapElement() {
    return mapElement_ != null;
  }
  /**
   * <pre>
   * 本组交通流统计值绑定的路网元素
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
   */
  public road.data.proto.TrafficFlowStatMapElement getMapElement() {
    return mapElement_ == null ? road.data.proto.TrafficFlowStatMapElement.getDefaultInstance() : mapElement_;
  }
  /**
   * <pre>
   * 本组交通流统计值绑定的路网元素
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
   */
  public road.data.proto.TrafficFlowStatMapElementOrBuilder getMapElementOrBuilder() {
    return getMapElement();
  }

  public static final int MAPELEMENTTYPE_FIELD_NUMBER = 2;
  private int mapElementType_;
  /**
   * <pre>
   *路网元素类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapElementType mapElementType = 2;</code>
   */
  public int getMapElementTypeValue() {
    return mapElementType_;
  }
  /**
   * <pre>
   *路网元素类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapElementType mapElementType = 2;</code>
   */
  public road.data.proto.MapElementType getMapElementType() {
    @SuppressWarnings("deprecation")
    road.data.proto.MapElementType result = road.data.proto.MapElementType.valueOf(mapElementType_);
    return result == null ? road.data.proto.MapElementType.UNRECOGNIZED : result;
  }

  public static final int PTCTYPE_FIELD_NUMBER = 3;
  private int ptcType_;
  /**
   * <pre>
   * 路侧单元检测到的交通参与者类型。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 3;</code>
   */
  public int getPtcTypeValue() {
    return ptcType_;
  }
  /**
   * <pre>
   * 路侧单元检测到的交通参与者类型。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 3;</code>
   */
  public road.data.proto.ParticipantType getPtcType() {
    @SuppressWarnings("deprecation")
    road.data.proto.ParticipantType result = road.data.proto.ParticipantType.valueOf(ptcType_);
    return result == null ? road.data.proto.ParticipantType.UNRECOGNIZED : result;
  }

  public static final int VEHICLETYPE_FIELD_NUMBER = 4;
  private int vehicleType_;
  /**
   * <pre>
   * 可选，参考VehicleType（0表示对所有车辆作聚合）
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 4;</code>
   */
  public int getVehicleTypeValue() {
    return vehicleType_;
  }
  /**
   * <pre>
   * 可选，参考VehicleType（0表示对所有车辆作聚合）
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 4;</code>
   */
  public road.data.proto.VehicleType getVehicleType() {
    @SuppressWarnings("deprecation")
    road.data.proto.VehicleType result = road.data.proto.VehicleType.valueOf(vehicleType_);
    return result == null ? road.data.proto.VehicleType.UNRECOGNIZED : result;
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 5;
  private long timestamp_;
  /**
   * <pre>
   *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 5;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int VOLUME_FIELD_NUMBER = 6;
  private int volume_;
  /**
   * <pre>
   *流量，0.01 pcu/h,
   * </pre>
   *
   * <code>uint32 volume = 6;</code>
   */
  public int getVolume() {
    return volume_;
  }

  public static final int SPEEDPOINT_FIELD_NUMBER = 7;
  private int speedPoint_;
  /**
   * <pre>
   * 可选，地点速度，0.01m/s
   * </pre>
   *
   * <code>uint32 speedPoint = 7;</code>
   */
  public int getSpeedPoint() {
    return speedPoint_;
  }

  public static final int SPEEDAREA_FIELD_NUMBER = 8;
  private int speedArea_;
  /**
   * <pre>
   * 区域平均速度，0.01m/s
   * </pre>
   *
   * <code>uint32 speedArea = 8;</code>
   */
  public int getSpeedArea() {
    return speedArea_;
  }

  public static final int DENSITY_FIELD_NUMBER = 9;
  private int density_;
  /**
   * <pre>
   * 密度，0.01 pcu/km
   * </pre>
   *
   * <code>uint32 density = 9;</code>
   */
  public int getDensity() {
    return density_;
  }

  public static final int TRAVELTIME_FIELD_NUMBER = 10;
  private int travelTime_;
  /**
   * <pre>
   *可选，行程时间，单位：0.1s/ vehicle
   * </pre>
   *
   * <code>uint32 travelTime = 10;</code>
   */
  public int getTravelTime() {
    return travelTime_;
  }

  public static final int DELAY_FIELD_NUMBER = 11;
  private int delay_;
  /**
   * <pre>
   *  平均延误，0.01 sec/vehicle
   * </pre>
   *
   * <code>uint32 delay = 11;</code>
   */
  public int getDelay() {
    return delay_;
  }

  public static final int QUEUELENGTH_FIELD_NUMBER = 12;
  private int queueLength_;
  /**
   * <pre>
   *可选，排队长度，0.1m
   * </pre>
   *
   * <code>uint32 queueLength = 12;</code>
   */
  public int getQueueLength() {
    return queueLength_;
  }

  public static final int QUEUEINT_FIELD_NUMBER = 13;
  private int queueInt_;
  /**
   * <pre>
   *可选，排队车辆数
   * </pre>
   *
   * <code>uint32 queueInt = 13;</code>
   */
  public int getQueueInt() {
    return queueInt_;
  }

  public static final int CONGESTION_FIELD_NUMBER = 14;
  private int congestion_;
  /**
   * <pre>
   * 拥堵指数，%
   * </pre>
   *
   * <code>uint32 congestion = 14;</code>
   */
  public int getCongestion() {
    return congestion_;
  }

  public static final int TRAFFICFLOWEXTENSION_FIELD_NUMBER = 15;
  private road.data.proto.TrafficFlowExtension trafficFlowExtension_;
  /**
   * <pre>
   *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
   */
  public boolean hasTrafficFlowExtension() {
    return trafficFlowExtension_ != null;
  }
  /**
   * <pre>
   *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
   */
  public road.data.proto.TrafficFlowExtension getTrafficFlowExtension() {
    return trafficFlowExtension_ == null ? road.data.proto.TrafficFlowExtension.getDefaultInstance() : trafficFlowExtension_;
  }
  /**
   * <pre>
   *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
   */
  public road.data.proto.TrafficFlowExtensionOrBuilder getTrafficFlowExtensionOrBuilder() {
    return getTrafficFlowExtension();
  }

  public static final int TIMEHEADWAY_FIELD_NUMBER = 16;
  private int timeHeadway_;
  /**
   * <pre>
   *可选，车头时距，单位：0.01s
   * </pre>
   *
   * <code>uint32 timeHeadway = 16;</code>
   */
  public int getTimeHeadway() {
    return timeHeadway_;
  }

  public static final int SPACEHEADWAY_FIELD_NUMBER = 17;
  private int spaceHeadway_;
  /**
   * <pre>
   *可选，车头间距，单位：0.01m
   * </pre>
   *
   * <code>uint32 spaceHeadway = 17;</code>
   */
  public int getSpaceHeadway() {
    return spaceHeadway_;
  }

  public static final int STOPNUMS_FIELD_NUMBER = 18;
  private int stopNums_;
  /**
   * <pre>
   *可选，停车次数，次
   * </pre>
   *
   * <code>uint32 stopNums = 18;</code>
   */
  public int getStopNums() {
    return stopNums_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (mapElement_ != null) {
      output.writeMessage(1, getMapElement());
    }
    if (mapElementType_ != road.data.proto.MapElementType.MAP_ELEMENT_TYPE_UNKNOWN.getNumber()) {
      output.writeEnum(2, mapElementType_);
    }
    if (ptcType_ != road.data.proto.ParticipantType.OBJECTTYPE_UNKNOWN.getNumber()) {
      output.writeEnum(3, ptcType_);
    }
    if (vehicleType_ != road.data.proto.VehicleType.UNKNOWN_VEHICLE_CLASS.getNumber()) {
      output.writeEnum(4, vehicleType_);
    }
    if (timestamp_ != 0L) {
      output.writeUInt64(5, timestamp_);
    }
    if (volume_ != 0) {
      output.writeUInt32(6, volume_);
    }
    if (speedPoint_ != 0) {
      output.writeUInt32(7, speedPoint_);
    }
    if (speedArea_ != 0) {
      output.writeUInt32(8, speedArea_);
    }
    if (density_ != 0) {
      output.writeUInt32(9, density_);
    }
    if (travelTime_ != 0) {
      output.writeUInt32(10, travelTime_);
    }
    if (delay_ != 0) {
      output.writeUInt32(11, delay_);
    }
    if (queueLength_ != 0) {
      output.writeUInt32(12, queueLength_);
    }
    if (queueInt_ != 0) {
      output.writeUInt32(13, queueInt_);
    }
    if (congestion_ != 0) {
      output.writeUInt32(14, congestion_);
    }
    if (trafficFlowExtension_ != null) {
      output.writeMessage(15, getTrafficFlowExtension());
    }
    if (timeHeadway_ != 0) {
      output.writeUInt32(16, timeHeadway_);
    }
    if (spaceHeadway_ != 0) {
      output.writeUInt32(17, spaceHeadway_);
    }
    if (stopNums_ != 0) {
      output.writeUInt32(18, stopNums_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (mapElement_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getMapElement());
    }
    if (mapElementType_ != road.data.proto.MapElementType.MAP_ELEMENT_TYPE_UNKNOWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, mapElementType_);
    }
    if (ptcType_ != road.data.proto.ParticipantType.OBJECTTYPE_UNKNOWN.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(3, ptcType_);
    }
    if (vehicleType_ != road.data.proto.VehicleType.UNKNOWN_VEHICLE_CLASS.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(4, vehicleType_);
    }
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(5, timestamp_);
    }
    if (volume_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(6, volume_);
    }
    if (speedPoint_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(7, speedPoint_);
    }
    if (speedArea_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(8, speedArea_);
    }
    if (density_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(9, density_);
    }
    if (travelTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(10, travelTime_);
    }
    if (delay_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(11, delay_);
    }
    if (queueLength_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(12, queueLength_);
    }
    if (queueInt_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(13, queueInt_);
    }
    if (congestion_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(14, congestion_);
    }
    if (trafficFlowExtension_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(15, getTrafficFlowExtension());
    }
    if (timeHeadway_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(16, timeHeadway_);
    }
    if (spaceHeadway_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(17, spaceHeadway_);
    }
    if (stopNums_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(18, stopNums_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.TrafficFlowStat)) {
      return super.equals(obj);
    }
    road.data.proto.TrafficFlowStat other = (road.data.proto.TrafficFlowStat) obj;

    if (hasMapElement() != other.hasMapElement()) return false;
    if (hasMapElement()) {
      if (!getMapElement()
          .equals(other.getMapElement())) return false;
    }
    if (mapElementType_ != other.mapElementType_) return false;
    if (ptcType_ != other.ptcType_) return false;
    if (vehicleType_ != other.vehicleType_) return false;
    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (getVolume()
        != other.getVolume()) return false;
    if (getSpeedPoint()
        != other.getSpeedPoint()) return false;
    if (getSpeedArea()
        != other.getSpeedArea()) return false;
    if (getDensity()
        != other.getDensity()) return false;
    if (getTravelTime()
        != other.getTravelTime()) return false;
    if (getDelay()
        != other.getDelay()) return false;
    if (getQueueLength()
        != other.getQueueLength()) return false;
    if (getQueueInt()
        != other.getQueueInt()) return false;
    if (getCongestion()
        != other.getCongestion()) return false;
    if (hasTrafficFlowExtension() != other.hasTrafficFlowExtension()) return false;
    if (hasTrafficFlowExtension()) {
      if (!getTrafficFlowExtension()
          .equals(other.getTrafficFlowExtension())) return false;
    }
    if (getTimeHeadway()
        != other.getTimeHeadway()) return false;
    if (getSpaceHeadway()
        != other.getSpaceHeadway()) return false;
    if (getStopNums()
        != other.getStopNums()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasMapElement()) {
      hash = (37 * hash) + MAPELEMENT_FIELD_NUMBER;
      hash = (53 * hash) + getMapElement().hashCode();
    }
    hash = (37 * hash) + MAPELEMENTTYPE_FIELD_NUMBER;
    hash = (53 * hash) + mapElementType_;
    hash = (37 * hash) + PTCTYPE_FIELD_NUMBER;
    hash = (53 * hash) + ptcType_;
    hash = (37 * hash) + VEHICLETYPE_FIELD_NUMBER;
    hash = (53 * hash) + vehicleType_;
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    hash = (37 * hash) + VOLUME_FIELD_NUMBER;
    hash = (53 * hash) + getVolume();
    hash = (37 * hash) + SPEEDPOINT_FIELD_NUMBER;
    hash = (53 * hash) + getSpeedPoint();
    hash = (37 * hash) + SPEEDAREA_FIELD_NUMBER;
    hash = (53 * hash) + getSpeedArea();
    hash = (37 * hash) + DENSITY_FIELD_NUMBER;
    hash = (53 * hash) + getDensity();
    hash = (37 * hash) + TRAVELTIME_FIELD_NUMBER;
    hash = (53 * hash) + getTravelTime();
    hash = (37 * hash) + DELAY_FIELD_NUMBER;
    hash = (53 * hash) + getDelay();
    hash = (37 * hash) + QUEUELENGTH_FIELD_NUMBER;
    hash = (53 * hash) + getQueueLength();
    hash = (37 * hash) + QUEUEINT_FIELD_NUMBER;
    hash = (53 * hash) + getQueueInt();
    hash = (37 * hash) + CONGESTION_FIELD_NUMBER;
    hash = (53 * hash) + getCongestion();
    if (hasTrafficFlowExtension()) {
      hash = (37 * hash) + TRAFFICFLOWEXTENSION_FIELD_NUMBER;
      hash = (53 * hash) + getTrafficFlowExtension().hashCode();
    }
    hash = (37 * hash) + TIMEHEADWAY_FIELD_NUMBER;
    hash = (53 * hash) + getTimeHeadway();
    hash = (37 * hash) + SPACEHEADWAY_FIELD_NUMBER;
    hash = (53 * hash) + getSpaceHeadway();
    hash = (37 * hash) + STOPNUMS_FIELD_NUMBER;
    hash = (53 * hash) + getStopNums();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.TrafficFlowStat parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowStat parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStat parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowStat parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStat parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowStat parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStat parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowStat parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStat parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowStat parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStat parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowStat parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.TrafficFlowStat prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *单路网元素统计值      
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.TrafficFlowStat}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.TrafficFlowStat)
      road.data.proto.TrafficFlowStatOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStat_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStat_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.TrafficFlowStat.class, road.data.proto.TrafficFlowStat.Builder.class);
    }

    // Construct using road.data.proto.TrafficFlowStat.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (mapElementBuilder_ == null) {
        mapElement_ = null;
      } else {
        mapElement_ = null;
        mapElementBuilder_ = null;
      }
      mapElementType_ = 0;

      ptcType_ = 0;

      vehicleType_ = 0;

      timestamp_ = 0L;

      volume_ = 0;

      speedPoint_ = 0;

      speedArea_ = 0;

      density_ = 0;

      travelTime_ = 0;

      delay_ = 0;

      queueLength_ = 0;

      queueInt_ = 0;

      congestion_ = 0;

      if (trafficFlowExtensionBuilder_ == null) {
        trafficFlowExtension_ = null;
      } else {
        trafficFlowExtension_ = null;
        trafficFlowExtensionBuilder_ = null;
      }
      timeHeadway_ = 0;

      spaceHeadway_ = 0;

      stopNums_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStat_descriptor;
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowStat getDefaultInstanceForType() {
      return road.data.proto.TrafficFlowStat.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowStat build() {
      road.data.proto.TrafficFlowStat result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowStat buildPartial() {
      road.data.proto.TrafficFlowStat result = new road.data.proto.TrafficFlowStat(this);
      if (mapElementBuilder_ == null) {
        result.mapElement_ = mapElement_;
      } else {
        result.mapElement_ = mapElementBuilder_.build();
      }
      result.mapElementType_ = mapElementType_;
      result.ptcType_ = ptcType_;
      result.vehicleType_ = vehicleType_;
      result.timestamp_ = timestamp_;
      result.volume_ = volume_;
      result.speedPoint_ = speedPoint_;
      result.speedArea_ = speedArea_;
      result.density_ = density_;
      result.travelTime_ = travelTime_;
      result.delay_ = delay_;
      result.queueLength_ = queueLength_;
      result.queueInt_ = queueInt_;
      result.congestion_ = congestion_;
      if (trafficFlowExtensionBuilder_ == null) {
        result.trafficFlowExtension_ = trafficFlowExtension_;
      } else {
        result.trafficFlowExtension_ = trafficFlowExtensionBuilder_.build();
      }
      result.timeHeadway_ = timeHeadway_;
      result.spaceHeadway_ = spaceHeadway_;
      result.stopNums_ = stopNums_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.TrafficFlowStat) {
        return mergeFrom((road.data.proto.TrafficFlowStat)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.TrafficFlowStat other) {
      if (other == road.data.proto.TrafficFlowStat.getDefaultInstance()) return this;
      if (other.hasMapElement()) {
        mergeMapElement(other.getMapElement());
      }
      if (other.mapElementType_ != 0) {
        setMapElementTypeValue(other.getMapElementTypeValue());
      }
      if (other.ptcType_ != 0) {
        setPtcTypeValue(other.getPtcTypeValue());
      }
      if (other.vehicleType_ != 0) {
        setVehicleTypeValue(other.getVehicleTypeValue());
      }
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (other.getVolume() != 0) {
        setVolume(other.getVolume());
      }
      if (other.getSpeedPoint() != 0) {
        setSpeedPoint(other.getSpeedPoint());
      }
      if (other.getSpeedArea() != 0) {
        setSpeedArea(other.getSpeedArea());
      }
      if (other.getDensity() != 0) {
        setDensity(other.getDensity());
      }
      if (other.getTravelTime() != 0) {
        setTravelTime(other.getTravelTime());
      }
      if (other.getDelay() != 0) {
        setDelay(other.getDelay());
      }
      if (other.getQueueLength() != 0) {
        setQueueLength(other.getQueueLength());
      }
      if (other.getQueueInt() != 0) {
        setQueueInt(other.getQueueInt());
      }
      if (other.getCongestion() != 0) {
        setCongestion(other.getCongestion());
      }
      if (other.hasTrafficFlowExtension()) {
        mergeTrafficFlowExtension(other.getTrafficFlowExtension());
      }
      if (other.getTimeHeadway() != 0) {
        setTimeHeadway(other.getTimeHeadway());
      }
      if (other.getSpaceHeadway() != 0) {
        setSpaceHeadway(other.getSpaceHeadway());
      }
      if (other.getStopNums() != 0) {
        setStopNums(other.getStopNums());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.TrafficFlowStat parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.TrafficFlowStat) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.TrafficFlowStatMapElement mapElement_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.TrafficFlowStatMapElement, road.data.proto.TrafficFlowStatMapElement.Builder, road.data.proto.TrafficFlowStatMapElementOrBuilder> mapElementBuilder_;
    /**
     * <pre>
     * 本组交通流统计值绑定的路网元素
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
     */
    public boolean hasMapElement() {
      return mapElementBuilder_ != null || mapElement_ != null;
    }
    /**
     * <pre>
     * 本组交通流统计值绑定的路网元素
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
     */
    public road.data.proto.TrafficFlowStatMapElement getMapElement() {
      if (mapElementBuilder_ == null) {
        return mapElement_ == null ? road.data.proto.TrafficFlowStatMapElement.getDefaultInstance() : mapElement_;
      } else {
        return mapElementBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 本组交通流统计值绑定的路网元素
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
     */
    public Builder setMapElement(road.data.proto.TrafficFlowStatMapElement value) {
      if (mapElementBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        mapElement_ = value;
        onChanged();
      } else {
        mapElementBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 本组交通流统计值绑定的路网元素
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
     */
    public Builder setMapElement(
        road.data.proto.TrafficFlowStatMapElement.Builder builderForValue) {
      if (mapElementBuilder_ == null) {
        mapElement_ = builderForValue.build();
        onChanged();
      } else {
        mapElementBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 本组交通流统计值绑定的路网元素
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
     */
    public Builder mergeMapElement(road.data.proto.TrafficFlowStatMapElement value) {
      if (mapElementBuilder_ == null) {
        if (mapElement_ != null) {
          mapElement_ =
            road.data.proto.TrafficFlowStatMapElement.newBuilder(mapElement_).mergeFrom(value).buildPartial();
        } else {
          mapElement_ = value;
        }
        onChanged();
      } else {
        mapElementBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 本组交通流统计值绑定的路网元素
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
     */
    public Builder clearMapElement() {
      if (mapElementBuilder_ == null) {
        mapElement_ = null;
        onChanged();
      } else {
        mapElement_ = null;
        mapElementBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 本组交通流统计值绑定的路网元素
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
     */
    public road.data.proto.TrafficFlowStatMapElement.Builder getMapElementBuilder() {
      
      onChanged();
      return getMapElementFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 本组交通流统计值绑定的路网元素
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
     */
    public road.data.proto.TrafficFlowStatMapElementOrBuilder getMapElementOrBuilder() {
      if (mapElementBuilder_ != null) {
        return mapElementBuilder_.getMessageOrBuilder();
      } else {
        return mapElement_ == null ?
            road.data.proto.TrafficFlowStatMapElement.getDefaultInstance() : mapElement_;
      }
    }
    /**
     * <pre>
     * 本组交通流统计值绑定的路网元素
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.TrafficFlowStatMapElement, road.data.proto.TrafficFlowStatMapElement.Builder, road.data.proto.TrafficFlowStatMapElementOrBuilder> 
        getMapElementFieldBuilder() {
      if (mapElementBuilder_ == null) {
        mapElementBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.TrafficFlowStatMapElement, road.data.proto.TrafficFlowStatMapElement.Builder, road.data.proto.TrafficFlowStatMapElementOrBuilder>(
                getMapElement(),
                getParentForChildren(),
                isClean());
        mapElement_ = null;
      }
      return mapElementBuilder_;
    }

    private int mapElementType_ = 0;
    /**
     * <pre>
     *路网元素类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapElementType mapElementType = 2;</code>
     */
    public int getMapElementTypeValue() {
      return mapElementType_;
    }
    /**
     * <pre>
     *路网元素类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapElementType mapElementType = 2;</code>
     */
    public Builder setMapElementTypeValue(int value) {
      mapElementType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *路网元素类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapElementType mapElementType = 2;</code>
     */
    public road.data.proto.MapElementType getMapElementType() {
      @SuppressWarnings("deprecation")
      road.data.proto.MapElementType result = road.data.proto.MapElementType.valueOf(mapElementType_);
      return result == null ? road.data.proto.MapElementType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *路网元素类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapElementType mapElementType = 2;</code>
     */
    public Builder setMapElementType(road.data.proto.MapElementType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      mapElementType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *路网元素类型
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MapElementType mapElementType = 2;</code>
     */
    public Builder clearMapElementType() {
      
      mapElementType_ = 0;
      onChanged();
      return this;
    }

    private int ptcType_ = 0;
    /**
     * <pre>
     * 路侧单元检测到的交通参与者类型。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 3;</code>
     */
    public int getPtcTypeValue() {
      return ptcType_;
    }
    /**
     * <pre>
     * 路侧单元检测到的交通参与者类型。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 3;</code>
     */
    public Builder setPtcTypeValue(int value) {
      ptcType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 路侧单元检测到的交通参与者类型。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 3;</code>
     */
    public road.data.proto.ParticipantType getPtcType() {
      @SuppressWarnings("deprecation")
      road.data.proto.ParticipantType result = road.data.proto.ParticipantType.valueOf(ptcType_);
      return result == null ? road.data.proto.ParticipantType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 路侧单元检测到的交通参与者类型。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 3;</code>
     */
    public Builder setPtcType(road.data.proto.ParticipantType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      ptcType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 路侧单元检测到的交通参与者类型。
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 3;</code>
     */
    public Builder clearPtcType() {
      
      ptcType_ = 0;
      onChanged();
      return this;
    }

    private int vehicleType_ = 0;
    /**
     * <pre>
     * 可选，参考VehicleType（0表示对所有车辆作聚合）
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 4;</code>
     */
    public int getVehicleTypeValue() {
      return vehicleType_;
    }
    /**
     * <pre>
     * 可选，参考VehicleType（0表示对所有车辆作聚合）
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 4;</code>
     */
    public Builder setVehicleTypeValue(int value) {
      vehicleType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，参考VehicleType（0表示对所有车辆作聚合）
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 4;</code>
     */
    public road.data.proto.VehicleType getVehicleType() {
      @SuppressWarnings("deprecation")
      road.data.proto.VehicleType result = road.data.proto.VehicleType.valueOf(vehicleType_);
      return result == null ? road.data.proto.VehicleType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 可选，参考VehicleType（0表示对所有车辆作聚合）
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 4;</code>
     */
    public Builder setVehicleType(road.data.proto.VehicleType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      vehicleType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，参考VehicleType（0表示对所有车辆作聚合）
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 4;</code>
     */
    public Builder clearVehicleType() {
      
      vehicleType_ = 0;
      onChanged();
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 5;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 5;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 5;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private int volume_ ;
    /**
     * <pre>
     *流量，0.01 pcu/h,
     * </pre>
     *
     * <code>uint32 volume = 6;</code>
     */
    public int getVolume() {
      return volume_;
    }
    /**
     * <pre>
     *流量，0.01 pcu/h,
     * </pre>
     *
     * <code>uint32 volume = 6;</code>
     */
    public Builder setVolume(int value) {
      
      volume_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *流量，0.01 pcu/h,
     * </pre>
     *
     * <code>uint32 volume = 6;</code>
     */
    public Builder clearVolume() {
      
      volume_ = 0;
      onChanged();
      return this;
    }

    private int speedPoint_ ;
    /**
     * <pre>
     * 可选，地点速度，0.01m/s
     * </pre>
     *
     * <code>uint32 speedPoint = 7;</code>
     */
    public int getSpeedPoint() {
      return speedPoint_;
    }
    /**
     * <pre>
     * 可选，地点速度，0.01m/s
     * </pre>
     *
     * <code>uint32 speedPoint = 7;</code>
     */
    public Builder setSpeedPoint(int value) {
      
      speedPoint_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 可选，地点速度，0.01m/s
     * </pre>
     *
     * <code>uint32 speedPoint = 7;</code>
     */
    public Builder clearSpeedPoint() {
      
      speedPoint_ = 0;
      onChanged();
      return this;
    }

    private int speedArea_ ;
    /**
     * <pre>
     * 区域平均速度，0.01m/s
     * </pre>
     *
     * <code>uint32 speedArea = 8;</code>
     */
    public int getSpeedArea() {
      return speedArea_;
    }
    /**
     * <pre>
     * 区域平均速度，0.01m/s
     * </pre>
     *
     * <code>uint32 speedArea = 8;</code>
     */
    public Builder setSpeedArea(int value) {
      
      speedArea_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 区域平均速度，0.01m/s
     * </pre>
     *
     * <code>uint32 speedArea = 8;</code>
     */
    public Builder clearSpeedArea() {
      
      speedArea_ = 0;
      onChanged();
      return this;
    }

    private int density_ ;
    /**
     * <pre>
     * 密度，0.01 pcu/km
     * </pre>
     *
     * <code>uint32 density = 9;</code>
     */
    public int getDensity() {
      return density_;
    }
    /**
     * <pre>
     * 密度，0.01 pcu/km
     * </pre>
     *
     * <code>uint32 density = 9;</code>
     */
    public Builder setDensity(int value) {
      
      density_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 密度，0.01 pcu/km
     * </pre>
     *
     * <code>uint32 density = 9;</code>
     */
    public Builder clearDensity() {
      
      density_ = 0;
      onChanged();
      return this;
    }

    private int travelTime_ ;
    /**
     * <pre>
     *可选，行程时间，单位：0.1s/ vehicle
     * </pre>
     *
     * <code>uint32 travelTime = 10;</code>
     */
    public int getTravelTime() {
      return travelTime_;
    }
    /**
     * <pre>
     *可选，行程时间，单位：0.1s/ vehicle
     * </pre>
     *
     * <code>uint32 travelTime = 10;</code>
     */
    public Builder setTravelTime(int value) {
      
      travelTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，行程时间，单位：0.1s/ vehicle
     * </pre>
     *
     * <code>uint32 travelTime = 10;</code>
     */
    public Builder clearTravelTime() {
      
      travelTime_ = 0;
      onChanged();
      return this;
    }

    private int delay_ ;
    /**
     * <pre>
     *  平均延误，0.01 sec/vehicle
     * </pre>
     *
     * <code>uint32 delay = 11;</code>
     */
    public int getDelay() {
      return delay_;
    }
    /**
     * <pre>
     *  平均延误，0.01 sec/vehicle
     * </pre>
     *
     * <code>uint32 delay = 11;</code>
     */
    public Builder setDelay(int value) {
      
      delay_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *  平均延误，0.01 sec/vehicle
     * </pre>
     *
     * <code>uint32 delay = 11;</code>
     */
    public Builder clearDelay() {
      
      delay_ = 0;
      onChanged();
      return this;
    }

    private int queueLength_ ;
    /**
     * <pre>
     *可选，排队长度，0.1m
     * </pre>
     *
     * <code>uint32 queueLength = 12;</code>
     */
    public int getQueueLength() {
      return queueLength_;
    }
    /**
     * <pre>
     *可选，排队长度，0.1m
     * </pre>
     *
     * <code>uint32 queueLength = 12;</code>
     */
    public Builder setQueueLength(int value) {
      
      queueLength_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，排队长度，0.1m
     * </pre>
     *
     * <code>uint32 queueLength = 12;</code>
     */
    public Builder clearQueueLength() {
      
      queueLength_ = 0;
      onChanged();
      return this;
    }

    private int queueInt_ ;
    /**
     * <pre>
     *可选，排队车辆数
     * </pre>
     *
     * <code>uint32 queueInt = 13;</code>
     */
    public int getQueueInt() {
      return queueInt_;
    }
    /**
     * <pre>
     *可选，排队车辆数
     * </pre>
     *
     * <code>uint32 queueInt = 13;</code>
     */
    public Builder setQueueInt(int value) {
      
      queueInt_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，排队车辆数
     * </pre>
     *
     * <code>uint32 queueInt = 13;</code>
     */
    public Builder clearQueueInt() {
      
      queueInt_ = 0;
      onChanged();
      return this;
    }

    private int congestion_ ;
    /**
     * <pre>
     * 拥堵指数，%
     * </pre>
     *
     * <code>uint32 congestion = 14;</code>
     */
    public int getCongestion() {
      return congestion_;
    }
    /**
     * <pre>
     * 拥堵指数，%
     * </pre>
     *
     * <code>uint32 congestion = 14;</code>
     */
    public Builder setCongestion(int value) {
      
      congestion_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 拥堵指数，%
     * </pre>
     *
     * <code>uint32 congestion = 14;</code>
     */
    public Builder clearCongestion() {
      
      congestion_ = 0;
      onChanged();
      return this;
    }

    private road.data.proto.TrafficFlowExtension trafficFlowExtension_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.TrafficFlowExtension, road.data.proto.TrafficFlowExtension.Builder, road.data.proto.TrafficFlowExtensionOrBuilder> trafficFlowExtensionBuilder_;
    /**
     * <pre>
     *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
     */
    public boolean hasTrafficFlowExtension() {
      return trafficFlowExtensionBuilder_ != null || trafficFlowExtension_ != null;
    }
    /**
     * <pre>
     *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
     */
    public road.data.proto.TrafficFlowExtension getTrafficFlowExtension() {
      if (trafficFlowExtensionBuilder_ == null) {
        return trafficFlowExtension_ == null ? road.data.proto.TrafficFlowExtension.getDefaultInstance() : trafficFlowExtension_;
      } else {
        return trafficFlowExtensionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
     */
    public Builder setTrafficFlowExtension(road.data.proto.TrafficFlowExtension value) {
      if (trafficFlowExtensionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        trafficFlowExtension_ = value;
        onChanged();
      } else {
        trafficFlowExtensionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
     */
    public Builder setTrafficFlowExtension(
        road.data.proto.TrafficFlowExtension.Builder builderForValue) {
      if (trafficFlowExtensionBuilder_ == null) {
        trafficFlowExtension_ = builderForValue.build();
        onChanged();
      } else {
        trafficFlowExtensionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
     */
    public Builder mergeTrafficFlowExtension(road.data.proto.TrafficFlowExtension value) {
      if (trafficFlowExtensionBuilder_ == null) {
        if (trafficFlowExtension_ != null) {
          trafficFlowExtension_ =
            road.data.proto.TrafficFlowExtension.newBuilder(trafficFlowExtension_).mergeFrom(value).buildPartial();
        } else {
          trafficFlowExtension_ = value;
        }
        onChanged();
      } else {
        trafficFlowExtensionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
     */
    public Builder clearTrafficFlowExtension() {
      if (trafficFlowExtensionBuilder_ == null) {
        trafficFlowExtension_ = null;
        onChanged();
      } else {
        trafficFlowExtension_ = null;
        trafficFlowExtensionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
     */
    public road.data.proto.TrafficFlowExtension.Builder getTrafficFlowExtensionBuilder() {
      
      onChanged();
      return getTrafficFlowExtensionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
     */
    public road.data.proto.TrafficFlowExtensionOrBuilder getTrafficFlowExtensionOrBuilder() {
      if (trafficFlowExtensionBuilder_ != null) {
        return trafficFlowExtensionBuilder_.getMessageOrBuilder();
      } else {
        return trafficFlowExtension_ == null ?
            road.data.proto.TrafficFlowExtension.getDefaultInstance() : trafficFlowExtension_;
      }
    }
    /**
     * <pre>
     *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.TrafficFlowExtension, road.data.proto.TrafficFlowExtension.Builder, road.data.proto.TrafficFlowExtensionOrBuilder> 
        getTrafficFlowExtensionFieldBuilder() {
      if (trafficFlowExtensionBuilder_ == null) {
        trafficFlowExtensionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.TrafficFlowExtension, road.data.proto.TrafficFlowExtension.Builder, road.data.proto.TrafficFlowExtensionOrBuilder>(
                getTrafficFlowExtension(),
                getParentForChildren(),
                isClean());
        trafficFlowExtension_ = null;
      }
      return trafficFlowExtensionBuilder_;
    }

    private int timeHeadway_ ;
    /**
     * <pre>
     *可选，车头时距，单位：0.01s
     * </pre>
     *
     * <code>uint32 timeHeadway = 16;</code>
     */
    public int getTimeHeadway() {
      return timeHeadway_;
    }
    /**
     * <pre>
     *可选，车头时距，单位：0.01s
     * </pre>
     *
     * <code>uint32 timeHeadway = 16;</code>
     */
    public Builder setTimeHeadway(int value) {
      
      timeHeadway_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车头时距，单位：0.01s
     * </pre>
     *
     * <code>uint32 timeHeadway = 16;</code>
     */
    public Builder clearTimeHeadway() {
      
      timeHeadway_ = 0;
      onChanged();
      return this;
    }

    private int spaceHeadway_ ;
    /**
     * <pre>
     *可选，车头间距，单位：0.01m
     * </pre>
     *
     * <code>uint32 spaceHeadway = 17;</code>
     */
    public int getSpaceHeadway() {
      return spaceHeadway_;
    }
    /**
     * <pre>
     *可选，车头间距，单位：0.01m
     * </pre>
     *
     * <code>uint32 spaceHeadway = 17;</code>
     */
    public Builder setSpaceHeadway(int value) {
      
      spaceHeadway_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，车头间距，单位：0.01m
     * </pre>
     *
     * <code>uint32 spaceHeadway = 17;</code>
     */
    public Builder clearSpaceHeadway() {
      
      spaceHeadway_ = 0;
      onChanged();
      return this;
    }

    private int stopNums_ ;
    /**
     * <pre>
     *可选，停车次数，次
     * </pre>
     *
     * <code>uint32 stopNums = 18;</code>
     */
    public int getStopNums() {
      return stopNums_;
    }
    /**
     * <pre>
     *可选，停车次数，次
     * </pre>
     *
     * <code>uint32 stopNums = 18;</code>
     */
    public Builder setStopNums(int value) {
      
      stopNums_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *可选，停车次数，次
     * </pre>
     *
     * <code>uint32 stopNums = 18;</code>
     */
    public Builder clearStopNums() {
      
      stopNums_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.TrafficFlowStat)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.TrafficFlowStat)
  private static final road.data.proto.TrafficFlowStat DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.TrafficFlowStat();
  }

  public static road.data.proto.TrafficFlowStat getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TrafficFlowStat>
      PARSER = new com.google.protobuf.AbstractParser<TrafficFlowStat>() {
    @java.lang.Override
    public TrafficFlowStat parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new TrafficFlowStat(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<TrafficFlowStat> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TrafficFlowStat> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.TrafficFlowStat getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

