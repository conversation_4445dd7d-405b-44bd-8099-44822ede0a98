// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface TrafficFlowStatByIntervalOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.TrafficFlowStatByInterval)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *固定时间间隔，单位：sec
   * </pre>
   *
   * <code>uint32 interval = 1;</code>
   */
  int getInterval();
}
