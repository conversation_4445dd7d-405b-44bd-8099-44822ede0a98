// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 * MSG 信号执行顺序 TrafficFlowStatBySignalCycle   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle}
 */
public  final class TrafficFlowStatBySignalCycle extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle)
    TrafficFlowStatBySignalCycleOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TrafficFlowStatBySignalCycle.newBuilder() to construct.
  private TrafficFlowStatBySignalCycle(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private TrafficFlowStatBySignalCycle() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new TrafficFlowStatBySignalCycle();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private TrafficFlowStatBySignalCycle(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            cycleStartTime_ = input.readUInt64();
            break;
          }
          case 16: {

            cycleEndTime_ = input.readUInt64();
            break;
          }
          case 24: {

            cycleTime_ = input.readUInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatBySignalCycle_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatBySignalCycle_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.TrafficFlowStatBySignalCycle.class, road.data.proto.TrafficFlowStatBySignalCycle.Builder.class);
  }

  public static final int CYCLESTARTTIME_FIELD_NUMBER = 1;
  private long cycleStartTime_;
  /**
   * <pre>
   *周期开始时间，Unix time，秒级时间戳，UTC 时间，单位秒，19700101000到现在的秒，消息时间
   * </pre>
   *
   * <code>uint64 cycleStartTime = 1;</code>
   */
  public long getCycleStartTime() {
    return cycleStartTime_;
  }

  public static final int CYCLEENDTIME_FIELD_NUMBER = 2;
  private long cycleEndTime_;
  /**
   * <pre>
   *周期结束时间，Unix time，秒级时间戳，UTC 时间，单位秒，19700101000到现在的秒，消息时间
   * </pre>
   *
   * <code>uint64 cycleEndTime = 2;</code>
   */
  public long getCycleEndTime() {
    return cycleEndTime_;
  }

  public static final int CYCLETIME_FIELD_NUMBER = 3;
  private int cycleTime_;
  /**
   * <pre>
   *周期时长，单位秒
   * </pre>
   *
   * <code>uint32 cycleTime = 3;</code>
   */
  public int getCycleTime() {
    return cycleTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (cycleStartTime_ != 0L) {
      output.writeUInt64(1, cycleStartTime_);
    }
    if (cycleEndTime_ != 0L) {
      output.writeUInt64(2, cycleEndTime_);
    }
    if (cycleTime_ != 0) {
      output.writeUInt32(3, cycleTime_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (cycleStartTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, cycleStartTime_);
    }
    if (cycleEndTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(2, cycleEndTime_);
    }
    if (cycleTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(3, cycleTime_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.TrafficFlowStatBySignalCycle)) {
      return super.equals(obj);
    }
    road.data.proto.TrafficFlowStatBySignalCycle other = (road.data.proto.TrafficFlowStatBySignalCycle) obj;

    if (getCycleStartTime()
        != other.getCycleStartTime()) return false;
    if (getCycleEndTime()
        != other.getCycleEndTime()) return false;
    if (getCycleTime()
        != other.getCycleTime()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CYCLESTARTTIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getCycleStartTime());
    hash = (37 * hash) + CYCLEENDTIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getCycleEndTime());
    hash = (37 * hash) + CYCLETIME_FIELD_NUMBER;
    hash = (53 * hash) + getCycleTime();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.TrafficFlowStatBySignalCycle parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowStatBySignalCycle parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatBySignalCycle parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowStatBySignalCycle parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatBySignalCycle parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowStatBySignalCycle parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatBySignalCycle parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowStatBySignalCycle parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatBySignalCycle parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowStatBySignalCycle parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatBySignalCycle parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowStatBySignalCycle parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.TrafficFlowStatBySignalCycle prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * MSG 信号执行顺序 TrafficFlowStatBySignalCycle   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle)
      road.data.proto.TrafficFlowStatBySignalCycleOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatBySignalCycle_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatBySignalCycle_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.TrafficFlowStatBySignalCycle.class, road.data.proto.TrafficFlowStatBySignalCycle.Builder.class);
    }

    // Construct using road.data.proto.TrafficFlowStatBySignalCycle.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      cycleStartTime_ = 0L;

      cycleEndTime_ = 0L;

      cycleTime_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatBySignalCycle_descriptor;
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowStatBySignalCycle getDefaultInstanceForType() {
      return road.data.proto.TrafficFlowStatBySignalCycle.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowStatBySignalCycle build() {
      road.data.proto.TrafficFlowStatBySignalCycle result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowStatBySignalCycle buildPartial() {
      road.data.proto.TrafficFlowStatBySignalCycle result = new road.data.proto.TrafficFlowStatBySignalCycle(this);
      result.cycleStartTime_ = cycleStartTime_;
      result.cycleEndTime_ = cycleEndTime_;
      result.cycleTime_ = cycleTime_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.TrafficFlowStatBySignalCycle) {
        return mergeFrom((road.data.proto.TrafficFlowStatBySignalCycle)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.TrafficFlowStatBySignalCycle other) {
      if (other == road.data.proto.TrafficFlowStatBySignalCycle.getDefaultInstance()) return this;
      if (other.getCycleStartTime() != 0L) {
        setCycleStartTime(other.getCycleStartTime());
      }
      if (other.getCycleEndTime() != 0L) {
        setCycleEndTime(other.getCycleEndTime());
      }
      if (other.getCycleTime() != 0) {
        setCycleTime(other.getCycleTime());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.TrafficFlowStatBySignalCycle parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.TrafficFlowStatBySignalCycle) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long cycleStartTime_ ;
    /**
     * <pre>
     *周期开始时间，Unix time，秒级时间戳，UTC 时间，单位秒，19700101000到现在的秒，消息时间
     * </pre>
     *
     * <code>uint64 cycleStartTime = 1;</code>
     */
    public long getCycleStartTime() {
      return cycleStartTime_;
    }
    /**
     * <pre>
     *周期开始时间，Unix time，秒级时间戳，UTC 时间，单位秒，19700101000到现在的秒，消息时间
     * </pre>
     *
     * <code>uint64 cycleStartTime = 1;</code>
     */
    public Builder setCycleStartTime(long value) {
      
      cycleStartTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *周期开始时间，Unix time，秒级时间戳，UTC 时间，单位秒，19700101000到现在的秒，消息时间
     * </pre>
     *
     * <code>uint64 cycleStartTime = 1;</code>
     */
    public Builder clearCycleStartTime() {
      
      cycleStartTime_ = 0L;
      onChanged();
      return this;
    }

    private long cycleEndTime_ ;
    /**
     * <pre>
     *周期结束时间，Unix time，秒级时间戳，UTC 时间，单位秒，19700101000到现在的秒，消息时间
     * </pre>
     *
     * <code>uint64 cycleEndTime = 2;</code>
     */
    public long getCycleEndTime() {
      return cycleEndTime_;
    }
    /**
     * <pre>
     *周期结束时间，Unix time，秒级时间戳，UTC 时间，单位秒，19700101000到现在的秒，消息时间
     * </pre>
     *
     * <code>uint64 cycleEndTime = 2;</code>
     */
    public Builder setCycleEndTime(long value) {
      
      cycleEndTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *周期结束时间，Unix time，秒级时间戳，UTC 时间，单位秒，19700101000到现在的秒，消息时间
     * </pre>
     *
     * <code>uint64 cycleEndTime = 2;</code>
     */
    public Builder clearCycleEndTime() {
      
      cycleEndTime_ = 0L;
      onChanged();
      return this;
    }

    private int cycleTime_ ;
    /**
     * <pre>
     *周期时长，单位秒
     * </pre>
     *
     * <code>uint32 cycleTime = 3;</code>
     */
    public int getCycleTime() {
      return cycleTime_;
    }
    /**
     * <pre>
     *周期时长，单位秒
     * </pre>
     *
     * <code>uint32 cycleTime = 3;</code>
     */
    public Builder setCycleTime(int value) {
      
      cycleTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *周期时长，单位秒
     * </pre>
     *
     * <code>uint32 cycleTime = 3;</code>
     */
    public Builder clearCycleTime() {
      
      cycleTime_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle)
  private static final road.data.proto.TrafficFlowStatBySignalCycle DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.TrafficFlowStatBySignalCycle();
  }

  public static road.data.proto.TrafficFlowStatBySignalCycle getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TrafficFlowStatBySignalCycle>
      PARSER = new com.google.protobuf.AbstractParser<TrafficFlowStatBySignalCycle>() {
    @java.lang.Override
    public TrafficFlowStatBySignalCycle parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new TrafficFlowStatBySignalCycle(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<TrafficFlowStatBySignalCycle> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TrafficFlowStatBySignalCycle> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.TrafficFlowStatBySignalCycle getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

