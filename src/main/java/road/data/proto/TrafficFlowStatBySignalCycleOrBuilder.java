// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface TrafficFlowStatBySignalCycleOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *周期开始时间，Unix time，秒级时间戳，UTC 时间，单位秒，19700101000到现在的秒，消息时间
   * </pre>
   *
   * <code>uint64 cycleStartTime = 1;</code>
   */
  long getCycleStartTime();

  /**
   * <pre>
   *周期结束时间，Unix time，秒级时间戳，UTC 时间，单位秒，19700101000到现在的秒，消息时间
   * </pre>
   *
   * <code>uint64 cycleEndTime = 2;</code>
   */
  long getCycleEndTime();

  /**
   * <pre>
   *周期时长，单位秒
   * </pre>
   *
   * <code>uint32 cycleTime = 3;</code>
   */
  int getCycleTime();
}
