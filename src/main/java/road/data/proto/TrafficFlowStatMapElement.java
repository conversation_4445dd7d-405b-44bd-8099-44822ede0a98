// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *路网元素绑定TrafficFlowStatMapElement 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.TrafficFlowStatMapElement}
 */
public  final class TrafficFlowStatMapElement extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.TrafficFlowStatMapElement)
    TrafficFlowStatMapElementOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TrafficFlowStatMapElement.newBuilder() to construct.
  private TrafficFlowStatMapElement(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private TrafficFlowStatMapElement() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new TrafficFlowStatMapElement();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private TrafficFlowStatMapElement(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.DetectorArea.Builder subBuilder = null;
            if (trafficFlowStatMapElementOneOfCase_ == 1) {
              subBuilder = ((road.data.proto.DetectorArea) trafficFlowStatMapElementOneOf_).toBuilder();
            }
            trafficFlowStatMapElementOneOf_ =
                input.readMessage(road.data.proto.DetectorArea.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.DetectorArea) trafficFlowStatMapElementOneOf_);
              trafficFlowStatMapElementOneOf_ = subBuilder.buildPartial();
            }
            trafficFlowStatMapElementOneOfCase_ = 1;
            break;
          }
          case 18: {
            road.data.proto.LaneStatInfo.Builder subBuilder = null;
            if (trafficFlowStatMapElementOneOfCase_ == 2) {
              subBuilder = ((road.data.proto.LaneStatInfo) trafficFlowStatMapElementOneOf_).toBuilder();
            }
            trafficFlowStatMapElementOneOf_ =
                input.readMessage(road.data.proto.LaneStatInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.LaneStatInfo) trafficFlowStatMapElementOneOf_);
              trafficFlowStatMapElementOneOf_ = subBuilder.buildPartial();
            }
            trafficFlowStatMapElementOneOfCase_ = 2;
            break;
          }
          case 26: {
            road.data.proto.SectionStatInfo.Builder subBuilder = null;
            if (trafficFlowStatMapElementOneOfCase_ == 3) {
              subBuilder = ((road.data.proto.SectionStatInfo) trafficFlowStatMapElementOneOf_).toBuilder();
            }
            trafficFlowStatMapElementOneOf_ =
                input.readMessage(road.data.proto.SectionStatInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.SectionStatInfo) trafficFlowStatMapElementOneOf_);
              trafficFlowStatMapElementOneOf_ = subBuilder.buildPartial();
            }
            trafficFlowStatMapElementOneOfCase_ = 3;
            break;
          }
          case 34: {
            road.data.proto.LinkStatInfo.Builder subBuilder = null;
            if (trafficFlowStatMapElementOneOfCase_ == 4) {
              subBuilder = ((road.data.proto.LinkStatInfo) trafficFlowStatMapElementOneOf_).toBuilder();
            }
            trafficFlowStatMapElementOneOf_ =
                input.readMessage(road.data.proto.LinkStatInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.LinkStatInfo) trafficFlowStatMapElementOneOf_);
              trafficFlowStatMapElementOneOf_ = subBuilder.buildPartial();
            }
            trafficFlowStatMapElementOneOfCase_ = 4;
            break;
          }
          case 42: {
            road.data.proto.NodeStatInfo.Builder subBuilder = null;
            if (trafficFlowStatMapElementOneOfCase_ == 5) {
              subBuilder = ((road.data.proto.NodeStatInfo) trafficFlowStatMapElementOneOf_).toBuilder();
            }
            trafficFlowStatMapElementOneOf_ =
                input.readMessage(road.data.proto.NodeStatInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.NodeStatInfo) trafficFlowStatMapElementOneOf_);
              trafficFlowStatMapElementOneOf_ = subBuilder.buildPartial();
            }
            trafficFlowStatMapElementOneOfCase_ = 5;
            break;
          }
          case 50: {
            road.data.proto.MovementStatInfo.Builder subBuilder = null;
            if (trafficFlowStatMapElementOneOfCase_ == 6) {
              subBuilder = ((road.data.proto.MovementStatInfo) trafficFlowStatMapElementOneOf_).toBuilder();
            }
            trafficFlowStatMapElementOneOf_ =
                input.readMessage(road.data.proto.MovementStatInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom((road.data.proto.MovementStatInfo) trafficFlowStatMapElementOneOf_);
              trafficFlowStatMapElementOneOf_ = subBuilder.buildPartial();
            }
            trafficFlowStatMapElementOneOfCase_ = 6;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatMapElement_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatMapElement_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.TrafficFlowStatMapElement.class, road.data.proto.TrafficFlowStatMapElement.Builder.class);
  }

  private int trafficFlowStatMapElementOneOfCase_ = 0;
  private java.lang.Object trafficFlowStatMapElementOneOf_;
  public enum TrafficFlowStatMapElementOneOfCase
      implements com.google.protobuf.Internal.EnumLite {
    DETECTORAREA(1),
    LANESTATINFO(2),
    SECTIONSTATINFO(3),
    LINKSTATINFO(4),
    NODESTATINFO(5),
    MOVEMENTSTATINFO(6),
    TRAFFICFLOWSTATMAPELEMENTONEOF_NOT_SET(0);
    private final int value;
    private TrafficFlowStatMapElementOneOfCase(int value) {
      this.value = value;
    }
    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TrafficFlowStatMapElementOneOfCase valueOf(int value) {
      return forNumber(value);
    }

    public static TrafficFlowStatMapElementOneOfCase forNumber(int value) {
      switch (value) {
        case 1: return DETECTORAREA;
        case 2: return LANESTATINFO;
        case 3: return SECTIONSTATINFO;
        case 4: return LINKSTATINFO;
        case 5: return NODESTATINFO;
        case 6: return MOVEMENTSTATINFO;
        case 0: return TRAFFICFLOWSTATMAPELEMENTONEOF_NOT_SET;
        default: return null;
      }
    }
    public int getNumber() {
      return this.value;
    }
  };

  public TrafficFlowStatMapElementOneOfCase
  getTrafficFlowStatMapElementOneOfCase() {
    return TrafficFlowStatMapElementOneOfCase.forNumber(
        trafficFlowStatMapElementOneOfCase_);
  }

  public static final int DETECTORAREA_FIELD_NUMBER = 1;
  /**
   * <pre>
   * 检测区对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
   */
  public boolean hasDetectorArea() {
    return trafficFlowStatMapElementOneOfCase_ == 1;
  }
  /**
   * <pre>
   * 检测区对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
   */
  public road.data.proto.DetectorArea getDetectorArea() {
    if (trafficFlowStatMapElementOneOfCase_ == 1) {
       return (road.data.proto.DetectorArea) trafficFlowStatMapElementOneOf_;
    }
    return road.data.proto.DetectorArea.getDefaultInstance();
  }
  /**
   * <pre>
   * 检测区对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
   */
  public road.data.proto.DetectorAreaOrBuilder getDetectorAreaOrBuilder() {
    if (trafficFlowStatMapElementOneOfCase_ == 1) {
       return (road.data.proto.DetectorArea) trafficFlowStatMapElementOneOf_;
    }
    return road.data.proto.DetectorArea.getDefaultInstance();
  }

  public static final int LANESTATINFO_FIELD_NUMBER = 2;
  /**
   * <pre>
   *车道对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
   */
  public boolean hasLaneStatInfo() {
    return trafficFlowStatMapElementOneOfCase_ == 2;
  }
  /**
   * <pre>
   *车道对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
   */
  public road.data.proto.LaneStatInfo getLaneStatInfo() {
    if (trafficFlowStatMapElementOneOfCase_ == 2) {
       return (road.data.proto.LaneStatInfo) trafficFlowStatMapElementOneOf_;
    }
    return road.data.proto.LaneStatInfo.getDefaultInstance();
  }
  /**
   * <pre>
   *车道对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
   */
  public road.data.proto.LaneStatInfoOrBuilder getLaneStatInfoOrBuilder() {
    if (trafficFlowStatMapElementOneOfCase_ == 2) {
       return (road.data.proto.LaneStatInfo) trafficFlowStatMapElementOneOf_;
    }
    return road.data.proto.LaneStatInfo.getDefaultInstance();
  }

  public static final int SECTIONSTATINFO_FIELD_NUMBER = 3;
  /**
   * <pre>
   *路段分段对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
   */
  public boolean hasSectionStatInfo() {
    return trafficFlowStatMapElementOneOfCase_ == 3;
  }
  /**
   * <pre>
   *路段分段对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
   */
  public road.data.proto.SectionStatInfo getSectionStatInfo() {
    if (trafficFlowStatMapElementOneOfCase_ == 3) {
       return (road.data.proto.SectionStatInfo) trafficFlowStatMapElementOneOf_;
    }
    return road.data.proto.SectionStatInfo.getDefaultInstance();
  }
  /**
   * <pre>
   *路段分段对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
   */
  public road.data.proto.SectionStatInfoOrBuilder getSectionStatInfoOrBuilder() {
    if (trafficFlowStatMapElementOneOfCase_ == 3) {
       return (road.data.proto.SectionStatInfo) trafficFlowStatMapElementOneOf_;
    }
    return road.data.proto.SectionStatInfo.getDefaultInstance();
  }

  public static final int LINKSTATINFO_FIELD_NUMBER = 4;
  /**
   * <pre>
   *有向路段对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
   */
  public boolean hasLinkStatInfo() {
    return trafficFlowStatMapElementOneOfCase_ == 4;
  }
  /**
   * <pre>
   *有向路段对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
   */
  public road.data.proto.LinkStatInfo getLinkStatInfo() {
    if (trafficFlowStatMapElementOneOfCase_ == 4) {
       return (road.data.proto.LinkStatInfo) trafficFlowStatMapElementOneOf_;
    }
    return road.data.proto.LinkStatInfo.getDefaultInstance();
  }
  /**
   * <pre>
   *有向路段对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
   */
  public road.data.proto.LinkStatInfoOrBuilder getLinkStatInfoOrBuilder() {
    if (trafficFlowStatMapElementOneOfCase_ == 4) {
       return (road.data.proto.LinkStatInfo) trafficFlowStatMapElementOneOf_;
    }
    return road.data.proto.LinkStatInfo.getDefaultInstance();
  }

  public static final int NODESTATINFO_FIELD_NUMBER = 5;
  /**
   * <pre>
   * 路口对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
   */
  public boolean hasNodeStatInfo() {
    return trafficFlowStatMapElementOneOfCase_ == 5;
  }
  /**
   * <pre>
   * 路口对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
   */
  public road.data.proto.NodeStatInfo getNodeStatInfo() {
    if (trafficFlowStatMapElementOneOfCase_ == 5) {
       return (road.data.proto.NodeStatInfo) trafficFlowStatMapElementOneOf_;
    }
    return road.data.proto.NodeStatInfo.getDefaultInstance();
  }
  /**
   * <pre>
   * 路口对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
   */
  public road.data.proto.NodeStatInfoOrBuilder getNodeStatInfoOrBuilder() {
    if (trafficFlowStatMapElementOneOfCase_ == 5) {
       return (road.data.proto.NodeStatInfo) trafficFlowStatMapElementOneOf_;
    }
    return road.data.proto.NodeStatInfo.getDefaultInstance();
  }

  public static final int MOVEMENTSTATINFO_FIELD_NUMBER = 6;
  /**
   * <pre>
   *一条路段与下游路段的连接关系
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
   */
  public boolean hasMovementStatInfo() {
    return trafficFlowStatMapElementOneOfCase_ == 6;
  }
  /**
   * <pre>
   *一条路段与下游路段的连接关系
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
   */
  public road.data.proto.MovementStatInfo getMovementStatInfo() {
    if (trafficFlowStatMapElementOneOfCase_ == 6) {
       return (road.data.proto.MovementStatInfo) trafficFlowStatMapElementOneOf_;
    }
    return road.data.proto.MovementStatInfo.getDefaultInstance();
  }
  /**
   * <pre>
   *一条路段与下游路段的连接关系
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
   */
  public road.data.proto.MovementStatInfoOrBuilder getMovementStatInfoOrBuilder() {
    if (trafficFlowStatMapElementOneOfCase_ == 6) {
       return (road.data.proto.MovementStatInfo) trafficFlowStatMapElementOneOf_;
    }
    return road.data.proto.MovementStatInfo.getDefaultInstance();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (trafficFlowStatMapElementOneOfCase_ == 1) {
      output.writeMessage(1, (road.data.proto.DetectorArea) trafficFlowStatMapElementOneOf_);
    }
    if (trafficFlowStatMapElementOneOfCase_ == 2) {
      output.writeMessage(2, (road.data.proto.LaneStatInfo) trafficFlowStatMapElementOneOf_);
    }
    if (trafficFlowStatMapElementOneOfCase_ == 3) {
      output.writeMessage(3, (road.data.proto.SectionStatInfo) trafficFlowStatMapElementOneOf_);
    }
    if (trafficFlowStatMapElementOneOfCase_ == 4) {
      output.writeMessage(4, (road.data.proto.LinkStatInfo) trafficFlowStatMapElementOneOf_);
    }
    if (trafficFlowStatMapElementOneOfCase_ == 5) {
      output.writeMessage(5, (road.data.proto.NodeStatInfo) trafficFlowStatMapElementOneOf_);
    }
    if (trafficFlowStatMapElementOneOfCase_ == 6) {
      output.writeMessage(6, (road.data.proto.MovementStatInfo) trafficFlowStatMapElementOneOf_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (trafficFlowStatMapElementOneOfCase_ == 1) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, (road.data.proto.DetectorArea) trafficFlowStatMapElementOneOf_);
    }
    if (trafficFlowStatMapElementOneOfCase_ == 2) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, (road.data.proto.LaneStatInfo) trafficFlowStatMapElementOneOf_);
    }
    if (trafficFlowStatMapElementOneOfCase_ == 3) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, (road.data.proto.SectionStatInfo) trafficFlowStatMapElementOneOf_);
    }
    if (trafficFlowStatMapElementOneOfCase_ == 4) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, (road.data.proto.LinkStatInfo) trafficFlowStatMapElementOneOf_);
    }
    if (trafficFlowStatMapElementOneOfCase_ == 5) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, (road.data.proto.NodeStatInfo) trafficFlowStatMapElementOneOf_);
    }
    if (trafficFlowStatMapElementOneOfCase_ == 6) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, (road.data.proto.MovementStatInfo) trafficFlowStatMapElementOneOf_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.TrafficFlowStatMapElement)) {
      return super.equals(obj);
    }
    road.data.proto.TrafficFlowStatMapElement other = (road.data.proto.TrafficFlowStatMapElement) obj;

    if (!getTrafficFlowStatMapElementOneOfCase().equals(other.getTrafficFlowStatMapElementOneOfCase())) return false;
    switch (trafficFlowStatMapElementOneOfCase_) {
      case 1:
        if (!getDetectorArea()
            .equals(other.getDetectorArea())) return false;
        break;
      case 2:
        if (!getLaneStatInfo()
            .equals(other.getLaneStatInfo())) return false;
        break;
      case 3:
        if (!getSectionStatInfo()
            .equals(other.getSectionStatInfo())) return false;
        break;
      case 4:
        if (!getLinkStatInfo()
            .equals(other.getLinkStatInfo())) return false;
        break;
      case 5:
        if (!getNodeStatInfo()
            .equals(other.getNodeStatInfo())) return false;
        break;
      case 6:
        if (!getMovementStatInfo()
            .equals(other.getMovementStatInfo())) return false;
        break;
      case 0:
      default:
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    switch (trafficFlowStatMapElementOneOfCase_) {
      case 1:
        hash = (37 * hash) + DETECTORAREA_FIELD_NUMBER;
        hash = (53 * hash) + getDetectorArea().hashCode();
        break;
      case 2:
        hash = (37 * hash) + LANESTATINFO_FIELD_NUMBER;
        hash = (53 * hash) + getLaneStatInfo().hashCode();
        break;
      case 3:
        hash = (37 * hash) + SECTIONSTATINFO_FIELD_NUMBER;
        hash = (53 * hash) + getSectionStatInfo().hashCode();
        break;
      case 4:
        hash = (37 * hash) + LINKSTATINFO_FIELD_NUMBER;
        hash = (53 * hash) + getLinkStatInfo().hashCode();
        break;
      case 5:
        hash = (37 * hash) + NODESTATINFO_FIELD_NUMBER;
        hash = (53 * hash) + getNodeStatInfo().hashCode();
        break;
      case 6:
        hash = (37 * hash) + MOVEMENTSTATINFO_FIELD_NUMBER;
        hash = (53 * hash) + getMovementStatInfo().hashCode();
        break;
      case 0:
      default:
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.TrafficFlowStatMapElement parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowStatMapElement parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatMapElement parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowStatMapElement parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatMapElement parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowStatMapElement parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatMapElement parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowStatMapElement parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatMapElement parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowStatMapElement parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatMapElement parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowStatMapElement parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.TrafficFlowStatMapElement prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *路网元素绑定TrafficFlowStatMapElement 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.TrafficFlowStatMapElement}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.TrafficFlowStatMapElement)
      road.data.proto.TrafficFlowStatMapElementOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatMapElement_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatMapElement_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.TrafficFlowStatMapElement.class, road.data.proto.TrafficFlowStatMapElement.Builder.class);
    }

    // Construct using road.data.proto.TrafficFlowStatMapElement.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      trafficFlowStatMapElementOneOfCase_ = 0;
      trafficFlowStatMapElementOneOf_ = null;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatMapElement_descriptor;
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowStatMapElement getDefaultInstanceForType() {
      return road.data.proto.TrafficFlowStatMapElement.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowStatMapElement build() {
      road.data.proto.TrafficFlowStatMapElement result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowStatMapElement buildPartial() {
      road.data.proto.TrafficFlowStatMapElement result = new road.data.proto.TrafficFlowStatMapElement(this);
      if (trafficFlowStatMapElementOneOfCase_ == 1) {
        if (detectorAreaBuilder_ == null) {
          result.trafficFlowStatMapElementOneOf_ = trafficFlowStatMapElementOneOf_;
        } else {
          result.trafficFlowStatMapElementOneOf_ = detectorAreaBuilder_.build();
        }
      }
      if (trafficFlowStatMapElementOneOfCase_ == 2) {
        if (laneStatInfoBuilder_ == null) {
          result.trafficFlowStatMapElementOneOf_ = trafficFlowStatMapElementOneOf_;
        } else {
          result.trafficFlowStatMapElementOneOf_ = laneStatInfoBuilder_.build();
        }
      }
      if (trafficFlowStatMapElementOneOfCase_ == 3) {
        if (sectionStatInfoBuilder_ == null) {
          result.trafficFlowStatMapElementOneOf_ = trafficFlowStatMapElementOneOf_;
        } else {
          result.trafficFlowStatMapElementOneOf_ = sectionStatInfoBuilder_.build();
        }
      }
      if (trafficFlowStatMapElementOneOfCase_ == 4) {
        if (linkStatInfoBuilder_ == null) {
          result.trafficFlowStatMapElementOneOf_ = trafficFlowStatMapElementOneOf_;
        } else {
          result.trafficFlowStatMapElementOneOf_ = linkStatInfoBuilder_.build();
        }
      }
      if (trafficFlowStatMapElementOneOfCase_ == 5) {
        if (nodeStatInfoBuilder_ == null) {
          result.trafficFlowStatMapElementOneOf_ = trafficFlowStatMapElementOneOf_;
        } else {
          result.trafficFlowStatMapElementOneOf_ = nodeStatInfoBuilder_.build();
        }
      }
      if (trafficFlowStatMapElementOneOfCase_ == 6) {
        if (movementStatInfoBuilder_ == null) {
          result.trafficFlowStatMapElementOneOf_ = trafficFlowStatMapElementOneOf_;
        } else {
          result.trafficFlowStatMapElementOneOf_ = movementStatInfoBuilder_.build();
        }
      }
      result.trafficFlowStatMapElementOneOfCase_ = trafficFlowStatMapElementOneOfCase_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.TrafficFlowStatMapElement) {
        return mergeFrom((road.data.proto.TrafficFlowStatMapElement)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.TrafficFlowStatMapElement other) {
      if (other == road.data.proto.TrafficFlowStatMapElement.getDefaultInstance()) return this;
      switch (other.getTrafficFlowStatMapElementOneOfCase()) {
        case DETECTORAREA: {
          mergeDetectorArea(other.getDetectorArea());
          break;
        }
        case LANESTATINFO: {
          mergeLaneStatInfo(other.getLaneStatInfo());
          break;
        }
        case SECTIONSTATINFO: {
          mergeSectionStatInfo(other.getSectionStatInfo());
          break;
        }
        case LINKSTATINFO: {
          mergeLinkStatInfo(other.getLinkStatInfo());
          break;
        }
        case NODESTATINFO: {
          mergeNodeStatInfo(other.getNodeStatInfo());
          break;
        }
        case MOVEMENTSTATINFO: {
          mergeMovementStatInfo(other.getMovementStatInfo());
          break;
        }
        case TRAFFICFLOWSTATMAPELEMENTONEOF_NOT_SET: {
          break;
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.TrafficFlowStatMapElement parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.TrafficFlowStatMapElement) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int trafficFlowStatMapElementOneOfCase_ = 0;
    private java.lang.Object trafficFlowStatMapElementOneOf_;
    public TrafficFlowStatMapElementOneOfCase
        getTrafficFlowStatMapElementOneOfCase() {
      return TrafficFlowStatMapElementOneOfCase.forNumber(
          trafficFlowStatMapElementOneOfCase_);
    }

    public Builder clearTrafficFlowStatMapElementOneOf() {
      trafficFlowStatMapElementOneOfCase_ = 0;
      trafficFlowStatMapElementOneOf_ = null;
      onChanged();
      return this;
    }


    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.DetectorArea, road.data.proto.DetectorArea.Builder, road.data.proto.DetectorAreaOrBuilder> detectorAreaBuilder_;
    /**
     * <pre>
     * 检测区对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
     */
    public boolean hasDetectorArea() {
      return trafficFlowStatMapElementOneOfCase_ == 1;
    }
    /**
     * <pre>
     * 检测区对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
     */
    public road.data.proto.DetectorArea getDetectorArea() {
      if (detectorAreaBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 1) {
          return (road.data.proto.DetectorArea) trafficFlowStatMapElementOneOf_;
        }
        return road.data.proto.DetectorArea.getDefaultInstance();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 1) {
          return detectorAreaBuilder_.getMessage();
        }
        return road.data.proto.DetectorArea.getDefaultInstance();
      }
    }
    /**
     * <pre>
     * 检测区对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
     */
    public Builder setDetectorArea(road.data.proto.DetectorArea value) {
      if (detectorAreaBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        trafficFlowStatMapElementOneOf_ = value;
        onChanged();
      } else {
        detectorAreaBuilder_.setMessage(value);
      }
      trafficFlowStatMapElementOneOfCase_ = 1;
      return this;
    }
    /**
     * <pre>
     * 检测区对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
     */
    public Builder setDetectorArea(
        road.data.proto.DetectorArea.Builder builderForValue) {
      if (detectorAreaBuilder_ == null) {
        trafficFlowStatMapElementOneOf_ = builderForValue.build();
        onChanged();
      } else {
        detectorAreaBuilder_.setMessage(builderForValue.build());
      }
      trafficFlowStatMapElementOneOfCase_ = 1;
      return this;
    }
    /**
     * <pre>
     * 检测区对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
     */
    public Builder mergeDetectorArea(road.data.proto.DetectorArea value) {
      if (detectorAreaBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 1 &&
            trafficFlowStatMapElementOneOf_ != road.data.proto.DetectorArea.getDefaultInstance()) {
          trafficFlowStatMapElementOneOf_ = road.data.proto.DetectorArea.newBuilder((road.data.proto.DetectorArea) trafficFlowStatMapElementOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          trafficFlowStatMapElementOneOf_ = value;
        }
        onChanged();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 1) {
          detectorAreaBuilder_.mergeFrom(value);
        }
        detectorAreaBuilder_.setMessage(value);
      }
      trafficFlowStatMapElementOneOfCase_ = 1;
      return this;
    }
    /**
     * <pre>
     * 检测区对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
     */
    public Builder clearDetectorArea() {
      if (detectorAreaBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 1) {
          trafficFlowStatMapElementOneOfCase_ = 0;
          trafficFlowStatMapElementOneOf_ = null;
          onChanged();
        }
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 1) {
          trafficFlowStatMapElementOneOfCase_ = 0;
          trafficFlowStatMapElementOneOf_ = null;
        }
        detectorAreaBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 检测区对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
     */
    public road.data.proto.DetectorArea.Builder getDetectorAreaBuilder() {
      return getDetectorAreaFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 检测区对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
     */
    public road.data.proto.DetectorAreaOrBuilder getDetectorAreaOrBuilder() {
      if ((trafficFlowStatMapElementOneOfCase_ == 1) && (detectorAreaBuilder_ != null)) {
        return detectorAreaBuilder_.getMessageOrBuilder();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 1) {
          return (road.data.proto.DetectorArea) trafficFlowStatMapElementOneOf_;
        }
        return road.data.proto.DetectorArea.getDefaultInstance();
      }
    }
    /**
     * <pre>
     * 检测区对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.DetectorArea, road.data.proto.DetectorArea.Builder, road.data.proto.DetectorAreaOrBuilder> 
        getDetectorAreaFieldBuilder() {
      if (detectorAreaBuilder_ == null) {
        if (!(trafficFlowStatMapElementOneOfCase_ == 1)) {
          trafficFlowStatMapElementOneOf_ = road.data.proto.DetectorArea.getDefaultInstance();
        }
        detectorAreaBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.DetectorArea, road.data.proto.DetectorArea.Builder, road.data.proto.DetectorAreaOrBuilder>(
                (road.data.proto.DetectorArea) trafficFlowStatMapElementOneOf_,
                getParentForChildren(),
                isClean());
        trafficFlowStatMapElementOneOf_ = null;
      }
      trafficFlowStatMapElementOneOfCase_ = 1;
      onChanged();;
      return detectorAreaBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneStatInfo, road.data.proto.LaneStatInfo.Builder, road.data.proto.LaneStatInfoOrBuilder> laneStatInfoBuilder_;
    /**
     * <pre>
     *车道对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
     */
    public boolean hasLaneStatInfo() {
      return trafficFlowStatMapElementOneOfCase_ == 2;
    }
    /**
     * <pre>
     *车道对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
     */
    public road.data.proto.LaneStatInfo getLaneStatInfo() {
      if (laneStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 2) {
          return (road.data.proto.LaneStatInfo) trafficFlowStatMapElementOneOf_;
        }
        return road.data.proto.LaneStatInfo.getDefaultInstance();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 2) {
          return laneStatInfoBuilder_.getMessage();
        }
        return road.data.proto.LaneStatInfo.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *车道对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
     */
    public Builder setLaneStatInfo(road.data.proto.LaneStatInfo value) {
      if (laneStatInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        trafficFlowStatMapElementOneOf_ = value;
        onChanged();
      } else {
        laneStatInfoBuilder_.setMessage(value);
      }
      trafficFlowStatMapElementOneOfCase_ = 2;
      return this;
    }
    /**
     * <pre>
     *车道对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
     */
    public Builder setLaneStatInfo(
        road.data.proto.LaneStatInfo.Builder builderForValue) {
      if (laneStatInfoBuilder_ == null) {
        trafficFlowStatMapElementOneOf_ = builderForValue.build();
        onChanged();
      } else {
        laneStatInfoBuilder_.setMessage(builderForValue.build());
      }
      trafficFlowStatMapElementOneOfCase_ = 2;
      return this;
    }
    /**
     * <pre>
     *车道对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
     */
    public Builder mergeLaneStatInfo(road.data.proto.LaneStatInfo value) {
      if (laneStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 2 &&
            trafficFlowStatMapElementOneOf_ != road.data.proto.LaneStatInfo.getDefaultInstance()) {
          trafficFlowStatMapElementOneOf_ = road.data.proto.LaneStatInfo.newBuilder((road.data.proto.LaneStatInfo) trafficFlowStatMapElementOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          trafficFlowStatMapElementOneOf_ = value;
        }
        onChanged();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 2) {
          laneStatInfoBuilder_.mergeFrom(value);
        }
        laneStatInfoBuilder_.setMessage(value);
      }
      trafficFlowStatMapElementOneOfCase_ = 2;
      return this;
    }
    /**
     * <pre>
     *车道对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
     */
    public Builder clearLaneStatInfo() {
      if (laneStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 2) {
          trafficFlowStatMapElementOneOfCase_ = 0;
          trafficFlowStatMapElementOneOf_ = null;
          onChanged();
        }
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 2) {
          trafficFlowStatMapElementOneOfCase_ = 0;
          trafficFlowStatMapElementOneOf_ = null;
        }
        laneStatInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *车道对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
     */
    public road.data.proto.LaneStatInfo.Builder getLaneStatInfoBuilder() {
      return getLaneStatInfoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *车道对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
     */
    public road.data.proto.LaneStatInfoOrBuilder getLaneStatInfoOrBuilder() {
      if ((trafficFlowStatMapElementOneOfCase_ == 2) && (laneStatInfoBuilder_ != null)) {
        return laneStatInfoBuilder_.getMessageOrBuilder();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 2) {
          return (road.data.proto.LaneStatInfo) trafficFlowStatMapElementOneOf_;
        }
        return road.data.proto.LaneStatInfo.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *车道对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LaneStatInfo, road.data.proto.LaneStatInfo.Builder, road.data.proto.LaneStatInfoOrBuilder> 
        getLaneStatInfoFieldBuilder() {
      if (laneStatInfoBuilder_ == null) {
        if (!(trafficFlowStatMapElementOneOfCase_ == 2)) {
          trafficFlowStatMapElementOneOf_ = road.data.proto.LaneStatInfo.getDefaultInstance();
        }
        laneStatInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LaneStatInfo, road.data.proto.LaneStatInfo.Builder, road.data.proto.LaneStatInfoOrBuilder>(
                (road.data.proto.LaneStatInfo) trafficFlowStatMapElementOneOf_,
                getParentForChildren(),
                isClean());
        trafficFlowStatMapElementOneOf_ = null;
      }
      trafficFlowStatMapElementOneOfCase_ = 2;
      onChanged();;
      return laneStatInfoBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.SectionStatInfo, road.data.proto.SectionStatInfo.Builder, road.data.proto.SectionStatInfoOrBuilder> sectionStatInfoBuilder_;
    /**
     * <pre>
     *路段分段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
     */
    public boolean hasSectionStatInfo() {
      return trafficFlowStatMapElementOneOfCase_ == 3;
    }
    /**
     * <pre>
     *路段分段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
     */
    public road.data.proto.SectionStatInfo getSectionStatInfo() {
      if (sectionStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 3) {
          return (road.data.proto.SectionStatInfo) trafficFlowStatMapElementOneOf_;
        }
        return road.data.proto.SectionStatInfo.getDefaultInstance();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 3) {
          return sectionStatInfoBuilder_.getMessage();
        }
        return road.data.proto.SectionStatInfo.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *路段分段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
     */
    public Builder setSectionStatInfo(road.data.proto.SectionStatInfo value) {
      if (sectionStatInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        trafficFlowStatMapElementOneOf_ = value;
        onChanged();
      } else {
        sectionStatInfoBuilder_.setMessage(value);
      }
      trafficFlowStatMapElementOneOfCase_ = 3;
      return this;
    }
    /**
     * <pre>
     *路段分段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
     */
    public Builder setSectionStatInfo(
        road.data.proto.SectionStatInfo.Builder builderForValue) {
      if (sectionStatInfoBuilder_ == null) {
        trafficFlowStatMapElementOneOf_ = builderForValue.build();
        onChanged();
      } else {
        sectionStatInfoBuilder_.setMessage(builderForValue.build());
      }
      trafficFlowStatMapElementOneOfCase_ = 3;
      return this;
    }
    /**
     * <pre>
     *路段分段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
     */
    public Builder mergeSectionStatInfo(road.data.proto.SectionStatInfo value) {
      if (sectionStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 3 &&
            trafficFlowStatMapElementOneOf_ != road.data.proto.SectionStatInfo.getDefaultInstance()) {
          trafficFlowStatMapElementOneOf_ = road.data.proto.SectionStatInfo.newBuilder((road.data.proto.SectionStatInfo) trafficFlowStatMapElementOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          trafficFlowStatMapElementOneOf_ = value;
        }
        onChanged();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 3) {
          sectionStatInfoBuilder_.mergeFrom(value);
        }
        sectionStatInfoBuilder_.setMessage(value);
      }
      trafficFlowStatMapElementOneOfCase_ = 3;
      return this;
    }
    /**
     * <pre>
     *路段分段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
     */
    public Builder clearSectionStatInfo() {
      if (sectionStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 3) {
          trafficFlowStatMapElementOneOfCase_ = 0;
          trafficFlowStatMapElementOneOf_ = null;
          onChanged();
        }
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 3) {
          trafficFlowStatMapElementOneOfCase_ = 0;
          trafficFlowStatMapElementOneOf_ = null;
        }
        sectionStatInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *路段分段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
     */
    public road.data.proto.SectionStatInfo.Builder getSectionStatInfoBuilder() {
      return getSectionStatInfoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *路段分段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
     */
    public road.data.proto.SectionStatInfoOrBuilder getSectionStatInfoOrBuilder() {
      if ((trafficFlowStatMapElementOneOfCase_ == 3) && (sectionStatInfoBuilder_ != null)) {
        return sectionStatInfoBuilder_.getMessageOrBuilder();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 3) {
          return (road.data.proto.SectionStatInfo) trafficFlowStatMapElementOneOf_;
        }
        return road.data.proto.SectionStatInfo.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *路段分段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.SectionStatInfo, road.data.proto.SectionStatInfo.Builder, road.data.proto.SectionStatInfoOrBuilder> 
        getSectionStatInfoFieldBuilder() {
      if (sectionStatInfoBuilder_ == null) {
        if (!(trafficFlowStatMapElementOneOfCase_ == 3)) {
          trafficFlowStatMapElementOneOf_ = road.data.proto.SectionStatInfo.getDefaultInstance();
        }
        sectionStatInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.SectionStatInfo, road.data.proto.SectionStatInfo.Builder, road.data.proto.SectionStatInfoOrBuilder>(
                (road.data.proto.SectionStatInfo) trafficFlowStatMapElementOneOf_,
                getParentForChildren(),
                isClean());
        trafficFlowStatMapElementOneOf_ = null;
      }
      trafficFlowStatMapElementOneOfCase_ = 3;
      onChanged();;
      return sectionStatInfoBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LinkStatInfo, road.data.proto.LinkStatInfo.Builder, road.data.proto.LinkStatInfoOrBuilder> linkStatInfoBuilder_;
    /**
     * <pre>
     *有向路段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
     */
    public boolean hasLinkStatInfo() {
      return trafficFlowStatMapElementOneOfCase_ == 4;
    }
    /**
     * <pre>
     *有向路段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
     */
    public road.data.proto.LinkStatInfo getLinkStatInfo() {
      if (linkStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 4) {
          return (road.data.proto.LinkStatInfo) trafficFlowStatMapElementOneOf_;
        }
        return road.data.proto.LinkStatInfo.getDefaultInstance();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 4) {
          return linkStatInfoBuilder_.getMessage();
        }
        return road.data.proto.LinkStatInfo.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *有向路段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
     */
    public Builder setLinkStatInfo(road.data.proto.LinkStatInfo value) {
      if (linkStatInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        trafficFlowStatMapElementOneOf_ = value;
        onChanged();
      } else {
        linkStatInfoBuilder_.setMessage(value);
      }
      trafficFlowStatMapElementOneOfCase_ = 4;
      return this;
    }
    /**
     * <pre>
     *有向路段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
     */
    public Builder setLinkStatInfo(
        road.data.proto.LinkStatInfo.Builder builderForValue) {
      if (linkStatInfoBuilder_ == null) {
        trafficFlowStatMapElementOneOf_ = builderForValue.build();
        onChanged();
      } else {
        linkStatInfoBuilder_.setMessage(builderForValue.build());
      }
      trafficFlowStatMapElementOneOfCase_ = 4;
      return this;
    }
    /**
     * <pre>
     *有向路段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
     */
    public Builder mergeLinkStatInfo(road.data.proto.LinkStatInfo value) {
      if (linkStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 4 &&
            trafficFlowStatMapElementOneOf_ != road.data.proto.LinkStatInfo.getDefaultInstance()) {
          trafficFlowStatMapElementOneOf_ = road.data.proto.LinkStatInfo.newBuilder((road.data.proto.LinkStatInfo) trafficFlowStatMapElementOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          trafficFlowStatMapElementOneOf_ = value;
        }
        onChanged();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 4) {
          linkStatInfoBuilder_.mergeFrom(value);
        }
        linkStatInfoBuilder_.setMessage(value);
      }
      trafficFlowStatMapElementOneOfCase_ = 4;
      return this;
    }
    /**
     * <pre>
     *有向路段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
     */
    public Builder clearLinkStatInfo() {
      if (linkStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 4) {
          trafficFlowStatMapElementOneOfCase_ = 0;
          trafficFlowStatMapElementOneOf_ = null;
          onChanged();
        }
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 4) {
          trafficFlowStatMapElementOneOfCase_ = 0;
          trafficFlowStatMapElementOneOf_ = null;
        }
        linkStatInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *有向路段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
     */
    public road.data.proto.LinkStatInfo.Builder getLinkStatInfoBuilder() {
      return getLinkStatInfoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *有向路段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
     */
    public road.data.proto.LinkStatInfoOrBuilder getLinkStatInfoOrBuilder() {
      if ((trafficFlowStatMapElementOneOfCase_ == 4) && (linkStatInfoBuilder_ != null)) {
        return linkStatInfoBuilder_.getMessageOrBuilder();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 4) {
          return (road.data.proto.LinkStatInfo) trafficFlowStatMapElementOneOf_;
        }
        return road.data.proto.LinkStatInfo.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *有向路段对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.LinkStatInfo, road.data.proto.LinkStatInfo.Builder, road.data.proto.LinkStatInfoOrBuilder> 
        getLinkStatInfoFieldBuilder() {
      if (linkStatInfoBuilder_ == null) {
        if (!(trafficFlowStatMapElementOneOfCase_ == 4)) {
          trafficFlowStatMapElementOneOf_ = road.data.proto.LinkStatInfo.getDefaultInstance();
        }
        linkStatInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.LinkStatInfo, road.data.proto.LinkStatInfo.Builder, road.data.proto.LinkStatInfoOrBuilder>(
                (road.data.proto.LinkStatInfo) trafficFlowStatMapElementOneOf_,
                getParentForChildren(),
                isClean());
        trafficFlowStatMapElementOneOf_ = null;
      }
      trafficFlowStatMapElementOneOfCase_ = 4;
      onChanged();;
      return linkStatInfoBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeStatInfo, road.data.proto.NodeStatInfo.Builder, road.data.proto.NodeStatInfoOrBuilder> nodeStatInfoBuilder_;
    /**
     * <pre>
     * 路口对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
     */
    public boolean hasNodeStatInfo() {
      return trafficFlowStatMapElementOneOfCase_ == 5;
    }
    /**
     * <pre>
     * 路口对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
     */
    public road.data.proto.NodeStatInfo getNodeStatInfo() {
      if (nodeStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 5) {
          return (road.data.proto.NodeStatInfo) trafficFlowStatMapElementOneOf_;
        }
        return road.data.proto.NodeStatInfo.getDefaultInstance();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 5) {
          return nodeStatInfoBuilder_.getMessage();
        }
        return road.data.proto.NodeStatInfo.getDefaultInstance();
      }
    }
    /**
     * <pre>
     * 路口对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
     */
    public Builder setNodeStatInfo(road.data.proto.NodeStatInfo value) {
      if (nodeStatInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        trafficFlowStatMapElementOneOf_ = value;
        onChanged();
      } else {
        nodeStatInfoBuilder_.setMessage(value);
      }
      trafficFlowStatMapElementOneOfCase_ = 5;
      return this;
    }
    /**
     * <pre>
     * 路口对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
     */
    public Builder setNodeStatInfo(
        road.data.proto.NodeStatInfo.Builder builderForValue) {
      if (nodeStatInfoBuilder_ == null) {
        trafficFlowStatMapElementOneOf_ = builderForValue.build();
        onChanged();
      } else {
        nodeStatInfoBuilder_.setMessage(builderForValue.build());
      }
      trafficFlowStatMapElementOneOfCase_ = 5;
      return this;
    }
    /**
     * <pre>
     * 路口对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
     */
    public Builder mergeNodeStatInfo(road.data.proto.NodeStatInfo value) {
      if (nodeStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 5 &&
            trafficFlowStatMapElementOneOf_ != road.data.proto.NodeStatInfo.getDefaultInstance()) {
          trafficFlowStatMapElementOneOf_ = road.data.proto.NodeStatInfo.newBuilder((road.data.proto.NodeStatInfo) trafficFlowStatMapElementOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          trafficFlowStatMapElementOneOf_ = value;
        }
        onChanged();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 5) {
          nodeStatInfoBuilder_.mergeFrom(value);
        }
        nodeStatInfoBuilder_.setMessage(value);
      }
      trafficFlowStatMapElementOneOfCase_ = 5;
      return this;
    }
    /**
     * <pre>
     * 路口对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
     */
    public Builder clearNodeStatInfo() {
      if (nodeStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 5) {
          trafficFlowStatMapElementOneOfCase_ = 0;
          trafficFlowStatMapElementOneOf_ = null;
          onChanged();
        }
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 5) {
          trafficFlowStatMapElementOneOfCase_ = 0;
          trafficFlowStatMapElementOneOf_ = null;
        }
        nodeStatInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 路口对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
     */
    public road.data.proto.NodeStatInfo.Builder getNodeStatInfoBuilder() {
      return getNodeStatInfoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 路口对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
     */
    public road.data.proto.NodeStatInfoOrBuilder getNodeStatInfoOrBuilder() {
      if ((trafficFlowStatMapElementOneOfCase_ == 5) && (nodeStatInfoBuilder_ != null)) {
        return nodeStatInfoBuilder_.getMessageOrBuilder();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 5) {
          return (road.data.proto.NodeStatInfo) trafficFlowStatMapElementOneOf_;
        }
        return road.data.proto.NodeStatInfo.getDefaultInstance();
      }
    }
    /**
     * <pre>
     * 路口对象
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.NodeStatInfo, road.data.proto.NodeStatInfo.Builder, road.data.proto.NodeStatInfoOrBuilder> 
        getNodeStatInfoFieldBuilder() {
      if (nodeStatInfoBuilder_ == null) {
        if (!(trafficFlowStatMapElementOneOfCase_ == 5)) {
          trafficFlowStatMapElementOneOf_ = road.data.proto.NodeStatInfo.getDefaultInstance();
        }
        nodeStatInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.NodeStatInfo, road.data.proto.NodeStatInfo.Builder, road.data.proto.NodeStatInfoOrBuilder>(
                (road.data.proto.NodeStatInfo) trafficFlowStatMapElementOneOf_,
                getParentForChildren(),
                isClean());
        trafficFlowStatMapElementOneOf_ = null;
      }
      trafficFlowStatMapElementOneOfCase_ = 5;
      onChanged();;
      return nodeStatInfoBuilder_;
    }

    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MovementStatInfo, road.data.proto.MovementStatInfo.Builder, road.data.proto.MovementStatInfoOrBuilder> movementStatInfoBuilder_;
    /**
     * <pre>
     *一条路段与下游路段的连接关系
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
     */
    public boolean hasMovementStatInfo() {
      return trafficFlowStatMapElementOneOfCase_ == 6;
    }
    /**
     * <pre>
     *一条路段与下游路段的连接关系
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
     */
    public road.data.proto.MovementStatInfo getMovementStatInfo() {
      if (movementStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 6) {
          return (road.data.proto.MovementStatInfo) trafficFlowStatMapElementOneOf_;
        }
        return road.data.proto.MovementStatInfo.getDefaultInstance();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 6) {
          return movementStatInfoBuilder_.getMessage();
        }
        return road.data.proto.MovementStatInfo.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *一条路段与下游路段的连接关系
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
     */
    public Builder setMovementStatInfo(road.data.proto.MovementStatInfo value) {
      if (movementStatInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        trafficFlowStatMapElementOneOf_ = value;
        onChanged();
      } else {
        movementStatInfoBuilder_.setMessage(value);
      }
      trafficFlowStatMapElementOneOfCase_ = 6;
      return this;
    }
    /**
     * <pre>
     *一条路段与下游路段的连接关系
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
     */
    public Builder setMovementStatInfo(
        road.data.proto.MovementStatInfo.Builder builderForValue) {
      if (movementStatInfoBuilder_ == null) {
        trafficFlowStatMapElementOneOf_ = builderForValue.build();
        onChanged();
      } else {
        movementStatInfoBuilder_.setMessage(builderForValue.build());
      }
      trafficFlowStatMapElementOneOfCase_ = 6;
      return this;
    }
    /**
     * <pre>
     *一条路段与下游路段的连接关系
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
     */
    public Builder mergeMovementStatInfo(road.data.proto.MovementStatInfo value) {
      if (movementStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 6 &&
            trafficFlowStatMapElementOneOf_ != road.data.proto.MovementStatInfo.getDefaultInstance()) {
          trafficFlowStatMapElementOneOf_ = road.data.proto.MovementStatInfo.newBuilder((road.data.proto.MovementStatInfo) trafficFlowStatMapElementOneOf_)
              .mergeFrom(value).buildPartial();
        } else {
          trafficFlowStatMapElementOneOf_ = value;
        }
        onChanged();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 6) {
          movementStatInfoBuilder_.mergeFrom(value);
        }
        movementStatInfoBuilder_.setMessage(value);
      }
      trafficFlowStatMapElementOneOfCase_ = 6;
      return this;
    }
    /**
     * <pre>
     *一条路段与下游路段的连接关系
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
     */
    public Builder clearMovementStatInfo() {
      if (movementStatInfoBuilder_ == null) {
        if (trafficFlowStatMapElementOneOfCase_ == 6) {
          trafficFlowStatMapElementOneOfCase_ = 0;
          trafficFlowStatMapElementOneOf_ = null;
          onChanged();
        }
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 6) {
          trafficFlowStatMapElementOneOfCase_ = 0;
          trafficFlowStatMapElementOneOf_ = null;
        }
        movementStatInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *一条路段与下游路段的连接关系
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
     */
    public road.data.proto.MovementStatInfo.Builder getMovementStatInfoBuilder() {
      return getMovementStatInfoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *一条路段与下游路段的连接关系
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
     */
    public road.data.proto.MovementStatInfoOrBuilder getMovementStatInfoOrBuilder() {
      if ((trafficFlowStatMapElementOneOfCase_ == 6) && (movementStatInfoBuilder_ != null)) {
        return movementStatInfoBuilder_.getMessageOrBuilder();
      } else {
        if (trafficFlowStatMapElementOneOfCase_ == 6) {
          return (road.data.proto.MovementStatInfo) trafficFlowStatMapElementOneOf_;
        }
        return road.data.proto.MovementStatInfo.getDefaultInstance();
      }
    }
    /**
     * <pre>
     *一条路段与下游路段的连接关系
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.MovementStatInfo, road.data.proto.MovementStatInfo.Builder, road.data.proto.MovementStatInfoOrBuilder> 
        getMovementStatInfoFieldBuilder() {
      if (movementStatInfoBuilder_ == null) {
        if (!(trafficFlowStatMapElementOneOfCase_ == 6)) {
          trafficFlowStatMapElementOneOf_ = road.data.proto.MovementStatInfo.getDefaultInstance();
        }
        movementStatInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.MovementStatInfo, road.data.proto.MovementStatInfo.Builder, road.data.proto.MovementStatInfoOrBuilder>(
                (road.data.proto.MovementStatInfo) trafficFlowStatMapElementOneOf_,
                getParentForChildren(),
                isClean());
        trafficFlowStatMapElementOneOf_ = null;
      }
      trafficFlowStatMapElementOneOfCase_ = 6;
      onChanged();;
      return movementStatInfoBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.TrafficFlowStatMapElement)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.TrafficFlowStatMapElement)
  private static final road.data.proto.TrafficFlowStatMapElement DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.TrafficFlowStatMapElement();
  }

  public static road.data.proto.TrafficFlowStatMapElement getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TrafficFlowStatMapElement>
      PARSER = new com.google.protobuf.AbstractParser<TrafficFlowStatMapElement>() {
    @java.lang.Override
    public TrafficFlowStatMapElement parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new TrafficFlowStatMapElement(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<TrafficFlowStatMapElement> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TrafficFlowStatMapElement> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.TrafficFlowStatMapElement getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

