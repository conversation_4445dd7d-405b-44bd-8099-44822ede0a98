// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface TrafficFlowStatMapElementOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.TrafficFlowStatMapElement)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 检测区对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
   */
  boolean hasDetectorArea();
  /**
   * <pre>
   * 检测区对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
   */
  road.data.proto.DetectorArea getDetectorArea();
  /**
   * <pre>
   * 检测区对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DetectorArea detectorArea = 1;</code>
   */
  road.data.proto.DetectorAreaOrBuilder getDetectorAreaOrBuilder();

  /**
   * <pre>
   *车道对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
   */
  boolean hasLaneStatInfo();
  /**
   * <pre>
   *车道对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
   */
  road.data.proto.LaneStatInfo getLaneStatInfo();
  /**
   * <pre>
   *车道对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LaneStatInfo laneStatInfo = 2;</code>
   */
  road.data.proto.LaneStatInfoOrBuilder getLaneStatInfoOrBuilder();

  /**
   * <pre>
   *路段分段对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
   */
  boolean hasSectionStatInfo();
  /**
   * <pre>
   *路段分段对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
   */
  road.data.proto.SectionStatInfo getSectionStatInfo();
  /**
   * <pre>
   *路段分段对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.SectionStatInfo sectionStatInfo = 3;</code>
   */
  road.data.proto.SectionStatInfoOrBuilder getSectionStatInfoOrBuilder();

  /**
   * <pre>
   *有向路段对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
   */
  boolean hasLinkStatInfo();
  /**
   * <pre>
   *有向路段对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
   */
  road.data.proto.LinkStatInfo getLinkStatInfo();
  /**
   * <pre>
   *有向路段对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.LinkStatInfo linkStatInfo = 4;</code>
   */
  road.data.proto.LinkStatInfoOrBuilder getLinkStatInfoOrBuilder();

  /**
   * <pre>
   * 路口对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
   */
  boolean hasNodeStatInfo();
  /**
   * <pre>
   * 路口对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
   */
  road.data.proto.NodeStatInfo getNodeStatInfo();
  /**
   * <pre>
   * 路口对象
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.NodeStatInfo nodeStatInfo = 5;</code>
   */
  road.data.proto.NodeStatInfoOrBuilder getNodeStatInfoOrBuilder();

  /**
   * <pre>
   *一条路段与下游路段的连接关系
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
   */
  boolean hasMovementStatInfo();
  /**
   * <pre>
   *一条路段与下游路段的连接关系
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
   */
  road.data.proto.MovementStatInfo getMovementStatInfo();
  /**
   * <pre>
   *一条路段与下游路段的连接关系
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MovementStatInfo movementStatInfo = 6;</code>
   */
  road.data.proto.MovementStatInfoOrBuilder getMovementStatInfoOrBuilder();

  public road.data.proto.TrafficFlowStatMapElement.TrafficFlowStatMapElementOneOfCase getTrafficFlowStatMapElementOneOfCase();
}
