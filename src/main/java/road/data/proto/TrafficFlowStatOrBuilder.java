// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface TrafficFlowStatOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.TrafficFlowStat)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 本组交通流统计值绑定的路网元素
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
   */
  boolean hasMapElement();
  /**
   * <pre>
   * 本组交通流统计值绑定的路网元素
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
   */
  road.data.proto.TrafficFlowStatMapElement getMapElement();
  /**
   * <pre>
   * 本组交通流统计值绑定的路网元素
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatMapElement mapElement = 1;</code>
   */
  road.data.proto.TrafficFlowStatMapElementOrBuilder getMapElementOrBuilder();

  /**
   * <pre>
   *路网元素类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapElementType mapElementType = 2;</code>
   */
  int getMapElementTypeValue();
  /**
   * <pre>
   *路网元素类型
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.MapElementType mapElementType = 2;</code>
   */
  road.data.proto.MapElementType getMapElementType();

  /**
   * <pre>
   * 路侧单元检测到的交通参与者类型。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 3;</code>
   */
  int getPtcTypeValue();
  /**
   * <pre>
   * 路侧单元检测到的交通参与者类型。
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.ParticipantType ptcType = 3;</code>
   */
  road.data.proto.ParticipantType getPtcType();

  /**
   * <pre>
   * 可选，参考VehicleType（0表示对所有车辆作聚合）
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 4;</code>
   */
  int getVehicleTypeValue();
  /**
   * <pre>
   * 可选，参考VehicleType（0表示对所有车辆作聚合）
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.VehicleType vehicleType = 4;</code>
   */
  road.data.proto.VehicleType getVehicleType();

  /**
   * <pre>
   *产生消息的最早时间，UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 5;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   *流量，0.01 pcu/h,
   * </pre>
   *
   * <code>uint32 volume = 6;</code>
   */
  int getVolume();

  /**
   * <pre>
   * 可选，地点速度，0.01m/s
   * </pre>
   *
   * <code>uint32 speedPoint = 7;</code>
   */
  int getSpeedPoint();

  /**
   * <pre>
   * 区域平均速度，0.01m/s
   * </pre>
   *
   * <code>uint32 speedArea = 8;</code>
   */
  int getSpeedArea();

  /**
   * <pre>
   * 密度，0.01 pcu/km
   * </pre>
   *
   * <code>uint32 density = 9;</code>
   */
  int getDensity();

  /**
   * <pre>
   *可选，行程时间，单位：0.1s/ vehicle
   * </pre>
   *
   * <code>uint32 travelTime = 10;</code>
   */
  int getTravelTime();

  /**
   * <pre>
   *  平均延误，0.01 sec/vehicle
   * </pre>
   *
   * <code>uint32 delay = 11;</code>
   */
  int getDelay();

  /**
   * <pre>
   *可选，排队长度，0.1m
   * </pre>
   *
   * <code>uint32 queueLength = 12;</code>
   */
  int getQueueLength();

  /**
   * <pre>
   *可选，排队车辆数
   * </pre>
   *
   * <code>uint32 queueInt = 13;</code>
   */
  int getQueueInt();

  /**
   * <pre>
   * 拥堵指数，%
   * </pre>
   *
   * <code>uint32 congestion = 14;</code>
   */
  int getCongestion();

  /**
   * <pre>
   *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
   */
  boolean hasTrafficFlowExtension();
  /**
   * <pre>
   *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
   */
  road.data.proto.TrafficFlowExtension getTrafficFlowExtension();
  /**
   * <pre>
   *可选，扩展交通流指标，包含扩展的车道、进口道、路口、信号灯的交通流指标
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowExtension trafficFlowExtension = 15;</code>
   */
  road.data.proto.TrafficFlowExtensionOrBuilder getTrafficFlowExtensionOrBuilder();

  /**
   * <pre>
   *可选，车头时距，单位：0.01s
   * </pre>
   *
   * <code>uint32 timeHeadway = 16;</code>
   */
  int getTimeHeadway();

  /**
   * <pre>
   *可选，车头间距，单位：0.01m
   * </pre>
   *
   * <code>uint32 spaceHeadway = 17;</code>
   */
  int getSpaceHeadway();

  /**
   * <pre>
   *可选，停车次数，次
   * </pre>
   *
   * <code>uint32 stopNums = 18;</code>
   */
  int getStopNums();
}
