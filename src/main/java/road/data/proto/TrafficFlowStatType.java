// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *统计方式信息 TrafficFlowStatType   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.TrafficFlowStatType}
 */
public  final class TrafficFlowStatType extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.TrafficFlowStatType)
    TrafficFlowStatTypeOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TrafficFlowStatType.newBuilder() to construct.
  private TrafficFlowStatType(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private TrafficFlowStatType() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new TrafficFlowStatType();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private TrafficFlowStatType(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            road.data.proto.TrafficFlowStatByInterval.Builder subBuilder = null;
            if (interval_ != null) {
              subBuilder = interval_.toBuilder();
            }
            interval_ = input.readMessage(road.data.proto.TrafficFlowStatByInterval.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(interval_);
              interval_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            road.data.proto.TrafficFlowStatBySignalCycle.Builder subBuilder = null;
            if (sequence_ != null) {
              subBuilder = sequence_.toBuilder();
            }
            sequence_ = input.readMessage(road.data.proto.TrafficFlowStatBySignalCycle.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(sequence_);
              sequence_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatType_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatType_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.TrafficFlowStatType.class, road.data.proto.TrafficFlowStatType.Builder.class);
  }

  public static final int INTERVAL_FIELD_NUMBER = 1;
  private road.data.proto.TrafficFlowStatByInterval interval_;
  /**
   * <pre>
   *可选，按照固定时间间隔进行统计
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
   */
  public boolean hasInterval() {
    return interval_ != null;
  }
  /**
   * <pre>
   *可选，按照固定时间间隔进行统计
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
   */
  public road.data.proto.TrafficFlowStatByInterval getInterval() {
    return interval_ == null ? road.data.proto.TrafficFlowStatByInterval.getDefaultInstance() : interval_;
  }
  /**
   * <pre>
   *可选，按照固定时间间隔进行统计
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
   */
  public road.data.proto.TrafficFlowStatByIntervalOrBuilder getIntervalOrBuilder() {
    return getInterval();
  }

  public static final int SEQUENCE_FIELD_NUMBER = 2;
  private road.data.proto.TrafficFlowStatBySignalCycle sequence_;
  /**
   * <pre>
   *可选，按信号控制周期方式统计
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
   */
  public boolean hasSequence() {
    return sequence_ != null;
  }
  /**
   * <pre>
   *可选，按信号控制周期方式统计
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
   */
  public road.data.proto.TrafficFlowStatBySignalCycle getSequence() {
    return sequence_ == null ? road.data.proto.TrafficFlowStatBySignalCycle.getDefaultInstance() : sequence_;
  }
  /**
   * <pre>
   *可选，按信号控制周期方式统计
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
   */
  public road.data.proto.TrafficFlowStatBySignalCycleOrBuilder getSequenceOrBuilder() {
    return getSequence();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (interval_ != null) {
      output.writeMessage(1, getInterval());
    }
    if (sequence_ != null) {
      output.writeMessage(2, getSequence());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (interval_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getInterval());
    }
    if (sequence_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getSequence());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.TrafficFlowStatType)) {
      return super.equals(obj);
    }
    road.data.proto.TrafficFlowStatType other = (road.data.proto.TrafficFlowStatType) obj;

    if (hasInterval() != other.hasInterval()) return false;
    if (hasInterval()) {
      if (!getInterval()
          .equals(other.getInterval())) return false;
    }
    if (hasSequence() != other.hasSequence()) return false;
    if (hasSequence()) {
      if (!getSequence()
          .equals(other.getSequence())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasInterval()) {
      hash = (37 * hash) + INTERVAL_FIELD_NUMBER;
      hash = (53 * hash) + getInterval().hashCode();
    }
    if (hasSequence()) {
      hash = (37 * hash) + SEQUENCE_FIELD_NUMBER;
      hash = (53 * hash) + getSequence().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.TrafficFlowStatType parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowStatType parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatType parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowStatType parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatType parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.TrafficFlowStatType parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatType parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowStatType parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatType parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowStatType parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.TrafficFlowStatType parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.TrafficFlowStatType parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.TrafficFlowStatType prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *统计方式信息 TrafficFlowStatType   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.TrafficFlowStatType}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.TrafficFlowStatType)
      road.data.proto.TrafficFlowStatTypeOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatType_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatType_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.TrafficFlowStatType.class, road.data.proto.TrafficFlowStatType.Builder.class);
    }

    // Construct using road.data.proto.TrafficFlowStatType.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (intervalBuilder_ == null) {
        interval_ = null;
      } else {
        interval_ = null;
        intervalBuilder_ = null;
      }
      if (sequenceBuilder_ == null) {
        sequence_ = null;
      } else {
        sequence_ = null;
        sequenceBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_TrafficFlowStatType_descriptor;
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowStatType getDefaultInstanceForType() {
      return road.data.proto.TrafficFlowStatType.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowStatType build() {
      road.data.proto.TrafficFlowStatType result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.TrafficFlowStatType buildPartial() {
      road.data.proto.TrafficFlowStatType result = new road.data.proto.TrafficFlowStatType(this);
      if (intervalBuilder_ == null) {
        result.interval_ = interval_;
      } else {
        result.interval_ = intervalBuilder_.build();
      }
      if (sequenceBuilder_ == null) {
        result.sequence_ = sequence_;
      } else {
        result.sequence_ = sequenceBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.TrafficFlowStatType) {
        return mergeFrom((road.data.proto.TrafficFlowStatType)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.TrafficFlowStatType other) {
      if (other == road.data.proto.TrafficFlowStatType.getDefaultInstance()) return this;
      if (other.hasInterval()) {
        mergeInterval(other.getInterval());
      }
      if (other.hasSequence()) {
        mergeSequence(other.getSequence());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.TrafficFlowStatType parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.TrafficFlowStatType) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private road.data.proto.TrafficFlowStatByInterval interval_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.TrafficFlowStatByInterval, road.data.proto.TrafficFlowStatByInterval.Builder, road.data.proto.TrafficFlowStatByIntervalOrBuilder> intervalBuilder_;
    /**
     * <pre>
     *可选，按照固定时间间隔进行统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
     */
    public boolean hasInterval() {
      return intervalBuilder_ != null || interval_ != null;
    }
    /**
     * <pre>
     *可选，按照固定时间间隔进行统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
     */
    public road.data.proto.TrafficFlowStatByInterval getInterval() {
      if (intervalBuilder_ == null) {
        return interval_ == null ? road.data.proto.TrafficFlowStatByInterval.getDefaultInstance() : interval_;
      } else {
        return intervalBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，按照固定时间间隔进行统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
     */
    public Builder setInterval(road.data.proto.TrafficFlowStatByInterval value) {
      if (intervalBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        interval_ = value;
        onChanged();
      } else {
        intervalBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，按照固定时间间隔进行统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
     */
    public Builder setInterval(
        road.data.proto.TrafficFlowStatByInterval.Builder builderForValue) {
      if (intervalBuilder_ == null) {
        interval_ = builderForValue.build();
        onChanged();
      } else {
        intervalBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，按照固定时间间隔进行统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
     */
    public Builder mergeInterval(road.data.proto.TrafficFlowStatByInterval value) {
      if (intervalBuilder_ == null) {
        if (interval_ != null) {
          interval_ =
            road.data.proto.TrafficFlowStatByInterval.newBuilder(interval_).mergeFrom(value).buildPartial();
        } else {
          interval_ = value;
        }
        onChanged();
      } else {
        intervalBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，按照固定时间间隔进行统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
     */
    public Builder clearInterval() {
      if (intervalBuilder_ == null) {
        interval_ = null;
        onChanged();
      } else {
        interval_ = null;
        intervalBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，按照固定时间间隔进行统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
     */
    public road.data.proto.TrafficFlowStatByInterval.Builder getIntervalBuilder() {
      
      onChanged();
      return getIntervalFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，按照固定时间间隔进行统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
     */
    public road.data.proto.TrafficFlowStatByIntervalOrBuilder getIntervalOrBuilder() {
      if (intervalBuilder_ != null) {
        return intervalBuilder_.getMessageOrBuilder();
      } else {
        return interval_ == null ?
            road.data.proto.TrafficFlowStatByInterval.getDefaultInstance() : interval_;
      }
    }
    /**
     * <pre>
     *可选，按照固定时间间隔进行统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.TrafficFlowStatByInterval, road.data.proto.TrafficFlowStatByInterval.Builder, road.data.proto.TrafficFlowStatByIntervalOrBuilder> 
        getIntervalFieldBuilder() {
      if (intervalBuilder_ == null) {
        intervalBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.TrafficFlowStatByInterval, road.data.proto.TrafficFlowStatByInterval.Builder, road.data.proto.TrafficFlowStatByIntervalOrBuilder>(
                getInterval(),
                getParentForChildren(),
                isClean());
        interval_ = null;
      }
      return intervalBuilder_;
    }

    private road.data.proto.TrafficFlowStatBySignalCycle sequence_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.TrafficFlowStatBySignalCycle, road.data.proto.TrafficFlowStatBySignalCycle.Builder, road.data.proto.TrafficFlowStatBySignalCycleOrBuilder> sequenceBuilder_;
    /**
     * <pre>
     *可选，按信号控制周期方式统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
     */
    public boolean hasSequence() {
      return sequenceBuilder_ != null || sequence_ != null;
    }
    /**
     * <pre>
     *可选，按信号控制周期方式统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
     */
    public road.data.proto.TrafficFlowStatBySignalCycle getSequence() {
      if (sequenceBuilder_ == null) {
        return sequence_ == null ? road.data.proto.TrafficFlowStatBySignalCycle.getDefaultInstance() : sequence_;
      } else {
        return sequenceBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，按信号控制周期方式统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
     */
    public Builder setSequence(road.data.proto.TrafficFlowStatBySignalCycle value) {
      if (sequenceBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        sequence_ = value;
        onChanged();
      } else {
        sequenceBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，按信号控制周期方式统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
     */
    public Builder setSequence(
        road.data.proto.TrafficFlowStatBySignalCycle.Builder builderForValue) {
      if (sequenceBuilder_ == null) {
        sequence_ = builderForValue.build();
        onChanged();
      } else {
        sequenceBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，按信号控制周期方式统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
     */
    public Builder mergeSequence(road.data.proto.TrafficFlowStatBySignalCycle value) {
      if (sequenceBuilder_ == null) {
        if (sequence_ != null) {
          sequence_ =
            road.data.proto.TrafficFlowStatBySignalCycle.newBuilder(sequence_).mergeFrom(value).buildPartial();
        } else {
          sequence_ = value;
        }
        onChanged();
      } else {
        sequenceBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，按信号控制周期方式统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
     */
    public Builder clearSequence() {
      if (sequenceBuilder_ == null) {
        sequence_ = null;
        onChanged();
      } else {
        sequence_ = null;
        sequenceBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，按信号控制周期方式统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
     */
    public road.data.proto.TrafficFlowStatBySignalCycle.Builder getSequenceBuilder() {
      
      onChanged();
      return getSequenceFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，按信号控制周期方式统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
     */
    public road.data.proto.TrafficFlowStatBySignalCycleOrBuilder getSequenceOrBuilder() {
      if (sequenceBuilder_ != null) {
        return sequenceBuilder_.getMessageOrBuilder();
      } else {
        return sequence_ == null ?
            road.data.proto.TrafficFlowStatBySignalCycle.getDefaultInstance() : sequence_;
      }
    }
    /**
     * <pre>
     *可选，按信号控制周期方式统计
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.TrafficFlowStatBySignalCycle, road.data.proto.TrafficFlowStatBySignalCycle.Builder, road.data.proto.TrafficFlowStatBySignalCycleOrBuilder> 
        getSequenceFieldBuilder() {
      if (sequenceBuilder_ == null) {
        sequenceBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.TrafficFlowStatBySignalCycle, road.data.proto.TrafficFlowStatBySignalCycle.Builder, road.data.proto.TrafficFlowStatBySignalCycleOrBuilder>(
                getSequence(),
                getParentForChildren(),
                isClean());
        sequence_ = null;
      }
      return sequenceBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.TrafficFlowStatType)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.TrafficFlowStatType)
  private static final road.data.proto.TrafficFlowStatType DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.TrafficFlowStatType();
  }

  public static road.data.proto.TrafficFlowStatType getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TrafficFlowStatType>
      PARSER = new com.google.protobuf.AbstractParser<TrafficFlowStatType>() {
    @java.lang.Override
    public TrafficFlowStatType parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new TrafficFlowStatType(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<TrafficFlowStatType> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TrafficFlowStatType> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.TrafficFlowStatType getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

