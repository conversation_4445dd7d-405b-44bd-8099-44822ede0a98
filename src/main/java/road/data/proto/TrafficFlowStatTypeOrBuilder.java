// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface TrafficFlowStatTypeOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.TrafficFlowStatType)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *可选，按照固定时间间隔进行统计
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
   */
  boolean hasInterval();
  /**
   * <pre>
   *可选，按照固定时间间隔进行统计
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
   */
  road.data.proto.TrafficFlowStatByInterval getInterval();
  /**
   * <pre>
   *可选，按照固定时间间隔进行统计
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatByInterval interval = 1;</code>
   */
  road.data.proto.TrafficFlowStatByIntervalOrBuilder getIntervalOrBuilder();

  /**
   * <pre>
   *可选，按信号控制周期方式统计
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
   */
  boolean hasSequence();
  /**
   * <pre>
   *可选，按信号控制周期方式统计
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
   */
  road.data.proto.TrafficFlowStatBySignalCycle getSequence();
  /**
   * <pre>
   *可选，按信号控制周期方式统计
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.TrafficFlowStatBySignalCycle sequence = 2;</code>
   */
  road.data.proto.TrafficFlowStatBySignalCycleOrBuilder getSequenceOrBuilder();
}
