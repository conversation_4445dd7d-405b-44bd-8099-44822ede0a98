// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public final class V2X {
  private V2X() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_RsiTimeDetails_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_RsiTimeDetails_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_Position3D_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_Position3D_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_PositionConfidenceSet_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_PositionConfidenceSet_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ParticipantSize_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ParticipantSize_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ParticipantSizeConfidence_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ParticipantSizeConfidence_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_Polygon_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_Polygon_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_DetectorArea_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_DetectorArea_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_RegulatorySpeedLimit_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_RegulatorySpeedLimit_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_AccelerationSet4Way_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_AccelerationSet4Way_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_AccelerationConfidence_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_AccelerationConfidence_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_MotionConfidenceSet_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_MotionConfidenceSet_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_VehicleSize_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_VehicleSize_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_NodeReferenceId_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_NodeReferenceId_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_MapLocation_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_MapLocation_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_PhaseId_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_PhaseId_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ReferenceLanes_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ReferenceLanes_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ReferencePath_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ReferencePath_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ReferenceLink_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ReferenceLink_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_AllowedManeuvers_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_AllowedManeuvers_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneStatInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneStatInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_SectionStatInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_SectionStatInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LinkStatInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LinkStatInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_NodeStatInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_NodeStatInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_MovementStatInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_MovementStatInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_TrafficFlowStatByInterval_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_TrafficFlowStatByInterval_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_TrafficFlowStatBySignalCycle_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_TrafficFlowStatBySignalCycle_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_TrafficFlowStatType_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_TrafficFlowStatType_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_TrafficFlowStatMapElement_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_TrafficFlowStatMapElement_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneIndexAdded_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneIndexAdded_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LinkIndexAdded_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LinkIndexAdded_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_MovementIndexAdded_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_MovementIndexAdded_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_NodeIndexAdded_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_NodeIndexAdded_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_SignalControlIndexAdded_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_SignalControlIndexAdded_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_TrafficFlowExtension_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_TrafficFlowExtension_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_TrafficFlowStat_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_TrafficFlowStat_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_TrafficFlow_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_TrafficFlow_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_TimeCountingDown_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_TimeCountingDown_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_PhaseState_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_PhaseState_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_Phase_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_Phase_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_IntersectionState_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_IntersectionState_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_SpatData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_SpatData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LocalTimePoint_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LocalTimePoint_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_PeriodictimeSpan_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_PeriodictimeSpan_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_SingleTimeSpan_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_SingleTimeSpan_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_OptimTimeType_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_OptimTimeType_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_MovementEx_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_MovementEx_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_OptimPhase_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_OptimPhase_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_OptimData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_OptimData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_SignalScheme_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_SignalScheme_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_BrakeSystemStatus_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_BrakeSystemStatus_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_PositionAccuracy_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_PositionAccuracy_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ThrottleSystemStatus_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ThrottleSystemStatus_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_BsmData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_BsmData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_PathHistoryPoint_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_PathHistoryPoint_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ParticipantData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ParticipantData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ObstacleData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ObstacleData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ObjIdValue_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ObjIdValue_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_RteData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_RteData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_RtsData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_RtsData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ConnectingLane_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ConnectingLane_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_Connection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_Connection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneAttributesParking_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneAttributesParking_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneAttributesCrosswalk_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneAttributesCrosswalk_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneAttributesBike_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneAttributesBike_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneAttributesSidewalk_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneAttributesSidewalk_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneAttributesBarrier_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneAttributesBarrier_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneAttributesStriping_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneAttributesStriping_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneAttributesTrackedVehicle_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneAttributesTrackedVehicle_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneAttributesVehicle_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneAttributesVehicle_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneTypeAttributes_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneTypeAttributes_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneSharing_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneSharing_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneType_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneType_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneAttributes_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneAttributes_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneBoundary_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneBoundary_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_Lane_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_Lane_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_SignalWaitingLane_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_SignalWaitingLane_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ConnectingLaneEx_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ConnectingLaneEx_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ConnectionEx_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ConnectionEx_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_STPoint_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_STPoint_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneEx_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneEx_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_Movement_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_Movement_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_Section_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_Section_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LinkEx_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LinkEx_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_Link_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_Link_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ProhibitedZone_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ProhibitedZone_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_Node_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_Node_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_MAP_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_MAP_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_MapData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_MapData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ReqLaneChange_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ReqLaneChange_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ReqClearTheWay_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ReqClearTheWay_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ReqSignalPriority_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ReqSignalPriority_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ReqSensorSharing_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ReqSensorSharing_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ParkingRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ParkingRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ParkingType_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ParkingType_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ReqParkingArea_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ReqParkingArea_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_ReqInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_ReqInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_DriveRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_DriveRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_DriveBehavior_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_DriveBehavior_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_PathPlanningPoint_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_PathPlanningPoint_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_PathPlanning_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_PathPlanning_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_IarData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_IarData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_VirData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_VirData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_DriveSuggestion_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_DriveSuggestion_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_CoordinationInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_CoordinationInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_VehicleCoordination_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_VehicleCoordination_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_LaneCoordination_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_LaneCoordination_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_RscData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_RscData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_CamData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_CamData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_StatusData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_StatusData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_DenmData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_DenmData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_RsiReply_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_RsiReply_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_RsuRsmReply_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_RsuRsmReply_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_RsmReply_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_RsmReply_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_seisys_v2x_pb_MonitorStatsData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_seisys_v2x_pb_MonitorStatsData_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\tv2x.proto\022\020cn.seisys.v2x.pb\"q\n\016RsiTime" +
      "Details\022\021\n\tstartTime\030\001 \001(\004\022\017\n\007endTime\030\002 " +
      "\001(\004\022;\n\021endTimeConfidence\030\003 \001(\0162 .cn.seis" +
      "ys.v2x.pb.TimeConfidence\"3\n\nPosition3D\022\013" +
      "\n\003lat\030\001 \001(\005\022\013\n\003lon\030\002 \001(\005\022\013\n\003ele\030\003 \001(\005\"\373\006" +
      "\n\025PositionConfidenceSet\022M\n\tposConfid\030\001 \001" +
      "(\0162:.cn.seisys.v2x.pb.PositionConfidence" +
      "Set.PositionConfidence\022N\n\teleConfid\030\002 \001(" +
      "\0162;.cn.seisys.v2x.pb.PositionConfidenceS" +
      "et.ElevationConfidence\"\337\002\n\022PositionConfi" +
      "dence\022\032\n\026UNAVAILABLE_POS_CONFID\020\000\022\023\n\017POS" +
      "_CONFID_500M\020\001\022\023\n\017POS_CONFID_200M\020\002\022\023\n\017P" +
      "OS_CONFID_100M\020\003\022\022\n\016POS_CONFID_50M\020\004\022\022\n\016" +
      "POS_CONFID_20M\020\005\022\022\n\016POS_CONFID_10M\020\006\022\021\n\r" +
      "POS_CONFID_5M\020\007\022\021\n\rPOS_CONFID_2M\020\010\022\021\n\rPO" +
      "S_CONFID_1M\020\t\022\023\n\017POS_CONFID_50CM\020\n\022\023\n\017PO" +
      "S_CONFID_20CM\020\013\022\023\n\017POS_CONFID_10CM\020\014\022\022\n\016" +
      "POS_CONFID_5CM\020\r\022\022\n\016POS_CONFID_2CM\020\016\022\022\n\016" +
      "POS_CONFID_1CM\020\017\"\340\002\n\023ElevationConfidence" +
      "\022\032\n\026UNAVAILABLE_ELE_CONFID\020\000\022\023\n\017ELE_CONF" +
      "ID_500M\020\001\022\023\n\017ELE_CONFID_200M\020\002\022\023\n\017ELE_CO" +
      "NFID_100M\020\003\022\022\n\016ELE_CONFID_50M\020\004\022\022\n\016ELE_C" +
      "ONFID_20M\020\005\022\022\n\016ELE_CONFID_10M\020\006\022\021\n\rELE_C" +
      "ONFID_5M\020\007\022\021\n\rELE_CONFID_2M\020\010\022\021\n\rELE_CON" +
      "FID_1M\020\t\022\023\n\017ELE_CONFID_50CM\020\n\022\023\n\017ELE_CON" +
      "FID_20CM\020\013\022\023\n\017ELE_CONFID_10CM\020\014\022\022\n\016ELE_C" +
      "ONFID_5CM\020\r\022\022\n\016ELE_CONFID_2CM\020\016\022\022\n\016ELE_C" +
      "ONFID_1CM\020\017\"@\n\017ParticipantSize\022\r\n\005width\030" +
      "\001 \001(\r\022\016\n\006length\030\002 \001(\r\022\016\n\006height\030\003 \001(\r\"\214\005" +
      "\n\031ParticipantSizeConfidence\022T\n\013widthConf" +
      "id\030\001 \001(\0162?.cn.seisys.v2x.pb.ParticipantS" +
      "izeConfidence.SizeValueConfidence\022U\n\014len" +
      "gthConfid\030\002 \001(\0162?.cn.seisys.v2x.pb.Parti" +
      "cipantSizeConfidence.SizeValueConfidence" +
      "\022U\n\014heightConfid\030\003 \001(\0162?.cn.seisys.v2x.p" +
      "b.ParticipantSizeConfidence.SizeValueCon" +
      "fidence\"\352\002\n\023SizeValueConfidence\022\033\n\027SIZE_" +
      "CONFID_UNAVAILABLE\020\000\022\026\n\022SIZE_CONFID_100_" +
      "00\020\001\022\026\n\022SIZE_CONFID_050_00\020\002\022\026\n\022SIZE_CON" +
      "FID_020_00\020\003\022\026\n\022SIZE_CONFID_010_00\020\004\022\026\n\022" +
      "SIZE_CONFID_005_00\020\005\022\026\n\022SIZE_CONFID_002_" +
      "00\020\006\022\026\n\022SIZE_CONFID_001_00\020\007\022\026\n\022SIZE_CON" +
      "FID_000_50\020\010\022\026\n\022SIZE_CONFID_000_20\020\t\022\026\n\022" +
      "SIZE_CONFID_000_10\020\n\022\026\n\022SIZE_CONFID_000_" +
      "05\020\013\022\026\n\022SIZE_CONFID_000_02\020\014\022\026\n\022SIZE_CON" +
      "FID_000_01\020\r\"4\n\007Polygon\022)\n\003pos\030\001 \003(\0132\034.c" +
      "n.seisys.v2x.pb.Position3D\"\236\001\n\014DetectorA" +
      "rea\022\016\n\006areaId\030\001 \001(\005\022\017\n\007setTime\030\002 \001(\003\022*\n\007" +
      "polygon\030\003 \001(\0132\031.cn.seisys.v2x.pb.Polygon" +
      "\0221\n\006nodeId\030\004 \001(\0132!.cn.seisys.v2x.pb.Node" +
      "ReferenceId\022\016\n\006laneId\030\005 \001(\005\"\233\004\n\024Regulato" +
      "rySpeedLimit\022M\n\016speedLimitType\030\001 \001(\01625.c" +
      "n.seisys.v2x.pb.RegulatorySpeedLimit.Spe" +
      "edLimitType\022\r\n\005speed\030\002 \001(\005\"\244\003\n\016SpeedLimi" +
      "tType\022\027\n\023SPEED_LIMIT_UNKNOWN\020\000\022\034\n\030MAX_SP" +
      "EED_IN_SCHOOL_ZONE\020\001\0225\n1MAX_SPEED_INSCHO" +
      "OL_ZONE_WHEN_CHILDREN_ARE_PRESENT\020\002\022!\n\035M" +
      "AX_SPEED_INCONSTRUCTION_ZONE\020\003\022\025\n\021VEHICL" +
      "E_MIN_SPEED\020\004\022\021\n\rVEHICLE_SPEED\020\005\022\033\n\027VEHI" +
      "CLE_NIGHT_MAX_SPEED\020\006\022\023\n\017TRUCK_MIN_SPEED" +
      "\020\007\022\023\n\017TRUCK_MAX_SPEED\020\010\022\031\n\025TRUCK_NIGHT_M" +
      "AX_SPEED\020\t\022$\n VEHICLES_WITH_TRAILERS_MIN" +
      "_SPEED\020\n\022$\n VEHICLES_WITH_TRAILERS_MAX_S" +
      "PEED\020\013\022)\n%VEHICLES_WITHTRAILERS_NIGHT_MA" +
      "X_SPEED\020\014\"J\n\023AccelerationSet4Way\022\013\n\003lat\030" +
      "\001 \001(\005\022\013\n\003lon\030\002 \001(\005\022\014\n\004vert\030\003 \001(\005\022\013\n\003yaw\030" +
      "\004 \001(\005\"\335\006\n\026AccelerationConfidence\022N\n\016lonA" +
      "ccelConfid\030\001 \001(\01626.cn.seisys.v2x.pb.Acce" +
      "lerationConfidence.AccConfidence\022N\n\016latA" +
      "ccelConfid\030\002 \001(\01626.cn.seisys.v2x.pb.Acce" +
      "lerationConfidence.AccConfidence\022S\n\023vert" +
      "icalAccelConfid\030\003 \001(\01626.cn.seisys.v2x.pb" +
      ".AccelerationConfidence.AccConfidence\022R\n" +
      "\ryawRateConfid\030\004 \001(\0162;.cn.seisys.v2x.pb." +
      "AccelerationConfidence.AngularVConfidenc" +
      "e\"\344\001\n\rAccConfidence\022\032\n\026ACC_CONFID_UNAVAI" +
      "LABLE\020\000\022\030\n\024ACC_CONFID_PREC100DE\020\001\022\030\n\024ACC" +
      "_CONFID_PREC10DEG\020\002\022\027\n\023ACC_CONFID_PREC5D" +
      "EG\020\003\022\027\n\023ACC_CONFID_PREC1DEG\020\004\022\031\n\025ACC_CON" +
      "FID_PREC0_1DEG\020\005\022\032\n\026ACC_CONFID_PREC0_05D" +
      "EG\020\006\022\032\n\026ACC_CONFID_PREC0_01DEG\020\007\"\222\002\n\022Ang" +
      "ularVConfidence\022\037\n\033ANGULARV_CONFID_UNAVA" +
      "ILABLE\020\000\022\036\n\032ANGULARV_CONFID_PREC100DEG\020\001" +
      "\022\035\n\031ANGULARV_CONFID_PREC10DEG\020\002\022\034\n\030ANGUL" +
      "ARV_CONFID_PREC5DEG\020\003\022\034\n\030ANGULARV_CONFID" +
      "_PREC1DEG\020\004\022\036\n\032ANGULARV_CONFID_PREC0_1DE" +
      "G\020\005\022\037\n\033ANGULARV_CONFID_PREC0_05DEG\020\006\022\037\n\033" +
      "ANGULARV_CONFID_PREC0_01DEG\020\007\"\250\003\n\023Motion" +
      "ConfidenceSet\0223\n\010speedCfd\030\001 \001(\0162!.cn.sei" +
      "sys.v2x.pb.SpeedConfidence\0227\n\nheadingCfd" +
      "\030\002 \001(\0162#.cn.seisys.v2x.pb.HeadingConfide" +
      "nce\022T\n\010steerCfd\030\003 \001(\0162B.cn.seisys.v2x.pb" +
      ".MotionConfidenceSet.SteeringWheelAngleC" +
      "onfidence\"\314\001\n\034SteeringWheelAngleConfiden" +
      "ce\022+\n\'STEERING_WHEEL_ANGLE_CONFID_UNAVAI" +
      "LABLE\020\000\022(\n$STEERING_WHEEL_ANGLE_CONFID_P" +
      "REC2DEG\020\001\022(\n$STEERING_WHEEL_ANGLE_CONFID" +
      "_PREC1DEG\020\002\022+\n\'STEERING_WHEEL_ANGLE_CONF" +
      "ID_PREC0_02DEG\020\003\"<\n\013VehicleSize\022\r\n\005width" +
      "\030\001 \001(\005\022\016\n\006length\030\002 \001(\005\022\016\n\006height\030\003 \001(\005\"1" +
      "\n\017NodeReferenceId\022\016\n\006region\030\001 \001(\r\022\016\n\006nod" +
      "eId\030\002 \001(\r\"\260\001\n\013MapLocation\0221\n\006nodeId\030\001 \001(" +
      "\0132!.cn.seisys.v2x.pb.NodeReferenceId\022\020\n\010" +
      "linkName\030\002 \001(\t\0229\n\016upstreamNodeId\030\003 \001(\0132!" +
      ".cn.seisys.v2x.pb.NodeReferenceId\022\021\n\tsec" +
      "tionId\030\004 \001(\r\022\016\n\006laneId\030\005 \001(\r\"\032\n\007PhaseId\022" +
      "\017\n\007phaseId\030\001 \001(\r\"(\n\016ReferenceLanes\022\026\n\016re" +
      "ferenceLanes\030\001 \001(\r\"U\n\rReferencePath\0220\n\na" +
      "ctivePath\030\001 \003(\0132\034.cn.seisys.v2x.pb.Posit" +
      "ion3D\022\022\n\npathRadius\030\002 \001(\r\"\301\001\n\rReferenceL" +
      "ink\0229\n\016upstreamNodeId\030\001 \001(\0132!.cn.seisys." +
      "v2x.pb.NodeReferenceId\022;\n\020downstreamNode" +
      "Id\030\002 \001(\0132!.cn.seisys.v2x.pb.NodeReferenc" +
      "eId\0228\n\016referenceLanes\030\003 \001(\0132 .cn.seisys." +
      "v2x.pb.ReferenceLanes\"$\n\020AllowedManeuver" +
      "s\022\020\n\010maneuver\030\001 \001(\r\"\237\001\n\014LaneStatInfo\022\016\n\006" +
      "laneId\030\001 \001(\r\0224\n\014linkStatInfo\030\002 \001(\0132\036.cn." +
      "seisys.v2x.pb.LinkStatInfo\022:\n\017sectionSta" +
      "tInfo\030\003 \001(\0132!.cn.seisys.v2x.pb.SectionSt" +
      "atInfo\022\r\n\005extId\030\004 \001(\t\"i\n\017SectionStatInfo" +
      "\022\021\n\tsectionId\030\001 \001(\r\0224\n\014linkStatInfo\030\002 \001(" +
      "\0132\036.cn.seisys.v2x.pb.LinkStatInfo\022\r\n\005ext" +
      "Id\030\003 \001(\t\"\234\001\n\014LinkStatInfo\0229\n\016upstreamNod" +
      "eId\030\001 \001(\0132!.cn.seisys.v2x.pb.NodeReferen" +
      "ceId\022\014\n\004name\030\002 \001(\t\0224\n\014nodeStatInfo\030\003 \001(\013" +
      "2\036.cn.seisys.v2x.pb.NodeStatInfo\022\r\n\005extI" +
      "d\030\004 \001(\t\"A\n\014NodeStatInfo\0221\n\006nodeId\030\001 \001(\0132" +
      "!.cn.seisys.v2x.pb.NodeReferenceId\"\311\001\n\020M" +
      "ovementStatInfo\022=\n\022remoteIntersection\030\001 " +
      "\001(\0132!.cn.seisys.v2x.pb.NodeReferenceId\0221" +
      "\n\rturnDirection\030\002 \001(\0162\032.cn.seisys.v2x.pb" +
      ".Maneuver\0224\n\014nodeStatInfo\030\003 \001(\0132\036.cn.sei" +
      "sys.v2x.pb.NodeStatInfo\022\r\n\005extId\030\004 \001(\t\"-" +
      "\n\031TrafficFlowStatByInterval\022\020\n\010interval\030" +
      "\001 \001(\r\"_\n\034TrafficFlowStatBySignalCycle\022\026\n" +
      "\016cycleStartTime\030\001 \001(\004\022\024\n\014cycleEndTime\030\002 " +
      "\001(\004\022\021\n\tcycleTime\030\003 \001(\r\"\226\001\n\023TrafficFlowSt" +
      "atType\022=\n\010interval\030\001 \001(\0132+.cn.seisys.v2x" +
      ".pb.TrafficFlowStatByInterval\022@\n\010sequenc" +
      "e\030\002 \001(\0132..cn.seisys.v2x.pb.TrafficFlowSt" +
      "atBySignalCycle\"\233\003\n\031TrafficFlowStatMapEl" +
      "ement\0226\n\014detectorArea\030\001 \001(\0132\036.cn.seisys." +
      "v2x.pb.DetectorAreaH\000\0226\n\014laneStatInfo\030\002 " +
      "\001(\0132\036.cn.seisys.v2x.pb.LaneStatInfoH\000\022<\n" +
      "\017sectionStatInfo\030\003 \001(\0132!.cn.seisys.v2x.p" +
      "b.SectionStatInfoH\000\0226\n\014linkStatInfo\030\004 \001(" +
      "\0132\036.cn.seisys.v2x.pb.LinkStatInfoH\000\0226\n\014n" +
      "odeStatInfo\030\005 \001(\0132\036.cn.seisys.v2x.pb.Nod" +
      "eStatInfoH\000\022>\n\020movementStatInfo\030\006 \001(\0132\"." +
      "cn.seisys.v2x.pb.MovementStatInfoH\000B \n\036T" +
      "rafficFlowStatMapElementOneOf\"\267\001\n\016LaneIn" +
      "dexAdded\022\021\n\ttimestamp\030\001 \001(\004\022\024\n\014laneCapac" +
      "ity\030\002 \001(\r\022\026\n\016laneSaturation\030\003 \001(\r\022\027\n\017lan" +
      "eSpaceOccupy\030\004 \001(\r\022\026\n\016laneTimeOccupy\030\005 \001" +
      "(\r\022\027\n\017laneAvgGrnQueue\030\006 \001(\r\022\032\n\022laneGrnUt" +
      "ilization\030\007 \001(\r\"\267\001\n\016LinkIndexAdded\022\021\n\tti" +
      "mestamp\030\001 \001(\004\022\024\n\014linkCapacity\030\002 \001(\r\022\026\n\016l" +
      "inkSaturation\030\003 \001(\r\022\027\n\017linkSpaceOccupy\030\004" +
      " \001(\r\022\026\n\016linkTimeOccupy\030\005 \001(\r\022\027\n\017linkAvgG" +
      "rnQueue\030\006 \001(\r\022\032\n\022linkGrnUtilization\030\007 \001(" +
      "\r\"\323\001\n\022MovementIndexAdded\022\021\n\ttimestamp\030\001 " +
      "\001(\004\022\030\n\020movementCapacity\030\002 \001(\r\022\032\n\022movemen" +
      "tSaturation\030\003 \001(\r\022\033\n\023movementSpaceOccupy" +
      "\030\004 \001(\r\022\032\n\022movementTimeOccupy\030\005 \001(\r\022\033\n\023mo" +
      "vementAvgGrnQueue\030\006 \001(\r\022\036\n\026movementGrnUt" +
      "ilization\030\007 \001(\r\"\366\001\n\016NodeIndexAdded\022\021\n\tti" +
      "mestamp\030\001 \001(\004\022\027\n\017nodeSpaceOccupy\030\002 \001(\r\022\026" +
      "\n\016nodeTimeOccupy\030\003 \001(\r\022\024\n\014nodeCapacity\030\004" +
      " \001(\004\022\026\n\016nodeSaturation\030\005 \001(\r\022\032\n\022nodeGrnU" +
      "tilization\030\006 \001(\r\022\027\n\017nodeAvgGrnQueue\030\007 \001(" +
      "\r\022\023\n\013demandIndex\030\010 \001(\r\022\023\n\013supplyIndex\030\t " +
      "\001(\r\022\023\n\013theoryIndex\030\n \001(\r\"t\n\027SignalContro" +
      "lIndexAdded\022\017\n\007phaseId\030\001 \001(\r\022\027\n\017greenSta" +
      "rtQueue\030\002 \001(\r\022\025\n\rredStartQueue\030\003 \001(\r\022\030\n\020" +
      "greenUtilization\030\004 \001(\r\"\262\002\n\024TrafficFlowEx" +
      "tension\0223\n\tlaneIndex\030\001 \003(\0132 .cn.seisys.v" +
      "2x.pb.LaneIndexAdded\0223\n\tlinkIndex\030\002 \003(\0132" +
      " .cn.seisys.v2x.pb.LinkIndexAdded\022;\n\rmov" +
      "ementIndex\030\003 \003(\0132$.cn.seisys.v2x.pb.Move" +
      "mentIndexAdded\0223\n\tnodeIndex\030\004 \003(\0132 .cn.s" +
      "eisys.v2x.pb.NodeIndexAdded\022>\n\013signalInd" +
      "ex\030\005 \003(\0132).cn.seisys.v2x.pb.SignalContro" +
      "lIndexAdded\"\260\004\n\017TrafficFlowStat\022?\n\nmapEl" +
      "ement\030\001 \001(\0132+.cn.seisys.v2x.pb.TrafficFl" +
      "owStatMapElement\0228\n\016mapElementType\030\002 \001(\016" +
      "2 .cn.seisys.v2x.pb.MapElementType\0222\n\007pt" +
      "cType\030\003 \001(\0162!.cn.seisys.v2x.pb.Participa" +
      "ntType\0222\n\013vehicleType\030\004 \001(\0162\035.cn.seisys." +
      "v2x.pb.VehicleType\022\021\n\ttimestamp\030\005 \001(\004\022\016\n" +
      "\006volume\030\006 \001(\r\022\022\n\nspeedPoint\030\007 \001(\r\022\021\n\tspe" +
      "edArea\030\010 \001(\r\022\017\n\007density\030\t \001(\r\022\022\n\ntravelT" +
      "ime\030\n \001(\r\022\r\n\005delay\030\013 \001(\r\022\023\n\013queueLength\030" +
      "\014 \001(\r\022\020\n\010queueInt\030\r \001(\r\022\022\n\ncongestion\030\016 " +
      "\001(\r\022D\n\024trafficFlowExtension\030\017 \001(\0132&.cn.s" +
      "eisys.v2x.pb.TrafficFlowExtension\022\023\n\013tim" +
      "eHeadway\030\020 \001(\r\022\024\n\014spaceHeadway\030\021 \001(\r\022\020\n\010" +
      "stopNums\030\022 \001(\r\"\274\001\n\013TrafficFlow\0221\n\006nodeId" +
      "\030\001 \001(\0132!.cn.seisys.v2x.pb.NodeReferenceI" +
      "d\022\017\n\007genTime\030\002 \001(\004\0227\n\010statType\030\003 \001(\0132%.c" +
      "n.seisys.v2x.pb.TrafficFlowStatType\0220\n\005s" +
      "tats\030\004 \003(\0132!.cn.seisys.v2x.pb.TrafficFlo" +
      "wStat\"\313\001\n\020TimeCountingDown\022\021\n\tstartTime\030" +
      "\001 \001(\r\022\022\n\nminEndTime\030\002 \001(\r\022\022\n\nmaxEndTime\030" +
      "\003 \001(\r\022\025\n\rlikelyEndTime\030\004 \001(\r\0228\n\016timeConf" +
      "idence\030\005 \001(\0162 .cn.seisys.v2x.pb.TimeConf" +
      "idence\022\025\n\rnextStartTime\030\006 \001(\r\022\024\n\014nextDur" +
      "ation\030\007 \001(\r\"\204\003\n\nPhaseState\0226\n\005light\030\001 \001(" +
      "\0162\'.cn.seisys.v2x.pb.PhaseState.LightSta" +
      "te\0222\n\006timing\030\002 \001(\0132\".cn.seisys.v2x.pb.Ti" +
      "meCountingDown\"\211\002\n\nLightState\022\027\n\023LIGHT_S" +
      "TATE_UNKNOWN\020\000\022\024\n\020LIGHT_STATE_DARK\020\001\022\034\n\030" +
      "LIGHT_STATE_FLASHING_RED\020\002\022\023\n\017LIGHT_STAT" +
      "E_RED\020\003\022\036\n\032LIGHT_STATE_FLASHING_GREEN\020\004\022" +
      " \n\034LIGHT_STATE_PERMISSIVE_GREEN\020\005\022\036\n\032LIG" +
      "HT_STATE_PROTETED_GREEN\020\006\022\026\n\022LIGHT_STATE" +
      "_YELLOW\020\007\022\037\n\033LIGHT_STATE_FLASHING_YELLOW" +
      "\020\010\"F\n\005Phase\022\n\n\002id\030\001 \001(\r\0221\n\013phaseStates\030\002" +
      " \003(\0132\034.cn.seisys.v2x.pb.PhaseState\"\324\001\n\021I" +
      "ntersectionState\0229\n\016intersectionId\030\001 \001(\013" +
      "2!.cn.seisys.v2x.pb.NodeReferenceId\022\016\n\006s" +
      "tatus\030\002 \001(\t\022\021\n\ttimestamp\030\003 \001(\004\0228\n\016timeCo" +
      "nfidence\030\004 \001(\0162 .cn.seisys.v2x.pb.TimeCo" +
      "nfidence\022\'\n\006phases\030\005 \003(\0132\027.cn.seisys.v2x" +
      ".pb.Phase\"i\n\010SpatData\022\016\n\006msgCnt\030\001 \001(\r\022\021\n" +
      "\ttimestamp\030\002 \001(\004\022:\n\rintersections\030\003 \003(\0132" +
      "#.cn.seisys.v2x.pb.IntersectionState\"4\n\016" +
      "LocalTimePoint\022\n\n\002hh\030\001 \001(\005\022\n\n\002mm\030\002 \001(\005\022\n" +
      "\n\002ss\030\003 \001(\005\"\301\001\n\020PeriodictimeSpan\022\023\n\013month" +
      "Filter\030\001 \001(\005\022\021\n\tdayFilter\030\002 \001(\005\022\025\n\rweekd" +
      "ayFilter\030\003 \001(\005\0227\n\rfromTimePoint\030\004 \001(\0132 ." +
      "cn.seisys.v2x.pb.LocalTimePoint\0225\n\013toTim" +
      "ePoint\030\005 \001(\0132 .cn.seisys.v2x.pb.LocalTim" +
      "ePoint\"4\n\016SingleTimeSpan\022\021\n\tstartTime\030\001 " +
      "\001(\004\022\017\n\007endTime\030\002 \001(\004\"\221\001\n\rOptimTimeType\0222" +
      "\n\006single\030\001 \001(\0132 .cn.seisys.v2x.pb.Single" +
      "TimeSpanH\000\0226\n\010periodic\030\002 \001(\0132\".cn.seisys" +
      ".v2x.pb.PeriodictimeSpanH\000B\024\n\022OptimTimeT" +
      "ypeOneOf\"\217\001\n\nMovementEx\022=\n\022remoteInterse" +
      "ction\030\001 \001(\0132!.cn.seisys.v2x.pb.NodeRefer" +
      "enceId\022\017\n\007phaseId\030\002 \001(\r\0221\n\rturnDirection" +
      "\030\003 \001(\0162\032.cn.seisys.v2x.pb.Maneuver\"\326\001\n\nO" +
      "ptimPhase\022\017\n\007phaseId\030\001 \001(\r\022\r\n\005order\030\002 \001(" +
      "\r\0220\n\nmovementId\030\003 \003(\0132\034.cn.seisys.v2x.pb" +
      ".MovementEx\022\021\n\tphaseTime\030\004 \001(\r\022\r\n\005green\030" +
      "\005 \001(\r\022\027\n\017phaseYellowTime\030\006 \001(\r\022\027\n\017phaseA" +
      "llRedTime\030\007 \001(\r\022\020\n\010minGreen\030\010 \001(\r\022\020\n\010max" +
      "Green\030\t \001(\r\"\340\001\n\tOptimData\0226\n\roptimTimeTy" +
      "pe\030\001 \001(\0132\037.cn.seisys.v2x.pb.OptimTimeTyp" +
      "e\022\026\n\016optimCycleTime\030\002 \001(\r\022\024\n\014minCycleTim" +
      "e\030\003 \001(\r\022\024\n\014maxCycleTime\030\004 \001(\r\0224\n\016optimPh" +
      "aseList\030\005 \003(\0132\034.cn.seisys.v2x.pb.OptimPh" +
      "ase\022\021\n\tcoorPhase\030\006 \001(\t\022\016\n\006offset\030\007 \001(\r\"\233" +
      "\001\n\014SignalScheme\0221\n\006nodeId\030\001 \001(\0132!.cn.sei" +
      "sys.v2x.pb.NodeReferenceId\022\021\n\toptimType\030" +
      "\002 \001(\r\022\021\n\ttimestamp\030\003 \001(\004\0222\n\roptimDataLis" +
      "t\030\004 \003(\0132\033.cn.seisys.v2x.pb.OptimData\"\371\007\n" +
      "\021BrakeSystemStatus\022H\n\nbrakePadel\030\001 \001(\01624" +
      ".cn.seisys.v2x.pb.BrakeSystemStatus.Brak" +
      "ePedalStatus\022\023\n\013wheelBrakes\030\002 \001(\r\022K\n\010tra" +
      "ction\030\003 \001(\01629.cn.seisys.v2x.pb.BrakeSyst" +
      "emStatus.TractionControlStatus\022D\n\003abs\030\004 " +
      "\001(\01627.cn.seisys.v2x.pb.BrakeSystemStatus" +
      ".AntiLockBrakeStatus\022G\n\003scs\030\005 \001(\0162:.cn.s" +
      "eisys.v2x.pb.BrakeSystemStatus.Stability" +
      "ControlStatus\022I\n\nbrakeBoost\030\006 \001(\01625.cn.s" +
      "eisys.v2x.pb.BrakeSystemStatus.BrakeBoos" +
      "tApplied\022K\n\tauxBrakes\030\007 \001(\01628.cn.seisys." +
      "v2x.pb.BrakeSystemStatus.AuxiliaryBrakeS" +
      "tatus\022\024\n\014brakeControl\030\010 \001(\r\"F\n\020BrakePeda" +
      "lStatus\022\025\n\021UNAVAILABLE_PEDAL\020\000\022\r\n\tOFF_PE" +
      "DAL\020\001\022\014\n\010ON_PEDAL\020\002\"j\n\025TractionControlSt" +
      "atus\022\030\n\024UNAVAILABLE_TRACTION\020\000\022\020\n\014OFF_TR" +
      "ACTION\020\001\022\017\n\013ON_TRACTION\020\002\022\024\n\020ENGAGED_TRA" +
      "CTION\020\003\"T\n\023AntiLockBrakeStatus\022\023\n\017UNAVAI" +
      "LABLE_ABS\020\000\022\013\n\007OFF_ABS\020\001\022\n\n\006ON_ABS\020\002\022\017\n\013" +
      "ENGAGED_ABS\020\003\"W\n\026StabilityControlStatus\022" +
      "\023\n\017UNAVAILABLE_SCS\020\000\022\013\n\007OFF_SCS\020\001\022\n\n\006ON_" +
      "SCS\020\002\022\017\n\013ENGAGED_SCS\020\003\"A\n\021BrakeBoostAppl" +
      "ied\022\023\n\017UNAVAILABLE_BBA\020\000\022\013\n\007OFF_BBA\020\001\022\n\n" +
      "\006ON_BBA\020\002\"U\n\024AuxiliaryBrakeStatus\022\023\n\017UNA" +
      "VAILABLE_AUX\020\000\022\013\n\007OFF_AUX\020\001\022\n\n\006ON_AUX\020\002\022" +
      "\017\n\013ENGAGED_AUX\020\003\"M\n\020PositionAccuracy\022\021\n\t" +
      "semiMajor\030\001 \001(\005\022\021\n\tsemiMinor\030\002 \001(\005\022\023\n\013or" +
      "ientation\030\003 \001(\005\"\331\001\n\024ThrottleSystemStatus" +
      "\022\027\n\017thorttleControl\030\001 \001(\r\022Q\n\rthrottlePad" +
      "el\030\002 \001(\0162:.cn.seisys.v2x.pb.ThrottleSyst" +
      "emStatus.ThrottlePedalStauts\022\026\n\016wheelThr" +
      "ottles\030\003 \001(\005\"=\n\023ThrottlePedalStauts\022\025\n\021U" +
      "NAVAILABLE_PEDAL\020\000\022\007\n\003OFF\020\001\022\006\n\002ON\020\002\"\342\014\n\007" +
      "BsmData\022\r\n\005obuId\030\001 \001(\t\022\017\n\007plateNo\030\002 \001(\t\022" +
      "\021\n\ttimestamp\030\003 \001(\004\022)\n\003pos\030\004 \001(\0132\034.cn.sei" +
      "sys.v2x.pb.Position3D\022:\n\tposConfid\030\005 \001(\013" +
      "2\'.cn.seisys.v2x.pb.PositionConfidenceSe" +
      "t\0227\n\013posAccuracy\030\006 \001(\0132\".cn.seisys.v2x.p" +
      "b.PositionAccuracy\022;\n\014acceleration\030\007 \001(\013" +
      "2%.cn.seisys.v2x.pb.AccelerationSet4Way\022" +
      "A\n\014transmission\030\010 \001(\0162+.cn.seisys.v2x.pb" +
      ".BsmData.TransmissionState\022\r\n\005speed\030\t \001(" +
      "\r\022\017\n\007heading\030\n \001(\r\022\032\n\022steeringWheelAngle" +
      "\030\013 \001(\005\022;\n\014motionConfid\030\014 \001(\0132%.cn.seisys" +
      ".v2x.pb.MotionConfidenceSet\0223\n\006brakes\030\r " +
      "\001(\0132#.cn.seisys.v2x.pb.BrakeSystemStatus" +
      "\0228\n\010throttle\030\016 \001(\0132&.cn.seisys.v2x.pb.Th" +
      "rottleSystemStatus\022+\n\004size\030\017 \001(\0132\035.cn.se" +
      "isys.v2x.pb.VehicleSize\0222\n\013vehicleType\030\020" +
      " \001(\0162\035.cn.seisys.v2x.pb.VehicleType\022;\n\010f" +
      "uelType\030\021 \001(\0162).cn.seisys.v2x.pb.BsmData" +
      ".VehicleFuelType\022C\n\024driveModedriveStatus" +
      "\030\022 \001(\0162%.cn.seisys.v2x.pb.BsmData.DriveS" +
      "tatus\022A\n\017emergencyStatus\030\023 \001(\0162(.cn.seis" +
      "ys.v2x.pb.BsmData.EmergenyStatus\022\r\n\005ligh" +
      "t\030\024 \001(\r\022.\n\005wiper\030\025 \001(\0162\037.cn.seisys.v2x.p" +
      "b.BsmData.Wiper\022<\n\014outofControl\030\026 \001(\0162&." +
      "cn.seisys.v2x.pb.BsmData.OutofControl\022\021\n" +
      "\tendurance\030\027 \001(\r\"\366\001\n\021TransmissionState\022\030" +
      "\n\024TRANSMISSION_NEUTRAL\020\000\022\025\n\021TRANSMISSION" +
      "_PARK\020\001\022\036\n\032TRANSMISSION_FORWARD_GEARS\020\002\022" +
      "\036\n\032TRANSMISSION_REVERSE_GEARS\020\003\022\032\n\026TRANS" +
      "MISSION_RESERVED1\020\004\022\032\n\026TRANSMISSION_RESE" +
      "RVED2\020\005\022\032\n\026TRANSMISSION_RESERVED3\020\006\022\034\n\030T" +
      "RANSMISSION_UNAVAILABLE\020\007\"\255\001\n\017VehicleFue" +
      "lType\022\034\n\030VEHICLE_TUEL_UNKNOWNFUEL\020\000\022\014\n\010G" +
      "ASOLINE\020\001\022\013\n\007ETHANOL\020\002\022\n\n\006DIESEL\020\003\022\014\n\010EL" +
      "ECTRIC\020\004\022\n\n\006HYBRID\020\005\022\014\n\010HYDROGEN\020\006\022\020\n\014NA" +
      "TGASLIQUID\020\007\022\016\n\nNATGASCOMP\020\010\022\013\n\007PROPANE\020" +
      "\t\"6\n\013DriveStatus\022\r\n\tAUTOPILOT\020\000\022\n\n\006MANUA" +
      "L\020\001\022\014\n\010SECURITY\020\002\"+\n\016EmergenyStatus\022\013\n\007N" +
      "O_EMER\020\000\022\014\n\010YES_EMER\020\001\")\n\005Wiper\022\007\n\003OFF\020\000" +
      "\022\007\n\003INT\020\001\022\006\n\002LO\020\002\022\006\n\002HI\020\003\"-\n\014OutofContro" +
      "l\022\r\n\tNO_OUTCON\020\000\022\016\n\nYES_OUTCON\020\001\"\255\001\n\020Pat" +
      "hHistoryPoint\022)\n\003pos\030\001 \001(\0132\034.cn.seisys.v" +
      "2x.pb.Position3D\022\022\n\ntimeOffset\030\002 \001(\r\022\r\n\005" +
      "speed\030\003 \001(\r\022:\n\tposConfid\030\004 \001(\0132\'.cn.seis" +
      "ys.v2x.pb.PositionConfidenceSet\022\017\n\007headi" +
      "ng\030\005 \001(\r\"\361\r\n\017ParticipantData\022\r\n\005ptcId\030\001 " +
      "\001(\004\0222\n\007ptcType\030\002 \001(\0162!.cn.seisys.v2x.pb." +
      "ParticipantType\0220\n\ndataSource\030\003 \001(\0162\034.cn" +
      ".seisys.v2x.pb.DataSource\022\024\n\014deviceIdLis" +
      "t\030\004 \001(\t\022\021\n\ttimestamp\030\005 \001(\004\0228\n\016timeConfid" +
      "ence\030\006 \001(\0162 .cn.seisys.v2x.pb.TimeConfid" +
      "ence\022,\n\006ptcPos\030\007 \001(\0132\034.cn.seisys.v2x.pb." +
      "Position3D\0222\n\013mapLocation\030\010 \001(\0132\035.cn.sei" +
      "sys.v2x.pb.MapLocation\022:\n\tposConfid\030\t \001(" +
      "\0132\'.cn.seisys.v2x.pb.PositionConfidenceS" +
      "et\022\r\n\005speed\030\n \001(\r\022\017\n\007heading\030\013 \001(\r\022;\n\014mo" +
      "tionConfid\030\014 \001(\0132%.cn.seisys.v2x.pb.Moti" +
      "onConfidenceSet\0227\n\010accelSet\030\r \001(\0132%.cn.s" +
      "eisys.v2x.pb.AccelerationSet4Way\022D\n\022acce" +
      "lerationConfid\030\016 \001(\0132(.cn.seisys.v2x.pb." +
      "AccelerationConfidence\0222\n\007ptcSize\030\017 \001(\0132" +
      "!.cn.seisys.v2x.pb.ParticipantSize\022\023\n\013ve" +
      "hicleBand\030\020 \001(\t\0222\n\013vehicleType\030\021 \001(\0162\035.c" +
      "n.seisys.v2x.pb.VehicleType\022\017\n\007plateNo\030\022" +
      " \001(\t\022.\n\tplateType\030\023 \001(\0162\033.cn.seisys.v2x." +
      "pb.PlateType\022@\n\nplateColor\030\024 \001(\0162,.cn.se" +
      "isys.v2x.pb.ParticipantData.PlateColor\022D" +
      "\n\014vehicleColor\030\025 \001(\0162..cn.seisys.v2x.pb." +
      "ParticipantData.VehicleColor\022B\n\rptcSizeC" +
      "onfid\030\026 \001(\0132+.cn.seisys.v2x.pb.Participa" +
      "ntSizeConfidence\022H\n\nptcTypeExt\030\027 \001(\01624.c" +
      "n.seisys.v2x.pb.ParticipantData.Particip" +
      "antTypeExt\022\030\n\020ptcTypeExtConfid\030\030 \001(\r\022\026\n\016" +
      "statusDuration\030\031 \001(\r\0227\n\013pathHistory\030\032 \003(" +
      "\0132\".cn.seisys.v2x.pb.PathHistoryPoint\022\020\n" +
      "\010tracking\030\033 \001(\r\022*\n\007polygon\030\034 \001(\0132\031.cn.se" +
      "isys.v2x.pb.Polygon\022\n\n\002id\030\035 \001(\004\"\233\001\n\nPlat" +
      "eColor\022\027\n\023UNKNOWN_PLATE_COLOR\020\000\022\016\n\nBLUE_" +
      "PLATE\020\001\022\020\n\014YELLOW_PLATE\020\002\022\017\n\013WHITE_PLATE" +
      "\020\003\022\017\n\013BLACK_PLATE\020\004\022\026\n\022YELLOW_GREEN_PLAT" +
      "E\020\005\022\030\n\024GRADIENT_GREEN_PLATE\020\006\"\237\001\n\014Vehicl" +
      "eColor\022\031\n\025UNKNOWN_VEHICEL_COLOR\020\000\022\t\n\005WHI" +
      "TE\020\001\022\010\n\004GRAY\020\002\022\n\n\006YELLOW\020\003\022\010\n\004PINK\020\004\022\007\n\003" +
      "RED\020\005\022\t\n\005GREEN\020\006\022\010\n\004BLUE\020\007\022\t\n\005BROWN\020\010\022\t\n" +
      "\005BLACK\020\t\022\n\n\006PURPLE\020\n\022\t\n\005OTHER\020\013\"\302\001\n\022Part" +
      "icipantTypeExt\022\033\n\027UNKNOWN_OBJECT_TYPE_EX" +
      "T\020\000\022\023\n\017UNKNOWN_MOVABLE\020\001\022\025\n\021UNKNOWN_UNMO" +
      "VABLE\020\002\022\007\n\003CAR\020\003\022\007\n\003VAN\020\004\022\t\n\005TRUCK\020\005\022\007\n\003" +
      "BUS\020\006\022\013\n\007CYCLIST\020\007\022\020\n\014MOTORCYCLIST\020\010\022\016\n\n" +
      "TRICYCLIST\020\t\022\016\n\nPEDESTRIAN\020\n\"\365\005\n\014Obstacl" +
      "eData\022\r\n\005obsId\030\001 \001(\004\0220\n\007obsType\030\002 \001(\0162\037." +
      "cn.seisys.v2x.pb.ObstaclesType\022\022\n\nobstyp" +
      "eCfd\030\003 \001(\r\022/\n\tobsSource\030\004 \001(\0162\034.cn.seisy" +
      "s.v2x.pb.DataSource\022\021\n\ttimestamp\030\005 \001(\004\022\024" +
      "\n\014deviceIdList\030\006 \001(\t\022,\n\006obsPos\030\007 \001(\0132\034.c" +
      "n.seisys.v2x.pb.Position3D\022:\n\tposConfid\030" +
      "\010 \001(\0132\'.cn.seisys.v2x.pb.PositionConfide" +
      "nceSet\0222\n\013mapLocation\030\t \001(\0132\035.cn.seisys." +
      "v2x.pb.MapLocation\022\r\n\005speed\030\n \001(\r\022\017\n\007hea" +
      "ding\030\013 \001(\r\022;\n\014motionConfid\030\014 \001(\0132%.cn.se" +
      "isys.v2x.pb.MotionConfidenceSet\022\020\n\010verSp" +
      "eed\030\r \001(\r\0229\n\016verSpeedConfid\030\016 \001(\0162!.cn.s" +
      "eisys.v2x.pb.SpeedConfidence\022;\n\014accelera" +
      "tion\030\017 \001(\0132%.cn.seisys.v2x.pb.Accelerati" +
      "onSet4Way\022/\n\004size\030\020 \001(\0132!.cn.seisys.v2x." +
      "pb.ParticipantSize\022B\n\robsSizeConfid\030\021 \001(" +
      "\0132+.cn.seisys.v2x.pb.ParticipantSizeConf" +
      "idence\022\020\n\010tracking\030\022 \001(\r\022*\n\007polygon\030\023 \001(" +
      "\0132\031.cn.seisys.v2x.pb.Polygon\"\212\001\n\nObjIdVa" +
      "lue\022\r\n\005ptcId\030\001 \001(\004\022\r\n\005obsId\030\002 \001(\004\022/\n\004rol" +
      "e\030\003 \001(\0162!.cn.seisys.v2x.pb.ObjIdValue.Ro",
      "le\"-\n\004Role\022\n\n\006ACTIVE\020\000\022\013\n\007PASSIVE\020\001\022\014\n\010N" +
      "OTCLEAR\020\002\"\233\006\n\007RteData\022\r\n\005rteId\030\001 \001(\r\022\017\n\007" +
      "rteType\030\002 \001(\r\022\023\n\013description\030\003 \001(\t\022:\n\013ev" +
      "entSource\030\004 \001(\0162%.cn.seisys.v2x.pb.RteDa" +
      "ta.EventSource\0220\n\ndataSource\030\005 \001(\0162\034.cn." +
      "seisys.v2x.pb.DataSource\022\024\n\014deviceIdList" +
      "\030\006 \001(\t\022,\n\006rtePos\030\007 \001(\0132\034.cn.seisys.v2x.p" +
      "b.Position3D\0222\n\013mapLocation\030\010 \001(\0132\035.cn.s" +
      "eisys.v2x.pb.MapLocation\022\023\n\013eventRadius\030" +
      "\t \001(\r\0225\n\013timeDetails\030\n \001(\0132 .cn.seisys.v" +
      "2x.pb.RsiTimeDetails\022\020\n\010priority\030\013 \001(\t\0226" +
      "\n\rreferencePath\030\014 \003(\0132\037.cn.seisys.v2x.pb" +
      ".ReferencePath\0227\n\016referenceLinks\030\r \003(\0132\037" +
      ".cn.seisys.v2x.pb.ReferenceLink\0220\n\nevent" +
      "ObjId\030\016 \003(\0132\034.cn.seisys.v2x.pb.ObjIdValu" +
      "e\022\023\n\013eventConfid\030\017 \001(\005\022\023\n\013eventImages\030\020 " +
      "\001(\t\022\023\n\013eventVideos\030\021 \001(\t\022\021\n\tsessionId\030\022 " +
      "\001(\004\022\n\n\002id\030\023 \001(\004\"\225\001\n\013EventSource\022\030\n\024UNKNO" +
      "WN_EVENT_SOURCE\020\000\022\022\n\016TRAFFIC_POLICE\020\001\022\r\n" +
      "\tGOVENMENT\020\002\022\035\n\031METEOROLOGICAL_DEPARTMEN" +
      "T\020\003\022\025\n\021INTERNET_SERVICES\020\004\022\023\n\017LOCAL_DETE" +
      "CTION\020\005\"\206\003\n\007RtsData\022\r\n\005rtsId\030\001 \001(\005\022\017\n\007rt" +
      "sType\030\002 \001(\005\0220\n\ndataSource\030\003 \001(\0162\034.cn.sei" +
      "sys.v2x.pb.DataSource\022\020\n\010priority\030\004 \001(\t\022" +
      ",\n\006rtsPos\030\005 \001(\0132\034.cn.seisys.v2x.pb.Posit" +
      "ion3D\0225\n\013timeDetails\030\006 \001(\0132 .cn.seisys.v" +
      "2x.pb.RsiTimeDetails\022\023\n\013description\030\007 \001(" +
      "\t\0224\n\013refPathList\030\010 \003(\0132\037.cn.seisys.v2x.p" +
      "b.ReferencePath\0224\n\013refLinkList\030\t \003(\0132\037.c" +
      "n.seisys.v2x.pb.ReferenceLink\022\022\n\npathRad" +
      "ius\030\n \001(\r\022\021\n\tsessionId\030\013 \001(\004\022\n\n\002id\030\014 \001(\004" +
      "\"T\n\016ConnectingLane\022\014\n\004lane\030\001 \001(\r\0224\n\010mane" +
      "uver\030\002 \001(\0132\".cn.seisys.v2x.pb.AllowedMan" +
      "euvers\"\226\001\n\nConnection\022=\n\022remoteIntersect" +
      "ion\030\001 \001(\0132!.cn.seisys.v2x.pb.NodeReferen" +
      "ceId\0228\n\016connectingLane\030\002 \001(\0132 .cn.seisys" +
      ".v2x.pb.ConnectingLane\022\017\n\007phaseId\030\003 \001(\r\"" +
      "8\n\025LaneAttributesParking\022\037\n\027parkingAndSt" +
      "oppingLanes\030\001 \001(\r\"7\n\027LaneAttributesCross" +
      "walk\022\034\n\024pedestrianCrosswalks\030\001 \001(\r\"\'\n\022La" +
      "neAttributesBike\022\021\n\tbikeLanes\030\001 \001(\r\"9\n\026L" +
      "aneAttributesSidewalk\022\037\n\027pedestrianSidew" +
      "alkPaths\030\001 \001(\r\"6\n\025LaneAttributesBarrier\022" +
      "\035\n\025mediansChannelization\030\001 \001(\r\"1\n\026LaneAt" +
      "tributesStriping\022\027\n\017roadwayMarkings\030\001 \001(" +
      "\r\"9\n\034LaneAttributesTrackedVehicle\022\031\n\021tra" +
      "insAndTrolleys\030\001 \001(\r\"2\n\025LaneAttributesVe" +
      "hicle\022\031\n\021motorVehicleLanes\030\001 \001(\r\"\360\004\n\022Lan" +
      "eTypeAttributes\022D\n\021motorVehicleLanes\030\001 \001" +
      "(\0132\'.cn.seisys.v2x.pb.LaneAttributesVehi" +
      "cleH\000\022I\n\024pedestrianCrosswalks\030\002 \001(\0132).cn" +
      ".seisys.v2x.pb.LaneAttributesCrosswalkH\000" +
      "\0229\n\tbikeLanes\030\003 \001(\0132$.cn.seisys.v2x.pb.L" +
      "aneAttributesBikeH\000\022K\n\027pedestrianSidewal" +
      "kPaths\030\004 \001(\0132(.cn.seisys.v2x.pb.LaneAttr" +
      "ibutesSidewalkH\000\022H\n\025mediansChannelizatio" +
      "n\030\005 \001(\0132\'.cn.seisys.v2x.pb.LaneAttribute" +
      "sBarrierH\000\022C\n\017roadwayMarkings\030\006 \001(\0132(.cn" +
      ".seisys.v2x.pb.LaneAttributesStripingH\000\022" +
      "K\n\021trainsAndTrolleys\030\007 \001(\0132..cn.seisys.v" +
      "2x.pb.LaneAttributesTrackedVehicleH\000\022J\n\027" +
      "parkingAndStoppingLanes\030\010 \001(\0132\'.cn.seisy" +
      "s.v2x.pb.LaneAttributesParkingH\000B\031\n\027Lane" +
      "TypeAttributesOneOf\" \n\013LaneSharing\022\021\n\tsh" +
      "areWith\030\001 \001(\r\"Q\n\010LaneType\022\020\n\010choiceId\030\001 " +
      "\001(\r\0223\n\005value\030\002 \001(\0132$.cn.seisys.v2x.pb.La" +
      "neTypeAttributes\"p\n\016LaneAttributes\0220\n\tsh" +
      "areWith\030\001 \001(\0132\035.cn.seisys.v2x.pb.LaneSha" +
      "ring\022,\n\010laneType\030\002 \001(\0132\032.cn.seisys.v2x.p" +
      "b.LaneType\"b\n\014LaneBoundary\022\030\n\020laneBounda" +
      "ryType\030\001 \001(\r\0228\n\022laneBoundaryPoints\030\002 \003(\013" +
      "2\034.cn.seisys.v2x.pb.Position3D\"\244\003\n\004Lane\022" +
      "\016\n\006laneId\030\001 \001(\r\022\021\n\tlaneWidth\030\002 \001(\r\0228\n\016la" +
      "neAttributes\030\003 \001(\0132 .cn.seisys.v2x.pb.La" +
      "neAttributes\0225\n\tmaneuvers\030\004 \001(\0132\".cn.sei" +
      "sys.v2x.pb.AllowedManeuvers\0220\n\nconnectsT" +
      "o\030\005 \003(\0132\034.cn.seisys.v2x.pb.Connection\022;\n" +
      "\013speedLimits\030\006 \003(\0132&.cn.seisys.v2x.pb.Re" +
      "gulatorySpeedLimit\022,\n\006points\030\007 \003(\0132\034.cn." +
      "seisys.v2x.pb.Position3D\0224\n\014leftBoundary" +
      "\030\010 \003(\0132\036.cn.seisys.v2x.pb.LaneBoundary\0225" +
      "\n\rrightBoundary\030\t \003(\0132\036.cn.seisys.v2x.pb" +
      ".LaneBoundary\"\210\001\n\021SignalWaitingLane\022\021\n\tl" +
      "aneWidth\030\001 \001(\005\022,\n\006points\030\002 \001(\0132\034.cn.seis" +
      "ys.v2x.pb.Position3D\0222\n\017allowedPhaseIds\030" +
      "\003 \003(\0132\031.cn.seisys.v2x.pb.PhaseId\"\266\001\n\020Con" +
      "nectingLaneEx\022\025\n\rtargetSection\030\001 \001(\005\022\022\n\n" +
      "targetLane\030\002 \001(\005\022\033\n\023connectingLaneWidth\030" +
      "\003 \001(\005\022:\n\024connectingLanePoints\030\004 \001(\0132\034.cn" +
      ".seisys.v2x.pb.Position3D\022\036\n\026isolatedCon" +
      "nectingLane\030\005 \001(\010\"\377\001\n\014ConnectionEx\022=\n\022re" +
      "moteIntersection\030\001 \001(\0132!.cn.seisys.v2x.p" +
      "b.NodeReferenceId\0220\n\003swl\030\002 \001(\0132#.cn.seis" +
      "ys.v2x.pb.SignalWaitingLane\022:\n\016connectio" +
      "nLane\030\003 \003(\0132\".cn.seisys.v2x.pb.Connectin" +
      "gLaneEx\022\017\n\007phaseId\030\004 \001(\r\0221\n\rturnDirectio" +
      "n\030\005 \001(\0162\032.cn.seisys.v2x.pb.Maneuver\"\'\n\007S" +
      "TPoint\022\r\n\005sAxis\030\001 \001(\005\022\r\n\005tAxis\030\002 \001(\005\"\254\003\n" +
      "\006LaneEx\022\021\n\tlaneRefId\030\001 \001(\005\022\021\n\tlaneWidth\030" +
      "\002 \001(\r\0228\n\016laneAttributes\030\003 \001(\0132 .cn.seisy" +
      "s.v2x.pb.LaneAttributes\0225\n\tmaneuvers\030\004 \001" +
      "(\0132\".cn.seisys.v2x.pb.AllowedManeuvers\0224" +
      "\n\014connectsToEx\030\005 \003(\0132\036.cn.seisys.v2x.pb." +
      "ConnectionEx\022;\n\013speedLimits\030\006 \003(\0132&.cn.s" +
      "eisys.v2x.pb.RegulatorySpeedLimit\022+\n\010stP" +
      "oints\030\007 \003(\0132\031.cn.seisys.v2x.pb.STPoint\0224" +
      "\n\014leftBoundary\030\010 \003(\0132\036.cn.seisys.v2x.pb." +
      "LaneBoundary\0225\n\rrightBoundary\030\t \003(\0132\036.cn" +
      ".seisys.v2x.pb.LaneBoundary\"Z\n\010Movement\022" +
      "=\n\022remoteIntersection\030\001 \001(\0132!.cn.seisys." +
      "v2x.pb.NodeReferenceId\022\017\n\007phaseId\030\002 \001(\r\"" +
      "A\n\007Section\022\r\n\005SecId\030\001 \001(\r\022\'\n\005lanes\030\002 \003(\013" +
      "2\030.cn.seisys.v2x.pb.LaneEx\"\260\002\n\006LinkEx\022\014\n" +
      "\004name\030\001 \001(\t\0229\n\016upstreamNodeId\030\002 \001(\0132!.cn" +
      ".seisys.v2x.pb.NodeReferenceId\022;\n\013speedL" +
      "imits\030\003 \003(\0132&.cn.seisys.v2x.pb.Regulator" +
      "ySpeedLimit\022\021\n\tlinkWidth\030\004 \001(\r\022-\n\007refLin" +
      "e\030\005 \003(\0132\034.cn.seisys.v2x.pb.Position3D\0221\n" +
      "\013movementsEx\030\006 \003(\0132\034.cn.seisys.v2x.pb.Mo" +
      "vementEx\022+\n\010sections\030\007 \003(\0132\031.cn.seisys.v" +
      "2x.pb.Section\"\243\002\n\004Link\022\014\n\004name\030\001 \001(\t\0229\n\016" +
      "upstreamNodeId\030\002 \001(\0132!.cn.seisys.v2x.pb." +
      "NodeReferenceId\022;\n\013speedLimits\030\003 \003(\0132&.c" +
      "n.seisys.v2x.pb.RegulatorySpeedLimit\022\021\n\t" +
      "linkWidth\030\004 \001(\r\022,\n\006points\030\005 \003(\0132\034.cn.sei" +
      "sys.v2x.pb.Position3D\022-\n\tmovements\030\006 \003(\013" +
      "2\032.cn.seisys.v2x.pb.Movement\022%\n\005lanes\030\007 " +
      "\003(\0132\026.cn.seisys.v2x.pb.Lane\"\326\001\n\016Prohibit" +
      "edZone\022>\n\033centralCirclePrihibitedZone\030\001 " +
      "\001(\0132\031.cn.seisys.v2x.pb.Polygon\022A\n\036nonMot" +
      "orVehicleProhibitedZones\030\002 \003(\0132\031.cn.seis" +
      "ys.v2x.pb.Polygon\022A\n\036gridLineMarkingProh" +
      "ibitedZones\030\003 \003(\0132\031.cn.seisys.v2x.pb.Pol" +
      "ygon\"\201\002\n\004Node\022\014\n\004name\030\001 \001(\t\022-\n\002id\030\002 \001(\0132" +
      "!.cn.seisys.v2x.pb.NodeReferenceId\022,\n\006re" +
      "fPos\030\003 \001(\0132\034.cn.seisys.v2x.pb.Position3D" +
      "\022\'\n\007inLinks\030\004 \003(\0132\026.cn.seisys.v2x.pb.Lin" +
      "k\022+\n\tinLinksEx\030\005 \003(\0132\030.cn.seisys.v2x.pb." +
      "LinkEx\0228\n\016prohibitedZone\030\006 \001(\0132 .cn.seis" +
      "ys.v2x.pb.ProhibitedZone\"O\n\003MAP\022\021\n\ttimes" +
      "tamp\030\001 \001(\r\022%\n\005nodes\030\002 \003(\0132\026.cn.seisys.v2" +
      "x.pb.Node\022\016\n\006msgCnt\030\003 \001(\r\"j\n\007MapData\022\020\n\010" +
      "mapSlice\030\001 \001(\t\022\"\n\003map\030\002 \001(\0132\025.cn.seisys." +
      "v2x.pb.MAP\022\014\n\004eTag\030\003 \001(\t\022\013\n\003ack\030\004 \001(\010\022\016\n" +
      "\006seqNum\030\005 \001(\t\"\227\001\n\rReqLaneChange\0227\n\014upStr" +
      "eamNode\030\001 \001(\0132!.cn.seisys.v2x.pb.NodeRef" +
      "erenceId\0229\n\016downStreamNode\030\002 \001(\0132!.cn.se" +
      "isys.v2x.pb.NodeReferenceId\022\022\n\ntargetLan" +
      "e\030\003 \001(\r\"\230\001\n\016ReqClearTheWay\0227\n\014upStreamNo" +
      "de\030\001 \001(\0132!.cn.seisys.v2x.pb.NodeReferenc" +
      "eId\0229\n\016downStreamNode\030\002 \001(\0132!.cn.seisys." +
      "v2x.pb.NodeReferenceId\022\022\n\ntargetLane\030\003 \001" +
      "(\r\"\305\001\n\021ReqSignalPriority\0229\n\016intersection" +
      "Id\030\001 \001(\0132!.cn.seisys.v2x.pb.NodeReferenc" +
      "eId\0228\n\014requiredMove\030\002 \001(\0132\".cn.seisys.v2" +
      "x.pb.MovementStatInfo\022\034\n\024estimatedArriva" +
      "lTime\030\003 \001(\r\022\035\n\025distance2Intersection\030\004 \001" +
      "(\r\"I\n\020ReqSensorSharing\0225\n\014detectorArea\030\001" +
      " \003(\0132\037.cn.seisys.v2x.pb.ReferencePath\"\035\n" +
      "\016ParkingRequest\022\013\n\003req\030\001 \001(\r\"\"\n\013ParkingT" +
      "ype\022\023\n\013parkingType\030\001 \001(\r\"\306\001\n\016ReqParkingA" +
      "rea\0222\n\013vehicleType\030\001 \001(\0162\035.cn.seisys.v2x" +
      ".pb.VehicleType\022-\n\003req\030\002 \001(\0132 .cn.seisys" +
      ".v2x.pb.ParkingRequest\0222\n\013parkingType\030\003 " +
      "\001(\0132\035.cn.seisys.v2x.pb.ParkingType\022\035\n\025ex" +
      "pectedParkingSlotId\030\004 \001(\r\"\272\002\n\007ReqInfo\0225\n" +
      "\nlaneChange\030\001 \001(\0132\037.cn.seisys.v2x.pb.Req" +
      "LaneChangeH\000\0227\n\013clearTheWay\030\002 \001(\0132 .cn.s" +
      "eisys.v2x.pb.ReqClearTheWayH\000\022=\n\016signalP" +
      "riority\030\003 \001(\0132#.cn.seisys.v2x.pb.ReqSign" +
      "alPriorityH\000\022;\n\rsensorSharing\030\004 \001(\0132\".cn" +
      ".seisys.v2x.pb.ReqSensorSharingH\000\0223\n\007par" +
      "king\030\005 \001(\0132 .cn.seisys.v2x.pb.ReqParking" +
      "AreaH\000B\016\n\014ReqInfoOneOf\"\235\002\n\014DriveRequest\022" +
      "\r\n\005reqId\030\001 \001(\r\0228\n\006status\030\002 \001(\0162(.cn.seis" +
      "ys.v2x.pb.DriveRequest.ReqStatus\022\023\n\013reqP" +
      "riority\030\003 \001(\t\022\021\n\ttargetVeh\030\004 \001(\t\022\021\n\ttarg" +
      "etRsu\030\005 \001(\t\022\'\n\004info\030\006 \001(\0132\031.cn.seisys.v2" +
      "x.pb.ReqInfo\022\020\n\010lifeTime\030\007 \001(\r\"N\n\tReqSta" +
      "tus\022\013\n\007UNKNOWN\020\000\022\013\n\007REQUEST\020\001\022\r\n\tCOMFIRM" +
      "ED\020\002\022\n\n\006CANCEL\020\003\022\014\n\010COMPLETE\020\004\"&\n\rDriveB" +
      "ehavior\022\025\n\rdriveBehavior\030\001 \001(\005\"\225\004\n\021PathP" +
      "lanningPoint\022)\n\003pos\030\001 \001(\0132\034.cn.seisys.v2" +
      "x.pb.Position3D\022:\n\tposConfid\030\002 \001(\0132\'.cn." +
      "seisys.v2x.pb.PositionConfidenceSet\022\r\n\005s" +
      "peed\030\003 \001(\r\022\017\n\007heading\030\004 \001(\r\0226\n\013speedConf" +
      "id\030\005 \001(\0162!.cn.seisys.v2x.pb.SpeedConfide" +
      "nce\022:\n\rheadingConfid\030\006 \001(\0162#.cn.seisys.v" +
      "2x.pb.HeadingConfidence\022;\n\014acceleration\030" +
      "\007 \001(\0132%.cn.seisys.v2x.pb.AccelerationSet" +
      "4Way\022D\n\022accelerationConfid\030\010 \001(\0132(.cn.se" +
      "isys.v2x.pb.AccelerationConfidence\022\025\n\res" +
      "timatedTime\030\t \001(\r\0228\n\016timeConfidence\030\n \001(" +
      "\0162 .cn.seisys.v2x.pb.TimeConfidence\0221\n\010p" +
      "osInMap\030\013 \001(\0132\037.cn.seisys.v2x.pb.Referen" +
      "ceLink\"I\n\014PathPlanning\0229\n\014pathPlanning\030\001" +
      " \003(\0132#.cn.seisys.v2x.pb.PathPlanningPoin" +
      "t\"\340\001\n\007IarData\0227\n\ncurrentPos\030\001 \001(\0132#.cn.s" +
      "eisys.v2x.pb.PathPlanningPoint\0224\n\014pathPl" +
      "anning\030\002 \001(\0132\036.cn.seisys.v2x.pb.PathPlan" +
      "ning\0228\n\017currentBehavior\030\003 \001(\0132\037.cn.seisy" +
      "s.v2x.pb.DriveBehavior\022,\n\004reqs\030\004 \003(\0132\036.c" +
      "n.seisys.v2x.pb.DriveRequest\"\230\001\n\007VirData" +
      "\022\016\n\006msgCnt\030\001 \001(\r\022\021\n\tvehicleId\030\002 \001(\t\022\021\n\tt" +
      "imestamp\030\003 \001(\004\022)\n\003pos\030\004 \001(\0132\034.cn.seisys." +
      "v2x.pb.Position3D\022,\n\tintAndReq\030\005 \001(\0132\031.c" +
      "n.seisys.v2x.pb.IarData\"\306\001\n\017DriveSuggest" +
      "ion\0223\n\nsuggestion\030\001 \001(\0132\037.cn.seisys.v2x." +
      "pb.DriveBehavior\022\022\n\ntimeOffset\030\002 \001(\r\0224\n\013" +
      "relatedLink\030\003 \001(\0132\037.cn.seisys.v2x.pb.Ref" +
      "erenceLink\0224\n\013relatedPath\030\004 \001(\0132\037.cn.sei" +
      "sys.v2x.pb.ReferencePath\",\n\020Coordination" +
      "Info\022\030\n\020coordinationInfo\030\001 \001(\005\"\310\001\n\023Vehic" +
      "leCoordination\022\r\n\005vehId\030\001 \001(\t\022:\n\017driveSu" +
      "ggestion\030\002 \001(\0132!.cn.seisys.v2x.pb.DriveS" +
      "uggestion\0224\n\014pathGuidance\030\003 \001(\0132\036.cn.sei" +
      "sys.v2x.pb.PathPlanning\0220\n\004info\030\004 \001(\0132\"." +
      "cn.seisys.v2x.pb.CoordinationInfo\"\272\002\n\020La" +
      "neCoordination\0223\n\ntargetLane\030\001 \001(\0132\037.cn." +
      "seisys.v2x.pb.ReferenceLink\0224\n\013relatedPa" +
      "th\030\002 \001(\0132\037.cn.seisys.v2x.pb.ReferencePat" +
      "h\022\016\n\006tBegin\030\003 \001(\004\022\014\n\004tEnd\030\004 \001(\004\022\030\n\020recom" +
      "mendedSpeed\030\005 \001(\r\022<\n\023recommendedBehavior" +
      "\030\006 \001(\0132\037.cn.seisys.v2x.pb.DriveBehavior\022" +
      "0\n\004info\030\007 \001(\0132\".cn.seisys.v2x.pb.Coordin" +
      "ationInfo\022\023\n\013description\030\010 \001(\t\"\337\001\n\007RscDa" +
      "ta\022\016\n\006msgCnt\030\001 \001(\r\022\r\n\005rsuId\030\002 \001(\t\022\021\n\ttim" +
      "estamp\030\003 \001(\004\022)\n\003pos\030\004 \001(\0132\034.cn.seisys.v2" +
      "x.pb.Position3D\022:\n\013coordinates\030\005 \001(\0132%.c" +
      "n.seisys.v2x.pb.VehicleCoordination\022;\n\017l" +
      "aneCoordinates\030\006 \001(\0132\".cn.seisys.v2x.pb." +
      "LaneCoordination\"\271\006\n\007CamData\022\014\n\004type\030\001 \001" +
      "(\r\022\013\n\003ver\030\002 \001(\t\022\016\n\006msgCnt\030\003 \001(\r\022\021\n\ttimes" +
      "tamp\030\004 \001(\004\022\020\n\010deviceId\030\005 \001(\t\022\023\n\013mapDevic" +
      "eId\030\006 \001(\t\022,\n\006refPos\030\007 \001(\0132\034.cn.seisys.v2" +
      "x.pb.Position3D\022.\n\tsceneType\030\010 \001(\0162\033.cn." +
      "seisys.v2x.pb.SceneType\0222\n\007ptcList\030\t \003(\013" +
      "2!.cn.seisys.v2x.pb.ParticipantData\0224\n\014o" +
      "bstacleList\030\n \003(\0132\036.cn.seisys.v2x.pb.Obs" +
      "tacleData\022*\n\007rteList\030\013 \003(\0132\031.cn.seisys.v" +
      "2x.pb.RteData\022*\n\007rtsList\030\014 \003(\0132\031.cn.seis" +
      "ys.v2x.pb.RtsData\022*\n\007bsmList\030\r \003(\0132\031.cn." +
      "seisys.v2x.pb.BsmData\022*\n\007virList\030\016 \003(\0132\031" +
      ".cn.seisys.v2x.pb.VirData\022*\n\007rscList\030\017 \003" +
      "(\0132\031.cn.seisys.v2x.pb.RscData\0223\n\017roadSig" +
      "nalState\030\020 \001(\0132\032.cn.seisys.v2x.pb.SpatDa" +
      "ta\0222\n\013trafficFlow\030\021 \003(\0132\035.cn.seisys.v2x." +
      "pb.TrafficFlow\0228\n\020signalSchemeList\030\022 \003(\013" +
      "2\036.cn.seisys.v2x.pb.SignalScheme\0221\n\016dete" +
      "ctedRegion\030\023 \003(\0132\031.cn.seisys.v2x.pb.Poly" +
      "gon\022\027\n\017toAlgorithmTime\030\024 \001(\004\022\025\n\rtoDatabu" +
      "sTime\030\025 \001(\004\022\023\n\013toCloudTime\030\026 \001(\004\022\n\n\002id\030\027" +
      " \001(\004\"\200\003\n\nStatusData\022\020\n\010deviceId\030\001 \001(\t\022\023\n" +
      "\013mapDeviceId\030\002 \001(\t\0220\n\ndeviceType\030\003 \001(\0162\034" +
      ".cn.seisys.v2x.pb.DeviceType\022;\n\nstatusTy" +
      "pe\030\004 \001(\0162\'.cn.seisys.v2x.pb.StatusData.S" +
      "tatusType\022/\n\tposDevice\030\005 \001(\0132\034.cn.seisys" +
      ".v2x.pb.Position3D\"\252\001\n\nStatusType\022\026\n\022DEV" +
      "_STATUS_UNKNOWN\020\000\022\021\n\rDEV_STATUS_OK\020\001\022\027\n\023" +
      "DEV_STATUS_ABNORMAL\020\002\022\022\n\016DEV_STATUS_OFF\020" +
      "\003\022\025\n\021DEV_STATUS_REBOOT\020\004\022\027\n\023DEV_STATUS_M" +
      "AINTAIN\020\005\022\024\n\020DEV_STATUS_SCRAP\020\006\"\351\001\n\010Denm" +
      "Data\022\014\n\004type\030\001 \001(\r\022\013\n\003ver\030\002 \001(\t\022\016\n\006msgCn" +
      "t\030\003 \001(\r\022\021\n\ttimestamp\030\004 \001(\004\022\017\n\007address\030\005 " +
      "\001(\t\022,\n\006refPos\030\006 \001(\0132\034.cn.seisys.v2x.pb.P" +
      "osition3D\022.\n\tsceneType\030\007 \001(\0162\033.cn.seisys" +
      ".v2x.pb.SceneType\0220\n\nstatusList\030\010 \003(\0132\034." +
      "cn.seisys.v2x.pb.StatusData\"\340\002\n\010RsiReply" +
      "\022\n\n\002id\030\001 \001(\004\022\021\n\teventType\030\002 \001(\r\022\026\n\016sourc" +
      "eDeviceId\030\003 \001(\t\022\026\n\016targetDeviceId\030\004 \001(\t\022" +
      "\021\n\tcreatTime\030\005 \001(\t\022\030\n\020distributionTime\030\006" +
      " \001(\t\022\026\n\016completionTime\030\007 \001(\t\022\022\n\nupdateTi" +
      "me\030\010 \001(\t\022\025\n\roperationType\030\t \001(\r\022\021\n\tcamDa" +
      "taId\030\n \001(\004\022\016\n\006dataId\030\013 \001(\004\022\025\n\reventSourc" +
      "eId\030\014 \001(\004\022\034\n\024distributionStatusId\030\r \001(\r\022" +
      "\023\n\013description\030\016 \001(\t\022\023\n\013sourceTopic\030\017 \001(" +
      "\t\022\023\n\013targetTopic\030\020 \001(\t\"\227\001\n\013RsuRsmReply\022\026" +
      "\n\016sourceDeviceId\030\001 \001(\t\022\026\n\016targetDeviceId" +
      "\030\002 \001(\t\022\021\n\tcamDataId\030\003 \001(\004\0220\n\014rsmReplyLis" +
      "t\030\004 \003(\0132\032.cn.seisys.v2x.pb.RsmReply\022\023\n\013t" +
      "argetTopic\030\005 \001(\t\"M\n\010RsmReply\022\016\n\006dataId\030\001" +
      " \001(\004\022\034\n\024distributionStatusId\030\002 \001(\r\022\023\n\013de" +
      "scription\030\003 \001(\t\"\324\002\n\020MonitorStatsData\022\021\n\t" +
      "timestamp\030\001 \001(\004\022\020\n\010deviceId\030\002 \001(\t\022\017\n\007cam" +
      "Nums\030\003 \001(\004\022\027\n\017participantNums\030\004 \001(\004\022\017\n\007r" +
      "teNums\030\005 \001(\004\022\027\n\017trafficflowNums\030\006 \001(\004\022\033\n" +
      "\023trafficflowStatNums\030\007 \001(\004\022\034\n\024intersecti" +
      "onStatNums\030\010 \001(\004\022\025\n\rphaseStatNums\030\t \001(\004\022" +
      "\017\n\007rtsNums\030\n \001(\004\022\032\n\022cameraPathListNums\030\013" +
      " \001(\004\022\026\n\016cameraPathNums\030\014 \001(\004\022\031\n\021radarPat" +
      "hListNums\030\r \001(\004\022\025\n\rradarPathNums\030\016 \001(\004*\311" +
      "\001\n\014Message_Type\022\016\n\nUKNOWN_MSG\020\000\022\016\n\nOBJEC" +
      "T_MSG\020\001\022\r\n\tEVENT_MSG\020\002\022\020\n\014OBSTACLE_MSG\020\003" +
      "\022\016\n\nSTATUS_MSG\020\004\022\013\n\007RTE_MSG\020\005\022\013\n\007RTS_MSG" +
      "\020\006\022\014\n\010SPAT_MSG\020\007\022\013\n\007MAP_MSG\020\010\022\013\n\007VIR_MSG" +
      "\020\t\022\013\n\007RSC_MSG\020\n\022\013\n\007CAM_MSG\020\013\022\014\n\010DENM_MSG" +
      "\020\014*\346\001\n\nDataSource\022\027\n\023DATA_SOURCE_UNKNOWN" +
      "\020\000\022\014\n\010SELFINFO\020\001\022\007\n\003V2X\020\002\022\t\n\005VIDEO\020\003\022\023\n\017" +
      "MICROWAVE_RADAR\020\004\022\010\n\004LOOP\020\005\022\t\n\005LIDAR\020\006\022\016" +
      "\n\nINTEGRATED\020\007\022\027\n\023DATA_SOURCE_RESERVE\020\010\022" +
      "\024\n\020CLOUD_FORWARDING\020\t\022\016\n\nMEC_TO_MEC\020\n\022\022\n" +
      "\016CLOUD_TO_CLOUD\020\013\022\020\n\014CLOUD_MANUAL\020\014*\370\006\n\016" +
      "TimeConfidence\022\017\n\013UNAVAILABLE\020\000\022\020\n\014TIME_" +
      "100_000\020\001\022\020\n\014TIME_050_000\020\002\022\020\n\014TIME_020_" +
      "000\020\003\022\020\n\014TIME_010_000\020\004\022\020\n\014TIME_002_000\020" +
      "\005\022\020\n\014TIME_001_000\020\006\022\020\n\014TIME_000_500\020\007\022\020\n" +
      "\014TIME_000_200\020\010\022\020\n\014TIME_000_100\020\t\022\020\n\014TIM" +
      "E_000_050\020\n\022\020\n\014TIME_000_020\020\013\022\020\n\014TIME_00" +
      "0_010\020\014\022\020\n\014TIME_000_005\020\r\022\020\n\014TIME_000_00" +
      "2\020\016\022\020\n\014TIME_000_001\020\017\022\022\n\016TIME_000_000_5\020" +
      "\020\022\022\n\016TIME_000_000_2\020\021\022\022\n\016TIME_000_000_1\020" +
      "\022\022\023\n\017TIME_000_000_05\020\023\022\023\n\017TIME_000_000_0" +
      "2\020\024\022\023\n\017TIME_000_000_01\020\025\022\024\n\020TIME_000_000" +
      "_005\020\026\022\024\n\020TIME_000_000_002\020\027\022\024\n\020TIME_000" +
      "_000_001\020\030\022\026\n\022TIME_000_000_000_5\020\031\022\026\n\022TI" +
      "ME_000_000_000_2\020\032\022\026\n\022TIME_000_000_000_1" +
      "\020\033\022\027\n\023TIME_000_000_000_05\020\034\022\027\n\023TIME_000_" +
      "000_000_02\020\035\022\027\n\023TIME_000_000_000_01\020\036\022\030\n" +
      "\024TIME_000_000_000_005\020\037\022\030\n\024TIME_000_000_" +
      "000_002\020 \022\030\n\024TIME_000_000_000_001\020!\022\032\n\026T" +
      "IME_000_000_000_000_5\020\"\022\032\n\026TIME_000_000_" +
      "000_000_2\020#\022\032\n\026TIME_000_000_000_000_1\020$\022" +
      "\033\n\027TIME_000_000_000_000_05\020%\022\033\n\027TIME_000" +
      "_000_000_000_02\020&\022\033\n\027TIME_000_000_000_00" +
      "0_01\020\'*\210\001\n\017ParticipantType\022\026\n\022OBJECTTYPE" +
      "_UNKNOWN\020\000\022\024\n\020OBJECTTYPE_MOTOR\020\001\022\030\n\024OBJE" +
      "CTTYPE_NON_MOTOR\020\002\022\031\n\025OBJECTTYPE_PEDESTR" +
      "IAN\020\003\022\022\n\016OBJECTTYPE_RSU\020\004*\324\001\n\017SpeedConfi" +
      "dence\022\034\n\030SPEED_CONFID_UNAVAILABLE\020\000\022\026\n\022S" +
      "PEED_CONFID_100MS\020\001\022\025\n\021SPEED_CONFID_10MS" +
      "\020\002\022\024\n\020SPEED_CONFID_5MS\020\003\022\024\n\020SPEED_CONFID" +
      "_1MS\020\004\022\026\n\022SPEED_CONFID_0_1MS\020\005\022\027\n\023SPEED_" +
      "CONFID_0_05MS\020\006\022\027\n\023SPEED_CONFID_0_01MS\020\007" +
      "*\217\002\n\021HeadingConfidence\022\036\n\032HEADING_CONFID" +
      "_UNAVAILABLE\020\000\022\034\n\030HEADING_CONFID_PREC10D" +
      "EG\020\001\022\035\n\031HEADING_CONFIDE_PREC05DEG\020\002\022\035\n\031H" +
      "EADING_CONFIDE_PREC01DEG\020\003\022\034\n\030HEADING_CO" +
      "NFID_PREC_1DEG\020\004\022\036\n\032HEADING_CONFID_PREC0" +
      "_05DEG\020\005\022\036\n\032HEADING_CONFID_PREC0_01DEG\020\006" +
      "\022 \n\034HEADING_CONFID_PREC0_0125DEG\020\007*\232\r\n\013V" +
      "ehicleType\022\031\n\025UNKNOWN_VEHICLE_CLASS\020\000\022\"\n" +
      "\036PASSENGER_VEHICLE_TYPE_UNKNOWN\020\n\022\024\n\020MOT" +
      "OR_LIGHTTRUNK\020\024\022\036\n\032TRUCK_VEHICLE_TYPE_UN" +
      "KNOWN\020\031\022\033\n\027MOTORCYCLE_TYPE_UNKNOWN\020(\022\030\n\024" +
      "TRANSIT_TYPE_UNKNOWN\0202\022\032\n\026EMERGENCY_TYPE" +
      "_UNKNOWN\020<\022\031\n\025SPECIAL_VEHICLE_CLASS\020\001\022 \n" +
      "\034PASSENGER_VEHICLE_TYPE_OTHER\020\013\022\"\n\036LIGHT" +
      "_TRUCK_VEHICLE_TYPE_OTHER\020\025\022\034\n\030TRUCK_VEH" +
      "ICLE_TYPE_OTHER\020\032\022\023\n\017TRUCK_AXLE_CNT2\020\033\022\023" +
      "\n\017TRUCK_AXLE_CNT3\020\034\022\023\n\017TRUCK_AXLE_CNT4\020\035" +
      "\022\033\n\027TRUCK_AXLE_CNT4_TRAILER\020\036\022\033\n\027TRUCK_A" +
      "XLE_CNT5_TRAILER\020\037\022\033\n\027TRUCK_AXLE_CNT6_TR" +
      "AILER\020 \022\036\n\032TRUCK_AXLECNT5MULTITRAILER\020!\022" +
      "!\n\035TRUCK_AXLE_CNT6_MULTI_TRAILER\020\"\022!\n\035TR" +
      "UCK_AXLE_CNT7_MULTI_TRAILER\020#\022\031\n\025MOTORCY" +
      "CLE_TYPE_OTHER\020)\022\037\n\033MOTORCYCLE_CRUISER_S" +
      "TANDARD\020*\022(\n$SPORT_UNCLAD_MOTORCYCLE_SPO" +
      "RT_UNCLAD\020+\022\034\n\030MOTORCYCLE_SPORT_TOURING\020" +
      ",\022\032\n\026MOTORCYCLE_SUPER_SPORT\020-\022\026\n\022MOTORCY" +
      "CLE_TOURING\020.\022\024\n\020MOTORCYCLE_TRIKE\020/\022\032\n\026M" +
      "OTORCYCLE_WPASSENGERS\0200\022\026\n\022TRANSIT_TYPE_" +
      "OTHER\0203\022\017\n\013TRANSIT_BRT\0204\022\027\n\023TRANSIT_EXPR" +
      "ESS_BUS\0205\022\025\n\021TRANSIT_LOCAL_BUS\0206\022\026\n\022TRAN" +
      "SIT_SCHOOL_BUS\0207\022\032\n\026TRANSIT_FIXED_GUIdEW" +
      "AY\0208\022\027\n\023TRANSIT_PARATRANSIT\0209\022!\n\035TRANSIT" +
      "_PARATRANSIT_AMBULANCE\020:\022\030\n\024EMERGENCY_TY" +
      "PE_OTHER\020=\022 \n\034EMERGENCY_FIRE_LIGHT_VEHIC" +
      "LE\020>\022 \n\034EMERGENCY_FIRE_HEAVY_VEHICLE\020?\022$" +
      "\n EMERGENCY_FIRE_PARAMEDIC_VEHICLE\020@\022$\n " +
      "EMERGENCY_FIRE_AMBULANCE_VEHICLE\020A\022\"\n\036EM" +
      "ERGENCY_POLICE_LIGHT_VEHICLE\020B\022\"\n\036EMERGE" +
      "NCY_POLICE_HEAVY_VEHICLE\020C\022\035\n\031EMERGENCY_" +
      "OTHER_RESPONDER\020D\022\035\n\031EMERGENCY_OTHER_AMB" +
      "ULANCE\020E\022\037\n\033OTHER_TRAVELER_TYPE_UNKNOWN\020" +
      "P\022\035\n\031OTHER_TRAVELER_TYPE_OTHER\020Q\022\035\n\031OTHE" +
      "R_TRAVELER_PEDESTRIAN\020R\022$\n OTHER_TRAVELE" +
      "R_VISUALLY_DISABLED\020S\022&\n\"OTHER_TRAVELER_" +
      "PHYSICALLY_DISABLED\020T\022\032\n\026OTHER_TRAVELER_" +
      "BICYCLE\020U\022)\n%OTHER_TRAVELER_VULNERABLE_R" +
      "OAD_WORKER\020V\022\037\n\033INFRASTRUCTURE_TYPE_UNKN" +
      "OWN\020Z\022\030\n\024INFRASTRUCTURE_FIXED\020[\022\032\n\026INFRA" +
      "STRUCTURE_MOVABLE\020\\\022\032\n\026EQUIPPED_CARGO_TR" +
      "AILER\020]*f\n\010Maneuver\022\025\n\021MANEUVER_STRAIGHT" +
      "\020\000\022\026\n\022MANEUVER_LEFT_TURN\020\001\022\027\n\023MANEUVER_R" +
      "IGHT_TURN\020\002\022\022\n\016MANEUVER_UTURN\020\003*\340\001\n\016MapE" +
      "lementType\022\034\n\030MAP_ELEMENT_TYPE_UNKNOWN\020\000" +
      "\022\"\n\036MAP_ELEMENT_TYPE_DETECTOR_AREA\020\001\022\031\n\025" +
      "MAP_ELEMENT_TYPE_LANE\020\002\022\034\n\030MAP_ELEMENT_T" +
      "YPE_SECTION\020\003\022\031\n\025MAP_ELEMENT_TYPE_LINK\020\004" +
      "\022\031\n\025MAP_ELEMENT_TYPE_NODE\020\005\022\035\n\031MAP_ELEME" +
      "NT_TYPE_MOVEMENT\020\006*\374\006\n\tPlateType\022\021\n\rUNKN" +
      "OWN_PLATE\020\000\022\023\n\017LARGE_CAR_PLATE\020\001\022\023\n\017SMAL" +
      "L_CAR_PLATE\020\002\022\025\n\021EMBASSY_CAR_PLATE\020\003\022\027\n\023" +
      "CONSULATE_CAR_PLATE\020\004\022\026\n\022OVERSEAS_CAR_PL" +
      "ATE\020\005\022\025\n\021FOREIGN_CAR_PLATE\020\006\022\035\n\031ORDINARY" +
      "_MOTORCYCLE_PLATE\020\007\022\017\n\013MOPED_PLATE\020\010\022\034\n\030" +
      "EMBASSY_MOTORCYCLE_PLATE\020\t\022\036\n\032CONSULATE_" +
      "MOTORCYCLE_PLATE\020\n\022\035\n\031OVERSEAS_MOTORCYCL" +
      "E_PLATE\020\013\022\034\n\030FOREIGN_MOTORCYCLE_PLATE\020\014\022" +
      "\023\n\017LOW_SPEED_PLATE\020\r\022\021\n\rTRACTOR_PLATE\020\016\022",
      "\021\n\rTRAILER_PLATE\020\017\022\023\n\017COACH_CAR_PLATE\020\020\022" +
      "\032\n\026COACH_MOTORCYCLE_PLATE\020\021\022\031\n\025TEMPORARY" +
      "_ENTRY_PLATE\020\024\022$\n TEMPORARY_ENTRY_MOTORC" +
      "YCLE_PLATE\020\025\022\033\n\027TEMPORARY_DRIVING_PLATE\020" +
      "\026\022\024\n\020POLICE_CAR_PLATE\020\027\022\033\n\027POLICE_MOTORC" +
      "YCLE_PLATE\020\030\022)\n%ORIGINAL_AGRICULTURAL_MA" +
      "CHINERY_PLATE\020\031\022\022\n\016HONGKONG_PLATE\020\032\022\017\n\013M" +
      "ACAU_PLATE\020\033\022\026\n\022ARMED_POLICE_PLATE\020\037\022\016\n\n" +
      "ARMY_PLATE\020 \022\023\n\017NO_NUMBER_PLATE\020)\022\016\n\nFAK" +
      "E_PLATE\020*\022\032\n\026MISAPPROPRIATION_PLATE\020+\022\026\n" +
      "\022UNRECOGNIZED_PLATE\020,\022\'\n#LARGE_NEW_ENERG" +
      "Y_YELLOW_GREEN_PLATE\0203\022 \n\034SMALL_NEW_ENER" +
      "GY_GREEN_PLATE\0204\022\017\n\013OTHER_PLATE\020c*\257\002\n\rOb" +
      "staclesType\022\032\n\026UNKNOWN_OBSTACLES_TYPE\020\000\022" +
      "\014\n\010ROCKFALL\020\001\022\r\n\tLANDSLIDE\020\002\022\024\n\020ANIMAL_I" +
      "NTRUSION\020\003\022\020\n\014LIQUID_SPILL\020\004\022\023\n\017GOODS_SC" +
      "ATTERED\020\005\022\017\n\013TRAFFICCONE\020\006\022\023\n\017SAFETY_TRI" +
      "ANGLE\020\007\022\025\n\021TRAFFIC_ROADBLOCK\020\010\022\"\n\036INSPEC" +
      "TION_SHAFT_WITHOUT_COVER\020\t\022\025\n\021UNKNOWN_FR" +
      "AGMENTS\020\n\022\027\n\023UNKNOWN_HARD_OBJECT\020\013\022\027\n\023UN" +
      "KNOWN_SOFT_OBJECT\020\014*o\n\tSceneType\022\024\n\020SCEN" +
      "E_TYPE_URBAN\020\000\022\030\n\024SCENE_TYPE_HIGHSPEED\020\001" +
      "\022\031\n\025SCENE_TYPE_CLOSEDPARK\020\002\022\027\n\023SCENE_TYP" +
      "E_RESERVED\020\003*\205\003\n\nDeviceType\022\027\n\023DEVICE_TY" +
      "PE_UNKONWN\020\000\022\007\n\003OBU\020\001\022\007\n\003RSU\020\002\022\t\n\005OTHER\020" +
      "d\022\013\n\006CAMERA\020\310\001\022\020\n\013MICRO_RADAR\020\312\001\022\020\n\013LASE" +
      "R_RADAR\020\313\001\022\010\n\003MEC\020\314\001\022\n\n\005SIBOX\020\315\001\022\023\n\016WEAT" +
      "HER_SENSOR\020\316\001\022\010\n\003VMS\020\317\001\022\016\n\tMMW_RADAR\020\320\001\022" +
      "\n\n\005CLOUD\020\321\001\022\024\n\017ELECTRONIC_TAGS\020\322\001\022\025\n\020WIS" +
      "DOM_LIGHTPOLE\020\323\001\022\030\n\023WISDOM_MANHOLECOVER\020" +
      "\324\001\022\024\n\017WISDOM_PLATFORM\020\325\001\022\014\n\007CARRIER\020\326\001\022\027" +
      "\n\022INTEGRATED_CABINET\020\330\001\022\014\n\007CORE_SW\020\331\001\022\016\n" +
      "\tGATHER_SW\020\332\001\022\016\n\tACCESS_SW\020\333\001\022\r\n\010POLE_BO" +
      "X\020\334\001B\023\n\017road.data.protoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_cn_seisys_v2x_pb_RsiTimeDetails_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_cn_seisys_v2x_pb_RsiTimeDetails_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_RsiTimeDetails_descriptor,
        new java.lang.String[] { "StartTime", "EndTime", "EndTimeConfidence", });
    internal_static_cn_seisys_v2x_pb_Position3D_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_cn_seisys_v2x_pb_Position3D_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_Position3D_descriptor,
        new java.lang.String[] { "Lat", "Lon", "Ele", });
    internal_static_cn_seisys_v2x_pb_PositionConfidenceSet_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_cn_seisys_v2x_pb_PositionConfidenceSet_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_PositionConfidenceSet_descriptor,
        new java.lang.String[] { "PosConfid", "EleConfid", });
    internal_static_cn_seisys_v2x_pb_ParticipantSize_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_cn_seisys_v2x_pb_ParticipantSize_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ParticipantSize_descriptor,
        new java.lang.String[] { "Width", "Length", "Height", });
    internal_static_cn_seisys_v2x_pb_ParticipantSizeConfidence_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_cn_seisys_v2x_pb_ParticipantSizeConfidence_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ParticipantSizeConfidence_descriptor,
        new java.lang.String[] { "WidthConfid", "LengthConfid", "HeightConfid", });
    internal_static_cn_seisys_v2x_pb_Polygon_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_cn_seisys_v2x_pb_Polygon_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_Polygon_descriptor,
        new java.lang.String[] { "Pos", });
    internal_static_cn_seisys_v2x_pb_DetectorArea_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_cn_seisys_v2x_pb_DetectorArea_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_DetectorArea_descriptor,
        new java.lang.String[] { "AreaId", "SetTime", "Polygon", "NodeId", "LaneId", });
    internal_static_cn_seisys_v2x_pb_RegulatorySpeedLimit_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_cn_seisys_v2x_pb_RegulatorySpeedLimit_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_RegulatorySpeedLimit_descriptor,
        new java.lang.String[] { "SpeedLimitType", "Speed", });
    internal_static_cn_seisys_v2x_pb_AccelerationSet4Way_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_cn_seisys_v2x_pb_AccelerationSet4Way_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_AccelerationSet4Way_descriptor,
        new java.lang.String[] { "Lat", "Lon", "Vert", "Yaw", });
    internal_static_cn_seisys_v2x_pb_AccelerationConfidence_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_cn_seisys_v2x_pb_AccelerationConfidence_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_AccelerationConfidence_descriptor,
        new java.lang.String[] { "LonAccelConfid", "LatAccelConfid", "VerticalAccelConfid", "YawRateConfid", });
    internal_static_cn_seisys_v2x_pb_MotionConfidenceSet_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_cn_seisys_v2x_pb_MotionConfidenceSet_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_MotionConfidenceSet_descriptor,
        new java.lang.String[] { "SpeedCfd", "HeadingCfd", "SteerCfd", });
    internal_static_cn_seisys_v2x_pb_VehicleSize_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_cn_seisys_v2x_pb_VehicleSize_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_VehicleSize_descriptor,
        new java.lang.String[] { "Width", "Length", "Height", });
    internal_static_cn_seisys_v2x_pb_NodeReferenceId_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_cn_seisys_v2x_pb_NodeReferenceId_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_NodeReferenceId_descriptor,
        new java.lang.String[] { "Region", "NodeId", });
    internal_static_cn_seisys_v2x_pb_MapLocation_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_cn_seisys_v2x_pb_MapLocation_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_MapLocation_descriptor,
        new java.lang.String[] { "NodeId", "LinkName", "UpstreamNodeId", "SectionId", "LaneId", });
    internal_static_cn_seisys_v2x_pb_PhaseId_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_cn_seisys_v2x_pb_PhaseId_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_PhaseId_descriptor,
        new java.lang.String[] { "PhaseId", });
    internal_static_cn_seisys_v2x_pb_ReferenceLanes_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_cn_seisys_v2x_pb_ReferenceLanes_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ReferenceLanes_descriptor,
        new java.lang.String[] { "ReferenceLanes", });
    internal_static_cn_seisys_v2x_pb_ReferencePath_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_cn_seisys_v2x_pb_ReferencePath_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ReferencePath_descriptor,
        new java.lang.String[] { "ActivePath", "PathRadius", });
    internal_static_cn_seisys_v2x_pb_ReferenceLink_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_cn_seisys_v2x_pb_ReferenceLink_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ReferenceLink_descriptor,
        new java.lang.String[] { "UpstreamNodeId", "DownstreamNodeId", "ReferenceLanes", });
    internal_static_cn_seisys_v2x_pb_AllowedManeuvers_descriptor =
      getDescriptor().getMessageTypes().get(18);
    internal_static_cn_seisys_v2x_pb_AllowedManeuvers_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_AllowedManeuvers_descriptor,
        new java.lang.String[] { "Maneuver", });
    internal_static_cn_seisys_v2x_pb_LaneStatInfo_descriptor =
      getDescriptor().getMessageTypes().get(19);
    internal_static_cn_seisys_v2x_pb_LaneStatInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneStatInfo_descriptor,
        new java.lang.String[] { "LaneId", "LinkStatInfo", "SectionStatInfo", "ExtId", });
    internal_static_cn_seisys_v2x_pb_SectionStatInfo_descriptor =
      getDescriptor().getMessageTypes().get(20);
    internal_static_cn_seisys_v2x_pb_SectionStatInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_SectionStatInfo_descriptor,
        new java.lang.String[] { "SectionId", "LinkStatInfo", "ExtId", });
    internal_static_cn_seisys_v2x_pb_LinkStatInfo_descriptor =
      getDescriptor().getMessageTypes().get(21);
    internal_static_cn_seisys_v2x_pb_LinkStatInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LinkStatInfo_descriptor,
        new java.lang.String[] { "UpstreamNodeId", "Name", "NodeStatInfo", "ExtId", });
    internal_static_cn_seisys_v2x_pb_NodeStatInfo_descriptor =
      getDescriptor().getMessageTypes().get(22);
    internal_static_cn_seisys_v2x_pb_NodeStatInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_NodeStatInfo_descriptor,
        new java.lang.String[] { "NodeId", });
    internal_static_cn_seisys_v2x_pb_MovementStatInfo_descriptor =
      getDescriptor().getMessageTypes().get(23);
    internal_static_cn_seisys_v2x_pb_MovementStatInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_MovementStatInfo_descriptor,
        new java.lang.String[] { "RemoteIntersection", "TurnDirection", "NodeStatInfo", "ExtId", });
    internal_static_cn_seisys_v2x_pb_TrafficFlowStatByInterval_descriptor =
      getDescriptor().getMessageTypes().get(24);
    internal_static_cn_seisys_v2x_pb_TrafficFlowStatByInterval_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_TrafficFlowStatByInterval_descriptor,
        new java.lang.String[] { "Interval", });
    internal_static_cn_seisys_v2x_pb_TrafficFlowStatBySignalCycle_descriptor =
      getDescriptor().getMessageTypes().get(25);
    internal_static_cn_seisys_v2x_pb_TrafficFlowStatBySignalCycle_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_TrafficFlowStatBySignalCycle_descriptor,
        new java.lang.String[] { "CycleStartTime", "CycleEndTime", "CycleTime", });
    internal_static_cn_seisys_v2x_pb_TrafficFlowStatType_descriptor =
      getDescriptor().getMessageTypes().get(26);
    internal_static_cn_seisys_v2x_pb_TrafficFlowStatType_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_TrafficFlowStatType_descriptor,
        new java.lang.String[] { "Interval", "Sequence", });
    internal_static_cn_seisys_v2x_pb_TrafficFlowStatMapElement_descriptor =
      getDescriptor().getMessageTypes().get(27);
    internal_static_cn_seisys_v2x_pb_TrafficFlowStatMapElement_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_TrafficFlowStatMapElement_descriptor,
        new java.lang.String[] { "DetectorArea", "LaneStatInfo", "SectionStatInfo", "LinkStatInfo", "NodeStatInfo", "MovementStatInfo", "TrafficFlowStatMapElementOneOf", });
    internal_static_cn_seisys_v2x_pb_LaneIndexAdded_descriptor =
      getDescriptor().getMessageTypes().get(28);
    internal_static_cn_seisys_v2x_pb_LaneIndexAdded_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneIndexAdded_descriptor,
        new java.lang.String[] { "Timestamp", "LaneCapacity", "LaneSaturation", "LaneSpaceOccupy", "LaneTimeOccupy", "LaneAvgGrnQueue", "LaneGrnUtilization", });
    internal_static_cn_seisys_v2x_pb_LinkIndexAdded_descriptor =
      getDescriptor().getMessageTypes().get(29);
    internal_static_cn_seisys_v2x_pb_LinkIndexAdded_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LinkIndexAdded_descriptor,
        new java.lang.String[] { "Timestamp", "LinkCapacity", "LinkSaturation", "LinkSpaceOccupy", "LinkTimeOccupy", "LinkAvgGrnQueue", "LinkGrnUtilization", });
    internal_static_cn_seisys_v2x_pb_MovementIndexAdded_descriptor =
      getDescriptor().getMessageTypes().get(30);
    internal_static_cn_seisys_v2x_pb_MovementIndexAdded_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_MovementIndexAdded_descriptor,
        new java.lang.String[] { "Timestamp", "MovementCapacity", "MovementSaturation", "MovementSpaceOccupy", "MovementTimeOccupy", "MovementAvgGrnQueue", "MovementGrnUtilization", });
    internal_static_cn_seisys_v2x_pb_NodeIndexAdded_descriptor =
      getDescriptor().getMessageTypes().get(31);
    internal_static_cn_seisys_v2x_pb_NodeIndexAdded_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_NodeIndexAdded_descriptor,
        new java.lang.String[] { "Timestamp", "NodeSpaceOccupy", "NodeTimeOccupy", "NodeCapacity", "NodeSaturation", "NodeGrnUtilization", "NodeAvgGrnQueue", "DemandIndex", "SupplyIndex", "TheoryIndex", });
    internal_static_cn_seisys_v2x_pb_SignalControlIndexAdded_descriptor =
      getDescriptor().getMessageTypes().get(32);
    internal_static_cn_seisys_v2x_pb_SignalControlIndexAdded_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_SignalControlIndexAdded_descriptor,
        new java.lang.String[] { "PhaseId", "GreenStartQueue", "RedStartQueue", "GreenUtilization", });
    internal_static_cn_seisys_v2x_pb_TrafficFlowExtension_descriptor =
      getDescriptor().getMessageTypes().get(33);
    internal_static_cn_seisys_v2x_pb_TrafficFlowExtension_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_TrafficFlowExtension_descriptor,
        new java.lang.String[] { "LaneIndex", "LinkIndex", "MovementIndex", "NodeIndex", "SignalIndex", });
    internal_static_cn_seisys_v2x_pb_TrafficFlowStat_descriptor =
      getDescriptor().getMessageTypes().get(34);
    internal_static_cn_seisys_v2x_pb_TrafficFlowStat_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_TrafficFlowStat_descriptor,
        new java.lang.String[] { "MapElement", "MapElementType", "PtcType", "VehicleType", "Timestamp", "Volume", "SpeedPoint", "SpeedArea", "Density", "TravelTime", "Delay", "QueueLength", "QueueInt", "Congestion", "TrafficFlowExtension", "TimeHeadway", "SpaceHeadway", "StopNums", });
    internal_static_cn_seisys_v2x_pb_TrafficFlow_descriptor =
      getDescriptor().getMessageTypes().get(35);
    internal_static_cn_seisys_v2x_pb_TrafficFlow_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_TrafficFlow_descriptor,
        new java.lang.String[] { "NodeId", "GenTime", "StatType", "Stats", });
    internal_static_cn_seisys_v2x_pb_TimeCountingDown_descriptor =
      getDescriptor().getMessageTypes().get(36);
    internal_static_cn_seisys_v2x_pb_TimeCountingDown_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_TimeCountingDown_descriptor,
        new java.lang.String[] { "StartTime", "MinEndTime", "MaxEndTime", "LikelyEndTime", "TimeConfidence", "NextStartTime", "NextDuration", });
    internal_static_cn_seisys_v2x_pb_PhaseState_descriptor =
      getDescriptor().getMessageTypes().get(37);
    internal_static_cn_seisys_v2x_pb_PhaseState_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_PhaseState_descriptor,
        new java.lang.String[] { "Light", "Timing", });
    internal_static_cn_seisys_v2x_pb_Phase_descriptor =
      getDescriptor().getMessageTypes().get(38);
    internal_static_cn_seisys_v2x_pb_Phase_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_Phase_descriptor,
        new java.lang.String[] { "Id", "PhaseStates", });
    internal_static_cn_seisys_v2x_pb_IntersectionState_descriptor =
      getDescriptor().getMessageTypes().get(39);
    internal_static_cn_seisys_v2x_pb_IntersectionState_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_IntersectionState_descriptor,
        new java.lang.String[] { "IntersectionId", "Status", "Timestamp", "TimeConfidence", "Phases", });
    internal_static_cn_seisys_v2x_pb_SpatData_descriptor =
      getDescriptor().getMessageTypes().get(40);
    internal_static_cn_seisys_v2x_pb_SpatData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_SpatData_descriptor,
        new java.lang.String[] { "MsgCnt", "Timestamp", "Intersections", });
    internal_static_cn_seisys_v2x_pb_LocalTimePoint_descriptor =
      getDescriptor().getMessageTypes().get(41);
    internal_static_cn_seisys_v2x_pb_LocalTimePoint_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LocalTimePoint_descriptor,
        new java.lang.String[] { "Hh", "Mm", "Ss", });
    internal_static_cn_seisys_v2x_pb_PeriodictimeSpan_descriptor =
      getDescriptor().getMessageTypes().get(42);
    internal_static_cn_seisys_v2x_pb_PeriodictimeSpan_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_PeriodictimeSpan_descriptor,
        new java.lang.String[] { "MonthFilter", "DayFilter", "WeekdayFilter", "FromTimePoint", "ToTimePoint", });
    internal_static_cn_seisys_v2x_pb_SingleTimeSpan_descriptor =
      getDescriptor().getMessageTypes().get(43);
    internal_static_cn_seisys_v2x_pb_SingleTimeSpan_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_SingleTimeSpan_descriptor,
        new java.lang.String[] { "StartTime", "EndTime", });
    internal_static_cn_seisys_v2x_pb_OptimTimeType_descriptor =
      getDescriptor().getMessageTypes().get(44);
    internal_static_cn_seisys_v2x_pb_OptimTimeType_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_OptimTimeType_descriptor,
        new java.lang.String[] { "Single", "Periodic", "OptimTimeTypeOneOf", });
    internal_static_cn_seisys_v2x_pb_MovementEx_descriptor =
      getDescriptor().getMessageTypes().get(45);
    internal_static_cn_seisys_v2x_pb_MovementEx_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_MovementEx_descriptor,
        new java.lang.String[] { "RemoteIntersection", "PhaseId", "TurnDirection", });
    internal_static_cn_seisys_v2x_pb_OptimPhase_descriptor =
      getDescriptor().getMessageTypes().get(46);
    internal_static_cn_seisys_v2x_pb_OptimPhase_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_OptimPhase_descriptor,
        new java.lang.String[] { "PhaseId", "Order", "MovementId", "PhaseTime", "Green", "PhaseYellowTime", "PhaseAllRedTime", "MinGreen", "MaxGreen", });
    internal_static_cn_seisys_v2x_pb_OptimData_descriptor =
      getDescriptor().getMessageTypes().get(47);
    internal_static_cn_seisys_v2x_pb_OptimData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_OptimData_descriptor,
        new java.lang.String[] { "OptimTimeType", "OptimCycleTime", "MinCycleTime", "MaxCycleTime", "OptimPhaseList", "CoorPhase", "Offset", });
    internal_static_cn_seisys_v2x_pb_SignalScheme_descriptor =
      getDescriptor().getMessageTypes().get(48);
    internal_static_cn_seisys_v2x_pb_SignalScheme_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_SignalScheme_descriptor,
        new java.lang.String[] { "NodeId", "OptimType", "Timestamp", "OptimDataList", });
    internal_static_cn_seisys_v2x_pb_BrakeSystemStatus_descriptor =
      getDescriptor().getMessageTypes().get(49);
    internal_static_cn_seisys_v2x_pb_BrakeSystemStatus_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_BrakeSystemStatus_descriptor,
        new java.lang.String[] { "BrakePadel", "WheelBrakes", "Traction", "Abs", "Scs", "BrakeBoost", "AuxBrakes", "BrakeControl", });
    internal_static_cn_seisys_v2x_pb_PositionAccuracy_descriptor =
      getDescriptor().getMessageTypes().get(50);
    internal_static_cn_seisys_v2x_pb_PositionAccuracy_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_PositionAccuracy_descriptor,
        new java.lang.String[] { "SemiMajor", "SemiMinor", "Orientation", });
    internal_static_cn_seisys_v2x_pb_ThrottleSystemStatus_descriptor =
      getDescriptor().getMessageTypes().get(51);
    internal_static_cn_seisys_v2x_pb_ThrottleSystemStatus_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ThrottleSystemStatus_descriptor,
        new java.lang.String[] { "ThorttleControl", "ThrottlePadel", "WheelThrottles", });
    internal_static_cn_seisys_v2x_pb_BsmData_descriptor =
      getDescriptor().getMessageTypes().get(52);
    internal_static_cn_seisys_v2x_pb_BsmData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_BsmData_descriptor,
        new java.lang.String[] { "ObuId", "PlateNo", "Timestamp", "Pos", "PosConfid", "PosAccuracy", "Acceleration", "Transmission", "Speed", "Heading", "SteeringWheelAngle", "MotionConfid", "Brakes", "Throttle", "Size", "VehicleType", "FuelType", "DriveModedriveStatus", "EmergencyStatus", "Light", "Wiper", "OutofControl", "Endurance", });
    internal_static_cn_seisys_v2x_pb_PathHistoryPoint_descriptor =
      getDescriptor().getMessageTypes().get(53);
    internal_static_cn_seisys_v2x_pb_PathHistoryPoint_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_PathHistoryPoint_descriptor,
        new java.lang.String[] { "Pos", "TimeOffset", "Speed", "PosConfid", "Heading", });
    internal_static_cn_seisys_v2x_pb_ParticipantData_descriptor =
      getDescriptor().getMessageTypes().get(54);
    internal_static_cn_seisys_v2x_pb_ParticipantData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ParticipantData_descriptor,
        new java.lang.String[] { "PtcId", "PtcType", "DataSource", "DeviceIdList", "Timestamp", "TimeConfidence", "PtcPos", "MapLocation", "PosConfid", "Speed", "Heading", "MotionConfid", "AccelSet", "AccelerationConfid", "PtcSize", "VehicleBand", "VehicleType", "PlateNo", "PlateType", "PlateColor", "VehicleColor", "PtcSizeConfid", "PtcTypeExt", "PtcTypeExtConfid", "StatusDuration", "PathHistory", "Tracking", "Polygon", "Id", });
    internal_static_cn_seisys_v2x_pb_ObstacleData_descriptor =
      getDescriptor().getMessageTypes().get(55);
    internal_static_cn_seisys_v2x_pb_ObstacleData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ObstacleData_descriptor,
        new java.lang.String[] { "ObsId", "ObsType", "ObstypeCfd", "ObsSource", "Timestamp", "DeviceIdList", "ObsPos", "PosConfid", "MapLocation", "Speed", "Heading", "MotionConfid", "VerSpeed", "VerSpeedConfid", "Acceleration", "Size", "ObsSizeConfid", "Tracking", "Polygon", });
    internal_static_cn_seisys_v2x_pb_ObjIdValue_descriptor =
      getDescriptor().getMessageTypes().get(56);
    internal_static_cn_seisys_v2x_pb_ObjIdValue_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ObjIdValue_descriptor,
        new java.lang.String[] { "PtcId", "ObsId", "Role", });
    internal_static_cn_seisys_v2x_pb_RteData_descriptor =
      getDescriptor().getMessageTypes().get(57);
    internal_static_cn_seisys_v2x_pb_RteData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_RteData_descriptor,
        new java.lang.String[] { "RteId", "RteType", "Description", "EventSource", "DataSource", "DeviceIdList", "RtePos", "MapLocation", "EventRadius", "TimeDetails", "Priority", "ReferencePath", "ReferenceLinks", "EventObjId", "EventConfid", "EventImages", "EventVideos", "SessionId", "Id", });
    internal_static_cn_seisys_v2x_pb_RtsData_descriptor =
      getDescriptor().getMessageTypes().get(58);
    internal_static_cn_seisys_v2x_pb_RtsData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_RtsData_descriptor,
        new java.lang.String[] { "RtsId", "RtsType", "DataSource", "Priority", "RtsPos", "TimeDetails", "Description", "RefPathList", "RefLinkList", "PathRadius", "SessionId", "Id", });
    internal_static_cn_seisys_v2x_pb_ConnectingLane_descriptor =
      getDescriptor().getMessageTypes().get(59);
    internal_static_cn_seisys_v2x_pb_ConnectingLane_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ConnectingLane_descriptor,
        new java.lang.String[] { "Lane", "Maneuver", });
    internal_static_cn_seisys_v2x_pb_Connection_descriptor =
      getDescriptor().getMessageTypes().get(60);
    internal_static_cn_seisys_v2x_pb_Connection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_Connection_descriptor,
        new java.lang.String[] { "RemoteIntersection", "ConnectingLane", "PhaseId", });
    internal_static_cn_seisys_v2x_pb_LaneAttributesParking_descriptor =
      getDescriptor().getMessageTypes().get(61);
    internal_static_cn_seisys_v2x_pb_LaneAttributesParking_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneAttributesParking_descriptor,
        new java.lang.String[] { "ParkingAndStoppingLanes", });
    internal_static_cn_seisys_v2x_pb_LaneAttributesCrosswalk_descriptor =
      getDescriptor().getMessageTypes().get(62);
    internal_static_cn_seisys_v2x_pb_LaneAttributesCrosswalk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneAttributesCrosswalk_descriptor,
        new java.lang.String[] { "PedestrianCrosswalks", });
    internal_static_cn_seisys_v2x_pb_LaneAttributesBike_descriptor =
      getDescriptor().getMessageTypes().get(63);
    internal_static_cn_seisys_v2x_pb_LaneAttributesBike_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneAttributesBike_descriptor,
        new java.lang.String[] { "BikeLanes", });
    internal_static_cn_seisys_v2x_pb_LaneAttributesSidewalk_descriptor =
      getDescriptor().getMessageTypes().get(64);
    internal_static_cn_seisys_v2x_pb_LaneAttributesSidewalk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneAttributesSidewalk_descriptor,
        new java.lang.String[] { "PedestrianSidewalkPaths", });
    internal_static_cn_seisys_v2x_pb_LaneAttributesBarrier_descriptor =
      getDescriptor().getMessageTypes().get(65);
    internal_static_cn_seisys_v2x_pb_LaneAttributesBarrier_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneAttributesBarrier_descriptor,
        new java.lang.String[] { "MediansChannelization", });
    internal_static_cn_seisys_v2x_pb_LaneAttributesStriping_descriptor =
      getDescriptor().getMessageTypes().get(66);
    internal_static_cn_seisys_v2x_pb_LaneAttributesStriping_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneAttributesStriping_descriptor,
        new java.lang.String[] { "RoadwayMarkings", });
    internal_static_cn_seisys_v2x_pb_LaneAttributesTrackedVehicle_descriptor =
      getDescriptor().getMessageTypes().get(67);
    internal_static_cn_seisys_v2x_pb_LaneAttributesTrackedVehicle_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneAttributesTrackedVehicle_descriptor,
        new java.lang.String[] { "TrainsAndTrolleys", });
    internal_static_cn_seisys_v2x_pb_LaneAttributesVehicle_descriptor =
      getDescriptor().getMessageTypes().get(68);
    internal_static_cn_seisys_v2x_pb_LaneAttributesVehicle_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneAttributesVehicle_descriptor,
        new java.lang.String[] { "MotorVehicleLanes", });
    internal_static_cn_seisys_v2x_pb_LaneTypeAttributes_descriptor =
      getDescriptor().getMessageTypes().get(69);
    internal_static_cn_seisys_v2x_pb_LaneTypeAttributes_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneTypeAttributes_descriptor,
        new java.lang.String[] { "MotorVehicleLanes", "PedestrianCrosswalks", "BikeLanes", "PedestrianSidewalkPaths", "MediansChannelization", "RoadwayMarkings", "TrainsAndTrolleys", "ParkingAndStoppingLanes", "LaneTypeAttributesOneOf", });
    internal_static_cn_seisys_v2x_pb_LaneSharing_descriptor =
      getDescriptor().getMessageTypes().get(70);
    internal_static_cn_seisys_v2x_pb_LaneSharing_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneSharing_descriptor,
        new java.lang.String[] { "ShareWith", });
    internal_static_cn_seisys_v2x_pb_LaneType_descriptor =
      getDescriptor().getMessageTypes().get(71);
    internal_static_cn_seisys_v2x_pb_LaneType_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneType_descriptor,
        new java.lang.String[] { "ChoiceId", "Value", });
    internal_static_cn_seisys_v2x_pb_LaneAttributes_descriptor =
      getDescriptor().getMessageTypes().get(72);
    internal_static_cn_seisys_v2x_pb_LaneAttributes_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneAttributes_descriptor,
        new java.lang.String[] { "ShareWith", "LaneType", });
    internal_static_cn_seisys_v2x_pb_LaneBoundary_descriptor =
      getDescriptor().getMessageTypes().get(73);
    internal_static_cn_seisys_v2x_pb_LaneBoundary_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneBoundary_descriptor,
        new java.lang.String[] { "LaneBoundaryType", "LaneBoundaryPoints", });
    internal_static_cn_seisys_v2x_pb_Lane_descriptor =
      getDescriptor().getMessageTypes().get(74);
    internal_static_cn_seisys_v2x_pb_Lane_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_Lane_descriptor,
        new java.lang.String[] { "LaneId", "LaneWidth", "LaneAttributes", "Maneuvers", "ConnectsTo", "SpeedLimits", "Points", "LeftBoundary", "RightBoundary", });
    internal_static_cn_seisys_v2x_pb_SignalWaitingLane_descriptor =
      getDescriptor().getMessageTypes().get(75);
    internal_static_cn_seisys_v2x_pb_SignalWaitingLane_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_SignalWaitingLane_descriptor,
        new java.lang.String[] { "LaneWidth", "Points", "AllowedPhaseIds", });
    internal_static_cn_seisys_v2x_pb_ConnectingLaneEx_descriptor =
      getDescriptor().getMessageTypes().get(76);
    internal_static_cn_seisys_v2x_pb_ConnectingLaneEx_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ConnectingLaneEx_descriptor,
        new java.lang.String[] { "TargetSection", "TargetLane", "ConnectingLaneWidth", "ConnectingLanePoints", "IsolatedConnectingLane", });
    internal_static_cn_seisys_v2x_pb_ConnectionEx_descriptor =
      getDescriptor().getMessageTypes().get(77);
    internal_static_cn_seisys_v2x_pb_ConnectionEx_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ConnectionEx_descriptor,
        new java.lang.String[] { "RemoteIntersection", "Swl", "ConnectionLane", "PhaseId", "TurnDirection", });
    internal_static_cn_seisys_v2x_pb_STPoint_descriptor =
      getDescriptor().getMessageTypes().get(78);
    internal_static_cn_seisys_v2x_pb_STPoint_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_STPoint_descriptor,
        new java.lang.String[] { "SAxis", "TAxis", });
    internal_static_cn_seisys_v2x_pb_LaneEx_descriptor =
      getDescriptor().getMessageTypes().get(79);
    internal_static_cn_seisys_v2x_pb_LaneEx_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneEx_descriptor,
        new java.lang.String[] { "LaneRefId", "LaneWidth", "LaneAttributes", "Maneuvers", "ConnectsToEx", "SpeedLimits", "StPoints", "LeftBoundary", "RightBoundary", });
    internal_static_cn_seisys_v2x_pb_Movement_descriptor =
      getDescriptor().getMessageTypes().get(80);
    internal_static_cn_seisys_v2x_pb_Movement_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_Movement_descriptor,
        new java.lang.String[] { "RemoteIntersection", "PhaseId", });
    internal_static_cn_seisys_v2x_pb_Section_descriptor =
      getDescriptor().getMessageTypes().get(81);
    internal_static_cn_seisys_v2x_pb_Section_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_Section_descriptor,
        new java.lang.String[] { "SecId", "Lanes", });
    internal_static_cn_seisys_v2x_pb_LinkEx_descriptor =
      getDescriptor().getMessageTypes().get(82);
    internal_static_cn_seisys_v2x_pb_LinkEx_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LinkEx_descriptor,
        new java.lang.String[] { "Name", "UpstreamNodeId", "SpeedLimits", "LinkWidth", "RefLine", "MovementsEx", "Sections", });
    internal_static_cn_seisys_v2x_pb_Link_descriptor =
      getDescriptor().getMessageTypes().get(83);
    internal_static_cn_seisys_v2x_pb_Link_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_Link_descriptor,
        new java.lang.String[] { "Name", "UpstreamNodeId", "SpeedLimits", "LinkWidth", "Points", "Movements", "Lanes", });
    internal_static_cn_seisys_v2x_pb_ProhibitedZone_descriptor =
      getDescriptor().getMessageTypes().get(84);
    internal_static_cn_seisys_v2x_pb_ProhibitedZone_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ProhibitedZone_descriptor,
        new java.lang.String[] { "CentralCirclePrihibitedZone", "NonMotorVehicleProhibitedZones", "GridLineMarkingProhibitedZones", });
    internal_static_cn_seisys_v2x_pb_Node_descriptor =
      getDescriptor().getMessageTypes().get(85);
    internal_static_cn_seisys_v2x_pb_Node_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_Node_descriptor,
        new java.lang.String[] { "Name", "Id", "RefPos", "InLinks", "InLinksEx", "ProhibitedZone", });
    internal_static_cn_seisys_v2x_pb_MAP_descriptor =
      getDescriptor().getMessageTypes().get(86);
    internal_static_cn_seisys_v2x_pb_MAP_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_MAP_descriptor,
        new java.lang.String[] { "Timestamp", "Nodes", "MsgCnt", });
    internal_static_cn_seisys_v2x_pb_MapData_descriptor =
      getDescriptor().getMessageTypes().get(87);
    internal_static_cn_seisys_v2x_pb_MapData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_MapData_descriptor,
        new java.lang.String[] { "MapSlice", "Map", "ETag", "Ack", "SeqNum", });
    internal_static_cn_seisys_v2x_pb_ReqLaneChange_descriptor =
      getDescriptor().getMessageTypes().get(88);
    internal_static_cn_seisys_v2x_pb_ReqLaneChange_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ReqLaneChange_descriptor,
        new java.lang.String[] { "UpStreamNode", "DownStreamNode", "TargetLane", });
    internal_static_cn_seisys_v2x_pb_ReqClearTheWay_descriptor =
      getDescriptor().getMessageTypes().get(89);
    internal_static_cn_seisys_v2x_pb_ReqClearTheWay_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ReqClearTheWay_descriptor,
        new java.lang.String[] { "UpStreamNode", "DownStreamNode", "TargetLane", });
    internal_static_cn_seisys_v2x_pb_ReqSignalPriority_descriptor =
      getDescriptor().getMessageTypes().get(90);
    internal_static_cn_seisys_v2x_pb_ReqSignalPriority_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ReqSignalPriority_descriptor,
        new java.lang.String[] { "IntersectionId", "RequiredMove", "EstimatedArrivalTime", "Distance2Intersection", });
    internal_static_cn_seisys_v2x_pb_ReqSensorSharing_descriptor =
      getDescriptor().getMessageTypes().get(91);
    internal_static_cn_seisys_v2x_pb_ReqSensorSharing_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ReqSensorSharing_descriptor,
        new java.lang.String[] { "DetectorArea", });
    internal_static_cn_seisys_v2x_pb_ParkingRequest_descriptor =
      getDescriptor().getMessageTypes().get(92);
    internal_static_cn_seisys_v2x_pb_ParkingRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ParkingRequest_descriptor,
        new java.lang.String[] { "Req", });
    internal_static_cn_seisys_v2x_pb_ParkingType_descriptor =
      getDescriptor().getMessageTypes().get(93);
    internal_static_cn_seisys_v2x_pb_ParkingType_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ParkingType_descriptor,
        new java.lang.String[] { "ParkingType", });
    internal_static_cn_seisys_v2x_pb_ReqParkingArea_descriptor =
      getDescriptor().getMessageTypes().get(94);
    internal_static_cn_seisys_v2x_pb_ReqParkingArea_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ReqParkingArea_descriptor,
        new java.lang.String[] { "VehicleType", "Req", "ParkingType", "ExpectedParkingSlotId", });
    internal_static_cn_seisys_v2x_pb_ReqInfo_descriptor =
      getDescriptor().getMessageTypes().get(95);
    internal_static_cn_seisys_v2x_pb_ReqInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_ReqInfo_descriptor,
        new java.lang.String[] { "LaneChange", "ClearTheWay", "SignalPriority", "SensorSharing", "Parking", "ReqInfoOneOf", });
    internal_static_cn_seisys_v2x_pb_DriveRequest_descriptor =
      getDescriptor().getMessageTypes().get(96);
    internal_static_cn_seisys_v2x_pb_DriveRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_DriveRequest_descriptor,
        new java.lang.String[] { "ReqId", "Status", "ReqPriority", "TargetVeh", "TargetRsu", "Info", "LifeTime", });
    internal_static_cn_seisys_v2x_pb_DriveBehavior_descriptor =
      getDescriptor().getMessageTypes().get(97);
    internal_static_cn_seisys_v2x_pb_DriveBehavior_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_DriveBehavior_descriptor,
        new java.lang.String[] { "DriveBehavior", });
    internal_static_cn_seisys_v2x_pb_PathPlanningPoint_descriptor =
      getDescriptor().getMessageTypes().get(98);
    internal_static_cn_seisys_v2x_pb_PathPlanningPoint_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_PathPlanningPoint_descriptor,
        new java.lang.String[] { "Pos", "PosConfid", "Speed", "Heading", "SpeedConfid", "HeadingConfid", "Acceleration", "AccelerationConfid", "EstimatedTime", "TimeConfidence", "PosInMap", });
    internal_static_cn_seisys_v2x_pb_PathPlanning_descriptor =
      getDescriptor().getMessageTypes().get(99);
    internal_static_cn_seisys_v2x_pb_PathPlanning_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_PathPlanning_descriptor,
        new java.lang.String[] { "PathPlanning", });
    internal_static_cn_seisys_v2x_pb_IarData_descriptor =
      getDescriptor().getMessageTypes().get(100);
    internal_static_cn_seisys_v2x_pb_IarData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_IarData_descriptor,
        new java.lang.String[] { "CurrentPos", "PathPlanning", "CurrentBehavior", "Reqs", });
    internal_static_cn_seisys_v2x_pb_VirData_descriptor =
      getDescriptor().getMessageTypes().get(101);
    internal_static_cn_seisys_v2x_pb_VirData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_VirData_descriptor,
        new java.lang.String[] { "MsgCnt", "VehicleId", "Timestamp", "Pos", "IntAndReq", });
    internal_static_cn_seisys_v2x_pb_DriveSuggestion_descriptor =
      getDescriptor().getMessageTypes().get(102);
    internal_static_cn_seisys_v2x_pb_DriveSuggestion_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_DriveSuggestion_descriptor,
        new java.lang.String[] { "Suggestion", "TimeOffset", "RelatedLink", "RelatedPath", });
    internal_static_cn_seisys_v2x_pb_CoordinationInfo_descriptor =
      getDescriptor().getMessageTypes().get(103);
    internal_static_cn_seisys_v2x_pb_CoordinationInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_CoordinationInfo_descriptor,
        new java.lang.String[] { "CoordinationInfo", });
    internal_static_cn_seisys_v2x_pb_VehicleCoordination_descriptor =
      getDescriptor().getMessageTypes().get(104);
    internal_static_cn_seisys_v2x_pb_VehicleCoordination_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_VehicleCoordination_descriptor,
        new java.lang.String[] { "VehId", "DriveSuggestion", "PathGuidance", "Info", });
    internal_static_cn_seisys_v2x_pb_LaneCoordination_descriptor =
      getDescriptor().getMessageTypes().get(105);
    internal_static_cn_seisys_v2x_pb_LaneCoordination_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_LaneCoordination_descriptor,
        new java.lang.String[] { "TargetLane", "RelatedPath", "TBegin", "TEnd", "RecommendedSpeed", "RecommendedBehavior", "Info", "Description", });
    internal_static_cn_seisys_v2x_pb_RscData_descriptor =
      getDescriptor().getMessageTypes().get(106);
    internal_static_cn_seisys_v2x_pb_RscData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_RscData_descriptor,
        new java.lang.String[] { "MsgCnt", "RsuId", "Timestamp", "Pos", "Coordinates", "LaneCoordinates", });
    internal_static_cn_seisys_v2x_pb_CamData_descriptor =
      getDescriptor().getMessageTypes().get(107);
    internal_static_cn_seisys_v2x_pb_CamData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_CamData_descriptor,
        new java.lang.String[] { "Type", "Ver", "MsgCnt", "Timestamp", "DeviceId", "MapDeviceId", "RefPos", "SceneType", "PtcList", "ObstacleList", "RteList", "RtsList", "BsmList", "VirList", "RscList", "RoadSignalState", "TrafficFlow", "SignalSchemeList", "DetectedRegion", "ToAlgorithmTime", "ToDatabusTime", "ToCloudTime", "Id", });
    internal_static_cn_seisys_v2x_pb_StatusData_descriptor =
      getDescriptor().getMessageTypes().get(108);
    internal_static_cn_seisys_v2x_pb_StatusData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_StatusData_descriptor,
        new java.lang.String[] { "DeviceId", "MapDeviceId", "DeviceType", "StatusType", "PosDevice", });
    internal_static_cn_seisys_v2x_pb_DenmData_descriptor =
      getDescriptor().getMessageTypes().get(109);
    internal_static_cn_seisys_v2x_pb_DenmData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_DenmData_descriptor,
        new java.lang.String[] { "Type", "Ver", "MsgCnt", "Timestamp", "Address", "RefPos", "SceneType", "StatusList", });
    internal_static_cn_seisys_v2x_pb_RsiReply_descriptor =
      getDescriptor().getMessageTypes().get(110);
    internal_static_cn_seisys_v2x_pb_RsiReply_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_RsiReply_descriptor,
        new java.lang.String[] { "Id", "EventType", "SourceDeviceId", "TargetDeviceId", "CreatTime", "DistributionTime", "CompletionTime", "UpdateTime", "OperationType", "CamDataId", "DataId", "EventSourceId", "DistributionStatusId", "Description", "SourceTopic", "TargetTopic", });
    internal_static_cn_seisys_v2x_pb_RsuRsmReply_descriptor =
      getDescriptor().getMessageTypes().get(111);
    internal_static_cn_seisys_v2x_pb_RsuRsmReply_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_RsuRsmReply_descriptor,
        new java.lang.String[] { "SourceDeviceId", "TargetDeviceId", "CamDataId", "RsmReplyList", "TargetTopic", });
    internal_static_cn_seisys_v2x_pb_RsmReply_descriptor =
      getDescriptor().getMessageTypes().get(112);
    internal_static_cn_seisys_v2x_pb_RsmReply_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_RsmReply_descriptor,
        new java.lang.String[] { "DataId", "DistributionStatusId", "Description", });
    internal_static_cn_seisys_v2x_pb_MonitorStatsData_descriptor =
      getDescriptor().getMessageTypes().get(113);
    internal_static_cn_seisys_v2x_pb_MonitorStatsData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_seisys_v2x_pb_MonitorStatsData_descriptor,
        new java.lang.String[] { "Timestamp", "DeviceId", "CamNums", "ParticipantNums", "RteNums", "TrafficflowNums", "TrafficflowStatNums", "IntersectionStatNums", "PhaseStatNums", "RtsNums", "CameraPathListNums", "CameraPathNums", "RadarPathListNums", "RadarPathNums", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
