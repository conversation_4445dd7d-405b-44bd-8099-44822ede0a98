// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface V2xOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.V2x)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>.cn.seisys.v2x.pb.CamData camData = 1;</code>
   */
  boolean hasCamData();
  /**
   * <code>.cn.seisys.v2x.pb.CamData camData = 1;</code>
   */
  road.data.proto.CamData getCamData();
  /**
   * <code>.cn.seisys.v2x.pb.CamData camData = 1;</code>
   */
  road.data.proto.CamDataOrBuilder getCamDataOrBuilder();

  /**
   * <code>.cn.seisys.v2x.pb.DenmData denmData = 2;</code>
   */
  boolean hasDenmData();
  /**
   * <code>.cn.seisys.v2x.pb.DenmData denmData = 2;</code>
   */
  road.data.proto.DenmData getDenmData();
  /**
   * <code>.cn.seisys.v2x.pb.DenmData denmData = 2;</code>
   */
  road.data.proto.DenmDataOrBuilder getDenmDataOrBuilder();

  /**
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData participantDataList = 3;</code>
   */
  java.util.List<road.data.proto.ParticipantData> 
      getParticipantDataListList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData participantDataList = 3;</code>
   */
  road.data.proto.ParticipantData getParticipantDataList(int index);
  /**
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData participantDataList = 3;</code>
   */
  int getParticipantDataListCount();
  /**
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData participantDataList = 3;</code>
   */
  java.util.List<? extends road.data.proto.ParticipantDataOrBuilder> 
      getParticipantDataListOrBuilderList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.ParticipantData participantDataList = 3;</code>
   */
  road.data.proto.ParticipantDataOrBuilder getParticipantDataListOrBuilder(
      int index);

  /**
   * <code>.cn.seisys.v2x.pb.MapData mapData = 4;</code>
   */
  boolean hasMapData();
  /**
   * <code>.cn.seisys.v2x.pb.MapData mapData = 4;</code>
   */
  road.data.proto.MapData getMapData();
  /**
   * <code>.cn.seisys.v2x.pb.MapData mapData = 4;</code>
   */
  road.data.proto.MapDataOrBuilder getMapDataOrBuilder();

  /**
   * <code>repeated .cn.seisys.v2x.pb.RteData rteDataList = 5;</code>
   */
  java.util.List<road.data.proto.RteData> 
      getRteDataListList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.RteData rteDataList = 5;</code>
   */
  road.data.proto.RteData getRteDataList(int index);
  /**
   * <code>repeated .cn.seisys.v2x.pb.RteData rteDataList = 5;</code>
   */
  int getRteDataListCount();
  /**
   * <code>repeated .cn.seisys.v2x.pb.RteData rteDataList = 5;</code>
   */
  java.util.List<? extends road.data.proto.RteDataOrBuilder> 
      getRteDataListOrBuilderList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.RteData rteDataList = 5;</code>
   */
  road.data.proto.RteDataOrBuilder getRteDataListOrBuilder(
      int index);

  /**
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsDataList = 6;</code>
   */
  java.util.List<road.data.proto.RtsData> 
      getRtsDataListList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsDataList = 6;</code>
   */
  road.data.proto.RtsData getRtsDataList(int index);
  /**
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsDataList = 6;</code>
   */
  int getRtsDataListCount();
  /**
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsDataList = 6;</code>
   */
  java.util.List<? extends road.data.proto.RtsDataOrBuilder> 
      getRtsDataListOrBuilderList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.RtsData rtsDataList = 6;</code>
   */
  road.data.proto.RtsDataOrBuilder getRtsDataListOrBuilder(
      int index);

  /**
   * <code>.cn.seisys.v2x.pb.SpatData spatData = 7;</code>
   */
  boolean hasSpatData();
  /**
   * <code>.cn.seisys.v2x.pb.SpatData spatData = 7;</code>
   */
  road.data.proto.SpatData getSpatData();
  /**
   * <code>.cn.seisys.v2x.pb.SpatData spatData = 7;</code>
   */
  road.data.proto.SpatDataOrBuilder getSpatDataOrBuilder();

  /**
   * <code>repeated .cn.seisys.v2x.pb.RscData rscDataList = 8;</code>
   */
  java.util.List<road.data.proto.RscData> 
      getRscDataListList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.RscData rscDataList = 8;</code>
   */
  road.data.proto.RscData getRscDataList(int index);
  /**
   * <code>repeated .cn.seisys.v2x.pb.RscData rscDataList = 8;</code>
   */
  int getRscDataListCount();
  /**
   * <code>repeated .cn.seisys.v2x.pb.RscData rscDataList = 8;</code>
   */
  java.util.List<? extends road.data.proto.RscDataOrBuilder> 
      getRscDataListOrBuilderList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.RscData rscDataList = 8;</code>
   */
  road.data.proto.RscDataOrBuilder getRscDataListOrBuilder(
      int index);

  /**
   * <code>repeated .cn.seisys.v2x.pb.VirData virDataList = 9;</code>
   */
  java.util.List<road.data.proto.VirData> 
      getVirDataListList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.VirData virDataList = 9;</code>
   */
  road.data.proto.VirData getVirDataList(int index);
  /**
   * <code>repeated .cn.seisys.v2x.pb.VirData virDataList = 9;</code>
   */
  int getVirDataListCount();
  /**
   * <code>repeated .cn.seisys.v2x.pb.VirData virDataList = 9;</code>
   */
  java.util.List<? extends road.data.proto.VirDataOrBuilder> 
      getVirDataListOrBuilderList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.VirData virDataList = 9;</code>
   */
  road.data.proto.VirDataOrBuilder getVirDataListOrBuilder(
      int index);

  /**
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleDataList = 10;</code>
   */
  java.util.List<road.data.proto.ObstacleData> 
      getObstacleDataListList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleDataList = 10;</code>
   */
  road.data.proto.ObstacleData getObstacleDataList(int index);
  /**
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleDataList = 10;</code>
   */
  int getObstacleDataListCount();
  /**
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleDataList = 10;</code>
   */
  java.util.List<? extends road.data.proto.ObstacleDataOrBuilder> 
      getObstacleDataListOrBuilderList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.ObstacleData obstacleDataList = 10;</code>
   */
  road.data.proto.ObstacleDataOrBuilder getObstacleDataListOrBuilder(
      int index);

  /**
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlowList = 11;</code>
   */
  java.util.List<road.data.proto.TrafficFlow> 
      getTrafficFlowListList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlowList = 11;</code>
   */
  road.data.proto.TrafficFlow getTrafficFlowList(int index);
  /**
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlowList = 11;</code>
   */
  int getTrafficFlowListCount();
  /**
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlowList = 11;</code>
   */
  java.util.List<? extends road.data.proto.TrafficFlowOrBuilder> 
      getTrafficFlowListOrBuilderList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.TrafficFlow trafficFlowList = 11;</code>
   */
  road.data.proto.TrafficFlowOrBuilder getTrafficFlowListOrBuilder(
      int index);

  /**
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 12;</code>
   */
  java.util.List<road.data.proto.SignalScheme> 
      getSignalSchemeListList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 12;</code>
   */
  road.data.proto.SignalScheme getSignalSchemeList(int index);
  /**
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 12;</code>
   */
  int getSignalSchemeListCount();
  /**
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 12;</code>
   */
  java.util.List<? extends road.data.proto.SignalSchemeOrBuilder> 
      getSignalSchemeListOrBuilderList();
  /**
   * <code>repeated .cn.seisys.v2x.pb.SignalScheme signalSchemeList = 12;</code>
   */
  road.data.proto.SignalSchemeOrBuilder getSignalSchemeListOrBuilder(
      int index);

  /**
   * <pre>
   * 你可以根据实际需要继续添加其他 message
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MonitorStatsData monitorStatsDataList = 13;</code>
   */
  java.util.List<road.data.proto.MonitorStatsData> 
      getMonitorStatsDataListList();
  /**
   * <pre>
   * 你可以根据实际需要继续添加其他 message
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MonitorStatsData monitorStatsDataList = 13;</code>
   */
  road.data.proto.MonitorStatsData getMonitorStatsDataList(int index);
  /**
   * <pre>
   * 你可以根据实际需要继续添加其他 message
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MonitorStatsData monitorStatsDataList = 13;</code>
   */
  int getMonitorStatsDataListCount();
  /**
   * <pre>
   * 你可以根据实际需要继续添加其他 message
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MonitorStatsData monitorStatsDataList = 13;</code>
   */
  java.util.List<? extends road.data.proto.MonitorStatsDataOrBuilder> 
      getMonitorStatsDataListOrBuilderList();
  /**
   * <pre>
   * 你可以根据实际需要继续添加其他 message
   * </pre>
   *
   * <code>repeated .cn.seisys.v2x.pb.MonitorStatsData monitorStatsDataList = 13;</code>
   */
  road.data.proto.MonitorStatsDataOrBuilder getMonitorStatsDataListOrBuilder(
      int index);
}
