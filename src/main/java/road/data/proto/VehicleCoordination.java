// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *定义RSU对某单一车辆的协调规划信息   
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.VehicleCoordination}
 */
public  final class VehicleCoordination extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.VehicleCoordination)
    VehicleCoordinationOrBuilder {
private static final long serialVersionUID = 0L;
  // Use VehicleCoordination.newBuilder() to construct.
  private VehicleCoordination(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private VehicleCoordination() {
    vehId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new VehicleCoordination();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private VehicleCoordination(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            vehId_ = s;
            break;
          }
          case 18: {
            road.data.proto.DriveSuggestion.Builder subBuilder = null;
            if (driveSuggestion_ != null) {
              subBuilder = driveSuggestion_.toBuilder();
            }
            driveSuggestion_ = input.readMessage(road.data.proto.DriveSuggestion.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(driveSuggestion_);
              driveSuggestion_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            road.data.proto.PathPlanning.Builder subBuilder = null;
            if (pathGuidance_ != null) {
              subBuilder = pathGuidance_.toBuilder();
            }
            pathGuidance_ = input.readMessage(road.data.proto.PathPlanning.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(pathGuidance_);
              pathGuidance_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            road.data.proto.CoordinationInfo.Builder subBuilder = null;
            if (info_ != null) {
              subBuilder = info_.toBuilder();
            }
            info_ = input.readMessage(road.data.proto.CoordinationInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(info_);
              info_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_VehicleCoordination_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_VehicleCoordination_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.VehicleCoordination.class, road.data.proto.VehicleCoordination.Builder.class);
  }

  public static final int VEHID_FIELD_NUMBER = 1;
  private volatile java.lang.Object vehId_;
  /**
   * <pre>
   *目标车辆的临时 ID
   * </pre>
   *
   * <code>string vehId = 1;</code>
   */
  public java.lang.String getVehId() {
    java.lang.Object ref = vehId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      vehId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *目标车辆的临时 ID
   * </pre>
   *
   * <code>string vehId = 1;</code>
   */
  public com.google.protobuf.ByteString
      getVehIdBytes() {
    java.lang.Object ref = vehId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      vehId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DRIVESUGGESTION_FIELD_NUMBER = 2;
  private road.data.proto.DriveSuggestion driveSuggestion_;
  /**
   * <pre>
   *可选，驾驶建议
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
   */
  public boolean hasDriveSuggestion() {
    return driveSuggestion_ != null;
  }
  /**
   * <pre>
   *可选，驾驶建议
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
   */
  public road.data.proto.DriveSuggestion getDriveSuggestion() {
    return driveSuggestion_ == null ? road.data.proto.DriveSuggestion.getDefaultInstance() : driveSuggestion_;
  }
  /**
   * <pre>
   *可选，驾驶建议
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
   */
  public road.data.proto.DriveSuggestionOrBuilder getDriveSuggestionOrBuilder() {
    return getDriveSuggestion();
  }

  public static final int PATHGUIDANCE_FIELD_NUMBER = 3;
  private road.data.proto.PathPlanning pathGuidance_;
  /**
   * <pre>
   *可选，使用路径引导进行协调
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
   */
  public boolean hasPathGuidance() {
    return pathGuidance_ != null;
  }
  /**
   * <pre>
   *可选，使用路径引导进行协调
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
   */
  public road.data.proto.PathPlanning getPathGuidance() {
    return pathGuidance_ == null ? road.data.proto.PathPlanning.getDefaultInstance() : pathGuidance_;
  }
  /**
   * <pre>
   *可选，使用路径引导进行协调
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
   */
  public road.data.proto.PathPlanningOrBuilder getPathGuidanceOrBuilder() {
    return getPathGuidance();
  }

  public static final int INFO_FIELD_NUMBER = 4;
  private road.data.proto.CoordinationInfo info_;
  /**
   * <pre>
   *可选，与当前协调相关的详细信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
   */
  public boolean hasInfo() {
    return info_ != null;
  }
  /**
   * <pre>
   *可选，与当前协调相关的详细信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
   */
  public road.data.proto.CoordinationInfo getInfo() {
    return info_ == null ? road.data.proto.CoordinationInfo.getDefaultInstance() : info_;
  }
  /**
   * <pre>
   *可选，与当前协调相关的详细信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
   */
  public road.data.proto.CoordinationInfoOrBuilder getInfoOrBuilder() {
    return getInfo();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getVehIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, vehId_);
    }
    if (driveSuggestion_ != null) {
      output.writeMessage(2, getDriveSuggestion());
    }
    if (pathGuidance_ != null) {
      output.writeMessage(3, getPathGuidance());
    }
    if (info_ != null) {
      output.writeMessage(4, getInfo());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getVehIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, vehId_);
    }
    if (driveSuggestion_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getDriveSuggestion());
    }
    if (pathGuidance_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getPathGuidance());
    }
    if (info_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getInfo());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.VehicleCoordination)) {
      return super.equals(obj);
    }
    road.data.proto.VehicleCoordination other = (road.data.proto.VehicleCoordination) obj;

    if (!getVehId()
        .equals(other.getVehId())) return false;
    if (hasDriveSuggestion() != other.hasDriveSuggestion()) return false;
    if (hasDriveSuggestion()) {
      if (!getDriveSuggestion()
          .equals(other.getDriveSuggestion())) return false;
    }
    if (hasPathGuidance() != other.hasPathGuidance()) return false;
    if (hasPathGuidance()) {
      if (!getPathGuidance()
          .equals(other.getPathGuidance())) return false;
    }
    if (hasInfo() != other.hasInfo()) return false;
    if (hasInfo()) {
      if (!getInfo()
          .equals(other.getInfo())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + VEHID_FIELD_NUMBER;
    hash = (53 * hash) + getVehId().hashCode();
    if (hasDriveSuggestion()) {
      hash = (37 * hash) + DRIVESUGGESTION_FIELD_NUMBER;
      hash = (53 * hash) + getDriveSuggestion().hashCode();
    }
    if (hasPathGuidance()) {
      hash = (37 * hash) + PATHGUIDANCE_FIELD_NUMBER;
      hash = (53 * hash) + getPathGuidance().hashCode();
    }
    if (hasInfo()) {
      hash = (37 * hash) + INFO_FIELD_NUMBER;
      hash = (53 * hash) + getInfo().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.VehicleCoordination parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.VehicleCoordination parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.VehicleCoordination parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.VehicleCoordination parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.VehicleCoordination parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.VehicleCoordination parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.VehicleCoordination parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.VehicleCoordination parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.VehicleCoordination parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.VehicleCoordination parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.VehicleCoordination parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.VehicleCoordination parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.VehicleCoordination prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *定义RSU对某单一车辆的协调规划信息   
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.VehicleCoordination}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.VehicleCoordination)
      road.data.proto.VehicleCoordinationOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_VehicleCoordination_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_VehicleCoordination_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.VehicleCoordination.class, road.data.proto.VehicleCoordination.Builder.class);
    }

    // Construct using road.data.proto.VehicleCoordination.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      vehId_ = "";

      if (driveSuggestionBuilder_ == null) {
        driveSuggestion_ = null;
      } else {
        driveSuggestion_ = null;
        driveSuggestionBuilder_ = null;
      }
      if (pathGuidanceBuilder_ == null) {
        pathGuidance_ = null;
      } else {
        pathGuidance_ = null;
        pathGuidanceBuilder_ = null;
      }
      if (infoBuilder_ == null) {
        info_ = null;
      } else {
        info_ = null;
        infoBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_VehicleCoordination_descriptor;
    }

    @java.lang.Override
    public road.data.proto.VehicleCoordination getDefaultInstanceForType() {
      return road.data.proto.VehicleCoordination.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.VehicleCoordination build() {
      road.data.proto.VehicleCoordination result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.VehicleCoordination buildPartial() {
      road.data.proto.VehicleCoordination result = new road.data.proto.VehicleCoordination(this);
      result.vehId_ = vehId_;
      if (driveSuggestionBuilder_ == null) {
        result.driveSuggestion_ = driveSuggestion_;
      } else {
        result.driveSuggestion_ = driveSuggestionBuilder_.build();
      }
      if (pathGuidanceBuilder_ == null) {
        result.pathGuidance_ = pathGuidance_;
      } else {
        result.pathGuidance_ = pathGuidanceBuilder_.build();
      }
      if (infoBuilder_ == null) {
        result.info_ = info_;
      } else {
        result.info_ = infoBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.VehicleCoordination) {
        return mergeFrom((road.data.proto.VehicleCoordination)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.VehicleCoordination other) {
      if (other == road.data.proto.VehicleCoordination.getDefaultInstance()) return this;
      if (!other.getVehId().isEmpty()) {
        vehId_ = other.vehId_;
        onChanged();
      }
      if (other.hasDriveSuggestion()) {
        mergeDriveSuggestion(other.getDriveSuggestion());
      }
      if (other.hasPathGuidance()) {
        mergePathGuidance(other.getPathGuidance());
      }
      if (other.hasInfo()) {
        mergeInfo(other.getInfo());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.VehicleCoordination parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.VehicleCoordination) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object vehId_ = "";
    /**
     * <pre>
     *目标车辆的临时 ID
     * </pre>
     *
     * <code>string vehId = 1;</code>
     */
    public java.lang.String getVehId() {
      java.lang.Object ref = vehId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        vehId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *目标车辆的临时 ID
     * </pre>
     *
     * <code>string vehId = 1;</code>
     */
    public com.google.protobuf.ByteString
        getVehIdBytes() {
      java.lang.Object ref = vehId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        vehId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *目标车辆的临时 ID
     * </pre>
     *
     * <code>string vehId = 1;</code>
     */
    public Builder setVehId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      vehId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *目标车辆的临时 ID
     * </pre>
     *
     * <code>string vehId = 1;</code>
     */
    public Builder clearVehId() {
      
      vehId_ = getDefaultInstance().getVehId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *目标车辆的临时 ID
     * </pre>
     *
     * <code>string vehId = 1;</code>
     */
    public Builder setVehIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      vehId_ = value;
      onChanged();
      return this;
    }

    private road.data.proto.DriveSuggestion driveSuggestion_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.DriveSuggestion, road.data.proto.DriveSuggestion.Builder, road.data.proto.DriveSuggestionOrBuilder> driveSuggestionBuilder_;
    /**
     * <pre>
     *可选，驾驶建议
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
     */
    public boolean hasDriveSuggestion() {
      return driveSuggestionBuilder_ != null || driveSuggestion_ != null;
    }
    /**
     * <pre>
     *可选，驾驶建议
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
     */
    public road.data.proto.DriveSuggestion getDriveSuggestion() {
      if (driveSuggestionBuilder_ == null) {
        return driveSuggestion_ == null ? road.data.proto.DriveSuggestion.getDefaultInstance() : driveSuggestion_;
      } else {
        return driveSuggestionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，驾驶建议
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
     */
    public Builder setDriveSuggestion(road.data.proto.DriveSuggestion value) {
      if (driveSuggestionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        driveSuggestion_ = value;
        onChanged();
      } else {
        driveSuggestionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，驾驶建议
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
     */
    public Builder setDriveSuggestion(
        road.data.proto.DriveSuggestion.Builder builderForValue) {
      if (driveSuggestionBuilder_ == null) {
        driveSuggestion_ = builderForValue.build();
        onChanged();
      } else {
        driveSuggestionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，驾驶建议
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
     */
    public Builder mergeDriveSuggestion(road.data.proto.DriveSuggestion value) {
      if (driveSuggestionBuilder_ == null) {
        if (driveSuggestion_ != null) {
          driveSuggestion_ =
            road.data.proto.DriveSuggestion.newBuilder(driveSuggestion_).mergeFrom(value).buildPartial();
        } else {
          driveSuggestion_ = value;
        }
        onChanged();
      } else {
        driveSuggestionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，驾驶建议
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
     */
    public Builder clearDriveSuggestion() {
      if (driveSuggestionBuilder_ == null) {
        driveSuggestion_ = null;
        onChanged();
      } else {
        driveSuggestion_ = null;
        driveSuggestionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，驾驶建议
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
     */
    public road.data.proto.DriveSuggestion.Builder getDriveSuggestionBuilder() {
      
      onChanged();
      return getDriveSuggestionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，驾驶建议
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
     */
    public road.data.proto.DriveSuggestionOrBuilder getDriveSuggestionOrBuilder() {
      if (driveSuggestionBuilder_ != null) {
        return driveSuggestionBuilder_.getMessageOrBuilder();
      } else {
        return driveSuggestion_ == null ?
            road.data.proto.DriveSuggestion.getDefaultInstance() : driveSuggestion_;
      }
    }
    /**
     * <pre>
     *可选，驾驶建议
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.DriveSuggestion, road.data.proto.DriveSuggestion.Builder, road.data.proto.DriveSuggestionOrBuilder> 
        getDriveSuggestionFieldBuilder() {
      if (driveSuggestionBuilder_ == null) {
        driveSuggestionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.DriveSuggestion, road.data.proto.DriveSuggestion.Builder, road.data.proto.DriveSuggestionOrBuilder>(
                getDriveSuggestion(),
                getParentForChildren(),
                isClean());
        driveSuggestion_ = null;
      }
      return driveSuggestionBuilder_;
    }

    private road.data.proto.PathPlanning pathGuidance_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PathPlanning, road.data.proto.PathPlanning.Builder, road.data.proto.PathPlanningOrBuilder> pathGuidanceBuilder_;
    /**
     * <pre>
     *可选，使用路径引导进行协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
     */
    public boolean hasPathGuidance() {
      return pathGuidanceBuilder_ != null || pathGuidance_ != null;
    }
    /**
     * <pre>
     *可选，使用路径引导进行协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
     */
    public road.data.proto.PathPlanning getPathGuidance() {
      if (pathGuidanceBuilder_ == null) {
        return pathGuidance_ == null ? road.data.proto.PathPlanning.getDefaultInstance() : pathGuidance_;
      } else {
        return pathGuidanceBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，使用路径引导进行协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
     */
    public Builder setPathGuidance(road.data.proto.PathPlanning value) {
      if (pathGuidanceBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        pathGuidance_ = value;
        onChanged();
      } else {
        pathGuidanceBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，使用路径引导进行协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
     */
    public Builder setPathGuidance(
        road.data.proto.PathPlanning.Builder builderForValue) {
      if (pathGuidanceBuilder_ == null) {
        pathGuidance_ = builderForValue.build();
        onChanged();
      } else {
        pathGuidanceBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，使用路径引导进行协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
     */
    public Builder mergePathGuidance(road.data.proto.PathPlanning value) {
      if (pathGuidanceBuilder_ == null) {
        if (pathGuidance_ != null) {
          pathGuidance_ =
            road.data.proto.PathPlanning.newBuilder(pathGuidance_).mergeFrom(value).buildPartial();
        } else {
          pathGuidance_ = value;
        }
        onChanged();
      } else {
        pathGuidanceBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，使用路径引导进行协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
     */
    public Builder clearPathGuidance() {
      if (pathGuidanceBuilder_ == null) {
        pathGuidance_ = null;
        onChanged();
      } else {
        pathGuidance_ = null;
        pathGuidanceBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，使用路径引导进行协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
     */
    public road.data.proto.PathPlanning.Builder getPathGuidanceBuilder() {
      
      onChanged();
      return getPathGuidanceFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，使用路径引导进行协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
     */
    public road.data.proto.PathPlanningOrBuilder getPathGuidanceOrBuilder() {
      if (pathGuidanceBuilder_ != null) {
        return pathGuidanceBuilder_.getMessageOrBuilder();
      } else {
        return pathGuidance_ == null ?
            road.data.proto.PathPlanning.getDefaultInstance() : pathGuidance_;
      }
    }
    /**
     * <pre>
     *可选，使用路径引导进行协调
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.PathPlanning, road.data.proto.PathPlanning.Builder, road.data.proto.PathPlanningOrBuilder> 
        getPathGuidanceFieldBuilder() {
      if (pathGuidanceBuilder_ == null) {
        pathGuidanceBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.PathPlanning, road.data.proto.PathPlanning.Builder, road.data.proto.PathPlanningOrBuilder>(
                getPathGuidance(),
                getParentForChildren(),
                isClean());
        pathGuidance_ = null;
      }
      return pathGuidanceBuilder_;
    }

    private road.data.proto.CoordinationInfo info_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.CoordinationInfo, road.data.proto.CoordinationInfo.Builder, road.data.proto.CoordinationInfoOrBuilder> infoBuilder_;
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
     */
    public boolean hasInfo() {
      return infoBuilder_ != null || info_ != null;
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
     */
    public road.data.proto.CoordinationInfo getInfo() {
      if (infoBuilder_ == null) {
        return info_ == null ? road.data.proto.CoordinationInfo.getDefaultInstance() : info_;
      } else {
        return infoBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
     */
    public Builder setInfo(road.data.proto.CoordinationInfo value) {
      if (infoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        info_ = value;
        onChanged();
      } else {
        infoBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
     */
    public Builder setInfo(
        road.data.proto.CoordinationInfo.Builder builderForValue) {
      if (infoBuilder_ == null) {
        info_ = builderForValue.build();
        onChanged();
      } else {
        infoBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
     */
    public Builder mergeInfo(road.data.proto.CoordinationInfo value) {
      if (infoBuilder_ == null) {
        if (info_ != null) {
          info_ =
            road.data.proto.CoordinationInfo.newBuilder(info_).mergeFrom(value).buildPartial();
        } else {
          info_ = value;
        }
        onChanged();
      } else {
        infoBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
     */
    public Builder clearInfo() {
      if (infoBuilder_ == null) {
        info_ = null;
        onChanged();
      } else {
        info_ = null;
        infoBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
     */
    public road.data.proto.CoordinationInfo.Builder getInfoBuilder() {
      
      onChanged();
      return getInfoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
     */
    public road.data.proto.CoordinationInfoOrBuilder getInfoOrBuilder() {
      if (infoBuilder_ != null) {
        return infoBuilder_.getMessageOrBuilder();
      } else {
        return info_ == null ?
            road.data.proto.CoordinationInfo.getDefaultInstance() : info_;
      }
    }
    /**
     * <pre>
     *可选，与当前协调相关的详细信息
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.CoordinationInfo, road.data.proto.CoordinationInfo.Builder, road.data.proto.CoordinationInfoOrBuilder> 
        getInfoFieldBuilder() {
      if (infoBuilder_ == null) {
        infoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.CoordinationInfo, road.data.proto.CoordinationInfo.Builder, road.data.proto.CoordinationInfoOrBuilder>(
                getInfo(),
                getParentForChildren(),
                isClean());
        info_ = null;
      }
      return infoBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.VehicleCoordination)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.VehicleCoordination)
  private static final road.data.proto.VehicleCoordination DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.VehicleCoordination();
  }

  public static road.data.proto.VehicleCoordination getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<VehicleCoordination>
      PARSER = new com.google.protobuf.AbstractParser<VehicleCoordination>() {
    @java.lang.Override
    public VehicleCoordination parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new VehicleCoordination(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<VehicleCoordination> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<VehicleCoordination> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.VehicleCoordination getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

