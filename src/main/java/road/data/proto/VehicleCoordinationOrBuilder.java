// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface VehicleCoordinationOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.VehicleCoordination)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *目标车辆的临时 ID
   * </pre>
   *
   * <code>string vehId = 1;</code>
   */
  java.lang.String getVehId();
  /**
   * <pre>
   *目标车辆的临时 ID
   * </pre>
   *
   * <code>string vehId = 1;</code>
   */
  com.google.protobuf.ByteString
      getVehIdBytes();

  /**
   * <pre>
   *可选，驾驶建议
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
   */
  boolean hasDriveSuggestion();
  /**
   * <pre>
   *可选，驾驶建议
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
   */
  road.data.proto.DriveSuggestion getDriveSuggestion();
  /**
   * <pre>
   *可选，驾驶建议
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.DriveSuggestion driveSuggestion = 2;</code>
   */
  road.data.proto.DriveSuggestionOrBuilder getDriveSuggestionOrBuilder();

  /**
   * <pre>
   *可选，使用路径引导进行协调
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
   */
  boolean hasPathGuidance();
  /**
   * <pre>
   *可选，使用路径引导进行协调
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
   */
  road.data.proto.PathPlanning getPathGuidance();
  /**
   * <pre>
   *可选，使用路径引导进行协调
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.PathPlanning pathGuidance = 3;</code>
   */
  road.data.proto.PathPlanningOrBuilder getPathGuidanceOrBuilder();

  /**
   * <pre>
   *可选，与当前协调相关的详细信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
   */
  boolean hasInfo();
  /**
   * <pre>
   *可选，与当前协调相关的详细信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
   */
  road.data.proto.CoordinationInfo getInfo();
  /**
   * <pre>
   *可选，与当前协调相关的详细信息
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.CoordinationInfo info = 4;</code>
   */
  road.data.proto.CoordinationInfoOrBuilder getInfoOrBuilder();
}
