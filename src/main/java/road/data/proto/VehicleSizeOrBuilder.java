// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface VehicleSizeOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.VehicleSize)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 车辆车身宽度。分辨率为1cm。数值0表示无效数据。10m以内
   * </pre>
   *
   * <code>int32 width = 1;</code>
   */
  int getWidth();

  /**
   * <pre>
   * 车辆车身长度。分辨率为1cm。数值0表示无效数据。40m以内
   * </pre>
   *
   * <code>int32 length = 2;</code>
   */
  int getLength();

  /**
   * <pre>
   * 车辆车身高度。分辨率为5cm。数值0表示无效数据
   * </pre>
   *
   * <code>int32 height = 3;</code>
   */
  int getHeight();
}
