// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车辆类型信息     
 * </pre>
 *
 * Protobuf enum {@code cn.seisys.v2x.pb.VehicleType}
 */
public enum VehicleType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   * 未配备、未知或不可用
   * </pre>
   *
   * <code>UNKNOWN_VEHICLE_CLASS = 0;</code>
   */
  UNKNOWN_VEHICLE_CLASS(0),
  /**
   * <pre>
   *乘用车辆默认类型
   * </pre>
   *
   * <code>PASSENGER_VEHICLE_TYPE_UNKNOWN = 10;</code>
   */
  PASSENGER_VEHICLE_TYPE_UNKNOWN(10),
  /**
   * <pre>
   *小型卡 车、皮卡、面包车
   * </pre>
   *
   * <code>MOTOR_LIGHTTRUNK = 20;</code>
   */
  MOTOR_LIGHTTRUNK(20),
  /**
   * <pre>
   * 大卡车、货车默认类型
   * </pre>
   *
   * <code>TRUCK_VEHICLE_TYPE_UNKNOWN = 25;</code>
   */
  TRUCK_VEHICLE_TYPE_UNKNOWN(25),
  /**
   * <pre>
   * 摩托车默认类型
   * </pre>
   *
   * <code>MOTORCYCLE_TYPE_UNKNOWN = 40;</code>
   */
  MOTORCYCLE_TYPE_UNKNOWN(40),
  /**
   * <pre>
   *公交、短驳、换乘的默认类型
   * </pre>
   *
   * <code>TRANSIT_TYPE_UNKNOWN = 50;</code>
   */
  TRANSIT_TYPE_UNKNOWN(50),
  /**
   * <pre>
   *消防、救护、警车等特种车辆
   * </pre>
   *
   * <code>EMERGENCY_TYPE_UNKNOWN = 60;</code>
   */
  EMERGENCY_TYPE_UNKNOWN(60),
  /**
   * <pre>
   *------------细化分类如下所示--------------------------------------
   *UNKNOWN_VEHICLE_CLASS= 0; // 未配备、未知或不可用
   * </pre>
   *
   * <code>SPECIAL_VEHICLE_CLASS = 1;</code>
   */
  SPECIAL_VEHICLE_CLASS(1),
  /**
   * <pre>
   *------------基本乘用车类型----------------------------------------
   *PASSENGER_VEHICLE_TYPE_UNKNOWN= 10; //乘用车辆默认类型
   * </pre>
   *
   * <code>PASSENGER_VEHICLE_TYPE_OTHER = 11;</code>
   */
  PASSENGER_VEHICLE_TYPE_OTHER(11),
  /**
   * <pre>
   *------------轻型卡车、皮卡、厢式货车、面板---------------------------
   *LIGHT_TRUCK_VEHICLE_TYPE_UNKNOWN= 20;// 轻型卡车车辆默认类型
   * </pre>
   *
   * <code>LIGHT_TRUCK_VEHICLE_TYPE_OTHER = 21;</code>
   */
  LIGHT_TRUCK_VEHICLE_TYPE_OTHER(21),
  /**
   * <pre>
   *------------卡车，各种车轴类型，包括 HPMS 项目-----------------------
   *TRUCK_VEHICLE_TYPE_UNKNOWN= 25;// 大卡车、货车默认类型
   * </pre>
   *
   * <code>TRUCK_VEHICLE_TYPE_OTHER = 26;</code>
   */
  TRUCK_VEHICLE_TYPE_OTHER(26),
  /**
   * <pre>
   * 两个轴，六个轮胎单单元
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT2 = 27;</code>
   */
  TRUCK_AXLE_CNT2(27),
  /**
   * <pre>
   * 三轴，单单元
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT3 = 28;</code>
   */
  TRUCK_AXLE_CNT3(28),
  /**
   * <pre>
   * 四个或更多轴，单个单元
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT4 = 29;</code>
   */
  TRUCK_AXLE_CNT4(29),
  /**
   * <pre>
   * 四轴或更少轴，单拖车
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT4_TRAILER = 30;</code>
   */
  TRUCK_AXLE_CNT4_TRAILER(30),
  /**
   * <pre>
   * 五个或更少轴，单拖车
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT5_TRAILER = 31;</code>
   */
  TRUCK_AXLE_CNT5_TRAILER(31),
  /**
   * <pre>
   * 六轴或更多轴，单拖车
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT6_TRAILER = 32;</code>
   */
  TRUCK_AXLE_CNT6_TRAILER(32),
  /**
   * <pre>
   * 五个或更少的轴，多拖车
   * </pre>
   *
   * <code>TRUCK_AXLECNT5MULTITRAILER = 33;</code>
   */
  TRUCK_AXLECNT5MULTITRAILER(33),
  /**
   * <pre>
   * 六轴，多拖车
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT6_MULTI_TRAILER = 34;</code>
   */
  TRUCK_AXLE_CNT6_MULTI_TRAILER(34),
  /**
   * <pre>
   * 七个或更多轴，多拖车
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT7_MULTI_TRAILER = 35;</code>
   */
  TRUCK_AXLE_CNT7_MULTI_TRAILER(35),
  /**
   * <pre>
   *------------摩托车类型--------------------------------------------
   *MOTORCYCLE_TYPE_UNKNOWN = 40;// 摩托车默认类型
   * </pre>
   *
   * <code>MOTORCYCLE_TYPE_OTHER = 41;</code>
   */
  MOTORCYCLE_TYPE_OTHER(41),
  /**
   * <pre>
   * 摩托车巡洋舰标准
   * </pre>
   *
   * <code>MOTORCYCLE_CRUISER_STANDARD = 42;</code>
   */
  MOTORCYCLE_CRUISER_STANDARD(42),
  /**
   * <pre>
   * 摩托车
   * </pre>
   *
   * <code>SPORT_UNCLAD_MOTORCYCLE_SPORT_UNCLAD = 43;</code>
   */
  SPORT_UNCLAD_MOTORCYCLE_SPORT_UNCLAD(43),
  /**
   * <pre>
   * 摩托车运动旅行
   * </pre>
   *
   * <code>MOTORCYCLE_SPORT_TOURING = 44;</code>
   */
  MOTORCYCLE_SPORT_TOURING(44),
  /**
   * <pre>
   * 摩托车超级运动
   * </pre>
   *
   * <code>MOTORCYCLE_SUPER_SPORT = 45;</code>
   */
  MOTORCYCLE_SUPER_SPORT(45),
  /**
   * <pre>
   * 摩托车旅行
   * </pre>
   *
   * <code>MOTORCYCLE_TOURING = 46;</code>
   */
  MOTORCYCLE_TOURING(46),
  /**
   * <pre>
   * 摩托车三轮车
   * </pre>
   *
   * <code>MOTORCYCLE_TRIKE = 47;</code>
   */
  MOTORCYCLE_TRIKE(47),
  /**
   * <pre>
   * 摩托车带乘客
   * </pre>
   *
   * <code>MOTORCYCLE_WPASSENGERS = 48;</code>
   */
  MOTORCYCLE_WPASSENGERS(48),
  /**
   * <pre>
   *------------运输类型----------------------------------------------
   *TRANSIT_TYPE_UNKNOWN= 50;//公交、短驳、换乘的默认类型
   * </pre>
   *
   * <code>TRANSIT_TYPE_OTHER = 51;</code>
   */
  TRANSIT_TYPE_OTHER(51),
  /**
   * <pre>
   * 公交 BRT
   * </pre>
   *
   * <code>TRANSIT_BRT = 52;</code>
   */
  TRANSIT_BRT(52),
  /**
   * <pre>
   * 中转高速巴士
   * </pre>
   *
   * <code>TRANSIT_EXPRESS_BUS = 53;</code>
   */
  TRANSIT_EXPRESS_BUS(53),
  /**
   * <pre>
   * 中转本地巴士
   * </pre>
   *
   * <code>TRANSIT_LOCAL_BUS = 54;</code>
   */
  TRANSIT_LOCAL_BUS(54),
  /**
   * <pre>
   * 过境校车
   * </pre>
   *
   * <code>TRANSIT_SCHOOL_BUS = 55;</code>
   */
  TRANSIT_SCHOOL_BUS(55),
  /**
   * <pre>
   * 过境固定导轨
   * </pre>
   *
   * <code>TRANSIT_FIXED_GUIdEWAY = 56;</code>
   */
  TRANSIT_FIXED_GUIdEWAY(56),
  /**
   * <pre>
   * 运输_辅助运输
   * </pre>
   *
   * <code>TRANSIT_PARATRANSIT = 57;</code>
   */
  TRANSIT_PARATRANSIT(57),
  /**
   * <pre>
   * 运输辅助运输救护车
   * </pre>
   *
   * <code>TRANSIT_PARATRANSIT_AMBULANCE = 58;</code>
   */
  TRANSIT_PARATRANSIT_AMBULANCE(58),
  /**
   * <pre>
   *------------紧急车辆类型-------------------------------------------
   *EMERGENCY_TYPE_UNKNOWN= 60;// 消防、救护、警车等特种车辆
   * </pre>
   *
   * <code>EMERGENCY_TYPE_OTHER = 61;</code>
   */
  EMERGENCY_TYPE_OTHER(61),
  /**
   * <pre>
   * 应急消防轻型车
   * </pre>
   *
   * <code>EMERGENCY_FIRE_LIGHT_VEHICLE = 62;</code>
   */
  EMERGENCY_FIRE_LIGHT_VEHICLE(62),
  /**
   * <pre>
   * 紧急消防重型车辆
   * </pre>
   *
   * <code>EMERGENCY_FIRE_HEAVY_VEHICLE = 63;</code>
   */
  EMERGENCY_FIRE_HEAVY_VEHICLE(63),
  /**
   * <pre>
   * 运送急救人员到病患所在地的紧急消防救护车辆
   * </pre>
   *
   * <code>EMERGENCY_FIRE_PARAMEDIC_VEHICLE = 64;</code>
   */
  EMERGENCY_FIRE_PARAMEDIC_VEHICLE(64),
  /**
   * <pre>
   * 转移病人到医院的紧急消防救护车
   * </pre>
   *
   * <code>EMERGENCY_FIRE_AMBULANCE_VEHICLE = 65;</code>
   */
  EMERGENCY_FIRE_AMBULANCE_VEHICLE(65),
  /**
   * <pre>
   * 紧急警用轻型车辆
   * </pre>
   *
   * <code>EMERGENCY_POLICE_LIGHT_VEHICLE = 66;</code>
   */
  EMERGENCY_POLICE_LIGHT_VEHICLE(66),
  /**
   * <pre>
   * 紧急警用重型车辆
   * </pre>
   *
   * <code>EMERGENCY_POLICE_HEAVY_VEHICLE = 67;</code>
   */
  EMERGENCY_POLICE_HEAVY_VEHICLE(67),
  /**
   * <pre>
   * 紧急其他响应者
   * </pre>
   *
   * <code>EMERGENCY_OTHER_RESPONDER = 68;</code>
   */
  EMERGENCY_OTHER_RESPONDER(68),
  /**
   * <pre>
   * 紧急其他救护车
   * </pre>
   *
   * <code>EMERGENCY_OTHER_AMBULANCE = 69;</code>
   */
  EMERGENCY_OTHER_AMBULANCE(69),
  /**
   * <pre>
   *------------其他配备 V2X 的旅行者-----------------------------------
   * </pre>
   *
   * <code>OTHER_TRAVELER_TYPE_UNKNOWN = 80;</code>
   */
  OTHER_TRAVELER_TYPE_UNKNOWN(80),
  /**
   * <pre>
   * 其他旅行者类型
   * </pre>
   *
   * <code>OTHER_TRAVELER_TYPE_OTHER = 81;</code>
   */
  OTHER_TRAVELER_TYPE_OTHER(81),
  /**
   * <pre>
   * 其他旅行者行人
   * </pre>
   *
   * <code>OTHER_TRAVELER_PEDESTRIAN = 82;</code>
   */
  OTHER_TRAVELER_PEDESTRIAN(82),
  /**
   * <pre>
   * 其他视力残疾的旅行者
   * </pre>
   *
   * <code>OTHER_TRAVELER_VISUALLY_DISABLED = 83;</code>
   */
  OTHER_TRAVELER_VISUALLY_DISABLED(83),
  /**
   * <pre>
   * 其他身体残疾的旅客
   * </pre>
   *
   * <code>OTHER_TRAVELER_PHYSICALLY_DISABLED = 84;</code>
   */
  OTHER_TRAVELER_PHYSICALLY_DISABLED(84),
  /**
   * <pre>
   * 其他旅行自行车
   * </pre>
   *
   * <code>OTHER_TRAVELER_BICYCLE = 85;</code>
   */
  OTHER_TRAVELER_BICYCLE(85),
  /**
   * <pre>
   * 其他旅行者易受伤害的道路工人
   * </pre>
   *
   * <code>OTHER_TRAVELER_VULNERABLE_ROAD_WORKER = 86;</code>
   */
  OTHER_TRAVELER_VULNERABLE_ROAD_WORKER(86),
  /**
   * <pre>
   *------------其他配备 V2X 的设备类型----------------------------------
   * </pre>
   *
   * <code>INFRASTRUCTURE_TYPE_UNKNOWN = 90;</code>
   */
  INFRASTRUCTURE_TYPE_UNKNOWN(90),
  /**
   * <pre>
   * 基础设施_固定
   * </pre>
   *
   * <code>INFRASTRUCTURE_FIXED = 91;</code>
   */
  INFRASTRUCTURE_FIXED(91),
  /**
   * <pre>
   * 基础设施_可移动
   * </pre>
   *
   * <code>INFRASTRUCTURE_MOVABLE = 92;</code>
   */
  INFRASTRUCTURE_MOVABLE(92),
  /**
   * <pre>
   *装备货运拖车
   * </pre>
   *
   * <code>EQUIPPED_CARGO_TRAILER = 93;</code>
   */
  EQUIPPED_CARGO_TRAILER(93),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   * 未配备、未知或不可用
   * </pre>
   *
   * <code>UNKNOWN_VEHICLE_CLASS = 0;</code>
   */
  public static final int UNKNOWN_VEHICLE_CLASS_VALUE = 0;
  /**
   * <pre>
   *乘用车辆默认类型
   * </pre>
   *
   * <code>PASSENGER_VEHICLE_TYPE_UNKNOWN = 10;</code>
   */
  public static final int PASSENGER_VEHICLE_TYPE_UNKNOWN_VALUE = 10;
  /**
   * <pre>
   *小型卡 车、皮卡、面包车
   * </pre>
   *
   * <code>MOTOR_LIGHTTRUNK = 20;</code>
   */
  public static final int MOTOR_LIGHTTRUNK_VALUE = 20;
  /**
   * <pre>
   * 大卡车、货车默认类型
   * </pre>
   *
   * <code>TRUCK_VEHICLE_TYPE_UNKNOWN = 25;</code>
   */
  public static final int TRUCK_VEHICLE_TYPE_UNKNOWN_VALUE = 25;
  /**
   * <pre>
   * 摩托车默认类型
   * </pre>
   *
   * <code>MOTORCYCLE_TYPE_UNKNOWN = 40;</code>
   */
  public static final int MOTORCYCLE_TYPE_UNKNOWN_VALUE = 40;
  /**
   * <pre>
   *公交、短驳、换乘的默认类型
   * </pre>
   *
   * <code>TRANSIT_TYPE_UNKNOWN = 50;</code>
   */
  public static final int TRANSIT_TYPE_UNKNOWN_VALUE = 50;
  /**
   * <pre>
   *消防、救护、警车等特种车辆
   * </pre>
   *
   * <code>EMERGENCY_TYPE_UNKNOWN = 60;</code>
   */
  public static final int EMERGENCY_TYPE_UNKNOWN_VALUE = 60;
  /**
   * <pre>
   *------------细化分类如下所示--------------------------------------
   *UNKNOWN_VEHICLE_CLASS= 0; // 未配备、未知或不可用
   * </pre>
   *
   * <code>SPECIAL_VEHICLE_CLASS = 1;</code>
   */
  public static final int SPECIAL_VEHICLE_CLASS_VALUE = 1;
  /**
   * <pre>
   *------------基本乘用车类型----------------------------------------
   *PASSENGER_VEHICLE_TYPE_UNKNOWN= 10; //乘用车辆默认类型
   * </pre>
   *
   * <code>PASSENGER_VEHICLE_TYPE_OTHER = 11;</code>
   */
  public static final int PASSENGER_VEHICLE_TYPE_OTHER_VALUE = 11;
  /**
   * <pre>
   *------------轻型卡车、皮卡、厢式货车、面板---------------------------
   *LIGHT_TRUCK_VEHICLE_TYPE_UNKNOWN= 20;// 轻型卡车车辆默认类型
   * </pre>
   *
   * <code>LIGHT_TRUCK_VEHICLE_TYPE_OTHER = 21;</code>
   */
  public static final int LIGHT_TRUCK_VEHICLE_TYPE_OTHER_VALUE = 21;
  /**
   * <pre>
   *------------卡车，各种车轴类型，包括 HPMS 项目-----------------------
   *TRUCK_VEHICLE_TYPE_UNKNOWN= 25;// 大卡车、货车默认类型
   * </pre>
   *
   * <code>TRUCK_VEHICLE_TYPE_OTHER = 26;</code>
   */
  public static final int TRUCK_VEHICLE_TYPE_OTHER_VALUE = 26;
  /**
   * <pre>
   * 两个轴，六个轮胎单单元
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT2 = 27;</code>
   */
  public static final int TRUCK_AXLE_CNT2_VALUE = 27;
  /**
   * <pre>
   * 三轴，单单元
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT3 = 28;</code>
   */
  public static final int TRUCK_AXLE_CNT3_VALUE = 28;
  /**
   * <pre>
   * 四个或更多轴，单个单元
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT4 = 29;</code>
   */
  public static final int TRUCK_AXLE_CNT4_VALUE = 29;
  /**
   * <pre>
   * 四轴或更少轴，单拖车
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT4_TRAILER = 30;</code>
   */
  public static final int TRUCK_AXLE_CNT4_TRAILER_VALUE = 30;
  /**
   * <pre>
   * 五个或更少轴，单拖车
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT5_TRAILER = 31;</code>
   */
  public static final int TRUCK_AXLE_CNT5_TRAILER_VALUE = 31;
  /**
   * <pre>
   * 六轴或更多轴，单拖车
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT6_TRAILER = 32;</code>
   */
  public static final int TRUCK_AXLE_CNT6_TRAILER_VALUE = 32;
  /**
   * <pre>
   * 五个或更少的轴，多拖车
   * </pre>
   *
   * <code>TRUCK_AXLECNT5MULTITRAILER = 33;</code>
   */
  public static final int TRUCK_AXLECNT5MULTITRAILER_VALUE = 33;
  /**
   * <pre>
   * 六轴，多拖车
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT6_MULTI_TRAILER = 34;</code>
   */
  public static final int TRUCK_AXLE_CNT6_MULTI_TRAILER_VALUE = 34;
  /**
   * <pre>
   * 七个或更多轴，多拖车
   * </pre>
   *
   * <code>TRUCK_AXLE_CNT7_MULTI_TRAILER = 35;</code>
   */
  public static final int TRUCK_AXLE_CNT7_MULTI_TRAILER_VALUE = 35;
  /**
   * <pre>
   *------------摩托车类型--------------------------------------------
   *MOTORCYCLE_TYPE_UNKNOWN = 40;// 摩托车默认类型
   * </pre>
   *
   * <code>MOTORCYCLE_TYPE_OTHER = 41;</code>
   */
  public static final int MOTORCYCLE_TYPE_OTHER_VALUE = 41;
  /**
   * <pre>
   * 摩托车巡洋舰标准
   * </pre>
   *
   * <code>MOTORCYCLE_CRUISER_STANDARD = 42;</code>
   */
  public static final int MOTORCYCLE_CRUISER_STANDARD_VALUE = 42;
  /**
   * <pre>
   * 摩托车
   * </pre>
   *
   * <code>SPORT_UNCLAD_MOTORCYCLE_SPORT_UNCLAD = 43;</code>
   */
  public static final int SPORT_UNCLAD_MOTORCYCLE_SPORT_UNCLAD_VALUE = 43;
  /**
   * <pre>
   * 摩托车运动旅行
   * </pre>
   *
   * <code>MOTORCYCLE_SPORT_TOURING = 44;</code>
   */
  public static final int MOTORCYCLE_SPORT_TOURING_VALUE = 44;
  /**
   * <pre>
   * 摩托车超级运动
   * </pre>
   *
   * <code>MOTORCYCLE_SUPER_SPORT = 45;</code>
   */
  public static final int MOTORCYCLE_SUPER_SPORT_VALUE = 45;
  /**
   * <pre>
   * 摩托车旅行
   * </pre>
   *
   * <code>MOTORCYCLE_TOURING = 46;</code>
   */
  public static final int MOTORCYCLE_TOURING_VALUE = 46;
  /**
   * <pre>
   * 摩托车三轮车
   * </pre>
   *
   * <code>MOTORCYCLE_TRIKE = 47;</code>
   */
  public static final int MOTORCYCLE_TRIKE_VALUE = 47;
  /**
   * <pre>
   * 摩托车带乘客
   * </pre>
   *
   * <code>MOTORCYCLE_WPASSENGERS = 48;</code>
   */
  public static final int MOTORCYCLE_WPASSENGERS_VALUE = 48;
  /**
   * <pre>
   *------------运输类型----------------------------------------------
   *TRANSIT_TYPE_UNKNOWN= 50;//公交、短驳、换乘的默认类型
   * </pre>
   *
   * <code>TRANSIT_TYPE_OTHER = 51;</code>
   */
  public static final int TRANSIT_TYPE_OTHER_VALUE = 51;
  /**
   * <pre>
   * 公交 BRT
   * </pre>
   *
   * <code>TRANSIT_BRT = 52;</code>
   */
  public static final int TRANSIT_BRT_VALUE = 52;
  /**
   * <pre>
   * 中转高速巴士
   * </pre>
   *
   * <code>TRANSIT_EXPRESS_BUS = 53;</code>
   */
  public static final int TRANSIT_EXPRESS_BUS_VALUE = 53;
  /**
   * <pre>
   * 中转本地巴士
   * </pre>
   *
   * <code>TRANSIT_LOCAL_BUS = 54;</code>
   */
  public static final int TRANSIT_LOCAL_BUS_VALUE = 54;
  /**
   * <pre>
   * 过境校车
   * </pre>
   *
   * <code>TRANSIT_SCHOOL_BUS = 55;</code>
   */
  public static final int TRANSIT_SCHOOL_BUS_VALUE = 55;
  /**
   * <pre>
   * 过境固定导轨
   * </pre>
   *
   * <code>TRANSIT_FIXED_GUIdEWAY = 56;</code>
   */
  public static final int TRANSIT_FIXED_GUIdEWAY_VALUE = 56;
  /**
   * <pre>
   * 运输_辅助运输
   * </pre>
   *
   * <code>TRANSIT_PARATRANSIT = 57;</code>
   */
  public static final int TRANSIT_PARATRANSIT_VALUE = 57;
  /**
   * <pre>
   * 运输辅助运输救护车
   * </pre>
   *
   * <code>TRANSIT_PARATRANSIT_AMBULANCE = 58;</code>
   */
  public static final int TRANSIT_PARATRANSIT_AMBULANCE_VALUE = 58;
  /**
   * <pre>
   *------------紧急车辆类型-------------------------------------------
   *EMERGENCY_TYPE_UNKNOWN= 60;// 消防、救护、警车等特种车辆
   * </pre>
   *
   * <code>EMERGENCY_TYPE_OTHER = 61;</code>
   */
  public static final int EMERGENCY_TYPE_OTHER_VALUE = 61;
  /**
   * <pre>
   * 应急消防轻型车
   * </pre>
   *
   * <code>EMERGENCY_FIRE_LIGHT_VEHICLE = 62;</code>
   */
  public static final int EMERGENCY_FIRE_LIGHT_VEHICLE_VALUE = 62;
  /**
   * <pre>
   * 紧急消防重型车辆
   * </pre>
   *
   * <code>EMERGENCY_FIRE_HEAVY_VEHICLE = 63;</code>
   */
  public static final int EMERGENCY_FIRE_HEAVY_VEHICLE_VALUE = 63;
  /**
   * <pre>
   * 运送急救人员到病患所在地的紧急消防救护车辆
   * </pre>
   *
   * <code>EMERGENCY_FIRE_PARAMEDIC_VEHICLE = 64;</code>
   */
  public static final int EMERGENCY_FIRE_PARAMEDIC_VEHICLE_VALUE = 64;
  /**
   * <pre>
   * 转移病人到医院的紧急消防救护车
   * </pre>
   *
   * <code>EMERGENCY_FIRE_AMBULANCE_VEHICLE = 65;</code>
   */
  public static final int EMERGENCY_FIRE_AMBULANCE_VEHICLE_VALUE = 65;
  /**
   * <pre>
   * 紧急警用轻型车辆
   * </pre>
   *
   * <code>EMERGENCY_POLICE_LIGHT_VEHICLE = 66;</code>
   */
  public static final int EMERGENCY_POLICE_LIGHT_VEHICLE_VALUE = 66;
  /**
   * <pre>
   * 紧急警用重型车辆
   * </pre>
   *
   * <code>EMERGENCY_POLICE_HEAVY_VEHICLE = 67;</code>
   */
  public static final int EMERGENCY_POLICE_HEAVY_VEHICLE_VALUE = 67;
  /**
   * <pre>
   * 紧急其他响应者
   * </pre>
   *
   * <code>EMERGENCY_OTHER_RESPONDER = 68;</code>
   */
  public static final int EMERGENCY_OTHER_RESPONDER_VALUE = 68;
  /**
   * <pre>
   * 紧急其他救护车
   * </pre>
   *
   * <code>EMERGENCY_OTHER_AMBULANCE = 69;</code>
   */
  public static final int EMERGENCY_OTHER_AMBULANCE_VALUE = 69;
  /**
   * <pre>
   *------------其他配备 V2X 的旅行者-----------------------------------
   * </pre>
   *
   * <code>OTHER_TRAVELER_TYPE_UNKNOWN = 80;</code>
   */
  public static final int OTHER_TRAVELER_TYPE_UNKNOWN_VALUE = 80;
  /**
   * <pre>
   * 其他旅行者类型
   * </pre>
   *
   * <code>OTHER_TRAVELER_TYPE_OTHER = 81;</code>
   */
  public static final int OTHER_TRAVELER_TYPE_OTHER_VALUE = 81;
  /**
   * <pre>
   * 其他旅行者行人
   * </pre>
   *
   * <code>OTHER_TRAVELER_PEDESTRIAN = 82;</code>
   */
  public static final int OTHER_TRAVELER_PEDESTRIAN_VALUE = 82;
  /**
   * <pre>
   * 其他视力残疾的旅行者
   * </pre>
   *
   * <code>OTHER_TRAVELER_VISUALLY_DISABLED = 83;</code>
   */
  public static final int OTHER_TRAVELER_VISUALLY_DISABLED_VALUE = 83;
  /**
   * <pre>
   * 其他身体残疾的旅客
   * </pre>
   *
   * <code>OTHER_TRAVELER_PHYSICALLY_DISABLED = 84;</code>
   */
  public static final int OTHER_TRAVELER_PHYSICALLY_DISABLED_VALUE = 84;
  /**
   * <pre>
   * 其他旅行自行车
   * </pre>
   *
   * <code>OTHER_TRAVELER_BICYCLE = 85;</code>
   */
  public static final int OTHER_TRAVELER_BICYCLE_VALUE = 85;
  /**
   * <pre>
   * 其他旅行者易受伤害的道路工人
   * </pre>
   *
   * <code>OTHER_TRAVELER_VULNERABLE_ROAD_WORKER = 86;</code>
   */
  public static final int OTHER_TRAVELER_VULNERABLE_ROAD_WORKER_VALUE = 86;
  /**
   * <pre>
   *------------其他配备 V2X 的设备类型----------------------------------
   * </pre>
   *
   * <code>INFRASTRUCTURE_TYPE_UNKNOWN = 90;</code>
   */
  public static final int INFRASTRUCTURE_TYPE_UNKNOWN_VALUE = 90;
  /**
   * <pre>
   * 基础设施_固定
   * </pre>
   *
   * <code>INFRASTRUCTURE_FIXED = 91;</code>
   */
  public static final int INFRASTRUCTURE_FIXED_VALUE = 91;
  /**
   * <pre>
   * 基础设施_可移动
   * </pre>
   *
   * <code>INFRASTRUCTURE_MOVABLE = 92;</code>
   */
  public static final int INFRASTRUCTURE_MOVABLE_VALUE = 92;
  /**
   * <pre>
   *装备货运拖车
   * </pre>
   *
   * <code>EQUIPPED_CARGO_TRAILER = 93;</code>
   */
  public static final int EQUIPPED_CARGO_TRAILER_VALUE = 93;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static VehicleType valueOf(int value) {
    return forNumber(value);
  }

  public static VehicleType forNumber(int value) {
    switch (value) {
      case 0: return UNKNOWN_VEHICLE_CLASS;
      case 10: return PASSENGER_VEHICLE_TYPE_UNKNOWN;
      case 20: return MOTOR_LIGHTTRUNK;
      case 25: return TRUCK_VEHICLE_TYPE_UNKNOWN;
      case 40: return MOTORCYCLE_TYPE_UNKNOWN;
      case 50: return TRANSIT_TYPE_UNKNOWN;
      case 60: return EMERGENCY_TYPE_UNKNOWN;
      case 1: return SPECIAL_VEHICLE_CLASS;
      case 11: return PASSENGER_VEHICLE_TYPE_OTHER;
      case 21: return LIGHT_TRUCK_VEHICLE_TYPE_OTHER;
      case 26: return TRUCK_VEHICLE_TYPE_OTHER;
      case 27: return TRUCK_AXLE_CNT2;
      case 28: return TRUCK_AXLE_CNT3;
      case 29: return TRUCK_AXLE_CNT4;
      case 30: return TRUCK_AXLE_CNT4_TRAILER;
      case 31: return TRUCK_AXLE_CNT5_TRAILER;
      case 32: return TRUCK_AXLE_CNT6_TRAILER;
      case 33: return TRUCK_AXLECNT5MULTITRAILER;
      case 34: return TRUCK_AXLE_CNT6_MULTI_TRAILER;
      case 35: return TRUCK_AXLE_CNT7_MULTI_TRAILER;
      case 41: return MOTORCYCLE_TYPE_OTHER;
      case 42: return MOTORCYCLE_CRUISER_STANDARD;
      case 43: return SPORT_UNCLAD_MOTORCYCLE_SPORT_UNCLAD;
      case 44: return MOTORCYCLE_SPORT_TOURING;
      case 45: return MOTORCYCLE_SUPER_SPORT;
      case 46: return MOTORCYCLE_TOURING;
      case 47: return MOTORCYCLE_TRIKE;
      case 48: return MOTORCYCLE_WPASSENGERS;
      case 51: return TRANSIT_TYPE_OTHER;
      case 52: return TRANSIT_BRT;
      case 53: return TRANSIT_EXPRESS_BUS;
      case 54: return TRANSIT_LOCAL_BUS;
      case 55: return TRANSIT_SCHOOL_BUS;
      case 56: return TRANSIT_FIXED_GUIdEWAY;
      case 57: return TRANSIT_PARATRANSIT;
      case 58: return TRANSIT_PARATRANSIT_AMBULANCE;
      case 61: return EMERGENCY_TYPE_OTHER;
      case 62: return EMERGENCY_FIRE_LIGHT_VEHICLE;
      case 63: return EMERGENCY_FIRE_HEAVY_VEHICLE;
      case 64: return EMERGENCY_FIRE_PARAMEDIC_VEHICLE;
      case 65: return EMERGENCY_FIRE_AMBULANCE_VEHICLE;
      case 66: return EMERGENCY_POLICE_LIGHT_VEHICLE;
      case 67: return EMERGENCY_POLICE_HEAVY_VEHICLE;
      case 68: return EMERGENCY_OTHER_RESPONDER;
      case 69: return EMERGENCY_OTHER_AMBULANCE;
      case 80: return OTHER_TRAVELER_TYPE_UNKNOWN;
      case 81: return OTHER_TRAVELER_TYPE_OTHER;
      case 82: return OTHER_TRAVELER_PEDESTRIAN;
      case 83: return OTHER_TRAVELER_VISUALLY_DISABLED;
      case 84: return OTHER_TRAVELER_PHYSICALLY_DISABLED;
      case 85: return OTHER_TRAVELER_BICYCLE;
      case 86: return OTHER_TRAVELER_VULNERABLE_ROAD_WORKER;
      case 90: return INFRASTRUCTURE_TYPE_UNKNOWN;
      case 91: return INFRASTRUCTURE_FIXED;
      case 92: return INFRASTRUCTURE_MOVABLE;
      case 93: return EQUIPPED_CARGO_TRAILER;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<VehicleType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      VehicleType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<VehicleType>() {
          public VehicleType findValueByNumber(int number) {
            return VehicleType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return road.data.proto.V2X.getDescriptor().getEnumTypes().get(6);
  }

  private static final VehicleType[] VALUES = values();

  public static VehicleType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private VehicleType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:cn.seisys.v2x.pb.VehicleType)
}

