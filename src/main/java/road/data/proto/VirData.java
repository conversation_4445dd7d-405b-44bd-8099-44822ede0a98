// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

/**
 * <pre>
 *车辆意图及请求消息 VirData 
 * </pre>
 *
 * Protobuf type {@code cn.seisys.v2x.pb.VirData}
 */
public  final class VirData extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:cn.seisys.v2x.pb.VirData)
    VirDataOrBuilder {
private static final long serialVersionUID = 0L;
  // Use VirData.newBuilder() to construct.
  private VirData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private VirData() {
    vehicleId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new VirData();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private VirData(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            msgCnt_ = input.readUInt32();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            vehicleId_ = s;
            break;
          }
          case 24: {

            timestamp_ = input.readUInt64();
            break;
          }
          case 34: {
            road.data.proto.Position3D.Builder subBuilder = null;
            if (pos_ != null) {
              subBuilder = pos_.toBuilder();
            }
            pos_ = input.readMessage(road.data.proto.Position3D.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(pos_);
              pos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 42: {
            road.data.proto.IarData.Builder subBuilder = null;
            if (intAndReq_ != null) {
              subBuilder = intAndReq_.toBuilder();
            }
            intAndReq_ = input.readMessage(road.data.proto.IarData.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(intAndReq_);
              intAndReq_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_VirData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_VirData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            road.data.proto.VirData.class, road.data.proto.VirData.Builder.class);
  }

  public static final int MSGCNT_FIELD_NUMBER = 1;
  private int msgCnt_;
  /**
   * <pre>
   *定义消息编号。
   * </pre>
   *
   * <code>uint32 msgCnt = 1;</code>
   */
  public int getMsgCnt() {
    return msgCnt_;
  }

  public static final int VEHICLEID_FIELD_NUMBER = 2;
  private volatile java.lang.Object vehicleId_;
  /**
   * <pre>
   *临时车辆ID，与 BsmData 中的 obu_id 相同
   * </pre>
   *
   * <code>string vehicleId = 2;</code>
   */
  public java.lang.String getVehicleId() {
    java.lang.Object ref = vehicleId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      vehicleId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *临时车辆ID，与 BsmData 中的 obu_id 相同
   * </pre>
   *
   * <code>string vehicleId = 2;</code>
   */
  public com.google.protobuf.ByteString
      getVehicleIdBytes() {
    java.lang.Object ref = vehicleId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      vehicleId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TIMESTAMP_FIELD_NUMBER = 3;
  private long timestamp_;
  /**
   * <pre>
   *UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 3;</code>
   */
  public long getTimestamp() {
    return timestamp_;
  }

  public static final int POS_FIELD_NUMBER = 4;
  private road.data.proto.Position3D pos_;
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  public boolean hasPos() {
    return pos_ != null;
  }
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  public road.data.proto.Position3D getPos() {
    return pos_ == null ? road.data.proto.Position3D.getDefaultInstance() : pos_;
  }
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  public road.data.proto.Position3DOrBuilder getPosOrBuilder() {
    return getPos();
  }

  public static final int INTANDREQ_FIELD_NUMBER = 5;
  private road.data.proto.IarData intAndReq_;
  /**
   * <pre>
   *车辆意图及请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
   */
  public boolean hasIntAndReq() {
    return intAndReq_ != null;
  }
  /**
   * <pre>
   *车辆意图及请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
   */
  public road.data.proto.IarData getIntAndReq() {
    return intAndReq_ == null ? road.data.proto.IarData.getDefaultInstance() : intAndReq_;
  }
  /**
   * <pre>
   *车辆意图及请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
   */
  public road.data.proto.IarDataOrBuilder getIntAndReqOrBuilder() {
    return getIntAndReq();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (msgCnt_ != 0) {
      output.writeUInt32(1, msgCnt_);
    }
    if (!getVehicleIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, vehicleId_);
    }
    if (timestamp_ != 0L) {
      output.writeUInt64(3, timestamp_);
    }
    if (pos_ != null) {
      output.writeMessage(4, getPos());
    }
    if (intAndReq_ != null) {
      output.writeMessage(5, getIntAndReq());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (msgCnt_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, msgCnt_);
    }
    if (!getVehicleIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, vehicleId_);
    }
    if (timestamp_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, timestamp_);
    }
    if (pos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getPos());
    }
    if (intAndReq_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getIntAndReq());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof road.data.proto.VirData)) {
      return super.equals(obj);
    }
    road.data.proto.VirData other = (road.data.proto.VirData) obj;

    if (getMsgCnt()
        != other.getMsgCnt()) return false;
    if (!getVehicleId()
        .equals(other.getVehicleId())) return false;
    if (getTimestamp()
        != other.getTimestamp()) return false;
    if (hasPos() != other.hasPos()) return false;
    if (hasPos()) {
      if (!getPos()
          .equals(other.getPos())) return false;
    }
    if (hasIntAndReq() != other.hasIntAndReq()) return false;
    if (hasIntAndReq()) {
      if (!getIntAndReq()
          .equals(other.getIntAndReq())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + MSGCNT_FIELD_NUMBER;
    hash = (53 * hash) + getMsgCnt();
    hash = (37 * hash) + VEHICLEID_FIELD_NUMBER;
    hash = (53 * hash) + getVehicleId().hashCode();
    hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTimestamp());
    if (hasPos()) {
      hash = (37 * hash) + POS_FIELD_NUMBER;
      hash = (53 * hash) + getPos().hashCode();
    }
    if (hasIntAndReq()) {
      hash = (37 * hash) + INTANDREQ_FIELD_NUMBER;
      hash = (53 * hash) + getIntAndReq().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static road.data.proto.VirData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.VirData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.VirData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.VirData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.VirData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static road.data.proto.VirData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static road.data.proto.VirData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.VirData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.VirData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static road.data.proto.VirData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static road.data.proto.VirData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static road.data.proto.VirData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(road.data.proto.VirData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *车辆意图及请求消息 VirData 
   * </pre>
   *
   * Protobuf type {@code cn.seisys.v2x.pb.VirData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:cn.seisys.v2x.pb.VirData)
      road.data.proto.VirDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_VirData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_VirData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              road.data.proto.VirData.class, road.data.proto.VirData.Builder.class);
    }

    // Construct using road.data.proto.VirData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      msgCnt_ = 0;

      vehicleId_ = "";

      timestamp_ = 0L;

      if (posBuilder_ == null) {
        pos_ = null;
      } else {
        pos_ = null;
        posBuilder_ = null;
      }
      if (intAndReqBuilder_ == null) {
        intAndReq_ = null;
      } else {
        intAndReq_ = null;
        intAndReqBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return road.data.proto.V2X.internal_static_cn_seisys_v2x_pb_VirData_descriptor;
    }

    @java.lang.Override
    public road.data.proto.VirData getDefaultInstanceForType() {
      return road.data.proto.VirData.getDefaultInstance();
    }

    @java.lang.Override
    public road.data.proto.VirData build() {
      road.data.proto.VirData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public road.data.proto.VirData buildPartial() {
      road.data.proto.VirData result = new road.data.proto.VirData(this);
      result.msgCnt_ = msgCnt_;
      result.vehicleId_ = vehicleId_;
      result.timestamp_ = timestamp_;
      if (posBuilder_ == null) {
        result.pos_ = pos_;
      } else {
        result.pos_ = posBuilder_.build();
      }
      if (intAndReqBuilder_ == null) {
        result.intAndReq_ = intAndReq_;
      } else {
        result.intAndReq_ = intAndReqBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof road.data.proto.VirData) {
        return mergeFrom((road.data.proto.VirData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(road.data.proto.VirData other) {
      if (other == road.data.proto.VirData.getDefaultInstance()) return this;
      if (other.getMsgCnt() != 0) {
        setMsgCnt(other.getMsgCnt());
      }
      if (!other.getVehicleId().isEmpty()) {
        vehicleId_ = other.vehicleId_;
        onChanged();
      }
      if (other.getTimestamp() != 0L) {
        setTimestamp(other.getTimestamp());
      }
      if (other.hasPos()) {
        mergePos(other.getPos());
      }
      if (other.hasIntAndReq()) {
        mergeIntAndReq(other.getIntAndReq());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      road.data.proto.VirData parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (road.data.proto.VirData) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int msgCnt_ ;
    /**
     * <pre>
     *定义消息编号。
     * </pre>
     *
     * <code>uint32 msgCnt = 1;</code>
     */
    public int getMsgCnt() {
      return msgCnt_;
    }
    /**
     * <pre>
     *定义消息编号。
     * </pre>
     *
     * <code>uint32 msgCnt = 1;</code>
     */
    public Builder setMsgCnt(int value) {
      
      msgCnt_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *定义消息编号。
     * </pre>
     *
     * <code>uint32 msgCnt = 1;</code>
     */
    public Builder clearMsgCnt() {
      
      msgCnt_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object vehicleId_ = "";
    /**
     * <pre>
     *临时车辆ID，与 BsmData 中的 obu_id 相同
     * </pre>
     *
     * <code>string vehicleId = 2;</code>
     */
    public java.lang.String getVehicleId() {
      java.lang.Object ref = vehicleId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        vehicleId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *临时车辆ID，与 BsmData 中的 obu_id 相同
     * </pre>
     *
     * <code>string vehicleId = 2;</code>
     */
    public com.google.protobuf.ByteString
        getVehicleIdBytes() {
      java.lang.Object ref = vehicleId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        vehicleId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *临时车辆ID，与 BsmData 中的 obu_id 相同
     * </pre>
     *
     * <code>string vehicleId = 2;</code>
     */
    public Builder setVehicleId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      vehicleId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *临时车辆ID，与 BsmData 中的 obu_id 相同
     * </pre>
     *
     * <code>string vehicleId = 2;</code>
     */
    public Builder clearVehicleId() {
      
      vehicleId_ = getDefaultInstance().getVehicleId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *临时车辆ID，与 BsmData 中的 obu_id 相同
     * </pre>
     *
     * <code>string vehicleId = 2;</code>
     */
    public Builder setVehicleIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      vehicleId_ = value;
      onChanged();
      return this;
    }

    private long timestamp_ ;
    /**
     * <pre>
     *UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }
    /**
     * <pre>
     *UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public Builder setTimestamp(long value) {
      
      timestamp_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *UTC 时间，单位毫秒，19700101000到现在的毫秒
     * </pre>
     *
     * <code>uint64 timestamp = 3;</code>
     */
    public Builder clearTimestamp() {
      
      timestamp_ = 0L;
      onChanged();
      return this;
    }

    private road.data.proto.Position3D pos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> posBuilder_;
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public boolean hasPos() {
      return posBuilder_ != null || pos_ != null;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public road.data.proto.Position3D getPos() {
      if (posBuilder_ == null) {
        return pos_ == null ? road.data.proto.Position3D.getDefaultInstance() : pos_;
      } else {
        return posBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public Builder setPos(road.data.proto.Position3D value) {
      if (posBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        pos_ = value;
        onChanged();
      } else {
        posBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public Builder setPos(
        road.data.proto.Position3D.Builder builderForValue) {
      if (posBuilder_ == null) {
        pos_ = builderForValue.build();
        onChanged();
      } else {
        posBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public Builder mergePos(road.data.proto.Position3D value) {
      if (posBuilder_ == null) {
        if (pos_ != null) {
          pos_ =
            road.data.proto.Position3D.newBuilder(pos_).mergeFrom(value).buildPartial();
        } else {
          pos_ = value;
        }
        onChanged();
      } else {
        posBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public Builder clearPos() {
      if (posBuilder_ == null) {
        pos_ = null;
        onChanged();
      } else {
        pos_ = null;
        posBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public road.data.proto.Position3D.Builder getPosBuilder() {
      
      onChanged();
      return getPosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    public road.data.proto.Position3DOrBuilder getPosOrBuilder() {
      if (posBuilder_ != null) {
        return posBuilder_.getMessageOrBuilder();
      } else {
        return pos_ == null ?
            road.data.proto.Position3D.getDefaultInstance() : pos_;
      }
    }
    /**
     * <pre>
     *
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder> 
        getPosFieldBuilder() {
      if (posBuilder_ == null) {
        posBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.Position3D, road.data.proto.Position3D.Builder, road.data.proto.Position3DOrBuilder>(
                getPos(),
                getParentForChildren(),
                isClean());
        pos_ = null;
      }
      return posBuilder_;
    }

    private road.data.proto.IarData intAndReq_;
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.IarData, road.data.proto.IarData.Builder, road.data.proto.IarDataOrBuilder> intAndReqBuilder_;
    /**
     * <pre>
     *车辆意图及请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
     */
    public boolean hasIntAndReq() {
      return intAndReqBuilder_ != null || intAndReq_ != null;
    }
    /**
     * <pre>
     *车辆意图及请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
     */
    public road.data.proto.IarData getIntAndReq() {
      if (intAndReqBuilder_ == null) {
        return intAndReq_ == null ? road.data.proto.IarData.getDefaultInstance() : intAndReq_;
      } else {
        return intAndReqBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *车辆意图及请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
     */
    public Builder setIntAndReq(road.data.proto.IarData value) {
      if (intAndReqBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        intAndReq_ = value;
        onChanged();
      } else {
        intAndReqBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *车辆意图及请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
     */
    public Builder setIntAndReq(
        road.data.proto.IarData.Builder builderForValue) {
      if (intAndReqBuilder_ == null) {
        intAndReq_ = builderForValue.build();
        onChanged();
      } else {
        intAndReqBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *车辆意图及请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
     */
    public Builder mergeIntAndReq(road.data.proto.IarData value) {
      if (intAndReqBuilder_ == null) {
        if (intAndReq_ != null) {
          intAndReq_ =
            road.data.proto.IarData.newBuilder(intAndReq_).mergeFrom(value).buildPartial();
        } else {
          intAndReq_ = value;
        }
        onChanged();
      } else {
        intAndReqBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *车辆意图及请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
     */
    public Builder clearIntAndReq() {
      if (intAndReqBuilder_ == null) {
        intAndReq_ = null;
        onChanged();
      } else {
        intAndReq_ = null;
        intAndReqBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *车辆意图及请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
     */
    public road.data.proto.IarData.Builder getIntAndReqBuilder() {
      
      onChanged();
      return getIntAndReqFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *车辆意图及请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
     */
    public road.data.proto.IarDataOrBuilder getIntAndReqOrBuilder() {
      if (intAndReqBuilder_ != null) {
        return intAndReqBuilder_.getMessageOrBuilder();
      } else {
        return intAndReq_ == null ?
            road.data.proto.IarData.getDefaultInstance() : intAndReq_;
      }
    }
    /**
     * <pre>
     *车辆意图及请求
     * </pre>
     *
     * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        road.data.proto.IarData, road.data.proto.IarData.Builder, road.data.proto.IarDataOrBuilder> 
        getIntAndReqFieldBuilder() {
      if (intAndReqBuilder_ == null) {
        intAndReqBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            road.data.proto.IarData, road.data.proto.IarData.Builder, road.data.proto.IarDataOrBuilder>(
                getIntAndReq(),
                getParentForChildren(),
                isClean());
        intAndReq_ = null;
      }
      return intAndReqBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:cn.seisys.v2x.pb.VirData)
  }

  // @@protoc_insertion_point(class_scope:cn.seisys.v2x.pb.VirData)
  private static final road.data.proto.VirData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new road.data.proto.VirData();
  }

  public static road.data.proto.VirData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<VirData>
      PARSER = new com.google.protobuf.AbstractParser<VirData>() {
    @java.lang.Override
    public VirData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new VirData(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<VirData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<VirData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public road.data.proto.VirData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

