// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: v2x.proto

package road.data.proto;

public interface VirDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:cn.seisys.v2x.pb.VirData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *定义消息编号。
   * </pre>
   *
   * <code>uint32 msgCnt = 1;</code>
   */
  int getMsgCnt();

  /**
   * <pre>
   *临时车辆ID，与 BsmData 中的 obu_id 相同
   * </pre>
   *
   * <code>string vehicleId = 2;</code>
   */
  java.lang.String getVehicleId();
  /**
   * <pre>
   *临时车辆ID，与 BsmData 中的 obu_id 相同
   * </pre>
   *
   * <code>string vehicleId = 2;</code>
   */
  com.google.protobuf.ByteString
      getVehicleIdBytes();

  /**
   * <pre>
   *UTC 时间，单位毫秒，19700101000到现在的毫秒
   * </pre>
   *
   * <code>uint64 timestamp = 3;</code>
   */
  long getTimestamp();

  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  boolean hasPos();
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  road.data.proto.Position3D getPos();
  /**
   * <pre>
   *
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.Position3D pos = 4;</code>
   */
  road.data.proto.Position3DOrBuilder getPosOrBuilder();

  /**
   * <pre>
   *车辆意图及请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
   */
  boolean hasIntAndReq();
  /**
   * <pre>
   *车辆意图及请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
   */
  road.data.proto.IarData getIntAndReq();
  /**
   * <pre>
   *车辆意图及请求
   * </pre>
   *
   * <code>.cn.seisys.v2x.pb.IarData intAndReq = 5;</code>
   */
  road.data.proto.IarDataOrBuilder getIntAndReqOrBuilder();
}
