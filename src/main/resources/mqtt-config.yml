#  MQTT连接地址
#    IPv6
#    tcp://[2408:860c:5:611:1:7a00:0:29]:31811
#
#  用户名: ackrkxq/caict-roadside-testenv
#  密码: ogiSaPzRnZCCevGf
#
#    MQTT topic
#    业务编码：
#    1.2.156.28896.1.04.121563000.31173000.06010111.00002002	米泉南路-安驰路
#    1.2.156.28896.1.04.121563000.31173000.06010111.00002001	博园路-米泉南路
#    名词解释：
#    业务对象编码：OID区域码（30）+实体类型码（8位）+顺序码（8位），示例 1.2.156.28896.1.04.121110000.31160000.06010139.00000015
#  short_boc(简短业务对象编码): 将业务对象编码中固定前缀"1.2.156.28896."拿掉，剩余部分移除小数点，变成 104121110000311600000601013900000015
#    node_short_boc(路口的简短业务对象编码)：路口清单中路口业务对象编码的简化版，规则见short_boc
#    device_short_boc(设备的简短业务对象编码)：设备清单中设备的业务对象编码的简化版，规则见short_boc
#
#    topic模板：
#    v2x/v1/mec/{node_short_boc}/{device_short_boc}/participant
#    v2x/v1/mec/{node_short_boc}/{device_short_boc}/trafficflow
#    v2x/v1/mec/{node_short_boc}/{device_short_boc}/rte

# MQTT配置
mqtt:
  # 客户端配置
  client:
    id: "10car10cityMqttConsumerClientTest"  # 客户端ID，建议使用唯一标识
  
  # MQTT Broker配置 - 支持多个broker
  brokers:
    # 项目一路口broker配置 [[memory:4816137]]
    broker1:
      url: "tcp://*************:31811"
      username: "abbrsne/device-all-pass"
      password: "FvREptbVZdWOHmTe"
      description: "项目一路口MQTT Broker"
      enabled: true
    # 项目十一路口broker配置
    broker2:
      url: "tcp://*************:31811"
      username: "atcztnt/device-all-pass"
      password: "YKRvpgNUCbOerSZL"
      description: "项目十一路口MQTT Broker"
      enabled: true

  # RocketMQ配置
  rocketmq:
    nameserver: "**************:56738;**************:56739"  # RocketMQ NameServer地址
    producer:
      group: "dual_mqtt_producer_group"  # 生产者组
      sendMsgTimeout: 6000  # 发送超时时间(ms)
      retryTimesWhenSendFailed: 3  # 发送失败重试次数
    consumer:
      groups:
        - name: "traffic_light_consumer_group"
          topics: ["dual_mqtt_trafficlightdata"]
          description: "交通灯数据消费者组"
        - name: "traffic_event_consumer_group"
          topics: ["dual_mqtt_trafficeventdata"]
          description: "交通事件数据消费者组"

  # 主题配置
  topics:
    cam:
      - name: "moyulu_caoan"
        topic: "v2x/v1/mec/104121100000311700000601011100000304/+/cam"
        description: "墨玉-曹安"
      - name: "moyulu_nanan"
        topic: "v2x/v1/mec/104121100000311600000601011100000303/+/cam"
        description: "墨玉-南安"
      - name: "boyuanlu_moyulu"
        topic: "v2x/v1/mec/104121100000311600000601011100000302/+/cam"
        description: "博园-墨玉"
      - name: "boyuanlu_qiboyuan"
        topic: "v2x/v1/mec/104121100000311700000601011100010002/+/cam"
        description: "博园-汽博园"
      - name: "boyuanlu_miquan"
        topic: "v2x/v1/mec/104121563000311730000601011100002001/+/cam"
        description: "博园-米泉"
      - name: "boyuanlu_yutian"
        topic: "v2x/v1/mec/104121553000311630000601011100002003/+/cam"
        description: "博园-于田"
      - name: "anchi_miquan"
        topic: "v2x/v1/mec/104121563000311730000601011100002002/+/cam"
        description: "安驰-米泉"
      - name: "anchi_tashan"
        topic: "v2x/v1/mec/104121100000311600000601011100000037/+/cam"
        description: "安驰-塔山"
      - name: "anchi_anyue"
        topic: "v2x/v1/mec/104121110000311600000601011100000047/+/cam"
        description: "安驰-安悦"
      - name: "anchi_anxie"
        topic: "v2x/v1/mec/104121110000311600000601011100000024/+/cam"
        description: "安驰安谐"
    map:
      - name: "墨玉-曹安"
        topic: "v2x/v1/rsu/104121100000311700000601011100000304/104121090000311700000601013900000027/map/down"
        description: "墨玉-曹安"
      - name: "墨玉-南安"
        topic: "v2x/v1/rsu/104121100000311600000601011100000303/104121090000311700000601013900000028/map/down"
        description: "墨玉-南安"
      - name: "博园-墨玉"
        topic: "v2x/v1/rsu/104121100000311600000601011100000302/104121090000311600000601013900000016/map/down"
        description: "博园-墨玉"
      - name: "博园-汽博园"
        topic: "v2x/v1/rsu/104121100000311700000601011100010002/104121184776312764130601013991000055/map/down"
        description: "博园-汽博园"
      - name: "博园-米泉"
        topic: "v2x/v1/rsu/104121563000311730000601011100002001/104121171021312841930601013991000073/map/down"
        description: "博园-米泉"
      - name: "博园-于田"
        topic: "v2x/v1/rsu/104121553000311630000601011100002003/104121175095312818470601013991000087/map/down"
        description: "博园-于田"
      - name: "安驰-米泉"
        topic: "v2x/v1/rsu/104121563000311730000601011100002002/104121170623128648206010139910000016/map/down"
        description: "安驰-米泉"
      - name: "安驰-塔山"
        topic: "v2x/v1/rsu/104121100000311600000601011100000037/104121100000311700000601013900000023/map/down"
        description: "安驰-塔山"
      - name: "安驰-安悦"
        topic: "v2x/v1/rsu/104121110000311600000601011100000047/104121100000311700000601013900000024/map/down"
        description: "安驰-安悦"
      - name: "安驰安谐"
        topic: "v2x/v1/rsu/104121110000311600000601011100000024/104121100000311700000601013900000025/map/down"
        description: "安驰安谐"

  # 事件类型映射关系配置
  eventType:
    - localCode: "913"
      remoteCode: "0903"
      description: "非法停车"
    - localCode: "412"
      remoteCode: "5502"
      description: "非机动车道行驶"
    - localCode: "901"
      remoteCode: "0901"
      description: "超速行驶"
    - localCode: "902"
      remoteCode: "0902"
      description: "低速行驶"
    - localCode: "904"
      remoteCode: "0904"
      description: "交通逆行"
    - localCode: "412"
      remoteCode: "5502"
      description: "非机动车在机动车道行驶的检测"
    - localCode: "405"
      remoteCode: "5520"
      description: "行人闯入"
    - localCode: "907"
      remoteCode: "6502"
      description: "异常变道"
    - localCode: "707"
      remoteCode: ""
      description: "交通拥堵"

  # 路口nodeId配置
  intersections:
    - id: "104121100000311700000601011100000304"
      name: "墨玉-曹安"
      nodeId: "10001"
      description: "墨玉路-曹安路路口"
      refPosLat: 31.2907390
      refPosLon: 121.1588260
      regionId: 1529
      localNodeId: 8807
    - id: "104121100000311600000601011100000303"
      name: "墨玉-南安"
      nodeId: "10002"
      description: "墨玉路-南安路路口"
      refPosLat: 31.2878700
      refPosLon: 121.1589240
      regionId: 1528 # 1293+18962  65293+18962
      localNodeId: 8978
    - id: "104121100000311600000601011100000302"
      name: "博园-墨玉"
      nodeId: "10003"
      description: "博园路-墨玉路路口"
      refPosLat: 31.2974290
      refPosLon: 121.1697740
#      regionId: 1629
#      localNodeId: 9774
      regionId: 1628
      localNodeId: 221
    - id: "104121100000311700000601011100010002"
      name: "博园-汽博园"
      nodeId: "10004"
      description: "博园路-汽博园路口"
      refPosLat: 31.2865661
      refPosLon: 121.1746741
      regionId: 1728
      localNodeId: 4665
    - id: "104121563000311730000601011100002001"
      name: "博园-米泉"
      nodeId: "10005"
      description: "博园路-米泉南路路口"
      refPosLat: 31.2218943
      refPosLon: 121.4084064
      regionId: 4022
      localNodeId: 8418
    - id: "104121553000311630000601011100002003"
      name: "博园-于田"
      nodeId: "10006"
      description: "博园路-于田路路口"
      refPosLat: 31.1556617
      refPosLon: 121.4980932
      regionId: 4915
      localNodeId: 8056
    - id: "104121563000311730000601011100002002"
      name: "安驰-米泉"
      nodeId: "10007"
      description: "安驰路-米泉南路路口"
      refPosLat: 31.2974290
      refPosLon: 121.1697740
      regionId: 1629
      localNodeId: 9774
    - id: "104121100000311600000601011100000037"
      name: "安驰-塔山"
      nodeId: "10008"
      description: "安驰路-塔山路路口"
      refPosLat: 31.2903915
      refPosLon: 121.1841050
      regionId: 1829
      localNodeId: 4103
    - id: "104121110000311600000601011100000047"
      name: "安驰-安悦"
      nodeId: "10009"
      description: "安驰路-安悦路路口"
      refPosLat: 31.2902446
      refPosLon: 121.1852108
      regionId: 1829
      localNodeId: 5202
    - id: "104121110000311600000601011100000024"
      name: "安驰-安谐"
      nodeId: "10010"
      description: "安驰路-安谐路路口"
      refPosLat: 31.2898767
      refPosLon: 121.1880420
      regionId: 1828
      localNodeId: 8098

  # 相位映射配置
  phaseMapping:
    - originalPhaseId: 9
      convertedPhaseId: 2
    - originalPhaseId: 1
      convertedPhaseId: 130
    - originalPhaseId: 5
      convertedPhaseId: 194
    - originalPhaseId: 13
      convertedPhaseId: 66
    - originalPhaseId: 10
      convertedPhaseId: 1
    - originalPhaseId: 2
      convertedPhaseId: 129
    - originalPhaseId: 6
      convertedPhaseId: 193
    - originalPhaseId: 14
      convertedPhaseId: 65
    - originalPhaseId: 11
      convertedPhaseId: 3
    - originalPhaseId: 3
      convertedPhaseId: 131
    - originalPhaseId: 7
      convertedPhaseId: 195
    - originalPhaseId: 15
      convertedPhaseId: 67
    - originalPhaseId: 12
      convertedPhaseId: 8
    - originalPhaseId: 4
      convertedPhaseId: 136
    - originalPhaseId: 8
      convertedPhaseId: 200
    - originalPhaseId: 16
      convertedPhaseId: 72