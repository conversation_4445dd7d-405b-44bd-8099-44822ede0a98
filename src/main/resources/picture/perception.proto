syntax = "proto3";


package road.data.proto;


message RsPerceptionInfo {
  uint32 channel_id = 1; //渠道来源


  string rcu_id = 2; //固定8位的RCU编号


  //0：未知来源，1：融合结果，2：摄像头，3：毫米波雷达，4：激光雷达,    5:相机融合，6:多杆融合 7: 单杆预测
  uint32 device_type = 3; //设备类别


  //当 deviceType 等于 0 或 1 时，此值等于0x0000000000000000000000（11 字节，每字节值等于 0）。
  //否则，此值等于对应感知设备的编号。
  //感知设备编号为22位数字字符串，每两位数字字符转换为一个字节的正整数，共计传输11个字节数据。
  string device_id = 4; //感知设备编号


  uint64 timestamp_of_dev_out = 5; //感知/传感/采集器件原始数据帧输出时间戳 t0时间


  uint64 timestamp_of_det_in = 6; //原始数据帧进入路侧融合计算应用的时间戳


  uint64 timestamp_of_det_out = 7; //路侧融合计算应用输出结构化结果的时间戳 t6


  //[0..10]，0：GCJ02坐标系；1：自定义独立坐标系；2-10：预留，不可缺省
  uint32 gnss_type = 8; //坐标系类型


  repeated PerceptionObject objective = 9; //感知对象列表


  //0：相机近顺；1：相机近逆；2：相机远顺；3：相机远逆；8：鱼眼；11:激光逆；12:激光顺；15:毫米波逆；16:毫米波顺；27: 单杆预测；28:数字孪生；29:多杆融合；30:相机融合；31:单杆融合
  uint32 slot = 10; //槽位


  string pole = 11; //杆位号


  uint64 timestamp_of_driver_out = 12; //原始数据帧出驱动的时间戳 t3


  uint64 timestamp_of_per_in = 13; //原始数据帧进入路侧感知计算应用的时间戳


  uint64 timestamp_of_per_out = 14; //原始数据帧输出路侧感知计算应用的时间戳

  string edge_cloud_id = 15; //边缘云编号

  uint64 edge_timestamp = 16; //上传时间戳

  uint64 timestamp_of_multi_poles_det_in = 17; //单杆融合结果进入路侧多杆融合计算应用的时间戳

  uint64 timestamp_of_multi_poles_det_out = 18; //单杆融合结果进入路侧多杆融合计算应用的时间戳

}


//感知对象信息
message PerceptionObject {
  string uuid = 1;


  uint32 obj_id = 2; //每个对象在本数据中的顺序号


  //0：行人，1：自行车，2：乘用车，3：摩托车，4：特殊用车辆，5：公交车，6：有轨道车，7：卡车，8：三轮车，9：交通信号灯
  //10：交通标识，15：动物，60：路障，61：交通锥，254：其它类型，255：未获取，101:骑行者，102:观光车
  //103：施工牌，104：施工堆，105：水马围栏，106：事故三角板，107：抛洒物
  uint32 type = 3; //类型.


  //0：静止，1：运动
  uint32 status = 4; //状态


  //单位：m
  float len = 5; //长度
  //单位：m
  float width = 6; //宽度
  //单位：m
  float height = 7; //高度

  double longitude = 8;

  double latitude = 9;


  //单位：m
  float loc_east = 10; //东西向距离


  //单位：m
  float loc_north = 11; //南北向距离


  //[0..255]，0xFF表示无效，定义应符合附录J的规定。
  uint32 pos_confidence = 12; //位置精度等级


  double elevation = 13; //高程（海拔）


  uint32 elev_confidence = 14; //高程精度


  //单位：m/s
  float speed = 15;


  uint32 speed_confidence = 16; //速度精度等级


  //单位：m/s
  float speed_east = 17; //东西向速度


  uint32 speed_east_confidence = 18; //东西向速度精度等级


  //单位：m/s
  float speed_north = 19; //南北向速度


  uint32 speed_north_confidence = 20; //南北向速度精度等级


  //正北方向与运动方向顺时针夹角
  float heading = 21; //航向角


  uint32 head_confidence = 22; //航向角精度等级


  //单位：m/s2
  float accel_vert = 23; //目标纵向加速度


  uint32 accel_vert_confidence = 24; //目标纵向加速度置精度等级


  uint32 tracked_times = 25; //目标跟踪时长


  uint32 hist_loc_num = 26; //目标历史轨迹数量


  //历史目标轨迹点列表（上传从本时刻起倒数 8 秒内的轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
  //距离本时刻越久远的数据在轨迹点列表中越靠前。当histLocNum值为0时，数据长度为0。
  repeated HistLoc hist_locs = 27; //目标历史轨迹列表


  uint32 pred_loc_num = 28; //目标预测轨迹数量


  //预测轨迹点列表（上传从本时刻起 3 秒内的预测轨迹信息，频率 10Hz），轨迹点数据格式和定义见表 32；
  //距离本时刻越近的数据在轨迹点列表中越靠前。当predLocNum值为0时，数据长度为0。
  repeated HistLoc pred_locs = 29; //目标预测轨迹列表


  int32 lane_id = 30; //目标所在车道编号


  //0：无效；1：卡尔曼滤波信息；2～255：预留。
  //当值为1时，传输卡尔曼滤波信息字段，其余值均不发送。
  uint32 filter_info_type = 31; //滤波信息的类型


  FilterInfo filter_info = 32; //卡尔曼滤波信息


  uint32 lenplate_no = 33; //车牌号字节数


  string plate_no = 34; //车牌号


  //1：大型汽车；2：挂车；3：大型新能源汽车；4：小型汽车；5：小型新能源汽车；6：使馆汽车；7：领馆汽车；8：港澳入出境车；
  // 9：教练汽车；10：警用汽车；11：普通摩托车；12：轻便摩托车；13：使馆摩托车；14：领馆摩托车；15：教练摩托车；
  // 16：警用摩托车；17：低速车；18：临时行驶车；19：临时入境汽车；20：临时入境摩托车；21：拖拉机；22：其他；
  //“0xFE”表示异常，“0xFF”表示无效
  uint32 plate_type = 35; //车牌类型


  //1：黄；2：蓝；3：黑；4：白；5：绿（农用车）；6：红；7：黄绿；8：渐变绿；20：天（酞）蓝；21：棕黄；22：其他；
  //“0xFE”表示异常，“0xFF”表示无效。
  uint32 plate_color = 36; //车牌颜色


  //1：白；4：灰；7：黄；10：粉；13：红；16：紫；19：绿；22：蓝；25：棕；28：黑；31：橙；34：青；37：银；40：银白；43：其他；
  //其中，（值+1）表示浅色，（值+2）表示深色。例如：22表示蓝色，（22+1）即23表示浅蓝色，（22+2）即24表示深蓝色。
  //“0xFE”表示异常，“0xFF”表示无效。
  uint32 obj_color = 37; //车身颜色


  uint32 device_idx = 38; //槽位集合


  uint32 road_id = 39; //高精地图roadPkId

  float distance = 40; //到mec距离

  uint32 parking_duration = 41; //停车时长，单位:ms

  string pole = 42; //杆位号

}


//轨迹点数据
message HistLoc {
  double longitude = 1;


  double latitude = 2;


  uint32 pos_confidence = 3; //位置精度等级


  //单位：m/s
  float speed = 4;


  uint32 speed_confidence = 5; //速度精度等级


  float heading = 6; //航向角


  uint32 head_confidence = 7; //航向角精度等级
}


//滤波信息数据
message FilterInfo {


  //表示后续的协方差矩阵由 N 个状态量构建而成。
  //无法给出协方差矩阵时，此值值为 0，后续状态量所在序号、状态量协方差数据长度为 0（无该区域的数据）。
  uint32 dimension = 1; //状态量协方差矩阵的维度


  //构建协方差的状态量所在的“序号-1”，共 N 个状态量，其中 N 为状态量协方差矩阵维度。
  //每个值是表 31 感知对象信息中“序号”中的值减去 1，用于表示下面的协方差由这几个数据构建而成。
  //如：一个由东西向距离、南北向距离、东西向速度、南北向速度构建成而的协方差矩阵，此处 4 个值分别是 9、10、16、18。
  //因假设各目标物均使用相同类型和数量的状态量，为减少传输量，因此状态量协方差维度、状态1～N所在序号只需在第一个目标物中提供，其他目标物中需省略。
  repeated uint32 var_n_index = 2; //状态量所在序号列表


  //由 N 个状态量构建而成的（n·n）协方差矩阵，其中 N 为状态量协方差矩阵维度，传输时只取矩阵下三角全部元素的值，
  //取的数值从上向下、从左向右顺序排列
  Cov covs = 3; //卡尔曼滤波的更新步骤得到的状态量协方差矩阵


  //矩阵及取值元素规则同上，数据长度当无法提供此数据时，此数据段长度为0。
  Cov covs_pred = 4; //卡尔曼滤波预测步骤得到的状态量协方差矩阵


  //状态量 1～N 对应的卡尔曼滤波预测步骤得到的状态量，各数据的类型与该状态在表 31
  //状态量确定了此处共计的数据长度。中定义的类型相同。
  //当无法提供此数据域的值时，总长度为0。
  repeated Cov var_pred = 5; //卡尔曼滤波预测步骤得到的状态量
}


//状态量协方差数据
message Cov {
  repeated float covs = 1;
}