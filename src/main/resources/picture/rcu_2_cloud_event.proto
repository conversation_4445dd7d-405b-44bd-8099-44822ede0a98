syntax = "proto3";

option java_multiple_files = true;

package com.zhidaohulian.mec.handler.bean;

message Rcu2CloudEvent {
    int32 channel_id = 1;   //渠道来源，[0..255]，枚举，定义见CSAE 295.3-2023附录F（1：云控基础平台，11/12：XXX公司感知设备，13~255：预留）
    string rcu_id = 2;       //长度固定为8位的RCU编号，定义应符合CSAE 295.3-2023附录A的规定（4为32进制编号，共计描述1048575个编号）
    int32 event_type = 3;   //事件类别，[0..255]，枚举，定义应符合CSAE 295.3-2023附录L的规定（0：倒车/逆行，1：慢行，2：快行，3：紧急制动，4：异常停车，5~255：预留）
    int32 confidence = 4;   //事件发生的置信度，[0..255]，0~254：预留，255：表示无效，当无法描述事件精度时，可使用此值
    int32 gnss_type = 5;    //坐标系类型，[0..10]，0：GCJ02坐标系，1：自定义独立坐标系，2~10：预留，不可缺省
    double longitude = 6;   //经度
    double latitude = 7;    //纬度
    int64 timestamp = 8;    //事件发生的时间戳（UTC时间，东八区）
    string event_id = 9;    //由16个字符组成的标识事件
    int32 exts_len = 10;    //扩宽字段内容长度
    string exts = 11;       //扩宽字段内容
//    int32 target_ids_len = 12;  //事件关联的目标对象的uuid个数
    repeated string target_ids = 13;    //事件关联的目标对象uuid列表，当target_ids_len值为0时，不传输此数据项
}

message Rcu2cloudEventAck {
    string event_id = 1;    //由16个字符组成的标识事件
}

message Rcu2cloudEventCancel {
    int32 channel_id = 1;   //渠道来源，[0..255]，枚举，定义见CSAE 295.3-2023附录F（1：云控基础平台，11/12：XXX公司感知设备，13~255：预留）
    string rcu_id = 2;       //长度固定为8位的RCU编号，定义应符合CSAE 295.3-2023附录A的规定（4为32进制编号，共计描述1048575个编号）
    int64 timestamp = 3;    //事件发生的时间戳（UTC时间，东八区）
    string event_id = 4;    //由16个字符组成的标识事件
}

message Rcu2cloudEventCancelAck {
    int32 channel_id = 1;   //渠道来源，[0..255]，枚举，定义见CSAE 295.3-2023附录F（1：云控基础平台，11/12：XXX公司感知设备，13~255：预留）
    string rcu_id = 2;       //长度固定为8位的RCU编号，定义应符合CSAE 295.3-2023附录A的规定（4为32进制编号，共计描述1048575个编号）
    int64 timestamp = 3;    //事件发生的时间戳（UTC时间，东八区）
    string event_id = 4;    //由16个字符组成的标识事件
}

